/**
 * Created by gluo006 on 8/22/2019.
 */
({
    getObjectRecords : function(component,event, helper){
        var userInfo = $A.get("$SObjectType.CurrentUser");
        var userId = '';
        if(userInfo.Id){
           userId  = userInfo.Id;
        }
        var action = component.get("c.allClaim");
        action.setParams({
            "userId": userId,
            "pageNumber": component.get('v.pageNumber'),
            'allPageSize': component.get('v.pageCount'),
            'startDate':component.get('v.claimStartDate'),
            'endDate':component.get('v.claimEndDate'),
            'isSearch':component.get('v.isSearch')
        });

        action.setCallback(this, function (response) {
            var state = response.getState();
            if (component.isValid() && state === "SUCCESS") {
                var results = response.getReturnValue();
                var data = JSON.parse(results);

                var claimColumns;
                if (data.boolIsHayward) {
                    claimColumns = [
                        {
                            label: $A.get("$Label.c.CCM_Portal_Action"),
                            width: '80px',
                            tdStyle: 'text-align: left',
                            children:[
                            {
                                type: "lightning:buttonIcon",
                                attributes:{
                                    value: "${claim.Id}",
                                    variant:"bare",
                                    iconName:"utility:preview",
                                    alternativeText: $A.get("$Label.c.CCM_Portal_View"),
                                    class: "${viewStyleCss}",
                                    onclick: component.getReference('c.doView')
                                }
                            },
                            {
                                type: "lightning:buttonIcon",
                                attributes:{
                                    value: "${claim.Id}",
                                    variant:"bare",
                                    iconName:"utility:edit",
                                    alternativeText: $A.get("$Label.c.CCM_Portal_Edit"),
                                    class: "${editStyleCss}",
                                    onclick: component.getReference("c.doEdit")
                                }
                            }
                        ]},
                        {label: $A.get("$Label.c.CCM_Portal_Name"), fieldName:'claim.Name'},
                        {label: $A.get("$Label.c.CCM_Portal_CustomerName"), fieldName:'claim.Customer__r.Name'},
                        {label: $A.get("$Label.c.CCM_Portal_Model"), fieldName:'claim.Case__r.Product_Code__c'},
                        {label: $A.get("$Label.c.CCM_Portal_Status"), fieldName: 'claim.Status__c' },
                        // {label: 'Payment Status', fieldName: 'claim.Payment_Status__c'},
                        {label: $A.get("$Label.c.CCM_Portal_LaborCost"),
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${claim.Labor_Cost_Summary__c}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },
                        {label: $A.get("$Label.c.CCM_Portal_PartsCost"),
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${claim.Parts_Cost__c}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },
                        {label: $A.get("$Label.c.CCM_Portal_Totals"),
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${claim.Total__c}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },
                        // { label: 'Invoice Number', fieldName: 'claim.Invoice_Item__r.Invoice__r.Name' },
                        { label: $A.get("$Label.c.CCM_Portal_ServiceOption"), fieldName: 'claim.Service_Option__c' },
                        { label: $A.get("$Label.c.CCM_Portal_PickupFee"), fieldName: 'claim.Pickup_Fee__c' },
                        { label: $A.get("$Label.c.CCM_Portal_ServicePartner"), fieldName: 'claim.Service_Partner__r.Name' },
                    ];
                } else {
                    claimColumns = [
                        {
                            label: $A.get("$Label.c.CCM_Portal_Action"),
                            width: '80px',
                            tdStyle: 'text-align: left',
                            children:[
                            {
                                type: "lightning:buttonIcon",
                                attributes:{
                                    value: "${claim.Id}",
                                    variant:"bare",
                                    iconName:"utility:preview",
                                    alternativeText: $A.get("$Label.c.CCM_Portal_View"),
                                    class: "${viewStyleCss}",
                                    onclick: component.getReference('c.doView')
                                }
                            },
                            {
                                type: "lightning:buttonIcon",
                                attributes:{
                                    value: "${claim.Id}",
                                    variant:"bare",
                                    iconName:"utility:edit",
                                    alternativeText: $A.get("$Label.c.CCM_Portal_Edit"),
                                    class: "${editStyleCss}",
                                    onclick: component.getReference("c.doEdit")
                                }
                            }
                        ]},
                        {label: $A.get("$Label.c.CCM_Portal_Name"), fieldName:'claim.Name'},
                        {label: $A.get("$Label.c.CCM_Portal_CustomerName"), fieldName:'claim.Customer__r.Name'},
                        {label: $A.get("$Label.c.CCM_Portal_Model"), fieldName:'claim.Case__r.Product_Code__c'},
                        {label: $A.get("$Label.c.CCM_Portal_Status"), fieldName: 'claim.Status__c' },
                        {label: $A.get("$Label.c.CCM_Portal_PaymentStatus"), fieldName: 'claim.Payment_Status__c'},
                        {label: $A.get("$Label.c.CCM_Portal_LaborCost"),
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${claim.Labor_Cost_Summary__c}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },
                        {label: $A.get("$Label.c.CCM_Portal_PartsCost"),
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${claim.Parts_Cost__c}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },
                        {label: $A.get("$Label.c.CCM_Portal_Totals"),
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${claim.Total__c}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },
                        {label: 'Pack Number', fieldName:'claim.Claim_Pack__r.Name'},
                        {label: $A.get("$Label.c.CCM_Portal_InvoiceNumber"), fieldName:'claim.Invoice_Item__r.Invoice__r.Name'},
                    ];
                }
                component.set('v.claimColumns', claimColumns);
                component.set('v.currentClaims', data.Claim);
                component.set('v.totalRecords', data.TotalPageNumber);
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert($A.get("$Label.c.CCM_Portal_ErrorTips"));
                }
            }
        });
        $A.enqueueAction(action);
    },
    excelWarrantyClaimListFormat: function(data){
        let excelDatasFormat = [];
        data.forEach(item => {
                let excelData = {
                    'Claim Number': item.claimNumber || '',
                    'Service Partner': item.servicePartner || '',
                    'Created Date': item.createdDate || '',
                    'Repair Date': item.repairDate || '',
                    'Labor Hour': item.laborHour || null,
                    'Labor Rate': item.laborRate || null,
                    'Repair / Replacement': item.repairOrReplacement || '',
                    'Product': item.product || '',
                    'Part': item.part || '',
                    'Part Qty': item.partQty || null,
                    'SN': item.sn || '',
                    'Material Cost Subtotal': item.materialCostSubtotal || null,
                    'Credit Mark Up': item.creditMarkUp || null,
                    'Admin Cost & Diagnosis Fee': item.eligibilityCheckFee || null,
                    'Labor Cost Subtotal': item.laborCostSubtotal || null,
                    'Pick-up Fee': item.pickUpFee || null,
                    'Total Cost': item.totalCost || null,
                    'pack Number': item.packNumber || ''
                };
                excelDatasFormat.push(excelData);
            }
        );
        return excelDatasFormat;
    }
})