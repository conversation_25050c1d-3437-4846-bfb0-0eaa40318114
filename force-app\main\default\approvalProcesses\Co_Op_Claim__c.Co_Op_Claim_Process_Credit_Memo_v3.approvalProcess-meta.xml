<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <type>creator</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>OPE_Sales_Director</submitter>
        <type>roleSubordinates</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>PT_Sales_Director</submitter>
        <type>roleSubordinates</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Sales_Operation</submitter>
        <type>roleSubordinates</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>RecordType</field>
        <field>Claim_Status__c</field>
        <field>Co_Op_Program__c</field>
        <field>Co_Op_Program_Customer__c</field>
        <field>Customer__c</field>
        <field>Claim_Returned_Reason__c</field>
        <field>Comment__c</field>
        <field>Submitted_Date__c</field>
        <field>LastModifiedBy</field>
        <field>CreatedBy</field>
        <field>CurrencyIsoCode</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Sales_Manager__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <label>FEAM Sales Manager Approval</label>
        <name>FEAM_Sales_Manager_Approval</name>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Superior_Approver__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <booleanFilter>1 AND 2</booleanFilter>
            <criteriaItems>
                <field>Co_Op_Claim__c.Is_Sales_Manager_Superior_Approver__c</field>
                <operation>equals</operation>
                <value>False</value>
            </criteriaItems>
            <criteriaItems>
                <field>Co_Op_Claim__c.Superior_Approver__c</field>
                <operation>notEqual</operation>
                <value>Ben Battaglia</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>FEAM Sales Manager Superior</label>
        <name>FEAM_Sales_Manager_Superior</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Co_Op_Claim__c.Is_Sales_Manager_Superior_Approver__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>FEAM Sales Manager Superior Approval</label>
        <name>Superior_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Update_Current_Approver</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <booleanFilter>1 OR 2</booleanFilter>
            <criteriaItems>
                <field>Co_Op_Claim__c.Sales_Group__c</field>
                <operation>equals</operation>
                <value>SG08</value>
            </criteriaItems>
            <criteriaItems>
                <field>Co_Op_Claim__c.Sales_Group__c</field>
                <operation>equals</operation>
                <value>SG07</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>BEAM Approve(08/07)</label>
        <name>BEAM_Approve_08_07</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Update_Current_Approver</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Co_Op_Claim__c.Sales_Group__c</field>
                <operation>equals</operation>
                <value>SG04</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>BEAM Approve(04)</label>
        <name>BEAM_Approve_04</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Update_Current_Approver</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Co_Op_Claim__c.Sales_Group__c</field>
                <operation>equals</operation>
                <value>SG06</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>BEAM Approve(06)</label>
        <name>BEAM_Approve_06</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Update_Current_Approver</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Co_Op_Claim__c.Sales_Group__c</field>
                <operation>equals</operation>
                <value>SG27</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>BEAM Approive(27)</label>
        <name>BEAM_Approive_27</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Co_Op_Claim__c.Sales_Group__c</field>
                <operation>equals</operation>
                <value>SG28</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>BEAM Approve(28)</label>
        <name>BEAM_Approve_28</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <booleanFilter>1 OR 2</booleanFilter>
            <criteriaItems>
                <field>Co_Op_Claim__c.Sales_Group__c</field>
                <operation>equals</operation>
                <value>SG29</value>
            </criteriaItems>
            <criteriaItems>
                <field>Co_Op_Claim__c.Sales_Group__c</field>
                <operation>equals</operation>
                <value>SG05</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>BEAM Approive(05/29)</label>
        <name>BEAM_Approive_05_29</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Update_Current_Approver</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <booleanFilter>1 OR 2 OR 3 OR 4 OR 5 OR 6 OR 7 OR 8 OR 9</booleanFilter>
            <criteriaItems>
                <field>Co_Op_Claim__c.Sales_Group__c</field>
                <operation>equals</operation>
                <value>SG22</value>
            </criteriaItems>
            <criteriaItems>
                <field>Co_Op_Claim__c.Sales_Group__c</field>
                <operation>equals</operation>
                <value>SG23</value>
            </criteriaItems>
            <criteriaItems>
                <field>Co_Op_Claim__c.Sales_Group__c</field>
                <operation>equals</operation>
                <value>SG24</value>
            </criteriaItems>
            <criteriaItems>
                <field>Co_Op_Claim__c.Sales_Group__c</field>
                <operation>equals</operation>
                <value>SG25</value>
            </criteriaItems>
            <criteriaItems>
                <field>Co_Op_Claim__c.Sales_Group__c</field>
                <operation>equals</operation>
                <value>SG26</value>
            </criteriaItems>
            <criteriaItems>
                <field>Co_Op_Claim__c.Sales_Group__c</field>
                <operation>equals</operation>
                <value>SG30</value>
            </criteriaItems>
            <criteriaItems>
                <field>Co_Op_Claim__c.Sales_Group__c</field>
                <operation>equals</operation>
                <value>SG31</value>
            </criteriaItems>
            <criteriaItems>
                <field>Co_Op_Claim__c.Sales_Group__c</field>
                <operation>equals</operation>
                <value>SG32</value>
            </criteriaItems>
            <criteriaItems>
                <field>Co_Op_Claim__c.Sales_Group__c</field>
                <operation>equals</operation>
                <value>SG21</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>BEAM Approve(21-26/30-32)</label>
        <name>BEAM_Approve_21_26_30_32</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <label>Finance Approval</label>
        <name>Finance_Approval</name>
        <rejectBehavior>
            <type>BackToPrevious</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Clear_Current_Approver</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Update_Current_Approver</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <label>BEAM Manager</label>
        <name>BEAM_Manager</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Co_Op_Claim__c.Total_Claim_Amount__c</field>
                <operation>greaterThan</operation>
                <value>&quot;USD 2,500&quot;</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Director of Finance Approve</label>
        <name>Director_of_Finance_Approve</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Co_Op_Claim__c.Total_Claim_Amount__c</field>
                <operation>greaterThan</operation>
                <value>&quot;USD 10,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>Co_Op_Claim__c.Is_Sales_Manager_Superior_Approver__c</field>
                <operation>equals</operation>
                <value>False</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>VP Sales and Channel Manager Approval</label>
        <name>VP_Sales_and_Channel_Manager_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Co_Op_Claim__c.Total_Claim_Amount__c</field>
                <operation>greaterThan</operation>
                <value>&quot;USD 25,000&quot;</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>CEO Approve</label>
        <name>CEO_Approve</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Co_Op_Claim__c.Total_Claim_Amount__c</field>
                <operation>greaterThan</operation>
                <value>&quot;USD 100,000&quot;</value>
            </criteriaItems>
        </entryCriteria>
        <label>EVP Approval</label>
        <name>EVP_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>Co_Op_Claim__c.Claim_Status__c</field>
            <operation>equals</operation>
            <value>Draft,Returned</value>
        </criteriaItems>
        <criteriaItems>
            <field>Co_Op_Claim__c.RecordType</field>
            <operation>equals</operation>
            <value>Credit Memo</value>
        </criteriaItems>
        <criteriaItems>
            <field>Co_Op_Claim__c.ORG_Code__c</field>
            <operation>notEqual</operation>
            <value>CCA</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Claim_Status_Approved</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Update_Last_Approval_Date</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>true</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Claim_Status_Returned</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Clear_Current_Approver</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Claim_Status_Submitted</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Claim_Submitted_Date_Today</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Update_Submitter</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>Co-Op Claim Process (Credit Memo)_v3</label>
    <nextAutomatedApprover>
        <useApproverFieldOfRecordOwner>false</useApproverFieldOfRecordOwner>
        <userHierarchyField>approver__c</userHierarchyField>
    </nextAutomatedApprover>
    <processOrder>1</processOrder>
    <recallActions>
        <action>
            <name>Claim_Status_Draft</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Clear_Current_Approver</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOrCurrentApprover</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
