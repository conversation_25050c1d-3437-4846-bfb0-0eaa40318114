/**
 * @description: test class for CCM_NewClaim
 */
@isTest(SeeAllData=true)
public class CCM_NewClaimTest {
    static testMethod void testMethod1() {
        List<RecordType> acRecordType = [
            SELECT Id, Name
            FROM RecordType
            WHERE SObjectType = 'Account' AND DeveloperName = 'PersonAccount'
        ];
        List<RecordType> acRecordType1 = [
            SELECT Id, Name
            FROM RecordType
            WHERE SObjectType = 'Account' AND Name = 'Channel'
        ];

        Account cac = new Account();
        cac.Name = 'test';
        cac.ShippingPostalCode = '60007';
        cac.RecordTypeId = acRecordType1[0].Id;
        cac.ORG_Code__c = 'CNA'; 
        cac.Distributor_or_Dealer__c = 'Dealer';
        cac.TaxID__c = 'testTax';
        insert cac;

        Account ac = new Account();
        ac.LastName = 'test';
        ac.FirstName = 'test';
        ac.ShippingPostalCode = '60007';
        ac.RecordTypeId = acRecordType[0].Id;
        ac.PersonEmail = '<EMAIL>';
        ac.Distributor_or_Dealer__c = 'Dealer';
        ac.TaxID__c = 'testTax';
        insert ac;

        Recordtype rt = [
            SELECT Id
            FROM Recordtype
            WHERE Name = 'Service' AND SObjectType = 'Sales_Program__c'
        ];
        Sales_Program__c sp = new Sales_Program__c();
        sp.Customer__c = cac.Id;
        sp.Brands__c = 'EGO';
        sp.Freight_Term__c = 'PICK';
        sp.Approval_Status__c = 'Approved';
        sp.RecordTypeId = rt.Id;
        sp.Labor_Rate__c = 30;
        sp.sales_group__c = 'SG01';
        sp.Payment_Term__c= 'NA001';
        sp.Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.';
        sp.Authorized_Brand_Name_To_Oracle__c = 'test1';
        insert sp;

        Recordtype rt1 = [
            SELECT Id
            FROM Recordtype
            WHERE
                Name = 'Billing Address'
                AND SObjectType = 'Account_Address__c'
        ];
        Account_Address__c aa = new Account_Address__c();
        aa.Active__c = true;
        aa.City__c = 'US';
        aa.Country__c = 'PA';
        aa.Customer__c = cac.id;
        aa.Name = 'test';
        aa.RecordTypeId = rt1.Id;
        aa.Postal_Code__c = '20005';
        aa.State__c = 'US';
        aa.Address1__c = '11111';
        insert aa;

        Address_With_Program__c awp = new Address_With_Program__c();
        awp.Status__c = 'A';
        awp.Program__c = sp.id;
        awp.Account_Address__c = aa.id;
        insert awp;

        List<RecordType> proRecordType = [
            SELECT Id, Name
            FROM RecordType
            WHERE SObjectType = 'Product2' AND DeveloperName = 'Product'
        ];
        List<RecordType> kitRecordType = [
            SELECT Id, Name
            FROM RecordType
            WHERE SObjectType = 'Product2' AND DeveloperName = 'Kit'
        ];
        List<RecordType> partRecordType = [
            SELECT Id, Name
            FROM RecordType
            WHERE SObjectType = 'Product2' AND DeveloperName = 'Parts'
        ];

        Product2 pro = new Product2();
        pro.Name = 'Test';
        pro.Brand_Name__c = 'EGO';
        pro.RecordTypeId = proRecordType[0].Id;
        pro.ProductCode = '1234567';
        pro.IsActive = true;
        pro.Source__c = 'PIM';
        insert pro;

        Product2 pkit = new Product2();
        pkit.Name = 'Test';
        pkit.Brand_Name__c = 'EGO';
        pkit.RecordTypeId = kitRecordType[0].Id;
        pkit.ProductCode = '1234567';
        pkit.IsActive = true;
        pkit.Source__c = 'PIM';
        insert pkit;

        Product2 par = new Product2();
        par.Name = 'Test1';
        par.Brand_Name__c = 'EGO';
        par.RecordTypeId = partRecordType[0].Id;
        par.ProductCode = pro.ProductCode;
        par.IsActive = true;
        par.Source__c = 'PIM';
        insert par;

        Recordtype rtd = [
            SELECT id
            FROM RecordType
            WHERE Name = 'Products and Parts' AND sobjecttype = 'Kit_item__c'
            LIMIT 1
        ];
        Kit_item__c ki1 = new Kit_item__c();
        ki1.product__c = pro.Id;
        ki1.Parts__c = par.Id;
        ki1.RecordTypeId = rtd.id;
        ki1.Source__c = 'PIM';
        ki1.Repairable__c = true;

        insert ki1;

        Recordtype rtd2 = [
            SELECT id
            FROM RecordType
            WHERE Name = 'Kits and Products' AND sobjecttype = 'Kit_item__c'
            LIMIT 1
        ];
        Kit_item__c ki2 = new Kit_item__c();
        ki2.product__c = pro.Id;
        ki2.Kit__c = pkit.Id;
        ki2.RecordTypeId = rtd2.id;
        ki2.Source__c = 'PIM';
        ki2.Repairable__c = true;
        insert ki2;

        BOM__c bom = new BOM__c();
        bom.Product_code__c = pro.ProductCode;
        bom.Parts_code__c = pro.ProductCode;
        bom.Quantity__c = 1;
        insert bom;

        Storage__c sto = new Storage__c();
        sto.Product__c = pro.Id;
        sto.Available_Inventory__c = 2;
        sto.Sub_storage__c = 'CNA01';
        insert sto;

        Storage__c sto2 = new Storage__c();
        sto2.Product__c = par.Id;
        sto2.Available_Inventory__c = 2;
        sto2.Sub_storage__c = 'CNA01';
        insert sto2;

        Storage_List__c sl = new Storage_List__c();
        sl.Name = 'CNA100';
        sl.Postal_Code__c = '91708';
        insert sl;
        
        test.startTest();
        
        Pricebook2 pb = new Pricebook2();
        pb.IsActive = true;
        pb.Name = 'Test';
        insert pb;

        Customer_Brand_Pricebook_Mapping__c cbpm = new Customer_Brand_Pricebook_Mapping__c();
        cbpm.Type__c = 'Service';
        cbpm.Name = 'CNA-Direct Dealer Price for Parts';
        cbpm.Price_Book__c = pb.Id;
        insert cbpm;

        Default_PriceBook__c dpb = new Default_PriceBook__c();
        dpb.Price_Book_Id__c = pb.Id;
        dpb.Name = 'defaultBook54321';
        dpb.DevelopName__c = 'Warranty and Sample price list';
        insert dpb;

        ID standardPBID = Test.getStandardPricebookId();

        /*PricebookEntry standardpbe = new PricebookEntry();
            standardpbe.Pricebook2Id = standardPBID;
            standardpbe.Product2Id = pro.Id;
            standardpbe.UnitPrice = 1000;
            standardpbe.IsActive = true;
            insert standardpbe;*/

       /** PricebookEntry pbe = new PricebookEntry();
        pbe.IsActive = true;
        pbe.Product2Id = pro.Id;
        pbe.UnitPrice = 1000;
        pbe.Pricebook2Id = pb.Id;
        pbe.UseStandardPrice = false;
        insert pbe;*/

            Product2 pro4 = new Product2();
            pro4.Name = 'Test';
            pro4.Brand_Name__c = 'EGO';
            pro4.RecordTypeId = proRecordType[0].Id;
            pro4.ProductCode = '1234567';
            pro4.IsActive = true;
            insert pro4;
 
            ID standardPBID4 = Test.getStandardPricebookId();
   
            PricebookEntry standardpbe4 = new PricebookEntry();
            standardpbe4.Pricebook2Id = standardPBID4;
            standardpbe4.Product2Id = pro4.Id;
            standardpbe4.UnitPrice = 1000;
            standardpbe4.IsActive = true;
            insert standardpbe4;

        Warranty__c wrty = TestDataFactory.createWarranty();
        wrty.Master_Product__c = pro.Id;
        wrty.AccountCustomer__c = cac.Id;
        wrty.Brand_Name__c = 'EGO';
        wrty.Purchase_Date__c = Date.today();
        
        insert wrty;

        Warranty_Item__c wi = TestDataFactory.createWarrantyItem(wrty.Id);
        wi.Product__c = par.Id;
        wi.Serial_Number__c = 'NLM0219080001X';
        insert wi;

        System_Configuration__c sc = new System_Configuration__c();
        sc.Name = 'Claim Auto Approval';
        sc.Is_Active__c = true;
        insert sc;

        System_Configuration__c sc1 = new System_Configuration__c();
        sc1.Name = 'Maximum Cost Threshold';
        sc1.Is_Active__c = true;
        sc1.value__c = '100';
        insert sc1;

        Account objAccount = new Account(Name = 'test', RecordTypeId = CCM_Constants.CUSTOMER_RECORD_TYPE_CHANNEL_ID);
        objAccount.ORG_Code__c = 'CNA';
        objAccount.TaxID__c = 'testTax';
        insert objAccount;
        Contact objContact = new Contact(LastName = 'test', AccountId = objAccount.Id, Email = 'test@test' + Datetime.now().getTime() + '.com');
        insert objContact;
        Profile objProfile = [SELECT Id FROM Profile WHERE Name = :CCM_Constants.PROFILE_NAME_PARTNER_COMMUNITY_SERVICE];
        User objPartner = TestDataFactory.communityUserSetUp(objContact, objProfile.Id);

        CCM_NewClaim.customerInfo('<EMAIL>', 'test', 'test');
        CCM_NewClaim.SearchWarrantyBySerialNumber(
            null,
            'NLM0219080001X',
            null,
            'test',
            'test',
            wi.Product__c,
            true
        );
        CCM_NewClaim.SearchWarrantyBySerialNumber(
            cac.id,
            'NLM0219080001X',
            'EGO',
            null,
            null,
            wi.Product__c,
            false
        );
        Map<String, Object> partsMap = new Map<String, Object>();
        partsMap.put('partsId', par.Id);
        partsMap.put('price', 2.00);
        partsMap.put('quantity', 2);
        partsMap.put('total', '4');
        partsMap.put('LaborTime', 3);

        Map<String, Object> body = new Map<String, Object>();
        //body.put('userId', '0054F000001tX1UQAU');
        body.put('userId', objPartner.Id);
        body.put('labourHours', 0);
        body.put('partsList', '[{"partsId": ' + pro4.id + ', "price": "' + 10 + '", "quantity": "' + 12 + '"}]');
        body.put('repairDescription', 'test');
        body.put('overTimeHour', '0');
        body.put('partsCost',0);
        body.put('totalPrice','15');
        body.put('dropOfDate', '2019-12-03');
        body.put('repairDate', '2019-12-05');
        body.put('productId', par.Id);
        body.put('warrantyId', wrty.Id);
        body.put('customerId', cac.Id);
        body.put('laborCostSubtotal','0');
        body.put('serviceOption', 'Replacement');
        body.put('repairType', ' ');
        body.put('auditComments', ' ');
        body.put('recallOption', '');
        body.put('failureCode', ' ');
        body.put('project', ' ');
        body.put('repairableParts', '{}}');
        body.put('inventory', 'Chervon inventory');
        body.put('ShippingStreet', '13 oak drive');
        body.put('ShippingCity', 'Hamilton');
        body.put('ShippingCountry', 'US');
        body.put('ShippingState', 'NY');
        body.put('ShippingPostalCode', '13346');
        body.put('replacementFirstName', 'yizhao');
        body.put('replacementLastName', 'Xu');
        body.put('replacementPhone', '');
        body.put('claimId', '');
        body.put('decPickupFeeSubtotal', 100);
        body.put('markup', 100);
        body.put('hst',100);
        body.put('qst',100);
        body.put('gst',100);
        body.put('pst',100);
        body.put('diagnosisFee', 100);
        body.put('replacementBaseFee', 100);
        body.put('boolPickup', true);
        body.put('decPickupDistance',100);
        body.put('overTimeDescription','overTimeDescriptionoverTimeDescription');
       
       
        
        
        String contentStr = Json.serialize(body);
        CCM_NewClaim.SaveWarrantyClaim(contentStr);
        CCM_NewClaim.saveDraftWarrantyClaim(contentStr);
        CCM_NewClaim.GenerateProjectPicklistValue(pro.Id, cac.id, Date.today());
        CCM_NewClaim.GenerateFailureCodePicklistValue();

        Case c1 = new Case();
        c1.Warranty_Item__c = wi.Id;
        // c1.Service_Option__c='Repair';
        insert c1;

        // Warranty_Claim__c wc = [SELECT Id FROM Warranty_Claim__c LIMIT 1];

        Claim_Item__c ci=new  Claim_Item__c();
        insert ci;

        Warranty_Claim__c wc = new Warranty_Claim__c(
            Customer__c = ac.Id,
            Case__c = c1.Id,
            // Inventory__c='Dealer Inventory',
            Status__c = 'Draft'
        );
        insert wc;

        CCM_NewClaim.viewWarrantyClaim(wc.Id);
        CCM_NewClaim.SetupBomList(pro.Id, '');
        CCM_NewClaim.GenerateVersionList(pro.Id);
        test.stopTest();
    }

    static testMethod void testMethod2() {
        List<RecordType> acRecordType = [
            SELECT Id, Name
            FROM RecordType
            WHERE SObjectType = 'Account' AND DeveloperName = 'PersonAccount'
        ];
        List<RecordType> acRecordType1 = [
            SELECT Id, Name
            FROM RecordType
            WHERE SObjectType = 'Account' AND Name = 'Channel'
        ];

        Account cac = new Account();
        cac.Name = 'test';
        cac.ShippingPostalCode = '60007';
        cac.RecordTypeId = acRecordType1[0].Id;
        cac.Distributor_or_Dealer__c = 'Dealer';
        cac.ORG_Code__c = 'CNA'; 
        cac.TaxID__c = 'testTax';
        insert cac;

        Account ac = new Account();
        ac.LastName = 'test';
        ac.FirstName = 'test';
        ac.ShippingPostalCode = '60007';
        ac.RecordTypeId = acRecordType[0].Id;
        ac.PersonEmail = '<EMAIL>';
        ac.Distributor_or_Dealer__c = 'Dealer';
        ac.TaxID__c = 'testTax';
        insert ac;

        Recordtype rt = [
            SELECT Id
            FROM Recordtype
            WHERE Name = 'Service' AND SObjectType = 'Sales_Program__c'
        ];
        Sales_Program__c sp = new Sales_Program__c();
        sp.Customer__c = cac.Id;
        sp.Brands__c = 'EGO';
        sp.Freight_Term__c = 'PICK';
        sp.Approval_Status__c = 'Approved';
        sp.RecordTypeId = rt.Id;
        sp.Labor_Rate__c = 30;
        sp.sales_group__c = 'SG01';
        sp.Payment_Term__c= 'NA001';
        sp.Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.';
        sp.Authorized_Brand_Name_To_Oracle__c = 'test2';
        insert sp;

        Recordtype rt1 = [
            SELECT Id
            FROM Recordtype
            WHERE
                Name = 'Billing Address'
                AND SObjectType = 'Account_Address__c'
        ];
        Account_Address__c aa = new Account_Address__c();
        aa.Active__c = true;
        aa.City__c = 'US';
        aa.Country__c = 'PA';
        aa.Customer__c = cac.id;
        aa.Name = 'test';
        aa.RecordTypeId = rt1.Id;
        aa.Postal_Code__c = '20005';
        aa.State__c = 'US';
        aa.Address1__c = '11111';
        insert aa;

        Address_With_Program__c awp = new Address_With_Program__c();
        awp.Status__c = 'A';
        awp.Program__c = sp.id;
        awp.Account_Address__c = aa.id;
        insert awp;

        List<RecordType> proRecordType = [
            SELECT Id, Name
            FROM RecordType
            WHERE SObjectType = 'Product2' AND DeveloperName = 'Product'
        ];
        List<RecordType> kitRecordType = [
            SELECT Id, Name
            FROM RecordType
            WHERE SObjectType = 'Product2' AND DeveloperName = 'Kit'
        ];
        List<RecordType> partRecordType = [
            SELECT Id, Name
            FROM RecordType
            WHERE SObjectType = 'Product2' AND DeveloperName = 'Parts'
        ];

        Product2 pro = new Product2();
        pro.Name = 'Test';
        pro.Brand_Name__c = 'EGO';
        pro.RecordTypeId = proRecordType[0].Id;
        pro.ProductCode = '1234567';
        pro.IsActive = true;
        pro.Source__c = 'PIM';
        insert pro;

        Product2 pkit = new Product2();
        pkit.Name = 'Test';
        pkit.Brand_Name__c = 'EGO';
        pkit.RecordTypeId = kitRecordType[0].Id;
        pkit.ProductCode = '1234567';
        pkit.IsActive = true;
        pkit.Source__c = 'PIM';
        insert pkit;

        Product2 par = new Product2();
        par.Name = 'Test1';
        par.Brand_Name__c = 'EGO';
        par.RecordTypeId = partRecordType[0].Id;
        par.ProductCode = pro.ProductCode;
        par.IsActive = true;
        par.Source__c = 'PIM';
        insert par;

        Recordtype rtd = [
            SELECT id
            FROM RecordType
            WHERE Name = 'Products and Parts' AND sobjecttype = 'Kit_item__c'
            LIMIT 1
        ];
        Kit_item__c ki1 = new Kit_item__c();
        ki1.product__c = pro.Id;
        ki1.Parts__c = par.Id;
        ki1.RecordTypeId = rtd.id;
        ki1.Source__c = 'PIM';
        ki1.Repairable__c = true;

        insert ki1;

        Recordtype rtd2 = [
            SELECT id
            FROM RecordType
            WHERE Name = 'Kits and Products' AND sobjecttype = 'Kit_item__c'
            LIMIT 1
        ];
        Kit_item__c ki2 = new Kit_item__c();
        ki2.product__c = pro.Id;
        ki2.Kit__c = pkit.Id;
        ki2.RecordTypeId = rtd2.id;
        ki2.Source__c = 'PIM';
        ki2.Repairable__c = true;
        insert ki2;

        BOM__c bom = new BOM__c();
        bom.Product_code__c = pro.ProductCode;
        bom.Parts_code__c = pro.ProductCode;
        bom.Quantity__c = 1;
        insert bom;

        Storage__c sto = new Storage__c();
        sto.Product__c = pro.Id;
        sto.Available_Inventory__c = 2;
        sto.Sub_storage__c = 'CNA01';
        insert sto;

        Storage__c sto2 = new Storage__c();
        sto2.Product__c = par.Id;
        sto2.Available_Inventory__c = 2;
        sto2.Sub_storage__c = 'CNA01';
        insert sto2;

        Storage_List__c sl = new Storage_List__c();
        sl.Name = 'CNA101';
        sl.Postal_Code__c = '91708';
        insert sl;
        
        test.startTest();
        
        Pricebook2 pb = new Pricebook2();
        pb.IsActive = true;
        pb.Name = 'Test12345';
        insert pb;

        Customer_Brand_Pricebook_Mapping__c cbpm = new Customer_Brand_Pricebook_Mapping__c();
        cbpm.Type__c = 'Service';
        cbpm.Name = 'CNA-Direct Dealer Price for Parts';
        cbpm.Price_Book__c = pb.Id;
        insert cbpm;

        Default_PriceBook__c dpb = new Default_PriceBook__c();
        dpb.Price_Book_Id__c = pb.Id;
        dpb.Name = 'defaultBook1234567';
        dpb.DevelopName__c = 'Warranty and Sample price list';
        insert dpb;

        ID standardPBID = Test.getStandardPricebookId();

        /*PricebookEntry standardpbe = new PricebookEntry();
            standardpbe.Pricebook2Id = standardPBID;
            standardpbe.Product2Id = pro.Id;
            standardpbe.UnitPrice = 1000;
            standardpbe.IsActive = true;
            insert standardpbe;*/

        // PricebookEntry pbe = new PricebookEntry();
        // pbe.IsActive = true;
        // pbe.Product2Id = pro.Id;
        // pbe.UnitPrice = 1000;
        // pbe.Pricebook2Id = pb.Id;
        // pbe.UseStandardPrice = false;
        // insert pbe;

            Product2 pro3 = new Product2();
            pro3.Name = 'Test';
            pro3.Brand_Name__c = 'EGO';
            pro3.RecordTypeId = proRecordType[0].Id;
            pro3.ProductCode = '1234567';
            pro3.IsActive = true;
            insert pro3;
 
            ID standardPBID3 = Test.getStandardPricebookId();
   
            PricebookEntry standardpbe3 = new PricebookEntry();
            standardpbe3.Pricebook2Id = standardPBID3;
            standardpbe3.Product2Id = pro3.Id;
            standardpbe3.UnitPrice = 1000;
            standardpbe3.IsActive = true;
            insert standardpbe3;

        Warranty__c wrty = TestDataFactory.createWarranty();
        wrty.Master_Product__c = pro.Id;
        wrty.AccountCustomer__c = cac.Id;
        wrty.Brand_Name__c = 'EGO';
        wrty.Purchase_Date__c = Date.today();
        insert wrty;

        Warranty_Item__c wi = TestDataFactory.createWarrantyItem(wrty.Id);
        wi.Product__c = par.Id;
        wi.Serial_Number__c = 'NLM0219080001X';
        insert wi;

        System_Configuration__c sc = new System_Configuration__c();
        sc.Name = 'Claim Auto Approval';
        sc.Is_Active__c = true;
        insert sc;

        System_Configuration__c sc1 = new System_Configuration__c();
        sc1.Name = 'Maximum Cost Threshold';
        sc1.Is_Active__c = true;
        sc1.value__c = '100';
        insert sc1;

        CCM_NewClaim.customerInfo('<EMAIL>', 'test', 'test');
        CCM_NewClaim.SearchWarrantyBySerialNumber(
            null,
            'NLM0219080001X',
            null,
            'test',
            'test',
            wi.Product__c,
            false
        );
        CCM_NewClaim.SearchWarrantyBySerialNumber(
            cac.id,
            'NLM0219080001X',
            'EGO',
            null,
            null,
            wi.Product__c,
            false

        );
        Map<String, Object> partsMap = new Map<String, Object>();
        partsMap.put('partsId', par.Id);
        partsMap.put('price', 2.00);
        partsMap.put('quantity', 2);
        partsMap.put('total', '4');
        partsMap.put('LaborTime', 3);

        Map<String, Object> body = new Map<String, Object>();
        //body.put('userId', '0054F000001tX1UQAU');
        body.put('userId', cac.Id);
        body.put('labourHours', 0);
        body.put('partsList', '');
        body.put('repairDescription', 'test');
        body.put('overTimeHour', '0');
        body.put('partsCost',0);
        body.put('totalPrice','15');
        body.put('dropOfDate', '2019-12-03');
        body.put('repairDate', '2019-12-05');
        body.put('productId', par.Id);
        body.put('warrantyId', wrty.Id);
        body.put('customerId', cac.Id);
        body.put('laborCostSubtotal','0');
        body.put('serviceOption', 'repair');
        body.put('repairType', ' ');
        body.put('auditComments', ' ');
        body.put('recallOption', '');
        body.put('failureCode', ' ');
        body.put('project', ' ');
        body.put('repairableParts', '{}}');
        body.put('inventory', 'Chervon inventory');
        body.put('ShippingStreet', '13 oak drive');
        body.put('ShippingCity', 'Hamilton');
        body.put('ShippingCountry', 'US');
        body.put('ShippingState', 'NY');
        body.put('ShippingPostalCode', '13346');
        body.put('replacementFirstName', 'yizhao');
        body.put('replacementLastName', 'Xu');
        body.put('replacementPhone', '');
        body.put('claimId', '');
        body.put('diagnosisFee', 100);
        body.put('replacementBaseFee', 100);
        body.put('decPickupFeeSubtotal', 100);
        body.put('boolPickup', true);
        body.put('decPickupDistance',100);
        body.put('markup', 100);
       
        
        String contentStr = Json.serialize(body);
        CCM_NewClaim.SaveWarrantyClaim(contentStr);
        CCM_NewClaim.saveDraftWarrantyClaim(contentStr);
        CCM_NewClaim.GenerateProjectPicklistValue(pro.Id, cac.id, Date.today());
        CCM_NewClaim.GenerateFailureCodePicklistValue();
        
        Case c1 = new Case();
        c1.Warranty_Item__c = wi.Id;
        insert c1;

        // Warranty_Claim__c wc = [SELECT Id FROM Warranty_Claim__c LIMIT 1];

        Warranty_Claim__c wc = new Warranty_Claim__c(
            Customer__c = ac.Id,
            Case__c = c1.Id
        );
        insert wc;
        CCM_NewClaim.getDealerInfo();
        CCM_NewClaim.viewWarrantyClaim(wc.Id);
        CCM_NewClaim.SetupBomList(pro.Id, '');
        CCM_NewClaim.GenerateVersionList(pro.Id);
        test.stopTest();
    }
    @IsTest
    static void testShowMessageByInventory() {
        Test.startTest();
        Account objAccount = new Account(Name = 'test', RecordTypeId = CCM_Constants.CUSTOMER_RECORD_TYPE_CHANNEL_ID);
        objAccount.ORG_Code__c = 'CNA';
        objAccount.TaxID__c = 'testTax';
        insert objAccount;
        Contact objContact = new Contact(LastName = 'test', AccountId = objAccount.Id, Email = 'test@test' + Datetime.now().getTime() + '.com');
        insert objContact;
        Profile objProfile = [SELECT Id FROM Profile WHERE Name = :CCM_Constants.PROFILE_NAME_PARTNER_COMMUNITY_SERVICE];
         //User objPartner = TestDataFactory.communityUserSetUp(objContact, objProfile.Id);



        List<Product2> lstProduct = new List<Product2>{
            new Product2(Name = 'test2', ProductCode = 'test', Brand_Name__c = 'EGO', IsActive = true, Source__c = CCM_Constants.PRODUCT_SOURCE_EBS, RecordTypeId = CCM_Constants.PRODUCT_PRODUCT_RECORD_TYPE_ID),
            new Product2(Name = 'test1', ProductCode = 'test', Brand_Name__c = 'EGO', IsActive = true, Source__c = CCM_Constants.PRODUCT_SOURCE_PIM, RecordTypeId = CCM_Constants.PRODUCT_PRODUCT_RECORD_TYPE_ID)
        };
        insert lstProduct;

      

        Pricebook2 objPB = new Pricebook2(Name = 'test', IsActive = true);
        objPB.Org_Code__c = 'CNA';
        insert objPB;

        List<RecordType> proRecordType = [SELECT Id, Name FROM RecordType 
                                                WHERE SObjectType = 'Product2' AND DeveloperName = 'Product'];

        Product2 pro = new Product2();
            pro.Name = 'Test';
            pro.Brand_Name__c = 'EGO';
            pro.RecordTypeId = proRecordType[0].Id;
            pro.ProductCode = '1234567';
            pro.IsActive = true;
            insert pro;

            ID standardPBID = Test.getStandardPricebookId(); 
    
            PricebookEntry standardpbe = new PricebookEntry();
            standardpbe.Pricebook2Id = standardPBID;
            standardpbe.Product2Id = pro.Id;
            standardpbe.UnitPrice = 1000;
            standardpbe.IsActive = true;
            insert standardpbe;
       // insert new PricebookEntry(Pricebook2Id = objPB.Id, Product2Id = lstProduct[1].Id, CurrencyIsoCode = 'USD', UnitPrice = 123, IsActive = true);


        Customer_Brand_Pricebook_Mapping__c objCBPM = new Customer_Brand_Pricebook_Mapping__c(
            Price_Book__c = objPB.Id
        );
        insert objCBPM;
        List<Sales_Program__c> lstAuthBrand = new List<Sales_Program__c>{
            new Sales_Program__c(
                Brands__c = 'EGO',
                Approval_Status__c = CCM_Constants.SALES_PROGRAM_APPROVAL_STATUS_APPROVED,
                RecordTypeId = CCM_Constants.SALES_PROGRAM_RECORD_TYPE_STANDARD_ID,
                Customer__c = objAccount.Id,
                Price_Book_Mapping__c = objCBPM.Id,
                Authorized_Brand_Name_To_Oracle__c = 'Test Authorized Brand',
                ORG_Code__c = 'CNA',
                Payment_Term__c= 'NA001',
                Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.'
            )
        };
        insert lstAuthBrand;



       
        Profile profile1=[select ID from Profile where name='Partner Community' Limit 1];
        User user1=new User(
            Email='<EMAIL>',
            ProfileId=profile1.id,
            username='<EMAIL>',
            Alias='test',
            TimeZoneSidKey='America/New_York',
            EmailEncodingKey='ISO-8859-1',
            LocaleSidKey='en_US',
            ContactId=objContact.id,
            FirstName='firstName',
            LastName='LastName',
            LanguageLocaleKey='en_US'
        );

        insert user1;

       User objPartner = [SELECT id,Contact.AccountId FROM User WHERE isactive = true and Profile.name like '%Partner%' LIMIT 1];
        System.runAs(objPartner) {
            CCM_NewClaim.ShowMessageByInventory(objAccount.Id, 'Dealer inventory', lstProduct[0].Id);
            CCM_NewClaim.ShowMessageByInventory(objAccount.Id, null, null);            
        }
        Test.stopTest();
    }

    @Istest 
    static void testgetAllStatusPriceBookEntryByProdId(){
        List<Product2> entries =[SELECT id FROM Product2 
        WHERE  Name = 'Test' and 
        Brand_Name__c = 'EGO'];
        String prodId = entries.size() > 0 ? entries[0].Id : null;
        
        List<Pricebook2> pricebooks = [SELECT Id FROM Pricebook2 WHERE Name = 'Test'];
        String priceBookId = pricebooks.size() > 0 ? pricebooks[0].Id : null;
        CCM_NewClaim.getAllStatusPriceBookEntryByProdId( prodId,  priceBookId);
    }

    @istest 
    static void testgetTierByZipCode(){
        ZIP_Tiers__c zip1=new ZIP_Tiers__c();
        zip1.Business_Name__c='test-zip1';
        zip1.Zip__c='111111';
        zip1.Tier__c='Tier 1';
        insert zip1;
        CCM_NewClaim.getTierByZipCode('111111');
    }

    @istest 
    static void testMethod3(){
        //contact -> account -> user (partner community user);
        
        Account objAccount = new Account(Name = 'test', RecordTypeId = CCM_Constants.CUSTOMER_RECORD_TYPE_CHANNEL_ID);
        objAccount.ORG_Code__c = 'CNA';
        objAccount.TaxID__c = 'testTax';
        insert objAccount;
        Contact objContact = new Contact(LastName = 'test', AccountId = objAccount.Id, Email = 'test@test' + Datetime.now().getTime() + '.com');
        insert objContact;
        Profile objProfile = [SELECT Id FROM Profile WHERE Name = :CCM_Constants.PROFILE_NAME_PARTNER_COMMUNITY_SERVICE];
        User objPartner = TestDataFactory.communityUserSetUp(objContact, objProfile.Id);

        CCM_NewClaim.getDealerInfo();
    }

    
}