@isTest
public class CCM_PayPalUtilTest {
	@isTest
    static void testMethod1(){
        Test.setMock(HttpCalloutMock.class, new CCM_PaypalMock());
        CCM_PayPalUtil.BillingAddress ba = new CCM_PayPalUtil.BillingAddress('US','US','US','US', 'US', 'US', '60007');

        CCM_PayPalUtil.AuthorizeResponse ap = CCM_PayPalUtil.authorizePayPalPayment('5105105105105105100', '12/22', '111', 30.00, '', ba);
    }
    
    @isTest
    static void testMethod2(){
        Test.setMock(HttpCalloutMock.class, new CCM_PaypalMock());
        CCM_PayPalUtil.BillingAddress ba = new CCM_PayPalUtil.BillingAddress('US','US','US','US', 'US', 'US', '60007');

        CCM_PayPalUtil.CaptureResponse ap = CCM_PayPalUtil.capturePayPalPayment(30.00, '12321321321', '0012131');
    }
}