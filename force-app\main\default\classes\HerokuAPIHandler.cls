/**
* @Author: Zack
* @Date: 2018-07-17
* @Description:  
* @Test_Class: 
* @Related_Class: 
---------------------------------------------------
* @Last_Modified_by: 
* @Last_Modified_time: 
* @Modifiy_Purpose: 
*/
public abstract class HerokuAPIHandler {
    // 空参构造
    public HerokuAPIHandler(){}
    // 处理Post请求
    public abstract HerokuEntity.ResponseEntity handlePost(String requstBody);
    // 处理Get请求
    public abstract HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap);

    // 创建或更新用户处理类
    public class UpsertUserHandler extends Heroku<PERSON>IHandler {
        public override HerokuEntity.ResponseEntity handlePost(String requstBody){
            HerokuEntity.RCUserRequestEntity userRequest = (HerokuEntity.RCUserRequestEntity) JSON.deserialize(requstBody, HerokuEntity.RCUserRequestEntity.class);
            System.debug('----:' + userRequest.toString());

            Account changedUser;
            if(String.isNotEmpty(userRequest.customerId)){
                System.debug(LoggingLevel.INFO, '*** userRequest.customerId: ' + userRequest.customerId);
                changedUser = HerokuAPIUtils.getAccountByID(userRequest.customerId);
                System.debug('----User:' + changedUser);
                if (changedUser == null) {
                    return HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
                }
                String brand = userRequest.brandName;
                if (userRequest.firstName != null) {
                    changedUser.FirstName = userRequest.firstName;
                }
                if (userRequest.lastName != null) {
                    changedUser.LastName  = userRequest.lastName;
                }
                if (userRequest.emailAddress != null) {
                    changedUser.PersonEmail = userRequest.emailAddress;
                }
                if (userRequest.organizationName != null) {
                    changedUser.Organization_Name__c = userRequest.organizationName;
                }

                if(userRequest.customerType != null) {
                    changedUser.Customer_Type2__c = userRequest.customerType;
                }

                // Add by yujie for service migration, not including merge logic
                // if (userRequest.password != null) {
                //     changedUser.Password__c = userRequest.password;
                // }
                if (userRequest.emailAddress != null) {
                    changedUser.EGO_username__c = userRequest.emailAddress;
                    changedUser.Skil_username__c = userRequest.emailAddress;
                    changedUser.SkilSaw_username__c = userRequest.emailAddress;
                    changedUser.FLEX_username__c = userRequest.emailAddress;
                }
                if( 'EGO'.equalsIgnoreCase (/*changedUser.Product_Type__c*/brand) ) {
                    //changedUser.EGO_username__c          = userRequest.emailAddress;
                    if(userRequest.password != null){
                        changeduser.EGO_password__c          = userRequest.password;
                    }                    
                } else if( 'Skil'.equalsIgnoreCase (brand) ) {
                    //changedUser.Skil_username__c          = userRequest.emailAddress;
                    if(userRequest.password != null){
                        changeduser.Skil_password__c          = userRequest.password;
                    }                      
                } else if( 'SkilSaw'.equalsIgnoreCase (brand) ) {
                    //changedUser.SkilSaw_username__c          = userRequest.emailAddress;
                    if(userRequest.password != null){
                        changeduser.SkilSaw_password__c          = userRequest.password;
                    } 
                } else if( 'FLEX'.equalsIgnoreCase (brand) ) {
                    //changedUser.SkilSaw_username__c          = userRequest.emailAddress;
                    if(userRequest.password != null){
                        changeduser.FLEX_password__c          = userRequest.password;
                    }                                         
                } else {
                    return new HerokuEntity.ResponseEntity(422, 'The brand is not correct.');
                }
        
                // add by Daniel 2019/05/14
                if (userRequest.siteOrigin != null && userRequest.siteOrigin != '' ) {
                    //add by yujie for EU, 'United States' set by default value by record type
                    changedUser.Site_Origin__pc = userRequest.siteOrigin;
                    changedUser.Site_Origin__c = userRequest.siteOrigin;
                } 
                if (String.isNotEmpty(userRequest.trade)) {
                    changedUser.Trade__c = userRequest.trade;
                }
                if (String.isNotEmpty(userRequest.tradeother)) {
                    changedUser.Trade_Other__c = userRequest.tradeother;
                }
                if (userRequest.mobilePhone != null) {
                    String formattedPhone = HerokuAPIUtils.formatPhone(userRequest.mobilePhone);
                    changedUser.PersonMobilePhone = formattedPhone;
                    //add by yujie for EU                    
                    changedUser.Phone = formattedPhone;
                }
                if (userRequest.sendMarketingEmails != null) {
                    changedUser.send_marketing_emails__c = userRequest.sendMarketingEmails;
                }
                // add by yujie for EU 2019/6/17
                if (userRequest.address1 != null) {
                    //changedUser.ADDRESS1__pc = userRequest.address1;
                    changedUser.ShippingStreet = userRequest.address1;
                }
                if (userRequest.address2 != null) {
                    changedUser.ShippingCity = userRequest.address2;
                }
                if (userRequest.address3 != null) {
                    changedUser.ShippingState = userRequest.address3;
                }
                if (userRequest.postcode != null) {
                    changedUser.ShippingPostalCode = userRequest.postcode;
                }
                if (userRequest.country != null) {
                    //modify by yujie from Emma's request in UAT 2019/8/21
                    changedUser.ShippingCountry = userRequest.country;
                    //changedUser.ShippingCountry = (changedUser.Site_Origin__pc.split('-'))[0];
                }
                if (userRequest.company != null) {
                    changedUser.Company__pc = userRequest.company;
                }
                if (userRequest.marketingOptIn != null) {
                    changedUser.MarketingOptIn__pc = userRequest.marketingOptIn;
                }
            } else {
                //获取客户的记录类型
                
                //Add by yujie for service migration
                //String recTypeId = QueryUtils.getRecordTypeIdBySobjectTypeAndDeveloperName('Account', userRequest.brandName + '_Customer');
                String recTypeId = QueryUtils.getRecordTypeIdBySobjectTypeAndDeveloperName('Account', 'PersonAccount');
                
                if (recTypeId == null) {
                    return new HerokuEntity.ResponseEntity(422, 'Can not find the record type according to the request information.');
                }
                String emailStr = userRequest.emailAddress;
                System.debug(LoggingLevel.INFO, '*** emailStr: ' + emailStr);
                String pWord = userRequest.password;
                if(String.isBlank(pWord)){
                     return new HerokuEntity.ResponseEntity(422, 'Password can not be empty.');
                }
                if(String.isNotBlank(emailStr)){
                    List<Account> acclist = [select id,Product_Type__c from Account where PersonEmail = :emailStr];
                    System.debug(LoggingLevel.INFO, '*** Json.serialize(acclist): ' + Json.serialize(acclist));
                    if(acclist.size() > 0){
                        changedUser = acclist.get(0); 
                        String pType = changedUser.Product_Type__c;
                        String[] pTypeArr = pType.split(';');
                        List<String> pTypeList = new List<String>();
                        for(String p : pTypeArr){
                            if(String.isNotBlank(p)){
                                pTypeList.add(p.toLowerCase()); 
                            }                           
                        }
                        if(String.isNotBlank(userRequest.brandName) && !pTypeList.contains(userRequest.brandName.toLowerCase())){
                            changedUser.Product_Type__c = pType + ';' + userRequest.brandName;
                            if( 'EGO'.equalsIgnoreCase (userRequest.brandName) ) {
                                changedUser.EGO_username__c          = userRequest.emailAddress;
                                changeduser.EGO_password__c          = userRequest.password;
                            } else if( 'Skil'.equalsIgnoreCase (userRequest.brandName) ) {
                                changedUser.Skil_username__c          = userRequest.emailAddress;
                                changeduser.Skil_password__c          = userRequest.password;
                            } else if( 'SkilSaw'.equalsIgnoreCase (userRequest.brandName) ) {
                                changedUser.SkilSaw_username__c          = userRequest.emailAddress;
                                changeduser.SkilSaw_password__c          = userRequest.password;
                            } else if( 'FLEX'.equalsIgnoreCase (userRequest.brandName) ) {
                                changedUser.FLEX_username__c          = userRequest.emailAddress;
                                changeduser.FLEX_password__c          = userRequest.password;
                            } else {
                                return new HerokuEntity.ResponseEntity(422, 'The brand is not correct.');
                            }
                            if (String.isNotEmpty(userRequest.trade)) {
                                changedUser.Trade__c = userRequest.trade;
                            }
                            if (String.isNotEmpty(userRequest.tradeother)) {
                                changedUser.Trade_Other__c = userRequest.tradeother;
                            }
                            if (userRequest.firstName != null) {
                                changedUser.FirstName = userRequest.firstName;
                            }
                            if (userRequest.lastName != null) {
                                changedUser.LastName  = userRequest.lastName;
                            }

                            if (userRequest.organizationName != null) {
                                changedUser.Organization_Name__c  = userRequest.organizationName;
                            }

                            if(userRequest.customerType != null) {
                                changedUser.Customer_Type2__c = userRequest.customerType; 
                            }

                            if (userRequest.siteOrigin != null && userRequest.siteOrigin != '' ) {
                                //add by yujie for EU, 'United States' set by default value by record type
                                changedUser.Site_Origin__pc = userRequest.siteOrigin;
                                changedUser.Site_Origin__c = userRequest.siteOrigin;
                            } 

                            if (userRequest.mobilePhone != null) {
                                String formattedPhone = HerokuAPIUtils.formatPhone(userRequest.mobilePhone);
                                changedUser.PersonMobilePhone = formattedPhone;
                                //add by yujie for EU                    
                                changedUser.Phone = formattedPhone;
                            }
                            if (userRequest.sendMarketingEmails != null) {
                                changedUser.send_marketing_emails__c = userRequest.sendMarketingEmails;
                            }
                            // add by yujie for EU 2019/6/17
                            if (userRequest.address1 != null) {
                                //changedUser.ADDRESS1__pc = userRequest.address1;
                                changedUser.ShippingStreet = userRequest.address1;
                            }
                            if (userRequest.address2 != null) {
                                changedUser.ShippingCity = userRequest.address2;
                            }
                            if (userRequest.address3 != null) {
                                changedUser.ShippingState = userRequest.address3;
                            }
                            if (userRequest.postcode != null) {
                                changedUser.ShippingPostalCode = userRequest.postcode;
                            }
                            if (userRequest.country != null) {
                                //modify by yujie from Emma's request in UAT 2019/8/21
                                changedUser.ShippingCountry = userRequest.country;
                                //changedUser.ShippingCountry = (changedUser.Site_Origin__pc.split('-'))[0];
                            }
                            if (userRequest.company != null) {
                                changedUser.Company__pc = userRequest.company;
                            }
                            if (userRequest.marketingOptIn != null) {
                                changedUser.MarketingOptIn__pc = userRequest.marketingOptIn;
                            }
                        }else{
                            return new HerokuEntity.ResponseEntity(422, 'The brand is empty or the account has been registered.');
                        } 
                    }else{
                        changedUser = new Account();
                        changedUser.RecordTypeId             = recTypeId;
                        // changedUser.Brand__c                 = userRequest.brandName;
                        
                        // Add by yujie for service migration
                        changedUser.Product_Type__c                 = userRequest.brandName;

                        changedUser.FirstName                = userRequest.firstName;
                        changedUser.LastName                 = userRequest.lastName;

                        if(userRequest.organizationName != null) {
                            changedUser.Organization_Name__c = userRequest.organizationName;
                        }

                        changedUser.PersonEmail              = userRequest.emailAddress;

                        if(userRequest.customerType != null) {
                            changedUser.Customer_Type2__c = userRequest.customerType; 
                        }
                        
                        // Add by yujie for service migration, not including merge logic
                        //changedUser.Password__c              = userRequest.password;
                        //changedUser.EGO_username__c          = userRequest.emailAddress;
                        //changedUser.Skil_username__c          = userRequest.emailAddress;
                        //changedUser.SkilSaw_username__c          = userRequest.emailAddress;
                        if( 'EGO'.equalsIgnoreCase (userRequest.brandName) ) {
                            changedUser.EGO_username__c          = userRequest.emailAddress;
                            changeduser.EGO_password__c          = userRequest.password;
                        } else if( 'Skil'.equalsIgnoreCase (userRequest.brandName) ) {
                            changedUser.Skil_username__c          = userRequest.emailAddress;
                            changeduser.Skil_password__c          = userRequest.password;
                        } else if( 'SkilSaw'.equalsIgnoreCase (userRequest.brandName) ) {
                            changedUser.SkilSaw_username__c          = userRequest.emailAddress;
                            changeduser.SkilSaw_password__c          = userRequest.password;
                        } else if( 'FLEX'.equalsIgnoreCase (userRequest.brandName) ) {
                                changedUser.FLEX_username__c          = userRequest.emailAddress;
                                changeduser.FLEX_password__c          = userRequest.password;
                        } else {
                            return new HerokuEntity.ResponseEntity(422, 'The brand is not correct.');
                        }
                        if (String.isNotEmpty(userRequest.trade)) {
                            changedUser.Trade__c = userRequest.trade;
                        }
                        if (String.isNotEmpty(userRequest.tradeother)) {
                            changedUser.Trade_Other__c = userRequest.tradeother;
                        }
                        // add by Daniel 2019/05/14
                        if( userRequest.siteOrigin != null && userRequest.siteOrigin != '' ) {
                            //add by yujie for EU, 'United States' set by default value by record type
                            changedUser.Site_Origin__pc          = userRequest.siteOrigin;
                            changedUser.Site_Origin__c          = userRequest.siteOrigin;
                        } else {
                            //created default United States
                            changedUser.Site_Origin__pc          = 'United States';
                            changedUser.Site_Origin__c          = 'United States';
                        }

                        changedUser.PersonMobilePhone        = userRequest.mobilePhone;
                        changedUser.Phone                    = userRequest.mobilePhone;
                        changedUser.send_marketing_emails__c = userRequest.sendMarketingEmails;
                
                        // add by YuJie for EU 2019/6/17
                        //changedUser.ADDRESS1__pc = userRequest.address1;
                        if (userRequest.address1 != null) {
                            changedUser.ShippingStreet = userRequest.address1;
                        }
                        if (userRequest.address2 != null) {
                            changedUser.ShippingCity = userRequest.address2;
                        }
                        if (userRequest.address3 != null) {
                            changedUser.ShippingState = userRequest.address3;
                        }
                        if (userRequest.postcode != null) {
                            changedUser.ShippingPostalCode = userRequest.postcode;
                        }
                        if (userRequest.country != null) {
                            //modify by yujie from Emma's request in UAT 2019/8/21
                            changedUser.ShippingCountry = userRequest.country;
                            //changedUser.ShippingCountry = (changedUser.Site_Origin__pc.split('-'))[0];
                        }
                        if (userRequest.company != null) {
                            changedUser.Company__pc = userRequest.company;
                        }
                        if(userRequest.marketingOptIn != null) {
                            changedUser.MarketingOptIn__pc = userRequest.marketingOptIn;
                        }
                    }
                }
            }            

            try {
                upsert changedUser;
            } catch (Exception e) {
                System.debug(LoggingLevel.INFO, '*** e.getMessage(): ' + e.getMessage());
                return HerokuAPIUtils.upsertFailedResponse(e.getMessage());
            }

            //返回正确结果，用户的sfid
            return new HerokuEntity.RCUserResponseEntity(200, '', changedUser.Id);
        }

        public override HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            return null;
        }
    }

    // 查询用户处理类
    public class SelectUserByEmailHandler extends HerokuAPIHandler {
        public override HerokuEntity.ResponseEntity handlePost(String requstBody){
            return null;
        }

        public override HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            String emailAddress = headerMap.get('emailAddress');
            String brandName = headerMap.get('brandName');
            if (String.isEmpty(emailAddress)/* || String.isEmpty(brandName)*/) {
                return HerokuAPIUtils.getErrorResponse(422, 'Email or brand_name is missing.');
            }
            Account acc = HerokuAPIUtils.getAccountByEmailAndBrandName(emailAddress, brandName);
            if (acc == null) {
                return HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }

            //返回正确结果，用户的sfid
            // Add by yujie for service migration, not include merge logic 
            String password1;
            if( 'EGO'.equalsIgnoreCase (brandName) ) {
                password1 = acc.EGO_password__c;
            } else if( 'Skil'.equalsIgnoreCase (brandName) ) {
                password1 = acc.Skil_password__c;
            } else if( 'SkilSaw'.equalsIgnoreCase (brandName) ) {
                password1 = acc.SkilSaw_password__c;
            } else if( 'FLEX'.equalsIgnoreCase (brandName) ) {
                password1 = acc.FLEX_password__c;
            } else if( String.isBlank(brandName)){
                password1 = '';
            }else {
                return new HerokuEntity.ResponseEntity(422, 'The brand is not correct.');
            }
            System.debug('----:' + (acc.Id + ';' + password1));
            return new HerokuEntity.RCUserResponseEntity(200, '', (acc.Id + ';' + password1));
        }
    }


    // 该接口用于IoT中获取用户数据
    public class GetUserDataByEamilHandler extends HerokuAPIHandler {
        public override HerokuEntity.ResponseEntity handlePost(String requstBody){
            return null;
        }

        public override HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            String emailAddress = headerMap.get('emailAddress');
            String brandName = headerMap.get('brandName');
            if (String.isEmpty(emailAddress) || String.isEmpty(brandName)) {
                return HerokuAPIUtils.getErrorResponse(422, 'Email or brand_name is missing.');
            }

            Account acc = HerokuAPIUtils.getAccountByEmailAndBrandName(emailAddress, brandName);
            if (acc == null) {
                return HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }

            //返回正确结果，用户的sfid
            System.debug('----:' + (acc.Id));
            return new HerokuEntity.RCUserDataResponseEntity(200, '', acc);
        }
    }

    //该接口用于R2C网站中获取用户数据
    public class GetUserDataByIdHandler extends HerokuAPIHandler {
        public override HerokuEntity.ResponseEntity handlePost(String requstBody){
            return null;
        }

        public override HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            String customerId = headerMap.get('customerId');
            if (String.isEmpty(customerId)) {
                return HerokuAPIUtils.getErrorResponse(422, 'customerId is missing.');
            }

            Account acc = HerokuAPIUtils.getAccountByID(customerId);
            if (acc == null) {
                return HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }

            //返回正确结果，用户的sfid
            System.debug('----:' + (acc.Id));
            return new HerokuEntity.RCUserDataResponseEntity(200, '', acc);
        }
    }
    
    //该接口用于EU网站中获取用户数据
    public class GetUserInfoByIdHandler extends HerokuAPIHandler {
        public override HerokuEntity.ResponseEntity handlePost(String requstBody){
            return null;
        }

        public override HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            String customerId = headerMap.get('customerId');
            if (String.isEmpty(customerId)) {
                return HerokuAPIUtils.getErrorResponse(422, 'customerId is missing.');
            }

            Account acc = HerokuAPIUtils.getAccountByID(customerId);
            acc.EGO_password__c = null;
            if (acc == null) {
                return HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }

            //返回正确结果，用户的sfid
            System.debug('----:' + (acc.Id));
            return new HerokuEntity.RCUserInfoResponseEntity(200, '', acc);
        }
    }

    // 查询用户处理类
    public class SelectUserByIdHandler extends HerokuAPIHandler {
        public override HerokuEntity.ResponseEntity handlePost(String requstBody){
            return null;
        }

        public override HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            String customerId = headerMap.get('customerId');
            if (String.isEmpty(customerId)) {
                return HerokuAPIUtils.getErrorResponse(422, 'CustomerID cannot be empty.');          
            }

            Account acc = HerokuAPIUtils.getAccountByID(customerId);
            if (acc == null) {
                return HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }

            //返回正确结果，用户的sfid
            return new HerokuEntity.RCUserResponseEntity(200, '', acc.Id);
        }
    }

    // 查询用户名下的Order处理类
    public class SelectOrderHandler extends HerokuAPIHandler {
        public override HerokuEntity.ResponseEntity handlePost(String requstBody){
            return null;
        }

        public override HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap) {
            String accountId = headerMap.get('customerId');
            if (String.isEmpty(accountId)) {
                return HerokuAPIUtils.getErrorResponse(422, 'CustomerID cannot be empty.');          
            }

            Account acc = HerokuAPIUtils.getAccountByID(accountId);
            if (acc == null) {
                return HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }
            // 查询ProductCode与Serial Number的匹配关系
            Map<String,String> proCodeAndSNMap = new Map<String,String>();
            for(Warranty__c war:[Select Id, (select Id,Serial_Number__c,Product__r.ProductCode 
                                            from Warranty_Items__r) 
                                from Warranty__c 
                                where AccountCustomer__r.Id =:accountId]) {
                for(Warranty_Item__c wItem : war.Warranty_Items__r) {
                    proCodeAndSNMap.put(wItem.Product__r.ProductCode, wItem.Serial_Number__c);
                }
            }

            // 封装OrderItemList
            HerokuEntity.RCOrderItemEntity orderItem;
            List<HerokuEntity.RCOrderItemEntity> orderItemList = new List<HerokuEntity.RCOrderItemEntity>();
            // 查询出OrderItem并封装成List集合
            for(OrderItem oi:[Select Id, PricebookEntry.Product2.Item_Number__c, Order.EffectiveDate,
                                Order.Is_Cancelled__c, PricebookEntry.Product2.Name, Order.PO_No__c,
                                Order.Tracking_Status__c,OrderItemNumber, Order.Tracking_Number__c,
                                PricebookEntry.ProductCode, Order.ActivatedDate
                              from OrderItem
                              where Order.Account.Id =:accountId]) {
                orderItem                    = new HerokuEntity.RCOrderItemEntity();
                orderItem.orderItemNumber    = oi.OrderItemNumber;
                orderItem.orderDate          = String.valueOf(oi.Order.EffectiveDate);
                orderItem.productName        = oi.PricebookEntry.Product2.Name;
                orderItem.productModelNumber = oi.PricebookEntry.ProductCode;
                orderItem.serialNumber       = proCodeAndSNMap.get(oi.PricebookEntry.ProductCode);
                orderItem.status             = oi.Order.Is_Cancelled__c?'Cancelled':(oi.Order.ActivatedDate==null||oi.Order.Tracking_Number__c==null)?'Waiting':'Success';
                orderItem.trackingNumber     = oi.Order.Tracking_Number__c;
                orderItemList.add(orderItem);
            }

            //返回正确结果，orderItemList
            return new HerokuEntity.RCOrderResponseEntity(200, '', orderItemList);
        }
    }

    // 创建Case处理类
    public class UpsertCaseHandler extends HerokuAPIHandler {
        public override HerokuEntity.ResponseEntity handlePost(String requestBody) {
            HerokuEntity.RCCaseRequestEntity caseRequest = (HerokuEntity.RCCaseRequestEntity) JSON.deserialize(requestBody, HerokuEntity.RCCaseRequestEntity.class);
            System.debug(LoggingLevel.INFO, '*** Json.serialize(caseRequest): ' + Json.serialize(caseRequest));
            //获取客户的记录类型
            Account acc = HerokuAPIUtils.getAccountByID(caseRequest.customerId);
            if (String.isEmpty(caseRequest.customerId) || acc == null) {
                return HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }

            //hervon中的user转化为客户对象
            Case newCase             = new Case();
            newCase.Origin           = 'Web';
            newCase.Subject          = caseRequest.caseType;
            newCase.AccountId        = caseRequest.customerId;
            newCase.Description      = caseRequest.description;
            newCase.AttachmentURL__c = caseRequest.attachmentUrl;
            newCase.SuppliedPhone    = caseRequest.phoneNumber;
            newCase.Case_Type__c     = caseRequest.caseType;
            newCase.ContactId        = acc.PersonContactId;
            newCase.Phone_Number__c  = caseRequest.phoneNumber;
            newCase.Brand_Name__c    = caseRequest.brandName;

            //Start: add by John Jiang 2019-07-25 Fixed record type is null issue
            if(newCase.RecordTypeId == null){
                List<RecordType> rtList = [SELECT Id FROM RecordType WHERE Name = 'General'];

                if(rtList.size() > 0){
                    newCase.RecordTypeId = rtList[0].Id;
                }
                
            }
            //End: add by John Jiang 2019-07-25 Fixed record type is null issue

            upsert newCase;

            //返回正确结果，用户的sfid
            return new HerokuEntity.RCCaseResponseEntity(200, '', newCase.Id);
        }

        public override HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            return null;
        }
    }

    // 查询Case处理类
    public class SelectCaseHandler extends HerokuAPIHandler {
        public override HerokuEntity.ResponseEntity handlePost(String requstBody){
            return null;
        }

        public override HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap) {
            String customerId = headerMap.get('customerId');
            String brandName = headerMap.get('brandName');
            if (String.isEmpty(customerId)) {
                return HerokuAPIUtils.getErrorResponse(422, 'CustomerID cannot be empty.');          
            }
            if (String.isEmpty(brandName)) {
                return HerokuAPIUtils.getErrorResponse(422, 'BrandName cannot be empty.');          
            }

            //获取客户
            Account acc = HerokuAPIUtils.getAccountByID(customerId);
            if (acc == null) {
                return HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }

            //封装CaseItemList
            HerokuEntity.RCCaseItemEntity csitem;
            List<HerokuEntity.RCCaseItemEntity> caseItemList = new List<HerokuEntity.RCCaseItemEntity>();
            for(Case cs:[Select Id, CaseNumber, Case_Type__c, Subject, Status, Description 
                        from Case 
                        where Account.Id =:customerId And Brand_Name__c =:brandName]) {
                csitem = new HerokuEntity.RCCaseItemEntity();
                csitem.status = cs.Status;
                csitem.case_type = cs.Case_Type__c;
                csitem.description = cs.Description;
                csitem.case_number = cs.CaseNumber;
                csitem.case_id = cs.Id;
                caseItemList.add(csitem);
            }

            //返回正确结果，caseItemList
            return new HerokuEntity.RCCaseListResponseEntity(200, '', caseItemList);
        }
    } 

    // 查询CaseType处理类
    public class SelectCaseTypeHandler extends HerokuAPIHandler {
        public override HerokuEntity.ResponseEntity handlePost(String requstBody){
            return null;
        }

        public override HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap) {
            String customerId = headerMap.get('customerId');
            if (String.isEmpty(customerId)) {
                return HerokuAPIUtils.getErrorResponse(422, 'CustomerID cannot be empty.');          
            }

             //获取客户
            Account acc = HerokuAPIUtils.getAccountByID(customerId);
            if (acc == null) {
                return HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }
            
            // 查询CaseType的值
            Schema.DescribeFieldResult cType = Schema.SObjectType.Case.fields.Case_Type__c;

            //封装CaseTypeList
            String[] caseTypeList = new List<String>();
            for(Schema.PicklistEntry entry : cType.getPicklistValues()) {
                caseTypeList.add(entry.getValue());
            }

            //返回正确结果，caseTypeList
            return new HerokuEntity.RCCaseTypeListResponseEntity(200, '', caseTypeList);
        }
    } 

    // CloseCase处理类
    public class CloseCaseHandler extends HerokuAPIHandler {
        public override HerokuEntity.ResponseEntity handlePost(String requstBody){
            return null;
        }

        public override HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap) {
            String case_id = headerMap.get('caseId');
            String customerId = headerMap.get('customerId');
            if (String.isEmpty(case_id) || String.isEmpty(customerId)) {
                return HerokuAPIUtils.getErrorResponse(422, 'Case_id or customer_id is missing.');
            }

            List<Case> css = new List<Case>();
            for(Case cs:[Select Status, CaseNumber, Id, AccountId from Case where Id =:case_id]) {
                css.add(cs);
            }

            if(css.size() == 0) {
                return new HerokuEntity.RCChangeCaseStatusResponseEntity(422, 'Error: Can not find the case.', case_id);
            } else {
                Case cs = css.get(0);
                if (cs.AccountId != customerId) {
                    return HerokuAPIUtils.getErrorResponse(4403, 'This case doesn\'t belong to this account.');
                }
                cs.Status = 'Closed';
                Database.SaveResult rt = Database.update(cs);
                if (rt.isSuccess()) {
                    return new HerokuEntity.RCChangeCaseStatusResponseEntity(200, '', case_id);
                } else {
                    return new HerokuEntity.RCChangeCaseStatusResponseEntity(500, 'Error: Failed to close case, please Contact manager.', case_id);  
                }
            }
        }
    } 

    // ReopenCase处理类
    public class ReopenCaseHandler extends HerokuAPIHandler {
        public override HerokuEntity.ResponseEntity handlePost(String requstBody){
            return null;
        }

        public override HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap) {
            String case_id = headerMap.get('caseId');
            String customerId = headerMap.get('customerId');
            if (String.isEmpty(case_id) || String.isEmpty(customerId)) {
                return HerokuAPIUtils.getErrorResponse(422, 'Case_id or customer_id is missing.');
            }

            Case reopenCase = null;
            for(Case cs:[SELECT Status, Id, CaseNumber, AccountId FROM Case WHERE Id =:case_id]) {
                reopenCase = cs;
                break;
            }
            if(reopenCase == null) {
                return HerokuAPIUtils.getErrorResponse(422, 'Error: Can not find case by this id \'' + case_id + '\'');
            } else {
                if (reopenCase.AccountId != customerId) {
                    return HerokuAPIUtils.getErrorResponse(4403, 'This case doesn\'t belong to this account.');
                }
                reopenCase.Status = 'Reopen';
                Database.SaveResult srt = Database.update(reopenCase);
                if (srt.isSuccess()) {
                    return new HerokuEntity.RCChangeCaseStatusResponseEntity(200, '', case_id);
                } else {
                    return new HerokuEntity.RCChangeCaseStatusResponseEntity(500, 'Error: Failed to reopen case, please Contact manager.', case_id);  
                }
            }
        }
    } 

    // 注册warranty处理类
    public class RegisterWarrantyHandler extends HerokuAPIHandler {
        public override HerokuEntity.ResponseEntity handlePost(String requestBody) {
            HerokuEntity.RCRegisterWarrantyRequestEntity regWarRequest = (HerokuEntity.RCRegisterWarrantyRequestEntity) JSON.deserialize(requestBody, HerokuEntity.RCRegisterWarrantyRequestEntity.class);
        
            Account acc = HerokuAPIUtils.getAccountByID(regWarRequest.customerId);
            if (String.isEmpty(regWarRequest.customerId) || acc == null) {
                return HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }
            system.debug(regWarRequest);
            Boolean flag          = false;
            Warranty__c wt        = new Warranty__c();
            wt.AccountCustomer__c = acc.Id;
            // Add by yujie for service migration, not include merge logic
            wt.Purchase_Date__c   = (String.isNotEmpty(regWarRequest.purchaseDate) && regWarRequest.purchaseDate.length() == 6)
                                    ? Date.valueOf('20' + regWarRequest.purchaseDate.substring(4, 6) + '-' +
                                    regWarRequest.purchaseDate.substring(0, 2) + '-' +
                                    regWarRequest.purchaseDate.substring(2, 4)) : null;
            // 查询主产品
            //Product2 masterProduct = ProductService.getProduct2BySiteOrignAndCode('United States', regWarRequest.masterModelNumber);
            //Add by yujie for EU
            // 2022-07-05: 美国官网注册的产品，欧洲客户全部挂美国
            String country = 'United States';
            if(acc.Site_Origin__pc == 'Australia' || acc.Site_Origin__pc == 'New Zealand'){
                country = 'Australia'; //Australia or New Zealand
            }

            system.debug(regWarRequest.masterModelNumber);
            system.debug(country);
            Product2 masterProduct = ProductService.getProduct2BySiteOrignAndCode(country, regWarRequest.masterModelNumber);            
            System.debug(LoggingLevel.INFO, '*** masterProduct: ' + Json.serialize(masterProduct));
            // 封装warrantyItem
            List<Warranty_Item__c> warrantyItemList = new List<Warranty_Item__c>();
            if(masterProduct != null) {
                wt.Master_Product__c = masterProduct.Id;
                wt.Brand_Name__c      = masterProduct.Brand_Name__c;

                HerokuEntity.RCWarrantyItemEntity[] warrantyItemArr = regWarRequest.warrantyItems;
                System.debug(LoggingLevel.INFO, '*** HerokuAPIHandler-Json.serialize(warrantyItemArr): ' + Json.serialize(warrantyItemArr));
                System.debug(LoggingLevel.INFO, '*** Json.serialize(masterProduct.Kit_Items__r): ' + Json.serialize(masterProduct.Kit_Items__r));
                //Add by yujie for EU
                Integer i = 1;


                Map<String,List<sequenceWapper>> sequenceMap = new Map<String,List<sequenceWapper>>();

                for(Kit_Item__c kitItem : masterProduct.Kit_Items__r) {
                    List<Product2> items = [SELECT Type__c, ProductCode, Name, ProductModel__c, Product_Type__c FROM Product2 WHERE Id = :kitItem.Product__c];
                    Product2 findItem = null;
                    if(!items.isEmpty()) {
                        findItem = items[0];
                    }
                    HerokuEntity.RCWarrantyItemEntity rsWarrantyItem = null;
                    for (Integer index = 0; index < warrantyItemArr.size() ; index++) {
                        if (warrantyItemArr[index].productModelNumber == kitItem.Product_Code__c) {
                            rsWarrantyItem = warrantyItemArr[index];
                            warrantyItemArr.remove(index);
                            break;
                        }
                    }
                    
                    if(String.isNotBlank(kitItem.Sequence__c)){
                        if(sequenceMap.containsKey(kitItem.Sequence__c)){
                            sequenceWapper seq = new sequenceWapper();
                            seq.productCode = kitItem.Product__c;
                            seq.SNumber = '';
                            if(rsWarrantyItem != null && String.isNotEmpty(rsWarrantyItem.serialNumber) && rsWarrantyItem.serialNumber != 'null'){
                                seq.SNumber = rsWarrantyItem.serialNumber;     
                            }
                            sequenceMap.get(kitItem.Sequence__c).add(seq);
                        }else{
                            List<sequenceWapper> seqlist = new List<sequenceWapper>();
                            sequenceWapper seq = new sequenceWapper();
                            seq.productCode = kitItem.Product__c;
                            seq.SNumber = '';
                            if(rsWarrantyItem != null && String.isNotEmpty(rsWarrantyItem.serialNumber) && rsWarrantyItem.serialNumber != 'null'){
                                seq.SNumber = rsWarrantyItem.serialNumber;     
                            }
                            seqlist.add(seq);
                            sequenceMap.put(kitItem.Sequence__c, seqlist);
                        }
                    }
                    
                

                    Warranty_Item__c warrantyItem   = new Warranty_Item__c();
                    warrantyItem.Product__c         = kitItem.Product__c;
                    warrantyItem.Product_Type__c = findItem.Type__c;
                    warrantyItem.Product_Code__c = findItem.ProductCode;
                    warrantyItem.Product_Name__c = findItem.Name;
                    warrantyItem.Product_Model__c = findItem.ProductModel__c;
                    System.debug(LoggingLevel.INFO, '*** Json.serialize(rsWarrantyItem): ' + Json.serialize(rsWarrantyItem));
                    System.debug(LoggingLevel.INFO, '*** rsWarrantyItem: ' + rsWarrantyItem);
                    if(rsWarrantyItem != null && String.isNotEmpty(rsWarrantyItem.serialNumber) && rsWarrantyItem.serialNumber != 'null') {
                        if(findItem.Product_Type__c == 'Product') {
                            warrantyItem.Serial_Number__c = rsWarrantyItem.serialNumber;
                        } else if(findItem.Product_Type__c == 'Charger') {
                            warrantyItem.Serial_Number__c = rsWarrantyItem.serialNumber;
                        } else if(findItem.Product_Type__c == 'Battery') {
                            warrantyItem.Serial_Number__c = rsWarrantyItem.serialNumber;
                        }
                        warrantyItemList.add(warrantyItem);
                    }

                    //Add by yujie for EU
                    if( i== 1 ) {
                        wt.Component_1_Name__c = warrantyItem.Product_Name__c;
                        wt.Component_1_Serial_Number__c = warrantyItem.Serial_Number__c;
                        wt.Component_1_Model_Number__c = warrantyItem.Product_Code__c;
                    } 
                    if( i== 2 ) {
                        wt.Component_2_Name__c = warrantyItem.Product_Name__c;
                        wt.Component_2_Serial_Number__c = warrantyItem.Serial_Number__c;
                        wt.Component_2_Model_Number__c = warrantyItem.Product_Code__c;
                    } 
                    if( i== 3 ) {
                        wt.Component_3_Name__c = warrantyItem.Product_Name__c;
                        wt.Component_3_Serial_Number__c = warrantyItem.Serial_Number__c;
                        wt.Component_3_Model_Number__c = warrantyItem.Product_Code__c;
                    } 
                    if( i== 4 ) {
                        wt.Component_4_Name__c = warrantyItem.Product_Name__c;
                        wt.Component_4_Serial_Number__c = warrantyItem.Serial_Number__c;
                        wt.Component_4_Model_Number__c = warrantyItem.Product_Code__c;
                    } 
                    if( i== 5 ) {
                        wt.Component_5_Name__c = warrantyItem.Product_Name__c;
                        wt.Component_5_Serial_Number__c = warrantyItem.Serial_Number__c;
                        wt.Component_5_Model_Number__c = warrantyItem.Product_Code__c;
                    } 
                    if( i== 6 ) {
                        wt.Component_6_Name__c = warrantyItem.Product_Name__c;
                        wt.Component_6_Serial_Number__c = warrantyItem.Serial_Number__c;
                        wt.Component_6_Model_Number__c = warrantyItem.Product_Code__c;
                    } 
                    i++;
                }
                
                List<String> delProCode = new List<String>();
                for(String s : sequenceMap.keySet()){
                    List<sequenceWapper> seqList = sequenceMap.get(s);
                    for(sequenceWapper seq : seqList){
                        Integer delCount = 0;
                        Integer delAble = seqList.size()-1;
                        if(String.isBlank(seq.SNumber) && delCount < delAble){
                            delProCode.add(seq.productCode); 
                            delCount++;
                        }
                    }
                }

                for(Integer i2=0; i2<warrantyItemList.size(); i2++){
                    if(delProCode.contains(warrantyItemList.get(i2).Product__c)){
                        warrantyItemList.remove(i2);    
                    }
                }

            }

            

            wt.Product_Model_Number__c = regWarRequest.masterModelNumber;
            wt.Product_Use_Type__c = regWarRequest.productUseType;
            wt.Product_Use_Type2__c = ((regWarRequest.productUseType.contains('mmercial') || 
                                        regWarRequest.productUseType.contains('rofessional'))
                                      ? 'Industrial/Professional/Commercial':'Residential');
            wt.Place_of_Purchase__c = regWarRequest.placeOfPurchase;
            // wt.Place_of_Purchase_picklist__c = requestJson.placeofPurchasePicklist == 'null'?null:requestJson.placeofPurchasePicklist;
            
            // add by yujie for logic change 2019/8/13
            // 1) if receiptStatus == lostReceipt then warranty's lost is true
            // 2) else, warranty's pending is true
            // 3) if receiptStatus == gift, then warranty's gift is true
            // and Receipt_received_and_verified__c is checked by manul
            //wt.Receipt_received_and_verified__c = (regWarRequest.receiptStatus == 'Uploaded' ? true:false);
            String receiptS = regWarRequest.receiptStatus;
            if (receiptS == null || receiptS == '') {
                return HerokuAPIUtils.getErrorResponse(422, 'Receipt status cannot be empty.');
            }
            receiptS = receiptS.toLowercase();
            if ( receiptS.contains('lost') ) {
                //lostReceipt
                wt.Lost_Receipt__c = true;
                wt.One_Time_Exception__c = true;
                wt.Pending__c = false;
            } else {
                //gift
                //I Have My Receipt
                wt.Pending__c = true;
                wt.Lost_Receipt__c = false;
                if(receiptS.contains('gift')) {
                    wt.Gift__c = true;
                }
            }

            if(String.isNotEmpty(regWarRequest.receiptUrl)) {
                wt.Image_of_Receipt__c = regWarRequest.receiptUrl;
            }
            System.debug(LoggingLevel.INFO, '*** Json.serialize(wt): ' + Json.serialize(wt));
            Database.insert(wt);
            /*Database.SaveResult sr2 = Database.insert(wt, false);
            System.debug(LoggingLevel.INFO, '*** sr2.isSuccess(): ' + sr2.isSuccess());
            if (!sr2.isSuccess()) {
                List<Database.Error> eList = sr2.getErrors();
                System.debug(LoggingLevel.INFO, '*** eList.get(0).getMessage(): ' + eList.get(0).getMessage());
            }*/
            

            //add by mike 20180428 for 注册warranty时，若没有关联上product时，将注册信息通过邮件发送给指定人员
            if(masterProduct == null) {
                try{
                    Warranty__c warrantyTemp = [select Id,Name from Warranty__c where Id =: wt.Id][0];
                    /*>>>>Add by Zack on 20180408:开始*/
                    String requestString = '<table width="600" cellspacing="0" border="1">';
                    requestString += '<tr><td colspan="2" style="background-color:#F90;text-align:center;"><strong>WARRANTY INFORMATION</strong></td></tr>';
                    requestString += '<tr><td style="background-color:#CCC" width="40%">&nbsp;WarrantyName:</td><td>&nbsp;' + warrantyTemp.Name + '</td></tr>';
                    requestString += '<tr><td style="background-color:#CCC">&nbsp;CustomerID</td><td>&nbsp;' + Utilities.fixNull(regWarRequest.customerId) + '</td></tr>';
                    requestString += '<tr><td style="background-color:#CCC">&nbsp;FirstName</td><td>&nbsp;' + Utilities.fixNull(acc.FirstName) + '</td></tr>';
                    requestString += '<tr><td style="background-color:#CCC">&nbsp;LastName</td><td>&nbsp;' + Utilities.fixNull(acc.LastName) + '</td></tr>';
                    requestString += '<tr><td style="background-color:#CCC">&nbsp;MasterModelNumber</td><td>&nbsp;' + Utilities.fixNull(regWarRequest.masterModelNumber) + '</td></tr>';
                    requestString += '<tr><td style="background-color:#CCC">&nbsp;ProductUseType</td><td>&nbsp;' + Utilities.fixNull(regWarRequest.productUseType) + '</td></tr>';
                    requestString += '<tr><td style="background-color:#CCC">&nbsp;PurchaseDate</td><td>&nbsp;' + Utilities.fixNull(regWarRequest.purchaseDate) + '</td></tr>';
                    requestString += '<tr><td style="background-color:#CCC">&nbsp;PlaceOfPurchase</td><td>&nbsp;' + Utilities.fixNull(regWarRequest.placeOfPurchase) + '</td></tr>';
                    requestString += '<tr><td style="background-color:#CCC">&nbsp;ReceiptStatus</td><td>&nbsp;' + Utilities.fixNull(regWarRequest.receiptStatus) + '</td></tr>';
                    requestString += '<tr><td style="background-color:#CCC">&nbsp;ReceiptUrl</td><td>&nbsp;' + Utilities.fixNull(regWarRequest.receiptUrl) + '</td></tr>';
                    requestString += '</table>';

                    EmailService.sendMasterProductNotFoundEmail(regWarRequest.masterModelNumber, requestString);
                    /*<<<<Add by Zack on 20180408:结束*/       
                }catch(Exception e) {}
            }

            //ADD BULLET
            WarrantyService warranty_service = new WarrantyService();
            warranty_service.warranty = wt;
            warranty_service.warrantyItemList = warrantyItemList;

            //调用warranty service方法计算过保时间
            warranty_service.setWarrantyItemExpirationDate(false);
            warrantyItemList = warranty_service.warrantyItemList;

            //add by mike 
            for(Warranty_Item__c warrantyItem : warrantyItemList){
                warrantyItem.Warranty__c = wt.Id;
            }
            Database.insert(warrantyItemList);

            //返回正确结果，用户的sfid
            return new HerokuEntity.ResponseEntity(200, '');
        }

        public override HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            return null;
        }
    }

    // 批量查询warranty处理类
    public class SelectWarrantyHandler extends HerokuAPIHandler {
        public override HerokuEntity.ResponseEntity handlePost(String requstBody){
            return null;
        }

        public override HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap) {
            String customerId = headerMap.get('customerId');
            String brandName = headerMap.get('brandName');
            System.debug(LoggingLevel.INFO, '*** customerId: ' + customerId);
            System.debug(LoggingLevel.INFO, '*** brandName: ' + brandName);

            // 如果CustomerId为空，返回错误信息
            if (String.isEmpty(customerId)) {
                return HerokuAPIUtils.getErrorResponse(422, 'CustomerID cannot be empty.');          
            }
            // 如果CustomerId为空，返回错误信息
            if (String.isEmpty(brandName)) {
                return HerokuAPIUtils.getErrorResponse(422, 'BrandName cannot be empty.');          
            }
            // 如果Account为null，返回错误信息
            Account acc = HerokuAPIUtils.getAccountByID(customerId);
            if (acc == null) {
                return HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }

            //获取用户名下注册的warranty信息
            List<HerokuEntity.RCWarrantyEntity> warrantyList = new List<HerokuEntity.RCWarrantyEntity>();
            for(Warranty__c warranty : [Select Id, Receipt_received_and_verified__c, Purchase_Date__c, Product_Use_Type__c,
                                        Product_Use_Type2__c, Place_Of_Purchase__c, Place_of_Purchase_picklist__c, 
                                        Kit__c, Master_Product__r.Kit__c, Master_Product_Code__c,
                                        Master_Product__r.Name,
                                            (SELECT Id, Serial_Number__c, Product_Type__c, Product_Name__c, 
                                                Product_Code__c
                                            FROM Warranty_Items__r WHERE Product__c != null 
                                            ORDER BY Product__r.Type__c) 
                                        From Warranty__c
                                        Where AccountCustomer__r.Id =: customerId 
                                        And Brand_Name__c =: brandName]){
                // 封装warranty信息
                HerokuEntity.RCWarrantyEntity tempWarranty = new HerokuEntity.RCWarrantyEntity();
                tempWarranty.receipt         = warranty.Receipt_received_and_verified__c == false ? 'false':'true';
                tempWarranty.PurchaseDate    = (warranty.Purchase_Date__c == null) ? '': DateTime.newInstance(warranty.Purchase_Date__c, Time.newInstance(0,0,0,0)).format('MMddyy');
                tempWarranty.ProductUseType  = (warranty.Product_Use_Type2__c == null) ? warranty.Product_Use_Type__c:warranty.Product_Use_Type2__c;
                tempWarranty.PlaceOfPurchase = (warranty.Place_Of_Purchase__c == null) ? warranty.Place_of_Purchase_picklist__c:warranty.Place_Of_Purchase__c;
                tempWarranty.Kit             = (warranty.Kit__c == true) ? 'TRUE' : warranty.Master_Product__r.Kit__c ? 'TRUE' : 'FALSE';
                tempWarranty.masterModelNumber = warranty.Master_Product_Code__c;
                tempWarranty.masterProductName = warranty.Master_Product__r.Name;

                // 封装warrantyItem信息
                List<HerokuEntity.RCWarrantyItemEntity> warrantyItemList = new List<HerokuEntity.RCWarrantyItemEntity>();
                for (Warranty_Item__c wiItem : warranty.Warranty_Items__r) {
                    HerokuEntity.RCWarrantyItemEntity warrantyItem = new HerokuEntity.RCWarrantyItemEntity();
                    warrantyItem.productType        = wiItem.Product_Type__c;
                    warrantyItem.productName        = wiItem.Product_Name__c;
                    warrantyItem.serialNumber       = wiItem.Serial_Number__c;
                    warrantyItem.productModelNumber = wiItem.Product_Code__c;
                    warrantyItemList.add(warrantyItem);
                }

                // 将warrantyItem封装入warranty中
                tempWarranty.warrantyItems = warrantyItemList;
                warrantyList.add(tempWarranty);
            }

            //返回正确结果，warranty的List集合
            return new HerokuEntity.RCWarrantyListResponseEntity(200, '', warrantyList);
        }
    }

    // 发送邮件处理类
    public class SendEmailHandler extends HerokuAPIHandler {
        public override HerokuEntity.ResponseEntity handlePost(String requstBody){
            return null;
        }

        public override HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap) {
            String customerId = headerMap.get('customerId');
            String emailType = headerMap.get('emailType');
            String jwtToken = headerMap.get('jwtToken');
            String brandName = headerMap.get('brandName');

            // 如果CustomerId为空，返回错误信息
            if (String.isEmpty(customerId)) {
                return HerokuAPIUtils.getErrorResponse(422, 'CustomerID cannot be empty.');          
            }
            // 如果Account为null，返回错误信息
            Account acc = HerokuAPIUtils.getAccountByID(customerId);
            if (acc == null) {
                return HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }

            if (emailType.equalsIgnoreCase('reset')) {
                HerokuAPIUtils.sendResetPasswordEmail(acc, jwtToken, brandName, null);
            } else if (emailType.equalsIgnoreCase('migrate')) {
                HerokuAPIUtils.sendMigrateAccountEmail(acc, jwtToken);
            } else if (emailType.equalsIgnoreCase('resetIoT')) {
                HerokuAPIUtils.sendIoTResetPasswordEmail(acc, jwtToken);
            } else if(emailType.equalsIgnoreCase('egocommercialreset')) {
                HerokuAPIUtils.sendResetPasswordEmail(acc, jwtToken, brandName, 'EGO_Commercial');
            } else {
                return HerokuAPIUtils.getErrorResponse(422, 'Can\'t detect the email_type from the request');
            }

            //返回正确结果，warranty的List集合
            return new HerokuEntity.ResponseEntity(200, '');
        }
    }

    public class sequenceWapper{
        public String productCode;
        public String SNumber;
    }
}