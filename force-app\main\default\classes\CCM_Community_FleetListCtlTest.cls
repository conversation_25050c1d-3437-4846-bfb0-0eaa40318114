@isTest
private class CCM_Community_FleetListCtlTest {
    @testSetup static void testSetup(){
        User admin = [SELECT Id, Username, UserRoleId FROM User WHERE Profile.Name = 'System Administrator'  AND UserRoleId != null AND IsActive = true LIMIT 1];
        Profile profilePartner = [
            SELECT Id 
            FROM Profile 
            WHERE Name = 'Partner Community Sales' 
            LIMIT 1
        ];

        User userPartner;
        System.runAs(admin){
            Account customer = new Account();
            customer.Name = 'Test Customer';
            customer.AccountNumber = '*********';
            customer.TaxID__c = 'test';
            insert customer;

            Contact con = new Contact();
            con.FirstName = 'test';
            con.LastName = 'Contact';
            con.AccountId = customer.Id;
            con.Phone = '*********0';
            con.Email = System.now().getTime() + '@test.com';
            insert con;

            userPartner = new User(
                Email = '<EMAIL>',
                ProfileId = profilePartner.Id,
                UserName = '<EMAIL>',
                Alias = 'Test',
                TimeZoneSidKey = 'America/New_York',
                EmailEncodingKey = 'ISO-8859-1',
                LocaleSidKey = 'en_US',
                LanguageLocaleKey = 'en_US',
                ContactId = con.Id,
                //PortalRole = 'Manager',
                FirstName = 'Firstname',
                LastName = 'Lastname'
            );

            INSERT userPartner;

            Fleet_Program_Rule__c rule = new Fleet_Program_Rule__c();
            rule.Deliver_At_Once__c = true;
            rule.Maximum_Discount_Criteria__c = 15;
            rule.Discount_Return__c = 7.5;
            rule.Year__c = String.valueOf(Date.today().year());
            rule.Actived__c = true;
            INSERT rule;

            Fleet_Program_Target_Customer__c targetCustomer = new Fleet_Program_Target_Customer__c();
            targetCustomer.Customer__c = customer.Id;
            targetCustomer.Fleet_Program_Rule__c = rule.Id;
            insert targetCustomer;

            List<RecordType> proRecordType = [
                SELECT Id, Name FROM RecordType 
                WHERE SObjectType = 'Product2' AND DeveloperName = 'Product'
            ];
            List<RecordType> kitRecordType = [
                SELECT Id, Name FROM RecordType 
                WHERE SObjectType = 'Product2' AND DeveloperName = 'Kit'
            ];

            Product2 pro = new Product2();
            pro.Name = 'Test';
            pro.Brand_Name__c = 'EGO';
            pro.RecordTypeId = proRecordType[0].Id;
            pro.ProductCode = '1234567';
            pro.IsActive = true;
            pro.Source__c = 'PIM';
            pro.Country_of_Origin__c = 'United States';
            insert pro;

            Product2 pro2 = new Product2();
            pro2.Name = 'Test';
            pro2.Brand_Name__c = 'EGO';
            pro2.RecordTypeId = proRecordType[0].Id;
            pro2.ProductCode = '12345670';
            pro2.IsActive = true;
            pro2.Source__c = 'PIM';
            pro2.Country_of_Origin__c = 'United States';
            insert pro2;

            Product2 pro3 = new Product2();
            pro3.Name = 'Test';
            pro3.Brand_Name__c = 'EGO';
            pro3.RecordTypeId = proRecordType[0].Id;
            pro3.ProductCode = '12345670';
            pro3.IsActive = true;
            pro3.Source__c = 'PIM';
            pro3.Country_of_Origin__c = 'United States';
            insert pro3;
        
            Product2 pkit = new Product2();
            pkit.Name = 'Test';
            pkit.Brand_Name__c = 'EGO';
            pkit.RecordTypeId = kitRecordType[0].Id;
            pkit.ProductCode = '1234567';
            pkit.IsActive = true;
            pkit.Source__c = 'PIM';
            pkit.Country_of_Origin__c = 'United States';
            insert pkit;

            Product2 pkit2 = new Product2();
            pkit2.Name = 'Test';
            pkit2.Brand_Name__c = 'EGO';
            pkit2.RecordTypeId = kitRecordType[0].Id;
            pkit2.ProductCode = '1234567---';
            pkit2.IsActive = true;
            pkit2.Source__c = 'PIM';
            pkit2.Country_of_Origin__c = 'United States';
            insert pkit2;

            Program_Rule_Product_Relationship__c relationship = new Program_Rule_Product_Relationship__c();
            relationship.Fleet_Program_Rule__c = rule.Id;
            relationship.Kit__c = pkit.Id;
            INSERT relationship;

            Program_Rule_Product_Relationship__c relationship2 = new Program_Rule_Product_Relationship__c();
            relationship2.Fleet_Program_Rule__c = rule.Id;
            relationship2.Kit__c = pkit2.Id;
            INSERT relationship2;

            Kit_Item__c item = new Kit_Item__c();
            item.Kit__c = pkit.Id;
            item.Product__c = pro.Id;
            item.Status__c = 'A';
            item.Sequence__c = 'xxxx';
            INSERT item;

            Kit_Item__c item2 = new Kit_Item__c();
            item2.Kit__c = pkit.Id;
            item2.Product__c = pro2.Id;
            item2.Status__c = 'A';
            item2.Sequence__c = 'xxxx';
            INSERT item2;

            Kit_Item__c item3 = new Kit_Item__c();
            item3.Kit__c = pkit.Id;
            item3.Product__c = pro3.Id;
            item3.Status__c = 'A';
            item3.Sequence__c = 'xxxx';
            INSERT item3;

            Pricebook2 pb = new Pricebook2();
            pb.IsActive = true;
            pb.Name = 'CNA-EGO-MSRP';
            insert pb;

            PricebookEntry pbe = new PricebookEntry();
            pbe.IsActive = true;
            pbe.Product2Id = pkit2.Id;
            pbe.UnitPrice = 1000;
            pbe.Pricebook2Id = pb.Id;
            pbe.UseStandardPrice = false;
            insert pbe;
        }
    }

    static testMethod void testMethod1() {
        List<User> userPartner = [
            SELECT Id
            FROM User
            WHERE UserName = '<EMAIL>'
        ];
        AuraResponseEntity aura = CCM_Community_FleetEditCtl.getInitData(userPartner[0].Id, null);

        List<Product2> kitList = [
            SELECT Id
            FROM Product2
            WHERE ProductCode = '1234567'
        ];

        List<Product2> proList = [
            SELECT Id
            FROM Product2
            WHERE ProductCode = '12345670'
        ];

        Map<String, Object> dataMap = (Map<String, Object>)aura.data;
        Map<Id, List<CCM_Community_FleetEditCtl.WarrantyItem>> kitId2WarrantyItemsInitMap = (Map<Id, List<CCM_Community_FleetEditCtl.WarrantyItem>>)dataMap.get('kitId2WarrantyItemsInitMap');
        CCM_Community_FleetEditCtl.FleetClaim fleetClaim = (CCM_Community_FleetEditCtl.FleetClaim)dataMap.get('fleetClaim');
        fleetClaim.fleetItemList = new List<CCM_Community_FleetEditCtl.FleetItem>();
        fleetClaim.fleetItemList.add(new CCM_Community_FleetEditCtl.FleetItem());
        fleetClaim.fleetItemList[0].qtyPurchased = 1;
        fleetClaim.fleetItemList[0].warrantyList = new List<CCM_Community_FleetEditCtl.Warranty>();


        CCM_Community_FleetEditCtl.WarrantyItem warrantyItem = new CCM_Community_FleetEditCtl.WarrantyItem();
        warrantyItem.fakeId = 'aaaaaaaaaaaaaaaaaaa';
        warrantyItem.kitId = kitList[0].Id;
        warrantyItem.productId = proList[0].Id;
        warrantyItem.serialNumber = '***********';
            Test.startTest();
        CCM_Community_FleetEditCtl.Warranty warranty = new CCM_Community_FleetEditCtl.Warranty();
        warranty.fakeId = 'xxxxxxxxxxxxxxxxxxxx';
        warranty.kitId = kitList[0].Id;
        warranty.warrantyItemList = new List<CCM_Community_FleetEditCtl.WarrantyItem>();
        warranty.warrantyItemList.add(warrantyItem);

        fleetClaim.fleetItemList[0].warrantyList.add(warranty);

        fleetClaim.endUserCustomer = new CCM_Community_FleetEditCtl.AccountInfo();
        fleetClaim.endUserCustomer.lastName = 'wang';
        fleetClaim.endUserCustomer.firstName = 'test';
        fleetClaim.endUserCustomer.emailAddress = '<EMAIL>';

        aura = CCM_Community_FleetEditCtl.saveFleetClaim(JSON.serialize(fleetClaim), 'Save');

        CCM_Community_FleetListCtl.FleetClaimFilter filter = new CCM_Community_FleetListCtl.FleetClaimFilter();
        filter.userId = userPartner[0].Id;

        CCM_Community_FleetListCtl.getFleetClaimList(1, 10, JSON.serialize(filter));
        Test.stopTest();
    }

    @IsTest
    static void testGetAvailableFleetPrograms(){
        
        Test.startTest();
        User u = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1];
        System.runAs(u){
            CCM_Community_FleetListCtl.getAvailableFleetPrograms();
        }
        Test.stopTest();
        
    }
}