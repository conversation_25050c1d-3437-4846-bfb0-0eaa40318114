({
    initData : function(component, event, helper) {
        var action = component.get("c.getAdvertisingBanners");
        // action.setParam("libarayName", 'Promotion Banner');

        action.setCallback(this, function (response) {
            var state = response.getState();

            if (component.isValid() && state === "SUCCESS") {
                var results = response.getReturnValue();

                component.set('v.currentData', results);
                component.set('v.totalRecords', results.length);
            } else {
                var errors = response.getError();
                
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
        });
        $A.enqueueAction(action);
    }
})