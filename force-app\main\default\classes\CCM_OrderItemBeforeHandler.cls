// handle order item before trigger event
public with sharing class CCM_OrderItemBeforeHandler implements Triggers.Handler {
    public static Boolean isBatch = false;
    public void handle() {
        if(isBatch) {
            return;
        }
        // get price books and products
        Set<Id> pricebookIdSet = new Set<Id>();
        Set<String> productCodeSet = new Set<String>();
        Map<Id, String> productIdCodeMap = new Map<Id, String>();
        Map<String, Product2> productLaunchDateMap = new Map<String, Product2>();
        Set<Id> productIdSet = new Set<Id>();

        for (Order_Item__c orderItem : (List<Order_Item__c>)Trigger.new) {
            pricebookIdSet.add(orderItem.Price_Book__c);
            productIdSet.add(orderItem.Product__c);
        }

        pricebookIdSet.remove(null);
        productIdSet.remove(null);

        // get productCode and product Map
        List<Product2> prodcutCodeList = [
                                        SELECT
                                            Id,
                                            ProductCode
                                        FROM Product2 WHERE Id != NULL
                                        AND Id IN: productIdSet
                                        AND Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                    ];
        for(Product2 prod : prodcutCodeList) {
            productCodeSet.add(prod.ProductCode);
            productIdCodeMap.put(prod.Id, prod.ProductCode);
        }
        productCodeSet.remove(null);
        List<Product2> proList =[select Id,Lanch_Date__c,Country_of_Origin__c,ProductCode FROM Product2 WHERE ProductCode IN :productCodeSet AND Source__c ='EBS' AND Lanch_Date__c != null  AND IsActive = true];
        for(Product2 pr : proList){
            String key = pr.Country_of_Origin__c + pr.ProductCode;
            productLaunchDateMap.put(key,pr);
        }
        // get price book entry
        List<PricebookEntry> pricebookEntryList = [
                                                    SELECT
                                                        UnitPrice,
                                                        Product2Id,
                                                        Product2.productCode,
                                                        Pricebook2Id
                                                    FROM PricebookEntry
                                                    WHERE Product2.productCode IN :productCodeSet
                                                    AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                                    AND Product2Id IN :productIdCodeMap.keySet()
                                                    AND Pricebook2Id IN :pricebookIdSet
                                                ];
        // get list price
        for (Order_Item__c orderItem : (List<Order_Item__c>)Trigger.new) {
            String key1 = '';
            if(orderItem.Org_Code__c == 'CCA'){
                key1 = 'Canada'+orderItem.Product_Text__c;
            }else{
                key1 = 'United States'+orderItem.Product_Text__c;
            }
            if(productLaunchDateMap.get(key1)!=null && productLaunchDateMap.get(key1).Lanch_Date__c != null){
                orderItem.Launch_Date__c = productLaunchDateMap.get(key1).Lanch_Date__c;
            } else{
                orderItem.Launch_Date__c = null;
            }   
            for (PricebookEntry entry : pricebookEntryList) {
                if (entry.Pricebook2Id == orderItem.Price_Book__c
                    && entry.Product2.productCode == productIdCodeMap.get(orderItem.Product__c)) {
                        orderItem.List_Price__c = entry.UnitPrice;
                        break;
                }
            }
        }
    }
}