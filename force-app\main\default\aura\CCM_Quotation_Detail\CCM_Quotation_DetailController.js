({
    doInit : function(component, event, helper) {
        component.set('v.isBusy', true);
        component.set('v.currencySymbol', $A.get("$Locale.currencyCode"));        
        var recordId = component.get("v.recordId");
        if(!recordId){
            recordId = helper.getUrlParameter('recordId');
        }

        var columns = [
            {label: $A.get("$Label.c.CCM_Portal_Index"), fieldName: 'Name'},
            {label: $A.get("$Label.c.CCM_Portal_Brand"), fieldName: 'Product__r.Brand_Name__c'},
            {label: $A.get("$Label.c.CCM_Portal_Product"), fieldName: 'Product__r.Name'},
            {label: $A.get("$Label.c.CCM_Portal_Qty"), fieldName: 'Quantity__c'},
            {label: $A.get("$Label.c.CCM_Portal_ListPrice"), fieldName: 'List_Price__c',type: "currency", typeAttributes: { currencyCode: 'USD', maximumSignificantDigits: 5}},
            {label: $A.get("$Label.c.CCM_Portal_YourPrice"), fieldName: 'Unit_Price__c',type: "currency", typeAttributes: { currencyCode: 'USD', maximumSignificantDigits: 5}}
        ];
        component.set('v.columns', columns);

        var action = component.get("c.getData");
        if(recordId){
            action.setParam('recordId', recordId);
        }
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if(results){
                    component.set('v.quotation', results.po);
                    console.log('results.po',results.po);
                    // component.set('v.quotation.Freight_Fee_Waived__c', Number(helper.currencyFormat(component.get('v.quotation.Freight_Fee_Waived__c'))).toFixed(2));
                    component.set('v.quotation.Freight_Fee_Waived__c', Number(component.get('v.quotation.Freight_Fee_Waived__c')).toFixed(2));

                    //yanko for cca caseqty
                    results.poItems.forEach(function(e){
                        if(results.po.ORG_ID__c != 'CCA'){
                            e.Product__r.CS_Exchange_Rate__c = e.Product__r.CS_Exchange_Rate__c ? e.Product__r.CS_Exchange_Rate__c : 1;
                        }else{
                            e.Product__r.CS_Exchange_Rate__c = 1;
                        }
                    });

                    component.set('v.orderItemList', results.poItems);
                    component.set('v.customerId', results.customerId);
                    component.set('v.isDelegate', results.po.Is_Delegate__c);
                    component.set('v.paymentTermVal', results.paymentTermVal);
                    component.set('v.freightTermVal', results.freightTermVal);
                    console.log('freight Fee-->'+results.po.Freight_Fee__c);
                    if (results.po.Freight_Fee__c == null){
                        component.set('v.quotation.Freight_Fee__c', 0.00);
                    }
                    if (results.po.Handing_Fee__c == null){
                        component.set('v.quotation.Handing_Fee__c', 0.00);
                    }
                    if (results.po.Order_Type__c && (results.po.Order_Type__c == 'CNA Dropship Order' || results.po.Order_Type__c == 'CA Dropship Order')){
                        component.set('v.isDropShipOrder', 'Yes');
                    }else{
                        component.set('v.isDropShipOrder', 'No');
                    }
                    component.set('v.brandScopeOpt', results.brandScopeList);
                    component.set('v.recordId', recordId);
                    component.set('v.isInnerUser',results.isInnerUser);
                    component.set('v.salesAgencyAlias', results.salesAgencyAlias);

                    //CCA Purhcase Order
                    if(results.po.ORG_ID__c && results.po.ORG_ID__c==='CCA'){
                        component.set('v.isCCA',true);
                    }else{
                        component.set('v.isCCA',false);
                    }

                    component.set('v.showTax', results.showTax);
                }
            } else {
                var errors = response.getError();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },

    previousStep: function(component){
        var currentStep = component.get("v.currentStep");
        component.set("v.currentStep", currentStep - 1);
    },
    backHome: function(component){
        if (location.host.indexOf('lightning') < 0) {
            window.location.href = window.location.origin + '/s/orderinformation';
        }else{
            var url = window.location.origin + '/lightning/n/Order_Apply_List';
            window.open(url,'_self'); 
        }
    },
    doSubmit: function(component){
        component.set("v.isBusy", true);
        let fileUploader = component.find("fileUploader");
        if (fileUploader) {
            if (fileUploader.validate() === false) {
                component.set("v.isBusy", false);
                return;
            } 
        }
        component.set('v.isBusy', true);
        var recordId = component.get("v.recordId");
        var action = component.get("c.submitData");
        action.setParam('recordId', recordId);
        action.setParam('isDelegate', component.get('v.isDelegate'));
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                if(results === 'Success'){
                    var toastEvent = $A.get("e.force:showToast");
                    component.set('v.isBusy', false);
                    toastEvent.setParams({
                        "title": $A.get("$Label.c.CCM_Portal_Success"),
                        "message": $A.get("$Label.c.CCM_Portal_Thequotationhasbeensubmittedsuccessfully"),
                        "type": "success"
                    });
                    toastEvent.fire();

                    setTimeout(function () {
                        if (location.host.indexOf('lightning') < 0) {
                            window.location.href = window.location.origin + '/s/orderinformation';
                        }else{
                            var url = window.location.origin + '/lightning/n/Order_Apply_List';
                            window.open(url,'_self'); 
                        }
                    }, 3000);
                }else{
                    var toastEvent = $A.get("e.force:showToast");
                    component.set('v.isBusy', false);
                    toastEvent.setParams({
                        "title": $A.get("$Label.c.CCM_Portal_Error"),
                        "message": $A.get("$Label.c.CCM_Portal_Duplicateordersalreadyexistinthesystem"),
                    });
                    toastEvent.fire();
                }
            } else {
                var errors = response.getError();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    }
})