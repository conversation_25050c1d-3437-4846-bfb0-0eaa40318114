/**
 * Created by gluo006 on 9/4/2019.
 */
({
    getInfoBySerialNum: function (component) {
        var action = component.get('c.SearchWarrantyBySerialNumber');
        action.setParams({
            'customerId': component.get('v.customerId'),
            'serial': component.get('v.SerialNum').trim(),
            'brand': component.get('v.brand'),
            'firstName': component.get('v.firstName'),
            'lastName': component.get('v.lastName'),
            'productId': component.get('v.productId'),
            'boolIsAltaQuip': component.get("v.boolIsAltaQuip")
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === 'SUCCESS') {
                if(response.getReturnValue()) {
                    var result = JSON.parse(response.getReturnValue());
                    if (result.WarrantyItemList) {
                        component.set('v.modelNumList', result.WarrantyItemList);
                        component.set('v.billToList', result.BillTo);
                        component.set('v.LaborRate', result.LaborRate);
                        component.set('v.realWarrantyStatus', result.WarrantyItemList[0].ActualIndicator__c);
                        //component.set('v.warrantyStatus', result.WarrantyItemList[0].ActualIndicator__c);
                        if (result.isRecall) {
                            component.set('v.isRecall', result.isRecall);
                        }
                    }
                    if (result.markupbfb != null && result.markupbfb != 0) {
                        component.set('v.markupbfb', result.markupbfb);
                    }
                    if (result.markupbfb != '0' && result.markupbfb != null) {
                        component.set('v.isshowmarkup', true);
                    }

                    if (result.Brand != null) {
                        component.set('v.brand', result.Brand);
                        component.set('v.brandList', result.Brand);

                    }
                    if (result.RateList) {
                        component.set('v.rateList', result.RateList)
                    }
                    if (result.CustomerId != null) {
                        component.set('v.customerId', result.CustomerId);
                    }
                    if (result.Email != null) {
                        component.set('v.emailAddress', result.Email);
                    }
                    if(result.customerFirstName) {
                        component.set('v.firstName', result.customerFirstName);
                    }
                    if(result.customerLastName) {
                        component.set('v.lastName', result.customerLastName);
                    }
                }
                else {
                    component.set('v.emailAddress', '');
                    component.set('v.firstName', '');
                    component.set('v.lastName', '');
                    component.set('v.customerId', '');
                    component.set('v.modelNumList', null);
                    component.set('v.billToList', null);
                    component.set('v.LaborRate', null);
                    component.set('v.realWarrantyStatus', '');
                    component.set('v.warrantyStatus', '');
                    component.set('v.isRecall', false);
                    component.set('v.markupbfb', 0);
                    component.set('v.isshowmarkup', false);
                    component.set('v.rateList', null);
                }
            } else {
                $A.get("e.force:showToast").setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                    "message": response.getError()[0].message,
                    "type": "Error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    calculateWarrantyStatus: function (component) {
        var warrantyStatus = component.get('v.realWarrantyStatus');
        var expirationDate = component.get('v.expirationDate');
        var dropOfDate = component.get('v.dropOfDate');
        var repairDate = component.get('v.repairDate');
        if (warrantyStatus == 'Vailid Warranty') {
            if (!dropOfDate || !repairDate) {
                component.set('v.warrantyStatus', 'Pending');
            } else if (dropOfDate && expirationDate) {
                if (dropOfDate <= expirationDate) {
                    component.set('v.warrantyStatus', 'Vailid Warranty');
                    component.set('v.isDisabled', false);
                } else {
                    component.set('v.warrantyStatus', 'Out of Warranty');
                    component.set('v.isDisabled', true);
                }
            }
        } else if (warrantyStatus != 'Vailid Warranty' && component.get('v.isValidRecall') && component.get('v.isRecall')) {
            component.set('v.warrantyStatus', 'Vailid Warranty');
            component.set('v.isDisabled', false);
        } else if (warrantyStatus == 'Out of Warranty') {
            component.set('v.warrantyStatus', 'Out of Warranty');
            component.set('v.isDisabled', true);
        }
    },
    getViewInfo: function (component, claimId) {
        var self = this;
        var action = component.get('c.viewWarrantyClaim');
        action.setParams({
            'claimId': claimId,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === 'SUCCESS') {
                // let results = JSON.parse(response.getReturnValue()),
                //     { Information } = results,
                //     { Actual_Time__c, Labor_Time__c, Case__r } = Information,
                //     { Warranty_Item__r } = Case__r,
                //     { Id: idWarrantyItem } = Warranty_Item__r;
                let results = JSON.parse(response.getReturnValue());
                let Information = results.Information;
                let Actual_Time__c = Information.Actual_Time__c;
                let Labor_Time__c = Information.Labor_Time__c;
                let Additional_Time__c = Information.Additional_Time__c;
                let Case__r = Information.Case__r;
                let Warranty_Item__r;
                if (Case__r.Warranty_Item__r) {
                    Warranty_Item__r = Case__r.Warranty_Item__r;
                }
                let idWarrantyItem;
                if (Warranty_Item__r) {
                    idWarrantyItem = Warranty_Item__r.Id;
                }
                if (results.LaborRate) {
                    component.set('v.LaborRate', results.LaborRate);
                }
                if (results.markupbfb != '0' && results.markupbfb != null) {
                    component.set('v.isshowmarkup', true);
                }
                component.set('v.markupbfb', results.markupbfb);

                component.set('v.orgCode', results.orgCode);
                component.set('v.gst', results.gst)
                component.set('v.hst', results.hst)
                component.set('v.qst', results.qst)
                component.set('v.pst', results.pst)
                let totalTax = 0;
                if (results.gst) {
                    totalTax = totalTax + results.gst;
                }
                if (results.hst) {
                    totalTax = totalTax + results.hst;
                }
                if (results.qst) {
                    totalTax = totalTax + results.qst;
                }
                if (results.pst) {
                    totalTax = totalTax + results.pst;
                }
                component.set('v.totalTax', totalTax);

                component.set('v.zipCode', results.ZipCode);
                component.set('v.zipCodeSearch', results.ZipCode);
                component.set('v.TierId', results.tierId);
                if (results.TierList) {
                    component.set('v.tierlist', JSON.parse(results.TierList));
                }

                component.set('v.partsCombimeLaborTimeCode', JSON.parse(results.partsCombimeLaborTimeCode));

                var result = results.Information;
                console.log(results);
                if(result.Explanation_Options__c) {
                    component.set('v.explanationOptionSelected', result.Explanation_Options__c);
                }
                self.getExplanationOptions(component);
                //basic information
                component.set('v.claimNo', result.Name);
                if (results.BrandList) {
                    component.set('v.brandList', results.BrandList.split(';'));
                }

                if (result.Pickup_Fee__c != null && result.Pickup_Fee__c != 0) {
                    component.set('v.decPickupFeeSubtotal', result.Pickup_Fee__c);
                    component.set('v.strUsePickup', 'Yes');
                }
                component.set('v.isPickUp', results.needDisplayPickup);
                component.set('v.boolPickup', results.needPickup);
                if (results.needPickup) {
                    component.set('v.decPickupDistance', results.decPickupDistance);
                }
                // add haibo: french
                let statusList = [
                    {
                        en: 'Approved',
                        fr: 'Approuvé'
                    },
                    {
                        en: 'Draft',
                        fr: ' Brouillon'
                    },
                    {
                        en: 'Rejected',
                        fr: 'Rejeté'
                    },
                    {
                        en: 'Submitted',
                        fr: 'Soumis'
                    },
                ];
                let paymentStatusList = [
                    {
                        en: 'Credited',
                        fr: 'Crédité'
                    },
                    {
                        en: 'N/A',
                        fr: ' S/O'
                    },
                    {
                        en: 'Paid',
                        fr: 'Payé'
                    },
                    {
                        en: 'Pending',
                        fr: 'En attente'
                    },
                ];
                if ($A.get("$Label.c.CCM_Portal_Language") == 'fr') {
                    statusList.forEach((statusItem)=>{
                        if (result.Status__c == statusItem.en) {
                            result.Status__c = statusItem.fr
                        }
                    });
                    paymentStatusList.forEach((paymentStatusItem)=>{
                        if (result.Payment_Status__c == paymentStatusItem.en) {
                            result.Payment_Status__c = paymentStatusItem.fr
                        }
                    })
                }
                component.set('v.claimStatus', result.Status__c);
                component.set('v.isDraft', (result.Status__c == 'Draft' || result.Status__c == 'Brouillon'));
                component.set('v.paymentStatus', result.Payment_Status__c);
                component.set('v.emailAddress', result.Customer__r.PersonEmail);
                component.set('v.firstName', result.Customer__r.FirstName);
                component.set('v.lastName', result.Customer__r.LastName);
                component.set('v.brand', result.Case__r.Brand_Name__c);
                component.set('v.modelNum', result.Case__r.Product.Id);
                component.set("v.warrantyItemId", idWarrantyItem);
                component.set('v.productName', result.Case__r.Product.Name);
                component.set('v.placeOfPurchase', result.Case__r.Warranty__r.Place_of_Purchase_picklist__c);
                component.set('v.purchaseDate', result.Case__r.Warranty__r.Purchase_Date__c);
                this.searchAllPartsByProduct(component);
                // add haibo: french
                if ($A.get("$Label.c.CCM_Portal_Language") == 'fr') {
                    let purchaseUseTypeList = component.get('v.purchaseUseTypeList');
                    purchaseUseTypeList.forEach((typeItem)=>{
                        if (typeItem.en == result.Case__r.Warranty__r.Product_Use_Type2__c) {
                            component.set('v.purchaseUseTypeFrench', typeItem.fr);
                        }
                    })
                }
                component.set('v.purchaseUseType', result.Case__r.Warranty__r.Product_Use_Type2__c);
                component.set('v.warrantyId', result.Case__r.Warranty__c);
                component.set('v.productId', result.Case__r.ProductId);
                component.set('v.customerId', result.Customer__c);
                component.set('v.markup', result.Warranty_parts_credit_mark_up__c);
                if (result.Case__r.Warranty_Item__r) {
                    component.set('v.SerialNum', result.Case__r.Warranty_Item__r.Serial_Number__c);
                    component.set('v.selectedItemId', result.Case__r.Warranty_Item__c);
                    component.set('v.modelNumList', [{ Id: result.Case__r.Warranty_Item__c, Product_Code__c: result.Case__r.Warranty_Item__r.Product_Code__c, Product__r: { Id: result.Case__r.Product.Id } }]);
                    component.set('v.warrantyStatus', result.Case__r.Warranty_Item__r.ActualIndicator__c);
                    component.set('v.realWarrantyStatus', result.Case__r.Warranty_Item__r.ActualIndicator__c);
                }

                component.set('v.repairDate', result.Repair_date__c);
                if (result.BillTo__r) {
                    component.set('v.billToList', [{ Id: result.BillTo__c, Account_Address__r: { Address1__c: result.BillTo__r.Account_Address__r.Address1__c, City__c: result.BillTo__r.Account_Address__r.City__c, State__c: result.BillTo__r.Account_Address__r.State__c, Country__c: result.BillTo__r.Account_Address__r.Country__c, Postal_Code__c: result.BillTo__r.Account_Address__r.Postal_Code__c } }]);
                    component.set('v.billToId', result.BillTo__c);
                }
                this.getCaseExplanationOptions(component);
                //service information
                if (result.Case__r.Service_Option__c == 'Replacement') {
                    component.set('v.Level2EORequired', true);
                    component.set('v.Level2ExtraRequired', true);
                    self.showEle(component, 'replacementType');
                    self.showEle(component,'EXReminder');
                    self.showEle(component, 'Level1ExplanationOption');
                    self.showEle(component, 'Level2ExplanationDetails');
                    component.set('v.Level1ExplanationOption', result.Case__r.Level1_Explanation_Options__c);
                    if(result.Case__r.Level1_Explanation_Options__c == 'Part(s) Out Of Stock'
                        || result.Case__r.Level1_Explanation_Options__c == 'Part Not Available On SBOM'){
                        let partsOptionValues = result.Case__r.Level2_Explanation_Options__c.split(';').filter(item => item != '');
                        component.set('v.partsOptionValues', partsOptionValues);
                    }
                    component.set('v.Level2ExplanationOption', result.Case__r.Level2_Explanation_Options__c);
                }
                if (result.Case__r.Service_Option__c == 'Repair') {
                    self.showEle(component, 'repairType');
                    if (component.get('v.isDraft')) {
                        self.getFailureCode(component);
                    }
                }
                if (result.Case__r.Service_Option__c == 'Recall') {
                    self.showEle(component, 'recallType');
                }
                component.set('v.serviceOption', result.Case__r.Service_Option__c);
                if (result.Case__r.Service_Option__c == 'Service Attempt') {
                    component.set('v.isAddPartsDisabled', true);
                    component.set('v.isDiagramDisabled', true);
                }
                component.set('v.repairDescription', result.Descrption__c);
                if (result.Claim_Items__r) {
                    //if (component.get('v.isDraft')) {
                    for (var index = 0; index < result.Claim_Items__r.records.length; index++) {
                        console.log(result.Claim_Items__r.records[index]);
                        result.Claim_Items__r.records[index].kitItemId = result.Claim_Items__r.records[index].Kit_Item__c;
                        result.Claim_Items__r.records[index].itemNumber = result.Claim_Items__r.records[index].Product__r.ProductCode;
                        result.Claim_Items__r.records[index].Name = result.Claim_Items__r.records[index].ProductName__c;
                        result.Claim_Items__r.records[index].quantity = result.Claim_Items__r.records[index].Quantity__c;
                        result.Claim_Items__r.records[index].price = result.Claim_Items__r.records[index].Price__c;
                        result.Claim_Items__r.records[index].total = result.Claim_Items__r.records[index].Total__c;
                        result.Claim_Items__r.records[index].LaborTime = result.Claim_Items__r.records[index].LaborTime__c;
                        result.Claim_Items__r.records[index].partsId = result.Claim_Items__r.records[index].Product__c;
                    }
                    //}
                    component.set('v.partsItemList', result.Claim_Items__r.records);
                }
                component.set('v.repairType', result.Repair_Type__c);
                component.set('v.failureCodeList', [{label: result.Failure_Code__c, value: result.Failure_Code__c}]);
                component.set('v.failureCode', result.Failure_Code__c);
                if (result.Case__r.Project__r) {
                    component.set('v.projectList', [{ Name: result.Case__r.Project__r.Name, Id: result.Case__r.Project__r.Id }]);
                    component.set('v.project', result.Case__r.Project__r.Id);
                }
                if (result.Product_Type__c) {
                    component.set('v.brandList', result.Product_Type__c.split(';'));
                }
                if (result.Inventory__c) {
                    component.set('v.inventory', result.Inventory__c);
                    if (result.Inventory__c == 'Chervon inventory') {
                        self.showEle(component, 'addressInfo');
                        component.set('v.ShippingCity', result.Case__r.ShippingCity__c);
                        component.set('v.ShippingCountry', result.Case__r.ShippingCountry__c);
                        component.set('v.ShippingState', result.Case__r.ShippingState__c);
                        component.set('v.ShippingStreet', result.Case__r.ShippingStreet__c);
                        component.set('v.ShippingPostalCode', result.Case__r.ShippingPostalCode__c);
                        component.set('v.replacementFirstName', result.Case__r.Replacement_First_Name__c);
                        component.set('v.replacementLastName', result.Case__r.Replacement_Last_Name__c);
                        component.set('v.replacementPhone', result.Case__r.ReplacementPhone__c);
                    }
                }
                component.set('v.recallOption', result.Case__r.Warranty__r.Product_Use_Type2__c);
                //additional information
                component.set('v.overTimeHour', result.Actual_Time__c);
                component.set('v.overTimeDescription', result.Actual_Description__c);
                if (result.Additional_Time__c) {
                    component.set('v.additionalTimeHour', result.Additional_Time__c);
                }
                //audit information
                component.set('v.auditTrailList', results.auditList);
                component.set('v.auditComments', '');
                //summary information
                component.set('v.labourHours', result.Labor_Time__c);
                if (Actual_Time__c) {
                    component.set('v.showActualTime', true);
                    component.set('v.showAdditionalTime', false);
                    component.set('v.finialLaborHour', (Actual_Time__c / 60).toFixed(2));
                }
                else {
                    component.set('v.finialLaborHour', ((Additional_Time__c + Labor_Time__c) / 60).toFixed(2))
                }
                // component.set("v.finialLaborHour", Number((Actual_Time__c > 0 ? Actual_Time__c : Labor_Time__c) / 60).toFixed(2));
                component.set('v.laborCostSubtotal', result.Labor_Cost_Summary__c);
                component.set('v.partsCost', result.Parts_Cost__c);
                component.set('v.totalPrice', result.Total__c);
                component.set('v.dropOfDate', result.Drop_off_date__c);

                if (result.Status__c == 'Draft') {
                    self.calculateWarrantyStatus(component);
                    component.set('v.billToList', results.BillTo);

                    switch (component.get("v.inventory")) {
                        case "Dealer inventory":
                            self.hideEle(component, "addressInfo");
                            self.getInventoryInfo(component);
                            break;
                        case "Chervon inventory":
                            self.showEle(component, "addressInfo");
                            break;
                        case "":
                            self.hideEle(component, "addressInfo");
                            self.clearAddress(component);
                            break;
                    }
                    if (component.get('v.repairType') == 'Parts') {
                        self.hideEle(component, 'laborHourInput');
                        self.showEle(component, 'laborHourPopUp');
                        component.set('v.isAddPartsDisabled', false);

                    } else {
                        self.hideEle(component, 'laborHourPopUp');
                        self.showEle(component, 'laborHourInput');
                        if (component.get('v.repairType') == 'Labor Time Only') {
                            component.set('v.isAddPartsDisabled', true);
                        }
                        //remove the validate style
                        $A.util.removeClass(component.find('majorIssue'), 'field-error');
                        $A.util.addClass(component.find('majorIssue-error-required'), 'slds-hide');
                    }

                    component.set('v.repairPartsName', results.majorIssueObj && results.majorIssueObj.Name ? results.majorIssueObj.Name : '');
                    component.set('v.majorIssueObj', results.majorIssueObj);
                }
                if (Information.Replacement_Cost_From_Backend__c != null) {
                    component.set('v.replacementBaseFee', Information.Replacement_Cost_From_Backend__c)
                }
            } else {
                $A.get("e.force:showToast").setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                    "message": response.getError()[0].message,
                    "type": "Error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    getCustomerInfoByEmail: function (component) {
        let self = this;
        var email = component.get('v.emailAddress');
        let firstName = component.get('v.firstName');
        let lastName = component.get('v.lastName');
        var action = component.get('c.customerInfo');
        action.setParams({
            'email': email,
            'firstName': firstName,
            'lastName': lastName
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === 'SUCCESS') {
                if(response.getReturnValue()) {
                    var result = JSON.parse(response.getReturnValue());
                    if (result.FirstName) {
                        component.set('v.firstName', result.FirstName)
                    }
                    if (result.LastName) {
                        component.set('v.lastName', result.LastName);
                    }
                    if(result.PersonEmail) {
                        component.set('v.emailAddress', result.PersonEmail);
                        self.matchWarranty(component, result.PersonEmail);
                    }
                    if (result.Id) {
                        component.set('v.customerId', result.Id)
                    }
                    if (result.Product_Type__c) {
                        component.set('v.brandList', result.Product_Type__c.split(';'));
                    }
                }
                else {
                    component.set('v.firstName', '');
                    component.set('v.lastName', '');
                    component.set('v.emailAddress', '');
                    component.set('v.customerId', '');
                }
            } else {
                $A.get("e.force:showToast").setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                    "message": response.getError()[0].message,
                    "type": "Error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    matchWarranty: function(component, email) {
        let warrantyItemList = component.get("v.modelNumList") || [];
        let self = this;
        if(email) {
            let selectedWarrantyItem = warrantyItemList.find(item => item.Warranty__r.AccountCustomer__r.PersonEmail === email) || {},
            { Id } = selectedWarrantyItem;
            let selectProductId = '';
            if(selectedWarrantyItem && selectedWarrantyItem.Product__r) {
                selectProductId = selectedWarrantyItem.Product__r.Id;
                component.set('v.modelNum', selectProductId);
                component.set('v.selectedItemId', selectedWarrantyItem.Id);
            }
            else {
                component.set('v.modelNum', selectProductId);
                component.set('v.selectedItemId', '');
            }
            self.initPartsCombineLaborTimeCost(component);
            component.set("v.warrantyItemId", Id);
            component.set('v.WarrantyStatus', selectedWarrantyItem.ActualIndicator__c);
            component.set('v.realWarrantyStatus', selectedWarrantyItem.ActualIndicator__c);
            if (selectProductId == '') {
                component.set('v.placeOfPurchase', '');
                component.set('v.productName', '');
                component.set('v.boolPickup', false);
                component.set('v.isPickUp', false);
                component.set('v.purchaseDate', '');
                component.set('v.purchaseUseType', '');
                component.set('v.warrantyStatus', '');
            } else {
                var rateList = component.get('v.rateList');
                var modelNumList = component.get('v.modelNumList');
                modelNumList.forEach(function (item) {
                    if (item.Id == Id) {
                        if (item.Warranty__r.Place_of_Purchase_picklist__c) {
                            component.set('v.placeOfPurchase', item.Warranty__r.Place_of_Purchase_picklist__c);
                        }
                        if (item.Product_Name__c) {
                            component.set('v.productName', item.Product_Name__c);
                        }
                        if (item.Product__r.Pick_Up__c === true) {
                            component.set('v.boolPickup', true);
                            component.set('v.isPickUp', true);
                        }
                        if (item.Product__r.Category_1__c) {
                            if (rateList.length > 0) {
                                rateList.forEach(e => {
                                    if (e.Category__c == item.Product__r.Category_1__c) {
                                        component.set('v.LaborRate', e.Labor_Rate__c);
                                    }
                                });
                            }
                        }
                        if (item.Warranty__r.Purchase_Date__c) {
                            component.set('v.purchaseDate', item.Warranty__r.Purchase_Date__c);
                        }
                        if (item.Warranty__r.Product_Use_Type2__c) {
                            // add haibo: french
                        if ($A.get("$Label.c.CCM_Portal_Language") == 'fr') {
                            let purchaseUseTypeList = component.get('v.purchaseUseTypeList');
                            purchaseUseTypeList.forEach((typeItem)=>{
                                if (typeItem.en == item.Warranty__r.Product_Use_Type2__c) {
                                    component.set('v.purchaseUseTypeFrench', typeItem.fr);
                                }
                            })
                        }
                            component.set('v.purchaseUseType', item.Warranty__r.Product_Use_Type2__c);
                        }
                        if (item.Warranty__r.AccountCustomer__r.PersonEmail) {
                            component.set('v.emailAddress', item.Warranty__r.AccountCustomer__r.PersonEmail);
                        }
                        if (item.Warranty__r.AccountCustomer__r.FirstName) {
                            component.set('v.firstName', item.Warranty__r.AccountCustomer__r.FirstName);
                        }
                        if (item.Warranty__r.AccountCustomer__r.LastName) {
                            component.set('v.lastName', item.Warranty__r.AccountCustomer__r.LastName);
                        }
                        if (item.Warranty__r.AccountCustomer__c) {
                            component.set('v.customerId', item.Warranty__r.AccountCustomer__c);
                        }

                        if (item.Brand_Name__c) {
                            component.set('v.brand', item.Brand_Name__c);
                            component.set('v.brandList', item.Brand_Name__c);
                        }
                        if (item.Expiration_Date_New__c) {
                            component.set('v.expirationDate', item.Expiration_Date_New__c);
                            self.calculateWarrantyStatus(component);
                        }
                        if (!component.get('v.dropOfDate')) {
                            component.set('v.warrantyStatus', 'Pending');
                        } else {
                            self.calculateWarrantyStatus(component);
                        }
                        if (item.Warranty__c) {
                            component.set('v.warrantyId', item.Warranty__c);
                        }
                        if (item.Product__r && (item.Product__r.Product_Type__c == 'Battery' || item.Product__r.Product_Type__c == 'Charger')) {
                            component.set('v.isProduct', false);
                        } else {
                            component.set('v.isProduct', true);
                        }
                        self.calculateTotalHourAndPrice(component);
                    }
                });
            }
        }
    },
    AdditionalParts: function (component) {
        var newPart = {};
        var partsItemList = component.get('v.partsItemList');
        newPart.itemNumber = '';
        newPart.addFromAdditionParts = true;
        newPart.Name = '';
        newPart.quantity = 1;
        newPart.editable = true;
        partsItemList.push(newPart);
        component.set('v.partsItemList', partsItemList)
    },
    calculateTotalHourAndPrice: function (component) {
        let self = this;
        let decPickupFeeSubtotal = component.get("v.decPickupFeeSubtotal");
        let partsItemList = component.get('v.partsItemList');
        console.log('partsItemList.size', partsItemList);
        let labourHours, price = 0;
        let markup = 0;
        for (let i = 0; i < partsItemList.length; i++) {
            price += Number(partsItemList[i].total);
            console.log('price-index:', price);
        }

        if (price > 0 && partsItemList && partsItemList.length > 0) {
            markup = (price * Number(component.get('v.markupbfb')) / 100).toFixed(2);
            component.set('v.markup', markup);
        }
        else {
            component.set('v.markup', 0);
        }
        price += component.get('v.replacementBaseFee');
        price = Math.round(price * 100) / 100;
        component.set('v.partsCost', 0);
        component.set('v.partsCost', price);
        console.log(component.get('v.partsCost'));
        console.log('partsCost' + price);
        let finalHours = 0;
        if (component.get('v.overTimeHour') !== '' && component.get('v.overTimeHour') !== 0) {
            labourHours = component.get('v.overTimeHour');
            finalHours = labourHours;
        } else {
            labourHours = component.get('v.labourHours');
            let repaireType = component.get('v.repairType');
            if (repaireType !== 'Labor Time Only') {
                labourHours = 0;
                for (let i = 0; i < partsItemList.length; i++) {
                    labourHours += partsItemList[i].LaborTime * partsItemList[i].quantity;
                }

                let laborTimeCombine = self.calculateLaborTimeForCombineParts(component);
                if (laborTimeCombine) {
                    labourHours = laborTimeCombine;
                }
            }
            let additionalHour = component.get('v.additionalTimeHour');
            if (additionalHour) {
                finalHours = Number(labourHours) + Number(additionalHour);
            } else {
                finalHours = Number(labourHours);
            }
        }
        if (component.get("v.boolIsAltaQuip") === true) {
            labourHours = 60;
            finalHours = 60;
        }

        component.set('v.finialLaborHour', (Number(finalHours) / 60).toFixed(2));
        if (component.get('v.isServiceLive')) {
            self.selectTier(component);
        }
        else {
            let laborHourSubtotal = (Number(finalHours) / 60).toFixed(2) * Number(component.get('v.LaborRate'));
            component.set('v.laborCostSubtotal', Number(laborHourSubtotal).toFixed(2));
            component.set('v.labourHours', Number(labourHours).toFixed(0));
            console.log('calculate totalprice');
            let taxTotal = 0;
            if (component.get('v.gst')) {
                taxTotal = taxTotal + Number(component.get('v.gst'));
            }
            if (component.get('v.hst')) {
                taxTotal = taxTotal + Number(component.get('v.hst'));
            }
            if (component.get('v.qst')) {
                taxTotal = taxTotal + Number(component.get('v.qst'));
            }
            if (component.get('v.pst')) {
                taxTotal = taxTotal + Number(component.get('v.pst'));
            }
            component.set('v.totalPrice', (Number(component.get('v.laborCostSubtotal')) + component.get('v.partsCost') + Number(component.get('v.markup')) + Number(component.get('v.diagnosisFee')) + Number(decPickupFeeSubtotal) + taxTotal).toFixed(2));
        }
    },
    getInventoryInfo: function (component) {
        let self = this,
            action = component.get("c.ShowMessageByInventory");
        action.setParams({
            customerId: component.get("v.customerId"),
            inventory: component.get("v.inventory"),
            productId: component.get("v.modelNum")
        });
        action.setCallback(this, function (response) {
            let state = response.getState();
            if (state === "SUCCESS") {
                let result = JSON.parse(response.getReturnValue()),
                    { account, PartsCost: partsCost } = result;
                if ($A.util.isEmpty(account) === false) {
                    let { ShippingCity, ShippingCountry, ShippingPostalCode, ShippingState, ShippingStreet, FirstName, LastName, Phone } = account[0] || {};
                    component.set("v.ShippingCity", ShippingCity || "");
                    component.set("v.ShippingCountry", ShippingCountry || "");
                    component.set("v.ShippingPostalCode", ShippingPostalCode || "");
                    component.set("v.ShippingState", ShippingState || "");
                    component.set("v.ShippingStreet", ShippingStreet || "");
                    component.set("v.replacementFirstName", FirstName || "");
                    component.set("v.replacementLastName", LastName || "");
                    component.set("v.replacementPhone", Phone || "");
                    component.set("v.partsCost", 0.00);
                    self.calculateTotalHourAndPrice(component);
                } else if ($A.util.isEmpty(partsCost) === false) {
                    self.updatePartsCost(component, partsCost);
                }
            } else {
                $A.get("e.force:showToast")
                    .setParams({
                        title: $A.get("$Label.c.CCM_Portal_Error"),
                        message: response.getError()[0].message,
                        type: "Error"
                    }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    getExplosiveInfo: function (component, activeIndex) {
        var self = this;
        var action = component.get("c.SetupBomList");
        action.setParams({
            "productId": component.get('v.modelNum'),
            'version': 'Version A'//component.get('v.version')
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log('state--->' + state);
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if (results) {
                    component.set('v.explosiveDataList', results.ProductList);
                    if (component.get('v.contents').length == 0) {
                        component.set('v.contents', results.contentIdList);
                    }
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert($A.get("$Label.c.CCM_Portal_Error") + ': ' + errors[0].message);
                    }
                } else {
                    alert($A.get("$Label.c.CCM_Portal_ErrorTips"));
                }
            }
        });
        $A.enqueueAction(action);
    },
    saveClaimAsDraft: function (component) {
        let self = this;
        let userInfo = $A.get("$SObjectType.CurrentUser");
        let userId = '';
        if (userInfo.Id) {
            userId = userInfo.Id;
        }
        let service = component.get('v.serviceOption');
        let boolIsAltaQuip = component.get("v.boolIsAltaQuip");
        let overTimeHour = component.get('v.overTimeHour');
        let additionalTimeHour = component.get('v.additionalTimeHour');
        let partList = component.get('v.partsItemList');
        if (service == 'Replacement' && !boolIsAltaQuip && overTimeHour == 0 && additionalTimeHour == 0 && partList.length == 0) {
            component.set('v.labourHours', 0);
            component.set('v.laborCostSubtotal', 0);
        }
        var Level2ExplanationOption = component.get('v.Level2ExplanationOption');
        let claimInfo = {
            warrantyItemId: component.get("v.warrantyItemId"),
            userId: userId,
            replacementBaseFee: Number(component.get('v.replacementBaseFee')),
            diagnosisFee: Number(component.get('v.diagnosisFee')).toFixed(2),
            labourHours: Number(component.get('v.labourHours')),
            partsList: component.get('v.partsItemList'),
            repairDescription: component.get('v.repairDescription'),
            overTimeHour: String(component.get('v.overTimeHour')),
            overTimeDescription: component.get('v.overTimeDescription'),
            additionalTimeHour: String(component.get('v.additionalTimeHour')),
            partsCost: component.get('v.partsCost'),
            markup: Number(component.get('v.markup')),
            totalPrice: String(component.get('v.totalPrice')),
            dropOfDate: component.find('dropOfDate').get('v.value'),
            repairDate: component.find('repairDate').get('v.value'),
            warrantyId: component.get('v.warrantyId'),
            productId: component.get('v.modelNum'),
            customerId: component.get('v.customerId'),
            laborCostSubtotal: String(component.get('v.laborCostSubtotal')),
            boolPickup: component.get("v.strUsePickup") === "Yes",
            isPickUp: component.get("v.isPickUp"),
            decPickupDistance: Number(component.get("v.decPickupDistance")),
            decPickupFeeSubtotal: Number(component.get("v.decPickupFeeSubtotal")),
            serviceOption: component.get('v.serviceOption'),
            repairType: component.get('v.repairType'),
            auditComments: component.get('v.auditComments'),
            recallOption: component.get('v.recallOption'),
            failureCode: component.get('v.failureCode'),
            project: component.get('v.project'),
            repairableParts: component.get('v.majorIssueObj'),
            inventory: component.get('v.inventory'),
            ShippingStreet: component.get('v.ShippingStreet'),
            ShippingCity: component.get('v.ShippingCity'),
            ShippingCountry: component.get('v.ShippingCountry'),
            ShippingState: component.get('v.ShippingState'),
            ShippingPostalCode: component.get('v.ShippingPostalCode'),
            replacementFirstName: component.get('v.replacementFirstName'),
            replacementLastName: component.get('v.replacementLastName'),
            replacementPhone: component.get('v.replacementPhone'),
            BillTo: component.get('v.billToId'),
            claimId: self.getUrlParameter('recordId') ? self.getUrlParameter('recordId') : '',
            tierId: component.get('v.tierId'),
            isServiceLive: component.get('v.isServiceLive'),
            gst: component.get('v.gst'),
            hst: component.get('v.hst'),
            qst: component.get('v.qst'),
            pst: component.get('v.pst'),
            explanationOptions: component.get('v.explanationOptionSelected'),
            level1ExplanationOption: component.get('v.Level1ExplanationOption'),
            level2ExplanationOption: Level2ExplanationOption
        };
        let action = component.get('c.saveDraftWarrantyClaim');
        action.setParams({
            'contentStr': JSON.stringify(claimInfo),
        });
        action.setCallback(this, function (response) {
            let state = response.getState();
            if (state === 'SUCCESS') {
                let result = JSON.parse(response.getReturnValue());
                if (result.Status == 'Success') {
                    window.location.href = '/s/servicehome'
                } else {
                    $A.get("e.force:showToast").setParams({
                        "title": $A.get("$Label.c.CCM_Portal_Error"),
                        "message": result.Message,
                        "type": "Error"
                    }).fire();
                    self.hideEle(component, 'spinner');
                }

            } else {
                $A.get("e.force:showToast").setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                    "message": response.getError()[0].message,
                    "type": "Error"
                }).fire();
                self.hideEle(component, 'spinner');
            }
        });
        $A.enqueueAction(action);
    },
    saveClaim: function (component) {
        let self = this;
        let userInfo = $A.get("$SObjectType.CurrentUser");
        let userId = '';
        if (userInfo.Id) {
            userId = userInfo.Id;
        }
        let service = component.get('v.serviceOption');
        let boolIsAltaQuip = component.get("v.boolIsAltaQuip");
        let overTimeHour = component.get('v.overTimeHour');
        let additionalTimeHour = component.get('v.additionalTimeHour');
        let partList = component.get('v.partsItemList');
        if (service == 'Replacement' && !boolIsAltaQuip && overTimeHour == 0 && additionalTimeHour == 0 && partList.length == 0) {
            component.set('v.labourHours', 0);
            component.set('v.laborCostSubtotal', 0);
        }
        if (component.get("v.inventory") && component.get("v.inventory") == 'Dealer inventory') {
            if (component.get('v.partsCost') == 0) {
                self.hideEle(component, 'spinner');
                return;
            }
        }
        var Level2ExplanationOption = component.get('v.Level2ExplanationOption');
        let claimInfo = {
            warrantyItemId: component.get("v.warrantyItemId"),
            userId: userId,
            replacementBaseFee: Number(component.get('v.replacementBaseFee')),
            diagnosisFee: Number(component.get('v.diagnosisFee')).toFixed(2),
            labourHours: Number(component.get('v.labourHours')),
            partsList: component.get('v.partsItemList'),
            repairDescription: component.get('v.repairDescription'),
            overTimeHour: String(component.get('v.overTimeHour')),
            overTimeDescription: component.get('v.overTimeDescription'),
            additionalTimeHour: String(component.get('v.additionalTimeHour')),
            partsCost: component.get('v.partsCost'),
            markup: Number(component.get('v.markup')),
            totalPrice: String(component.get('v.totalPrice')),
            dropOfDate: component.find('dropOfDate').get('v.value'),
            repairDate: component.find('repairDate').get('v.value'),
            warrantyId: component.get('v.warrantyId'),
            productId: component.get('v.modelNum'),
            customerId: component.get('v.customerId'),
            laborCostSubtotal: String(component.get('v.laborCostSubtotal')),
            boolPickup: component.get("v.strUsePickup") === "Yes",
            isPickUp: component.get("v.isPickUp"),
            decPickupDistance: Number(component.get("v.decPickupDistance")),
            decPickupFeeSubtotal: Number(component.get("v.decPickupFeeSubtotal")),
            serviceOption: component.get('v.serviceOption'),
            repairType: component.get('v.repairType'),
            auditComments: component.get('v.auditComments'),
            recallOption: component.get('v.recallOption'),
            failureCode: component.get('v.failureCode'),
            project: component.get('v.project'),
            repairableParts: component.get('v.majorIssueObj'),
            inventory: component.get('v.inventory'),
            ShippingStreet: component.get('v.ShippingStreet'),
            ShippingCity: component.get('v.ShippingCity'),
            ShippingCountry: component.get('v.ShippingCountry'),
            ShippingState: component.get('v.ShippingState'),
            ShippingPostalCode: component.get('v.ShippingPostalCode'),
            replacementFirstName: component.get('v.replacementFirstName'),
            replacementLastName: component.get('v.replacementLastName'),
            replacementPhone: component.get('v.replacementPhone'),
            BillTo: component.get('v.billToId'),
            claimId: self.getUrlParameter('recordId') ? self.getUrlParameter('recordId') : '',
            tierId: component.get('v.tierId'),
            isServiceLive: component.get('v.isServiceLive'),
            gst: component.get('v.gst'),
            hst: component.get('v.hst'),
            qst: component.get('v.qst'),
            pst: component.get('v.pst'),
            explanationOptions: component.get('v.explanationOptionSelected'),
            level1ExplanationOption: component.get('v.Level1ExplanationOption'),
            level2ExplanationOption: Level2ExplanationOption,
            additionalInformation:component.get('v.isShowAdditionalInformation')
        };
        let action = component.get('c.SaveWarrantyClaim');
        action.setParams({
            'contentStr': JSON.stringify(claimInfo),
        });
        action.setCallback(this, function (response) {
            let state = response.getState();
            if (state === 'SUCCESS') {
                let result = JSON.parse(response.getReturnValue());
                if (result.Status == 'Success') {
                    window.location.href = '/s/servicehome'
                } else {
                    $A.get("e.force:showToast").setParams({
                        "title": $A.get("$Label.c.CCM_Portal_Error"),
                        "message": result.Message,
                        "type": "Error"
                    }).fire();
                    self.hideEle(component, 'spinner');
                }

            } else {
                $A.get("e.force:showToast").setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                    "message": response.getError()[0].message,
                    "type": "Error"
                }).fire();
                self.hideEle(component, 'spinner');
            }
        });
        $A.enqueueAction(action);
    },
    getFailureCode: function (component) {
        var action = component.get("c.GenerateFailureCodePicklistValue");
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                var data = JSON.parse(results);
                component.set('v.failureCodeList', data);
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert($A.get("$Label.c.CCM_Portal_Error") + ': ' + errors[0].message);
                    }
                } else {
                    alert($A.get("$Label.c.CCM_Portal_ErrorTips"));
                }
            }
        });
        $A.enqueueAction(action);
    },
    getProject: function (component) {
        var action = component.get("c.GenerateProjectPicklistValue");
        action.setParams({
            "productId": component.get('v.modelNum'),
            "customerId": component.get('v.customerId'),
            "dropOffDate": component.get('v.dropOfDate')
        });

        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                var data = JSON.parse(results);
                if (data.Name.length !== 0) {
                    component.set('v.projectList', data.Name);
                    component.set('v.recallOption', data.Name.Solution);
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert($A.get("$Label.c.CCM_Portal_Error") + ': ' + errors[0].message);
                    }
                } else {
                    alert($A.get("$Label.c.CCM_Portal_ErrorTips"));
                }
            }
        });
        $A.enqueueAction(action);
    },
    getUrlParameter: function (sParam) {
        var sPageURL = decodeURIComponent(window.location.search.substring(1)),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');
            if (sParameterName[0] === sParam || sParameterName[0] === ('0.' + sParam)) {
                return sParameterName[1] === undefined ? true : sParameterName[1];
            }
        }
    },
    getValidation: function (component) {
        var valid = true;
        //valid = valid && this.getElementRequiredError(component, 'emailAddress');
        //valid = valid && this.getElementRequiredError(component, 'firstName');
        //valid = valid && this.getElementRequiredError(component, 'lastName');
        valid = valid && this.getElementRequiredError(component, 'brand');
        valid = valid && this.getElementRequiredError(component, 'serialNumber');
        valid = valid && this.getElementRequiredError(component, 'dropOfDate');
        valid = valid && this.getElementRequiredError(component, 'repairDate');
        valid = valid && this.getElementRequiredError(component, 'billTo');

        valid = valid && this.getElementRequiredError(component, 'description');
        valid = valid && this.getElementRequiredError(component, 'serviceoption');
        // let orgCode = component.get('v.orgCode');
        // if( 'CCA' === orgCode) {
        //     valid = valid && this.getElementRequiredError(component, 'GST');
        //     valid = valid && this.getElementRequiredError(component, 'HST');
        //     valid = valid && this.getElementRequiredError(component, 'QST');
        //     valid = valid && this.getElementRequiredError(component, 'PST');
        // }
        valid = valid && this.checkExplanation(component);
        if (component.get('v.serviceOption') == 'Replacement' && component.get("v.inventory") == 'Chervon inventory') {
            valid = valid && this.getElementRequiredError(component, 'address');
            valid = valid && this.getElementRequiredError(component, 'firstNa');
            valid = valid && this.getElementRequiredError(component, 'LastNa');
            valid = valid && this.getElementRequiredError(component, 'phone');
        }
        if (component.get('v.serviceOption') == 'Replacement') {
            valid = valid && this.getElementRequiredError(component, 'replacementoption');
            var isHaveLevel1 = component.find('Level1ExplanationOption');
            if(isHaveLevel1){
                valid = valid && this.getElementRequiredError(component, 'Level1ExplanationOption');
            }
            var inputCmp = component.find('Level2ExplanationOption');
            if(inputCmp){
                if(component.get('v.Level1ExplanationOption') == 'Part(s) Out Of Stock'){
                    valid = valid && !component.get('v.Level2ExplanationOptionIsError');
                }else{
                    let isValid = false;
                    if(Array.isArray(inputCmp)){
                        let componentTemp = inputCmp[0].get('v.validity');
                        if(componentTemp){
                            isValid = componentTemp.valid;
                        } 
                    }else{ 
                        let componentTemp = inputCmp.get('v.validity')
                        if(componentTemp){
                            isValid = componentTemp.valid;
                        }    
                    }
                    valid = valid && isValid;
                }
            }
            var inputCmp2 = component.find('Level2ExplanationOptionExtra');
            if(inputCmp2){
                let isValid = false; 
                let componentTemp = inputCmp2.get('v.validity')
                if(componentTemp){
                    isValid = componentTemp.valid;
                }  
                valid = valid && isValid;
            }
            
            if (valid) {
                let ele = 'repairtype'
                var element = component.find(ele);
                $A.util.removeClass(element, 'field-error');
                var requiredText = component.find(ele + '-error-required');
                if (requiredText) {
                    $A.util.addClass(requiredText, 'slds-hide');
                }
            }
        }

        if (component.get('v.serviceOption') == 'Repair') {
            valid = valid && this.getElementRequiredError(component, 'repairtype');
            if (valid) {
                let ele = 'replacementoption'
                var element = component.find(ele);
                $A.util.removeClass(element, 'field-error');
                var requiredText = component.find(ele + '-error-required');
                if (requiredText) {
                    $A.util.addClass(requiredText, 'slds-hide');
                }
            }
        }

        if (component.get('v.repairType') == 'Parts') {
            valid = valid && this.getElementRequiredError(component, 'majorIssue');
        }
        if (component.get('v.serviceOption') == 'Recall') {
            valid = valid && this.getElementRequiredError(component, 'recallOption');
        }
        if (component.get('v.overTimeHour')) {
            valid = valid && this.getElementRequiredError(component, 'additionalExplantion');
        }
        return valid;
    },
    getElementRequiredError: function (component, ele) {
        var val;
        var valid;
        var requiredText = component.find(ele + '-error-required');
        var element = component.find(ele);
        if (ele.toUpperCase().indexOf('MAJORISSUE') > -1 && component.get('v.majorIssueObj')) {
            val = JSON.parse(JSON.stringify(component.get('v.majorIssueObj'))).Name;
        } else if (ele.toUpperCase().indexOf('ADDRESS') > -1) {
            if (component.get('v.ShippingStreet') && component.get('v.ShippingCity') && component.get('v.ShippingCountry') && component.get('v.ShippingState') && component.get('v.ShippingPostalCode')) {
                val = true;
            }
        } else {
            val = element.get('v.value');
        }
        if (val && val instanceof String) {
            val = val.trim();
        }
        valid = !!val;
        if (valid) {
            $A.util.removeClass(element, 'field-error');
            if (requiredText) {
                $A.util.addClass(requiredText, 'slds-hide');
            }
        } else {
            $A.util.addClass(element, 'field-error');
            if (requiredText) {
                $A.util.removeClass(requiredText, 'slds-hide');
            }
        }
        return valid;
    },
    checkExplanation: function (component) {
        let valid = true;
        let explanationOptionSelected = component.get("v.explanationOptionSelected");
        let additionalTimeHour = component.get("v.additionalTimeHour");

        let element = component.find("additionalExplantion-error-required");
        if (additionalTimeHour && additionalTimeHour !== '0' && !explanationOptionSelected) {
            valid = false;
            $A.util.removeClass(element, 'slds-hide');
        }
        else {
            $A.util.addClass(element, 'slds-hide');
        }
        return valid;
    },
    GenerateVersionList: function (component, productId) {
        var action = component.get("c.GenerateVersionList");
        action.setParams({
            "productId": productId,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                console.log(results);
                if (results) {
                    component.set('v.versionList', results.Version);
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert($A.get("$Label.c.CCM_Portal_Error") + ': ' + errors[0].message);
                    }
                } else {
                    alert($A.get("$Label.c.CCM_Portal_ErrorTips"));
                }
            }
        });
        $A.enqueueAction(action);
    },
    searchParts: function (component) {
        var self = this;
        var action = component.get("c.SetupBomList");
        action.setParams({
            "productId": component.get('v.modelNum'),
            "version": component.get('v.version')
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log('state--->' + state);
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                console.log(results);
                if (results) {
                    results.ProductList.sort(function (a, b) { return a.ExplosionID__c - b.ExplosionID__c });
                    let explosiveDataList = (results.ProductList || []).filter(item => (item.ExplosionID__c !== undefined && item.ExplosionID__c !== ''));
                    component.set('v.explosiveDataList', explosiveDataList);
                    component.set('v.contents', results.contentIdList);
                    self.showEle(component, 'explosiveContent');
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert($A.get("$Label.c.CCM_Portal_Error") + ': ' + errors[0].message);
                    }
                } else {
                    alert($A.get("$Label.c.CCM_Portal_ErrorTips"));
                }
            }
        });
        $A.enqueueAction(action);
    },
    showEle: function (component, ele) {
        $A.util.removeClass(
            component.find(ele),
            "slds-hide"
        );
    },
    hideEle: function (component, ele) {
        $A.util.addClass(
            component.find(ele),
            "slds-hide"
        );
    },
    clearAddress: function (component) {
        component.set("v.ShippingCity", "");
        component.set("v.ShippingCountry", "");
        component.set("v.ShippingPostalCode", "");
        component.set("v.ShippingState", "");
        component.set("v.ShippingStreet", "");
        component.set("v.replacementFirstName", "");
        component.set("v.replacementLastName", "");
        component.set("v.replacementPhone", "");
    },
    updatePartsCost: function (component, increment) {
        let localPartsCost = (component.get("v.partsItemList") || []).reduce(((total, item) => total + +item.total), 0);
        component.set("v.partsCost", localPartsCost + (increment || 0));
        component.set("v.replacementBaseFee", (increment || 0));
        let taxTotal = 0;
        if (component.get('v.gst')) {
            taxTotal = taxTotal + Number(component.get('v.gst'));
        }
        if (component.get('v.hst')) {
            taxTotal = taxTotal + Number(component.get('v.hst'));
        }
        if (component.get('v.qst')) {
            taxTotal = taxTotal + Number(component.get('v.qst'));
        }
        if (component.get('v.pst')) {
            taxTotal = taxTotal + Number(component.get('v.pst'));
        }
        component.set(
            "v.totalPrice",
            (
                Number(component.get("v.laborCostSubtotal")) +
                Number(component.get("v.partsCost")) +
                Number(component.get("v.diagnosisFee")) +
                Number(component.get("v.decPickupFeeSubtotal")) +
                taxTotal
            ).toFixed(2));
    },
    getBusinessName: function (component) {
        let zipCode = component.get("v.zipCode");
        component.set("v.tierlist", []);
        component.set("v.tierId", '');
        if (!zipCode) {
            return;
        }
        let action = component.get("c.getTierByZipCode")
        action.setParams({
            'zipCode': zipCode
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state == "SUCCESS") {
                let result = JSON.parse(response.getReturnValue());
                if (result) {
                    component.set("v.tierlist", result);
                    if (result.length == 1) {
                        component.set("v.tierId", result[0].Id);
                    }
                }
            } else {
                // show error message
            }
        });
        $A.enqueueAction(action);
    },
    selectTier: function (component) {
        let serviceOption = component.get("v.serviceOption");
        if (serviceOption === 'Service Attempt') {
            let laborCost = 100;
            component.set("v.laborCostSubtotal", laborCost);
            component.set('v.partsCost', 0);
            component.set('v.markup', 0);
            component.set('v.diagnosisFee', 0);
            component.set("v.totalPrice", Number(component.get('v.laborCostSubtotal')));
            return;
        }

        let value = component.get("v.tierId");
        // show tier level and cost
        let tierList = component.get("v.tierlist");
        let laborCost = 0;
        if (value && tierList && tierList.length > 0) {
            let selectedTier = tierList.find(item => item.Id == value);
            if (selectedTier && serviceOption) {
                if (selectedTier.Tier__c === 'Tier 3') {
                    laborCost = 238;
                } else if (selectedTier.Tier__c === 'Tier 2') {
                    laborCost = 198;
                } else if (selectedTier.Tier__c === 'Tier 1') {
                    laborCost = 178;
                }
            }
        }
        component.set("v.laborCostSubtotal", laborCost);
        if (serviceOption === 'Repair') {
            component.set("v.totalPrice", Number(component.get('v.laborCostSubtotal')) + component.get('v.partsCost') + Number(component.get('v.markup')));
        }
        else if (serviceOption === 'Replacement') {
            component.set('v.markup', 0);
            component.set("v.totalPrice", Number(component.get('v.laborCostSubtotal')) + component.get('v.partsCost'));
        }
    },
    checkWearablePartsAvaibility: function (component, partItem, purchaseDate, purchaseUseType, partsItemList, index) {
        let period = undefined;
        if (purchaseUseType === 'Industrial/Professional/Commercial') {
            period = partItem.wearablePeriodCommercial;
        }
        else if (purchaseUseType === 'Residential') {
            period = partItem.wearablePeriodResidential;
        }

        if (partItem.partsWearable && period) {
            let dropoffDate = component.get('v.dropOfDate');
            let dropoffDate2 = new Date(dropoffDate);
            let thresholdDate = dropoffDate2.setDate(dropoffDate2.getDate() - parseInt(period));
            thresholdDate = new Date(thresholdDate);
            if (new Date(dropoffDate) < new Date(purchaseDate) || thresholdDate > new Date(purchaseDate)) {
                // exceed the wearable period
                $A.get("e.force:showToast").setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                    "message": $A.get("$Label.c.CCM_Portal_Thispartisoutofwarranty"),
                    "type": "error"
                }).fire();

                if (index) {
                    partsItemList.splice(index, 1);
                    component.set('v.partsItemList', partsItemList);
                }
                return false;
            }
            else {
                return true;
            }
        }
        else {
            return true;
        }
    },
    getExplanationOptions: function (component) {
        let action = component.get("c.getExplanationOption");
        action.setParams({});
        action.setCallback(this, function (response) {
            let state = response.getState();
            if (state === 'SUCCESS') {
                let selectedOptionsStr = component.get('v.explanationOptionSelected');
                let options = JSON.parse(response.getReturnValue());
                if (selectedOptionsStr) {
                    let selectedOptions = selectedOptionsStr.split(';');
                    options.forEach(option => {
                        if (selectedOptions.includes(option.value)) {
                            option.selected = true;
                        }
                        else {
                            option.selected = false;
                        }
                    });
                } else {
                    options.forEach(option => option.selected = false);
                }
                component.set('v.explanationOptions', options);
            }
        });
        $A.enqueueAction(action);
    },
    initPartsCombineLaborTimeCost: function (component) {
        let action = component.get('c.getPartsCombineLaborTimeCost');
        let productId = component.get('v.modelNum');
        let modelNumList = component.get('v.modelNumList');
        if (productId) {
            let matchProducts = modelNumList.filter(item => item.Product__c == productId);
            if (matchProducts) {
                let productCode = matchProducts[0].Product_Code__c;
                action.setParams({
                    'productCode': productCode
                });
                action.setCallback(this, function (response) {
                    if (response.getState() === 'SUCCESS') {
                        let result = JSON.parse(response.getReturnValue());
                        component.set('v.partsCombimeLaborTimeCode', result);
                    }
                });
                $A.enqueueAction(action)
            }
        }
    },
    calculateLaborTimeForCombineParts: function (component) {
        let laborTimeCombine = undefined;
        let partsItemList = component.get('v.partsItemList');
        let partsCombimeLaborTimeCode = component.get('v.partsCombimeLaborTimeCode');
        if (partsItemList && partsCombimeLaborTimeCode) {
            partsCombimeLaborTimeCode.forEach(item => {
                let combinePartsCode = [...item.partcodes];
                let labortime = Number(item.labortime);
                if (combinePartsCode && combinePartsCode.length == partsItemList.length) {
                    let selectParts = [];
                    partsItemList.forEach(partitem => {
                        selectParts.push(partitem.itemNumber);
                    });
                    selectParts.forEach(partcode => {
                        let idx = combinePartsCode.findIndex(code => code == partcode);
                        if (idx >= 0) {
                            combinePartsCode.splice(idx, 1);
                        }
                    });
                    if (combinePartsCode.length == 0) {
                        laborTimeCombine = labortime;
                    }
                }
            });
        }
        return laborTimeCombine;
    },
    checkShowAdditionalInformation: function (component, productCode){
        var action = component.get("c.isShowAdditionalInformation");
        action.setParams({
            "productCode": productCode,
        });
        action.setCallback(this, function (response){
            var state = response.getState();
            if (state == "SUCCESS"){
                var result = response.getReturnValue();
                if(result == false){
                    component.set('v.isShowAdditionalInformation', false);
                }else{
                    component.set('v.isShowAdditionalInformation', true);
                }
            }
        });
        $A.enqueueAction(action);
    },
    getCaseExplanationOptions: function (component) {
        let action = component.get("c.getCaseExplanationOptions");
        action.setParams({});
        action.setCallback(this, function (response) {
            let state = response.getState();
            if (state === 'SUCCESS') {
                let result = JSON.parse(response.getReturnValue());
                component.set('v.Level1ExplanationOptions', result);
                component.set('v.Level2ExplanationOptions', [
                    {label: $A.get("$Label.c.CCM_Portal_PartsOutOfStock"), value: $A.get("$Label.c.CCM_Portal_Partonextendedbackorder")},
                    {label: $A.get("$Label.c.CCM_Portal_PartNotAvailableOnSBOM"), value: $A.get("$Label.c.CCM_Portal_Partneedednotavailable")},
                    {label: $A.get("$Label.c.CCM_Portal_CustomerDissatisfactionDuetoLongRepairTime"), value: $A.get("$Label.c.CCM_Portal_Tooldropoffdate")}]);
            }
        });
        $A.enqueueAction(action);
    },
    searchAllPartsByProduct: function(component){
        // helper.showEle(component, 'spinner');
        var action = component.get("c.searchAllPartsByProduct");
        action.setParams({
           productId: component.get('v.modelNum'),
        });
        action.setCallback(this, function (response) {
           var state = response.getState();
           if (state === "SUCCESS") {
               var result = response.getReturnValue();
               if(result){
                    component.set('v.partsOption', JSON.parse(result).PartsList);
                    component.set('v.isShowLevel2ExplanationOption', JSON.parse(result).isShowLevel2ExplanationOption);
               }
           } else {
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": $A.get("$Label.c.CCM_Portal_Error"),
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
           }
        });
        $A.enqueueAction(action);
    }
})