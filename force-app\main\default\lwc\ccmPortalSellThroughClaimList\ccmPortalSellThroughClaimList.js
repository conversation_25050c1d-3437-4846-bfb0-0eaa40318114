import { LightningElement, track } from 'lwc';
import getAllClaimRequest from '@salesforce/apex/CCM_ClaimRequestCtl.getAllClaimRequest';
import { NavigationMixin } from 'lightning/navigation';
import { ShowToastEvent } from "lightning/platformShowToastEvent";
import momentJs from '@salesforce/resourceUrl/momentJs';
import { loadScript } from 'lightning/platformResourceLoader';
import CLAIM_REQUEST from '@salesforce/schema/Claim_Request__c';

// 导入 Custom Label
import CCM_Portal_RaiseAClaim from "@salesforce/label/c.CCM_Portal_RaiseAClaim";
import CCM_Portal_PromotionClaimRequestNumber from "@salesforce/label/c.CCM_Portal_PromotionClaimRequestNumber";
import CCM_Portal_PromotionName from "@salesforce/label/c.CCM_Portal_PromotionName";
import CCM_Portal_PromotionCode from "@salesforce/label/c.CCM_Portal_PromotionCode";
import CCM_Portal_TotalClaimAmount from "@salesforce/label/c.CCM_Portal_TotalClaimAmount";
import CCM_Portal_CreatedDate from "@salesforce/label/c.CCM_Portal_CreatedDate";
import CCM_Portal_LastModifiedDate from "@salesforce/label/c.CCM_Portal_LastModifiedDate";
import CCM_Portal_CreatedBy from "@salesforce/label/c.CCM_Portal_CreatedBy";
import CCM_Portal_ClaimStatus from "@salesforce/label/c.CCM_Portal_ClaimStatus";
import CCM_Portal_Action from "@salesforce/label/c.CCM_Portal_Action";
import CCM_Portal_LoadDataError from "@salesforce/label/c.CCM_Portal_LoadDataError";
import CCM_Portal_Draft from "@salesforce/label/c.CCM_Portal_Draft";
import CCM_Portal_Rejected from "@salesforce/label/c.CCM_Portal_Rejected";
import CCM_Portal_Language from "@salesforce/label/c.CCM_Portal_Language";



export default class CcmPortalSellThroughClaimList extends NavigationMixin(LightningElement) {

    // 定义 label
    @track label = {
        CCM_Portal_RaiseAClaim,
        CCM_Portal_PromotionClaimRequestNumber,
        CCM_Portal_PromotionName,
        CCM_Portal_PromotionCode,
        CCM_Portal_TotalClaimAmount,
        CCM_Portal_CreatedDate,
        CCM_Portal_LastModifiedDate,
        CCM_Portal_CreatedBy,
        CCM_Portal_ClaimStatus,
        CCM_Portal_Action,
        CCM_Portal_LoadDataError,
        CCM_Portal_Draft,
        CCM_Portal_Rejected,
        CCM_Portal_Language
    }
    totalCounts = 0;
    pageSize = 10;
    pageIndex = 0;

    @track
    claimRequestList = [];

    showSpinner = false;

    connectedCallback(){
        if(!this.connected){
            loadScript(this, momentJs+'/moment.min.js').then(()=>{
                this.getPageData(this.pageIndex,this.pageSize);
            })
        }
    }

    handleGoToPage(event){
        this.pageIndex = event.detail;
        this.getPageData(this.pageIndex,this.pageSize);
    }

    /**
     * Allen, Comment on 2021/05/27
     * handle change page size on paginator
    */
    handlePageSizeChange(event) {
        this.pageIndex = 0;
        this.pageSize = event.detail;
        this.getPageData(this.pageIndex,this.pageSize);
    }

    /**
     * Allen, Comment on 2021/05/27
     * get claim list, gaging fetch
    */
    getPageData(pageIndex, pageSize){
        this.showSpinner = true;
        getAllClaimRequest({pageIndex:pageIndex, pageSize:pageSize}).then(data=>{
            this.showSpinner = false;
            console.log(data);
            this.totalCounts = data.totalCounts;
            this.claimRequestList = data.claimRequestList.map(item=>{
                // add haibo: french
                if (this.label.CCM_Portal_Language == 'fr') {
                    if (item.Promotion_Code__c) {
                        item.Promotion_Code__r.Name = item.Promotion_Code__r.Promotion_Name_French__c || item.Promotion_Code__r.Name;
                    } 
                }
                item.isDraft = item.Claim_Status__c === this.label.CCM_Portal_Draft || item.Claim_Status__c === this.label.CCM_Portal_Rejected;
                item.CreatedDateStr = moment(item.CreatedDate).format("MM-DD-YYYY");
                item.LastModifiedDateStr = moment(item.LastModifiedDate).format("MM-DD-YYYY");
                return item;
            });
        },error=>{
            console.log(error);
            this.dispatchEvent(
                new ShowToastEvent({
                    title: this.label.CCM_Portal_LoadDataError,
                    message: error.body.message,
                    variant: 'error'
                })
            );
        })
    }

    handleClaimClickView(event){
        let _id = event.currentTarget.dataset.id;
        this[NavigationMixin.Navigate]({
            type: 'standard__recordPage',
            attributes: {
                recordId: _id,
                objectApiName: CLAIM_REQUEST.objectApiName,
                actionName: 'view'
            },
            state: {
                c__mode: "view"
            }
        });
    }
    handleClaimClickEdit(event){
        let _id = event.currentTarget.dataset.id;
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                name: "Sell_Through_Promotion_Claim__c"
            },
            state: {
                c__claimid: _id
            }
        });
    }

    handleClickRaiseClaim(event){
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                name: "Sell_Through_Promotion_Claim__c"
            }
        });
    }
}