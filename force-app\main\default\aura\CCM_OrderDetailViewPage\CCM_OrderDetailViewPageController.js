({
    doInit: function(component, event, helper){
        component.set('v.isBusy', true);

        let isDesktop = false;
        if($A.get("$Browser.formFactor") === 'DESKTOP'){
            isDesktop = true;
        }
        // 临时关闭订单功能
        console.log("A_LOCK_ORDER_FUNC_TMP:",$A.get("$Label.c.A_LOCK_ORDER_FUNC_TMP"));
        component.set("v.blLockOrderFuncTmp", $A.get("$Label.c.A_LOCK_ORDER_FUNC_TMP") == 'True' || !isDesktop);

        //Community User展示的进度条
       var pathData1 = [
            {label: $A.get("$Label.c.CCM_Portal_NewOrder"), icon: 'edit_form'},
            {label: $A.get("$Label.c.CCM_Portal_Submitted"), icon: 'privately_shared'},
            {label: $A.get("$Label.c.CCM_Portal_OrderProcessing"), icon: 'locker_service_api_viewer'},
            {label: $A.get("$Label.c.CCM_Portal_PartialShipment"), icon: 'travel_and_places'},
            {label: $A.get("$Label.c.CCM_Portal_ShipComplete"), icon:'success'}
        ];

        component.set('v.processData', pathData1);

        component.set('v.shipmentColumns', [
            {label: $A.get("$Label.c.CCM_Portal_ProductName"), fieldName:'productName'},
            {label: $A.get("$Label.c.CCM_Portal_ProductModel"), fieldName: 'productModelNum'},
            {label: $A.get("$Label.c.CCM_Portal_ItemQuantityInShipment"), fieldName: 'itemQtyInShipment'},
            {label: $A.get("$Label.c.CCM_Portal_ItemQuantityInOrder"), fieldName: 'itemQtyInOrder'},
        ]);

        var invoiceColumns = [
            {
                label: $A.get("$Label.c.CCM_Portal_Action"),
                width: '80px',
                tdStyle: 'text-align: left',
                children:[
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${invoiceId}",
                        variant:"bare",
                        iconName:"utility:preview",
                        alternativeText:"View",
                        onclick: component.getReference('c.doView')
                    }
                },
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${invoiceId}",
                        variant:"bare",
                        iconName:"utility:download",
                        alternativeText:"Download",
                        onclick: component.getReference("c.doDownload")
                    }
                }]
            },
            {label: $A.get("$Label.c.CCM_Portal_InvoiceNumber"), fieldName:'invoiceNum'},
            {label: $A.get("$Label.c.CCM_Portal_TrackingNumber"), fieldName: 'trackingNum'},
            /*{label: 'Invoice Status', fieldName: 'invoiceStatus'},*/
            {label: $A.get("$Label.c.CCM_Portal_NumofShippingUnits"), fieldName: 'numOfShippingUnits'},
            {label: $A.get("$Label.c.CCM_Portal_TotalDue"), 
                children:[
                {
                    type: "lightning:formattedNumber",
                    attributes:{
                        value: "${totalDue}",
                        currencyCode: "${currencyIsoCode}",
                        currencyDisplayAs:"code",
                        style:"currency"
                    }
                }]
            },
            /*{label: 'Total Remaining', fieldName: 'totalRemaining'},*/
            {label: $A.get("$Label.c.CCM_Portal_Ispaid"), fieldName: 'isPaid'},
            {label: $A.get("$Label.c.CCM_Portal_OrderDate"), fieldName: 'orderDate'}];
        component.set('v.invoiceColumns', invoiceColumns);
        // update,napoleon,23-1-10,
        var recordId = helper.getUrlParameter('recordId');
        var action = null;
        if(recordId && recordId.indexOf("@parts") !== -1){
            recordId = recordId.split("@")[0];
            action = component.get("c.getOrderPartsInfo");
        } else {
            action = component.get("c.getData");
        }
        console.log('record Id end--->',recordId);
        // update end;

        action.setParam('recordId', recordId);
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if(results){
                    component.set('v.order', results.order);
                    // 后端传的是 Date 类型，不改变后端数据结构，修改年月日显示顺序。
                    // yyyy-MM-dd 到 MM-dd-yyyy
                    if(component.get('v.order.expectedDeliveryDate')){
                        let expectedDeliveryDate = component.get('v.order.expectedDeliveryDate').split('-');
                        component.set('v.order.expectedDeliveryDate', expectedDeliveryDate[1] + '-' + expectedDeliveryDate[2] + '-' + expectedDeliveryDate[0]);
                    }

                    if(!component.get('v.order.productPrice')){
                        component.set('v.order.productPrice', 0.00)
                    }
                    if(!component.get('v.order.freightFee')){
                        component.set('v.order.freightFee', 0.00)
                    }
                    if(!component.get('v.order.handingFee')){
                        component.set('v.order.handingFee', 0.00)
                    }
                    component.set('v.isInnerUser', results.isInnerUser);
                    console.log('results.order.orderCancelledType--->'+results.order.orderCancelledType);
                    if (results.order.orderCancelledType == 'Return'){
                        //如果是Return Order进度条坪有三个状思（New Return Order-Order Processing-Closed）
                        var pathData4 = [
                                        {label: $A.get("$Label.c.CCM_Portal_NewReturnGoods"), icon: 'edit_form'},
                                        {label: $A.get("$Label.c.CCM_Portal_OrderProcessing"), icon: 'privately_shared'},
                                        {label: $A.get("$Label.c.CCM_Portal_Closed"), icon:'success'}
                                    ];
                        component.set('v.processData', pathData4);
                    }else{
                        if(results.isInnerUser == true){
                            //Inner User展示的进度条
                            var pathData3 = [
                                        {label: $A.get("$Label.c.CCM_Portal_NewOrder"), icon: 'edit_form'},
                                        {label: $A.get("$Label.c.CCM_Portal_PendingReview"), icon: 'approval'},
                                        {label: $A.get("$Label.c.CCM_Portal_Booked"), icon: 'locker_service_api_viewer'},
                                        {label: $A.get("$Label.c.CCM_Portal_OrderProcessing"), icon: 'privately_shared'},
                                        {label: $A.get("$Label.c.CCM_Portal_PartialShipment"), icon: 'travel_and_places'},
                                        {label: $A.get("$Label.c.CCM_Portal_ShipComplete"), icon:'success'}
                                    ];
                            component.set('v.processData', pathData3);
                        }else{
                            component.set('v.processData', pathData1);
                        }
                    }

                    component.set("v.paymentTermVal", results.paymentTermVal);
                    component.set("v.freightTermVal", results.freightTermVal);
                    component.set("v.isAlternativeAddress", results.order.isAlternativeAddress);
                    component.set('v.orderItemList', results.orderItems);
                    component.set('v.currentStep', results.currentStep);
                    component.set('v.customerId', results.order.customerId);
                    component.set('v.brandScope', results.order.brandScopeName);
                    component.set('v.isDelegate', results.order.isDelegated);
                    component.set('v.shipmentInfo', results.shipmentInfo);
                    component.set('v.invoiceInfo', results.invoice);
                    component.set('v.hasAmwareProducts', results.hasAmwareProducts);
                    component.set('v.hasAmwareShipments', results.hasAmwareShipments);
                    
                    if(results.order.orgCode === 'CCA'){
                        component.set('v.isCCA', true);
                    }else{
                        component.set('v.isCCA', false);
                    }
                    // order's currency
                    component.set('v.currencySymbol', results.order.currencyIsoCode);

                    /**
                     * Anony, added on 2023-08-31
                     * Change Sample Order determine logic location
                     */
                    if(results.order.orderType === 'Order' && results.order.orderSubType === 'CNA Sample Order Only') {
                        component.set('v.isSampleOrder', true);
                    }
                    /**
                     * Allen, Comment on 2021/06/02
                     * display reverse order links by order's status and type
                    */
                    if(((results.order.orderStatus === "Partial Shipment" || results.order.orderStatus === "Expédition partielle") || 
                    (results.order.orderStatus === "Ship Complete" || results.order.orderStatus === "Expédition complète") ||
                    (results.order.orderStatus === "Draft" || results.order.orderStatus === "Brouillon")) && 
                    (results.order.orderType === 'Order' || results.order.orderType === "Commande")){
                        if(results.order.orderType !=='Place_Parts_Order' && results.order.customerRecordType ==='Channel' && results.order.orderSubType !== 'CNA Return Order - Warranty' && results.order.orderSubType !== 'CNA Sample Order Only'){
                            component.set('v.isCanReverse', true);
                        }
                        if(results.isInnerUser){
                            component.set('v.reverseOrderLink', '/lightning/n/Reverse_Order_Request_Creation?c__order='+recordId);
                        }else{
                            component.set('v.reverseOrderLink', '/s/reverse-order-request?c__order='+recordId);
                        }
                        if(results.orderItems && Array.isArray(results.orderItems)){
                            results.orderItems.forEach(item => {
                                if(results.isInnerUser){
                                    item.reverseOrderLink = `/lightning/n/Reverse_Order_Request_Creation?c__order=${recordId}&c__orderitem=${item.id}`;
                                }else{
                                    item.reverseOrderLink = `/s/reverse-order-request?c__order=${recordId}&c__orderitem=${item.id}`;
                                }
                            })
                        }
                    }
                    component.set('v.dataInitComplete', true);
                }
            } else {
                var errors = response.getError();
                console.log('errors:',errors);
                component.set('v.dataInitComplete', true);
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    doBack: function(component) {
        let url = window.location.origin + $A.get("$Label.c.CCM_Back_To_Order_List_Relative_Link");
        window.open(url, "_self");
    },
    doView: function(component, event, helper){
        var recordId = event.getSource().get('v.value');
        console.log('Invoice recordId--->'+recordId);
        var url = '/apex/Invoice?invoiceID=' + recordId;
        window.open(url);
    },
    doDownload: function(component, event, helper){
        var recordId = event.getSource().get('v.value');
        var url = '/apex/Invoice?invoiceID=' + recordId;
        window.open(url);
    },
    showAddAmwareShipment: function(component, event, helper){
        component.set('v.isShow',true);
    },
    hiddenAddAmwareShipment: function(component, event, helper){
        component.set('v.isShow',false);
    }
})