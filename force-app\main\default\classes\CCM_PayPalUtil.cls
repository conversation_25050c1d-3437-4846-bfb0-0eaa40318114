public with sharing class CCM_PayPalUtil {
    public static AuthorizeResponse authorizePayPalPayment(String CardNum, String ExpDate, String cvv2, Decimal Amount, String OrderCode, BillingAddress billToAddress) {
        String mdtName = 'PayPal_Sandbox';
        String orgId = UserInfo.getOrganizationId();
        Organization org = [SELECT Id, IsSandbox FROM Organization WHERE Id = :orgId];
        if (!org.IsSandbox) {
            mdtName = 'PayPal_Production';
        }
        PayPal_Configuration__mdt config = [SELECT End_Point__c, PayPal_User__c, PayPal_Vender__c,User_credentia__c,Partner__c
                                           FROM PayPal_Configuration__mdt
                                           WHERE DeveloperName = :mdtName LIMIT 1];

        Map<String, String> paramMap = new Map<String, String>();
        paramMap.put('TRXTYPE','A');
        paramMap.put('TENDER','C');
        paramMap.put('USER',config.PayPal_User__c);
        paramMap.put('VENDOR',config.PayPal_Vender__c);
        paramMap.put('PWD',config.User_credentia__c);
        paramMap.put('PARTNER',config.Partner__c);
        paramMap.put('ACCT', CardNum);
        paramMap.put('EXPDATE', ExpDate);
        if (cvv2 != '' && cvv2 != null) {
            paramMap.put('CVV2', cvv2);
        }
        paramMap.put('AMT', String.valueOf(Amount));
        paramMap.put('COMMENT1',OrderCode);
        paramMap.put('BILLTOFIRSTNAME', billToAddress.BillToFirstName);
        paramMap.put('BILLTOLASTNAME', billToAddress.BillToLastName);
        paramMap.put('BILLTOSTREET', billToAddress.BillToStreet);
        paramMap.put('BILLTOCITY', billToAddress.BillToCity);
        paramMap.put('BILLTOSTATE', billToAddress.BillToState);
        if (billToAddress.BillToCountry != '' && billToAddress.BillToCountry != null) {
            paramMap.put('BILLTOCOUNTRY',billToAddress.BillToCountry);
        }
        paramMap.put('BILLTOZIP', billToAddress.BillToZIP);

        List<String> params = new List<String>();
        for (String key : paramMap.keySet()) {
            params.add(key+'='+paramMap.get(key));
        }

        String body = String.join(params, '&');
        //System.debug(LoggingLevel.INFO, '*** body: ' + body);
        system.debug('Sample Request : TRXTYPE=A&TENDER=C&USER=PaypalUserName&VENDOR=PaypalVendor&PWD=Testxxxx&PARTNER=PayPal&ACCT=411111111111xxxx&EXPDATE=xx/xx&CVV2=xxx&AMT=50&BILLTOFIRSTNAME=debi&BILLTOLASTNAME=behera&BILLTOSTREET=702st&BILLTOCITY=Carmel&BILLTOSTATE=Indiana&BILLTOCOUNTRY=US&BILLTOZIP=46032');
        try {
            HttpRequest req = new HttpRequest();
            req.setEndpoint(config.End_Point__c);
            req.setMethod('POST');
            req.setBody(body);
            HttpResponse response = new Http().send(req);
            System.debug(LoggingLevel.INFO, '*** response.getBody(): ' + response.getBody());
            String respBody = response.getBody();
            Map<String, String> valMap = new Map<String, String>();
            for (String valPair : respBody.split('&')) {
                List<String> pairs = valPair.split('=');
                if (pairs.size() > 1) {
                    valMap.put(pairs[0], pairs[1]);
                }
            }

            //check result 
            if (valMap.containsKey('RESULT') && valMap.get('RESULT') == '0') {
                AuthorizeResponse resp = new AuthorizeResponse();
                resp.resultCode = '0';
                resp.respMsg = valMap.containsKey('RESPMSG') ? valMap.get('RESPMSG') : '';
                resp.PNREF = valMap.containsKey('PNREF') ? valMap.get('PNREF') : '';
                return resp;
            } else {
                AuthorizeResponse resp = new AuthorizeResponse();
                resp.resultCode = valMap.containsKey('RESULT') ? valMap.get('RESULT') : '';
                resp.respMsg = valMap.containsKey('RESPMSG') ? valMap.get('RESPMSG') : '';
                resp.PNREF = '';
                return resp;
            }
        } catch (Exception e) {
            AuthorizeResponse resp = new AuthorizeResponse();
            resp.resultCode = '9999';
            resp.respMsg = e.getMessage();
            resp.PNREF = '';
            return resp;
        }
    }

    public static CaptureResponse capturePayPalPayment(Decimal amount, String PNREF, String orderNumber) {
        String mdtName = 'PayPal_Sandbox';
        String orgId = UserInfo.getOrganizationId();
        Organization org = [SELECT Id, IsSandbox FROM Organization WHERE Id = :orgId];
        if (!org.IsSandbox) {
            mdtName = 'PayPal_Production';
        }
        PayPal_Configuration__mdt config = [SELECT End_Point__c, PayPal_User__c, PayPal_Vender__c,User_credentia__c,Partner__c
                                           FROM PayPal_Configuration__mdt
                                           WHERE DeveloperName = :mdtName LIMIT 1];

        Map<String, String> paramMap = new Map<String, String>();
        paramMap.put('TRXTYPE','D');
        paramMap.put('TENDER','C');
        paramMap.put('USER',config.PayPal_User__c);
        paramMap.put('VENDOR',config.PayPal_Vender__c);
        paramMap.put('PWD',config.User_credentia__c);
        paramMap.put('PARTNER',config.Partner__c);
        paramMap.put('AMT', String.valueOf(amount));
        paramMap.put('ORIGID',PNREF);
        paramMap.put('CAPTURECOMPLETE','Y');
        paramMap.put('VERBOSITY','HIGH');

        List<String> params = new List<String>();
        for (String key : paramMap.keySet()) {
            params.add(key+'='+paramMap.get(key));
        }
        String body = String.join(params, '&');
        System.debug(LoggingLevel.INFO, '*** body: ' + body);

        try {
            HttpRequest req = new HttpRequest();
            req.setEndpoint(config.End_Point__c);
            req.setMethod('POST');
            req.setBody(body);
            HttpResponse response = new Http().send(req);
            System.debug(LoggingLevel.INFO, '*** response.getBody(): ' + response.getBody());
            String respBody = response.getBody();
            Map<String, String> valMap = new Map<String, String>();
            for (String valPair : respBody.split('&')) {
                List<String> pairs = valPair.split('=');
                if (pairs.size() > 1) {
                    valMap.put(pairs[0], pairs[1]);
                }
            }

            //check result 
            if (valMap.containsKey('RESULT') && valMap.get('RESULT') == '0') {
                CaptureResponse resp = new CaptureResponse();
                resp.resultCode = '0';
                resp.respMsg = valMap.containsKey('RESPMSG') ? valMap.get('RESPMSG') : '';
                resp.PNREF = valMap.containsKey('PNREF') ? valMap.get('PNREF') : '';

                system.enqueueJob(new CCM_callPushPayaplToEBSQueue(resp.PNREF,orderNumber,String.valueOf(amount)));

                return resp;
            } else {
                CaptureResponse resp = new CaptureResponse();
                resp.resultCode = valMap.containsKey('RESULT') ? valMap.get('RESULT') : '';
                resp.respMsg = valMap.containsKey('RESPMSG') ? valMap.get('RESPMSG') : '';
                resp.PNREF = '';
                return resp;
            }
        } catch (Exception e) {
            CaptureResponse resp = new CaptureResponse();
            resp.resultCode = '9999';
            resp.respMsg = e.getMessage();
            resp.PNREF = '';
            return resp;
        }
    }

    public static string syncInvoiceInfoToERP(InvoiceWrap invoiceInfo) {
        //callout interface to ERP or seeburger
        return '';
    }

    public Class CaptureResponse {
        public String resultCode;
        public String respMsg;
        public String PNREF;
    }

    public Class AuthorizeResponse {
        public String resultCode;
        public String respMsg;
        public String PNREF;
    }

    public Class BillingAddress {
        public String BillToFirstName;
        public String BillToLastName;
        public String BillToStreet;
        public String BillToCity;
        public String BillToState;
        public String BillToCountry;
        public String BillToZIP;

        public BillingAddress(String fn, String ln, String street, String city, String state, String country, String zipcode) {
            BillToFirstName = fn;
            BillToLastName = ln;
            BillToStreet = street;
            BillToCity = city;
            BillToState = state;
            BillToCountry = country;
            BillToZIP = zipcode;
        }
    }

    public Class InvoiceWrap {
        public String CustomerId;
        public String OrderId;
        public Decimal Amount;
        public String CurrencyCode;
        public Date IssueDate;
        public String PayFlowTransactionId;
        public BillingAddress BillTo;
    }
}