public without sharing class CCM_CalculateProfileReturnShareHandler extends CCM_Core implements Triggers.Handler{
    public void handle() {
        Set<Id> OrderIdList = new Set<Id>();
        Set<Id> setCustomerId = new Set<Id>();
        List<Customer_Profile__c> lstValidCustomerProfile = new List<Customer_Profile__c>();
        Map<Id, Decimal> mapCustomerSumOrderAmount = new Map<Id, Decimal>();
        Map<Id, Decimal> mapWarrantyReturnClaimSumOrderAmount = new Map<Id, Decimal>();
        if (Trigger.isAfter) {
            if(Trigger.isDelete){
                for (Order_Item__c oi : (List<Order_Item__c>)Trigger.old) {
                    if (String.isNotEmpty(oi.Order__c)) {
                        OrderIdList.add(oi.Order__c);
                    }
                }
                if(OrderIdList.size() > 0 ){
                    setCustomerId = getCustomerId(OrderIdList);
                    lstValidCustomerProfile = filterValidCustomerProfile(setCustomerId);
                    mapCustomerSumOrderAmount = getCustomerOrderAmount(setCustomerId);
                    mapWarrantyReturnClaimSumOrderAmount = getWarrantyReturnClaimAmount(setCustomerId);
                    calculateWarrantyReturnShare(lstValidCustomerProfile,mapCustomerSumOrderAmount,mapWarrantyReturnClaimSumOrderAmount);
                }
            }else {
                for (Order_Item__c oi : (List<Order_Item__c>)Trigger.new) {
                    if (String.isNotEmpty(oi.Order__c)) {
                        OrderIdList.add(oi.Order__c);
                    }
                }
                if(OrderIdList.size() > 0 ){
                    setCustomerId = getCustomerId(OrderIdList);
                    lstValidCustomerProfile = filterValidCustomerProfile(setCustomerId);
                    mapCustomerSumOrderAmount = getCustomerOrderAmount(setCustomerId);
                    mapWarrantyReturnClaimSumOrderAmount = getWarrantyReturnClaimAmount(setCustomerId);
                    calculateWarrantyReturnShare(lstValidCustomerProfile,mapCustomerSumOrderAmount,mapWarrantyReturnClaimSumOrderAmount);
                }
            }
        }
    }


    private void calculateWarrantyReturnShare(
        List<Customer_Profile__c> lstValidCustomerProfile,
        Map<Id,Decimal> mapCustomerSumOrderAmount,
        Map<Id,Decimal> mapWarrantyReturnClaimSumOrderAmount
    ) {

        List<Customer_Profile__c> customerProfileUpdateList = new List<Customer_Profile__c>();
        for (Customer_Profile__c objCP : lstValidCustomerProfile) {
            if(mapCustomerSumOrderAmount.size() > 0 && mapCustomerSumOrderAmount.get(objCP.Customer__c) != null){
                Decimal OrderAmount = mapCustomerSumOrderAmount.get(objCP.Customer__c);
                Decimal ClaimOrderAmount = 0;
                if(mapWarrantyReturnClaimSumOrderAmount.containsKey(objCP.Customer__c) ){
                    ClaimOrderAmount = mapWarrantyReturnClaimSumOrderAmount.get(objCP.Customer__c);
                }
                if(OrderAmount != 0){
                    objCP.Warranty_Return_Share__c = ((ClaimOrderAmount / OrderAmount) * 100).setScale(2);
                }
                customerProfileUpdateList.add(objCP);
            }
        }
        if(customerProfileUpdateList.size() > 0){
            update customerProfileUpdateList;
        }

    }


    private Map<Id,Decimal> getWarrantyReturnClaimAmount(Set<Id> setCustomerId) {
        Id idCustomer;
        String strKey;
        Map<Id,Decimal> mapWarrantyReturnClaimSumOrderAmount = new Map<Id,Decimal>();
        if (setCustomerId.size() == 0) {
            return null;
        }
        for (Warranty_Return_Claim_Item__c objAR : [
            SELECT Id,Order_Item__c,Order_Item__r.Invoice_Year__c,Order_Item__r.Price__c,Order_Item__r.Order_Quantity__c,Warranty_Return_Claim__r.Customer__c,Subtotal__c
            FROM Warranty_Return_Claim_Item__c
            WHERE Warranty_Return_Claim__r.Customer__c IN :setCustomerId
        ]) {
            idCustomer = objAR.Warranty_Return_Claim__r.Customer__c;
            if (!mapWarrantyReturnClaimSumOrderAmount.containsKey(idCustomer)) {
                mapWarrantyReturnClaimSumOrderAmount.put(idCustomer, objAR.Subtotal__c);
            } else{
                mapWarrantyReturnClaimSumOrderAmount.put(idCustomer, mapWarrantyReturnClaimSumOrderAmount.get(idCustomer)+objAR.Subtotal__c);
            }
        }
        System.debug(LoggingLevel.INFO, 'mapWarrantyReturnClaimSumOrderAmount:' + mapWarrantyReturnClaimSumOrderAmount);
        return mapWarrantyReturnClaimSumOrderAmount;
    }

    private Map<Id,Decimal> getCustomerOrderAmount(Set<Id> setCustomerId){
        Id idCustomer;
        Map<Id,Decimal> mapCustomerSumOrderAmount = new Map<Id,Decimal>();
        for (Order_Item__c objOd : [Select Order__c,Order__r.Date_Order__c ,Price__c ,Order__r.AccountId,Order_Quantity__c
                                      FROM Order_Item__c
                                      WHERE Order__r.AccountId IN :setCustomerId
                                      AND Price__c >0
                                      AND Order_Quantity__c > 0
                                      AND Line_Status__c !='CANCELLED'
                                     ]) {
            idCustomer = objOd.Order__r.AccountId ;
            if (!mapCustomerSumOrderAmount.containsKey(idCustomer)) {
                mapCustomerSumOrderAmount.put(idCustomer, objOd.Price__c*objOd.Order_Quantity__c);
            } else{
                mapCustomerSumOrderAmount.put(idCustomer, mapCustomerSumOrderAmount.get(idCustomer) + objOd.Price__c*objOd.Order_Quantity__c);
            }
        }
        System.debug(LoggingLevel.INFO, 'mapCustomerSumOrderAmount:' + mapCustomerSumOrderAmount);
        return mapCustomerSumOrderAmount;
    }

    private List<Customer_Profile__c> filterValidCustomerProfile(Set<Id> setCustomerId) {
        List<Customer_Profile__c> lstValidCustomerProfile = new List<Customer_Profile__c>();

        for (Customer_Profile__c objCP : [SELECT Id,Effective_Year__c,Customer__c,Warranty_Return_Share__c,Warranty_Return_Share_YTD__c FROM Customer_Profile__c WHERE Customer__c IN :setCustomerId]) {
            lstValidCustomerProfile.add(objCP);
        }
        return lstValidCustomerProfile;
    }

    private Set<Id> getCustomerId(Set<Id> OrderIdList) {
        Set<Id> customerIdSet = new Set<Id>();
        for(Order od : [SELECT Id, AccountId FROM Order WHERE Id IN : OrderIdList]){
            customerIdSet.add(od.AccountId);
        }
        return customerIdSet;
    }
}