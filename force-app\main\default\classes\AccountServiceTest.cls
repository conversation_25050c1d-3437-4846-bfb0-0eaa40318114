/**
 * @Author:          <PERSON>
 * @Date:            2018-02-23
 * @Description:
 * @Test_Class:      AccountService
 * @Related_Class:
 * @Last_Modified_by:
 * @Last_Modified_time:
 * @Modifiy_Purpose: 
 */
@isTest
private class AccountServiceTest {
	/**
	 * @Author:     Zack
	 * @Date:       2018-02-23
	 * @Return:     
	 * @Function:   测试AccountService的所有方法
	 * @Last_Modified_by:
 	 * @Last_Modified_time:
 	 * @Modifiy_Purpose: 
	 */
    static testMethod void testMethods() {
    	Account acc = TestDataFactory.createAccount();
        insert acc;

        AccountService.getAccountByRTDeveloperName('EGO_Customer');
        AccountService.getRecordType('0121A0000007qh1QAA');
        AccountService.getRecordType('');
        AccountService.getAccountByID(acc.Id);
        AccountService.getAccountByID('');
        AccountService.getAccountSimpleByID(acc.Id);
		System.debug('Test');
    }
}
