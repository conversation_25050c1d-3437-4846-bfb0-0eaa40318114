({
    doInit : function(component, event, helper) {
        component.set('v.isBusy', true);
        component.set('v.hasDropShipAddress', component.get('v.hasDropShipAddress'));
        var customerFilterCondition = '[{"FieldName":"RecordType.DeveloperName","Condtion":"=","Value":"Channel"},{"FieldName":"Distributor_or_Dealer__c","Condtion":"!=","Value":""}]';
        component.set("v.customerFilterCondition", customerFilterCondition);

        if(component.get('v.recordId')){
            component.set('v.disableFlag', true);
        }

        //According to current user role, refresh customer Org filter.
        var action = component.get("c.getCustomerOrgFilter");
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                if(results){
                    component.set("v.customerFilterCondition", results);
                }
            } else {
                component.set('v.isBusy', false);
                var errors = response.getError();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);

        component.set('v.isBusy', false);
    },
    nextStep: function(component, event, helper){
        component.set('v.isBusy', true);
        var result = Validator.pass(component.find("required-Field"));
        if(!result){
            component.set('v.isBusy', false);
            return;
        }
        var currentStep = component.get("v.currentStep");
        component.set("v.currentStep", currentStep + 1);
    },
    cancel: function(component){
        var url = window.location.origin + '/lightning/n/Order_Apply_List';
        window.open(url,'_self');
    },
    handleSelectCustomer: function(component, helper) {
        let customerId = component.get("v.customerId");
        let action = component.get('c.getCustomerInfo');
        action.setParams({
            'customerId': customerId
        });
        action.setCallback(this, function(response){
            let state = response.getState();
                if (state === "SUCCESS"){
                    let result = JSON.parse(response.getReturnValue());
                    if(!result) {
                        var toastEvent = $A.get("e.force:showToast");
                        toastEvent.setParams({
                            "title": "Warning!",
                            "message": "You are currently not assigned to have access to the selected Customer, please contact your Chervon Representative or System Administrator for further assistance.",
                            "type": "Warning"
                        });
                        toastEvent.fire();
                        component.set("v.validated", true);
                    }
                    else {
                        component.set("v.validated", false);
                    }
                }
        });
        $A.enqueueAction(action);
    }
})