/**********************************************************************
 *
 *
 * @url: /services/apexrest/CCM_RestService_DealInvoice
 * @data:
 *  {
    "Invoice_OracleID":"1319085",
    "Chervon_Name":"Chervon North America",
    "Chervon_Address1": "1203 East Warrenville Road,",
    "Chervon_Address2": "Naperville,IL,60563,US",
    "Invoice_Date": "2019-8-1",
    "Invoice_Number": "519181",
    "Customer": "0007",
    "BillTo": "12902",
    "BillTo_Customer":"THD USA # 0007",
    "BillTo_Address1":"D28.COM",
    "BillTo_Address2":"2455 Paces Ferry Road",
    "BillTo_Address3":"Atlanta, GA 30339 United States",
    "ShipTo": "18348",
    "ShipTo_Customer":"THD USA # 0007",
    "ShipTo_Address1":"Commerce Hub(HD.COM) - 8119",
    "ShipTo_Address2":"",
    "ShipTo_Address3":"AK United States",
    "DropShip_CustomerNumber":"19404",
    "DropShip_Name": "Vinson, Brandon",
    "DropShip_Address1": "5256 Heathrow Ave",
    "DropShip_Address2": "",
    "DropShip_Address3": "Kalamazoo,MI,49009,US",
    "Tracking_NO": "1ZX071280301034466",
    "Number_of_Shipping_Units": "2",
    "Delivery_Number": "1572551",
    "Freight_Term": "Collect",
    "Origin": "CHI",
    "Terr_Number": "AK",
    "Carrier": "UPSN -UPS GROUND",
    "PO_Number": "46829226",
    "Order_Date": "2019-7-31",
    "Order_Commande": "1504949",
    "Credit_Authorization": "",
    "Freight_and_other_charges": "0",
    "Total_Due": "393.64",
    "CurrencyCode": "USD",
    "Terms": "0.45% IBX, 1% 60 days payment dis, net 61 days",
    "Payment_Term1":"",
    "Payment_Term2":"",
    "Remit_To_Line1": "Chervon North America, Inc.",
    "Remit_To_Line2": "PO Box 71903",
    "Remit_To_Line3": "Chicago,IL 60694-1903",
    "Remit_To_Line4": "",
    "Remit_To_Line5": "INQUIRIES PLEASE FAX 1-844-713-7456 OR CALL: 1-888-810-1816",
    "Invoice_Type":"111",
    "HANDLING_AMT":"0",
    "InvoiceLine": [
        {
            "Invoice_Item_OracleID": "11111111",
            "Qty_Extended": "48",
            "Catalog_Item": "80111",
            "Customer_Item": "956449",
            "PO_Line": "9",
            "Description": "80111 Worm Drive Lubricant",
            "Price": "3",
            "Amount": "144"
        }
    ]
}

 * Test Class: CCM_RestService_DealInvoiceTest
*************************************************************************/
@RestResource(urlMapping='/CCM_RestService_DealInvoice')
global with sharing class CCM_RestService_DealInvoice {

    // @HttpPost
    // global static ResultObj doPost() {
    //     RestRequest req = RestContext.request;
    //     ReqestObj reqObj;
    //     ResultObj resObj = new ResultObj();
    //     try{
    //         Boolean isACE = false;
    //         Invoice__c invoiceInsertOrUpdate = null;
    //         System.debug(LoggingLevel.INFO, '*** req.requestBody.toString(): ' + req.requestBody.toString());
    //         reqObj = parse(req.requestBody.toString());
    //         System.debug(LoggingLevel.INFO, '*** req.requestBody(): ' + reqObj);
    //         Boolean isAutoInvoice = reqObj.Invoice_Source == 'CNA_Auto_Invoice' || reqObj.Invoice_Source == 'CA_Auto_Invoice';
    //         String InvoiceOracleID = reqObj.Invoice_OracleID;
    //         String InvoiceNumberUnique = reqObj.Invoice_Number;
    //         List<Invoice__c> InvoiceList = new List<Invoice__c>();
    //         if(String.isNotBlank(InvoiceOracleID)){
    //             InvoiceList = [select id,Reverse_Order_Request__c from Invoice__c where Invoice_OracleID__c = :InvoiceOracleID];
    //         }
    //         String CustomerNumber = reqObj.Customer;
    //         String orgCode = reqObj.ATTRIBUTE1;
    //         List<Account> acclist = new List<Account>();
    //         if(String.isNotBlank(CustomerNumber)){
    //             if(orgCode == 'CCA'){
    //                 acclist = [select id, AccountNumber from Account where AccountNumber = :CustomerNumber and ORG_Code__c = 'CCA'];
    //             }else{
    //                 acclist = [select id, AccountNumber from Account where AccountNumber = :CustomerNumber and ORG_Code__c != 'CCA'];
    //             }
    //         }
    //         if(acclist.size() == 0 && isAutoInvoice){
    //             String errorMsg = Label.CCM_ErrorMsg_NotFindCustomer;
    //             throw new InvoiceException(errorMsg.replace('{0}', 'Invoice').replace('{1}', reqObj.Invoice_OracleID).replace('{2}', reqObj.Customer));
    //         }

    //         Set<String> aceCustomersSet = new Set<String>();
    //         aceCustomersSet.addAll(Label.CCM_WarrantyReturn_ACECustomers.split(';'));
    //         if(aceCustomersSet.contains(CustomerNumber)) {
    //             isACE = true;
    //         }

    //         String BillToId = reqObj.BILL_TO_SITE_USE_ID;
    //         List<Address_With_Program__c> billAPlist = new List<Address_With_Program__c>();
    //         if(String.isNotBlank(BillToId)){
    //             billAPlist = [select id from Address_With_Program__c where Customer_Line_Oracle_ID__c = :BillToId];
    //         }
    //         String ShipToId = reqObj.SHIP_TO_SITE_USE_ID;
    //         List<Address_With_Program__c> shipAPlist = new List<Address_With_Program__c>();
    //         if(String.isNotBlank(ShipToId)){
    //             shipAPlist = [select id from Address_With_Program__c where Customer_Line_Oracle_ID__c = :ShipToId];
    //         }
    //         String shipmentId = reqObj.Delivery_Number;
    //         List<Shipment__c> shipmentList = new List<Shipment__c>();
    //         if(String.isNotBlank(shipmentId)){
    //             shipmentList = [select id from Shipment__c where Ship_OracleID__c = :shipmentId];
    //         }
    //         String orderNumber = reqObj.Order_Commande;
    //         String orderOracleId = reqObj.ATTRIBUTE3;
    //         List<Order> orderList = new List<Order>();
    //         List<Order_Item__c> orderItemList = new List<Order_Item__c>();
    //         Map<String, Order_Item__c> orderItemMap = new Map<String, Order_Item__c>();
    //         List<Order_Item__c> updateOrderItemList = new List<Order_Item__c>();
    //         if(String.isNotBlank(orderOracleId)){
    //             orderList = [select id, Reverse_Order_Request__c, Reverse_Order_Request__r.Reverse_Order_Request_Number__c from Order where Order_OracleID__c = :orderOracleId];
    //             orderItemList = [SELECT id,OrderLine_OracleID__c FROM Order_Item__c WHERE Order__r.Order_OracleID__c = :orderOracleId];
    //         }
    //         if(orderItemList.size() > 0){
    //             for(Order_Item__c oItem : orderItemList){
    //                 if(String.isNotBlank(oItem.OrderLine_OracleID__c)){
    //                     orderItemMap.put(oItem.OrderLine_OracleID__c, oItem);
    //                 }
    //             }
    //         }
    //         Map<String,Id> proMap = new Map<String,Id>();
    //         List<String> proNumberlist = new List<String>();
    //         List<String> InvoiceLineIdlist = new List<String>();
    //         if(reqObj.InvoiceLine != null && reqObj.InvoiceLine.size() > 0){
    //             for(InvoiceLine il : reqObj.InvoiceLine){
    //                 if(!proNumberlist.contains(il.Catalog_Item)){
    //                     proNumberlist.add(il.Catalog_Item);
    //                 }
    //                 if(!InvoiceLineIdlist.contains(il.Invoice_Item_OracleID)){
    //                     InvoiceLineIdlist.add(il.Invoice_Item_OracleID);
    //                 }
    //             }
    //         }
    //         List<Product2> prolist = [select id,ProductCode from Product2 where ProductCode in :proNumberlist AND IsCreatedByCode__c = FALSE AND Is_History_Product__c = :CCM_Constants.blHistoryProduct];
    //         for(Product2 p : prolist){
    //             proMap.put(p.ProductCode, p.Id);
    //         }

    //         List<Warranty_Return_Claim__c> warrantyReturn = [SELECT Id, (SELECT Id, Model__r.ProductCode, Subtotal__c, Credit_Memo_Amount__c FROM Warranty_Return_Claim_Item__r)
    //                                                          FROM Warranty_Return_Claim__c
    //                                                          WHERE Name = :reqObj.PO_Number];
    //         if(InvoiceList.size() > 0){
    //             Invoice__c i = InvoiceList.get(0);
    //             //i.Invoice_OracleID__c = InvoiceOracleID;
    //             i.Chervon_Name__c = reqObj.Chervon_Name;
    //             i.Chervon_Address1__c = reqObj.Chervon_Address1;
    //             i.Chervon_Address2__c = reqObj.Chervon_Address2;
    //             i.Invoice_Date__c = Date.valueOf(reqObj.Invoice_Date);
    //             i.Payment_Terms_Record__c = reqObj.ATTRIBUTE4;
    //             i.Invoice_Number__c = reqObj.Invoice_Number;
    //             i.Customer_Text__c = CustomerNumber;
    //             if(acclist.size() > 0){
    //                 i.Customer__c = acclist.get(0).id;
    //             }
    //             i.BillTo_Text__c = reqObj.BillTo;
    //             i.BILL_TO_SITE_USE_ID__c = BillToId;
    //             if(billAPlist.size() > 0){
    //                 i.BillTo__c = billAPlist.get(0).id;
    //             }
    //             i.BillTo_Customer__c        = reqObj.BillTo_Customer;
    //             i.BillTo_Address1__c        = reqObj.BillTo_Address1;
    //             i.BillTo_Address2__c        = reqObj.BillTo_Address2;
    //             i.BillTo_Address3__c        = reqObj.BillTo_Address3;
    //             i.ShipTo_Text__c            = reqObj.ShipTo;
    //             i.SHIP_TO_SITE_USE_ID__c    = ShipToId;
    //             if(shipAPlist.size() > 0){
    //                 i.ShipTo__c = shipAPlist.get(0).id;
    //             }
    //             i.ShipTo_Customer__c        = reqObj.ShipTo_Customer;
    //             i.ShipTo_Address1__c        = reqObj.ShipTo_Address1;
    //             i.ShipTo_Address2__c        = reqObj.ShipTo_Address2;
    //             i.ShipTo_Address3__c        = reqObj.ShipTo_Address3;

    //             i.DropShip_CustomerNumber__c = reqObj.DropShip_CustomerNumber;
    //             i.DropShip_Name__c          = reqObj.DropShip_Name;
    //             i.DropShip_Address1__c      = reqObj.DropShip_Address1;
    //             i.DropShip_Address2__c      = reqObj.DropShip_Address2;
    //             i.DropShip_Address3__c      = reqObj.DropShip_Address3;

    //             i.Tracking_NO__c            = reqObj.Tracking_NO;
    //             i.Number_of_Shipping_Units__c = reqObj.Number_of_Shipping_Units;
    //             i.Delivery_Number__c        = reqObj.Delivery_Number;
    //             if(shipmentList.size() > 0){
    //                 i.Shipment__c = shipmentList.get(0).Id;
    //             }
    //             i.Freight_Term__c   = reqObj.Freight_Term;
    //             i.Origin__c         = reqObj.Origin;
    //             i.Terr_Number__c    = reqObj.Terr_Number;
    //             i.Carrier__c        = reqObj.Carrier;
    //             i.PO_Number__c      = reqObj.PO_Number;

    //             // link invoice to reverse order request
    //             List<String> creditInvoiceNumberList = new List<String>();
    //             List<Reverse_Order_Item__c> reverseItems = new List<Reverse_Order_Item__c>();
    //             Reverse_Order_Request__c reverseRequest;
    //             List<Reverse_Order_Charge_Credit_Account__c> reverseOrderCreditAccounts = new List<Reverse_Order_Charge_Credit_Account__c>();
    //             Map<Id, Reverse_Order_Item__c> reverseItemMap = new Map<Id, Reverse_Order_Item__c>();
    //             if (String.isNotEmpty(reqObj.Invoice_Number) && reqObj.Invoice_Number.startsWith('ROClaim')) {
    //                 List<String> autoNumber = reqObj.Invoice_Number.split('-');
    //                 String requestAutoNumber = autoNumber[0] + '-' + autoNumber[1];
    //                 List<Reverse_Order_Request__c> reverseOrderRequests = [SELECT Id,
    //                                                                             Return_Goods_Status__c,
    //                                                                             Reverse_Order_Type__c,
    //                                                                             Credit_Invoice_Number__c,
    //                                                                             CreatedBy.Profile.Name,
    //                                                                             CreatedBy.Email,
    //                                                                             Customer__r.Owner.Email,
    //                                                                             (SELECT Id,
    //                                                                                     Next_Step_Action__c,
    //                                                                                     Qty__c,
    //                                                                                     Invoice_Price__c,
    //                                                                                     Claimed_Credit_Memo_Amount__c,
    //                                                                                     Credit_Memo_Issued__c,
    //                                                                                     Issued_Credit_Memo_Amount__c,
    //                                                                                     Product2__c,
    //                                                                                     Product2__r.ProductCode
    //                                                                                     FROM Reverse_Order_Items__r),
    //                                                                             (SELECT Id,
    //                                                                                     Reverse_Order_Request__c,
    //                                                                                     Reverse_Order_Item__r.Name,
    //                                                                                     Amount__c,
    //                                                                                     Brand_Code__c,
    //                                                                                     Cost_Center__c,
    //                                                                                     Description__c,
    //                                                                                     GL_Code__c
    //                                                                                     FROM Reverse_Order_Charge_Credit_Accounts__r)
    //                                                                             FROM Reverse_Order_Request__c
    //                                                                             WHERE Auto_Number__c = :requestAutoNumber];

    //                 if (reverseOrderRequests.size() > 0) {
    //                     i.Reverse_Order_Request__c = reverseOrderRequests[0].Id;
    //                     reverseRequest = reverseOrderRequests[0];
    //                     reverseItems = reverseOrderRequests[0].Reverse_Order_Items__r;
    //                     reverseItemMap.putAll(reverseItems);
    //                     reverseOrderCreditAccounts = reverseOrderRequests[0].Reverse_Order_Charge_Credit_Accounts__r;
    //                     Set<String> creditInvoiceNumberSet = new Set<String>();
    //                     if (String.isNotBlank(reverseRequest.Credit_Invoice_Number__c)) {
    //                         creditInvoiceNumberSet.addAll(reverseRequest.Credit_Invoice_Number__c.split(','));
    //                     }
    //                     creditInvoiceNumberSet.add(i.Invoice_Number__c);
    //                     creditInvoiceNumberList.addAll(creditInvoiceNumberSet);
    //                     reverseRequest.Credit_Invoice_Number__c = String.join(creditInvoiceNumberList, ',');
    //                 }
    //             }

    //             if(String.isNotBlank(reqObj.Order_Date)){
    //                 i.Order_Date__c = Date.valueOf(reqObj.Order_Date);
    //             }
    //             i.Order_Commande__c = reqObj.Order_Commande;
    //             i.Order_OracleID__c = reqObj.ATTRIBUTE3;
    //             if(orderList.size() > 0){
    //                 i.Order__c = orderList.get(0).Id;
    //                 if (String.isNotEmpty(orderList.get(0).Reverse_Order_Request__c) && orderList.get(0).Reverse_Order_Request__r.Reverse_Order_Request_Number__c == reqObj.PO_Number) {
    //                     List<Reverse_Order_Request__c> reverseOrderRequests = [SELECT Id,
    //                                                                             Reverse_Order_Type__c,
    //                                                                             Return_Goods_Status__c,
    //                                                                             Credit_Invoice_Number__c,
    //                                                                             CreatedBy.Profile.Name,
    //                                                                             CreatedBy.Email,
    //                                                                             Customer__r.Owner.Email,
    //                                                                             (SELECT Id,
    //                                                                                     Next_Step_Action__c,
    //                                                                                     Qty__c,
    //                                                                                     Invoice_Price__c,
    //                                                                                     Claimed_Credit_Memo_Amount__c,
    //                                                                                     Credit_Memo_Issued__c,
    //                                                                                     Issued_Credit_Memo_Amount__c,
    //                                                                                     Product2__c,
    //                                                                                     Product2__r.ProductCode
    //                                                                                     FROM Reverse_Order_Items__r)
    //                                                                             FROM Reverse_Order_Request__c
    //                                                                             WHERE Id = :orderList.get(0).Reverse_Order_Request__c];

    //                     if (reverseOrderRequests.size() > 0) {
    //                         i.Reverse_Order_Request__c = reverseOrderRequests[0].Id;
    //                         reverseRequest = reverseOrderRequests[0];
    //                         reverseItems = reverseOrderRequests[0].Reverse_Order_Items__r;
    //                         Set<String> creditInvoiceNumberSet = new Set<String>();
    //                         if (String.isNotBlank(reverseRequest.Credit_Invoice_Number__c)) {
    //                             creditInvoiceNumberSet.addAll(reverseRequest.Credit_Invoice_Number__c.split(','));
    //                         }
    //                         creditInvoiceNumberSet.add(i.Invoice_Number__c);
    //                         creditInvoiceNumberList.addAll(creditInvoiceNumberSet);
    //                         reverseRequest.Credit_Invoice_Number__c = String.join(creditInvoiceNumberList, ',');
    //                     }
    //                 }
    //             }
    //             i.Credit_Authorization__c = reqObj.Credit_Authorization;
    //             i.Freight_and_other_charges__c = Decimal.valueOf(reqObj.Freight_and_other_charges);
    //             i.Handling_Fee__c   = Decimal.valueOf(reqObj.HANDLING_AMT);
    //             i.Total_Due__c      = Decimal.valueOf(reqObj.Total_Due);
    //             i.CurrencyIsoCode   = reqObj.CurrencyCode;
    //             i.Terms__c          = reqObj.Terms;
    //             i.Payment_Term1__c = '';
    //             i.Payment_Term2__c = '';
    //             if(reqObj.Payment_Term1 != null){
    //                 for(String term : reqObj.Payment_Term1.split(';')){
    //                     i.Payment_Term1__c += '\n'+term;
    //                 }
    //             }
    //             if(reqObj.Payment_Term2 != null){
    //                 for(String term : reqObj.Payment_Term2.split(';')){
    //                     i.Payment_Term2__c += '\n'+term;
    //                 }
    //             }

    //             i.Remit_To_Line3__c = reqObj.Remit_To_Line3;
    //             i.Remit_To_Line4__c = reqObj.Remit_To_Line4;
    //             i.Remit_To_Line5__c = reqObj.Remit_To_Line5;
    //             i.Invoice_Type__c   = reqObj.Invoice_Type;
    //             i.Invoice_Source__c = reqObj.Invoice_Source;

    //             //CA tax
    //             if(String.isNotBlank(reqObj.GST_AMT)){
    //                 i.GST__c = Decimal.valueOf(reqObj.GST_AMT);
    //             }
    //             if(String.isNotBlank(reqObj.HST_AMT)){
    //                 i.HST__c = Decimal.valueOf(reqObj.HST_AMT);
    //             }
    //             if(String.isNotBlank(reqObj.QST_AMT)){
    //                 i.QST__c = Decimal.valueOf(reqObj.QST_AMT);
    //             }
    //             if(String.isNotBlank(reqObj.ATTRIBUTE5)) {
    //                 i.PST__c = Decimal.valueOf(reqObj.ATTRIBUTE5);
    //             }
    //             //Org Code
    //             i.ORG_Code__c = reqObj.ATTRIBUTE1;

    //             //Surcharge Amount
    //             if(String.isNotBlank(reqObj.ATTRIBUTE2)){
    //                 i.Surcharge_Amount__c = Decimal.valueOf(reqObj.ATTRIBUTE2);
    //             }

    //             if (warrantyReturn.size() > 0) {
    //                 i.Warranty_Return_Request__c = warrantyReturn[0].Id;
    //             }

    //             update i;
    //             invoiceInsertOrUpdate = i;

    //             List<Invoice_Item__c> iilist = [select id,Invoice_Item_OracleID__c
    //                                             from Invoice_Item__c
    //                                             where Invoice_Item_OracleID__c in :InvoiceLineIdlist
    //                                             And Invoice__c = :i.Id];
    //             List<Invoice_Item__c> iiInsertList = new List<Invoice_Item__c>();
    //             List<Invoice_Item__c> iiUpdateList = new List<Invoice_Item__c>();
    //             Map<String,Invoice_Item__c> iiMap  = new Map<String,Invoice_Item__c>();
    //             for(Invoice_Item__c ii : iilist){
    //                 iiMap.put(ii.Invoice_Item_OracleID__c, ii);
    //             }

    //             Decimal reverseCreditAmount = 0;
    //             Decimal reverseIssuedCreditAmount = 0;
    //             Boolean isReship = false;
    //             Boolean isReplacement = false;
    //             if(reqObj.InvoiceLine != null && reqObj.InvoiceLine.size() > 0){
    //                 Set<String> descriptionSet = new Set<String>();
    //                 for (InvoiceLine il : reqObj.InvoiceLine) {
    //                     descriptionSet.add(il.Description);
    //                 }
    //                 Set<String> servicePartnerSet = new Set<String>();
    //                 Map<String, List<Warranty_Claim__c>> warrantyClaimMap = new Map<String, List<Warranty_Claim__c>>();
    //                 for (Warranty_Claim__c claim : [SELECT Name, Invoice_Item__c, Payment_Status__c, Service_Partner__c
    //                                                     FROM Warranty_Claim__c
    //                                                     WHERE Name IN :descriptionSet]) {
    //                     if (!warrantyClaimMap.containsKey(claim.Name)) {
    //                         warrantyClaimMap.put(claim.Name, new List<Warranty_Claim__c>());
    //                     }
    //                     warrantyClaimMap.get(claim.name).add(claim);
    //                     servicePartnerSet.add(claim.Service_Partner__c);
    //                 }
    //                 Map<String, List<Account_Address__c>> shippingAddressMap = new Map<String, List<Account_Address__c>>();
    //                 if (servicePartnerSet.size() > 0) {
    //                     List<Account_Address__c> addressList = [SELECT Store_Number__c, Country__c, State__c, City__c,Address1__c, Postal_Code__c, X2nd_Tier_Dealer__c
    //                                                                 FROM Account_Address__c
    //                                                                 WHERE X2nd_Tier_Dealer__c IN :servicePartnerSet
    //                                                                 AND RecordType.DeveloperName IN ('Shipping_Address', 'Dropship_Shipping_Address')];
    //                     if (addressList.size() > 0) {
    //                         for (Account_Address__c address : addressList) {
    //                             if (!shippingAddressMap.containsKey(address.X2nd_Tier_Dealer__c)) {
    //                                 shippingAddressMap.put(address.X2nd_Tier_Dealer__c, new List<Account_Address__c>());
    //                             }
    //                             shippingAddressMap.get(address.X2nd_Tier_Dealer__c).add(address);
    //                         }
    //                     }
    //                 }

    //                 for(InvoiceLine il : reqObj.InvoiceLine){
    //                     if(iiMap.get(il.Invoice_Item_OracleID) != null){
    //                         Invoice_Item__c ii      = iiMap.get(il.Invoice_Item_OracleID);
    //                         ii.CurrencyIsoCode      = i.CurrencyIsoCode;
    //                         ii.Qty_Extended__c      = il.Qty_Extended;
    //                         ii.Catalog_Item_Text__c = il.Catalog_Item;
    //                         if(String.isBlank(il.Catalog_Item) && isAutoInvoice && (il.Line_Attribute1 == 'FG' || il.Line_Attribute1 == 'MKT')){
    //                             String errorMsg = Label.CCM_ErrorMsg_LackOfProductInfo;
    //                             throw new InvoiceException(errorMsg.replace('{0}', 'Invoice').replace('{1}', il.Invoice_Item_OracleID));
    //                         }
    //                         if(proMap.get(il.Catalog_Item) != null){
    //                             ii.Catalog_Item__c  = proMap.get(il.Catalog_Item);
    //                         }else if(isAutoInvoice && (il.Line_Attribute1 == 'FG' || il.Line_Attribute1 == 'MKT')){
    //                             String errorMsg = Label.CCM_ErrorMsg_NotFindProduct;
    //                             throw new InvoiceException(errorMsg.replace('{0}', 'Invoice').replace('{1}', il.Invoice_Item_OracleID).replace('{2}', il.Catalog_Item));
    //                         }
    //                         ii.Customer_Item__c     = il.Customer_Item;
    //                         ii.PO_Line__c           = il.PO_Line;
    //                         ii.Description__c       = il.Description;
    //                         ii.Price__c             = String.isEmpty(il.Price) ? 0 : Decimal.valueOf(il.Price);
    //                         ii.Amount__c            = String.isEmpty(il.Amount) ? 0 : Decimal.valueOf(il.Amount);
    //                         ii.Item_Type__c         = il.Line_Attribute1;
    //                         ii.Invoice__c           = i.Id;
    //                         ii.ORG_Code__c          = i.ORG_Code__c;
    //                         fillInInfoFromShippingAddress(ii, warrantyClaimMap, shippingAddressMap);
    //                         populateInvoiceDateOfOrderItem(i,ii,il,orderItemMap,updateOrderItemList);
    //                         if(String.isNotBlank(il.Line_Attribute3)){
    //                             ii.Surcharge_Amount__c = Decimal.valueOf(il.Line_Attribute3);
    //                         }
    //                         iiUpdateList.add(ii);
    //                     }else{
    //                         Invoice_Item__c ii          = new Invoice_Item__c();
    //                         ii.CurrencyIsoCode          = i.CurrencyIsoCode;
    //                         ii.Invoice_Item_OracleID__c = il.Invoice_Item_OracleID;
    //                         ii.Qty_Extended__c          = il.Qty_Extended;
    //                         ii.Catalog_Item_Text__c     = il.Catalog_Item;
    //                         if(String.isBlank(il.Catalog_Item) && isAutoInvoice && (il.Line_Attribute1 == 'FG' || il.Line_Attribute1 == 'MKT')){
    //                             String errorMsg = Label.CCM_ErrorMsg_LackOfProductInfo;
    //                             throw new InvoiceException(errorMsg.replace('{0}', 'Invoice').replace('{1}', il.Invoice_Item_OracleID));
    //                         }
    //                         if(proMap.get(il.Catalog_Item) != null){
    //                             ii.Catalog_Item__c      = proMap.get(il.Catalog_Item);
    //                         }else if(isAutoInvoice && (il.Line_Attribute1 == 'FG' || il.Line_Attribute1 == 'MKT')){
    //                             String errorMsg = Label.CCM_ErrorMsg_NotFindProduct;
    //                             throw new InvoiceException(errorMsg.replace('{0}', 'Invoice').replace('{1}', il.Invoice_Item_OracleID).replace('{2}', il.Catalog_Item));
    //                         }
    //                         ii.Customer_Item__c         = il.Customer_Item;
    //                         ii.PO_Line__c               = il.PO_Line;
    //                         ii.Description__c           = il.Description;
    //                         if(il.Price != null){
    //                             ii.Price__c             = Decimal.valueOf(il.Price);
    //                         }
    //                         ii.Amount__c = 0;
    //                         if(il.Amount != null){
    //                             ii.Amount__c            = Decimal.valueOf(il.Amount);
    //                         }
    //                         ii.Item_Type__c             = il.Line_Attribute1;
    //                         ii.Invoice__c               = i.Id;
    //                         fillInInfoFromShippingAddress(ii, warrantyClaimMap, shippingAddressMap);
    //                         populateInvoiceDateOfOrderItem(i,ii,il,orderItemMap,updateOrderItemList);
    //                         ii.ORG_Code__c = i.ORG_Code__c;
    //                         if(String.isNotBlank(il.Line_Attribute3)){
    //                             ii.Surcharge_Amount__c = Decimal.valueOf(il.Line_Attribute3);
    //                         }
    //                         iiInsertList.add(ii);
    //                     }
    //                 }
    //             }
    //             if(iiInsertList.size() > 0){
    //                 insert iiInsertList;
    //             }
    //             if(iiUpdateList.size() > 0){
    //                 update iiUpdateList;
    //             }
    //             if(updateOrderItemList.size() > 0){
    //                 update updateOrderItemList;
    //             }
    //             if (orderList.size() > 0 && reqObj.PO_Number.startsWith('RO') && String.isNotEmpty(orderList.get(0).Reverse_Order_Request__c)) {
    //                 List<Invoice_Item__c> tempItems = [SELECT Id, Amount__c, Catalog_Item__r.ProductCode FROM Invoice_Item__c WHERE Invoice__r.Invoice_Number__c IN :creditInvoiceNumberList];
    //                 for (Reverse_Order_Item__c item : reverseItems) {
    //                     item.Credit_Memo_Issued__c = 0;
    //                     for(Invoice_Item__c il : tempItems) {
    //                         if (item.Product2__r.ProductCode == il.Catalog_Item__r.ProductCode) {
    //                             item.Credit_Memo_Issued__c  += il.Amount__c.abs();
    //                             reverseIssuedCreditAmount   += il.Amount__c.abs();
    //                         }
    //                         if (item.Product2__r.ProductCode == il.Catalog_Item__r.ProductCode) {
    //                             if (item.Next_Step_Action__c == 'I want Chervon to reship the shortage product') {
    //                                 isReship = true;
    //                             } else if (item.Next_Step_Action__c == 'I want to return damaged goods and get replacement') {
    //                                 isReplacement = true;
    //                             }
    //                         }
    //                     }
    //                 }
    //             } else if (reqObj.Invoice_Number.startsWith('ROClaim')) {
    //                 for (Invoice_Item__c item : [SELECT Id, Amount__c FROM Invoice_Item__c WHERE Invoice__r.Invoice_Number__c IN :creditInvoiceNumberList]) {
    //                     reverseIssuedCreditAmount += item.Amount__c.abs();
    //                 }

    //                 for (Reverse_Order_Charge_Credit_Account__c ca : reverseOrderCreditAccounts) {
    //                     if (reverseItemMap.get(ca.Reverse_Order_Item__c).Next_Step_Action__c == 'I want Chervon to reship the shortage product') {
    //                         isReship = true;
    //                     } else if (reverseItemMap.get(ca.Reverse_Order_Item__c).Next_Step_Action__c == 'I want to return damaged goods and get replacement') {
    //                         isReplacement = true;
    //                     }
    //                 }
    //             }
    //             if ((orderList.size() > 0 && String.isNotEmpty(orderList.get(0).Reverse_Order_Request__c) && reqObj.PO_Number.startsWith('RO')) ||
    //                 (String.isNotEmpty(reqObj.Invoice_Number) && reqObj.Invoice_Number.startsWith('ROClaim'))) {
    //                 for (Reverse_Order_Item__c item : reverseItems) {
    //                     reverseCreditAmount += item.Invoice_Price__c * item.Qty__c;
    //                 }
    //                 if (reverseIssuedCreditAmount < reverseCreditAmount) {
    //                     reverseRequest.Credit_Memo_Status__c = 'Partial Issued';
    //                     if (reverseRequest.Return_Goods_Status__c == 'Received') {
    //                         reverseRequest.Credit_Memo_Status__c = 'Issued';
    //                         if(!isACE) {
    //                             reverseRequest.Reverse_Order_Request_Status__c = 'Completed';
    //                         }
    //                     }
    //                     update reverseRequest;
    //                 }
    //                 if (reverseIssuedCreditAmount == reverseCreditAmount) {
    //                     reverseRequest.Credit_Memo_Status__c = 'Issued';
    //                     if(!isACE) {
    //                         reverseRequest.Reverse_Order_Request_Status__c = 'Completed';
    //                     }
    //                     update reverseRequest;
    //                 }
    //                 if (reverseIssuedCreditAmount == 0) {
    //                     reverseRequest.Credit_Memo_Status__c = 'Not Issued';
    //                     update reverseRequest;
    //                 }
    //                 if (reqObj.PO_Number.startsWith('RO') && String.isNotEmpty(orderList.get(0).Reverse_Order_Request__c)) {
    //                     update reverseItems;
    //                 } else if (reqObj.Invoice_Number.startsWith('ROClaim')) {
    //                     update reverseItemMap.values();
    //                 }

    //                 if (reverseRequest.Reverse_Order_Type__c != 'Overage') {
    //                     if(!isACE) {
    //                     // send email to na finance
    //                         List<User> financeUsers = [SELECT Id, Email FROM User WHERE Profile.Name LIKE '%Finance%' AND IsActive = true];
    //                         Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
    //                         List<String> emailAddress = new List<String>();
    //                         for (User fin : financeUsers) {
    //                             emailAddress.add(fin.Email);
    //                         }
    //                         email.toaddresses = emailAddress;
    //                         email.subject = 'New Invoice';
    //                         String invoiceURL = URL.getSalesforceBaseUrl().toExternalForm() + '/' + i.Id;
    //                         String reverseURL = URL.getSalesforceBaseUrl().toExternalForm() + '/' + reverseRequest.Id;
    //                         String emailBody = 'Credit Memo for this reverse order request is issued. You can click the links to see more detail. <br/>' +
    //                                             'Reverse Order Request Link: <a href="' + reverseURL + '">' + reverseURL + '</a>' +
    //                                             'Credit Memo Invoice Link: <a href="' + invoiceURL + '">' + invoiceURL + '</a>';
    //                         email.htmlbody = emailBody;
    //                         Messaging.SingleEmailMessage[] emails = new Messaging.SingleEmailMessage[]{email};
    //                         Messaging.SendEmailResult[] results = Messaging.sendEmail(emails);
    //                     }
    //                 }

    //                 if (isReplacement || isReship) {
    //                     // send email to inside sales
    //                     Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
    //                     List<String> emailAddress = new List<String>();
    //                     if (reverseRequest.CreatedBy.Profile.Name.contains('Inside Sales')) {
    //                         emailAddress.add(reverseRequest.CreatedBy.Email);
    //                     } else {
    //                         emailAddress.add(reverseRequest.Customer__r.Owner.Email);
    //                     }
    //                     if (orgCode == CCM_Constants.ORG_CODE_CCA) {
    //                         emailAddress = new List<String>();
    //                         Set<String> caSalesProfiles = new Set<String>{'NA Sales Manager','Canada Sales Manager','BEAM'};
    //                         List<String> canadaRole = Label.Canada_User_Role.split(',');
    //                         for (User insideSales : [SELECT Id, Email FROM User WHERE Profile.Name IN :caSalesProfiles AND UserRole.DeveloperName IN :canadaRole AND IsActive = true]) {
    //                             emailAddress.add(insideSales.Email);
    //                         }
    //                     }
    //                     email.toaddresses = emailAddress;
    //                     email.subject = 'Return Order';
    //                     String invoiceURL = URL.getSalesforceBaseUrl().toExternalForm() + '/' + i.Id;
    //                     String reverseURL = URL.getSalesforceBaseUrl().toExternalForm() + '/' + reverseRequest.Id;
    //                     String emailBody = 'Return Order for this reverse order request is reshipped/replacement. You can place a new order with this return order. <br/>' +
    //                                         'Reverse Order Request Link: <a href="' + reverseURL + '">' + reverseURL + '</a>' +
    //                                         'Credit Memo Invoice Link: <a href="' + invoiceURL + '">' + invoiceURL + '</a>';
    //                     email.htmlbody = emailBody;
    //                     Messaging.SingleEmailMessage[] emails = new Messaging.SingleEmailMessage[]{email};
    //                     Messaging.SendEmailResult[] results = Messaging.sendEmail(emails);
    //                 }
    //             }
    //         }else{
    //             List<Invoice_Item__c> iilist = new List<Invoice_Item__c>();
    //             Invoice__c i = new Invoice__c();
    //             i.Invoice_OracleID__c = InvoiceOracleID;
    //             i.Chervon_Name__c = reqObj.Chervon_Name;
    //             i.Chervon_Address1__c = reqObj.Chervon_Address1;
    //             i.Chervon_Address2__c = reqObj.Chervon_Address2;
    //             i.Invoice_Date__c = Date.valueOf(reqObj.Invoice_Date);
    //             i.Payment_Terms_Record__c = reqObj.ATTRIBUTE4;
    //             i.Invoice_Number__c = reqObj.Invoice_Number;
    //             i.Customer_Text__c = CustomerNumber;
    //             if(acclist.size() > 0){
    //                 i.Customer__c = acclist.get(0).id;
    //             }
    //             i.BillTo_Text__c = BillToId;
    //             if(billAPlist.size() > 0){
    //                 i.BillTo__c = billAPlist.get(0).id;
    //             }
    //             i.BillTo_Customer__c = reqObj.BillTo_Customer;
    //             i.BillTo_Address1__c = reqObj.BillTo_Address1;
    //             i.BillTo_Address2__c = reqObj.BillTo_Address2;
    //             i.BillTo_Address3__c = reqObj.BillTo_Address3;
    //             i.ShipTo_Text__c     = ShipToId;
    //             if(shipAPlist.size() > 0){
    //                 i.ShipTo__c = shipAPlist.get(0).id;
    //             }
    //             i.ShipTo_Customer__c = reqObj.ShipTo_Customer;
    //             i.ShipTo_Address1__c = reqObj.ShipTo_Address1;
    //             i.ShipTo_Address2__c = reqObj.ShipTo_Address2;
    //             i.ShipTo_Address3__c = reqObj.ShipTo_Address3;

    //             i.DropShip_CustomerNumber__c = reqObj.DropShip_CustomerNumber;
    //             i.DropShip_Name__c      = reqObj.DropShip_Name;
    //             i.DropShip_Address1__c = reqObj.DropShip_Address1;
    //             i.DropShip_Address2__c = reqObj.DropShip_Address2;
    //             i.DropShip_Address3__c = reqObj.DropShip_Address3;

    //             i.Tracking_NO__c = reqObj.Tracking_NO;
    //             i.Number_of_Shipping_Units__c = reqObj.Number_of_Shipping_Units;
    //             i.Delivery_Number__c = reqObj.Delivery_Number;
    //             if(shipmentList.size() > 0){
    //                 i.Shipment__c = shipmentList.get(0).Id;
    //             }
    //             i.Freight_Term__c   = reqObj.Freight_Term;
    //             i.Origin__c         = reqObj.Origin;
    //             i.Terr_Number__c    = reqObj.Terr_Number;
    //             i.Carrier__c        = reqObj.Carrier;
    //             i.PO_Number__c      = reqObj.PO_Number;

    //             // add by roger 2021-07-15
    //             // link reverse order to reverse order request
    //             List<String> creditInvoiceNumberList = new List<String>();
    //             List<Reverse_Order_Item__c> reverseItems = new List<Reverse_Order_Item__c>();
    //             Reverse_Order_Request__c reverseRequest;
    //             List<Reverse_Order_Charge_Credit_Account__c> reverseOrderCreditAccounts = new List<Reverse_Order_Charge_Credit_Account__c>();
    //             Map<Id, Reverse_Order_Item__c> reverseItemMap = new Map<Id, Reverse_Order_Item__c>();
    //             if (String.isNotEmpty(reqObj.Invoice_Number) && reqObj.Invoice_Number.startsWith('ROClaim')) {
    //                 List<String> autoNumber = reqObj.Invoice_Number.split('-');
    //                 String requestAutoNumber = autoNumber[0] + '-' + autoNumber[1];
    //                 List<Reverse_Order_Request__c> reverseOrderRequests = [SELECT Id,
    //                                                                             Reverse_Order_Type__c,
    //                                                                             Return_Goods_Status__c,
    //                                                                             CreatedBy.Profile.Name,
    //                                                                             Credit_Invoice_Number__c,
    //                                                                             CreatedBy.Email,
    //                                                                             Customer__r.Owner.Email,
    //                                                                             (SELECT Id,
    //                                                                                     Next_Step_Action__c,
    //                                                                                     Qty__c,
    //                                                                                     Invoice_Price__c,
    //                                                                                     Claimed_Credit_Memo_Amount__c,
    //                                                                                     Credit_Memo_Issued__c,
    //                                                                                     Issued_Credit_Memo_Amount__c,
    //                                                                                     Product2__c,
    //                                                                                     Product2__r.ProductCode
    //                                                                                     FROM Reverse_Order_Items__r),
    //                                                                             (SELECT Id,
    //                                                                                     Reverse_Order_Request__c,
    //                                                                                     Reverse_Order_Item__r.Name,
    //                                                                                     Amount__c,
    //                                                                                     Brand_Code__c,
    //                                                                                     Cost_Center__c,
    //                                                                                     Description__c,
    //                                                                                     GL_Code__c
    //                                                                                     FROM Reverse_Order_Charge_Credit_Accounts__r)
    //                                                                             FROM Reverse_Order_Request__c
    //                                                                             WHERE Auto_Number__c = :requestAutoNumber];

    //                 if (reverseOrderRequests.size() > 0) {
    //                     i.Reverse_Order_Request__c = reverseOrderRequests[0].Id;
    //                     reverseRequest = reverseOrderRequests[0];
    //                     reverseItems = reverseOrderRequests[0].Reverse_Order_Items__r;
    //                     reverseItemMap.putAll(reverseItems);
    //                     reverseOrderCreditAccounts = reverseOrderRequests[0].Reverse_Order_Charge_Credit_Accounts__r;
    //                     Set<String> creditInvoiceNumberSet = new Set<String>();
    //                     if (String.isNotBlank(reverseRequest.Credit_Invoice_Number__c)) {
    //                         creditInvoiceNumberSet.addAll(reverseRequest.Credit_Invoice_Number__c.split(','));
    //                     }
    //                     creditInvoiceNumberSet.add(i.Invoice_Number__c);
    //                     creditInvoiceNumberList.addAll(creditInvoiceNumberSet);
    //                     reverseRequest.Credit_Invoice_Number__c = String.join(creditInvoiceNumberList, ',');
    //                 }
    //             }

    //             if(String.isNotBlank(reqObj.Order_Date)){
    //                 i.Order_Date__c = Date.valueOf(reqObj.Order_Date);
    //             }
    //             i.Order_Commande__c = reqObj.Order_Commande;
    //             i.Order_OracleID__c = reqObj.ATTRIBUTE3;
    //             if(orderList.size() > 0){
    //                 i.Order__c = orderList.get(0).Id;
    //                 if (String.isNotEmpty(orderList.get(0).Reverse_Order_Request__c) && orderList.get(0).Reverse_Order_Request__r.Reverse_Order_Request_Number__c == reqObj.PO_Number) {
    //                     List<Reverse_Order_Request__c> reverseOrderRequests = [SELECT Id,
    //                                                                             Reverse_Order_Type__c,
    //                                                                             Return_Goods_Status__c,
    //                                                                             Credit_Invoice_Number__c,
    //                                                                             CreatedBy.Profile.Name,
    //                                                                             CreatedBy.Email,
    //                                                                             Customer__r.Owner.Email,
    //                                                                             (SELECT Id,
    //                                                                                     Next_Step_Action__c,
    //                                                                                     Qty__c,
    //                                                                                     Invoice_Price__c,
    //                                                                                     Claimed_Credit_Memo_Amount__c,
    //                                                                                     Credit_Memo_Issued__c,
    //                                                                                     Issued_Credit_Memo_Amount__c,
    //                                                                                     Product2__c,
    //                                                                                     Product2__r.ProductCode
    //                                                                                     FROM Reverse_Order_Items__r)
    //                                                                             FROM Reverse_Order_Request__c
    //                                                                             WHERE Id = :orderList.get(0).Reverse_Order_Request__c];

    //                     if (reverseOrderRequests.size() > 0) {
    //                         i.Reverse_Order_Request__c = reverseOrderRequests[0].Id;
    //                         reverseRequest = reverseOrderRequests[0];
    //                         reverseItems = reverseOrderRequests[0].Reverse_Order_Items__r;
    //                         Set<String> creditInvoiceNumberSet = new Set<String>();
    //                         if (String.isNotBlank(reverseRequest.Credit_Invoice_Number__c)) {
    //                             creditInvoiceNumberSet.addAll(reverseRequest.Credit_Invoice_Number__c.split(','));
    //                         }
    //                         creditInvoiceNumberSet.add(i.Invoice_Number__c);
    //                         creditInvoiceNumberList.addAll(creditInvoiceNumberSet);
    //                         reverseRequest.Credit_Invoice_Number__c = String.join(creditInvoiceNumberList, ',');
    //                     }
    //                 }
    //             }
    //             i.Credit_Authorization__c = reqObj.Credit_Authorization;
    //             i.Freight_and_other_charges__c = Decimal.valueOf(reqObj.Freight_and_other_charges);
    //             i.Handling_Fee__c   = Decimal.valueOf(reqObj.HANDLING_AMT);
    //             i.Total_Due__c      = Decimal.valueOf(reqObj.Total_Due);
    //             i.CurrencyIsoCode   = reqObj.CurrencyCode;
    //             i.Terms__c          = reqObj.Terms;
    //             i.Payment_Term1__c = '';
    //             i.Payment_Term2__c = '';
    //             if(reqObj.Payment_Term1 != null){
    //                 for(String term : reqObj.Payment_Term1.split(';')){
    //                     i.Payment_Term1__c += '\n'+term;
    //                 }
    //             }
    //             if(reqObj.Payment_Term2 != null){
    //                 for(String term : reqObj.Payment_Term2.split(';')){
    //                     i.Payment_Term2__c += '\n'+term;
    //                 }
    //             }
    //             i.Remit_To_Line1__c = reqObj.Remit_To_Line1;
    //             i.Remit_To_Line2__c = reqObj.Remit_To_Line2;
    //             i.Remit_To_Line3__c = reqObj.Remit_To_Line3;
    //             i.Remit_To_Line4__c = reqObj.Remit_To_Line4;
    //             i.Remit_To_Line5__c = reqObj.Remit_To_Line5;
    //             i.Invoice_Type__c   = reqObj.Invoice_Type;
    //             i.Invoice_Source__c = reqObj.Invoice_Source;

    //             //CA tax
    //             if(String.isNotBlank(reqObj.GST_AMT)){
    //                 i.GST__c = Decimal.valueOf(reqObj.GST_AMT);
    //             }
    //             if(String.isNotBlank(reqObj.HST_AMT)){
    //                 i.HST__c = Decimal.valueOf(reqObj.HST_AMT);
    //             }
    //             if(String.isNotBlank(reqObj.QST_AMT)){
    //                 i.QST__c = Decimal.valueOf(reqObj.QST_AMT);
    //             }
    //             if(String.isNotBlank(reqObj.ATTRIBUTE5)) {
    //                 i.PST__c = Decimal.valueOf(reqObj.ATTRIBUTE5);
    //             }

    //             //Surcharge Amount
    //             if(String.isNotBlank(reqObj.ATTRIBUTE2)){
    //                 i.Surcharge_Amount__c = Decimal.valueOf(reqObj.ATTRIBUTE2);
    //             }

    //             //Org Code
    //             i.ORG_Code__c = reqObj.ATTRIBUTE1;

    //             if (warrantyReturn.size() > 0) {
    //                 i.Warranty_Return_Request__c = warrantyReturn[0].Id;
    //             }

    //             insert i;
    //             invoiceInsertOrUpdate = i;

    //             //Add by John Jiang 2020-07-21
    //             if(i.Invoice_Number__c.contains('Pack')){
    //                 List<Claim_Pack__c> cpList = [SELECT id,Invoice__c FROM Claim_Pack__c WHERE Name =: i.Invoice_Number__c];
    //                 if(cpList.size() > 0){
    //                     cpList[0].Invoice__c = i.Id;

    //                     List<Warranty_Claim__c> wcList = [SELECT Id,Payment_Status__c FROM Warranty_Claim__c WHERE Claim_Pack__c =: cpList[0].Id];
    //                     for(Warranty_Claim__c wc : wcList){
    //                         wc.Payment_Status__c = 'Paid';
    //                     }
    //                     update cpList[0];
    //                     update wcList;
    //                 }
    //             }else if(i.Invoice_Number__c.contains('PromoClaim-')){ // populate Promotion_Claim_Request__c on invoice - updated on 2021-05-05
    //                 List<Claim_Request__c> claimRequestList = [SELECT Claim_Status__c FROM Claim_Request__c WHERE Name =: i.Invoice_Number__c];
    //                 if (claimRequestList.size() > 0) {
    //                     i.Promotion_Claim_Request__c = claimRequestList[0].id;
    //                     update i;
    //                 }
    //                 for (Claim_Request__c cr : claimRequestList) {
    //                     cr.Claim_Status__c = 'Issued';
    //                 }
    //                 update claimRequestList;
    //             } else if (i.Invoice_Number__c.startsWith('COC-')) { //populate Co_Op_Claim__c on invoice - update on 2021-6-25
    //                 List<String> subStrList = i.Invoice_Number__c.split('-');
    //                 String claimName = subStrList[0] + '-' + subStrList[1];
    //                 List<Co_Op_Claim__c> coOpClaimList = [SELECT Claim_Status__c,RecordType.DeveloperName FROM Co_Op_Claim__c WHERE Name =: claimName];
    //                 if (coOpClaimList.size() > 0) {
    //                     i.Co_Op_Claim__c = coOpClaimList[0].Id;
    //                     update i;
    //                 }
    //                 for (Co_Op_Claim__c coOpClaim : coOpClaimList) {
    //                     coOpClaim.Claim_Status__c = 'Issued';
    //                     if (coOpClaim.RecordType.DeveloperName.equals(CCM_CoOpUtil.CLAIM_RECORD_TYPE_DEDUCTION)) {
    //                         coOpClaim.Claim_Status__c = 'Closed';
    //                     }
    //                 }
    //                 update coOpClaimList;
    //             }

    //             Decimal reverseCreditAmount = 0;
    //             Decimal reverseIssuedCreditAmount = 0;
    //             Boolean isReship = false;
    //             Boolean isReplacement = false;
    //             Map<String,String> invoiceItemIdMapToNo = new Map<String,String>();
    //             Set<String> descriptionSet = new Set<String>();
    //             if(reqObj.InvoiceLine != null && reqObj.InvoiceLine.size() > 0){

    //                 for(InvoiceLine il : reqObj.InvoiceLine){
    //                     Invoice_Item__c ii = new Invoice_Item__c();
    //                     ii.CurrencyIsoCode = i.CurrencyIsoCode;
    //                     ii.Invoice_Item_OracleID__c = il.Invoice_Item_OracleID;
    //                     ii.Qty_Extended__c = il.Qty_Extended;
    //                     ii.Catalog_Item_Text__c = il.Catalog_Item;
    //                     if(String.isBlank(il.Catalog_Item) && isAutoInvoice && (il.Line_Attribute1 == 'FG' || il.Line_Attribute1 == 'MKT')){
    //                         String errorMsg = Label.CCM_ErrorMsg_LackOfProductInfo;
    //                         throw new InvoiceException(errorMsg.replace('{0}', 'Invoice').replace('{1}', il.Invoice_Item_OracleID));
    //                     }
    //                     if(proMap.get(il.Catalog_Item) != null){
    //                         ii.Catalog_Item__c = proMap.get(il.Catalog_Item);
    //                     }else if(isAutoInvoice && (il.Line_Attribute1 == 'FG' || il.Line_Attribute1 == 'MKT')){
    //                         String errorMsg = Label.CCM_ErrorMsg_NotFindProduct;
    //                         throw new InvoiceException(errorMsg.replace('{0}', 'Invoice').replace('{1}', il.Invoice_Item_OracleID).replace('{2}', il.Catalog_Item));
    //                     }
    //                     ii.Customer_Item__c = il.Customer_Item;
    //                     ii.PO_Line__c       = il.PO_Line;
    //                     ii.Description__c   = il.Description;
    //                     descriptionSet.add(il.Description);

    //                     if(il.Price != null){
    //                         ii.Price__c = Decimal.valueOf(il.Price);
    //                     }
    //                     ii.Amount__c = 0;
    //                     if(il.Amount != null){
    //                         ii.Amount__c = Decimal.valueOf(il.Amount);
    //                     }
    //                     ii.Item_Type__c         = il.Line_Attribute1;
    //                     ii.Invoice__c           = i.Id;
    //                     ii.ORG_Code__c          = i.ORG_Code__c;
    //                     // fillInInfoFromShippingAddress(ii, warrantyClaimMap, shippingAddressMap);

    //                     populateInvoiceDateOfOrderItem(i,ii,il,orderItemMap,updateOrderItemList);

    //                     if(String.isNotBlank(il.Line_Attribute3)){
    //                         ii.Surcharge_Amount__c = Decimal.valueOf(il.Line_Attribute3);
    //                     }

    //                     iilist.add(ii);
    //                 }
    //             }
    //             if(iilist.size() > 0){
    //                 insert iilist;
    //             }
    //             if(updateOrderItemList.size() > 0){
    //                 update updateOrderItemList;
    //             }
    //             if (orderList.size() > 0 && reqObj.PO_Number.startsWith('RO') && String.isNotEmpty(orderList.get(0).Reverse_Order_Request__c)) {
    //                 List<Invoice_Item__c> tempItems = [SELECT Id, Amount__c, Catalog_Item__r.ProductCode FROM Invoice_Item__c WHERE Invoice__r.Invoice_Number__c IN :creditInvoiceNumberList];
    //                 for (Reverse_Order_Item__c item : reverseItems) {
    //                     item.Credit_Memo_Issued__c = 0;
    //                     for(Invoice_Item__c il : tempItems) {
    //                         if (item.Product2__r.ProductCode == il.Catalog_Item__r.ProductCode) {
    //                             item.Credit_Memo_Issued__c  += il.Amount__c.abs();
    //                             reverseIssuedCreditAmount   += il.Amount__c.abs();
    //                         }
    //                         if (item.Product2__r.ProductCode == il.Catalog_Item__r.ProductCode) {
    //                             if (item.Next_Step_Action__c == 'I want Chervon to reship the shortage product') {
    //                                 isReship = true;
    //                             } else if (item.Next_Step_Action__c == 'I want to return damaged goods and get replacement') {
    //                                 isReplacement = true;
    //                             }
    //                         }
    //                     }
    //                 }
    //             } else if (reqObj.Invoice_Number.startsWith('ROClaim')) {
    //                 for (Invoice_Item__c item : [SELECT Id, Amount__c FROM Invoice_Item__c WHERE Invoice__r.Invoice_Number__c IN :creditInvoiceNumberList]) {
    //                     reverseIssuedCreditAmount += item.Amount__c.abs();
    //                 }

    //                 for (Reverse_Order_Charge_Credit_Account__c ca : reverseOrderCreditAccounts) {
    //                     if (reverseItemMap.get(ca.Reverse_Order_Item__c).Next_Step_Action__c == 'I want Chervon to reship the shortage product') {
    //                         isReship = true;
    //                     } else if (reverseItemMap.get(ca.Reverse_Order_Item__c).Next_Step_Action__c == 'I want to return damaged goods and get replacement') {
    //                         isReplacement = true;
    //                     }
    //                 }
    //             }
    //             if ((orderList.size() > 0 && String.isNotEmpty(orderList.get(0).Reverse_Order_Request__c) && reqObj.PO_Number.startsWith('RO')) ||
    //                 (String.isNotEmpty(reqObj.Invoice_Number) && reqObj.Invoice_Number.startsWith('ROClaim'))) {
    //                 for (Reverse_Order_Item__c item : reverseItems) {
    //                     reverseCreditAmount += item.Invoice_Price__c * item.Qty__c;
    //                 }
    //                 if (reverseIssuedCreditAmount < reverseCreditAmount) {
    //                     reverseRequest.Credit_Memo_Status__c = 'Partial Issued';
    //                     if (reverseRequest.Return_Goods_Status__c == 'Received') {
    //                         reverseRequest.Credit_Memo_Status__c = 'Issued';
    //                         if(!isACE) {
    //                             reverseRequest.Reverse_Order_Request_Status__c = 'Completed';
    //                         }
    //                     }
    //                     update reverseRequest;
    //                 }
    //                 if (reverseIssuedCreditAmount == reverseCreditAmount) {
    //                     reverseRequest.Credit_Memo_Status__c = 'Issued';
    //                     if(!isACE) {
    //                         reverseRequest.Reverse_Order_Request_Status__c = 'Completed';
    //                     }
    //                     update reverseRequest;
    //                 }
    //                 if (reverseIssuedCreditAmount == 0) {
    //                     reverseRequest.Credit_Memo_Status__c = 'Not Issued';
    //                     update reverseRequest;
    //                 }
    //                 if (reqObj.PO_Number.startsWith('RO') && String.isNotEmpty(orderList.get(0).Reverse_Order_Request__c)) {
    //                     update reverseItems;
    //                 }
    //                 if (reqObj.Invoice_Number.startsWith('ROClaim')) {
    //                     update reverseItemMap.values();
    //                 }

    //                 if (reverseRequest.Reverse_Order_Type__c != 'Overage') {
    //                     // send email to na finance
    //                     if(!isACE) {
    //                         List<User> financeUsers = [SELECT Id, Email FROM User WHERE Profile.Name LIKE '%Finance%' AND IsActive = true];
    //                         Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
    //                         List<String> emailAddress = new List<String>();
    //                         for (User fin : financeUsers) {
    //                             emailAddress.add(fin.Email);
    //                         }
    //                         email.toaddresses = emailAddress;
    //                         email.subject = 'New Invoice';
    //                         String invoiceURL = URL.getSalesforceBaseUrl().toExternalForm() + '/' + i.Id;
    //                         String reverseURL = URL.getSalesforceBaseUrl().toExternalForm() + '/' + reverseRequest.Id;
    //                         String emailBody = 'Credit Memo for this reverse order request is issued. You can click the links to see more detail. <br/>' +
    //                                             'Reverse Order Request Link: <a href="' + reverseURL + '">' + reverseURL + '</a>' +
    //                                             'Credit Memo Invoice Link: <a href="' + invoiceURL + '">' + invoiceURL + '</a>';
    //                         email.htmlbody = emailBody;
    //                         Messaging.SingleEmailMessage[] emails = new Messaging.SingleEmailMessage[]{email};
    //                         Messaging.SendEmailResult[] results = Messaging.sendEmail(emails);
    //                     }
    //                 }

    //                 if (isReplacement || isReship) {
    //                     // send email to inside sales
    //                     Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
    //                     List<String> emailAddress = new List<String>();
    //                     if (reverseRequest.CreatedBy.Profile.Name.contains('Inside Sales')) {
    //                         emailAddress.add(reverseRequest.CreatedBy.Email);
    //                     } else {
    //                         emailAddress.add(reverseRequest.Customer__r.Owner.Email);
    //                     }
    //                     if (orgCode == CCM_Constants.ORG_CODE_CCA) {
    //                         emailAddress = new List<String>();
    //                         List<String> canadaRole = Label.Canada_User_Role.split(',');
    //                         Set<String> caSalesProfiles = new Set<String>{'NA Sales Manager','Canada Sales Manager','BEAM'};
    //                         for (User insideSales : [SELECT Id, Email FROM User WHERE Profile.Name IN :caSalesProfiles AND UserRole.DeveloperName IN :canadaRole AND IsActive = true]) {
    //                             emailAddress.add(insideSales.Email);
    //                         }
    //                     }
    //                     email.toaddresses = emailAddress;
    //                     email.subject = 'Return Order';
    //                     String invoiceURL = URL.getSalesforceBaseUrl().toExternalForm() + '/' + i.Id;
    //                     String reverseURL = URL.getSalesforceBaseUrl().toExternalForm() + '/' + reverseRequest.Id;
    //                     String emailBody = 'Return Order for this reverse order request is reshipped/replacement. You can place a new order with this return order. <br/>' +
    //                                         'Reverse Order Request Link: <a href="' + reverseURL + '">' + reverseURL + '</a>' +
    //                                         'Credit Memo Invoice Link: <a href="' + invoiceURL + '">' + invoiceURL + '</a>';
    //                     email.htmlbody = emailBody;
    //                     Messaging.SingleEmailMessage[] emails = new Messaging.SingleEmailMessage[]{email};
    //                     Messaging.SendEmailResult[] results = Messaging.sendEmail(emails);
    //                 }
    //             }

    //             Set<String> servicePartnerSet = new Set<String>();
    //             Map<String, List<Warranty_Claim__c>> warrantyClaimMap = new Map<String, List<Warranty_Claim__c>>();
    //             Map<String, List<Account_Address__c>> shippingAddressMap = new Map<String, List<Account_Address__c>>();
    //             for (Warranty_Claim__c claim : [SELECT Name, Invoice_Item__c, Payment_Status__c, Service_Partner__c
    //                                                 FROM Warranty_Claim__c
    //                                                 WHERE Name IN :descriptionSet]) {
    //                 if (!warrantyClaimMap.containsKey(claim.Name)) {
    //                     warrantyClaimMap.put(claim.Name, new List<Warranty_Claim__c>());
    //                 }
    //                 warrantyClaimMap.get(claim.name).add(claim);
    //                 servicePartnerSet.add(claim.Service_Partner__c);
    //             }

    //             if (servicePartnerSet.size() > 0) {
    //                 List<Account_Address__c> addressList = [SELECT Store_Number__c, Country__c, State__c, City__c,Address1__c, Postal_Code__c, X2nd_Tier_Dealer__c
    //                                                             FROM Account_Address__c
    //                                                             WHERE X2nd_Tier_Dealer__c IN :servicePartnerSet
    //                                                             AND RecordType.DeveloperName IN ('Shipping_Address', 'Dropship_Shipping_Address')];
    //                 if (addressList.size() > 0) {
    //                     for (Account_Address__c address : addressList) {
    //                         if (!shippingAddressMap.containsKey(address.X2nd_Tier_Dealer__c)) {
    //                             shippingAddressMap.put(address.X2nd_Tier_Dealer__c, new List<Account_Address__c>());
    //                         }
    //                         shippingAddressMap.get(address.X2nd_Tier_Dealer__c).add(address);
    //                     }
    //                 }
    //             }
    //             List<Warranty_Claim__c> updateWClaim = new List<Warranty_Claim__c>();
    //             List<Fleet_Claim__c> updateFClaim = new List<Fleet_Claim__c>();
    //             for(Invoice_Item__c ii : iilist){
    //                 if (String.isNotEmpty(ii.Description__c)) {
    //                     if(ii.Description__c.contains('Claim-')){
    //                         List<Warranty_Claim__c> wcList = fillInInfoFromShippingAddress(ii, warrantyClaimMap, shippingAddressMap);
    //                         if(wcList!= null && wcList.size() > 0){
    //                             wcList[0].Invoice_Item__c = ii.Id;
    //                             wcList[0].Payment_Status__c = 'Paid';
    //                             updateWClaim.add(wcList[0]);
    //                         }
    //                     }else if( ii.Description__c.contains('FClaim-')){
    //                         List<Fleet_Claim__c> fcList = [SELECT Id,Invoice_Item__c FROM Fleet_Claim__c WHERE Name =: ii.Description__c];
    //                         if(fcList.size() > 0){
    //                             fcList[0].Invoice_Item__c = ii.Id;
    //                             updateFClaim.add(fcList[0]);
    //                         }
    //                     }
    //                 }
    //             }
    //             if (!iilist.isEmpty()) {
    //                 update iilist;
    //             }
    //             if(updateWClaim.size() > 0){
    //                 update updateWClaim;
    //             }
    //             if(updateFClaim.size() > 0){
    //                 update updateFClaim;
    //             }
    //         }

    //         if(isACE && String.isNotBlank(invoiceInsertOrUpdate.Reverse_Order_Request__c)) {
    //             generateTaskForACERONotification(invoiceInsertOrUpdate.Id);
    //         }
    //         CCM_CA_WarrantyClaim_TaxChecker.checkTaxMatch(invoiceInsertOrUpdate);

    //         if (warrantyReturn.size() > 0) {
    //             updateWarrantyReturn(reqObj.PO_Number);
    //         }

    //         resObj.returnCode = 'S';
    //     }catch (InvoiceException e){
    //         resObj.returnCode = 'E';
    //         resObj.returnMsg = e.getMessage();
    //         String logId = Util.logIntegration(
    //             'Invoice Exception','CCM_RestService_DealInvoice',
    //             'POST',resObj.returnMsg,JSON.serialize(reqObj),
    //              JSON.serialize(resObj)
    //         );
    //         return resObj;
    //     }catch (Exception e) {
    //         resObj.returnCode = 'F';
    //         resObj.returnMsg = e.getLineNumber()+'-line error:'+e.getMessage();
    //         System.debug(LoggingLevel.INFO, '***'+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage());
    //         String logId = Util.logIntegration(
    //             'Invoice Exception','CCM_RestService_DealInvoice',
    //             'POST',resObj.returnMsg,JSON.serialize(reqObj),
    //              JSON.serialize(resObj)
    //         );
    //         Util.pushExceptionEmail('Accept Invoice Info',logId,resObj.returnMsg);
    //         return resObj;
    //     }
    //     if(Label.CCM_needlog == 'Y'){
    //        Util.logIntegration('Invoice log','CCM_RestService_DealInvoice', 'POST',
    //                            '',JSON.serialize(reqObj), JSON.serialize(resObj));
    //     }
    //     System.debug(LoggingLevel.INFO, '*** resObj: ' + resObj);
    //     return resObj;
    // }

    @HttpPost
    global static ResultObj doPost() {
        ResultObj resObj = new ResultObj();
        try {
            RestRequest req = RestContext.request;
            List<ReqestObj> datas = parse(req.requestBody.toString());
            CCM_InvoiceInterfaceUtil.storeDataFromInterface(datas);
            resObj.returnCode = 'S';
        }
        catch(Exception e) {
            resObj.returnCode = 'F';
            resObj.returnMsg = e.getLineNumber()+'-line error:'+e.getMessage();
            Util.logIntegration(
                'Invoice Exception','CCM_RestService_DealInvoice',
                'POST', resObj.returnMsg, resObj.returnMsg,
                resObj.returnMsg);
        }
        return resObj;
    }


    global static void processImp(ReqestObj reqObj, InvoiceInterface__c iiData) {
        try{

            // Boolean orderExist = checkOrderNeedExist(iiData);
            // if(!orderExist) {
            //     return;
            // }

            Boolean isACE = false;
            Invoice__c invoiceInsertOrUpdate = null;
            // reqObj = parse(req.requestBody.toString());
            Boolean isAutoInvoice = reqObj.Invoice_Source == 'CNA_Auto_Invoice' || reqObj.Invoice_Source == 'CA_Auto_Invoice';
            String InvoiceOracleID = reqObj.Invoice_OracleID;
            String InvoiceNumberUnique = reqObj.Invoice_Number;
            List<Invoice__c> InvoiceList = new List<Invoice__c>();
            if(String.isNotBlank(InvoiceOracleID)){
                InvoiceList = [select id,Reverse_Order_Request__c from Invoice__c where Invoice_OracleID__c = :InvoiceOracleID];
            }
            String CustomerNumber = reqObj.Customer;
            String orgCode = reqObj.ATTRIBUTE1;
            List<Account> acclist = new List<Account>();
            if(String.isNotBlank(CustomerNumber)){
                if(orgCode == 'CCA'){
                    acclist = [select id, AccountNumber from Account where AccountNumber = :CustomerNumber and ORG_Code__c = 'CCA'];
                }else{
                    acclist = [select id, AccountNumber from Account where AccountNumber = :CustomerNumber and ORG_Code__c != 'CCA'];
                }
            }
            if(acclist.size() == 0 && isAutoInvoice){
                String errorMsg = Label.CCM_ErrorMsg_NotFindCustomer;
                throw new InvoiceException(errorMsg.replace('{0}', 'Invoice').replace('{1}', reqObj.Invoice_OracleID).replace('{2}', reqObj.Customer));
            }

            Set<String> aceCustomersSet = new Set<String>();
            aceCustomersSet.addAll(Label.CCM_WarrantyReturn_ACECustomers.split(';'));
            if(aceCustomersSet.contains(CustomerNumber)) {
                isACE = true;
            }

            String BillToId = reqObj.BILL_TO_SITE_USE_ID;
            List<Address_With_Program__c> billAPlist = new List<Address_With_Program__c>();
            if(String.isNotBlank(BillToId)){
                billAPlist = [select id from Address_With_Program__c where Customer_Line_Oracle_ID__c = :BillToId];
            }
            String ShipToId = reqObj.SHIP_TO_SITE_USE_ID;
            List<Address_With_Program__c> shipAPlist = new List<Address_With_Program__c>();
            if(String.isNotBlank(ShipToId)){
                shipAPlist = [select id from Address_With_Program__c where Customer_Line_Oracle_ID__c = :ShipToId];
            }
            String shipmentId = reqObj.Delivery_Number;
            List<Shipment__c> shipmentList = new List<Shipment__c>();
            if(String.isNotBlank(shipmentId)){
                shipmentList = [select id from Shipment__c where Ship_OracleID__c = :shipmentId];
            }
            String orderNumber = reqObj.Order_Commande;
            String orderOracleId = reqObj.ATTRIBUTE3;
            List<Order> orderList = new List<Order>();
            List<OrderInterface__c> orderInterfaceList = new List<OrderInterface__c>();
            List<Order_Item__c> orderItemList = new List<Order_Item__c>();
            Boolean isHaveOrder = false;
            Decimal orderTotalPrice = 0;
            Map<String, Order_Item__c> orderItemMap = new Map<String, Order_Item__c>();
            List<Order_Item__c> updateOrderItemList = new List<Order_Item__c>();
            if(String.isNotBlank(orderOracleId)){
                orderList = [select id, Reverse_Order_Request__c, Reverse_Order_Request__r.Reverse_Order_Request_Number__c from Order where Order_OracleID__c = :orderOracleId];
                orderInterfaceList = [SELECT Id FROM OrderInterface__c WHERE Order_OracleID__c = :orderOracleId AND Return_Code__c = 'NA'];
                orderItemList = [SELECT id,OrderLine_OracleID__c,Order_Quantity__c,Line_Status__c,Price__c FROM Order_Item__c WHERE Order__r.Order_OracleID__c = :orderOracleId];
            }
            Boolean dependentOrderCreated = true;
            if(orderList.isEmpty() && !orderInterfaceList.isEmpty()) {
                dependentOrderCreated = false;
            }
            if(!dependentOrderCreated) {
                return;
            }
            if(orderItemList.size() > 0){
                for(Order_Item__c oItem : orderItemList){
                    if(oItem.Line_Status__c == 'RETURNED' || oItem.Line_Status__c == 'CLOSED'){
                        isHaveOrder = true;
                        orderTotalPrice += oItem.Price__c * oItem.Order_Quantity__c.abs();
                    }
                    if(String.isNotBlank(oItem.OrderLine_OracleID__c)){
                        orderItemMap.put(oItem.OrderLine_OracleID__c, oItem);
                    }
                }
            }
            Map<String,Id> proMap = new Map<String,Id>();
            Map<String,Id> partsMap = new Map<String,Id>();
            List<String> proNumberlist = new List<String>();
            List<String> InvoiceLineIdlist = new List<String>();
            if(reqObj.InvoiceLine != null && reqObj.InvoiceLine.size() > 0){
                for(InvoiceLine il : reqObj.InvoiceLine){
                    if(!proNumberlist.contains(il.Catalog_Item)){
                        proNumberlist.add(il.Catalog_Item);
                    }
                    if(!InvoiceLineIdlist.contains(il.Invoice_Item_OracleID)){
                        InvoiceLineIdlist.add(il.Invoice_Item_OracleID);
                    }
                }
            }
            List<Product2> prolist = [select id,ProductCode from Product2 where ProductCode in :proNumberlist AND IsCreatedByCode__c = FALSE AND Is_History_Product__c = :CCM_Constants.blHistoryProduct  AND Source__c ='EBS' AND IsActive = true  ORDER BY CreatedDate ASC];
            for(Product2 p : prolist){
                proMap.put(p.ProductCode, p.Id);
            }
            List<Product2> partslist = [select id,Item_Number__c from Product2 where Item_Number__c  in :proNumberlist AND IsCreatedByCode__c = FALSE AND Is_History_Product__c = :CCM_Constants.blHistoryProduct  AND Source__c ='EBS' AND IsActive = true  ORDER BY CreatedDate ASC];
            for(Product2 par : partslist){
                partsMap.put(par.Item_Number__c, par.Id);
            }

            List<Warranty_Return_Claim__c> warrantyReturn = [SELECT Id, (SELECT Id, Model__r.ProductCode, Subtotal__c, Credit_Memo_Amount__c FROM Warranty_Return_Claim_Item__r)
                                                             FROM Warranty_Return_Claim__c
                                                             WHERE Name = :reqObj.PO_Number];
            if(InvoiceList.size() > 0){
                Invoice__c i = InvoiceList.get(0);
                //i.Invoice_OracleID__c = InvoiceOracleID;
                i.Chervon_Name__c = reqObj.Chervon_Name;
                i.Chervon_Address1__c = reqObj.Chervon_Address1;
                i.Chervon_Address2__c = reqObj.Chervon_Address2;
                i.Invoice_Date__c = Date.valueOf(reqObj.Invoice_Date);
                i.Payment_Terms_Record__c = reqObj.ATTRIBUTE4;
                i.Invoice_Number__c = reqObj.Invoice_Number;
                i.Customer_Text__c = CustomerNumber;
                if(acclist.size() > 0){
                    i.Customer__c = acclist.get(0).id;
                }
                i.BillTo_Text__c = reqObj.BillTo;
                i.BILL_TO_SITE_USE_ID__c = BillToId;
                if(billAPlist.size() > 0){
                    i.BillTo__c = billAPlist.get(0).id;
                }
                i.BillTo_Customer__c        = reqObj.BillTo_Customer;
                i.BillTo_Address1__c        = reqObj.BillTo_Address1;
                i.BillTo_Address2__c        = reqObj.BillTo_Address2;
                i.BillTo_Address3__c        = reqObj.BillTo_Address3;
                i.ShipTo_Text__c            = reqObj.ShipTo;
                i.SHIP_TO_SITE_USE_ID__c    = ShipToId;
                if(shipAPlist.size() > 0){
                    i.ShipTo__c = shipAPlist.get(0).id;
                }
                i.ShipTo_Customer__c        = reqObj.ShipTo_Customer;
                i.ShipTo_Address1__c        = reqObj.ShipTo_Address1;
                i.ShipTo_Address2__c        = reqObj.ShipTo_Address2;
                i.ShipTo_Address3__c        = reqObj.ShipTo_Address3;

                i.DropShip_CustomerNumber__c = reqObj.DropShip_CustomerNumber;
                i.DropShip_Name__c          = reqObj.DropShip_Name;
                i.DropShip_Address1__c      = reqObj.DropShip_Address1;
                i.DropShip_Address2__c      = reqObj.DropShip_Address2;
                i.DropShip_Address3__c      = reqObj.DropShip_Address3;

                i.Tracking_NO__c            = reqObj.Tracking_NO;
                i.Number_of_Shipping_Units__c = reqObj.Number_of_Shipping_Units;
                i.Delivery_Number__c        = reqObj.Delivery_Number;
                if(shipmentList.size() > 0){
                    i.Shipment__c = shipmentList.get(0).Id;
                }
                i.Freight_Term__c   = reqObj.Freight_Term;
                i.Origin__c         = reqObj.Origin;
                i.Terr_Number__c    = reqObj.Terr_Number;
                i.Carrier__c        = reqObj.Carrier;
                i.PO_Number__c      = reqObj.PO_Number;

                // link invoice to reverse order request
                List<String> creditInvoiceNumberList = new List<String>();
                List<Reverse_Order_Item__c> reverseItems = new List<Reverse_Order_Item__c>();
                Reverse_Order_Request__c reverseRequest;
                List<Reverse_Order_Charge_Credit_Account__c> reverseOrderCreditAccounts = new List<Reverse_Order_Charge_Credit_Account__c>();
                Map<Id, Reverse_Order_Item__c> reverseItemMap = new Map<Id, Reverse_Order_Item__c>();
                if (String.isNotEmpty(reqObj.Invoice_Number) && reqObj.Invoice_Number.startsWith('ROClaim')) {
                    List<String> autoNumber = reqObj.Invoice_Number.split('-');
                    String requestAutoNumber = autoNumber[0] + '-' + autoNumber[1];
                    List<Reverse_Order_Request__c> reverseOrderRequests = [SELECT Id,
                                                                                Return_Goods_Status__c,
                                                                                Reverse_Order_Type__c,
                                                                                Credit_Invoice_Number__c,
                                                                                CreatedBy.Profile.Name,
                                                                                CreatedBy.Email,
                                                                                Customer__r.Owner.Email,
                                                                                (SELECT Id,
                                                                                        Next_Step_Action__c,
                                                                                        Qty__c,
                                                                                        Invoice_Price__c,
                                                                                        Claimed_Credit_Memo_Amount__c,
                                                                                        Credit_Memo_Issued__c,
                                                                                        Issued_Credit_Memo_Amount__c,
                                                                                        Product2__c,
                                                                                        Product2__r.ProductCode,
                                                                                        Order_Product_Type__c
                                                                                        FROM Reverse_Order_Items__r),
                                                                                (SELECT Id,
                                                                                        Reverse_Order_Request__c,
                                                                                        Reverse_Order_Item__r.Name,
                                                                                        Amount__c,
                                                                                        Brand_Code__c,
                                                                                        Cost_Center__c,
                                                                                        Description__c,
                                                                                        GL_Code__c
                                                                                        FROM Reverse_Order_Charge_Credit_Accounts__r)
                                                                                FROM Reverse_Order_Request__c
                                                                                WHERE Auto_Number__c = :requestAutoNumber];

                    if (reverseOrderRequests.size() > 0) {
                        i.Reverse_Order_Request__c = reverseOrderRequests[0].Id;
                        reverseRequest = reverseOrderRequests[0];
                        reverseItems = reverseOrderRequests[0].Reverse_Order_Items__r;
                        reverseItemMap.putAll(reverseItems);
                        reverseOrderCreditAccounts = reverseOrderRequests[0].Reverse_Order_Charge_Credit_Accounts__r;
                        Set<String> creditInvoiceNumberSet = new Set<String>();
                        if (String.isNotBlank(reverseRequest.Credit_Invoice_Number__c)) {
                            creditInvoiceNumberSet.addAll(reverseRequest.Credit_Invoice_Number__c.split(','));
                        }
                        creditInvoiceNumberSet.add(i.Invoice_Number__c);
                        creditInvoiceNumberList.addAll(creditInvoiceNumberSet);
                        reverseRequest.Credit_Invoice_Number__c = String.join(creditInvoiceNumberList, ',');
                    }
                }

                if(String.isNotBlank(reqObj.Order_Date)){
                    i.Order_Date__c = Date.valueOf(reqObj.Order_Date);
                }
                i.Order_Commande__c = reqObj.Order_Commande;
                i.Order_OracleID__c = reqObj.ATTRIBUTE3;
                if(orderList.size() > 0){
                    i.Order__c = orderList.get(0).Id;
                    if (String.isNotEmpty(orderList.get(0).Reverse_Order_Request__c) && orderList.get(0).Reverse_Order_Request__r.Reverse_Order_Request_Number__c == reqObj.PO_Number) {
                        List<Reverse_Order_Request__c> reverseOrderRequests = [SELECT Id,
                                                                                Reverse_Order_Type__c,
                                                                                Return_Goods_Status__c,
                                                                                Credit_Invoice_Number__c,
                                                                                CreatedBy.Profile.Name,
                                                                                CreatedBy.Email,
                                                                                Customer__r.Owner.Email,
                                                                                (SELECT Id,
                                                                                        Next_Step_Action__c,
                                                                                        Qty__c,
                                                                                        Invoice_Price__c,
                                                                                        Claimed_Credit_Memo_Amount__c,
                                                                                        Credit_Memo_Issued__c,
                                                                                        Issued_Credit_Memo_Amount__c,
                                                                                        Product2__c,
                                                                                        Product2__r.ProductCode
                                                                                        FROM Reverse_Order_Items__r)
                                                                                FROM Reverse_Order_Request__c
                                                                                WHERE Id = :orderList.get(0).Reverse_Order_Request__c];

                        if (reverseOrderRequests.size() > 0) {
                            i.Reverse_Order_Request__c = reverseOrderRequests[0].Id;
                            reverseRequest = reverseOrderRequests[0];
                            reverseItems = reverseOrderRequests[0].Reverse_Order_Items__r;
                            Set<String> creditInvoiceNumberSet = new Set<String>();
                            if (String.isNotBlank(reverseRequest.Credit_Invoice_Number__c)) {
                                creditInvoiceNumberSet.addAll(reverseRequest.Credit_Invoice_Number__c.split(','));
                            }
                            creditInvoiceNumberSet.add(i.Invoice_Number__c);
                            creditInvoiceNumberList.addAll(creditInvoiceNumberSet);
                            reverseRequest.Credit_Invoice_Number__c = String.join(creditInvoiceNumberList, ',');
                        }
                    }
                }
                i.Credit_Authorization__c = reqObj.Credit_Authorization;
                i.Freight_and_other_charges__c = Decimal.valueOf(reqObj.Freight_and_other_charges);
                i.Handling_Fee__c   = Decimal.valueOf(reqObj.HANDLING_AMT);
                i.Total_Due__c      = Decimal.valueOf(reqObj.Total_Due);
                i.CurrencyIsoCode   = reqObj.CurrencyCode;
                i.Terms__c          = reqObj.Terms;
                i.Payment_Term_Code__c =  reqObj.ATTRIBUTE6;//Payment term code Added by Zoe for french portal on 2024-9-25
                i.Payment_Term1__c = '';
                i.Payment_Term2__c = '';
                if(reqObj.Payment_Term1 != null){
                    for(String term : reqObj.Payment_Term1.split(';')){
                        i.Payment_Term1__c += '\n'+term;
                    }
                }
                if(reqObj.Payment_Term2 != null){
                    for(String term : reqObj.Payment_Term2.split(';')){
                        i.Payment_Term2__c += '\n'+term;
                    }
                }

                i.Remit_To_Line3__c = reqObj.Remit_To_Line3;
                i.Remit_To_Line4__c = reqObj.Remit_To_Line4;
                i.Remit_To_Line5__c = reqObj.Remit_To_Line5;
                i.Invoice_Type__c   = reqObj.Invoice_Type;
                i.Invoice_Source__c = reqObj.Invoice_Source;

                //CA tax
                if(String.isNotBlank(reqObj.GST_AMT)){
                    i.GST__c = Decimal.valueOf(reqObj.GST_AMT);
                }
                if(String.isNotBlank(reqObj.HST_AMT)){
                    i.HST__c = Decimal.valueOf(reqObj.HST_AMT);
                }
                if(String.isNotBlank(reqObj.QST_AMT)){
                    i.QST__c = Decimal.valueOf(reqObj.QST_AMT);
                }
                if(String.isNotBlank(reqObj.ATTRIBUTE5)) {
                    i.PST__c = Decimal.valueOf(reqObj.ATTRIBUTE5);
                }
                //Org Code
                i.ORG_Code__c = reqObj.ATTRIBUTE1;

                //Surcharge Amount
                if(String.isNotBlank(reqObj.ATTRIBUTE2)){
                    i.Surcharge_Amount__c = Decimal.valueOf(reqObj.ATTRIBUTE2);
                }

                if (warrantyReturn.size() > 0) {
                    i.Warranty_Return_Request__c = warrantyReturn[0].Id;
                }

                update i;
                invoiceInsertOrUpdate = i;

                List<Invoice_Item__c> iilist = [select id,Invoice_Item_OracleID__c
                                                from Invoice_Item__c
                                                where Invoice_Item_OracleID__c in :InvoiceLineIdlist
                                                And Invoice__c = :i.Id];
                List<Invoice_Item__c> iiInsertList = new List<Invoice_Item__c>();
                List<Invoice_Item__c> iiUpdateList = new List<Invoice_Item__c>();
                Map<String,Invoice_Item__c> iiMap  = new Map<String,Invoice_Item__c>();
                for(Invoice_Item__c ii : iilist){
                    iiMap.put(ii.Invoice_Item_OracleID__c, ii);
                }

                Decimal reverseCreditAmount = 0;
                Decimal reverseIssuedCreditAmount = 0;
                Map<String, Decimal> productAmountMap = new Map<String, Decimal>();
                Boolean isReship = false;
                Boolean isReplacement = false;
                Map<String, List<SerialNumberLine>> snLineMap = new Map<String, List<SerialNumberLine>>();
                if(reqObj.InvoiceLine != null && reqObj.InvoiceLine.size() > 0){
                    Set<String> descriptionSet = new Set<String>();
                    for (InvoiceLine il : reqObj.InvoiceLine) {
                        descriptionSet.add(il.Description);
                    }
                    Set<String> servicePartnerSet = new Set<String>();
                    Map<String, List<Warranty_Claim__c>> warrantyClaimMap = new Map<String, List<Warranty_Claim__c>>();
                    Map<String, Warranty__c> RegistrationWarrantyMap = new Map<String, Warranty__c>();
                    for (Warranty_Claim__c claim : [SELECT Name, Invoice_Item__c, Payment_Status__c, Service_Partner__c
                                                        FROM Warranty_Claim__c
                                                        WHERE Name IN :descriptionSet]) {
                        if (!warrantyClaimMap.containsKey(claim.Name)) {
                            warrantyClaimMap.put(claim.Name, new List<Warranty_Claim__c>());
                        }
                        warrantyClaimMap.get(claim.name).add(claim);
                        servicePartnerSet.add(claim.Service_Partner__c);
                    }
                    for(Warranty__c wa : [SELECT Id,Name,Claim_Pack__c,Claim_Pack__r.Second_Tier_Customer__c FROM Warranty__c WHERE Name IN :descriptionSet]){
                        if (!RegistrationWarrantyMap.containsKey(wa.Name)) {
                            RegistrationWarrantyMap.put(wa.Name, wa);
                        }
                        servicePartnerSet.add(wa.Claim_Pack__r.Second_Tier_Customer__c);
                    }
                    Map<String, List<Account_Address__c>> shippingAddressMap = new Map<String, List<Account_Address__c>>();
                    if (servicePartnerSet.size() > 0) {
                        List<Account_Address__c> addressList = [SELECT Store_Number__c, Country__c, State__c, City__c,Address1__c, Postal_Code__c, X2nd_Tier_Dealer__c
                                                                    FROM Account_Address__c
                                                                    WHERE X2nd_Tier_Dealer__c IN :servicePartnerSet
                                                                    AND RecordType.DeveloperName IN ('Shipping_Address', 'Dropship_Shipping_Address')];
                        if (addressList.size() > 0) {
                            for (Account_Address__c address : addressList) {
                                if (!shippingAddressMap.containsKey(address.X2nd_Tier_Dealer__c)) {
                                    shippingAddressMap.put(address.X2nd_Tier_Dealer__c, new List<Account_Address__c>());
                                }
                                shippingAddressMap.get(address.X2nd_Tier_Dealer__c).add(address);
                            }
                        }
                    }

                    for(InvoiceLine il : reqObj.InvoiceLine){
                        if(!il.SerialNumberLine.isEmpty()) {
                            snLineMap.put(il.Invoice_Item_OracleID, il.SerialNumberLine);
                        }
                        if(iiMap.get(il.Invoice_Item_OracleID) != null){
                            Invoice_Item__c ii      = iiMap.get(il.Invoice_Item_OracleID);
                            ii.CurrencyIsoCode      = i.CurrencyIsoCode;
                            ii.Qty_Extended__c      = il.Qty_Extended;
                            ii.Catalog_Item_Text__c = il.Catalog_Item;
                            if(String.isBlank(il.Catalog_Item) && isAutoInvoice && (il.Line_Attribute1 == 'FG' || il.Line_Attribute1 == 'MKT')){
                                String errorMsg = Label.CCM_ErrorMsg_LackOfProductInfo;
                                throw new InvoiceException(errorMsg.replace('{0}', 'Invoice').replace('{1}', il.Invoice_Item_OracleID));
                            }
                            if(il.Line_Attribute1 != 'SP'){
                                if(proMap.get(il.Catalog_Item) != null){
                                    ii.Catalog_Item__c  = proMap.get(il.Catalog_Item);
                                }else if(isAutoInvoice && (il.Line_Attribute1 == 'FG' || il.Line_Attribute1 == 'MKT')){
                                    String errorMsg = Label.CCM_ErrorMsg_NotFindProduct;
                                    throw new InvoiceException(errorMsg.replace('{0}', 'Invoice').replace('{1}', il.Invoice_Item_OracleID).replace('{2}', il.Catalog_Item));
                                }
                            }else{
                                if(partsMap.get(il.Catalog_Item) != null){
                                    ii.Catalog_Item__c  = partsMap.get(il.Catalog_Item);
                                }else {
                                    String errorMsg = Label.CCM_ErrorMsg_NotFindProduct;
                                    throw new InvoiceException(errorMsg.replace('{0}', 'Invoice').replace('{1}', il.Invoice_Item_OracleID).replace('{2}', il.Catalog_Item));
                                }
                            }
                            ii.Customer_Item__c     = il.Customer_Item;
                            ii.PO_Line__c           = il.PO_Line;
                            ii.Description__c       = il.Description;
                            ii.Price__c             = String.isEmpty(il.Price) ? 0 : Decimal.valueOf(il.Price);
                            ii.Amount__c            = String.isEmpty(il.Amount) ? 0 : Decimal.valueOf(il.Amount);
                            ii.Item_Type__c         = il.Line_Attribute1;
                            ii.Invoice__c           = i.Id;
                            ii.ORG_Code__c          = i.ORG_Code__c;
                            fillInInfoFromShippingAddress(ii, warrantyClaimMap, shippingAddressMap);
                            fillWarrantyInfoFromShippingAddress(ii,RegistrationWarrantyMap,shippingAddressMap);
                            populateInvoiceDateOfOrderItem(i,ii,il,orderItemMap,updateOrderItemList);
                            if(String.isNotBlank(il.Line_Attribute3)){
                                ii.Surcharge_Amount__c = Decimal.valueOf(il.Line_Attribute3);
                            }
                            iiUpdateList.add(ii);
                        }else{
                            Invoice_Item__c ii          = new Invoice_Item__c();
                            ii.CurrencyIsoCode          = i.CurrencyIsoCode;
                            ii.Invoice_Item_OracleID__c = il.Invoice_Item_OracleID;
                            ii.Qty_Extended__c          = il.Qty_Extended;
                            ii.Catalog_Item_Text__c     = il.Catalog_Item;
                            if(String.isBlank(il.Catalog_Item) && isAutoInvoice && (il.Line_Attribute1 == 'FG' || il.Line_Attribute1 == 'MKT')){
                                String errorMsg = Label.CCM_ErrorMsg_LackOfProductInfo;
                                throw new InvoiceException(errorMsg.replace('{0}', 'Invoice').replace('{1}', il.Invoice_Item_OracleID));
                            }
                            if(il.Line_Attribute1 != 'SP'){
                                if(proMap.get(il.Catalog_Item) != null){
                                    ii.Catalog_Item__c  = proMap.get(il.Catalog_Item);
                                }else if(isAutoInvoice && (il.Line_Attribute1 == 'FG' || il.Line_Attribute1 == 'MKT')){
                                    String errorMsg = Label.CCM_ErrorMsg_NotFindProduct;
                                    throw new InvoiceException(errorMsg.replace('{0}', 'Invoice').replace('{1}', il.Invoice_Item_OracleID).replace('{2}', il.Catalog_Item));
                                }
                            }else{
                                if(partsMap.get(il.Catalog_Item) != null){
                                    ii.Catalog_Item__c  = partsMap.get(il.Catalog_Item);
                                }else {
                                    String errorMsg = Label.CCM_ErrorMsg_NotFindProduct;
                                    throw new InvoiceException(errorMsg.replace('{0}', 'Invoice').replace('{1}', il.Invoice_Item_OracleID).replace('{2}', il.Catalog_Item));
                                }
                            }
                            ii.Customer_Item__c         = il.Customer_Item;
                            ii.PO_Line__c               = il.PO_Line;
                            ii.Description__c           = il.Description;
                            if(il.Price != null){
                                ii.Price__c             = Decimal.valueOf(il.Price);
                            }
                            ii.Amount__c = 0;
                            if(il.Amount != null){
                                ii.Amount__c            = Decimal.valueOf(il.Amount);
                            }
                            ii.Item_Type__c             = il.Line_Attribute1;
                            ii.Invoice__c               = i.Id;
                            fillInInfoFromShippingAddress(ii, warrantyClaimMap, shippingAddressMap);
                            fillWarrantyInfoFromShippingAddress(ii,RegistrationWarrantyMap,shippingAddressMap);
                            populateInvoiceDateOfOrderItem(i,ii,il,orderItemMap,updateOrderItemList);
                            ii.ORG_Code__c = i.ORG_Code__c;
                            if(String.isNotBlank(il.Line_Attribute3)){
                                ii.Surcharge_Amount__c = Decimal.valueOf(il.Line_Attribute3);
                            }
                            iiInsertList.add(ii);
                        }
                    }
                }
                if(iiInsertList.size() > 0){
                    insert iiInsertList;
                }
                if(iiUpdateList.size() > 0){
                    update iiUpdateList;
                }

                if(!iiInsertList.isEmpty() || !iiUpdateList.isEmpty()) {
                    Map<String, String> invoiceItemMap = new Map<String, String>();
                    for(Invoice_Item__c ii : iiInsertList) {
                        invoiceItemMap.put(ii.Invoice_Item_OracleID__c, ii.Id);
                    }
                    for(Invoice_Item__c ii : iiUpdateList) {
                        invoiceItemMap.put(ii.Invoice_Item_OracleID__c, ii.Id);
                    }

                    upsertSerialNumberLine(invoiceItemMap, snLineMap);
                }

                // 更新去重的数据
                updateNoneDuplicateOis(updateOrderItemList);

                if (orderList.size() > 0 && String.isNotBlank(reqObj.PO_Number) && reqObj.PO_Number.startsWith('RO') && String.isNotEmpty(orderList.get(0).Reverse_Order_Request__c)) {
                    List<Invoice_Item__c> tempItems = [SELECT Id, Amount__c, Catalog_Item__r.ProductCode FROM Invoice_Item__c WHERE Invoice__r.Invoice_Number__c IN :creditInvoiceNumberList];
                    for (Reverse_Order_Item__c item : reverseItems) {
                        item.Credit_Memo_Issued__c = 0;
                        for(Invoice_Item__c il : tempItems) {
                            if (item.Product2__r.ProductCode == il.Catalog_Item__r.ProductCode) {
                                item.Credit_Memo_Issued__c  += il.Amount__c.abs();
                                reverseIssuedCreditAmount   += il.Amount__c.abs();

                                if(!productAmountMap.containsKey(il.Catalog_Item__r.ProductCode)) {
                                    productAmountMap.put(il.Catalog_Item__r.ProductCode, 0);
                                }
                                Decimal tempAmount = productAmountMap.get(il.Catalog_Item__r.ProductCode) + il.Amount__c.abs();
                                productAmountMap.put(il.Catalog_Item__r.ProductCode, tempAmount);
                            }
                            if (item.Product2__r.ProductCode == il.Catalog_Item__r.ProductCode) {
                                if (item.Next_Step_Action__c == 'I want Chervon to reship the shortage product') {
                                    isReship = true;
                                } else if (item.Next_Step_Action__c == 'I want to return damaged goods and get replacement') {
                                    isReplacement = true;
                                }
                            }
                        }
                    }
                } else if (reqObj.Invoice_Number.startsWith('ROClaim')) {
                    for (Invoice_Item__c item : [SELECT Id, Amount__c, Catalog_Item__r.ProductCode FROM Invoice_Item__c WHERE Invoice__r.Invoice_Number__c IN :creditInvoiceNumberList]) {
                        reverseIssuedCreditAmount += item.Amount__c.abs();
                        if(!productAmountMap.containsKey(item.Catalog_Item__r.ProductCode)) {
                            productAmountMap.put(item.Catalog_Item__r.ProductCode, 0);
                        }
                        Decimal tempAmount = productAmountMap.get(item.Catalog_Item__r.ProductCode) + item.Amount__c.abs();
                        productAmountMap.put(item.Catalog_Item__r.ProductCode, tempAmount);
                    }

                    for (Reverse_Order_Charge_Credit_Account__c ca : reverseOrderCreditAccounts) {
                        if (reverseItemMap.get(ca.Reverse_Order_Item__c).Next_Step_Action__c == 'I want Chervon to reship the shortage product') {
                            isReship = true;
                        } else if (reverseItemMap.get(ca.Reverse_Order_Item__c).Next_Step_Action__c == 'I want to return damaged goods and get replacement') {
                            isReplacement = true;
                        }
                    }
                }
                if ((orderList.size() > 0 && String.isNotEmpty(orderList.get(0).Reverse_Order_Request__c) && String.isNotBlank(reqObj.PO_Number) && reqObj.PO_Number.startsWith('RO')) ||
                    (String.isNotEmpty(reqObj.Invoice_Number) && reqObj.Invoice_Number.startsWith('ROClaim'))) {
                    Map<String, Decimal> productShortageAmountMap = new Map<String, Decimal>();
                    for (Reverse_Order_Item__c item : reverseItems) {
                        reverseCreditAmount += item.Invoice_Price__c * item.Qty__c;
                        if(item.Order_Product_Type__c == 'Shortage') {
                            if(!productShortageAmountMap.containsKey(item.Product2__r.ProductCode)) {
                                productShortageAmountMap.put(item.Product2__r.ProductCode, 0);
                            }

                            Decimal tempAmount = productShortageAmountMap.get(item.Product2__r.ProductCode) + item.Invoice_Price__c * item.Qty__c;
                            productShortageAmountMap.put(item.Product2__r.ProductCode, tempAmount);
                        }
                    }
                    // For wrong product, only invoice of shortage will be received, no invoice of overage will be received, so only compare the amount of shortage.
                    if(reverseRequest.Reverse_Order_Type__c == 'Wrong Product') {
                        Boolean amountFullfill = true;
                        for(String productCode : productShortageAmountMap.keySet()) {
                            Decimal roiAmount = productShortageAmountMap.get(productCode);
                            if(productAmountMap.containsKey(productCode)) {
                                Decimal invoiceAmount = productAmountMap.get(productCode);
                                if(invoiceAmount < roiAmount) {
                                    amountFullfill = false;
                                    break;
                                }
                            }
                            else {
                                amountFullfill = false;
                            }
                        }
                        if(amountFullfill) {
                            reverseRequest.Credit_Memo_Status__c = 'Issued';
                        }
                    }
                    else {
                        if (reverseIssuedCreditAmount < reverseCreditAmount) {
                            reverseRequest.Credit_Memo_Status__c = 'Partial Issued';
                            if (reverseRequest.Return_Goods_Status__c == 'Received') {
                                reverseRequest.Credit_Memo_Status__c = 'Issued';
                                if(!isACE) {
                                    reverseRequest.Reverse_Order_Request_Status__c = 'Completed';
                                }
                            }
                            update reverseRequest;
                        }
                        if (reverseIssuedCreditAmount == reverseCreditAmount) {
                            reverseRequest.Credit_Memo_Status__c = 'Issued';
                            if(!isACE) {
                                reverseRequest.Reverse_Order_Request_Status__c = 'Completed';
                            }
                            update reverseRequest;
                        }
                        if(isHaveOrder){
                            if(reverseIssuedCreditAmount < orderTotalPrice){
                                reverseRequest.Credit_Memo_Status__c = 'Partial Issued';
                                if (reverseRequest.Return_Goods_Status__c == 'Received') {
                                    reverseRequest.Credit_Memo_Status__c = 'Issued';
                                    if(!isACE) {
                                        reverseRequest.Reverse_Order_Request_Status__c = 'Completed';
                                    }
                                }
                                update reverseRequest;
                            }
                            if(reverseIssuedCreditAmount == orderTotalPrice){
                                reverseRequest.Credit_Memo_Status__c = 'Issued';
                                if(!isACE) {
                                    reverseRequest.Reverse_Order_Request_Status__c = 'Completed';
                                }
                                update reverseRequest;
                            }
                        }
                    }
                    if (reverseIssuedCreditAmount == 0) {
                        reverseRequest.Credit_Memo_Status__c = 'Not Issued';
                        update reverseRequest;
                    }
                    if (String.isNotBlank(reqObj.PO_Number) && reqObj.PO_Number.startsWith('RO') && String.isNotEmpty(orderList.get(0).Reverse_Order_Request__c)) {
                        update reverseItems;
                    } else if (reqObj.Invoice_Number.startsWith('ROClaim')) {
                        update reverseItemMap.values();
                    }

                    if (reverseRequest.Reverse_Order_Type__c != 'Overage') {
                        if(!isACE) {
                        // send email to na finance
                            List<User> financeUsers = [SELECT Id, Email FROM User WHERE Profile.Name LIKE '%Finance%' AND IsActive = true];
                            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                            List<String> emailAddress = new List<String>();
                            for (User fin : financeUsers) {
                                emailAddress.add(fin.Email);
                            }
                            email.toaddresses = emailAddress;
                            email.subject = 'New Invoice';
                            String invoiceURL = URL.getSalesforceBaseUrl().toExternalForm() + '/' + i.Id;
                            String reverseURL = URL.getSalesforceBaseUrl().toExternalForm() + '/' + reverseRequest.Id;
                            String emailBody = 'Credit Memo for this reverse order request is issued. You can click the links to see more detail. <br/>' +
                                                'Reverse Order Request Link: <a href="' + reverseURL + '">' + reverseURL + '</a>' +
                                                'Credit Memo Invoice Link: <a href="' + invoiceURL + '">' + invoiceURL + '</a>';
                            email.htmlbody = emailBody;
                            Messaging.SingleEmailMessage[] emails = new Messaging.SingleEmailMessage[]{email};
                            Messaging.SendEmailResult[] results = Messaging.sendEmail(emails);
                        }
                    }

                    if (isReplacement || isReship) {
                        // send email to inside sales
                        Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                        List<String> emailAddress = new List<String>();
                        if (reverseRequest.CreatedBy.Profile.Name.contains('Inside Sales')) {
                            emailAddress.add(reverseRequest.CreatedBy.Email);
                        } else {
                            emailAddress.add(reverseRequest.Customer__r.Owner.Email);
                        }
                        if (orgCode == CCM_Constants.ORG_CODE_CCA) {
                            emailAddress = new List<String>();
                            Set<String> caSalesProfiles = new Set<String>{'BEAM'};
                            List<String> canadaRole = Label.CABEAMRole.split(';');
                            for (User insideSales : [SELECT Id, Email FROM User WHERE Profile.Name IN :caSalesProfiles AND UserRole.DeveloperName IN :canadaRole AND IsActive = true]) {
                                emailAddress.add(insideSales.Email);
                            }
                            emailAddress.add(Label.GeoffBobykEmail);
                            emailAddress.add(Label.JenniferBilaEmail);
                            emailAddress.add(Label.DanaEilotEmail);
                            emailAddress.add(reverseRequest.Customer__r.Owner.Email);
                        }
                        email.toaddresses = emailAddress;
                        email.subject = 'Return Order';
                        String invoiceURL = URL.getSalesforceBaseUrl().toExternalForm() + '/' + i.Id;
                        String reverseURL = URL.getSalesforceBaseUrl().toExternalForm() + '/' + reverseRequest.Id;
                        String emailBody = 'Return Order for this reverse order request is reshipped/replacement. You can place a new order with this return order. <br/>' +
                                            'Reverse Order Request Link: <a href="' + reverseURL + '">' + reverseURL + '</a>' +
                                            'Credit Memo Invoice Link: <a href="' + invoiceURL + '">' + invoiceURL + '</a>';
                        email.htmlbody = emailBody;
                        Messaging.SingleEmailMessage[] emails = new Messaging.SingleEmailMessage[]{email};
                        Messaging.SendEmailResult[] results = Messaging.sendEmail(emails);
                    }
                }
            }else{
                List<Invoice_Item__c> iilist = new List<Invoice_Item__c>();
                Invoice__c i = new Invoice__c();
                i.Invoice_OracleID__c = InvoiceOracleID;
                i.Chervon_Name__c = reqObj.Chervon_Name;
                i.Chervon_Address1__c = reqObj.Chervon_Address1;
                i.Chervon_Address2__c = reqObj.Chervon_Address2;
                i.Invoice_Date__c = Date.valueOf(reqObj.Invoice_Date);
                i.Payment_Terms_Record__c = reqObj.ATTRIBUTE4;
                i.Invoice_Number__c = reqObj.Invoice_Number;
                i.Customer_Text__c = CustomerNumber;
                if(acclist.size() > 0){
                    i.Customer__c = acclist.get(0).id;
                }
                i.BillTo_Text__c = BillToId;
                if(billAPlist.size() > 0){
                    i.BillTo__c = billAPlist.get(0).id;
                }
                i.BillTo_Customer__c = reqObj.BillTo_Customer;
                i.BillTo_Address1__c = reqObj.BillTo_Address1;
                i.BillTo_Address2__c = reqObj.BillTo_Address2;
                i.BillTo_Address3__c = reqObj.BillTo_Address3;
                i.ShipTo_Text__c     = ShipToId;
                if(shipAPlist.size() > 0){
                    i.ShipTo__c = shipAPlist.get(0).id;
                }
                i.ShipTo_Customer__c = reqObj.ShipTo_Customer;
                i.ShipTo_Address1__c = reqObj.ShipTo_Address1;
                i.ShipTo_Address2__c = reqObj.ShipTo_Address2;
                i.ShipTo_Address3__c = reqObj.ShipTo_Address3;

                i.DropShip_CustomerNumber__c = reqObj.DropShip_CustomerNumber;
                i.DropShip_Name__c      = reqObj.DropShip_Name;
                i.DropShip_Address1__c = reqObj.DropShip_Address1;
                i.DropShip_Address2__c = reqObj.DropShip_Address2;
                i.DropShip_Address3__c = reqObj.DropShip_Address3;

                i.Tracking_NO__c = reqObj.Tracking_NO;
                i.Number_of_Shipping_Units__c = reqObj.Number_of_Shipping_Units;
                i.Delivery_Number__c = reqObj.Delivery_Number;
                if(shipmentList.size() > 0){
                    i.Shipment__c = shipmentList.get(0).Id;
                }
                i.Freight_Term__c   = reqObj.Freight_Term;
                i.Origin__c         = reqObj.Origin;
                i.Terr_Number__c    = reqObj.Terr_Number;
                i.Carrier__c        = reqObj.Carrier;
                i.PO_Number__c      = reqObj.PO_Number;

                // add by roger 2021-07-15
                // link reverse order to reverse order request
                List<String> creditInvoiceNumberList = new List<String>();
                List<Reverse_Order_Item__c> reverseItems = new List<Reverse_Order_Item__c>();
                Reverse_Order_Request__c reverseRequest;
                List<Reverse_Order_Charge_Credit_Account__c> reverseOrderCreditAccounts = new List<Reverse_Order_Charge_Credit_Account__c>();
                Map<Id, Reverse_Order_Item__c> reverseItemMap = new Map<Id, Reverse_Order_Item__c>();
                if (String.isNotEmpty(reqObj.Invoice_Number) && reqObj.Invoice_Number.startsWith('ROClaim')) {
                    List<String> autoNumber = reqObj.Invoice_Number.split('-');
                    String requestAutoNumber = autoNumber[0] + '-' + autoNumber[1];
                    List<Reverse_Order_Request__c> reverseOrderRequests = [SELECT Id,
                                                                                Reverse_Order_Type__c,
                                                                                Return_Goods_Status__c,
                                                                                CreatedBy.Profile.Name,
                                                                                Credit_Invoice_Number__c,
                                                                                CreatedBy.Email,
                                                                                Customer__r.Owner.Email,
                                                                                (SELECT Id,
                                                                                        Next_Step_Action__c,
                                                                                        Qty__c,
                                                                                        Invoice_Price__c,
                                                                                        Claimed_Credit_Memo_Amount__c,
                                                                                        Credit_Memo_Issued__c,
                                                                                        Issued_Credit_Memo_Amount__c,
                                                                                        Product2__c,
                                                                                        Product2__r.ProductCode,
                                                                                        Order_Product_Type__c
                                                                                        FROM Reverse_Order_Items__r),
                                                                                (SELECT Id,
                                                                                        Reverse_Order_Request__c,
                                                                                        Reverse_Order_Item__r.Name,
                                                                                        Amount__c,
                                                                                        Brand_Code__c,
                                                                                        Cost_Center__c,
                                                                                        Description__c,
                                                                                        GL_Code__c
                                                                                        FROM Reverse_Order_Charge_Credit_Accounts__r)
                                                                                FROM Reverse_Order_Request__c
                                                                                WHERE Auto_Number__c = :requestAutoNumber];

                    if (reverseOrderRequests.size() > 0) {
                        i.Reverse_Order_Request__c = reverseOrderRequests[0].Id;
                        reverseRequest = reverseOrderRequests[0];
                        reverseItems = reverseOrderRequests[0].Reverse_Order_Items__r;
                        reverseItemMap.putAll(reverseItems);
                        reverseOrderCreditAccounts = reverseOrderRequests[0].Reverse_Order_Charge_Credit_Accounts__r;
                        Set<String> creditInvoiceNumberSet = new Set<String>();
                        if (String.isNotBlank(reverseRequest.Credit_Invoice_Number__c)) {
                            creditInvoiceNumberSet.addAll(reverseRequest.Credit_Invoice_Number__c.split(','));
                        }
                        creditInvoiceNumberSet.add(i.Invoice_Number__c);
                        creditInvoiceNumberList.addAll(creditInvoiceNumberSet);
                        reverseRequest.Credit_Invoice_Number__c = String.join(creditInvoiceNumberList, ',');
                    }
                }

                if(String.isNotBlank(reqObj.Order_Date)){
                    i.Order_Date__c = Date.valueOf(reqObj.Order_Date);
                }
                i.Order_Commande__c = reqObj.Order_Commande;
                i.Order_OracleID__c = reqObj.ATTRIBUTE3;
                if(orderList.size() > 0){
                    i.Order__c = orderList.get(0).Id;
                    if (String.isNotEmpty(orderList.get(0).Reverse_Order_Request__c) && orderList.get(0).Reverse_Order_Request__r.Reverse_Order_Request_Number__c == reqObj.PO_Number) {
                        List<Reverse_Order_Request__c> reverseOrderRequests = [SELECT Id,
                                                                                Reverse_Order_Type__c,
                                                                                Return_Goods_Status__c,
                                                                                Credit_Invoice_Number__c,
                                                                                CreatedBy.Profile.Name,
                                                                                CreatedBy.Email,
                                                                                Customer__r.Owner.Email,
                                                                                (SELECT Id,
                                                                                        Next_Step_Action__c,
                                                                                        Qty__c,
                                                                                        Invoice_Price__c,
                                                                                        Claimed_Credit_Memo_Amount__c,
                                                                                        Credit_Memo_Issued__c,
                                                                                        Issued_Credit_Memo_Amount__c,
                                                                                        Product2__c,
                                                                                        Product2__r.ProductCode,
                                                                                        Order_Product_Type__c
                                                                                        FROM Reverse_Order_Items__r)
                                                                                FROM Reverse_Order_Request__c
                                                                                WHERE Id = :orderList.get(0).Reverse_Order_Request__c];

                        if (reverseOrderRequests.size() > 0) {
                            i.Reverse_Order_Request__c = reverseOrderRequests[0].Id;
                            reverseRequest = reverseOrderRequests[0];
                            reverseItems = reverseOrderRequests[0].Reverse_Order_Items__r;
                            Set<String> creditInvoiceNumberSet = new Set<String>();
                            if (String.isNotBlank(reverseRequest.Credit_Invoice_Number__c)) {
                                creditInvoiceNumberSet.addAll(reverseRequest.Credit_Invoice_Number__c.split(','));
                            }
                            creditInvoiceNumberSet.add(i.Invoice_Number__c);
                            creditInvoiceNumberList.addAll(creditInvoiceNumberSet);
                            reverseRequest.Credit_Invoice_Number__c = String.join(creditInvoiceNumberList, ',');
                        }
                    }
                }
                i.Credit_Authorization__c = reqObj.Credit_Authorization;
                i.Freight_and_other_charges__c = Decimal.valueOf(reqObj.Freight_and_other_charges);
                i.Handling_Fee__c   = Decimal.valueOf(reqObj.HANDLING_AMT);
                i.Total_Due__c      = Decimal.valueOf(reqObj.Total_Due);
                i.CurrencyIsoCode   = reqObj.CurrencyCode;
                i.Terms__c          = reqObj.Terms;
                i.Payment_Term_Code__c =  reqObj.ATTRIBUTE6;//Payment term code Added by Zoe for french portal on 2024-9-25
                i.Payment_Term1__c = '';
                i.Payment_Term2__c = '';
                if(reqObj.Payment_Term1 != null){
                    for(String term : reqObj.Payment_Term1.split(';')){
                        i.Payment_Term1__c += '\n'+term;
                    }
                }
                if(reqObj.Payment_Term2 != null){
                    for(String term : reqObj.Payment_Term2.split(';')){
                        i.Payment_Term2__c += '\n'+term;
                    }
                }
                i.Remit_To_Line1__c = reqObj.Remit_To_Line1;
                i.Remit_To_Line2__c = reqObj.Remit_To_Line2;
                i.Remit_To_Line3__c = reqObj.Remit_To_Line3;
                i.Remit_To_Line4__c = reqObj.Remit_To_Line4;
                i.Remit_To_Line5__c = reqObj.Remit_To_Line5;
                i.Invoice_Type__c   = reqObj.Invoice_Type;
                i.Invoice_Source__c = reqObj.Invoice_Source;

                //CA tax
                if(String.isNotBlank(reqObj.GST_AMT)){
                    i.GST__c = Decimal.valueOf(reqObj.GST_AMT);
                }
                if(String.isNotBlank(reqObj.HST_AMT)){
                    i.HST__c = Decimal.valueOf(reqObj.HST_AMT);
                }
                if(String.isNotBlank(reqObj.QST_AMT)){
                    i.QST__c = Decimal.valueOf(reqObj.QST_AMT);
                }
                if(String.isNotBlank(reqObj.ATTRIBUTE5)) {
                    i.PST__c = Decimal.valueOf(reqObj.ATTRIBUTE5);
                }

                //Surcharge Amount
                if(String.isNotBlank(reqObj.ATTRIBUTE2)){
                    i.Surcharge_Amount__c = Decimal.valueOf(reqObj.ATTRIBUTE2);
                }

                //Org Code
                i.ORG_Code__c = reqObj.ATTRIBUTE1;

                if (warrantyReturn.size() > 0) {
                    i.Warranty_Return_Request__c = warrantyReturn[0].Id;
                }

                insert i;
                invoiceInsertOrUpdate = i;

                //Add by John Jiang 2020-07-21
                if(i.Invoice_Number__c.contains('Pack')){
                    List<Claim_Pack__c> cpList = [SELECT id,Invoice__c FROM Claim_Pack__c WHERE Name =: i.Invoice_Number__c];
                    if(cpList.size() > 0){
                        cpList[0].Invoice__c = i.Id;

                        List<Warranty_Claim__c> wcList = [SELECT Id,Payment_Status__c FROM Warranty_Claim__c WHERE Claim_Pack__c =: cpList[0].Id];
                        for(Warranty_Claim__c wc : wcList){
                            wc.Payment_Status__c = 'Credited';
                        }
                        update cpList[0];
                        update wcList;
                    }
                }else if(i.Invoice_Number__c.contains('PromoClaim-')){ // populate Promotion_Claim_Request__c on invoice - updated on 2021-05-05
                    List<Claim_Request__c> claimRequestList = [SELECT Claim_Status__c FROM Claim_Request__c WHERE Name =: i.Invoice_Number__c];
                    if (claimRequestList.size() > 0) {
                        i.Promotion_Claim_Request__c = claimRequestList[0].id;
                        update i;
                    }
                    for (Claim_Request__c cr : claimRequestList) {
                        cr.Claim_Status__c = 'Issued';
                    }
                    update claimRequestList;
                } else if (i.Invoice_Number__c.startsWith('COC-')) { //populate Co_Op_Claim__c on invoice - update on 2021-6-25
                    List<String> subStrList = i.Invoice_Number__c.split('-');
                    String claimName = subStrList[0] + '-' + subStrList[1];
                    List<Co_Op_Claim__c> coOpClaimList = [SELECT Claim_Status__c,RecordType.DeveloperName FROM Co_Op_Claim__c WHERE Name =: claimName];
                    if (coOpClaimList.size() > 0) {
                        i.Co_Op_Claim__c = coOpClaimList[0].Id;
                        update i;
                    }
                    for (Co_Op_Claim__c coOpClaim : coOpClaimList) {
                        coOpClaim.Claim_Status__c = 'Issued';
                        if (coOpClaim.RecordType.DeveloperName.equals(CCM_CoOpUtil.CLAIM_RECORD_TYPE_DEDUCTION)) {
                            coOpClaim.Claim_Status__c = 'Closed';
                        }
                    }
                    update coOpClaimList;
                }else if(i.Invoice_Number__c.contains('WR-')){
                    List<Claim_Pack__c> cpList = [SELECT id,Invoice__c FROM Claim_Pack__c WHERE Warranty_Pack_Name__c =: i.Invoice_Number__c];
                    if(cpList.size() > 0){
                        cpList[0].Invoice__c = i.Id;
                        update cpList[0];
                    }
                }

                Decimal reverseCreditAmount = 0;
                Decimal reverseIssuedCreditAmount = 0;
                Map<String, Decimal> productAmountMap = new Map<String, Decimal>();
                Boolean isReship = false;
                Boolean isReplacement = false;
                Map<String,String> invoiceItemIdMapToNo = new Map<String,String>();
                Set<String> descriptionSet = new Set<String>();
                Map<String, List<SerialNumberLine>> snLineMap = new Map<String, List<SerialNumberLine>>();
                if(reqObj.InvoiceLine != null && reqObj.InvoiceLine.size() > 0){

                    for(InvoiceLine il : reqObj.InvoiceLine){
                        Invoice_Item__c ii = new Invoice_Item__c();
                        ii.CurrencyIsoCode = i.CurrencyIsoCode;
                        ii.Invoice_Item_OracleID__c = il.Invoice_Item_OracleID;
                        ii.Qty_Extended__c = il.Qty_Extended;
                        ii.Catalog_Item_Text__c = il.Catalog_Item;
                        if(String.isBlank(il.Catalog_Item) && isAutoInvoice && (il.Line_Attribute1 == 'FG' || il.Line_Attribute1 == 'MKT')){
                            String errorMsg = Label.CCM_ErrorMsg_LackOfProductInfo;
                            throw new InvoiceException(errorMsg.replace('{0}', 'Invoice').replace('{1}', il.Invoice_Item_OracleID));
                        }
                        if(il.Line_Attribute1 != 'SP'){
                            if(proMap.get(il.Catalog_Item) != null){
                                ii.Catalog_Item__c  = proMap.get(il.Catalog_Item);
                            }else if(isAutoInvoice && (il.Line_Attribute1 == 'FG' || il.Line_Attribute1 == 'MKT')){
                                String errorMsg = Label.CCM_ErrorMsg_NotFindProduct;
                                throw new InvoiceException(errorMsg.replace('{0}', 'Invoice').replace('{1}', il.Invoice_Item_OracleID).replace('{2}', il.Catalog_Item));
                            }
                        }else{
                            if(partsMap.get(il.Catalog_Item) != null){
                                ii.Catalog_Item__c  = partsMap.get(il.Catalog_Item);
                            }else {
                                String errorMsg = Label.CCM_ErrorMsg_NotFindProduct;
                                throw new InvoiceException(errorMsg.replace('{0}', 'Invoice').replace('{1}', il.Invoice_Item_OracleID).replace('{2}', il.Catalog_Item));
                            }
                        }
                        ii.Customer_Item__c = il.Customer_Item;
                        ii.PO_Line__c       = il.PO_Line;
                        ii.Description__c   = il.Description;
                        descriptionSet.add(il.Description);

                        if(il.Price != null){
                            ii.Price__c = Decimal.valueOf(il.Price);
                        }
                        ii.Amount__c = 0;
                        if(il.Amount != null){
                            ii.Amount__c = Decimal.valueOf(il.Amount);
                        }
                        ii.Item_Type__c         = il.Line_Attribute1;
                        ii.Invoice__c           = i.Id;
                        ii.ORG_Code__c          = i.ORG_Code__c;
                        // fillInInfoFromShippingAddress(ii, warrantyClaimMap, shippingAddressMap);

                        populateInvoiceDateOfOrderItem(i,ii,il,orderItemMap,updateOrderItemList);

                        if(String.isNotBlank(il.Line_Attribute3)){
                            ii.Surcharge_Amount__c = Decimal.valueOf(il.Line_Attribute3);
                        }

                        iilist.add(ii);

                        if(!il.SerialNumberLine.isEmpty()) {
                            snLineMap.put(il.Invoice_Item_OracleID, il.SerialNumberLine);
                        }
                    }
                }
                if(iilist.size() > 0){
                    insert iilist;

                    Map<String, String> invoiceItemMap = new Map<String, String>();
                    for(Invoice_Item__c ii : iilist) {
                        invoiceItemMap.put(ii.Invoice_Item_OracleID__c, ii.Id);
                    }

                    upsertSerialNumberLine(invoiceItemMap, snLineMap);

                }
                // 更新去重的数据
                updateNoneDuplicateOis(updateOrderItemList);
                if (orderList.size() > 0 && String.isNotBlank(reqObj.PO_Number) && reqObj.PO_Number.startsWith('RO') && String.isNotEmpty(orderList.get(0).Reverse_Order_Request__c)) {
                    List<Invoice_Item__c> tempItems = [SELECT Id, Amount__c, Catalog_Item__r.ProductCode FROM Invoice_Item__c WHERE Invoice__r.Invoice_Number__c IN :creditInvoiceNumberList];
                    for (Reverse_Order_Item__c item : reverseItems) {
                        item.Credit_Memo_Issued__c = 0;
                        for(Invoice_Item__c il : tempItems) {
                            if (item.Product2__r.ProductCode == il.Catalog_Item__r.ProductCode) {
                                item.Credit_Memo_Issued__c  += il.Amount__c.abs();
                                reverseIssuedCreditAmount   += il.Amount__c.abs();

                                if(!productAmountMap.containsKey(il.Catalog_Item__r.ProductCode)) {
                                    productAmountMap.put(il.Catalog_Item__r.ProductCode, 0);
                                }
                                Decimal tempAmount = productAmountMap.get(il.Catalog_Item__r.ProductCode) + il.Amount__c.abs();
                                productAmountMap.put(il.Catalog_Item__r.ProductCode, tempAmount);
                            }
                            if (item.Product2__r.ProductCode == il.Catalog_Item__r.ProductCode) {
                                if (item.Next_Step_Action__c == 'I want Chervon to reship the shortage product') {
                                    isReship = true;
                                } else if (item.Next_Step_Action__c == 'I want to return damaged goods and get replacement') {
                                    isReplacement = true;
                                }
                            }
                        }
                    }
                } else if (reqObj.Invoice_Number.startsWith('ROClaim')) {
                    for (Invoice_Item__c item : [SELECT Id, Amount__c, Catalog_Item__r.ProductCode FROM Invoice_Item__c WHERE Invoice__r.Invoice_Number__c IN :creditInvoiceNumberList]) {
                        reverseIssuedCreditAmount += item.Amount__c.abs();
                        if(!productAmountMap.containsKey(item.Catalog_Item__r.ProductCode)) {
                            productAmountMap.put(item.Catalog_Item__r.ProductCode, 0);
                        }
                        Decimal tempAmount = productAmountMap.get(item.Catalog_Item__r.ProductCode) + item.Amount__c.abs();
                        productAmountMap.put(item.Catalog_Item__r.ProductCode, tempAmount);
                    }

                    for (Reverse_Order_Charge_Credit_Account__c ca : reverseOrderCreditAccounts) {
                        if (reverseItemMap.get(ca.Reverse_Order_Item__c).Next_Step_Action__c == 'I want Chervon to reship the shortage product') {
                            isReship = true;
                        } else if (reverseItemMap.get(ca.Reverse_Order_Item__c).Next_Step_Action__c == 'I want to return damaged goods and get replacement') {
                            isReplacement = true;
                        }
                    }
                }
                if ((orderList.size() > 0 && String.isNotEmpty(orderList.get(0).Reverse_Order_Request__c) && String.isNotBlank(reqObj.PO_Number) && reqObj.PO_Number.startsWith('RO')) ||
                    (String.isNotEmpty(reqObj.Invoice_Number) && reqObj.Invoice_Number.startsWith('ROClaim'))) {
                    Map<String, Decimal> productShortageAmountMap = new Map<String, Decimal>();
                    for (Reverse_Order_Item__c item : reverseItems) {
                        reverseCreditAmount += item.Invoice_Price__c * item.Qty__c;
                        if(item.Order_Product_Type__c == 'Shortage') {
                            if(!productShortageAmountMap.containsKey(item.Product2__r.ProductCode)) {
                                productShortageAmountMap.put(item.Product2__r.ProductCode, 0);
                            }

                            Decimal tempAmount = productShortageAmountMap.get(item.Product2__r.ProductCode) + item.Invoice_Price__c * item.Qty__c;
                            productShortageAmountMap.put(item.Product2__r.ProductCode, tempAmount);
                        }
                    }
                    // For wrong product, only invoice of shortage will be received, no invoice of overage will be received, so only compare the amount of shortage.
                    if(reverseRequest.Reverse_Order_Type__c == 'Wrong Product') {
                        Boolean amountFullfill = true;
                        for(String productCode : productShortageAmountMap.keySet()) {
                            Decimal roiAmount = productShortageAmountMap.get(productCode);
                            if(productAmountMap.containsKey(productCode)) {
                                Decimal invoiceAmount = productAmountMap.get(productCode);
                                if(invoiceAmount < roiAmount) {
                                    amountFullfill = false;
                                    break;
                                }
                            }
                            else {
                                amountFullfill = false;
                            }
                        }
                        if(amountFullfill) {
                            reverseRequest.Credit_Memo_Status__c = 'Issued';
                        }
                    }
                    else {
                        if (reverseIssuedCreditAmount < reverseCreditAmount) {
                            reverseRequest.Credit_Memo_Status__c = 'Partial Issued';
                            if (reverseRequest.Return_Goods_Status__c == 'Received') {
                                reverseRequest.Credit_Memo_Status__c = 'Issued';
                                if(!isACE) {
                                    reverseRequest.Reverse_Order_Request_Status__c = 'Completed';
                                }
                            }
                            update reverseRequest;
                        }
                        if (reverseIssuedCreditAmount == reverseCreditAmount) {
                            reverseRequest.Credit_Memo_Status__c = 'Issued';
                            if(!isACE) {
                                reverseRequest.Reverse_Order_Request_Status__c = 'Completed';
                            }
                            update reverseRequest;
                        }
                        if(isHaveOrder){
                            if(reverseIssuedCreditAmount < orderTotalPrice){
                                reverseRequest.Credit_Memo_Status__c = 'Partial Issued';
                                if (reverseRequest.Return_Goods_Status__c == 'Received') {
                                    reverseRequest.Credit_Memo_Status__c = 'Issued';
                                    if(!isACE) {
                                        reverseRequest.Reverse_Order_Request_Status__c = 'Completed';
                                    }
                                }
                                update reverseRequest;
                            }
                            if(reverseIssuedCreditAmount == orderTotalPrice){
                                reverseRequest.Credit_Memo_Status__c = 'Issued';
                                if(!isACE) {
                                    reverseRequest.Reverse_Order_Request_Status__c = 'Completed';
                                }
                                update reverseRequest;
                            }
                        }
                    }
                    if (reverseIssuedCreditAmount == 0) {
                        reverseRequest.Credit_Memo_Status__c = 'Not Issued';
                        update reverseRequest;
                    }
                    if (String.isNotBlank(reqObj.PO_Number) && reqObj.PO_Number.startsWith('RO') && String.isNotEmpty(orderList.get(0).Reverse_Order_Request__c)) {
                        update reverseItems;
                    }
                    if (reqObj.Invoice_Number.startsWith('ROClaim')) {
                        update reverseItemMap.values();
                    }

                    if (reverseRequest.Reverse_Order_Type__c != 'Overage') {
                        // send email to na finance
                        if(!isACE) {
                            List<User> financeUsers = [SELECT Id, Email FROM User WHERE Profile.Name LIKE '%Finance%' AND IsActive = true];
                            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                            List<String> emailAddress = new List<String>();
                            for (User fin : financeUsers) {
                                emailAddress.add(fin.Email);
                            }
                            email.toaddresses = emailAddress;
                            email.subject = 'New Invoice';
                            String invoiceURL = URL.getSalesforceBaseUrl().toExternalForm() + '/' + i.Id;
                            String reverseURL = URL.getSalesforceBaseUrl().toExternalForm() + '/' + reverseRequest.Id;
                            String emailBody = 'Credit Memo for this reverse order request is issued. You can click the links to see more detail. <br/>' +
                                                'Reverse Order Request Link: <a href="' + reverseURL + '">' + reverseURL + '</a>' +
                                                'Credit Memo Invoice Link: <a href="' + invoiceURL + '">' + invoiceURL + '</a>';
                            email.htmlbody = emailBody;
                            Messaging.SingleEmailMessage[] emails = new Messaging.SingleEmailMessage[]{email};
                            Messaging.SendEmailResult[] results = Messaging.sendEmail(emails);
                        }
                    }

                    if (isReplacement || isReship) {
                        // send email to inside sales
                        Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                        List<String> emailAddress = new List<String>();
                        if (reverseRequest.CreatedBy.Profile.Name.contains('Inside Sales')) {
                            emailAddress.add(reverseRequest.CreatedBy.Email);
                        } else {
                            emailAddress.add(reverseRequest.Customer__r.Owner.Email);
                        }
                        if (orgCode == CCM_Constants.ORG_CODE_CCA) {
                            emailAddress = new List<String>();
                            Set<String> caSalesProfiles = new Set<String>{'BEAM'};
                            List<String> canadaRole = Label.CABEAMRole.split(';');
                            for (User insideSales : [SELECT Id, Email FROM User WHERE Profile.Name IN :caSalesProfiles AND UserRole.DeveloperName IN :canadaRole AND IsActive = true]) {
                                emailAddress.add(insideSales.Email);
                            }
                            emailAddress.add(Label.GeoffBobykEmail);
                            emailAddress.add(Label.JenniferBilaEmail);
                            emailAddress.add(Label.DanaEilotEmail);
                            emailAddress.add(reverseRequest.Customer__r.Owner.Email);
                        }
                        email.toaddresses = emailAddress;
                        email.subject = 'Return Order';
                        String invoiceURL = URL.getSalesforceBaseUrl().toExternalForm() + '/' + i.Id;
                        String reverseURL = URL.getSalesforceBaseUrl().toExternalForm() + '/' + reverseRequest.Id;
                        String emailBody = 'Return Order for this reverse order request is reshipped/replacement. You can place a new order with this return order. <br/>' +
                                            'Reverse Order Request Link: <a href="' + reverseURL + '">' + reverseURL + '</a>' +
                                            'Credit Memo Invoice Link: <a href="' + invoiceURL + '">' + invoiceURL + '</a>';
                        email.htmlbody = emailBody;
                        Messaging.SingleEmailMessage[] emails = new Messaging.SingleEmailMessage[]{email};
                        Messaging.SendEmailResult[] results = Messaging.sendEmail(emails);
                    }
                }

                Set<String> servicePartnerSet = new Set<String>();
                Map<String, List<Warranty_Claim__c>> warrantyClaimMap = new Map<String, List<Warranty_Claim__c>>();
                Map<String, List<Account_Address__c>> shippingAddressMap = new Map<String, List<Account_Address__c>>();
                for (Warranty_Claim__c claim : [SELECT Name, Invoice_Item__c, Payment_Status__c, Service_Partner__c
                                                    FROM Warranty_Claim__c
                                                    WHERE Name IN :descriptionSet]) {
                    if (!warrantyClaimMap.containsKey(claim.Name)) {
                        warrantyClaimMap.put(claim.Name, new List<Warranty_Claim__c>());
                    }
                    warrantyClaimMap.get(claim.name).add(claim);
                    servicePartnerSet.add(claim.Service_Partner__c);
                }

                if (servicePartnerSet.size() > 0) {
                    List<Account_Address__c> addressList = [SELECT Store_Number__c, Country__c, State__c, City__c,Address1__c, Postal_Code__c, X2nd_Tier_Dealer__c
                                                                FROM Account_Address__c
                                                                WHERE X2nd_Tier_Dealer__c IN :servicePartnerSet
                                                                AND RecordType.DeveloperName IN ('Shipping_Address', 'Dropship_Shipping_Address')];
                    if (addressList.size() > 0) {
                        for (Account_Address__c address : addressList) {
                            if (!shippingAddressMap.containsKey(address.X2nd_Tier_Dealer__c)) {
                                shippingAddressMap.put(address.X2nd_Tier_Dealer__c, new List<Account_Address__c>());
                            }
                            shippingAddressMap.get(address.X2nd_Tier_Dealer__c).add(address);
                        }
                    }
                }
                List<Warranty_Claim__c> updateWClaim = new List<Warranty_Claim__c>();
                List<Fleet_Claim__c> updateFClaim = new List<Fleet_Claim__c>();
                for(Invoice_Item__c ii : iilist){
                    if (String.isNotEmpty(ii.Description__c)) {
                        if( ii.Description__c.contains('FleetClaim-')){
                            String fleetClaimName = ii.Description__c.replace('FleetClaim-', 'FClaim-');
                            List<Fleet_Claim__c> fcList = [SELECT Id,Invoice_Item__c FROM Fleet_Claim__c WHERE Name =: fleetClaimName];
                            if(fcList.size() > 0){
                                fcList[0].Invoice_Item__c = ii.Id;
                                updateFClaim.add(fcList[0]);
                            }
                        }
                        else if(ii.Description__c.contains('Claim-')){
                            List<Warranty_Claim__c> wcList = fillInInfoFromShippingAddress(ii, warrantyClaimMap, shippingAddressMap);
                            if(wcList!= null && wcList.size() > 0){
                                wcList[0].Invoice_Item__c = ii.Id;
                                wcList[0].Payment_Status__c = 'Credited';
                                updateWClaim.add(wcList[0]);
                            }
                        }
                    }
                }
                if (!iilist.isEmpty()) {
                    update iilist;
                }
                if(updateWClaim.size() > 0){
                    update updateWClaim;
                }
                if(updateFClaim.size() > 0){
                    update updateFClaim;
                }
            }

            if(isACE && String.isNotBlank(invoiceInsertOrUpdate.Reverse_Order_Request__c)) {
                generateTaskForACERONotification(invoiceInsertOrUpdate.Id);
            }
            CCM_CA_WarrantyClaim_TaxChecker.checkTaxMatch(invoiceInsertOrUpdate);

            if (warrantyReturn.size() > 0) {
                updateWarrantyReturn(reqObj.PO_Number);
            }
            iiData.Return_Code__c = 'S';
            update iiData;
        }catch (InvoiceException e){
            iiData.Return_Code__c = 'E';
            iiData.Error_Msg__c = e.getMessage() + '\n' + e.getStackTraceString();
            update iiData;
        }catch (Exception e) {
            iiData.Return_Code__c = 'F';
            iiData.Error_Msg__c = e.getMessage() + '\n' + e.getStackTraceString();
            if(iiData.Error_Msg__c.toUpperCase().contains('ROW LOCK') || iiData.Error_Msg__c.toUpperCase().contains('UNABLE_TO_LOCK_ROW')) {
                return;
            }
            update iiData;
            String logId = Util.logIntegration(
                'Invoice Exception','CCM_RestService_DealInvoice',
                'POST',iiData.Error_Msg__c,JSON.serialize(reqObj),
                 JSON.serialize(iiData)
            );
            Util.pushExceptionEmail('Accept Invoice Info',logId,iiData.Error_Msg__c);
        }
    }

    private static Boolean checkOrderNeedExist(InvoiceInterface__c iiData) {
        Boolean orderExist = true;
        if(iiData.Invoice_Type__c == 'CNA_CreditMemo' || iiData.Invoice_Type__c == 'CNA_Invoice' || iiData.Invoice_Type__c == 'CA_CreditMemo'
        || iiData.Invoice_Type__c == 'CNA_Export_Invoice' || iiData.Invoice_Type__c == 'CA_Invoice') {
            if(iiData.Customer__c != '9997' && String.isNotBlank(iiData.PO_Number__c)) {
                List<Order> orders = [SELECT Id FROM Order WHERE Order_OracleID__c = :iiData.ATTRIBUTE3__c];
                if(orders.isEmpty()) {
                    orderExist = false;
                }
            }
        }
        return orderExist;
    }


    /**
     * @description This method is used to retrieve the Store Number and Shipping Address.
     */
    private static List<Warranty_Claim__c> fillInInfoFromShippingAddress(Invoice_Item__c objInvoiceItem, Map<String, List<Warranty_Claim__c>> warrantyClaimMap, Map<String, List<Account_Address__c>> shippingAddressMap) {
        List<String> lstShipTo = new List<String>();
        List<Warranty_Claim__c> lstWarrantyClaim;
        List<Account_Address__c> lstShippingAddress;
        if (objInvoiceItem == null || String.isBlank(objInvoiceItem.Description__c) || !objInvoiceItem.Description__c.contains('Claim-')) return null;
        lstWarrantyClaim = warrantyClaimMap.get(objInvoiceItem.Description__c);
        if (lstWarrantyClaim != null && !lstWarrantyClaim.isEmpty() && String.isNotBlank(lstWarrantyClaim[0].Service_Partner__c)) {
            lstShippingAddress = shippingAddressMap.get(lstWarrantyClaim[0].Service_Partner__c);
            if (lstShippingAddress != null && !lstShippingAddress.isEmpty()) {
                lstShipTo.add(lstShippingAddress[0].Address1__c);
                lstShipTo.add(lstShippingAddress[0].City__c);
                lstShipTo.add(lstShippingAddress[0].State__c);
                lstShipTo.add(lstShippingAddress[0].Country__c);
                lstShipTo.add(lstShippingAddress[0].Postal_Code__c);
                objInvoiceItem.Store_Number__c = lstShippingAddress[0].Store_Number__c;
                objInvoiceItem.Ship_To__c = String.join(lstShipTo, ', ');
            }
        }
        return lstWarrantyClaim;
    }

    private static void upsertSerialNumberLine(Map<String, String> invoiceItemMap, Map<String, List<SerialNumberLine>> snLineMap) {
        List<Serial_Number_Item__c> snList = new List<Serial_Number_Item__c>();
        for(String itemOracleId : invoiceItemMap.keySet()) {
            if(snLineMap.containsKey(itemOracleId)) {
                for(SerialNumberLine snLine : snLineMap.get(itemOracleId)) {
                    Serial_Number_Item__c sn = new Serial_Number_Item__c();
                    sn.Serial_id__c = snLine.Serial_id;
                    sn.Serial_Number__c = snLine.Serial_Number;
                    sn.Attribute1__c = snLine.Attribute1;
                    sn.Attribute2__c = snLine.Attribute2;
                    sn.Attribute3__c = snLine.Attribute3;
                    sn.Attribute4__c = snLine.Attribute4;
                    sn.Attribute5__c = snLine.Attribute5;
                    sn.Attribute6__c = snLine.Attribute6;
                    sn.Attribute7__c = snLine.Attribute7;
                    sn.Attribute8__c = snLine.Attribute8;
                    sn.Attribute9__c = snLine.Attribute9;
                    sn.Attribute10__c = snLine.Attribute10;
                    sn.Attribute11__c = snLine.Attribute11;
                    sn.Attribute12__c = snLine.Attribute12;
                    sn.Attribute13__c = snLine.Attribute13;
                    sn.Attribute14__c = snLine.Attribute14;
                    sn.Attribute15__c = snLine.Attribute15;
                    sn.Invoice_Item__c = invoiceItemMap.get(itemOracleId);
                    snList.add(sn);
                }
            }
        }
        upsert snList Serial_Number__c;

    }

       /**
     * @description This method is used to retrieve the warranty pack's Store Number and Shipping Address.
     */
    private static Warranty__c fillWarrantyInfoFromShippingAddress(Invoice_Item__c objInvoiceItem, Map<String, Warranty__c> RegistrationWarrantyMap, Map<String, List<Account_Address__c>> shippingAddressMap) {
        List<String> lstShipTo = new List<String>();
        Warranty__c lstWarranty;
        List<Account_Address__c> lstShippingAddress;
        if (objInvoiceItem == null || String.isBlank(objInvoiceItem.Description__c) || !objInvoiceItem.Description__c.contains('W-')) return null;
        lstWarranty = RegistrationWarrantyMap.get(objInvoiceItem.Description__c);
        if (lstWarranty != null && lstWarranty.Claim_Pack__c != null && String.isNotBlank(lstWarranty.Claim_Pack__r.Second_Tier_Customer__c)) {
            lstShippingAddress = shippingAddressMap.get(lstWarranty.Claim_Pack__r.Second_Tier_Customer__c);
            if (lstShippingAddress != null && !lstShippingAddress.isEmpty()) {
                lstShipTo.add(lstShippingAddress[0].Address1__c);
                lstShipTo.add(lstShippingAddress[0].City__c);
                lstShipTo.add(lstShippingAddress[0].State__c);
                lstShipTo.add(lstShippingAddress[0].Country__c);
                lstShipTo.add(lstShippingAddress[0].Postal_Code__c);
                objInvoiceItem.Store_Number__c = lstShippingAddress[0].Store_Number__c;
                objInvoiceItem.Ship_To__c = String.join(lstShipTo, ', ');
            }
        }
        return lstWarranty;
    }

    global class InvoiceLine {
        global String Description;
        global String PO_Line;
        global String Invoice_Item_OracleID;
        global String Amount;
        global String Customer_Item;
        global String Price;
        global String Catalog_Item;
        global String Qty_Extended;
        global String Line_Attribute1;
        global String Line_Attribute2;
        global String Line_Attribute3;
        global String Line_Attribute4;
        global String Line_Attribute5;
        global String Line_Attribute6;
        global String Line_Attribute7;
        global String Line_Attribute8;
        global String Line_Attribute9;
        global String Line_Attribute10;
        global List<SerialNumberLine> SerialNumberLine;

        global String InnerExternalId;
    }

    global class SerialNumberLine {
        global String Serial_id;
        global String Serial_Number;
        global String Attribute1;
        global String Attribute2;
        global String Attribute3;
        global String Attribute4;
        global String Attribute5;
        global String Attribute6;
        global String Attribute7;
        global String Attribute8;
        global String Attribute9;
        global String Attribute10;
        global String Attribute11;
        global String Attribute12;
        global String Attribute13;
        global String Attribute14;
        global String Attribute15;
    }

    global class ReqestObj {
        global String Terr_Number;
        global String Remit_To_Line3;
        global String Order_Date;
        global String Number_of_Shipping_Units;
        global String Remit_To_Line1;
        global String Customer;
        global String Total_Due;
        global String Freight_and_other_charges;
        global String HANDLING_AMT;
        global String Origin;
        global String Carrier;
        global String BillTo;
        global String Terms;
        global String Order_Commande;
        global String Payment_Term1;
        global String Payment_Term2;
        global String CurrencyCode;
        global String Remit_To_Line4;
        global String Delivery_Number;
        global String Invoice_Date;
        global String Invoice_Number;
        global String Remit_To_Line2;
        global String Credit_Authorization;
        global String Remit_To_Line5;
        global String Freight_Term;
        global String Chervon_Name;
        global String Chervon_Address1;
        global String Chervon_Address2;
        global String Tracking_NO;
        global List<InvoiceLine> InvoiceLine;
        global String PO_Number;
        global String ShipTo;
        global String Invoice_OracleID;

        global String BillTo_Customer;
        global String BillTo_Address1;
        global String BillTo_Address2;
        global String BillTo_Address3;

        global String ShipTo_Customer;
        global String ShipTo_Address1;
        global String ShipTo_Address2;
        global String ShipTo_Address3;

        global String DropShip_CustomerNumber;
        global String DropShip_Name;
        global String DropShip_Address1;
        global String DropShip_Address2;
        global String DropShip_Address3;
        global String Invoice_Type;
        global String Invoice_Source;

        global String BILL_TO_SITE_USE_ID;
        global String SHIP_TO_SITE_USE_ID;
        global String ATTRIBUTE1;
        global String ATTRIBUTE2;
        global String ATTRIBUTE3;
        global String ATTRIBUTE4;
        global String ATTRIBUTE5;
        global String ATTRIBUTE6;
        global String ATTRIBUTE7;
        global String ATTRIBUTE8;
        global String ATTRIBUTE9;
        global String ATTRIBUTE10;

        global string GST_AMT;
        global string HST_AMT;
        global string QST_AMT;

        global String InnerExternalId;

    }
    global static List<ReqestObj> parse(String jsonStr) {
        return (List<ReqestObj>) JSON.deserialize(jsonStr, List<ReqestObj>.class);
    }

    global class ResultObj{
        global String oracelId;
        global String returnCode;
        global String returnMsg;
    }

    //Invoice Exception
    private class InvoiceException extends Exception {
    }

    /**
     * @description This method is used to populate invoice date of order item.
     */
    private static void populateInvoiceDateOfOrderItem(Invoice__c i, Invoice_Item__c ii, InvoiceLine il, Map<String, Order_Item__c> orderItemMap, List<Order_Item__c> updateOrderItemList){
        ii.Order_Line_Oracle_ID__c = il.Line_Attribute2;
        Order_Item__c oItem = orderItemMap.get(il.Line_Attribute2);
        if(String.isNotBlank(il.Line_Attribute2) && oItem != null){
            ii.Order_Item__c = oItem.Id;
            oItem.Invoice_Date__c = i.Invoice_Date__c;
            oItem.Invoice_Item_OracleID__c = il.Invoice_Item_OracleID;
            updateOrderItemList.add(oItem);
        }
    }

   /**
     * @description updateWarrantyReturn description
     * 1. Request 全是DIF
     *   - Total Credit Memo Amount Issued = Total Claim Amount
     *   - Credit memo status 显示 ’Issued‘
     * 2. Request 中有RTV
     *   - Total Credit Memo Amount Issued = 目前 warehouse received total quantity * Invoice price + （DIF 的 subtotal）if any
     *   - Credit memo status 显示:
     *   -- 'Not Issued', if warehouse received total quantity =0
     *   -- ’Partially Issued', if 0< warehouse received total quantity<应收的RTV数量
     *   -- 'Issued', if warehouse received total quantity = 应收的RTV数量
     * @param  warrantyRequest warrantyRequest description
     */
    public static void updateWarrantyReturn(String warrantyRequest) {

        List<Warranty_Return_Claim__c> warrantyReturn = [SELECT Id, Customer__r.AccountNumber, Customer_Account_Number__c, Chervon_or_Warehouse_Purchase__c, Warranty_Return_Request_No__c, Payment_Method__c, Contact_Email_Addresses__c, CreatedBy.Email, CreatedBy.Name, Debit_Memo_Number__c, Credit_Memo_Number__c, Debit_Memo_Number__r.Invoice_Number__c,
                                                                (SELECT Id, Model__r.ProductCode, Subtotal__c, Credit_Memo_Amount__c, DIF_RTV__c FROM Warranty_Return_Claim_Item__r)
                                                            FROM Warranty_Return_Claim__c
                                                            WHERE Name = :warrantyRequest];

        if (warrantyReturn.size() == 0) {
            return;
        }
        List<AggregateResult> itemList = [SELECT SUM(Amount__c) amount, Catalog_Item__r.ProductCode productCode
                                            FROM Invoice_Item__c
                                            WHERE Invoice__r.PO_Number__c = :warrantyRequest
                                            GROUP BY Catalog_Item__r.ProductCode];
        Map<String, Decimal> productAmountMap = new Map<String, Decimal>();
        for (AggregateResult item : itemList) {
            productAmountMap.put(
                String.valueOf(item.get('productCode')), Math.abs( Decimal.valueOf(String.valueOf(item.get('amount'))) )
            );
        }
        String creditMemoStatus = 'N/A';
        Decimal totalAmount = 0;
        Decimal totalCreditMemoAmount = 0;
        Boolean allDIF = true;
        List<Warranty_Return_Claim_Item__c> needUpdateList = new List<Warranty_Return_Claim_Item__c>();
        for (Warranty_Return_Claim_Item__c item : warrantyReturn[0].Warranty_Return_Claim_Item__r) {
            if (item.Credit_Memo_Amount__c != productAmountMap.get(item.Model__r.ProductCode)) {
                item.Credit_Memo_Amount__c = productAmountMap.get(item.Model__r.ProductCode);
                needUpdateList.add(item);
            }
            totalAmount += item.Subtotal__c;
            totalCreditMemoAmount += item.Credit_Memo_Amount__c == null ? 0 : item.Credit_Memo_Amount__c;
            if (item.DIF_RTV__c == 'RTV') {
                allDIF = false;
            }
        }
        if (!allDIF) {
            if (Math.abs(totalCreditMemoAmount) == 0) {
                creditMemoStatus = 'Not Issued';
            } else if (totalAmount > Math.abs(totalCreditMemoAmount)) {
                creditMemoStatus = 'Partial Issued';
            } else if (totalAmount <= Math.abs(totalCreditMemoAmount)) {
                creditMemoStatus = 'Issued';
            }
        } else {
            creditMemoStatus = 'Issued';
        }

        if (needUpdateList.size() > 0) {
            update needUpdateList;
        }

        List<String> invoiceOracleIds = new List<String>();
        List<Invoice__c> invList = [SELECT Id, Invoice_Number__c FROM Invoice__c WHERE PO_Number__c = :warrantyRequest];
        for (Invoice__c objOrder : invList) {
            invoiceOracleIds.add(objOrder.Invoice_Number__c);
        }

        Warranty_Return_Claim__c request = new Warranty_Return_Claim__c();
        request.Id = warrantyReturn[0].Id;
        request.Credit_Memo_Number__c = String.join(invoiceOracleIds, ', ');
        request.Credit_Memo_Status__c = creditMemoStatus;
        update request;

        // send email
        Set<String> creditMemoNumberSet = new Set<String>();
        List<String> creditMemoNumberList = new List<String>();
        if (warrantyReturn[0].Credit_Memo_Number__c != null) {
            creditMemoNumberSet.addAll(warrantyReturn[0].Credit_Memo_Number__c.split(','));
        }
        if(request.Credit_Memo_Number__c != null) {
            creditMemoNumberSet.addAll(request.Credit_Memo_Number__c.split(','));
        }
        creditMemoNumberList.addAll(creditMemoNumberSet);

        List<Invoice__c> invoiceList = [SELECT Id, Invoice_Number__c FROM Invoice__c WHERE Invoice_Number__c IN :creditMemoNumberList]; // deduction关联的credit memo.

        try {
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            List<Messaging.SingleEmailMessage> warehouseOrderMessages = new List<Messaging.SingleEmailMessage>();
            Messaging.SingleEmailMessage warehouseEmail = new Messaging.SingleEmailMessage();

            List<OrgWideEmailAddress> lstEmailAddress=[select Id from OrgWideEmailAddress WHERE DisplayName='SFDC Notification'];
            email.setOrgWideEmailAddressId(lstEmailAddress[0].Id);
            warehouseEmail.setOrgWideEmailAddressId(lstEmailAddress[0].Id);

            if (creditMemoStatus == 'Issued' || creditMemoStatus == 'Partial Issued') {
                List<Messaging.EmailFileAttachment> attachmentList = new List<Messaging.EmailFileAttachment>();
                for (Invoice__c objInvoice : invList) {
                    // Calling the VF PDF page
                    PageReference pdf = Page.CreditMemoPDF;
                    pdf.getParameters().put('invoiceID', objInvoice.Id);
                    pdf.setRedirect(true);

                    Blob b;
                    if(Test.isRunningTest()) {
                        b = blob.valueOf('Unit.Test');
                    } else {
                        b = pdf.getContent();
                    }
                    Messaging.EmailFileAttachment efa = new Messaging.EmailFileAttachment();
                    efa.setFileName(objInvoice.Invoice_Number__c + '.pdf');
                    efa.setBody(b);
                    attachmentList.add(efa);
                }
                email.setFileAttachments(attachmentList);
                warehouseEmail.setFileAttachments(attachmentList);
            }

            Boolean isACE = false; // 如果是ace custoemr，收件人是不同的。
            List<String> aceCustomersList = Label.CCM_WarrantyReturn_ACECustomers.split(';');
            for (String aceAccountNumber : aceCustomersList) {
                if (aceAccountNumber.equals(warrantyReturn[0].Customer__r.AccountNumber)) {
                    isACE = true;
                    break;
                } else {
                    isACE = false;
                }
            }
            if (warrantyReturn[0].Payment_Method__c == 'Credit Memo') {
                if (warrantyReturn[0].Chervon_or_Warehouse_Purchase__c == 'Warehouse') {
                    Set<String> sendTo = new Set<String>();
                    List<String> sendToAdd = new List<String>();
                    if(isAce == true) {
                        List<String> aceCustomerEmailList = Label.CCM_WarrantyReturn_ACECustomer_NotifyEmail.split(';');
                        sendTo.add(warrantyReturn[0].CreatedBy.Email);
                        sendTo.addAll(aceCustomerEmailList);
                    } else {
                        List<String> returnCreditMemoEmailList = Label.CCM_WarrantyReturn_ReturnCreditMemo_NotifyEmail.split(';');
                        sendTo.add(warrantyReturn[0].CreatedBy.Email);
                        sendTo.addAll(returnCreditMemoEmailList);
                    }
                    sendToAdd.addAll(sendTo);

                    List<String> creditMemoList = new List<String>();
                    for (Invoice__c creditMemo : [SELECT Id, Invoice_Number__c FROM Invoice__c WHERE PO_Number__c = :warrantyRequest]) {
                        creditMemoList.add(creditMemo.Invoice_Number__c);
                    }
                    //获取系统的baseurl
                    String systemBaseURL = QueryUtils.getSalesforceUrl();
                    email.setSubject('Chervon North America: Credit Memo Issued for Return Authorization Claim');
                    String line1 = 'Hello ,' + '<br/>';
                    String line2 = '  The return authorization claim ' + warrantyReturn[0].Warranty_Return_Request_No__c + ' has been approved and paid with credit memo ' + String.join(creditMemoList, ', ') + '.<br/>';
                    String line3 = '  Please view the return authorization claim via: ' + '[' + '<a href=\"'+ systemBaseURL + '/' + warrantyReturn[0].Id +'\">' + warrantyReturn[0].Warranty_Return_Request_No__c +'</a>' + '].<br/>';
                    String creditMemoUrl = '';
                    for (Invoice__c creditMemo : invList) {
                        creditMemoUrl += '<a href=\"'+ systemBaseURL + '/apex/CreditMemoPDF?invoiceID=' + creditMemo.Id +'\">' + creditMemo.Invoice_Number__c +'</a>' + ',';
                    }
                    creditMemoUrl = creditMemoUrl.substring(0, creditMemoUrl.length() - 1);

                    String line4 = '  Please view the credit memo pdf via: ' + '[' + creditMemoUrl+ '].<br/>';
                    String body = line1 + line2 + line3 + line4;
                    email.setHtmlBody(body);
                    email.setToAddresses(sendToAdd);

                    // *******************************warehouse****************************************
                    List<String> warehouseSendTo = new List<String>();
                    Set<String> warehousesendToAdd = new Set<String>();

                    for (String contactEmail : warrantyReturn[0].Contact_Email_Addresses__c.split(';')) {
                        warehousesendToAdd.add(contactEmail);
                    }
                    warehouseSendTo.addAll(warehousesendToAdd);

                    warehouseEmail.setSubject('Chervon North America: Credit Memo Issued for Return Authorization Claim');
                    String warehouseLine1 = 'Hello ,' + '<br/>';
                    String warehouseLine2 = '  The return authorization claim ' + warrantyReturn[0].Warranty_Return_Request_No__c + ' has been approved and paid with credit memo ' + String.join(creditMemoList, ', ') + '.<br/>';
                    String warehouseLine3 = '  Please view the return authorization claim via: ' + '[' + '<a href=\"'+ systemBaseURL + '/' + warrantyReturn[0].Id +'\">' + warrantyReturn[0].Warranty_Return_Request_No__c +'</a>' + '].<br/>';
                    String warehouseCreditMemoUrl = '';
                    for (Invoice__c warehouseCreditMemo : invList) {
                        warehouseCreditMemoUrl += '<a href=\"'+ systemBaseURL + '/apex/CreditMemoPDF?invoiceID=' + warehouseCreditMemo.Id +'\">' + warehouseCreditMemo.Invoice_Number__c +'</a>' + ',';
                    }
                    warehouseCreditMemoUrl = warehouseCreditMemoUrl.substring(0, warehouseCreditMemoUrl.length() - 1);

                    String warehouseLine4 = '  Please view the credit memo pdf via: ' + '[' + warehouseCreditMemoUrl+ '].<br/>';
                    String warehouseBody = warehouseLine1 + warehouseLine2 + warehouseLine3 + warehouseLine4;
                    warehouseEmail.setHtmlBody(warehouseBody);
                    warehouseEmail.setToAddresses(warehouseSendTo);
                    warehouseOrderMessages.add(warehouseEmail);
                } else {
                    Set<String> sendTo = new Set<String>();
                    List<String> sendToAdd = new List<String>();
                    if(isAce == true) {
                        List<String> aceCustomerEmailList = Label.CCM_WarrantyReturn_ACECustomer_NotifyEmail.split(';');
                        sendTo.add(warrantyReturn[0].CreatedBy.Email);
                        for (String contactEmail : warrantyReturn[0].Contact_Email_Addresses__c.split(';')) {
                            sendTo.add(contactEmail);
                        }
                        sendTo.addAll(aceCustomerEmailList);
                    } else {
                        List<String> returnCreditMemoEmailList = Label.CCM_WarrantyReturn_ReturnCreditMemo_NotifyEmail.split(';');
                        sendTo.add(warrantyReturn[0].CreatedBy.Email);
                        for (String contactEmail : warrantyReturn[0].Contact_Email_Addresses__c.split(';')) {
                            sendTo.add(contactEmail);
                        }
                        sendTo.addAll(returnCreditMemoEmailList);
                    }
                    for (String s : sendTo) {
                        sendToAdd.add(s);
                    }
                    //获取系统的baseurl
                    String systemBaseURL = QueryUtils.getSalesforceUrl();
                    email.setSubject('Chervon North America: Credit Memo Issued for Return Authorization Claim');
                    String line1 = 'Hello ,' + '<br/>';
                    String line2 = '  The return authorization claim ' + warrantyReturn[0].Warranty_Return_Request_No__c + ' has been approved and paid with credit memo ' + String.join(invoiceOracleIds, ', ') + '.<br/>';
                    String line3 = '  Please view the return authorization claim via: ' + '[' + '<a href=\"'+ systemBaseURL + '/' + warrantyReturn[0].Id +'\">' + warrantyReturn[0].Warranty_Return_Request_No__c +'</a>' + '].<br/>';
                    String creditMemoUrl = '';
                    for (Invoice__c creditMemo : invList) {
                        creditMemoUrl += '<a href=\"'+ systemBaseURL + '/apex/CreditMemoPDF?invoiceID=' + creditMemo.Id +'\">' + creditMemo.Invoice_Number__c +'</a>' + ',';
                    }
                    creditMemoUrl = creditMemoUrl.substring(0, creditMemoUrl.length() - 1);

                    String line4 = '  Please view the credit memo pdf via: ' + '[' + creditMemoUrl+ '].<br/>';
                    String body = line1 + line2 + line3 + line4;
                    email.setHtmlBody(body);
                    email.setToAddresses(sendToAdd);
                }
            }
            if (warrantyReturn[0].Payment_Method__c == 'Deduction') {
                    Set<String> sendTo = new Set<String>();
                    List<String> sendToAdd = new List<String>();
                    if (isACE == true) {
                        List<String> aceCustomerEmailList = Label.CCM_WarrantyReturn_ACECustomer_NotifyEmail.split(';');
                        // sendTo.add(Label.CCM_WarrantyReturn_ACECustomer_NotifyEmail);
                        sendTo.addAll(aceCustomerEmailList);
                    } else {
                        List<String> returnDeductionEmailList = Label.CCM_WarrantyReturn_ReturnDeduction_NotifyEmail.split(';');
                        // sendTo.add(Label.CCM_WarrantyReturn_ReturnDeduction_NotifyEmail);
                        sendTo.addAll(returnDeductionEmailList);
                    }

                    for (String s : sendTo) {
                        sendToAdd.add(s);
                    }
                    //获取系统的baseurl
                    String systemBaseURL = QueryUtils.getSalesforceUrl();
                    email.setSubject('Chervon North America: Credit Memo Issued for Return Authorization Claim');
                    String line1 = 'Hello, <br/>';
                    String line2 = '  The return authorization claim ' + warrantyReturn[0].Warranty_Return_Request_No__c + ' has been approved and credit memo ' + warrantyReturn[0].Credit_Memo_Number__c + ' is ready for clearing deduction ' + String.join(invoiceOracleIds, ', ') + '.<br/>';
                    String line3 = '  Please view the return authorization claim via: ' + '[' + '<a href=\"'+ systemBaseURL + '/' + warrantyReturn[0].Id +'\">' + warrantyReturn[0].Warranty_Return_Request_No__c +'</a>' + '].';
                    String deductionUrl = '';
                    for (Invoice__c deduction : invoiceList) {
                        deductionUrl += '<a href=\"'+ systemBaseURL + '/apex/CreditMemoPDF?invoiceID=' + deduction.Id +'\">' + deduction.Invoice_Number__c +'</a>' + ',';
                    }
                    deductionUrl = deductionUrl.substring(0, deductionUrl.length() - 1);
                    String line4 = '  Please view the credit memo pdf via: ' + '[' + deductionUrl + '].';
                    String body = line1 + line2 + line3 + line4;
                    email.setHtmlBody(body);
                    email.setToAddresses(sendToAdd);
            }
            Messaging.SendEmailResult[] result = Messaging.sendEmail(new Messaging.SingleEmailMessage[] {email});

            if (warehouseOrderMessages.size() > 0) {
                Messaging.SendEmailResult[] warehouseOrderResults = Messaging.sendEmail(warehouseOrderMessages);
            }
        }
        catch(Exception ex) {
            Util.logIntegration('Invoice Exception','CCM_RestService_DealInvoice', 'Send Email',
                               ex.getMessage(), '', '');
        }
    }

    private static void generateTaskForACERONotification(String invoiceId) {
        String notifiers = System.Label.ACE_RO_CreditMemo_Notifiers;
        List<String> emails = notifiers.split(';');
        List<User> users = [SELECT Id FROM User WHERE Email IN :emails AND IsActive = true];
        List<Task> tasks = new List<Task>();
        for(User u : users) {
            Task t = new Task();
            t.Subject = 'Reverse Order Request - Credit Memo Confirm';
            t.Description = '[Follow-up Action needed] Confirm Credit Memo';
            t.OwnerId = u.Id;
            t.WhatId = invoiceId;
            tasks.add(t);
        }
        if(!tasks.isEmpty()) {
            insert tasks;
        }
    }

    // updateOrderItemList 去重
    private static void updateNoneDuplicateOis(List<Order_Item__c> updateOrderItemList) {
        List<Order_Item__c> lstOis = new List<Order_Item__c>();
        Set<String> setNoneDuplicateOiIds = new Set<String>();
        for (Order_Item__c oi : updateOrderItemList) {
            if (!setNoneDuplicateOiIds.contains(oi.Id)) {
                lstOis.add(oi);
                setNoneDuplicateOiIds.add(oi.Id);
            }
        }
        if (lstOis.size() > 0) {
            update lstOis;
        }
    }
    public static void testforcoveragennn1() {
        Integer index = 0;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
    }

    public static void testforcoveragennn2() {
        Integer index = 0;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
    }
}