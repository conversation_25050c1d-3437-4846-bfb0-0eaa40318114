<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>ActualIndicator__c</fullName>
    <externalId>false</externalId>
    <formula>IF(   ISBLANK(Serial_Number__c) ,&apos;Pending&apos;,
IF(ISBLANK(TEXT(Warranty__r.Place_of_Purchase_picklist__c)),
    &apos;&apos;,
    IF(ISPICKVAL(Warranty__r.Place_of_Purchase_picklist__c, &apos;Unauthorized Dealer&apos;),
        &apos;Out of Warranty&apos;,
        IF(ISNULL(Expiration_Date_New__c),
            &apos;Out of Warranty&apos;,
            IF(Expiration_Date_New__c - TODAY() &lt; 0,
                &apos;Out of Warranty&apos;,
                IF(
                    OR(
                        ISPICKVAL(Warranty__r.Place_of_Purchase_picklist__c, &apos;Unknown&apos;),
                        AND(
                            CONTAINS($Label.CCM_Site_Origin_Outside_Europe, TEXT(Warranty__r.AccountCustomer__r.Site_Origin__c)),
                            ISPICKVAL(Warranty__r.Place_of_Purchase_picklist__c, &apos;Other&apos;)
                        )
                    ),
                    &apos;Pending&apos;,
                    IF(Warranty__r.Receipt_Received_Warranty_Ineligible__c,
                        &apos;Out of Warranty&apos;,
                        IF(Warranty__r.Receipt_received_and_verified__c,
                            IF(ISBLANK(Serial_Number__c),
                              &apos;Pending&apos;,
                              &apos;Vailid Warranty&apos;
                             ),
                            IF(
                                AND(
                                    Warranty__r.Lost_Receipt__c,
                                    Warranty__r.One_Time_Exception__c
                                ),
                                &apos;Vailid Warranty&apos;,
                                IF(
                                    AND(
                                        Warranty__r.Lost_Receipt__c,
                                        NOT(Warranty__r.One_Time_Exception__c) ,
                                        Warranty__r.Order_Times__c = 0,
                                        Warranty__r.Replacement_Order_Time__c
                                    
                                    ),
                                    &apos;Vailid Warranty&apos;,
                                    IF(Warranty__r.Pending__c,
                                        &apos;Pending&apos;,
                                        &apos;Out of Warranty&apos;
                                )
                            )
                        )
                    )
                )
            )
        )
    )
  )
))</formula>
    <label>Actual Indicator</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Text</type>
    <unique>false</unique>
</CustomField>
