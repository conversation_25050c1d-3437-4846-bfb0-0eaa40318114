({
	init:function(comp,event,helper)
    {
        // var createRecordEvent = $A.get("e.force:createRecord");
        // createRecordEvent.setParams({
        //     "entityApiName": "Task"
        // });
        // createRecordEvent.fire();
    },

    handleCancel: function(comp, event, helper) {
        $A.get("e.force:closeQuickAction").fire();
    },

    handleSave: function(comp, event, helper) {
        $A.get("e.force:closeQuickAction").fire();
    }   
})