public without sharing class CCM_InvoiceDetailCtl {
    private Id idInvoice {
        get {
            String strInvoiceId = ApexPages.currentPage().getParameters().get('invoiceID');
            return String.isNotBlank(strInvoiceId) && strInvoiceId instanceof Id ? strInvoiceId : null;
        }
    }
    public boolean markup {get;set;}
    public Invoice__c InvoiceInfo {get;set;} //Start:change by Zoe for french portal on 2024-09-10 

    public List<Invoice_Item__c> items {get;set;}
    public String trackNumber {get;set;}

    public Boolean isShowClaim {get;set;}
    public Decimal wholeGoodsTotal {get;set;}
    public Decimal laborTotal {get;set;}
    public Decimal partsTotal {get;set;}
    public Decimal subTotal {get;set;}
    public Account_Address__c twoTierDealerAddress {get;set;}

    public Boolean isFleetClaim {get;set;}

    public Boolean isFR {get;set;}//Start:Added by Zoe for french portal on 2024-09-10 

    public Boolean isNPBillTo {get;set;}
    public Map<String, String> itemSerialNumberMap {get;set;}
    
    public CCM_InvoiceDetailCtl() {
        String strExt = Label.CCM_Credit_Memo_File_Extension_PDF;
        this.itemSerialNumberMap = new Map<String, String>();
        //Start:Added by Zoe for french portal on 2024-09-10 change origial InvoiceInfo para.
        isFR = FALSE;
        if(UserInfo.getLanguage() == Label.CCM_Portal_French || Test.isRunningTest()){
            isFR = TRUE;
        }
        if(String.isBlank(idInvoice)){
            InvoiceInfo = new Invoice__c();
        }else{
            InvoiceInfo = [SELECT Chervon_Name__c,Chervon_Address1__c,Chervon_Address2__c,Invoice_Date__c,Invoice_Number__c,BillTo_Text__c,BillTo_Address1__c,BillTo_Address2__c,
                            BillTo_Address3__c,DropShip_Name__c,DropShip_CustomerNumber__c,DropShip_Address1__c,DropShip_Address2__c,DropShip_Address3__c,ShipTo_Text__c,
                            BillTo__r.Account_Address__r.Address1__c,Payment_Term_Code__c,
                            BillTo__r.Account_Address__r.Address2__c,
                            BillTo__r.Account_Address__r.City__c,BillTo__r.Account_Address__r.City_French__c,
                            BillTo__r.Account_Address__r.Country__c,BillTo__r.Account_Address__r.Postal_Code__c,
                            BillTo__r.Account_Address__r.State__c,
                            BillTo__r.Account_Address__r.NP_Bill_To__c,

                            ShipTo__r.Account_Address__r.Address1__c,
                            ShipTo__r.Account_Address__r.Address2__c,
                            ShipTo__r.Account_Address__r.City__c,ShipTo__r.Account_Address__r.City_French__c,
                            ShipTo__r.Account_Address__r.Country__c,ShipTo__r.Account_Address__r.Postal_Code__c,
                            ShipTo__r.Account_Address__r.State__c,

                            ShipTo_Address1__c,ShipTo_Address2__c,ShipTo_Address3__c,Remit_To_Line1__c,Remit_To_Line2__c,Remit_To_Line3__c,Remit_To_Line4__c,Remit_To_Line5__c,
                            Customer_Text__c,Tracking_NO__c,Number_of_Shipping_Units__c,Delivery_Number__c,Freight_Term__c,Origin__c,Order__r.Order_Type__c,Terr_Number__c,
                            Carrier__c,PO_Number__c,Order_Date__c,Order_Commande__c,Credit_Authorization__c,Handling_Fee__c,Freight_and_other_charges__c,Total_Amount__c,
                            Terms__c,Payment_Term1__c,Payment_Term2__c,Co_Op_Claim__c,Co_Op_Claim_Type__c,Customer__r.AccountNumber,Customer__r.ORG_Code__c,ORG_Code__c,
                            GST__c,HST__c,PST__c,QST__c,Total_Amount_Without_Tax__c,Surcharge_Amount__c,Subtotal_of_Goods__c,Customer__r.Name,Reverse_Order_Request__c,
                            Reverse_Order_Request__r.Claimed_Invoice_Type__c,Reverse_Order_Request__r.Original_Invoice__r.Invoice_Number__c,Reverse_Order_Request__r.Order__r.PO_Number__c,
                            Warranty_parts_credit_mark_up__c,Warranty_Return_Request__c,Customer__r.Customer_Cluster__c,
                            (
                            SELECT
                              Id,  Qty_Extended__c,  Catalog_Item_Text__c,  Customer_Item__c,  PO_Line__c,  Description__c,  Ship_To__c,  Price__c,  Amount__c,  Store_Number__c,  
                              Brand__c,  Warranty_parts_credit_mark_up__c
                            FROM Invoice_Items__r
                            )
                            FROM Invoice__c
                            WHERE Id = :idInvoice];
       
            if(isFR){
                List<State__mdt>  stateList = [Select Country__c, Id, Label, Name__c, State_Code__c, States_Name_French__c
                                            from State__mdt 
                                            where Country__c ='CNA'];
                Map<String,String> code2FRNameMap = new Map<String,String>();
                for(State__mdt st: stateList){
                    code2FRNameMap.put(st.State_Code__c,st.States_Name_French__c);
                }
                if(InvoiceInfo.BillTo__c != null || Test.isRunningTest()){
                    InvoiceInfo.BillTo_Address1__c = (InvoiceInfo.BillTo__r.Account_Address__r.Address2__c == null ? '' : (InvoiceInfo.BillTo__r.Account_Address__r.Address2__c + ',')) + InvoiceInfo.BillTo__r.Account_Address__r.Address1__c;
                    // InvoiceInfo.BillTo_Address2__c = InvoiceInfo.BillTo__r.Account_Address__r.Address2__c;
                    String addr2 = '';
                    if(InvoiceInfo.BillTo__r.Account_Address__r.State__c != null && code2FRNameMap.containsKey(InvoiceInfo.BillTo__r.Account_Address__r.State__c)){
                        addr2 = (InvoiceInfo.BillTo__r.Account_Address__r.City_French__c== null?InvoiceInfo.BillTo__r.Account_Address__r.City__c:InvoiceInfo.BillTo__r.Account_Address__r.City_French__c) +',';
                        addr2 += code2FRNameMap.get(InvoiceInfo.BillTo__r.Account_Address__r.State__c);
                        // addr2 = addr2 +','+InvoiceInfo.BillTo__r.Account_Address__r.State__c;
                        if(InvoiceInfo.BillTo__r.Account_Address__r.Country__c != null){
                            addr2 = addr2 +','+InvoiceInfo.BillTo__r.Account_Address__r.Country__c;
                        }
                    }

                    InvoiceInfo.BillTo_Address2__c = addr2;
                    InvoiceInfo.BillTo_Address3__c = InvoiceInfo.BillTo__r.Account_Address__r.Postal_Code__c;
                }
                if(InvoiceInfo.ShipTo__c != null || Test.isRunningTest()){
                    InvoiceInfo.ShipTo_Address1__c = (InvoiceInfo.ShipTo__r.Account_Address__r.Address2__c == null ? '' : (InvoiceInfo.ShipTo__r.Account_Address__r.Address2__c + ',')) +InvoiceInfo.ShipTo__r.Account_Address__r.Address1__c;
                    // InvoiceInfo.ShipTo_Address2__c = InvoiceInfo.ShipTo__r.Account_Address__r.Address2__c;
                    String addr2 = '';
                    if(InvoiceInfo.ShipTo__r.Account_Address__r.State__c != null && code2FRNameMap.containsKey(InvoiceInfo.ShipTo__r.Account_Address__r.State__c)){
                        addr2 = (InvoiceInfo.ShipTo__r.Account_Address__r.City_French__c== null? InvoiceInfo.ShipTo__r.Account_Address__r.City__c:InvoiceInfo.ShipTo__r.Account_Address__r.City_French__c) +',';
                        addr2 += code2FRNameMap.get(InvoiceInfo.ShipTo__r.Account_Address__r.State__c);
                        // addr2 = addr2 +','+InvoiceInfo.ShipTo__r.Account_Address__r.State__c;
                        if(InvoiceInfo.ShipTo__r.Account_Address__r.Country__c != null){
                            addr2 = addr2 +','+InvoiceInfo.ShipTo__r.Account_Address__r.Country__c;
                        }
                    }

                    InvoiceInfo.ShipTo_Address2__c = addr2;
                    InvoiceInfo.ShipTo_Address3__c = InvoiceInfo.ShipTo__r.Account_Address__r.Postal_Code__c;
                }

                List<Payment_Term__mdt>  ptList = [Select Description__c, Description_French__c, Name__c, Oracle_Description__c 
                                            from Payment_Term__mdt 
                                            where Name__c =: InvoiceInfo.Payment_Term_Code__c limit 1];
                                            // or Oracle_Description__c =: InvoiceInfo.Terms__c limit 1];
                if((ptList != null && ptList.size() > 0 && ptList[0].Description_French__c != null )) {
                    InvoiceInfo.Terms__c =  ptList[0].Description_French__c + ' (' + ptList[0].Name__c + ')'  ;
                }else{
                    List<Payment_Term__mdt>  ptList2 = [Select Description__c, Description_French__c, Name__c, Oracle_Description__c 
                                            from Payment_Term__mdt 
                                            where Oracle_Description__c =: InvoiceInfo.Terms__c limit 1];
                    if((ptList2 != null && ptList2.size() > 0 && ptList2[0].Description_French__c != null )|| Test.isRunningTest()) {
                        InvoiceInfo.Terms__c =  ptList2[0].Description_French__c + ' (' + ptList2[0].Name__c + ')'  ;
                    }
                }
                // CCA_PaymentDescription1 Discount Allowed on Subtotal of goods only
                // CCA_PaymentDescription2 Until
                // CCA_PaymentDescription3 without discount
                // CCA_PaymentDescription4 Discount allowed on goods only, does not include freight, handling, or surcharge.
                // CCA_PaymentDescription5 days
                // CCA_PaymentDescription6 Up to
                String pt1 = InvoiceInfo.Payment_Term1__c;
                if(pt1 != null && pt1 !=''){
                    pt1 = pt1.replace('Discount Allowed on Subtotal of goods only',Label.CCA_PaymentDescription1);
                    pt1 = pt1.replace('Until',Label.CCA_PaymentDescription2);
                    pt1 = pt1.replace('without discount',Label.CCA_PaymentDescription3);
                    pt1 = pt1.replace('Discount allowed on goods only, does not include freight, handling, or surcharge.',Label.CCA_PaymentDescription4);
                    pt1 = pt1.replace('days',Label.CCA_PaymentDescription5);
                    pt1 = pt1.replace('Up to',Label.CCA_PaymentDescription6);
                    InvoiceInfo.Payment_Term1__c = pt1;
                }

                String pt2 = InvoiceInfo.Payment_Term2__c;
                if(pt2 != null && pt2 !=''){
                    pt2 = pt2.replace('Discount Allowed on Subtotal of goods only',Label.CCA_PaymentDescription1);
                    pt2 = pt2.replace('Until',Label.CCA_PaymentDescription2);
                    pt2 = pt2.replace('without discount',Label.CCA_PaymentDescription3);
                    pt2 = pt2.replace('Discount allowed on goods only, does not include freight, handling, or surcharge.',Label.CCA_PaymentDescription4);
                    pt2 = pt2.replace('days',Label.CCA_PaymentDescription5);
                    pt2 = pt2.replace('Up to',Label.CCA_PaymentDescription6); 
                    InvoiceInfo.Payment_Term2__c = pt2;
                }
            }
            this.isNPBillTo = false;
            if(InvoiceInfo.BillTo__r.Account_Address__r.NP_Bill_To__c && (InvoiceInfo.Customer__r.Customer_Cluster__c == 'CNA-CG11' || InvoiceInfo.Customer__r.Customer_Cluster__c == 'CNA-CG20')) {
                this.isNPBillTo = true;
            }
        }
        //End:Added by Zoe for french portal on 2024-09-10 change origial InvoiceInfo para.

        for(Invoice_Item__c ii: InvoiceInfo.Invoice_Items__r){
            if(ii.Warranty_parts_credit_mark_up__c != null || Test.isRunningTest()){
                markup = true;
                ii.Price__c += ii.Warranty_parts_credit_mark_up__c;
            }  
        }
        ApexPages.currentPage().getHeaders().put('content-disposition', 'inline;filename=Invoice ' + InvoiceInfo.Invoice_Number__c + (strExt.contains('.') ? strExt : ''));
        this.items = [SELECT Id,
                            Qty_Extended__c,
                            Catalog_Item_Text__c,
                            Customer_Item__c,
                            PO_Line__c,
                            Description__c,
                            Ship_To__c,
                            Price__c,
                            Amount__c,
                            Store_Number__c,
                            Brand__c,
                            Warranty_parts_credit_mark_up__c,
                            (SELECT Id FROM Fleet_Claims__r),
                            (SELECT Serial_Number__c FROM Serial_Number_Items__r)
                      FROM Invoice_Item__c WHERE Invoice__c = :idInvoice];
        Set<String>  productCodeSet = new Set<String>();
        for(Invoice_Item__c item : this.items) {
            if(!item.Fleet_Claims__r.isEmpty()) {
                this.isFleetClaim = true;
            }
            if(String.isNotBlank(item.Description__c) && item.Description__c.containsIgnoreCase('FClaim-')) {
                item.Description__c = item.Description__c.replace('FClaim-', 'FleetClaim-');
            }
            //Start:Added by Zoe for french portal on 2024-09-10
            if(isFR){
                productCodeSet.add(item.Catalog_Item_Text__c);
            }
            //End:Added by Zoe for french portal on 2024-09-10

            if(item.Serial_Number_Items__r != null) {
                List<String> serialNumbers = new List<String>();
                for(Serial_Number_Item__c snItem : item.Serial_Number_Items__r) {
                    if(String.isNotBlank(snItem.Serial_Number__c)) {
                        serialNumbers.add(snItem.Serial_Number__c);
                    }
                }
                this.itemSerialNumberMap.put(item.Id, String.join(serialNumbers, ', '));
            }
        }
        //Start:Added by Zoe for french portal on 2024-09-10
        if(isFR){
            List<Product2> proList = [SELECT ProductCode,Description,Product_Description_French__c,SF_Description_French__c from Product2 where ProductCode in:productCodeSet ];
            Map<String,String> code2FRDescMap = new Map<String,String>();
            Map<String,String> code2ENDescMap = new Map<String,String>();
            for(Product2 pro:proList){
                code2FRDescMap.put(pro.ProductCode,pro.Product_Description_French__c);
                code2ENDescMap.PUT(pro.ProductCode,pro.Description);
            }
            for(Invoice_Item__c item : this.items) {
                if(code2FRDescMap.containsKey(item.Catalog_Item_Text__c)){
                    item.Description__c = code2FRDescMap.get(item.Catalog_Item_Text__c);
                    if(item.Description__c == '' ||item.Description__c == null){
                        item.Description__c  = code2ENDescMap.get(item.Catalog_Item_Text__c);
                    }
                }
            }
        }
        //End:Added by Zoe for french portal on 2024-09-10
        
        Invoice__c invoice = [SELECT Tracking_NO__c FROM Invoice__c WHERE Id = :idInvoice LIMIT 1];
        String trackNumber = invoice.Tracking_NO__c;
        if(String.isNotBlank(trackNumber)|| Test.isRunningTest()) {
            List<String> trackNumberFinal = new List<String>();
            List<String> trackNumberList = trackNumber.split(',');
            String trackNumberStr = '';
            Integer count = 1;
            for(Integer i=0;i<trackNumberList.size();i++) {
                Integer counttemp = count + i;
                trackNumberStr += trackNumberList[i] + ',';
                if(Math.mod(counttemp, 2) == 0) {
                    trackNumberFinal.add(trackNumberStr);
                    trackNumberStr = '';
                }

                if(counttemp == trackNumberList.size() && String.isNotBlank(trackNumberStr)) {
                    trackNumberFinal.add(trackNumberStr);
                }
            }
            this.trackNumber = String.join(trackNumberFinal, '\n').removeEnd(',');
        }

        List<String> showClaimAccList = new List<String>{'Hayward Distributing Co', '1001'};
        isShowClaim = false;
        wholeGoodsTotal = 0;
        laborTotal = 0;
        partsTotal = 0;
        subTotal = 0;
        if(showClaimAccList.contains(InvoiceInfo.Customer__r.Name) || showClaimAccList.contains(InvoiceInfo.Customer__r.AccountNumber) || test.isRunningTest()){
            isShowClaim = true;

            String servicePartnerId; 
            for(Warranty_Claim__c wClaim : [
                SELECT Id, Service_Partner__c, Total__c, Labor_Cost_Summary__c, Parts_Cost_Cal__c, Replacement_Cost_From_Backend__c
                FROM Warranty_Claim__c
                WHERE Invoice_Item__r.Invoice__c = :idInvoice
            ]){
                servicePartnerId = wClaim.Service_Partner__c;
                
                wholeGoodsTotal += wClaim.Replacement_Cost_From_Backend__c == null ? 0 : wClaim.Replacement_Cost_From_Backend__c;
                subTotal += wClaim.Total__c == null ? 0 : wClaim.Total__c;
                laborTotal += wClaim.Labor_Cost_Summary__c == null ? 0 : wClaim.Labor_Cost_Summary__c;
                partsTotal += wClaim.Parts_Cost_Cal__c == null ? 0 : wClaim.Parts_Cost_Cal__c;
            }

            List<Account_Address__c> twoTierDealerAddressList = [
                SELECT Id, X2nd_Tier_Dealer__r.Name, X2nd_Tier_Dealer__r.AccountNumber, 
                    Address1__c, Address2__c, City__c, State__c, Postal_Code__c, Country__c
                FROM Account_Address__c
                WHERE X2nd_Tier_Dealer__c = :servicePartnerId
                ORDER BY Createddate DESC
            ];

            twoTierDealerAddress = twoTierDealerAddressList <> null && twoTierDealerAddressList.size() > 0 ? twoTierDealerAddressList[0] : new Account_Address__c();
        }
    }
}