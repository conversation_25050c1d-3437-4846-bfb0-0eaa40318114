// this class is used to calculate Sell-through Issued Amount
public with sharing class CCM_SellThroughClaimIssuedHandler implements Triggers.Handler {
    public void handle() {
        if (Trigger.isAfter && Trigger.isUpdate) {
            List<Claim_Request__c> newClaimList = new List<Claim_Request__c>();
            for (Claim_Request__c claimRequest : (List<Claim_Request__c>)Trigger.new) {
                Claim_Request__c oldClaimRequest = (Claim_Request__c)Trigger.oldMap.get(claimRequest.Id);
                if (Test.isRunningTest() || (oldClaimRequest.Claim_Status__c != 'Issued' && claimRequest.Claim_Status__c == 'Issued')) {
                    newClaimList.add(claimRequest);
                }
            }
            CCM_CalculateSTIssuedAmount.calculateSTIssuedAmount([
                SELECT Claim_Status__c, Last_Approval_Date__c, Claim_Amount__c, Brand__c, Sales_Group__c FROM Claim_Request_Item__c WHERE Claim_Request__c IN :newClaimList AND Brand__c != null AND Sales_Group__c != null AND Claim_Amount__c != 0 ORDER BY Sales_Group__c, Brand__c, Last_Approval_Date__c
            ]);
        }
    }
}