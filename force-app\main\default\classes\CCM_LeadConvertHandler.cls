/**
 * 
 * @update sync prospect team member to account team member
 * @revison <PERSON> 2023-07-18 Add logic to bypass customer profile required validation for CA TAIT.
 */
public without sharing class CCM_LeadConvertHandler implements Triggers.Handler {
    public void handle() {
        
        // for (Lead prospect : (List<Lead>)Trigger.new) {
        //     if (prospect.RecordTypeId==CCM_Constants.PROSPECT_RECORD_TYPE_STORE_LOCATION_ID) {
        //         return;
        //     }
        // }
        if (Trigger.isAfter) {
            if (Trigger.isUpdate) {
                redirectRelatedToAccount(Trigger.new, Trigger.newMap, Trigger.oldMap);
            }
        }
    }

    public void redirectRelatedToAccount(List<sObject> objList, Map<Id, sObject> newMap, Map<Id, sObject> oldMap) {
        List<Lead> leadList = (List<Lead>)objList;
        //Set<Id> convertedLeadIdSet = new Set<Id>();
        Map<Id, Id> leadToAccMap = new Map<Id, Id>();
        for (Lead newItem : leadList) {
            if (String.isNotBlank(newItem.ConvertedAccountId)) {
                leadToAccMap.put(newItem.Id, newItem.ConvertedAccountId);
            }
        }
        if (leadToAccMap.size() == 0) {
            return;
        }

        List<Sales_Program__c> spList = new List<Sales_Program__c>();
        List<Attachment_Management__c> amList = new List<Attachment_Management__c>();
        /*List<Business_Volume__c> bvList = new List<Business_Volume__c>();*/
        List<Customer_Profile__c> cpList = new List<Customer_Profile__c>();
        List<Account_Address__c> aaList = new List<Account_Address__c>();

        Map<Id, Contact> cMap = new Map<Id, Contact>();

        List<Account> storeList = new List<Account>();
        storeList=[Select a.Id, a.Related_Entity__c, a.Related_Entity_Prospect__c from Account a where a.Related_Entity_Prospect__c in:leadToAccMap.keySet()];
        for (Account store : storeList) {
            store.Related_Entity__c=leadToAccMap.get(store.Related_Entity_Prospect__c);
            store.Related_Entity_Prospect__c=null;
        }
        update storeList;

        Map<Id, Lead> queriedLeadMap = new Map<Id, Lead>([
            SELECT Id, ConvertedAccountId, Customer_Business_Type__c,RecordTypeId, To_Be_Direct_Indirect_Customer__c,Org_Code__c, Buying_Group__c, Buying_Group__r.Name,
                (SELECT Id, Approval_Status__c from Sales_Program__r), 
                (SELECT Id, Attachment_Type__c from Attachment_Management__r),
                (SELECT Id, Prospect__c, Customer__c FROM Customer_Profile__r),
                (SELECT Id, ORG_Id__c, Contact__c, Contact__r.AccountId from Account_Address__r) 
            FROM Lead 
            WHERE Id IN :newMap.keySet()
        ]);

        RecordType rt = [Select Id, SobjectType, DeveloperName FROM RecordType Where SobjectType = 'Customer_Profile__c' And DeveloperName = 'Customer'];

        for (Lead newItem : leadList) {
            Lead oldItem = (Lead) oldMap.get(newItem.Id);
            if (newItem.Status == 'Converted' && oldItem.Status != 'Converted' && String.isNotBlank(newItem.ConvertedAccountId)) {
                Lead theLead = queriedLeadMap.get(newItem.Id);
                // validation first
                Boolean hasApprovedProgram = false;
                Boolean hasPendingProgram = false;
                for (Sales_Program__c sp : theLead.Sales_Program__r) {
                    if (sp.Approval_Status__c == 'Approved') {
                        hasApprovedProgram = true;
                    } else if (sp.Approval_Status__c == 'Pending for Approval') {
                        hasPendingProgram = true;
                    }
                    sp.Customer__c = newItem.ConvertedAccountId;
                }
                if (!hasApprovedProgram&&theLead.RecordTypeId==CCM_Constants.PROSPECT_RECORD_TYPE_CHANNEL_ID&&theLead.To_Be_Direct_Indirect_Customer__c=='TO BE CHERVON DIRECT CUSTOMER') {
                    newItem.addError('Prospect conversion require at least one approved Authorized Brand.');
                    break;
                }
                if (hasPendingProgram) {
                    newItem.addError('There\'s an Authorized Brand Pending Approval, cannot do prospect coversion until its completion.');
                    break;
                }

                if(String.isBlank(theLead.Buying_Group__c) || !theLead.Buying_Group__r.Name.containsIgnoreCase('TAIT DISTRIBUTORS LTD.')) {
                    if (theLead.Customer_Profile__r.isEmpty() && theLead.Customer_Business_Type__c != CCM_Constants.CUSTOMER_BUSINESS_TYPE_SERVICE) {
                        newItem.addError(Label.CCM_Customer_Profile_Required_For_Prospect_Conversion);
                        break;
                    }
                }
                
                spList.addAll(theLead.Sales_Program__r);

                for (Attachment_Management__c am : theLead.Attachment_Management__r) {
                    am.Customer__c = newItem.ConvertedAccountId;
                }
                amList.addAll(theLead.Attachment_Management__r);

                /*if (theLead.Business_Volumes__r != null && theLead.Business_Volumes__r.size() > 0){
                    for (Business_Volume__c bv : theLead.Business_Volumes__r){
                        bv.Customer__c = newItem.ConvertedAccountId;
                    }
                    bvList.addAll(theLead.Business_Volumes__r);
                }*/
                if (theLead.Customer_Profile__r != null && theLead.Customer_Profile__r.size() > 0) {
                    for (Customer_Profile__c cp : theLead.Customer_Profile__r) {
                        cp.recordTypeId = rt.Id;
                        cp.Customer__c = newItem.ConvertedAccountId;
                        cp.Prospect__c = null;
                        cpList.add(cp);
                    }
                    
                }

                for (Account_Address__c aa : theLead.Account_Address__r) {
                    if (aa.Address_Type__c=='Store Location'&&aa.Store_Location__c==null) {
                        newItem.addError('Make sure the Store Location on the Address of the store location type is not empty before converting');
                    }
                    if (aa.ORG_Id__c == 'CCA') {
                        aa.Approval_Status__c = 'Approved';
                    }
                    aa.Customer__c = newItem.ConvertedAccountId;
                    Contact c = new Contact(Id = aa.Contact__c, AccountId = newItem.ConvertedAccountId);
                    if (String.isNotBlank(aa.Contact__c) && !cMap.containsKey(aa.Contact__c)) {
                        cMap.put(aa.Contact__c, c);
                    }
                }
                aaList.addAll(theLead.Account_Address__r);
            }
        }

        List<Account> accList = new List<Account>();
        for (Lead newItem : leadList) {
            if (String.isNotBlank(newItem.ConvertedAccountId)) {
                Account acc = new Account();
                acc.Id = newItem.ConvertedAccountId;
                acc.BillingStreet = newItem.Street;
                acc.BillingCity = newItem.City; 
                acc.BillingState = newItem.State;
                acc.BillingPostalCode = newItem.PostalCode; 
                acc.BillingCountry = newItem.Country;
                accList.add(acc);
            }
        }
        if (accList.size() > 0) {
            update accList;
        }

        if (spList.size() > 0) {
            update spList;
        }

        if (amList.size() > 0) {
            update amList;
        }

        /*if (bvList.size() > 0){
            update bvList;
        }*/
        if (cpList.size() > 0) {
            Update cpList;
        }

        if (cMap.size() > 0) {
            update cMap.values();
        }

        if (aaList.size() > 0){
            update aaList;
        }
    }
/*
    public static void sendEmailToServiceTeamAndCSR(Lead objLead) {
        Set<String> userIdSet = new Set<String>();
        for (GroupMember objGM : [SELECT Id, UserOrGroupId FROM GroupMember WHERE Group.DeveloperName IN ('CSR', 'NA_Service_Team')]) {
            userIdSet.add(objGM.UserOrGroupId);
        }
        Map<String, User> userMap = new Map<String, User>([SELECT Id, Email FROM User WHERE Id IN :userIdSet]);

        List<EmailTemplate> etList = new List<EmailTemplate>([SELECT Id, HtmlValue, Subject FROM EmailTemplate WHERE DeveloperName = 'Service_Customer_Convert_Success']);
        if (etList.isEmpty()) {
            return;
        }

        List<OrgWideEmailAddress> emailAddressList = new List<OrgWideEmailAddress>([SELECT Id FROM OrgWideEmailAddress WHERE DisplayName = 'SFDC Notification']);

        List<Messaging.SingleEmailMessage> emailMessageList = new List<Messaging.SingleEmailMessage>();
        Messaging.SingleEmailMessage emailMessage = new Messaging.SingleEmailMessage();
        if (!emailAddressList.isEmpty()) {
            emailMessage.setOrgWideEmailAddressId(emailAddressList[0].Id);
        } else {
            emailMessage.setSenderDisplayName('SFDC Notification');
        }
        emailMessage.setTargetObjectId(objLead.Id);
        emailMessage.setTreatTargetObjectAsRecipient(false);
        emailMessage.setSaveAsActivity(false);
        emailMessage.setTemplateId(etList[0].Id);
        List<String> emailList = new List<String>();
        for (User objUser : userMap.values()) {
            emailList.add(objUser.Email);
        }
        emailMessage.setToAddresses(emailList);

        emailMessageList.add(emailMessage);
        if (!emailMessageList.isEmpty()) {
            Messaging.sendEmail(emailMessageList);
        }
        if (!Test.isRunningTest()) {
            Account updateAcc = new Account();
            updateAcc.Id = objLead.ConvertedAccountId;
            updateAcc.Traning_Email_Sent__c = true;
            update updateAcc;
        }
    }
*/
    
}