/**
 * Created by gluo006 on 7/16/2019.
 */
({
    init: function (component) {
        var self = this;
        var userInfo = $A.get("$SObjectType.CurrentUser");
        var userId;
        if (userInfo.Id) {
            userId = userInfo.Id;
        }
        var action = component.get('c.setupProductRegistration');
        action.setParams({
            'userId': userId,
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = JSON.parse(response.getReturnValue());
            if (state === 'SUCCESS') {
                if (result.Name) {
                    component.set('v.organizationName', result.Name);
                    component.set('v.customerId', result.CustomerId);
                }
                if(result.customerTypeList) {
                    let customerTypeList = [];
                    customerTypeList.push({
                        'name': '-- None --',
                        'value': ''
                    });
                    result.customerTypeList.forEach(item => {
                        customerTypeList.push({
                            'name': item,
                            'value': item
                        });
                    });
                    component.set('v.customerTypeList', customerTypeList);
                }
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": $A.get("$Label.c.CCM_Portal_Error"),
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    checkCustomerInfoRequiredInputs: function (component) {
        let email = component.get('v.emailAddress');
        let phone = component.get('v.phone');

        let emailMissing = email === '' || email === undefined;
        let phoneOrEmailMissing = phone === '' || phone === undefined
        return {
            'emailmiss': emailMissing,
            'phoneaddressmiss': phoneOrEmailMissing
        };
    },
    getCustomerInfo: function (component) {
        var email = component.get('v.emailAddress');
        var phone = component.get('v.phone');
        var action = component.get("c.customerInfo");
        action.setParams({
            'email': email,
            'phone': phone
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                let customerInfoResult = JSON.parse(response.getReturnValue());
                if (customerInfoResult['result'] === 'success') {
                    let result = customerInfoResult['data'];
                    if (!result) {
                        component.set('v.nameInputDisable', false);
                        component.set('v.endUserId', '');
                    }
                    else {
                        component.set('v.nameInputDisable', true);
                        result = JSON.parse(result);
                        component.set('v.nameInputDisable', true);
                    }

                    if(result.Id) {
                        component.set('v.endUserId', result.Id);
                    }

                    if (result.Id) {
                        component.set('v.endUserId', result.Id);
                    }

                    if (result.FirstName) {
                        component.set('v.firstName', result.FirstName);
                        component.set('v.originalFirstName', result.FirstName);
                    }
                    if (result.LastName) {
                        component.set('v.lastName', result.LastName);
                        component.set('v.originalLastName', result.LastName);
                    }
                    if (result.PersonEmail) {
                        component.set('v.emailAddress', result.PersonEmail);
                    }
                    else if (result.Email__c) {
                        component.set('v.emailAddress', result.Email__c);
                    }
                    if (result.Phone) {
                        component.set('v.phone', result.Phone);
                    }
                    if (result.ShippingStreet) {
                        component.set('v.addressLine1', result.ShippingStreet);
                    }
                    if (result.ShippingPostalCode) {
                        component.set('v.zipPostalCode', result.ShippingPostalCode);
                    }
                    if (result.ShippingState) {
                        component.set('v.state', result.ShippingState);
                    }
                    if (result.ShippingCity) {
                        component.set('v.city', result.ShippingCity);
                    }
                    if (result.ShippingCountry) {
                        component.set('v.country', result.ShippingCountry);
                    }
                    if (result.Customer_Type2__c) {
                        let selectedCustomerType = result.Customer_Type2__c;
                        component.set('v.selectedCustomerType', selectedCustomerType);
                        component.set('v.customerTypeInputDisable', true);
                        if(result.Customer_Type2__c == 'Residential') {
                            component.set('v.organizationNameInputDisable', true);
                        }
                    }
                    if(result.Organization_Name__c) {
                        component.set('v.organizationNameCommercial', result.Organization_Name__c);
                        if(result.Customer_Type2__c == 'Commercial') {
                            component.set('v.organizationNameInputDisable', true);
                        }
                    }
                }
                else if (customerInfoResult['result'] === 'error') {
                    if (customerInfoResult['errmsg']) {
                        component.set('v.emailAddress', '');
                        component.set('v.phone', '');
                        component.set('v.addressLine1', '');
                        component.set('v.zipPostalCode', '');
                        component.set('v.state', '');
                        component.set('v.city', '');
                        component.set('v.country', '');
                        component.set('v.firstName', '');
                        component.set('v.originalFirstName', '');
                        component.set('v.lastName', '');
                        component.set('v.originalLastName', '');
                        component.set('v.endUserId', '');
                        var toastEvt = $A.get("e.force:showToast");
                        toastEvt.setParams({
                            "title": $A.get("$Label.c.CCM_Portal_Error"),
                            "message": customerInfoResult['errmsg'],
                            "type": "error"
                        }).fire();
                    }
                }
            } else {
                component.set('v.endUserId', '');
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    getAddressByZipCode: function(component){
        var zipCode = component.get('v.zipPostalCode');
        var country = component.get('v.country');
        var action = component.get('c.getAddressByCode');
        action.setParams({
            'postalCode': zipCode,
            'country': country
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = response.getReturnValue();
            if (state === 'SUCCESS') {
                result = result.split(',');
                var newCity = result[0];
                var newState = result[1];
                var newCountry = result[2];
                component.set('v.city', newCity);
                component.set('v.state', newState);
                component.set('v.country', newCountry);
            } else {
                $A.get("e.force:showToast").setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                    "message": response.getError()[0].message,
                    "type": "Error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    changeMasterProduct: function (component, productId) {
        var self = this;
        var action = component.get('c.changeMasterProduct');
        action.setParams({
            'productId': productId,//'01t0h000004XO1d',
            'warrantyId': component.get('v.recordId')
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = JSON.parse(response.getReturnValue());
            if (state === 'SUCCESS') {
                if (result.productCode) {
                    if (result.proList) {
                        var len = result.proList.length;
                        for (var i = 0; i < len; i++) {
                            result.proList[i].isRequired = true;
                            if (result.proList[i].surveyId) {
                                component.set('v.surveyId', result.proList[i].surveyId);
                                component.set('v.surveyTitle', result.proList[i].surveyTitle);
                                component.set('v.surveyComments', result.proList[i].surveyComments);
                            }
                        }
                        component.set('v.productListData', result);
                        console.log('test', result.proList);
                    }
                } else {
                    if (result.Message.indexOf('Product Id can not be null') < 0) {
                        self.showToast($A.get("$Label.c.CCM_Portal_Failed"), result.Message);
                    }
                    //component.set('v.productListData', '');
                }
            }
            self.hideEle(component, 'proListSpinner');
        });
        $A.enqueueAction(action);
    },
    checkSNAndUpdateIndicator: function (component) {
        var self = this;
        var brand = component.get('v.brandName');
        var action = component.get('c.checkSNAndUpdateIndicator');
        action.setParams({
            'warrantyBrandName': brand,
            'proListStr': JSON.stringify(component.get('v.productListData')),
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result;
            if (response.getReturnValue() && response.getReturnValue().indexOf('proList') < 0) {
                result = response.getReturnValue();
            } else {
                result = JSON.parse(response.getReturnValue());
            }
            if (state === 'SUCCESS') {
                if (result && result.proList) {
                    component.set('v.productListData', result);
                    self.hideEle(component, 'proListSpinner');
                } else {
                    self.showToast($A.get("$Label.c.CCM_Portal_Failed"), result);
                    return;
                }
                self.hideEle(component, 'proListSpinner');
            } else {
                $A.get("e.force:showToast").setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                    "message": response.getError()[0].message,
                    "type": "Error"
                }).fire();
            }
            self.hideEle(component, 'proListSpinner');
        });
        $A.enqueueAction(action);
    },
    uploadReceipt: function (component, file) {
        var self = this;
        var action = component.get('c.uploadFile');
        // create a FileReader object
        var objFileReader = new FileReader();
        // set onload function of FileReader object
        objFileReader.onload = $A.getCallback(function () {
            var fileContents = objFileReader.result;
            var base64 = 'base64,';
            var dataStart = fileContents.indexOf(base64) + base64.length;

            fileContents = fileContents.substring(dataStart);
            var fileName = file.name;
            action.setParams({
                'fileName': fileName,
                'content': fileContents
            })
            action.setCallback(this, function (response) {
                var state = response.getState();
                var result = JSON.parse(response.getReturnValue());
                if (state === 'SUCCESS') {
                    if (result.Status == 'Success') {
                        component.set('v.uploadFinished', true);
                        component.set('v.uploadFileName', fileName);
                        component.set('v.contentId', result.ContentId);
                    } else {
                        $A.get("e.force:showToast").setParams({
                         "title": $A.get("$Label.c.CCM_Portal_Error"),
                         "message": result.Message,
                         "type": "Error"
                        }).fire();
                    }
                } else {
                    component.set('v.uploadFinished', false);
                    $A.get("e.force:showToast").setParams({
                     "title": $A.get("$Label.c.CCM_Portal_Error"),
                     "message": response.getError()[0].message,
                     "type": "Error"
                    }).fire();
                }
            });
            $A.enqueueAction(action);
            // call the uploadProcess method
            //            self.uploadProcess(component, file, fileContents);
        });

        objFileReader.readAsDataURL(file);
    },
    deleteReceipt: function (component) {
        var action = component.get('c.deleteFile');
        action.setParams({
            'fileId': component.get('v.contentId'),
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = JSON.parse(response.getReturnValue());
            if (state === 'SUCCESS') {
                if (result.Status == 'Success') {
                    component.set('v.uploadFinished', false);
                    $A.get("e.force:showToast").setParams({
                     "title": $A.get("$Label.c.CCM_Portal_Success"),
                     "message": 'Delete successful',
                     "type": "Success"
                    }).fire();
                } else {
                    $A.get("e.force:showToast").setParams({
                     "title": $A.get("$Label.c.CCM_Portal_Error"),
                     "message": result.Message,
                     "type": "Error"
                    }).fire();
                }
            } else {
                component.set('v.uploadFinished', false);
                $A.get("e.force:showToast").setParams({
                 "title": $A.get("$Label.c.CCM_Portal_Error"),
                 "message": response.getError()[0].message,
                 "type": "Error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    onSaveRecord: function (component) {
        var self = this;
        if (!component.get('v.uploadFinished')) {
            component.set('v.lostReceipt', true);
        }
        if (component.get('v.emailAddress') == "") {
            component.set('v.emailAddress', null);
        }
        var registrationInfo = {
            FirstName: component.get('v.firstName'),
            LastName: component.get('v.lastName'),

            PersonEmail: component.get('v.emailAddress'),
            ShippingStreet: component.get('v.addressLine1'),
            Phone: component.get('v.phone'),
            ShippingPostalCode: component.get('v.zipPostalCode'),
            ShippingCity: component.get('v.city'),
            ShippingState: component.get('v.state'),
            purchaseDate: component.find('purchaseDate').get('v.value'),
            customerType: component.get('v.selectedCustomerType'),
            organizationName: component.get('v.organizationNameCommercial'),
            brand: component.get('v.brandName'),
            masterProduct: component.get('v.masterProductObj').Id,
            purchasePlace: component.get('v.purchasePlaceObj').Name,
            purchaseUseType: component.find('purchaseUseType').get('v.value'),
            proListStr: JSON.stringify(component.get('v.productListData')),
            lostReceipt: component.get('v.lostReceipt'),
            ContentId: component.get('v.contentId'),
            EndUserId: component.get('v.endUserId')
        };
        registrationInfo = JSON.stringify(registrationInfo);
        var action = component.get('c.SaveWarranty');
        action.setParams({
            'proListStr': registrationInfo,
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result;
            result = JSON.parse(response.getReturnValue());
            if (state === 'SUCCESS') {
                if (result && result.message) {
                    self.showToast($A.get("$Label.c.CCM_Portal_Failed"), result.message);
                    component.set('v.firstSave', true);
                    self.hideEle(component, 'spinner');
                } else {
                    self.showToast($A.get("$Label.c.CCM_Portal_Success"), $A.get("$Label.c.CCM_Portal_SubmitSuccessfully"));
                    self.hideEle(component, 'spinner');
                    if (component.get('v.surveyId') == null || component.get('v.surveyId') == '') {
                        window.location.href = '/s/servicehome'
                    } else {
                        if (component.find('purchaseUseType').get('v.value') === 'Residential') {
                            component.set('v.isShowSurveyConfirmation', true);
                            component.set('v.warrantyId', result.warrantyId);
                        } else {
                            window.location.href = '/s/servicehome';
                        }
                    }
                }
            } else {
                component.set('v.firstSave', true);
                self.hideEle(component, 'spinner');
                $A.get("e.force:showToast").setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                    "message": response.getError()[0].message,
                    "type": "Error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    getValidation: function (component) {
        var valid = true;

        let fieldCheckResult = this.checkCustomerInfoRequiredInputs(component);
        if (fieldCheckResult['emailmiss'] && fieldCheckResult['phoneaddressmiss']) {
            valid = false;
            this.getElementRequiredError(component, 'emailAddress');
            this.getElementRequiredError(component, 'phone');
            valid = valid && component.find("phone").get("v.validity");
        }
        // valid = valid && this.getElementRequiredError(component, 'emailAddress');
        valid = valid && this.getElementRequiredError(component, 'firstName');
        valid = valid && this.getElementRequiredError(component, 'lastName');
        valid = valid && this.getElementRequiredError(component, 'purchaseDate');
        valid = valid && this.getElementRequiredError(component, 'brand');
        valid = valid && this.getElementRequiredError(component, 'masterProduct');
        valid = valid && this.getElementRequiredError(component, 'purchasePlace');
        valid = valid && this.getElementRequiredError(component, 'purchaseUseType');
        //valid = valid && this.checkFirstNameandLastNameChange(component);
        valid = valid && this.getElementRequiredError(component, 'customerType');
        let selectedCustomerType = component.get('v.selectedCustomerType');
        if(selectedCustomerType === 'Commercial') {
            valid = valid && this.getElementRequiredError(component, 'organizationName');
        }
        return valid;
    },
    checkDate: function (component, ele, compare_date, compare) {
        let element = component.find(ele);
        let datevalue = element.get('v.value');
        let valueText = component.find(ele + '-error-value');
        let today = new Date();
        let valid = new Date(datevalue).getTime() < today.setHours(0, 0, 0, 0);
        if (valid) {
            $A.util.removeClass(element, 'field-error');
            if (valueText) {
                $A.util.addClass(valueText, 'slds-hide');
            }
        }
        else {
            $A.util.addClass(element, 'field-error');
            if (valueText) {
                $A.util.removeClass(valueText, 'slds-hide');
            }
        }
        return valid;
    },
    checkFirstNameandLastNameChange: function (component) {
        let valid = true;
        let firstandlastname = {
            "firstName": "originalFirstName",
            "lastName": "originalLastName"
        };
        for (const key in firstandlastname) {
            if (component.get(firstandlastname[key]) != null && component.get(key) != null) {
                let element = component.find(key);
                let errorText = component.find(key + '-error');
                if (component.get(firstandlastname[key]) != component.get(key)) {
                    $A.util.addClass(element, 'field-error');
                    if (errorText) {
                        $A.util.removeClass(requiredText, 'slds-hide');
                    }
                    valid = true;
                }
                else {
                    $A.util.removeClass(element, 'field-error');
                    if (errorText) {
                        $A.util.addClass(errorText, 'slds-hide');
                    }
                }
            }
        }
        return valid;
    },
    getElementRequiredError: function (component, ele, fieldType) {
        var element = component.find(ele);
        var requiredText = component.find(ele + '-error-required');
        var val = '';
        if (ele.indexOf('masterProduct') > -1) {
            val = component.get('v.masterProductObj').Id;
        } else if (ele.indexOf('purchasePlace') > -1) {
            val = component.get('v.purchasePlaceObj').Id;
        } else {
            val = element.get('v.value');
        }
        if (val !== undefined && val !== null) {
            val = val.trim();
        }
        var valid;
        //validate the email format
        if (ele.toUpperCase().indexOf('EMAIL') > -1) {
            valid = (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(val));
        } else {
            if (val == '--None--') {
                valid = false;
            } else {
                valid = !!val;
            }
        }
        if (valid) {
            $A.util.removeClass(element, 'field-error');
            if (requiredText) {
                $A.util.addClass(requiredText, 'slds-hide');
            }
        } else {
            $A.util.addClass(element, 'field-error');
            if (requiredText) {
                $A.util.removeClass(requiredText, 'slds-hide');
            }
        }
        return valid;
    },
    showToast: function(status, message){
         var toastEvent = $A.get('e.force:showToast');
        //  add haibo: french
         if(status == $A.get("$Label.c.CCM_Portal_Success")){
              toastEvent.setParams({
                  'title': $A.get("$Label.c.CCM_Portal_Success"),
                  'message': message,
                  'type':'success',
              });
         }else if(status == $A.get("$Label.c.CCM_Portal_Failed")){
              toastEvent.setParams({
                  'title': $A.get("$Label.c.CCM_Portal_Error"),
                  'message': message,
                  'type':'error',
              });
         }
         toastEvent.fire();
    },
    showEle: function (component, ele) {
        $A.util.removeClass(
            component.find(ele),
            "slds-hide"
        );
    },
    hideEle: function (component, ele) {
        $A.util.addClass(
            component.find(ele),
            "slds-hide"
        );
    },

    /**
     * @date 2022/03/13
     * @description call apex class to save survey result
     */
    onSaveSurveyRecord: function (component) {
        let self = this,
            idCustomer = component.get("v.customerId"),
            idSurvey = component.get("v.surveyId"),
            surveyData = component.get("v.surveyData"),
            idWarranty = component.get("v.warrantyId"),
            lstResponse = [];
        const QUESTION_TYPE = {
            FREE_TEXT: "Free Text",
            MULTI_SELECT: "Multi Select",
            SINGLE_SELECT: "Single Select"
        };
        (surveyData || []).forEach((q) => {
            let { Id: questionId, Type__c, Chervon_Survey_Question_Choice__r } = q,
                localResponseList = [],
                localMultiResponse = {};
            (Chervon_Survey_Question_Choice__r || [])
                .filter((c) => c.checked === true || c.checked === undefined)
                .forEach((c) => {
                    let { Id: choiceId, checked, answerText } = c,
                        response = Object.assign({}, { questionId: questionId, result: {} });
                    if (Type__c === QUESTION_TYPE.FREE_TEXT || Type__c === QUESTION_TYPE.SINGLE_SELECT) {
                        response.result = {
                            answer: choiceId,
                            haveManualText: $A.util.isEmpty(answerText) === false,
                            manualSpecialChoice: checked === true && $A.util.isEmpty(answerText) === false ? choiceId : null,
                            manualText: $A.util.isEmpty(answerText) ? null : answerText
                        };
                        localResponseList.push(response);
                    } else if (Type__c === QUESTION_TYPE.MULTI_SELECT && checked === true) {
                        let { result } = localMultiResponse,
                            { answer } = result || {};
                        result = $A.util.isEmpty(result) ? {} : result;
                        result.answer = $A.util.isEmpty(answer) ? choiceId : answer + "," + choiceId;
                        if ($A.util.isEmpty(answerText) === false) {
                            Object.assign(result, {
                                haveManualText: true,
                                manualSpecialChoice: choiceId,
                                manualText: answerText
                            });
                        }
                        Object.assign(localMultiResponse, { questionId, result });
                    }
                });
            if (localMultiResponse && Object.keys(localMultiResponse).length > 0) {
                lstResponse = [...lstResponse, localMultiResponse];
            }
            if (localResponseList && localResponseList.length > 0) {
                lstResponse = [...lstResponse, ...localResponseList];
            }
        });
        let action = component.get("c.saveSurvey");
        action.setParams({
            idSurvey,
            idCustomer,
            idWarranty,
            strResponseJSON: JSON.stringify(lstResponse)
        });
        action.setCallback(this, function (response) {
            let state = response.getState();
            if (state === "SUCCESS") {
                console.log("save survey answer successfully");
                window.location.href = '/s/servicehome';
            } else {
                self.showToast($A.get("$Label.c.CCM_Portal_Failed"), response.getError()[0].message);
            }
        });
        $A.enqueueAction(action);
    },
})