/**
 * <AUTHOR>
 * @date 2024-03-02
 * @description batch job to process shipment interface data
 */
public with sharing class CCM_ProcessShipmentInterfaceBatch implements Database.Batchable<SObject>, Database.AllowsCallouts{
    public String query;
    public CCM_ProcessShipmentInterfaceBatch() {
        this.query = 'SELECT Id FROM ShipmentInterface__c WHERE Return_Code__c = \'NA\'';
    }

    public CCM_ProcessShipmentInterfaceBatch(String query) {
        this.query = query;
    }

    public Database.QueryLocator start(Database.BatchableContext objBC) {
        return Database.getQueryLocator(this.query);
    }

    public void execute(Database.BatchableContext objBC, List<SObject> scope) {
        List<ShipmentInterface__c> shipmentInterfaceList = (List<ShipmentInterface__c>)scope;
        Set<String> shipmentInterfaceIds = new Set<String>();
        for(ShipmentInterface__c si : shipmentInterfaceList) {
            shipmentInterfaceIds.add(si.Id);
        }

        List<ShipmentInterface__c> siList = [SELECT Attribute10__c,Attribute1__c,Attribute2__c,
        Attribute3__c,Attribute4__c,Attribute5__c,Attribute6__c,Attribute7__c,Attribute8__c,
        Attribute9__c,Delivery_Address__c,Delivery_City__c,Delivery_Country__c,Delivery_Postal_Code__c,
        Delivery_State__c,Error_Msg__c,Forecast_Date__c,Order__c,Receipt_Address__c,Receipt_City__c,
        Receipt_Country__c,Receipt_Postal_Code__c,Receipt_State__c,Return_Code__c,Ship_Date__c,
        Ship_Method__c,Ship_OracleID__c,Shipper__c,Tracking_Number__c,WeightUnit__c,Weight__c, 
        (SELECT Item_Quantity_in_Order__c,Item_Quantity_in_Shipment__c,Line_Attribute10__c,
        Line_Attribute1__c,Line_Attribute2__c,Line_Attribute3__c,Line_Attribute4__c,Line_Attribute5__c,
        Line_Attribute6__c,Line_Attribute7__c,Line_Attribute8__c,Line_Attribute9__c,Product__c,
        ShipmentInterface__c,Shipment_Item_OracleID__c,UPC_code__c FROM ShipmentItemInterfaces__r) 
        FROM ShipmentInterface__c WHERE Id IN :shipmentInterfaceIds];

        CCM_ShipmentInterfaceUtil.processDataFromInterface(siList);
    }

    public void finish(Database.BatchableContext objBC) {
        Database.executeBatch(new CCM_ProcessAccountBalanceInterfaceBatch(), 20);
    }
}