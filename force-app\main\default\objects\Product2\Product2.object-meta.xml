<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>Add</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Add</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Add</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>false</enableFeeds>
    <externalSharingModel>ReadWrite</externalSharingModel>
    <profileSearchLayouts>
        <fields>PRODUCT2.NAME</fields>
        <fields>PRODUCT2.CUSTOMER_PRODUCT_ID</fields>
        <fields>Item_Number__c</fields>
        <fields>Brand__c</fields>
        <fields>Source__c</fields>
        <fields>PRODUCT2.RECORD_TYPE</fields>
        <fields>Country_of_Origin__c</fields>
        <fields>Product_Type__c</fields>
        <fields>Kit__c</fields>
        <fields>Warranty_Year__c</fields>
        <profileName>System Administrator</profileName>
    </profileSearchLayouts>
    <recordTypeTrackHistory>true</recordTypeTrackHistory>
    <searchLayouts>
        <customTabListAdditionalFields>PRODUCT2.NAME</customTabListAdditionalFields>
        <customTabListAdditionalFields>PRODUCT2.CUSTOMER_PRODUCT_ID</customTabListAdditionalFields>
        <customTabListAdditionalFields>Country_of_Origin__c</customTabListAdditionalFields>
        <customTabListAdditionalFields>PRODUCT2.DESCRIPTION</customTabListAdditionalFields>
        <customTabListAdditionalFields>PRODUCT2.RECORD_TYPE</customTabListAdditionalFields>
        <lookupDialogsAdditionalFields>PRODUCT2.NAME</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>PRODUCT2.CUSTOMER_PRODUCT_ID</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Product_Type__c</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Country_of_Origin__c</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>PRODUCT2.DESCRIPTION</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Kit__c</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>PRODUCT2.RECORD_TYPE</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>PRODUCT2.ACTIVE</lookupDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>PRODUCT2.NAME</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>PRODUCT2.CUSTOMER_PRODUCT_ID</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>PRODUCT2.RECORD_TYPE</lookupPhoneDialogsAdditionalFields>
        <searchResultsAdditionalFields>PRODUCT2.NAME</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>PRODUCT2.CUSTOMER_PRODUCT_ID</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Item_Number__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Brand_Name__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>PRODUCT2.RECORD_TYPE</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Country_of_Origin__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Product_Type__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Kit__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Warranty_Year__c</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>ReadWrite</sharingModel>
</CustomObject>
