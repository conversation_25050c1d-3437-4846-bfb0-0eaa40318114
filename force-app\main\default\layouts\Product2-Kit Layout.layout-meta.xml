<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Clone</excludeButtons>
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Kit Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>RecordTypeId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Product_Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Brand_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>ProductCode</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ProductModel__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Bare_tool__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Charger__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Battery__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Battery2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Sellable__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Chervon_Survey__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EBS_Category_1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EBS_Category_2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EBS_Category_3__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Sellable_CCA__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CS_Exchange_Rate__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Source__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsActive</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Status__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Master_Product__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Kit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Country_of_Origin__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>UOM__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FilterType__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>For_Website__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Total_Available_Inventory__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Full_Pallet_Quantity__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Item_Number__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EBS_ExternalID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EBS_Description_of_Category_1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EBS_Description_of_Category_2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EBS_Description_of_Category_3__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Brand_Series__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Category Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Category_1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Category_2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Category_3__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Description_of_Category_1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Description_of_Category_2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Description_of_Category_3__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Package Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Depth__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Length__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Width__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Height__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Weight__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Tool_Length__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Tool_Width__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Tool_Height__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Tool_Weight__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Short_description__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SF_Description__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Long_description__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Depth_unit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Dimension_unit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Width_unit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Height_unit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Weight_unit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Product_Weight__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>GrossWeightInclPackage__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>GrossWeightInclPackage_unit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>OverSize__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Warranty Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Warranty_Year__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Repairable__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Warranty_Period__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>PIM_ExternalID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Unique_Key__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExternalID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExternalId</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Materail_ERP_created_time__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Material_ERP_last_modified_time__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedDate__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ChangeRecordType</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>PrintableView</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Share</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <quickActionList>
        <quickActionListItems>
            <quickActionName>FeedItem.MobileSmartActions</quickActionName>
        </quickActionListItems>
    </quickActionList>
    <relatedLists>
        <relatedList>RelatedStandardPriceList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedPricebookEntryList</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <excludeButtons>New</excludeButtons>
        <fields>NAME</fields>
        <fields>Product_Type__c</fields>
        <fields>Product__c</fields>
        <fields>Product_Code__c</fields>
        <relatedList>Kit_Item__c.Kit__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <fields>NAME</fields>
        <fields>Within_Store_Policy__c</fields>
        <fields>Within_Store_Policy_Formula__c</fields>
        <relatedList>Warranty__c.Product__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <excludeButtons>New</excludeButtons>
        <fields>NAME</fields>
        <fields>Sub_storage__c</fields>
        <fields>Available_Inventory__c</fields>
        <fields>Inventory_On_Hand__c</fields>
        <fields>Description__c</fields>
        <relatedList>Storage__c.Product__c</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h0h00000bXiox</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
