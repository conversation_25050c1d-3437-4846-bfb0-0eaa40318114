.THIS td {
    overflow: visible !important; /* 确保悬浮层不会被裁剪 */
}

.THIS .search-container {
    position: relative;
}

.THIS .search-results {
    position: absolute;
    top: 100%; 
    left: 0;
    z-index: 1000;
    background-color: white;
    border: 1px solid #ccc;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    width: 225%;
    max-height: 120px;
    overflow-y: auto;
}

.THIS .search-results ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.THIS .search-results li {
    padding: 5px;
    cursor: pointer; /* 鼠标悬停时显示手型 */
    border-bottom: 1px solid #eee;
}

.THIS .search-results li:hover {
    background-color: #f9f9f9; /* 悬停时背景色 */
}

.THIS .search-results[data-empty="true"] {
    display: none;
}
.THIS li p{
    line-height: 1.5;
    font-size: 13px;
}
.THIS li p span{
    color: #aaa;
}