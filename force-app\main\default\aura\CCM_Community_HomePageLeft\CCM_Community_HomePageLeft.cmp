<aura:component
    access="global"
    controller="CCM_Community_HomePageCtl"
    description="CCM_Community_HomePage"
    implements="forceCommunity:availableForAllPageTypes"
>
    <aura:attribute name="pendingNum" type="Integer" default="0" />
    <aura:attribute name="approvedNum" type="Integer" default="0" />
    <aura:attribute name="awaitingNum" type="Integer" default="0" />
    <aura:attribute name="paymentNum" type="Integer" default="0" />
    <aura:attribute name="contactName" type="String" default="" />
    <aura:attribute name="phoneNum" type="String" default="" />
    <aura:attribute name="emailStr" type="String" default="" />
    <aura:attribute
        name="addressStr"
        type="String"
        default="1203 East Warrenville Road,Naperville,&lt;br /&gt;IL,60563,US"
    />
    <aura:attribute
        name="photoURL"
        type="String"
        default="/docs/component-library/app/images/examples/avatar2.jpg"
    />
    <aura:attribute name="columns" type="List" default="[]" />
    <aura:attribute name="currentOrders" type="List" default="[]" />
    <aura:attribute name="allOrders" type="List" default="[]" />
    <aura:attribute
        name="vfHost"
        type="String"
        default="msdev--steven.lightning.force.com"
    />
    <aura:handler
        name="onGoto"
        event="c:CCM_Community_OrderFilterEvt"
        action="{!c.doSearch}"
    />

    <!-- ORG Code: CCA -->
    <aura:attribute name="isNotCCA" type="Boolean" default="true" />

    <aura:handler name="init" value="{!this}" action="{!c.doInit}" />
    <div class="slds-grid slds-grid_align-space">
        <div class="slds-grid slds-grid--align-center slds-size--1-of-1">
            <div
                class="slds-col slds-wrap ccm_gridSpace leftCon"
                style="width: 100%"
            >
                <article class="slds-card">
                    <div class="slds-grid">
                        <header
                            class="slds-media slds-media_center slds-has-flexi-truncate"
                        >
                            <div class="slds-media__body">
                                <h2
                                    class="slds-card__header-title section-header-title slds-section__title slds-theme--shade"
                                >
                                    <span
                                        class="section-header-title slds-p-horizontal--small slds-truncate"
                                        title="{!$Label.c.CCM_Portal_ToDo}"
                                    >
                                        <span><strong>{!$Label.c.CCM_Portal_ToDo}</strong></span>
                                    </span>
                                </h2>
                            </div>
                        </header>
                    </div>
                    <div
                        class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small"
                    >
                        <div>
                            <aura:if isTrue="{!v.pendingNum > 1}">
                                <span
                                    ><a
                                        class="underline"
                                        href='/s/orderfilter?filterStr={"status":["Draft"]}'
                                        >{!$Label.c.CCM_Portal_YouHave}&nbsp;&nbsp;{!v.pendingNum}&nbsp;&nbsp;{!$Label.c.CCM_Portal_PendingSubmittedOrders}</a
                                    ></span
                                >
                                <aura:set attribute="else">
                                    <span
                                        ><a
                                            class="underline"
                                            href='/s/orderfilter?filterStr={"status":["Draft"]}'
                                            >{!$Label.c.CCM_Portal_YouHave}&nbsp;&nbsp;{!v.pendingNum}&nbsp;&nbsp;{!$Label.c.CCM_Portal_PendingSubmittedOrder}</a
                                        ></span
                                    >
                                </aura:set>
                            </aura:if>
                        </div>
                        <div class="slds-border_bottom" />
                        <div class="border_height">
                            <aura:if isTrue="{!v.paymentNum > 1}">
                                <span
                                    ><a class="red" href="/s/myaccount"
                                        >{!$Label.c.CCM_Portal_YouHave}&nbsp;&nbsp;{!v.paymentNum}&nbsp;&nbsp;{!$Label.c.CCM_Portal_OverduePayments}</a
                                    ></span
                                >
                                <aura:set attribute="else">
                                    <aura:if isTrue="{!v.paymentNum == 1}">
                                        <span
                                            ><a class="red" href="/s/myaccount"
                                                >{!$Label.c.CCM_Portal_YouHave}&nbsp;&nbsp;{!v.paymentNum}&nbsp;&nbsp;{!$Label.c.CCM_Portal_OverduePayments}</a
                                            ></span
                                        >
                                        <aura:set attribute="else">
                                            <span
                                                ><a
                                                    class="underline"
                                                    href="/s/myaccount"
                                                    >{!$Label.c.CCM_Portal_YouHave}&nbsp;&nbsp;{!v.paymentNum}&nbsp;&nbsp;{!$Label.c.CCM_Portal_OverduePayment}</a
                                                ></span
                                            >
                                        </aura:set>
                                    </aura:if>
                                </aura:set>
                            </aura:if>
                        </div>
                    </div>
                </article>
                <div class="slds-item_detail slds-p-top_small iconLinks">
                    <div class="slds-grid slds-color__background_gray-1">
                        <!-- <aura:if isTrue="{!v.isNotCCA}"> -->
                        <div class="slds-size_1-of-3">
                            <a href="/s/productregistration">
                                <lightning:icon
                                    iconName="action:approval"
                                    size="large"
                                    class="slds-icon-utility-check slds-current-color ccm_image1"
                                />
                                <span class="slds-m-top_medium"
                                    >{!$Label.c.CCM_Portal_ProductRegistration}</span
                                >
                            </a>
                        </div>
                        <div class="slds-size_1-of-3">
                            <a href="/s/rebate">
                                <lightning:icon
                                    iconName="utility:moneybag"
                                    size="large"
                                    class="slds-icon-utility-check slds-current-color ccm_image2"
                                />
                                <span class="slds-m-top_medium"
                                    >{!$Label.c.CCM_Portal_RebateProgram}</span
                                >
                            </a>
                        </div>
                        <div class="slds-size_1-of-3">
                            <a href="/s/claim-list">
                                <lightning:icon
                                    iconName="utility:price_book_entries"
                                    size="large"
                                    class="slds-icon-utility-check slds-current-color ccm_image2"
                                />
                                <span class="slds-m-top_medium"
                                    >{!$Label.c.CCM_Portal_SellThroughClaim}</span
                                >
                            </a>
                        </div>
                        <!-- <aura:set attribute="else">
                                <div class="slds-size_1-of-3">
                                    <a href="/s/productregistration">
                                        <lightning:icon
                                            iconName="action:approval"
                                            alternativeText="Product Registration"
                                            size="large"
                                            class="slds-icon-utility-check slds-current-color ccm_image1"
                                        />
                                        <span class="slds-m-top_medium"
                                            >Product Registration</span
                                        >
                                    </a>
                                </div>
                                <div class="slds-size_1-of-3">
                                    <a href="/s/rebate">
                                        <lightning:icon
                                            iconName="utility:moneybag"
                                            size="large"
                                            class="slds-icon-utility-check slds-current-color ccm_image2"
                                        />
                                        <span class="slds-m-top_medium"
                                            >Rebate Program</span
                                        >
                                    </a>
                                </div>
                                <div class="slds-size_1-of-3">
                                    <a href="/s/claim-list">
                                        <lightning:icon
                                            iconName="utility:price_book_entries"
                                            size="large"
                                            class="slds-icon-utility-check slds-current-color ccm_image2"
                                        />
                                        <span class="slds-m-top_medium"
                                            >Sell-Through Claim</span
                                        >
                                    </a>
                                </div>
                            </aura:set> -->
                        <!-- </aura:if> -->
                    </div>
                </div>

                <div class="slds-item_detail">
                    <c:CCM_Community_OrderInvoiceFilterCmp />
                </div>
            </div>
        </div>
    </div>
</aura:component>