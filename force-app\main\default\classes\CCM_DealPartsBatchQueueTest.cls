@isTest
private class CCM_DealPartsBatchQueueTest {
    static testMethod void testMethod1() {
    	List<Integer> slist1 = new List<Integer>();
    	slist1.add(1);
    	List<String> slist2 = new List<String>();
    	slist2.add('1');
        String PIMInfoStr = '{"cacheId":"20191128_014847_0","entityIdentifier":"Article","totalSize":3443,"startIndex":0,"pageSize":1,"rowCount":1,"columnCount":0,"columns":[],"rows":[{"object":{"id":"6054@1","label":"2825312001","entityId":1000},"values":["2825312001","Sealing Arrangement","","","SKIL","No",""]}]}';
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        List<Object> rowsObjs = (List<Object>) PIMInfoObj.get('rows');
        System.enqueueJob(new CCM_DealPartsBatchQueue(slist1,1,'CCM_DealPartsInfoBatch', slist2, '1')); 
        System.enqueueJob(new CCM_DealPartsBatchQueue(slist1, 1, 'CCM_DealKitsAndProductsBatch', slist2, '1'));
        // System.enqueueJob(new CCM_DealPartsBatchQueue(rowsObjs, 1, 'CCM_DealProductsAndPartsBatch'));
        System.enqueueJob(new CCM_DealPartsBatchQueue(slist1,1,'CCM_DealProductsAndPartsBatch',slist2,'1'));
        System.enqueueJob(new CCM_DealPartsBatchQueue(rowsObjs, 1, 'CCM_DealProductsAndDiagramBatch'));
    }
}