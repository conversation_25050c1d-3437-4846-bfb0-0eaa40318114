<aura:component description="CCM_IN_OrderListView" controller="CCM_IN_OrderListViewCtl" implements="forceCommunity:availableForAllPageTypes,force:appHostable,flexipage:availableForAllPageTypes" access="global">

    <aura:attribute name="columns" type="List" default="[]"/>
    <aura:attribute name="allData" type="List" default="[]"/>
    <aura:attribute name="currentData" type="List" default="[]"/>
    <aura:attribute name="filterObj" type="Object"/>
    <aura:attribute name="hasDelegatePermission" type="Boolean" default="false"/>
    <aura:attribute name="poTypeOptions" type="List" default="[]"/>
    <aura:attribute name="statusOptions" type="List" default="[]"/>
    <aura:attribute name="brandOptions" type="List" default="[]"/>
    <aura:attribute name="createdByInput" type="String" default=""/>
    <aura:attribute name="cOwnerInput" type="String" default=""/>
    <aura:attribute name="invoiceNo" type="String" default=""/>

    <!-- 分页相关属性：页号、每页记录数、总记录数 -->
    <aura:attribute name="pageNumber" type="String" default="1"/>
    <aura:attribute name="pageCount" type="String" default="10" />
    <aura:attribute name="totalRecords" type="String" default="0" />
    <aura:handler name="pageChange" event="c:CCM_ListPageFooter_PageChangeEvt" action="{!c.pageChange}" />
    <aura:handler name="pageCountChange" event="c:CCM_ListPageFooter_PageCountChangeEvt" action="{!c.pageCountChange}" />

    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <div class="slds-box slds-theme_default">
        <lightning:spinner aura:id="loading" variant="brand" alternativeText="loading" class="slds-hide"/>
        <lightning:layout multipleRows="true" horizontalAlign="space">
            <lightning:layoutItem size="5" class="slds-p-top_small">
                <lightning:combobox class="ccm_display"
                        name="orderType" 
                        value="{!v.filterObj.poRecordType}" 
                        options="{!v.poTypeOptions}" 
                        label="Order Type"/>
            </lightning:layoutItem>

            <lightning:layoutItem size="5" class="slds-p-top_small">
                <lightning:input name="" label="Customer Name" value="{!v.filterObj.customerName}"/>
            </lightning:layoutItem>

            <lightning:layoutItem size="5" class="slds-p-top_small">
                <lightning:input name="" label="Customer PO Number" value="{!v.filterObj.cutomerPoNum}"/>
            </lightning:layoutItem>

            <lightning:layoutItem size="5" class="slds-p-top_small">
                <lightning:input name="" label="Account Number" value="{!v.filterObj.accountNumber}"/>
            </lightning:layoutItem>

            <lightning:layoutItem size="5" class="slds-p-top_small">
                <lightning:input name="" label="Order Number in SF" value="{!v.filterObj.orderNumber}"/>
            </lightning:layoutItem>

            <lightning:layoutItem size="5" class="slds-p-top_small">
                <lightning:input name="" label="Order Number in OMS" value="{!v.filterObj.orderOracleId}"/>
            </lightning:layoutItem>

            <lightning:layoutItem size="5" class="slds-p-top_small">
                <c:CCM_AutoMatchPickList 
                            objectType="User" 
                            inputValue="{!v.cOwnerInput}"
                            labelField="Name"
                            filterCondition=''
                            fieldList="UserName,Name,Alias"
                            value="{!v.filterObj.customerOwner}"
                            placeholder="Select"
                            label="Customer Owner"/>
            </lightning:layoutItem>

            <lightning:layoutItem size="5" class="slds-p-top_small">
                <lightning:input name="" label="PO Number" value="{!v.filterObj.poNumber}"/>
            </lightning:layoutItem>

            <lightning:layoutItem size="5" class="slds-p-top_small">
                <c:CCM_MultipleSelect   label="Brand Name"
                                        options="{! v.brandOptions}"
                                        value="{! v.filterObj.brand}"
                                        aura:id='brand'/>
            </lightning:layoutItem>

            <lightning:layoutItem size="5" class="slds-p-top_small">
                <lightning:layout>
                    <lightning:layoutItem size="5">
                        <lightning:input type="date" class="date-box-item" name="submitFrom"  label="Submit Date" value="{!v.filterObj.submitDateMin}"/>
                    </lightning:layoutItem>
                    <lightning:layoutItem size="2">
                    <div class="date-box-b">——</div>
                </lightning:layoutItem>
                <lightning:layoutItem size="5">
                    <lightning:input type="date" class="date-box-item slds-p-top--xx-small" name="submitTo" value="{!v.filterObj.submitDateMax}"/>
                    </lightning:layoutItem>
                </lightning:layout>
            </lightning:layoutItem>

            <lightning:layoutItem size="5" class="slds-p-top_small">
                <c:CCM_MultipleSelect   label="Order Status"
                                        options="{! v.statusOptions}"
                                        value="{! v.filterObj.status}"
                                        aura:id='status'/>
            </lightning:layoutItem>
            
            <lightning:layoutItem size="5" class="slds-p-top_small">
                <lightning:input name="" label="Model #" value="{!v.filterObj.model}"/>
            </lightning:layoutItem>

            <lightning:layoutItem size="5" class="slds-p-top_small">
                <c:CCM_AutoMatchPickList 
                            objectType="Invoice__c" 
                            inputValue="{!v.invoiceNo}"
                            labelField="Name"
                            labelField1="Invoice_Number__c"
                            filterCondition=''
                            fieldList="Name,Invoice_Number__c,Invoice_OracleID__c"
                            value="{!v.filterObj.invoiceId}"
                            placeholder="Select"
                            label="Invoice #"/>
            </lightning:layoutItem>

            <lightning:layoutItem size="5" class="slds-p-top_small">
                <lightning:input name="" label="Ship-to city or state" value="{!v.filterObj.shipto}"/>
            </lightning:layoutItem>

            <lightning:layoutItem size="5" class="slds-p-top_small">
                <lightning:input name="" label="Ship to Name" value="{!v.filterObj.shiptoName}"/>
            </lightning:layoutItem>

            <lightning:layoutItem size="5" class="slds-p-top_small">
                <c:CCM_AutoMatchPickList 
                            objectType="User" 
                            inputValue="{!v.createdByInput}"
                            labelField="Name"
                            filterCondition=''
                            fieldList="UserName,Name,Alias"
                            value="{!v.filterObj.createdById}"
                            placeholder="Select"
                            label="Created By"/>
            </lightning:layoutItem>

            <lightning:layoutItem size="5" class="slds-p-top_small">
            </lightning:layoutItem>
        </lightning:layout>
        

        <div class="slds-m-top--large slds-m-bottom--large">
            <lightning:layout horizontalAlign="center">
                <lightning:layoutItem >
                    <lightning:button variant="brand" label="Search" onclick="{! c.handleSearch }"/>
                    <lightning:button  variant="brand" label="Reset" onclick="{!c.doReset}"/>
                </lightning:layoutItem>
            </lightning:layout>

            <c:CCM_DataTable columns="{!v.columns}" data="{!v.currentData}" />
            <div class="slds-m-top--large slds-m-bottom--large">
                <aura:renderIf isTrue="{! v.totalRecords != 0}">
                    <c:CCM_ListPageFooter totalRecords="{! v.totalRecords}" />
                </aura:renderIf>
            </div>
        </div>
    </div>
</aura:component>