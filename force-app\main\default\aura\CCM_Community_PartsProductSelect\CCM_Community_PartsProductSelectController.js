/**
 * Created by gluo006 on 9/25/2019.
 */
({
    doInit:function(component, event, helper){
        var isHistoryProduct = $A.get("$Label.c.Is_History_Product");
        var defaultCondition = "";
        // false 意味着 item switch to model
        if (isHistoryProduct == 'false') {
            defaultCondition = '[{"Value":"false","FieldName":"Is_History_Product__c","Condtion":"="},{"Value":"true","FieldName":"IsActive","Condtion":"="},{"Value":"PIM","FieldName":"Source__c","Condtion":"="},{"Value":"Parts","FieldName":"RecordType.Name","Condtion":"="},{"Value":"true","FieldName":"Sellable__c","Condtion":"="}]';
        } else {
        // true 意味着 item switch to model 回滚
            defaultCondition = '[{"Value":"true","FieldName":"IsActive","Condtion":"="},{"Value":"PIM","FieldName":"Source__c","Condtion":"="},{"Value":"Parts","FieldName":"RecordType.Name","Condtion":"="},{"Value":"true","FieldName":"Sellable__c","Condtion":"="}]';
        }
        
        component.set('v.prodCondition', defaultCondition);
        
        var host = window.location.origin;
        component.set('v.vfHost', host);
        if (component.get('v.orderItemList').length == 0){
            component.set('v.quotation.Total_Quantity__c', '0');
            component.set('v.quotation.Product_Price__c', 0.00);
        }else{
            for(var i = 0; i<component.get('v.orderItemList').length;i++){
                // fix,napoleon,23-2-17,before using `.Product__r`, need to juge status of empty.  
                if (component.get('v.orderItemList')[i].Product__r) {
                    component.get('v.orderItemList')[i].Name = component.get('v.orderItemList')[i].Product__r.Name;    
                }
                if(component.get('v.orderItemList')[i].Product__r && component.get('v.orderItemList')[i].Product__r.ProductCode){
                    component.get('v.orderItemList')[i].Item_Number__c = component.get('v.orderItemList')[i].Product__r.ProductCode + "";
                }
                component.get('v.orderItemList')[i].Unit_Price__c = component.get('v.orderItemList')[i].Unit_Price__c + "";

            }
        }
        if (component.get('v.brand')){
            component.set('v.disableBtn', false);
        }
        helper.getData(component);
    },
    highLightSequenceNo: function(component){
        var sequenceNo = component.find('sequenceNo').get('v.value');
        var explosiveDataList = component.get('v.explosiveDataList');
        var partsIndex, selectedSequenceNo;
        for(var i=0; i<explosiveDataList.length; i++){
            explosiveDataList[i].highLight = false;
            if(explosiveDataList[i].ExplosionID__c.includes(sequenceNo)){
              explosiveDataList[i].highLight = true;
              partsIndex = i;
              if(i>3){
                  selectedSequenceNo = explosiveDataList[i-3].ExplosionID__c;
              }
            }
        }
        if(selectedSequenceNo){
            document.getElementById(selectedSequenceNo).scrollIntoView();
        }
        component.set('v.explosiveDataList', explosiveDataList)
    },
    backToTop: function(component){
        document.getElementById('top').scrollIntoView();
    },
    rowFocus:function(component, event, helper) {
        var indexRow = event.currentTarget.id;
        component.set('v.operationRow', indexRow);
    },
    addItem:function(component, event, helper){
        var orderItemList = component.get('v.orderItemList');
        var newOrderItem =
        {
            "Product__c":'',
            "Brand__c":'',
            'Model__c':'',
            'Name': '',
            "Quantity__c":'1',
            'Unit_Price__c': '0.00',
            "Sub_Total__c":0.00
        };
        orderItemList.push(newOrderItem);

        component.set('v.orderItemList', orderItemList);
    },
    onSelectProd:function(component, event, helper){
        component.set('v.productPriceRefreshComplete', false);
        var productId = event.getSource().get('v.value');

        // `component.get('v.operationRow')` is the index of the new row which adding item instead of `event.getSource().get('v.indexnum')`;
        var index = component.get('v.operationRow');

        if(!productId){
            component.set('v.productPriceRefreshComplete', true);
            return;
        }
        var action = component.get("c.getPriceBook");
        action.setParams({
            "prodId": productId,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log('state--->'+ state);
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                if (results){
                    var data = JSON.parse(results);
                    console.log("parts_selectedprod:", data);
                    if (data){
                        // throw error when selected product's priceBookEntry is invalid;
                        var orderItemList = component.get('v.orderItemList');
                        if(!data.priceBookEntry.Id){
                            var noPriceBookEvent = $A.get("e.force:showToast");
                            noPriceBookEvent.setParams({
                                "title": $A.get("$Label.c.CCM_Portal_Error"),
                                "message": $A.get("$Label.c.CCM_Portal_NoPriceBookEntry"),
                                "type": "Error"
                            });
                            noPriceBookEvent.fire();
                            let deleteIndex = component.get('v.operationRow');
                            if(deleteIndex !== undefined){
                                orderItemList.splice(deleteIndex, 1);
                                component.set('v.orderItemList', orderItemList);
                                helper.calculateTotal(component);
                            }
                            return;
                        }

                        component.set('v.priceBookEntry', data.priceBookEntry);
                        component.set('v.product', data.product);

                        if (data.product){
                            var repeatParts = false;
                            var i = 0
                            orderItemList.forEach(function(item){
                                if(item.Item_Number__c == data.product.ProductCode && i != index ){
                                    repeatParts = true;
                                }
                                   i+=1;
                            });

                            if(repeatParts){
                                var toastEvent = $A.get("e.force:showToast");
                                toastEvent.setParams({
                                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                                    "message": $A.get("$Label.c.CCM_Portal_parthasbeenadded"),
                                    "type": "Error"
                                });
                                toastEvent.fire();
                                orderItemList.splice(index, 1);
                                component.set('v.orderItemList', orderItemList);
                                return;
                            }
                            orderItemList[index].Brand_Name__c = data.product.Brand_Name__c;
                            orderItemList[index].Item_Number__c = data.product.ProductCode;
                            orderItemList[index].ProductCode =  data.product.ProductCode;
                            orderItemList[index].Name = data.product.Name;
                        }else{
                            orderItemList[index].Brand_Name__c = '';
                            orderItemList[index].Item_Number__c = '';
                            orderItemList[index].ProductCode = '';
                        }
                        if(data.priceBookEntry.UnitPrice){
                            orderItemList[index].Current_Price__c = data.priceBookEntry.UnitPrice.toFixed(2);
                        }
                        var unitPrice = data.priceBookEntry.UnitPrice;
                        if(unitPrice){
                            orderItemList[index].Current_Price__c = unitPrice.toFixed(2);
                            orderItemList[index].Unit_Price__c = unitPrice.toFixed(2);
                            var quantity = orderItemList[index].Quantity__c;
                            var subtotal = unitPrice * quantity;
                            orderItemList[index].Sub_Total__c = subtotal.toFixed(2);
                        }else{
                            orderItemList[index].Current_Price__c = '0.00';
                            orderItemList[index].Unit_Price__c = '0.00';
                            orderItemList[index].Sub_Total__c = '0.00';
                        }
                        component.set('v.orderItemList', orderItemList);
                    }
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert($A.get("$Label.c.CCM_Portal_Error") + ': ' + errors[0].message);
                    }
                } else {
                    alert($A.get("$Label.c.CCM_Portal_ErrorTips"));
                }
            }

            helper.calculateTotal(component);
            component.set('v.productPriceRefreshComplete', true);
        });
        $A.enqueueAction(action);
    },
    handleDeleteAndCalculate:function(component, event, helper){
        var orderItemList = component.get('v.orderItemList');
        if(event.getParam('index')){
            //the index of array begins 0
            var index = Number(event.getParam('index')) - 1; 
            orderItemList.splice(index, 1);
            component.set('v.orderItemList', orderItemList);
        }
        if(event.getParam('calculatePriceAndQuantity')){
            helper.calculateTotal(component);
        }
    },
    doSave : function(component, event, helper){
        if(helper.productPriceUpdateNotComplete(component)) {
            return;
        }
        // add,napoleon,can not add empty order item ;
        if(helper.checkProductItemLine(component, event, helper)){
            helper.doSaveAction(component, event, true);
        } 
        // end
    },
    nextStep: function(component, event, helper){
        if(helper.productPriceUpdateNotComplete(component)) {
            return;
        }
        // update,napoleon,check product item line, if there is an empty line, alter error before next step,stop next action.
        if(helper.checkProductItemLine(component, event, helper)){
            helper.doSaveAction(component, event, false);
        }
    },
    calculateSubTotal: function(component, event, helper){
        var orderItemList = component.get('v.orderItemList');
        if(orderItemList && orderItemList.length > 0){
            var index = event.getSource().get('v.name');
            console.log('index',index);
            // since typing `quantity` in parts order select in external,`subtotal` hidden.`orderItemList[index].Unit_Price__c· instead of `orderItemList[index].Current_Price__c`
            var unitPrice = Number(orderItemList[index].Unit_Price__c);
            var quantity = orderItemList[index].Quantity__c;
            var subtotal = unitPrice * quantity;
            orderItemList[index].Sub_Total__c = subtotal.toFixed(2);
            component.set('v.orderItemList', orderItemList);
        }

        helper.calculateTotal(component);
    },
    handleDelete: function(component, event, helper){
        var orderItemList = component.get('v.orderItemList');
        var deleteIndex = component.get('v.operationRow');/*Number(event.target.getAttribute('data-index'));*/
        if(deleteIndex !== undefined){
            orderItemList.splice(deleteIndex, 1);
            component.set('v.orderItemList', orderItemList);
            helper.calculateTotal(component);
        }
    },
    onClickModalCancel: function(component){
        $A.util.addClass(
              component.find('boomModal'),
              "slds-hide"
        );
    },
    openModal: function(component, event, helper){
        component.set('v.activeIndex', '');
        $A.util.removeClass(
              component.find('boomModal'),
              "slds-hide"
        );
        var mySwiper = new Swiper ('.swiper-container', {
            // Optional parameters
            direction: 'horizontal',
            loop: false,
            observer: true,
            observeParents: true,
            queueEndCallbacks: true,
            // Navigation arrows
            navigation: {
              nextEl: '.swiper-button-next',
              prevEl: '.swiper-button-prev',
            }
        });
    },
    changeSlide: function(component, event, helper){

    },
    afterScriptsLoaded: function(component, event, helper){

    },
    addToCartSingle: function(component, event, helper){
        var index = event.target.getAttribute('data-index');
        var explosiveDataList = component.get('v.explosiveDataList');
        var orderItemList = component.get('v.orderItemList');
        explosiveDataList[index].Quantity__c = '1';
        if(explosiveDataList[index].Parts__r.Current_Price__c){
            explosiveDataList[index].Unit_Price__c = explosiveDataList[index].Parts__r.Current_Price__c.toFixed(2);
            explosiveDataList[index].Current_Price__c = explosiveDataList[index].Parts__r.Current_Price__c.toFixed(2);
            explosiveDataList[index].Sub_Total__c = explosiveDataList[index].Parts__r.Current_Price__c;
        }
        explosiveDataList[index].Brand_Name__c = explosiveDataList[index].Parts__r.Brand_Name__c;
        explosiveDataList[index].Name = explosiveDataList[index].Parts__r.Name;
        explosiveDataList[index].ProductCode = explosiveDataList[index].Product__r.ProductCode;
        explosiveDataList[index].Item_Number__c = explosiveDataList[index].Parts__r.ProductCode;

        orderItemList.push(explosiveDataList[index]);
        component.set('v.orderItemList', orderItemList);
        $A.util.addClass(
              component.find('boomModal'),
              "slds-hide"
        );
        helper.calculateTotal(component);
    },
    addSelectedToCart: function(component, event, helper){
        var explosiveDataList = component.get('v.explosiveDataList');
        var orderItemList = component.get('v.orderItemList');
        var selectedParts = [];
        var isOneChecked = false;
        let isValid = helper.validateSellable(explosiveDataList);
        if(!isValid) {
            return;
        }
        let partsHasNoPrice = [];
        explosiveDataList.forEach(function(item){
            if(item.isSelect){
                if(!item.Parts__r.Current_Price__c) {
                    partsHasNoPrice.push(item.Parts__r.ProductCode);
                }
                isOneChecked = true;
                item.Quantity__c = item.Quantity__c ? item.Quantity__c : '1';
                item.ProductCode = item.Product__r.ProductCode;
                item.Item_Number__c = item.Parts__r.ProductCode;
                item.Brand_Name__c = item.Parts__r.Brand_Name__c;
                // add haibo: french
                if ($A.get("$Label.c.CCM_Portal_Language") == 'fr') {
                    item.Name = item.Parts__r.Product_Name_French__c;
                } else {
                    item.Name = item.Parts__r.Name;
                }
                item.Current_Price__c = Number(item.Parts__r.Current_Price__c).toFixed(2);
                item.Unit_Price__c = Number(item.Parts__r.Current_Price__c).toFixed(2);
                item.Sub_Total__c = Number(item.Parts__r.Current_Price__c).toFixed(2);
                orderItemList.push(item);
            }
        })
        if(!isOneChecked){
            return;
        }
        if(partsHasNoPrice.length > 0) {
            let noPriceBookEvent = $A.get("e.force:showToast");
            noPriceBookEvent.setParams({
                "title": "Error!",
                "message": partsHasNoPrice.join(", ") + ' has no price.',
                "type": "Error"
            });
            noPriceBookEvent.fire();
            return;
        }
        component.set('v.orderItemList', orderItemList);
        $A.util.addClass(
              component.find('boomModal'),
              "slds-hide"
        );
        helper.calculateTotal(component);
        component.set('v.productPriceRefreshComplete', true);
    },

    cancel: function(component){
        window.location.href = window.location.origin + '/s/orderinformation';
    },
    confirmChange: function(component, event, helper) {
        var action = component.get("c.savePurchaseOrder");
        action.setParams({
            "poInfo": JSON.stringify(component.get('v.po')),
            "poItemInfos": JSON.stringify(component.get('v.orderItemList')),
            "customerId": component.get('v.customerId')
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                results = JSON.parse(results);
                if (!results.isSuccess) {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": $A.get("$Label.c.CCM_Portal_Error"),
                        "message": results.errorMsg,
                        "type": "Error"
                    });
                    toastEvent.fire();
                } else {
                    component.set("v.showEditModal", false);
                    component.set("v.orderItemList", []);
                }
            } else {
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                    "message": response.getError(),
                    "type": "Error"
                });
                toastEvent.fire();
            }
        });
        $A.enqueueAction(action);
    },
    refreshFlag: function(component, event, helper){
        var searchExplosiveProductCondition = "";
        var isHistoryProduct = $A.get("$Label.c.Is_History_Product");
        // false 意味着 item switch to model
        if (isHistoryProduct == 'false') {
            searchExplosiveProductCondition = '[{"Value":"false","FieldName":"Is_History_Product__c","Condtion":"="},{"Value":"true","FieldName":"IsActive","Condtion":"="},{"Value":"(\'{1}\')","FieldName":"Brand_Name__c","Condtion":"IN"},{"Value":"0120h000000Un4LAAS","FieldName":"RecordTypeId","Condtion":"="},{"Value":"PIM","FieldName":"Source__c","Condtion":"="},{"value":"(\'CS\',\'FC\')","FieldName":"FilterType__c","Condtion":"NOT IN"}]';
        } else {
        // true 意味着 item switch to model 回滚
            searchExplosiveProductCondition = '[{"Value":"true","FieldName":"IsActive","Condtion":"="},{"Value":"(\'{1}\')","FieldName":"Brand_Name__c","Condtion":"IN"},{"Value":"0120h000000Un4LAAS","FieldName":"RecordTypeId","Condtion":"="},{"Value":"PIM","FieldName":"Source__c","Condtion":"="},{"value":"(\'CS\',\'FC\')","FieldName":"FilterType__c","Condtion":"NOT IN"}]';
        }
        searchExplosiveProductCondition = searchExplosiveProductCondition.replace('{1}', component.get('v.explosiveBrand'));

        component.set('v.searchExplosiveProductCondition', searchExplosiveProductCondition);
    },
    getVersion: function(component, event, helper){
        var productId = event.getSource().get('v.value');
        component.set('v.explosiveProductId', productId);
        helper.GenerateVersionList(component, productId);
    },
    //explosive search
    handleSearch: function(component, event, helper){
        helper.searchParts(component);
    },
    handleUploadFinish: function(component, event, helper) {
        // const productInfos = component.find('uploadCmp').productInfos;
        const productInfosStr = event.getParam('data');
        let productInfos = JSON.parse(productInfosStr);
        let orderItemList = component.get('v.orderItemList');
        productInfos.forEach(item => {
            orderItemList.push(item);
        });
        component.set('v.orderItemList', orderItemList);
        component.set('v.productPriceRefreshComplete', true);
        helper.calculateTotal(component);
    }
})