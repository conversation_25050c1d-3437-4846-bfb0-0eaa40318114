({
    getInitDataHelper : function(component, userId, programId) {
        var self = this;
        var action = component.get("c.getInitData");

        action.setParams({
            'userId': userId,
            'programId': programId
        });
        self.showEle(component, 'spinner');
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === 'SUCCESS' ) {
                var auraResponse = response.getReturnValue();
                if(auraResponse.code === 200 && !$A.util.isEmpty(auraResponse.data)){
                    // add haibo: french
                    let statusList = [
                        {
                            en: 'Approved',
                            fr: 'Approuvé'
                        },
                        {
                            en: 'Draft',
                            fr: 'Brouillon'
                        },
                        {
                            en: 'Pending Approval',
                            fr: 'En attente d’approbation'
                        },
                        {
                            en: 'Rejected',
                            fr: 'Rejeté'
                        }
                    ];
                    if ($A.get("$Label.c.CCM_Portal_Language") == 'fr') {
                        statusList.forEach((item)=>{
                            if (auraResponse.data.fleetClaim && auraResponse.data.fleetClaim.approvalStatus == item.en) {
                                auraResponse.data.fleetClaim.approvalStatusFrench = item.fr
                            }
                        })
                    }
                    component.set('v.fleetProgramRule', auraResponse.data.fleetProgramRule);
                    component.set('v.kitId2WarrantyItemsInitMap', auraResponse.data.kitId2WarrantyItemsInitMap);
                    component.set('v.kitId2MsrpMap', auraResponse.data.kitId2MsrpMap);
                    component.set('v.readonly', false);
                    component.set('v.step', 1);
                    component.set('v.fleetClaim', auraResponse.data.fleetClaim);
                }else{
                    self.showToast(null, auraResponse.message, "error");
                }
            }else{
                self.showToast(null, JSON.stringify(response.getError()), "error");
            }
            self.hideEle(component, 'spinner');
        });
        $A.enqueueAction(action);
    },
    getFleetClaimDataHelper : function(component, recordId) {
        var self = this;
        var action = component.get("c.getFleetClaimData");
        action.setParams({
            'recordId': recordId
        });
        self.showEle(component, 'spinner');
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === 'SUCCESS' ) {
                var auraResponse = response.getReturnValue();
                if(auraResponse.code === 200 && !$A.util.isEmpty(auraResponse.data)){
                    component.set('v.fleetClaim', auraResponse.data.fleetClaim);
                    component.set('v.fleetProgramRule', auraResponse.data.fleetProgramRule);
                    component.set('v.kitId2WarrantyItemsInitMap', auraResponse.data.kitId2WarrantyItemsInitMap);
                    component.set('v.kitId2MsrpMap', auraResponse.data.kitId2MsrpMap);
                    self.checkReadonly(component, auraResponse.data.fleetClaim);
                    component.set('v.step', 1);
                }else{
                    self.showToast(null, auraResponse.message, "error");
                }
            }else{
                self.showToast(null, JSON.stringify(response.getError()), "error");
            }
            self.hideEle(component, 'spinner');
        });
        $A.enqueueAction(action);
    },
    initWarrantyDataHelper: function(component) {
        var fakeIdList = component.get("v.fakeIdList");
        var kitId2WarrantyItemsInitMap = component.get("v.kitId2WarrantyItemsInitMap");//一个kit对应的 warranty Items 的基础数据

        var fleetItemList = component.get("v.fleetClaim.fleetItemList");//fleetItemList 列表

        var index = 0;
        // console.log('initWarrantyDataHelper');

        for (var i = 0; i < fleetItemList.length; i++) {
            //已分配数量
            var warrantyExist = fleetItemList[i].warrantyList.length;
            if($A.util.isEmpty(warrantyExist)){
                warrantyExist = 0;
            }
            //填写的数量
            var qtyPurchased = fleetItemList[i].qtyPurchased;
            if($A.util.isEmpty(qtyPurchased) || qtyPurchased < 0){
                qtyPurchased = 0;
            }
            //防止JSON解析出错
            fleetItemList[i].qtyPurchased = qtyPurchased;
            //填写数量大于已分配的数量
            if(qtyPurchased >= 1 && qtyPurchased > warrantyExist){
                for (var j = 0; j < qtyPurchased - warrantyExist; j++) {
                    var fakeId;
                    var flag = true;
                    while(flag){
                        fakeId = this.randomWord();
                        if(fakeIdList.indexOf(fakeId) == -1){
                            flag = false;
                        }
                    }
                    fakeIdList.push(fakeId);
                    var warrantyItemListTemp = kitId2WarrantyItemsInitMap[fleetItemList[i].kitId];
                    if(!$A.util.isEmpty(warrantyItemListTemp)){
                        var warrantyItemList = JSON.parse(JSON.stringify(warrantyItemListTemp));//断连

                        for (var k = warrantyItemList.length - 1; k >= 0; k--) {
                            var itemFakeId;
                            var itemFlag = true;
                            while(itemFlag){
                                itemFakeId = this.randomWord();
                                if(fakeIdList.indexOf(itemFakeId) == -1){
                                    itemFlag = false;
                                }
                            }
                            fakeIdList.push(itemFakeId);
                            warrantyItemList[k].fakeId = itemFakeId;
                        }

                        var warranty = {
                            "fakeId": fakeId,
                            "kitId": fleetItemList[i].kitId,
                            "warrantyItemList": warrantyItemList
                        };
                        fleetItemList[i].warrantyList.push(warranty);
                    }
                }
            }
            //填写的数量小于已分配的数量
            else if(qtyPurchased < warrantyExist){
                //正序删除
                for (var j = 0; j < warrantyExist; j++) {
                    //大于的数量要移除
                    if(j > (qtyPurchased - 1)){
                        fleetItemList[i].warrantyList.splice(qtyPurchased, warrantyExist - qtyPurchased);
                        break;
                    }
                }
            }
            //分配 index
            for (var y = 0; y < fleetItemList[i].warrantyList.length; y++) {
                fleetItemList[i].warrantyList[y].index = index++;
            }
        }

        component.set("v.fakeIdList", fakeIdList);
        component.set("v.fleetClaim.fleetItemList", fleetItemList);
    },
    doSaveHelper: function(component, type) {
        var self = this;
        var fleetItemList = component.get("v.fleetClaim.fleetItemList");
        var filterFleetItem = fleetItemList.filter(item => {
            return item.kitCode != '' && item.kitCode !== undefined;
        });
        component.set("v.fleetClaim.fleetItemList", filterFleetItem);
        //如果在第一步提交/保存
        //则需要 initWarrantyDataHelper
        var step = component.get('v.step');
        if(step == 1){
            self.initWarrantyDataHelper(component);
        }
        var fleetClaim = component.get('v.fleetClaim');
        var valid = self.verifyForm(component, fleetClaim, type);
        if(!valid){
            self.showToast(null, $A.get("$Label.c.CCM_Portal_Cannotsubmitthedatapleasereviewerrorfields"), "warning");
            return;
        }
        if(type == 'Submit'){
            valid = self.checkForm(component, fleetClaim);
            if(!valid){
                return;
            }
        }
        fleetClaim.fleetItemList.forEach(fleetItem=>{
            if("unitSalesPrice" in fleetItem) {
                if(fleetItem["unitSalesPrice"] === "") {
                    fleetItem["unitSalesPrice"] = 0;
                }
            }
        });
        let fleetClaimStr = JSON.stringify(fleetClaim);
        fleetClaimStr = fleetClaimStr.replaceAll('\\"', '\'');
        var action = component.get("c.saveFleetClaim");
        action.setParams({
            'fleetClaimStr': fleetClaimStr,
            'type': type
        });
        self.showEle(component, 'spinner');
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === 'SUCCESS' ) {
                var auraResponse = response.getReturnValue();
                if(auraResponse.code === 200){
                    // console.log('dataMap', auraResponse.data.id);
                    component.set('v.fleetClaim', auraResponse.data);
                    self.checkReadonly(component, auraResponse.data);
                    self.showToast(null, type + " " + $A.get("$Label.c.CCM_Portal_SaveSubmitsuccessful"), "success");
                }else{
                    self.showToast(null, auraResponse.message, "error");
                }
            }else{
                self.showToast(null, JSON.stringify(response.getError()), "error");
            }
            self.hideEle(component, 'spinner');
        });
        $A.enqueueAction(action);
    },
    checkReadonly: function(component, fleetClaim){
        var readonly = true;
        if(!$A.util.isEmpty(fleetClaim)){
            if(fleetClaim.approvalStatus == 'Draft' || fleetClaim.approvalStatus == 'Rejected'){
                readonly = false;
            }
        }
        component.set('v.readonly', readonly);
    },
    verifyForm: function(component, fleetClaim, type) {
        //即使是 save, 也要校验 ownerInfo 必填
        var valid = true;
        if(!this.getElementRequiredError(component, fleetClaim, 'ownerInfo', 'emailAddress', 'email')){
            valid = false;
            // console.log('emailAddress');
        }
        if(!this.getElementRequiredError(component, fleetClaim, 'ownerInfo', 'orgName', null)){
            valid = false;
            // console.log('orgName');
        }
        if(!this.getElementRequiredError(component, fleetClaim, 'ownerInfo', 'firstName', null)){
            valid = false;
            // console.log('firstName');
        }

        if(!this.getElementRequiredError(component, fleetClaim, 'ownerInfo', 'phone', null)){
            valid = false;
            // console.log('lastName');
        }

        if(component.find('ownerInfo').find('phone').checkValidity() !== undefined) {
            // check phone format
            valid = valid && component.find('ownerInfo').find('phone').checkValidity();
        }

        if(!this.getElementRequiredError(component, fleetClaim, 'ownerInfo', 'lastName', null)){
            valid = false;
            // console.log('lastName');
        }

        if(!this.getElementRequiredError(component, fleetClaim, 'ownerInfo', 'country', null)){
            valid = false;
            // console.log('lastName');
        }

        if(!this.getElementRequiredError(component, fleetClaim, 'ownerInfo', 'state', null)){
            valid = false;
            // console.log('lastName');
        }

        if(!this.getElementRequiredError(component, fleetClaim, 'ownerInfo', 'city', null)){
            valid = false;
            // console.log('lastName');
        }

        if(!this.getElementRequiredError(component, fleetClaim, 'ownerInfo', 'zipPostalCode', null)){
            valid = false;
            // console.log('lastName');
        }

        if(!this.getElementRequiredError(component, fleetClaim, 'ownerInfo', 'addressLine', null)){
            valid = false;
            // console.log('lastName');
        }
        if (!this.getElementRequiredError(component, fleetClaim, "salesInfo", "salesDateStr", null)) {
            valid = false;
        }
        if (!this.getElementRequiredError(component, fleetClaim, "invoiceInfo", "billToAddressId", null)) {
            valid = false;
        }
        if (!this.getElementRequiredError(component, fleetClaim, "invoiceInfo", "shipToAddressId", null)) {
            valid = false;
        }
        if (!this.getElementRequiredError(component, fleetClaim, "invoiceInfo", "invoiceFile", null)) {
            valid = false;
        }

        if(type == 'Submit'){
            if(!this.getElementRequiredError(component, fleetClaim, 'productInfo', 'salesDateStr', null)){
                valid = false;
                // console.log('sub-salesDateStr');
            }
            //检验 unitSalesPrice 是否填写
            if(!$A.util.isEmpty(fleetClaim.fleetItemList)){
                for (var i = 0; i < fleetClaim.fleetItemList.length; i++) {
                    if(fleetClaim.fleetItemList[i].qtyPurchased > 0 && $A.util.isEmpty(fleetClaim.fleetItemList[i].unitSalesPrice)){
                        valid = false;
                        break;
                    }
                }
            }
        }
        return valid;
    },
    checkSerialNumber: function(fleetClaim) {
        //检验 serialNumber 是否正确
        var valid = true;
        if(!$A.util.isEmpty(fleetClaim.fleetItemList)){
            for (var i = 0; i < fleetClaim.fleetItemList.length; i++) {
                if($A.util.isEmpty(fleetClaim.fleetItemList[i].warrantyList)) continue;
                for (var j = 0; j < fleetClaim.fleetItemList[i].warrantyList.length; j++) {
                    if($A.util.isEmpty(fleetClaim.fleetItemList[i].warrantyList[j].warrantyItemList)) continue;
                    for (var k = 0; k < fleetClaim.fleetItemList[i].warrantyList[j].warrantyItemList.length; k++) {
                        if($A.util.isEmpty(fleetClaim.fleetItemList[i].warrantyList[j].warrantyItemList[k].serialNumber)){
                            valid = false;
                        }
                        //特殊情况
                        //Warning: Entered SN may not match the product model, please double confirm.
                        else if(
                            !$A.util.isEmpty(fleetClaim.fleetItemList[i].warrantyList[j].warrantyItemList[k].snFormatErrorMessage)
                            && fleetClaim.fleetItemList[i].warrantyList[j].warrantyItemList[k].snFormatErrorMessage != $A.get("$Label.c.CCM_Portal_WarningEnteredSNTips")
                        ){
                            valid = false;
                        }
                        if(!valid) break;
                    }
                    if(!valid) break;
                }
                if(!valid) break;
            }
        }
        return valid;
    },
    getElementRequiredError: function(component, fleetClaim, parentCmp, cmp, fieldType){
        var requiredInput = component.find(parentCmp).find(cmp);
        var requiredText = component.find(parentCmp).find(cmp + '-error-required');
        var val;
        if(cmp == 'billToAddressId'){
            val = fleetClaim.billToAddressId;
        }else if(cmp == 'shipToAddressId') {
            val = fleetClaim.shipToAddressId;
        }else if (cmp === "invoiceFile") {
            let { fileInfoList } = fleetClaim;
            val = (fileInfoList || []).length === 0 ? [] : JSON.stringify(fileInfoList);
        } else {
            val = requiredInput.get('v.value');
        }
        if(!$A.util.isEmpty(val)){
            val = val.trim();
        }
        var valid;
        if($A.util.isEmpty(val)){
            // $A.util.addClass(requiredInput, 'field-error');
            if (requiredText) {
                $A.util.removeClass(requiredText, 'slds-hide');
            }
            valid = false;
        }else{
            // $A.util.removeClass(requiredInput, 'field-error');
            if (requiredText) {
                $A.util.addClass(requiredText, 'slds-hide');
            }
            if(fieldType == 'email'){
                valid = (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(val));
                if(!valid){
                    $A.util.removeClass(requiredText, 'slds-hide');
                }
            }else{
                valid = true;
            }
        }
        return valid;
    },
    checkForm: function(component, fleetClaim){
        var valid = true;
        var fleetProgramRule = component.get('v.fleetProgramRule');
        // console.log('fleetProgramRule', JSON.stringify(fleetProgramRule));
        // console.log('fleetClaim.deliverAtOnce', fleetClaim.deliverAtOnce);
        // console.log('fleetClaim.totalSalesAmount', fleetClaim.totalSalesAmount);
        if(!this.checkSerialNumber(fleetClaim)){
            this.showToast(null, $A.get("$Label.c.CCM_Portal_PleasechecktheSNyouentered"), "warning");
            valid = false;
        // }else if(fleetProgramRule.deliverAtOnce && fleetProgramRule.deliverAtOnce != fleetClaim.deliverAtOnce){
        //     this.showToast(null, $A.get("$Label.c.CCM_Portal_OrderFleetprogramTips"), "warning");
        //     valid = false;
        }else if(!fleetClaim.hasOneMatchThreshold){
            if (fleetClaim.totalSalesAmount < fleetProgramRule.purchaseOrderAmount && !fleetClaim.endUserCustomer.eligibleForFleet) {
                this.showToast(null, $A.get("$Label.c.CCM_Portal_Orderamountdoesnotfulfillminimumrequirementoffleetprogram"), "warning");
                valid = false;
            }
        }else if($A.util.isEmpty(fleetClaim.fileInfoList)){
            this.showToast(null, $A.get("$Label.c.CCM_Portal_Pleaseuploadyourinvoice"), "warning");
            valid = false;
        }else{

        }
        return valid;
    },
    showToast: function(title, message,type) {
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams({
            "title": title,
            "message": message,
            "type": type
        });
        toastEvent.fire();
    },
    showEle: function(component, ele){
        $A.util.removeClass(
            component.find(ele),
            "slds-hide"
        );
    },
    hideEle: function(component, ele){
        $A.util.addClass(
            component.find(ele),
            "slds-hide"
        );
    },
    //生成随机32位字符串
    randomWord:function (){
        var str = "",
            range = 32,
            arr = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
        for(var i=0; i<range; i++){
            var pos = Math.round(Math.random() * (arr.length-1));
            str += arr[pos];
        }
        return str;
    },
    getUrlParameter : function(sParam) {
        var sPageURL = decodeURIComponent(window.location.search.substring(1)),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');
            if (sParameterName[0] === sParam || sParameterName[0] === ('0.' + sParam)) {
                return sParameterName[1] === undefined ? true : sParameterName[1];
            }
        }
    }
})