@IsTest
public class CCM_SalesOrgValidationHandlerTest {
    @IsTest
    static void testRelationshipIncorrect(){
        Sales_Attribute_Relationship_Map__c relationshipMap = new Sales_Attribute_Relationship_Map__c();
        relationshipMap.Org_Code__c = 'CNA';
        relationshipMap.Sales_Channel__c = 'SC01';
        relationshipMap.Cluster__c = 'CNA-CG01';
        relationshipMap.Sub_Cluster__c = 'KA001';
        insert relationshipMap;
        Test.startTest();
        try{
            Lead theLead = Test_SalesData.createProspectData();
        }
        catch(Exception ex) {

        }
        Test.stopTest();
    }
}