public with sharing class CCM_IN_Quotation_SelectCustomerCtl {
    @AuraEnabled
    public static String getAccInfo(String recordId){
        Purchase_Order__c po = new Purchase_Order__c();
        if (String.isNotBlank(recordId)){
            List<Purchase_Order__c> poList = [
                    SELECT Id,Customer__c,Customer__r.Name,
                           tolabel(Order_Type__c) orderType,Order_Type__c
                    FROM Purchase_Order__c 
                    WHERE Id =: recordId];
            if (poList != null && poList.size() > 0){
                po = poList[0];
            }
        }
        return JSON.serialize(po); 
    }

    @AuraEnabled
    public static String getCustomerOrgFilter(){
        String cyFilters = '(' + null +  ',' + '\'Dealer Location\'' + ')';
        List<FilterObj> customerFilters = new List<FilterObj>();
        customerFilters.add(new FilterObj('RecordType.DeveloperName', '=', 'Channel'));
        customerFilters.add(new FilterObj('Distributor_or_Dealer__c', 'NOT IN', cyFilters));
        customerFilters.add(new FilterObj('Status__c', '=', 'A'));
        //String customerFilterCondition = '[{"FieldName":"RecordType.DeveloperName","Condtion":"=","Value":"Channel"},{"FieldName":"Distributor_or_Dealer__c","Condtion":"!=","Value":""}]';
        if(!System.Label.CustomerSelectionAll.contains(UserInfo.getUserEmail())) {
            User currentUsr = Util.getUserInfo(UserInfo.getUserId());
            if(currentUsr.UserRole.DeveloperName.contains('CA')){
                customerFilters.add(new FilterObj('ORG_Code__c', '=', 'CCA'));
            }else{
                customerFilters.add(new FilterObj('ORG_Code__c', '!=', 'CCA'));
            }
        }
        return JSON.serialize(customerFilters);
    }

    // add,napoleon,return selected customer info
    /*
    This method is like a plugin or extension that can add more functions into.
    Because `select cusomter` is wrapped into common components,so using below method can
    enhance some limitations and avoid impacting those common components.
    And what need to return can be add into the inner class named `CustomerInfoWrp`.
    * */
    @AuraEnabled
    public static String getSelectedCustomerInfo(String strCustomerId){
        CustomerInfoWrp wrpRes = new CustomerInfoWrp();
        if (String.isNotBlank(strCustomerId)) {
            strCustomerId = String.escapeSingleQuotes(strCustomerId);
            wrpRes.customerId = strCustomerId;
            // add,napoleon,23-2-3,blCustomerBeDlg, if this customer can be placed parts order,blCustomerBeDlg = true;
            for (Sales_Program__c spProgram : [
                                            SELECT
                                                Id,Name,recordtype.developername,Customer__c
                                            FROM
                                                Sales_Program__c
                                            WHERE Id != NULL
                                            AND IsDeleted = False
                                            AND Approval_Status__c = 'Approved'
                                            AND Customer__c = :strCustomerId
                                            ]){
                    // AND Status__c = 'Active'
                if (spProgram.RecordType.DeveloperName == 'Service_Customized'
                    || spProgram.RecordType.DeveloperName == 'Service_Standard'
                    || spProgram.RecordType.DeveloperName == 'Customized'
                    || spProgram.RecordType.DeveloperName == 'Standard'
                ) {
                    wrpRes.blCustomerBeDlg = true;
                    break;
                }
            }
        }

        return JSON.serialize(wrpRes);
    }

    @AuraEnabled
    public static String getCustomerInfo(String customerId){
        if(String.isNotBlank(customerId)) {
            List<Account> accs = [SELECT Id FROM Account WHERE Id = :customerId];
            if(!accs.isEmpty()) {
                return JSON.serialize(accs[0]);
            }
            return null;
        }
        return null;
    }

    public class CustomerInfoWrp {
        @AuraEnabled public String customerId;
        @AuraEnabled public Boolean blCustomerBeDlg;

        public CustomerInfoWrp(){
            this.customerId = '';
            this.blCustomerBeDlg = false;
        }
    }

    // add end
    
    public class FilterObj{
        public String FieldName;
        public String Condtion;
        public String Value;


        public FilterObj(String fieldName, String condition, String value){
            this.FieldName = fieldName;
            this.Condtion = condition;
            this.Value = value;
        }
    }
}