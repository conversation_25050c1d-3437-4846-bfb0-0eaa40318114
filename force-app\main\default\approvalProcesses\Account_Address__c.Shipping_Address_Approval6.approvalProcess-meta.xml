<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>false</allowRecall>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>BEAM</submitter>
        <type>role</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Chervon_NA</submitter>
        <type>role</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Field_Service</submitter>
        <type>role</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>NA_Administrator</submitter>
        <type>role</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Nanjing_BEAM_Manager</submitter>
        <type>role</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>OPE_Sales_Director</submitter>
        <type>role</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Sales_Operation</submitter>
        <type>role</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter><EMAIL></submitter>
        <type>user</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Owner</field>
        <field>Customer__c</field>
        <field>Sales_Rep__c</field>
        <field>Address1__c</field>
        <field>Address2__c</field>
        <field>City__c</field>
        <field>State__c</field>
        <field>Country__c</field>
        <field>Postal_Code__c</field>
        <field>Contact__c</field>
        <field>Contact2__c</field>
        <field>Contact3__c</field>
        <field>Contact4__c</field>
        <field>Contact5__c</field>
        <field>x2nd_Tier_Dealer_for_Sales__c</field>
        <field>X2nd_Tier_Dealer__c</field>
        <field>Account_Number__c</field>
        <field>CreatedBy</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>First_Step</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <booleanFilter>1 AND 2</booleanFilter>
            <criteriaItems>
                <field>Account_Address__c.Address_Type__c</field>
                <operation>equals</operation>
                <value>Store Location</value>
            </criteriaItems>
            <criteriaItems>
                <field>Account_Address__c.Account_Number__c</field>
                <operation>equals</operation>
                <value>0376</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>BEAM Approval for ACE Store</label>
        <name>BEAM_Approval_for_ACE_Store</name>
        <rejectionActions>
            <action>
                <name>Reject_Recover_Frist_Step</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>First_Step</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Account_Address__c.Address_Type__c</field>
                <operation>equals</operation>
                <value>Store Location</value>
            </criteriaItems>
            <criteriaItems>
                <field>Account_Address__c.Account_Number__c</field>
                <operation>notEqual</operation>
                <value>0376</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Store Location Approval</label>
        <name>Store_Location_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Reject_Recover_Frist_Step</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Sales_Director__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Account_Address__c.Account_Number__c</field>
                <operation>notEqual</operation>
                <value>0376</value>
            </criteriaItems>
        </entryCriteria>
        <label>Sales Director Approval</label>
        <name>Sales_Director_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <emailTemplate>Sales_Cloud/Address_Send_Approval</emailTemplate>
    <enableMobileDeviceAccess>true</enableMobileDeviceAccess>
    <entryCriteria>
        <booleanFilter>(1 OR 2 OR 3) AND 4</booleanFilter>
        <criteriaItems>
            <field>Account_Address__c.RecordType</field>
            <operation>equals</operation>
            <value>Shipping Address</value>
        </criteriaItems>
        <criteriaItems>
            <field>Account_Address__c.RecordType</field>
            <operation>equals</operation>
            <value>Dropship Billing Address</value>
        </criteriaItems>
        <criteriaItems>
            <field>Account_Address__c.RecordType</field>
            <operation>equals</operation>
            <value>Dropship Shipping Address</value>
        </criteriaItems>
        <criteriaItems>
            <field>Account_Address__c.Is_2nd_Tier_Dealer__c</field>
            <operation>equals</operation>
            <value>False</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Address_Approved_Email</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Address_Update_Approval_Status_Approved</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Address_Update_Approved_Date</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>false</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Address_Rejected_Email</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Address_Update_Approval_Status_Rejected</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Reject_Recover_Frist_Step</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Address_Update_Approval_Status_Pending</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Address_Update_Submit_Time</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>Shipping Address Approval6</label>
    <processOrder>5</processOrder>
    <recordEditability>AdminOrCurrentApprover</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
