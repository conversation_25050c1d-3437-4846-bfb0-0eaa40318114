public without sharing class CCM_ReverseOrderUtil {
    //OPS
    public static final String CONFIRMED_BY_OPS_CONFIRMED_WAREHOUSE ='OPS confirmed – Warehouse';
    public static final String CONFIRMED_BY_OPS_CONFIRMED_TRANSPORTATION ='OPS confirmed – Transportation';
    public static final String CONFIRMED_BY_OPS_CONFIRMED_OTHERS ='OPS confirmed – Others';
    public static final String CONFIRMED_BY_OPS_NOT_CONFIRMED ='OPS not confirmed';
    public static final String CONFIRMED_BY_OPS_ALLOW_TO_RETURN ='Allow to return';
    public static final String CONFIRMED_BY_OPS_REJECT_THE_REQUEST ='Reject the request';
    //External Return Reason
    public static final String EXTERNAL_RETURN_REASON_LESS_THAN_I_ORDERED = 'Shortage - Qty received is less than ordered';
    public static final String EXTERNAL_RETURN_REASON_MORE_THAN_I_ORDERED = 'Overage - Qty received is greater than ordered';
    public static final String EXTERNAL_RETURN_REASON_PRODUCT_DAMAGED_IN_SHIPMENT = 'Damaged in Shipment';
    public static final String EXTERNAL_RETURN_REASON_WRONG_PRODUCT = 'Shortage & Overage';
    public static final String EXTERNAL_RETURN_REASON_WITH_NO_REASON = 'Customer Refusal - No reason provided';
    public static final String EXTERNAL_RETURN_REASON_ERROR_BY_SALES = 'Order Entry by Sales';
    //Return Goods or not
    public static final String RETURN_GOODS_FOR_NO ='N';
    public static final String RETURN_GOODS_FOR_YES ='Y';
    //Next Step Action
    public static final String NEXT_STEP_ACTION_CREDIT_MEMO_BACK_FOR_SHORTAGE_PRODUCT = 'I want credit memo back for shortage product';
    public static final String NEXT_STEP_ACTION_RESHIP_THE_SHORTAGE_PRODUCT = 'I want Chervon to reship the shortage product';
    public static final String NEXT_STEP_ACTION_BUY_THE_OVERAGE_PRODUCT = 'I want to buy the overage product';
    public static final String NEXT_STEP_ACTION_REJECT_AND_RETURN_THE_OVERAGE_PRODUCT = 'I want to reject and return the overage product';
    public static final String NEXT_STEP_ACTION_RETURN_DAMAGED_GOODS_AND_GET_CREDIT_BACK = 'I want to return damaged goods and get credit back';
    public static final String NEXT_STEP_ACTION_RETURN_DAMAGED_GOODS_AND_GET_REPLACEMENT = 'I want to return damaged goods and get replacement';
    public static final String NEXT_STEP_ACTION_RETURN_GOODS_AND_GET_CREDIT_BACK = 'I want to return goods and get credit back';
    //Line Type
    public static final String LINE_TYPE_CNA_RETURN_CREDIT_WITH_RECEIPT = 'CNA Return Credit with Receipt';
    public static final String LINE_TYPE_CNA_RETURN_LINE_CREDIT_ONLY = 'CNA Return Line Credit Only';
    //Brand
    public static final String BRAND_EGO = 'EGO';
    public static final String BRAND_SKIL = 'SKIL'; //SKILSAW also think as the same kind of SKIL
    public static final String BRAND_FLEX = 'FLEX';

    // Assign Inside Sales Logic
    // public static ResponseEntity assignInsideSales(String comstomerId) {
    //     List<AccountTeamMember> TeamMemberList = [SELECT Id,Customer__c,AccountId,TeamMemberRole FROM AccountTeamMember WHERE AccountId =:comstomerId];
    //     List<AccountTeamMember> ManagerList = new List<AccountTeamMember>();
    //     ResponseEntity responseEntity = new ResponseEntity();
    //     if(TeamMemberList.size()>0) {
    //         for(AccountTeamMember TeamMember:TeamMemberList){
    //             if(TeamMember.TeamMemberRole == 'Account Assistant'){
    //                 responseEntity.account = TeamMember;
    //                 responseEntity.returnMessage = 'Assistant';
    //                 return responseEntity;
    //             }
    //             if(TeamMember.TeamMemberRole == 'Account Manager'){
    //                 ManagerList.add(TeamMember);
    //             }
    //         }
    //     }
    //     if(ManagerList.size()>0){
    //         AccountTeamMember manager = ManagerList[0];
    //         responseEntity.account = manager;
    //         responseEntity.returnMessage = 'Manager';
    //         return responseEntity;
    //     }
    //     responseEntity.returnMessage = 'Not Assign';
    //     return responseEntity;
    // }

    public static List<Order> getOrder(String queryStr, String orderId, String ebsOrderNo) {
        List<Order> orderList = Database.query(queryStr);
        return orderList;
    }

    public static List<Invoice__c> getInvoices(String orderId) {
        List<Invoice__c> invoices = [SELECT Id, Name, Invoice_Number__c  FROM Invoice__c WHERE Order__c = :orderId];
        return invoices;
    }

    public static Reverse_Order_Request__c upsertReverseOrderRequest(Reverse_Order_Request__c reverse) {
        upsert reverse;
        return reverse;
    }

    public static List<Reverse_Order_Item__c> upsertReverseOrderItems(List<Reverse_Order_Item__c> items) {
        upsert items;
        return items;
    }

    public static Order getOrderById(String idStr) {
        Order objOrder = [SELECT Id, Purchase_Order__c,  Price_Book__c, CurrencyIsoCode, ShipTo__c, ShipTo__r.Account_Address__c,ShipTo__r.Customer_Line_Oracle_ID__c FROM Order WHERE Id = :idStr LIMIT 1];
        return objOrder;
    }

    public static Map<Id, Order_Item__c> getOrderItemsByOrderId(String idStr) {
        Map<Id, Order_Item__c> orderItemMap = new Map<Id, Order_Item__c>([SELECT Id, Price__c, Price_Book__c, Brand__c FROM Order_Item__c WHERE Order__c = :idStr]);
        return orderItemMap;
    }

    public static List<Reverse_Order_Request__c> getReverseOrder(String queryStr, String reverseOrderId){
        return (List<Reverse_Order_Request__c>)Database.query(queryStr);
    }

    public static Reverse_Order_Request__c updateReverseOrder(Reverse_Order_Request__c reverseOrder){
        upsert reverseOrder;
        return reverseOrder;
    }

    public static void deleteReverseOrderItems(List<Reverse_Order_Item__c> itemList) {
        delete itemList;
    }

    /**
     * @description: get shipment and shipment item info by spacific sql
     */
    public static List<Shipment__c> getShipmentWithShipmentItem(String sql) {
        return Database.query(sql);
    }

    /**
     * @description: get shipment item info
     */
    public static List<Shipment_Item__c> getShipmentItemInfo(String sql) {
        return Database.query(sql);
    }

    /**
     *
     */
    public static String getProductCodeByOrderItemId(String orderItemId) {
        return [SELECT Id, Product__r.ProductCode FROM Order_Item__c WHERE Id = :orderItemId].Product__r.ProductCode;
    }

    /**
     *
     */
    public static List<Order_Item__c> getOrderItems(String sql) {
        return Database.query(sql);
    }

    /**
     *
     */
    public static List<Invoice__c> getInvoiceAndItems(String sql) {
        return Database.query(sql);
    }

    /**
     * @description: get all billing address
     */
    @AuraEnabled
    public static CCM_ReverseOrderInfoCtl.ResponseWrapper getAddress(String customerId) {
        CCM_ReverseOrderInfoCtl.ResponseWrapper response = new CCM_ReverseOrderInfoCtl.ResponseWrapper();
        try {
            CCM_ReverseOrderInfoCtl.BillingShippingAddress billingShipping = new CCM_ReverseOrderInfoCtl.BillingShippingAddress();

            if (String.isEmpty(customerId)) {
                // portal
                User currentUser = [SELECT Id, ContactId, Contact.AccountId FROM User WHERE Id = :UserInfo.getUserId()];
                if (String.isNotBlank(currentUser.ContactId)) {
                    customerId = currentUser.contact.AccountId;
                }
            }

            if (String.isEmpty(customerId)) {
                return null;
            }

            // List<String> addressIds = new List<String>();
            // Account objAccount = [SELECT Distributor_or_Dealer__c FROM Account WHERE Id = :customerId];
            // if (objAccount.Distributor_or_Dealer__c == '2nd Tier Dealer') {
            //     for (Account_Address__c objShipTo : [SELECT Customer__c FROM Account_Address__c WHERE X2nd_Tier_Dealer__c = :customerId LIMIT 1]) {
            //         if (String.isNotBlank(objShipTo.Customer__c)) {
            //             customerId = objShipTo.Customer__c;
            //         }
            //     }
            // }

            // List<CCM_ReverseOrderInfoCtl.AddressWrapper> addressList = new List<CCM_ReverseOrderInfoCtl.AddressWrapper>();
            // if (objAccount.Distributor_or_Dealer__c == '2nd Tier Dealer') {
            //     List<Account_Address__c> shippingAccountAddress = [SELECT Customer__c
            //                                                             FROM Account_Address__c
            //                                                             WHERE Active__c = TRUE
            //                                                             AND (RecordType_Name__c = 'Shipping_Address' OR RecordType_Name__c = 'Dropship_Shipping_Address')
            //                                                             AND Approval_Status__c = 'Approved'
            //                                                             AND X2nd_Tier_Dealer__c = :customerId
            //                                                             AND Customer__c != null
            //                                                             LIMIT 1];

            //     // Billing Address
            //     if (shippingAccountAddress!= null && shippingAccountAddress.size() > 0) {
            //         billingShipping.billing = generateAddressWrapper(false, shippingAccountAddress[0].Customer__c, new List<String> {'Billing_Address'});
            //         billingShipping.shipping = generateAddressWrapper(true, customerId, new List<String> {'Shipping_Address', 'Dropship_Shipping_Address'});
            //     }
            // } else {
                // shipping address
                billingShipping.billing = generateAddressWrapper(false, customerId, new List<String> {'Billing_Address'});
                billingShipping.shipping = generateAddressWrapper(false, customerId, new List<String> {'Shipping_Address', 'Dropship_Shipping_Address'});
            // }

            response.returnData = billingShipping;
            response.isSuccess = true;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: generate address wrapper for existing
     */
    public static List<CCM_ReverseOrderInfoCtl.AddressWrapper> generateAddressWrapper(Boolean is2ndTierDealerShipingAddress, String customerId, List<String> recordtype) {
        String queryStr = 'SELECT Id, Name, Address1__c, Address2__c, City__c, Country__c, State__c FROM Account_Address__c WHERE Active__c = true AND RecordType_Name__c IN :recordtype AND Approval_Status__c = \'Approved\' ';
        if (is2ndTierDealerShipingAddress) {
            queryStr += ' AND X2nd_Tier_Dealer__c = :customerId ';
        } else {
            queryStr += ' AND Customer__c = :customerId ';
        }
        queryStr += ' LIMIT 250 ';
        System.debug(Database.query(queryStr));
        List<CCM_ReverseOrderInfoCtl.AddressWrapper> addressList = new List<CCM_ReverseOrderInfoCtl.AddressWrapper>();
        for (Account_Address__c aa : (List<Account_Address__c>)Database.query(queryStr)) {

            CCM_ReverseOrderInfoCtl.AddressWrapper wrapper = new CCM_ReverseOrderInfoCtl.AddressWrapper();
            wrapper.address1    = aa.Address1__c;
            wrapper.address2    = aa.Address2__c;
            wrapper.state       = aa.State__c;
            wrapper.city        = aa.City__c;
            wrapper.country     = aa.Country__c;
            wrapper.name        = aa.Name;
            wrapper.addressId   = aa.Id;

            addressList.add(wrapper);
        }

        return addressList;
    }

    /**
    * @description Response Entity
    */
    public class ResponseEntity{
        public AccountTeamMember account{get;set;}
        public String returnMessage{get;set;}
    }
}