({
    doInit : function(component, event, helper) {
        var columns = [
            /*{label: 'Number', type:'index'},*/
            {
                label: $A.get("$Label.c.CCM_Action"),
                width: '80px',
                children:[
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${Id}",
                        variant:"bare",
                        iconName:"utility:preview",
                        alternativeText:"View",
                        class: "${viewStyleCss}",
                        onclick: component.getReference('c.doView')
                    }
                },
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${Id}",
                        variant:"bare",
                        iconName:"utility:edit",
                        alternativeText:"Edit",
                        class: "${editStyleCss}",
                        onclick: component.getReference("c.doEdit")
                    }
                }
          ]},
            {label: $A.get("$Label.c.CCM_Portal_OrderNumber"), fieldName:'orderNumber'},
            {label: $A.get("$Label.c.CCM_Portal_PONumber"), fieldName: 'poNumber'},
            {label: $A.get("$Label.c.CCM_Portal_SubmitDate"), fieldName: 'submitDate'},
            {label: $A.get("$Label.c.CCM_Portal_Status"), fieldName: 'status'},
            {label: $A.get("$Label.c.CCM_Portal_Totals"), fieldName: 'totalPrice'}
            ];
        component.set('v.columns',columns);

        var host = window.location.origin;
        component.set('v.vfHost', host);

        var action = component.get("c.getInitInfo");
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (component.isValid() && state === "SUCCESS") {
                var returnData = JSON.parse(response.getReturnValue());
                component.set('v.pendingNum', returnData.pendingOrderNumber);
                component.set('v.paymentNum', returnData.pendingInvoiceNumber);
                /*component.set('v.contactName', returnData.customerManagerInfo.contactName);
                component.set('v.phoneNum', returnData.customerManagerInfo.phone);
                component.set('v.emailStr', returnData.customerManagerInfo.email);
                component.set('v.photoURL', returnData.customerManagerInfo.photolink);*/
                if(returnData.isCCA){
                    component.set('v.isNotCCA', false);
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert($A.get("$Label.c.CCM_Portal_ErrorTips"));
                }
            }
        });
        $A.enqueueAction(action);
    },
    doSearch : function(component, event, helper){
        
    }
})