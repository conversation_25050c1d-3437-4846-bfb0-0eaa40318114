/**
 * @data: 2022-08-12
 * @description: This is the test class of CCM_UpdateCustomerBusinessTypeHandle.
 */
@isTest
public class CCM_UpdateCustomerBusinessTypeHandleTest {
    
    @TestSetup
    static void init() {
        CCM_SharingUtil.isSharingOnly = true;
        CCM_UpaftCtrl.inFutureContext = true;
        Account acct = new Account();
        acct.ORG_Code__c = CCM_Constants.ORG_CODE_CNA;
        acct.Name = 'TestName';
        acct.Customer_Class__c = 'Consumer';
        acct.Phone = '***********';
        acct.BillingCountry = 'US';
        acct.BillingState = 'AK';
        acct.BillingPostalCode = '123456';
        acct.Distributor_or_Dealer__c = '1st Tier Service Only Partner';
        acct.Intended_Brand__c = 'EGO';
        acct.Invoicing_Method__c = 'Email';
        acct.Company__c = 'testcompany';
        acct.Approval_Status__c = 'Approved';
        acct.TaxID__c = 'test';
        insert acct;

        Lead l = new Lead();
        l.LastName = 'test';
        l.Email = '<EMAIL>';
        insert l;
        
        Account_Address__c account_address = new Account_Address__c();
        account_address.Customer__c = acct.Id;
        account_address.Name = 'test address';
        account_address.Address1__c = 'ss01';
        account_address.City__c = 'AK01';
        account_address.State__c = 'AK';
        account_address.Country__c = 'US';
        account_address.Postal_Code__c = '111222';
        account_Address.Email_for_Invoicing__c  = '<EMAIL>';
        account_address.Approval_Status__c = 'Approved';
        insert account_address;
        
        Sales_Program__c sales_program = new Sales_Program__c();
        sales_program.Name = 'Service EGO 2022';
        sales_program.Authorized_Brand_Name_To_Oracle__c = 'Service EGO 2022';
        sales_program.Brands__c = 'EGO';
        sales_program.ORG_Code__c = CCM_Constants.ORG_CODE_CNA;
        sales_program.RecordTypeId = CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_Standard_ID;
        sales_program.Condition_Service__c = 'Service what they sell';
        sales_program.Customer__c = acct.Id;
        sales_program.Prospect__c = l.Id;
        sales_program.Mileage_One_Trip__c = 1000;
        sales_program.Value_Added_Services__c = 'Pick up and delivery services';
        sales_program.Payment_Lead_Time__c = 10;
        sales_program.Payment_Discount__c = 2;
        sales_program.Approval_Status__c = CCM_Constants.SALES_PROGRAM_APPROVAL_STATUS_APPROVED;
        sales_program.Payment_Term__c = 'NA001';
        sales_program.Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.';
        insert sales_program;   
        
        Address_With_Program__c address_with_program = new Address_With_Program__c();
        // Address_Type__c = 'Billing Address';
        address_with_program.Status__c = 'A';
        address_with_program.Program__c = sales_program.Id;
        address_with_program.Account_Address__c = account_address.Id;
        insert address_with_program; 
    }
    
    @isTest
    static void testUpdateCustomerBusinessType(){
        Test.startTest();
        
        Sales_Program__c objSP = [SELECT Id, Customer__c, customer__r.Customer_Business_type__c, Approval_Status__c FROM Sales_Program__c LIMIT 1];
        objSP.RecordTypeId = CCM_Constants.SALES_PROGRAM_RECORD_TYPE_STANDARD_ID;
        objSP.Approval_Status__c = 'Approved';
        update objSP;
        Test.stopTest();
    }
    
    @isTest
    static void test1() {
        Lead objLead = [SELECT Id FROM Lead LIMIT 1];
        Sales_Program__c objSP = [SELECT Id FROM Sales_Program__c Limit 1];
        objSP.Brands__c = 'Flex';
        objsp.Approval_Status__c = 'Approved';
        objsp.Prospect__c = objLead.Id;
        update objSP;
    }

}