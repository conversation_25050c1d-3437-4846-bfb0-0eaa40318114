/**
 * @Author: <PERSON><PERSON>
 * @description: Create a Detail page to show detal of Warranty return Claim 
 * @Create Date: 2022-10-31
 */

public without sharing class CCM_WarrantyReturnClaimDetail {
    
    public static String sqlString = 'SELECT Id, Name, '
                    // Warranty Return Request Basic Information
                    + 'Warranty_Return_Request_No__c, '
                    + 'CreatedBy.Name, '
                    + 'Credit_Memo_Number__c, '
                    + 'Warranty_Return_Order_Number_in_EBS__c, '
                    + 'CreatedBy.Email, '
                    + 'Inside_Sales__c, ' 
                    + 'Inside_Sales__r.Name, '

                    // Customer Information
                    + 'Customer__r.AccountNumber,'
                    + 'Contact_Name__c, '
                    + 'Customer__c, '
                    + 'Customer__r.Name, '
                    + 'Customer_Reference_Number__c, '
                    + 'Contact_Phone_Number__c, '
                    // + 'Contact_Email_Address__c, '
                    + 'Contact_Email_Addresses__c, '
                    + 'Chervon_or_Warehouse_Purchase__c, '
                    + 'Notes__c, '
                    //Warehouse Loaction
                    + 'Warehouse_Location__c, '
                    + 'Warehouse_Location__r.name, '
                    + 'Warehouse_Location__r.Address1__c, '
                    + 'Warehouse_Location__r.Address2__c, '
                    + 'Warehouse_Location__r.City__c, '
                    + 'Warehouse_Location__r.State__c, '
                    + 'Warehouse_Location__r.Country__c, '
                    + 'Warehouse_Location__r.Postal_Code__c, '
                    //Store Loaction
                    + 'Store_Location__c, '
                    + 'Store_Location__r.name, '
                    + 'Store_Location__r.Address1__c, '
                    + 'Store_Location__r.Address2__c, '
                    + 'Store_Location__r.City__c, '
                    + 'Store_Location__r.State__c, '
                    + 'Store_Location__r.Country__c, '
                    + 'Store_Location__r.Postal_Code__c, '
                    
                    //Payment Information
                    + 'Payment_Method__c, '
                    + 'Customer__r.BillingAddress, '
                    + 'Customer__r.ShippingAddress, '
                    + 'Debit_Memo_Number__c, '
                    + 'Debit_Memo_Number__r.Invoice_Number__c, '
                    + 'Debit_Memo_Number__r.Total_Due__c, '
                    //Shipping Additional Address
                    + 'Additional_Shipping_Street__c, '
                    + 'Additional_Shipping_Street2__c, '
                    + 'Additional_Shipping_City__c, '
                    + 'Additional_Shipping_Country__c, '
                    + 'Additional_Shipping_Postal_Code__c, '
                    + 'Additional_Contact_Email__c, '
                    + 'Additional_Shipping_Province__c, '
                    //Shipping Address
                    + 'Shipping_Address__c, '
                    + 'Shipping_Address__r.Name, '
                    + 'Shipping_Address__r.Address1__c, '
                    + 'Shipping_Address__r.Address2__c, '
                    + 'Shipping_Address__r.State__c, '
                    + 'Shipping_Address__r.City__c, '
                    + 'Shipping_Address__r.Country__c, '
                    + 'Shipping_Address__r.Postal_Code__c, '
                    //Billing Address
                    + 'Billing_Address__c, '
                    + 'Billing_Address__r.name, '
                    + 'Billing_Address__r.Address1__c, '
                    + 'Billing_Address__r.Address2__c, '
                    + 'Billing_Address__r.City__c, '
                    + 'Billing_Address__r.State__c, '
                    + 'Billing_Address__r.Country__c, '
                    + 'Billing_Address__r.Postal_Code__c, '
                    //Store Address
                    + 'Store_Address__c, '
                    + 'Store_Address__r.name, '
                    + 'Store_Address__r.Address1__c, '
                    + 'Store_Address__r.Address2__c, '
                    + 'Store_Address__r.City__c, '
                    + 'Store_Address__r.State__c, '
                    + 'Store_Address__r.Country__c, '
                    + 'Store_Address__r.Postal_Code__c, '
                    //Store Additional Address
                    + 'Additional_Store_Street__c, '
                    + 'Additional_Store_Street2__c, '
                    + 'Additional_Store_City__c, '
                    + 'Additional_Store_Country__c, '
                    + 'Additional_Store_Postal_Code__c, '
                    + 'Additional_Store_Contact_Email__c, '
                    + 'Additional_Store_Contact_Name__c, '
                    + 'Additional_Store_Contact_Phone__c, '
                    + 'Additional_Store_Province__c, '

                    //Status
                    + 'Is_Alternative_Address__c, '
                    + 'Is_Alternative_Store_Address__c, '
                    + 'Warranty_Return_Request_Status__c, '
                    + 'Warranty_Return_Request_Status_Formula__c, '
                    + 'Return_Goods_Status__c, '
                    + 'Approval_Status__c, '
                    + 'Credit_Memo_Status__c '
                    
                + 'FROM Warranty_Return_Claim__c '
                + 'WHERE IsDeleted = false ';

    public static String sqlItem = 'SELECT Id, Name, '
                    + 'Warranty_Return_Claim__c, '
                    + 'Model__c, '
                    + 'Model__r.Name, '
                    + 'Model__r.ProductCode, '
                    + 'Model__r.SF_Description__c, '
                    + 'Model__r.Brand_Name__c, '
                    + 'Model__r.Product_unit__c,'
                    + 'Invoice_Price__c, '
                    + 'Order_Item__c, '
                    + 'Order_Item__r.Order__r.PO_Number__c, '
                    + 'Quantity__c, '
                    + 'Subtotal__c, '
                    + 'End_Consumer_Purchase_Date__c, '
                    + 'End_Consumer_Return_Date__c, '
                    + 'DIF_RTV__c, '
                    + 'Serial_Number__c, '
                    + 'Warehouse_Received_Total_Quantity__c, '
                    + 'Credit_Memo_Amount__c, '
                    + 'Return_Reason_Remark__c, '
                    + 'Return_Reason__c '
                + 'FROM Warranty_Return_Claim_Item__c '
                + 'WHERE IsDeleted = false ';

    /**
    * @description: show Warranty Return Claim and Items on the Detail Page
    */
    @AuraEnabled
    public static ResponseWrapper getWarrantyReturnClaimInfo(String WarrantyReturnClaimId) {
        System.debug('claim id===========>' + WarrantyReturnClaimId);
        ResponseWrapper response = new ResponseWrapper();
        try {
            //insert Warranty Return Claim ID into these query
            sqlString += ' AND Id = :WarrantyReturnClaimId ';
            sqlItem += 'AND Warranty_Return_Claim__c = :WarrantyReturnClaimId';
            
            List<Warranty_Return_Claim__c> querywrc = (List<Warranty_Return_Claim__c>)Database.query(sqlString);
            List<Warranty_Return_Claim_Item__c> querywrcItem = (List<Warranty_Return_Claim_Item__c>)Database.query(sqlItem);

            if(querywrc.size()>0){
                //Assign the data from this record to warpper
                Warranty_Return_Claim__c  objwrc = querywrc[0];

                wrcWrapper wrcWrapper = new wrcWrapper();
                wrcWrapper.requestNumber = objwrc.Warranty_Return_Request_No__c;
                wrcWrapper.recordId = objwrc.Id;
                wrcWrapper.customerId = objwrc.Customer__c;
                wrcWrapper.approvalStatus = objwrc.Approval_Status__c;
                wrcWrapper.returnGoodsStatus = objwrc.Return_Goods_Status__c;
                wrcWrapper.creditMemoStatus = objwrc.Credit_Memo_Status__c;
                wrcWrapper.returnOrderNumberInEBS = objwrc.Warranty_Return_Order_Number_in_EBS__c;
                wrcWrapper.insideSalesName = objwrc.Inside_Sales__c != null ? objwrc.Inside_Sales__r.name : '';
                String basicURL = QueryUtils.getSalesforceUrl();
                // if (String.isNotBlank(objwrc.Warranty_Return_Order_Number_in_EBS__c)) {
                List<URLWrapper> wrapperList = new List<URLWrapper>();
                // List<Order> orderList = [SELECT Id, Order_OracleID__c FROM Order WHERE Order_OracleID__c IN :objwrc.Warranty_Return_Order_Number_in_EBS__c.split(', ') OR Purchase_Order_Text__c = :objwrc.Name];
                List<Order> orderList = [SELECT Id, Order_OracleID__c FROM Order WHERE Purchase_Order_Text__c = :objwrc.Name];
                if (orderList.size() > 0) {
                    for (Order objOrder : orderList) {
                        URLWrapper wrap = new URLWrapper();
                        wrap.label = objOrder.Order_OracleID__c;
                        wrap.id = Url.getOrgDomainUrl().toExternalForm() + '/' + objOrder.Id;
                        wrapperList.add(wrap);
                    }
                    wrcWrapper.returnOrderList = wrapperList;
                }
                // }
                // if (String.isNotBlank(objwrc.Credit_Memo_Number__c)) {
                List<URLWrapper> creditWrapperList = new List<URLWrapper>();
                List<Invoice__c> invList = [SELECT Id, Invoice_Number__c FROM Invoice__c WHERE PO_Number__c = :objwrc.Name];
                if (invList.size() > 0) {
                    for (Invoice__c objInv : invList) {
                        URLWrapper wrap = new URLWrapper();
                        wrap.label = objInv.Invoice_Number__c;
                        wrap.id = '/apex/CreditMemoPDF?invoiceID=' + objInv.Id;
                        creditWrapperList.add(wrap);
                    }
                    wrcWrapper.creditMemoList = creditWrapperList;
                }
                // }
                wrcWrapper.requestStatus = objwrc.Warranty_Return_Request_Status_Formula__c;
                wrcWrapper.customerName = objwrc.Customer__r.Name;
                wrcWrapper.customerNumber = objwrc.Customer__r.AccountNumber;
                wrcWrapper.contactName = objwrc.Contact_Name__c;
                wrcWrapper.requestInitiator = objwrc.CreatedBy.Name;
                wrcWrapper.requestInitiatorEmail = objwrc.CreatedBy.Email;
                wrcWrapper.creditMemoNumber = objwrc.Credit_Memo_Number__c;
                // wrcWrapper.contactEmailAddress = objwrc.Contact_Email_Address__c;
                wrcWrapper.contactEmailAddress = objwrc.Contact_Email_Addresses__c;
                wrcWrapper.contactPhoneNumber = objwrc.Contact_Phone_Number__c;
                wrcWrapper.chervonorWarehousePurchase = objwrc.Chervon_or_Warehouse_Purchase__c;
                wrcWrapper.customerReferenceNumber = objwrc.Customer_Reference_Number__c;
                wrcWrapper.notes = objwrc.Notes__c;
                wrcWrapper.paymentMethod = objwrc.Payment_Method__c;
                wrcWrapper.debitMemoNumber = objwrc.Debit_Memo_Number__r.Invoice_Number__c;
                wrcWrapper.debitMemoAmount = objwrc.Debit_Memo_Number__r.Total_Due__c;

                //shipping address , if Is_Alternative_Store_Address__c is true, take the address you filled in.
                if (objwrc.Is_Alternative_Address__c) {
                    AddressWrapper address  = new AddressWrapper();
                    address.address1        = objwrc.Additional_Shipping_Street__c;
                    address.address2        = objwrc.Additional_Shipping_Street2__c;
                    address.city            = objwrc.Additional_Shipping_City__c;
                    address.country         = objwrc.Additional_Shipping_Country__c;
                    address.postalCode      = objwrc.Additional_Shipping_Postal_Code__c;
                    address.province        = objwrc.Additional_Shipping_Province__c;
                    wrcWrapper.shippingAddress = address;
                } else if (String.isNotBlank(objwrc.Shipping_Address__c)) {
                    AddressWrapper address  = new AddressWrapper();
                    address.addressId       = objwrc.Shipping_Address__c;
                    address.name            = objwrc.Shipping_Address__r.Name;
                    address.address1        = objwrc.Shipping_Address__r.Address1__c;
                    address.address2        = objwrc.Shipping_Address__r.Address2__c;
                    address.city            = objwrc.Shipping_Address__r.City__c;
                    address.country         = objwrc.Shipping_Address__r.Country__c;
                    address.state           = objwrc.Shipping_Address__r.State__c;
                    address.postalCode      = objwrc.Shipping_Address__r.Postal_Code__c;
                    wrcWrapper.shippingAddress = address;
                }
                //Billing Address
                if (String.isNotBlank(objwrc.Billing_Address__c)) {
                    AddressWrapper address  = new AddressWrapper();
                    address.addressId       = objwrc.Billing_Address__c;
                    address.name            = objwrc.Billing_Address__r.name;
                    address.address1        = objwrc.Billing_Address__r.Address1__c;
                    address.address2        = objwrc.Billing_Address__r.Address2__c;
                    address.city            = objwrc.Billing_Address__r.City__c;
                    address.state           = objwrc.Billing_Address__r.State__c;
                    address.country         = objwrc.Billing_Address__r.Country__c;
                    address.postalCode      = objwrc.Billing_Address__r.Postal_Code__c;
                    wrcWrapper.billingAddress  = address;
                }
                //Store Loaction
                if (String.isNotBlank(objwrc.Store_Location__c)) {
                    AddressWrapper address  = new AddressWrapper();
                    address.addressId       = objwrc.Store_Location__c;
                    address.name            = objwrc.Store_Location__r.name;
                    address.address1        = objwrc.Store_Location__r.Address1__c;
                    address.address2        = objwrc.Store_Location__r.Address2__c;
                    address.city            = objwrc.Store_Location__r.City__c;
                    address.state           = objwrc.Store_Location__r.State__c;
                    address.country         = objwrc.Store_Location__r.Country__c;
                    address.postalCode      = objwrc.Store_Location__r.Postal_Code__c;
                    wrcWrapper.storeLocation  = address;
                }
                //Warehouse Loaction
                if (String.isNotBlank(objwrc.Warehouse_Location__c)) {
                    AddressWrapper address  = new AddressWrapper();
                    address.addressId       = objwrc.Warehouse_Location__c;
                    address.name            = objwrc.Warehouse_Location__r.name;
                    address.address1        = objwrc.Warehouse_Location__r.Address1__c;
                    address.address2        = objwrc.Warehouse_Location__r.Address2__c;
                    address.city            = objwrc.Warehouse_Location__r.City__c;
                    address.state           = objwrc.Warehouse_Location__r.State__c;
                    address.country         = objwrc.Warehouse_Location__r.Country__c;
                    address.postalCode      = objwrc.Warehouse_Location__r.Postal_Code__c;
                    wrcWrapper.warehouseLocation  = address;
                }

                // store address, if Is_Alternative_Store_Address__c is true, take the address you filled in.
                if (objwrc.Is_Alternative_Store_Address__c) {
                    AddressWrapper address  = new AddressWrapper();
                    address.address1        = objwrc.Additional_Store_Street__c;
                    address.address2        = objwrc.Additional_Store_Street2__c;
                    address.city            = objwrc.Additional_Store_City__c;
                    address.country         = objwrc.Additional_Store_Country__c;
                    address.postalCode      = objwrc.Additional_Store_Postal_Code__c;
                    address.province        = objwrc.Additional_Store_Province__c;
                    wrcWrapper.storeAddress = address;
                } else if (String.isNotBlank(objwrc.Store_Address__c)) {
                    AddressWrapper address  = new AddressWrapper();
                    address.addressId       = objwrc.Store_Address__c;
                    address.name            = objwrc.Store_Address__r.Name;
                    address.address1        = objwrc.Store_Address__r.Address1__c;
                    address.address2        = objwrc.Store_Address__r.Address2__c;
                    address.city            = objwrc.Store_Address__r.City__c;
                    address.country         = objwrc.Store_Address__r.Country__c;
                    address.state           = objwrc.Store_Address__r.State__c;
                    address.postalCode      = objwrc.Store_Address__r.Postal_Code__c;
                    wrcWrapper.storeAddress = address;
                }

                //find thr Next Approver
                for (ProcessInstanceWorkitem pi : [SELECT Id, ProcessInstanceId, OriginalActorId, OriginalActor.Name, ActorId, Actor.Name  
                                                        FROM ProcessInstanceWorkitem 
                                                        WHERE ProcessInstance.TargetObjectId = :WarrantyReturnClaimId 
                                                        ORDER BY CreatedDate, Actor.Name DESC]) {
                    wrcWrapper.nextApprover = pi.Actor.Name;
                    break;
                }

                Map<String, List<ImageWrapper>> purchaseDateAttachmentMap = new Map<String, List<ImageWrapper>>();
                Map<String, List<ImageWrapper>> returnDateAttachmentMap = new Map<String, List<ImageWrapper>>();
                Map<String, List<ImageWrapper>> returnReasonAttachmentMap = new Map<String, List<ImageWrapper>>();
                Map<Id, Warranty_Return_Claim_Item_Attachment__c> attachmentMap = new Map<Id, Warranty_Return_Claim_Item_Attachment__c>([SELECT Id, Attachment_Type__c, Warranty_Return_Claim_Item__c FROM Warranty_Return_Claim_Item_Attachment__c WHERE Warranty_Return_Claim__c = :WarrantyReturnClaimId]);
                if (attachmentMap.keySet().size() > 0) {
                    
                    for(ContentDocumentLink link : [SELECT ContentDocumentId,ContentDocument.Title, LinkedEntityId, ContentDocument.CreatedById FROM ContentDocumentLink WHERE LinkedEntityId IN :attachmentMap.keySet() ]) {
                        if (attachmentMap.containsKey(link.LinkedEntityId)) {
                            String attachmentType = attachmentMap.get(link.LinkedEntityId).Attachment_Type__c;
                            String itemId = attachmentMap.get(link.LinkedEntityId).Warranty_Return_Claim_Item__c;
                            if (attachmentType == 'End Customer Purchase Date') {
                                if (!purchaseDateAttachmentMap.containsKey(itemId)) {
                                    purchaseDateAttachmentMap.put(itemId, new List<ImageWrapper>());
                                }
                                purchaseDateAttachmentMap.get(itemId).add(new ImageWrapper(itemId, link.LinkedEntityId, link.ContentDocumentId, link.ContentDocument.Title, link.ContentDocument.CreatedById));
                            } else if (attachmentType == 'End Customer Return Date') {
                                if (!returnDateAttachmentMap.containsKey(itemId)) {
                                    returnDateAttachmentMap.put(itemId, new List<ImageWrapper>());
                                }
                                returnDateAttachmentMap.get(itemId).add(new ImageWrapper(itemId, link.LinkedEntityId, link.ContentDocumentId, link.ContentDocument.Title, link.ContentDocument.CreatedById));
                            }else if (attachmentType == 'Return Reason') {
                                if (!returnReasonAttachmentMap.containsKey(itemId)) {
                                    returnReasonAttachmentMap.put(itemId, new List<ImageWrapper>());
                                }
                                returnReasonAttachmentMap.get(itemId).add(new ImageWrapper(itemId, link.LinkedEntityId, link.ContentDocumentId, link.ContentDocument.Title, link.ContentDocument.CreatedById));
                            }        
                        }
                    }
                }
                
                List<wrcItemWrapper> lstItemWrapper = new List<wrcItemWrapper>();
                List<String> itemIds = new List<String>();
                //Iterate over the item query
                if(querywrcItem.size() > 0){
                    for (Warranty_Return_Claim_Item__c objwrcItem : querywrcItem){
                        itemIds.add(objwrcItem.Id);
                        wrcItemWrapper itemWrapper = new wrcItemWrapper();
                        itemWrapper.recordId = objwrcItem.Id;
                        itemWrapper.productModel = objwrcItem.Model__r.ProductCode;
                        itemWrapper.productDescription = objwrcItem.Model__r.SF_Description__c;
                        itemWrapper.productBrand = objwrcItem.Model__r.Brand_Name__c;
                        itemWrapper.productInvoicePrice = objwrcItem.Invoice_Price__c;
                        itemWrapper.productQuantity = objwrcItem.Quantity__c;
                        itemWrapper.productSubtotal = objwrcItem.Subtotal__c;
                        itemWrapper.endConsumerPurchaseProof = purchaseDateAttachmentMap.get(objwrcItem.Id);
                        itemWrapper.endConsumerReturnProof = returnDateAttachmentMap.get(objwrcItem.Id);
                        itemWrapper.returnReasonProof = returnReasonAttachmentMap.get(objwrcItem.Id);
                        itemWrapper.customerPONumber = objwrcItem.Order_Item__r.Order__r.PO_Number__c;
                        
                        if(objwrcItem.End_Consumer_Purchase_Date__c != null){
                            itemWrapper.productECPDate = DateTime.newInstance(
                                objwrcItem.End_Consumer_Purchase_Date__c.year(), 
                                objwrcItem.End_Consumer_Purchase_Date__c.month(), 
                                objwrcItem.End_Consumer_Purchase_Date__c.day()
                            ).format('yyyy-MM-dd');
                        }
                        if(objwrcItem.End_Consumer_Return_Date__c != null){
                            itemWrapper.productECRDate = DateTime.newInstance(
                                objwrcItem.End_Consumer_Return_Date__c.year(), 
                                objwrcItem.End_Consumer_Return_Date__c.month(), 
                                objwrcItem.End_Consumer_Return_Date__c.day()
                            ).format('yyyy-MM-dd');
                        }
                        itemWrapper.productDIForRTV = objwrcItem.DIF_RTV__c;
                        itemWrapper.productSerialNumber = objwrcItem.Serial_Number__c;
                        itemWrapper.productReturnReason = objwrcItem.Return_Reason__c;
                        itemWrapper.productReturnReasonRemark = objwrcItem.Return_Reason_Remark__c;
                        itemWrapper.productUnit = objwrcItem.Model__r.Product_unit__c;
                        itemWrapper.wrTotalQuantity = Math.abs(objwrcItem.Warehouse_Received_Total_Quantity__c != null ? objwrcItem.Warehouse_Received_Total_Quantity__c : 0);
                        itemWrapper.creditMemoIssued = Math.abs(objwrcItem.Credit_Memo_Amount__c != null ? objwrcItem.Credit_Memo_Amount__c : 0);
                        lstItemWrapper.add(itemWrapper);
                    }
                }

                // add by jet.查询当前用户的profile，返回给前台。
                String currentUserProfileName = [SELECT Id, Name FROM Profile WHERE Id = :UserInfo.getProfileId()].Name;
                response.currentUserProfile = currentUserProfileName;
                // if (currentUserProfileName.contains('Community')) {
                //     response.currentUserProfile = 'Portal User';
                // } else {
                //     response.currentUserProfile = 'Inside Sales';
                // }

		        List<FileWrapper> attachments = new List<FileWrapper>();
                for(ContentDocumentLink iterator : [SELECT ContentDocumentId,ContentDocument.Title, LinkedEntityId, ContentDocument.CreatedById FROM ContentDocumentLink WHERE LinkedEntityId = :WarrantyReturnClaimId ]) {
                    attachments.add(new FileWrapper(iterator.LinkedEntityId, iterator.ContentDocumentId, iterator.ContentDocument.Title, iterator.ContentDocument.CreatedById));
                }
                wrcWrapper.attachments = attachments;
                wrcWrapper.returnItems = lstItemWrapper;
                response.returnData = wrcWrapper;
                response.isSuccess = true;

                System.debug('response info=========>' + response.returnData);
            }
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    //added by jet
    @AuraEnabled
    public static ResponseWrapper updateWarrantyReturn(String warrantyReturnStr) {
        Savepoint sp = Database.setSavePoint();
        ResponseWrapper response = new ResponseWrapper();

        try {
            WarrantyReturnWrapper wrapper = (WarrantyReturnWrapper)JSON.deserialize(warrantyReturnStr, WarrantyReturnWrapper.class);
            Warranty_Return_Claim__c objWarranty = new Warranty_Return_Claim__c();
            objWarranty.Id = wrapper.recordId;
            objWarranty.Payment_Method__c = wrapper.paymentMethod;
            objWarranty.Debit_Memo_Number__c = wrapper.debitMemoId;

            update objWarranty;

            response.isSuccess = true;
        } catch (Exception e) {
            Database.rollback(sp);
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    @AuraEnabled
    public static void deleteContentDocument(String cdId){
        ContentDocument cd = new ContentDocument(Id=cdId);
        delete cd;
    }

    public class WarrantyReturnWrapper { 
        @AuraEnabled public String recordId {get; set;}

        @AuraEnabled public String paymentMethod {get; set;}

        @AuraEnabled public String debitMemoId {get; set;}
    }

    public class FileWrapper {
        @AuraEnabled public String itemId {get; set;}
        @AuraEnabled public String documentId {get; set;}

        @AuraEnabled public String name {get; set;}
        @AuraEnabled public String createdById {get; set;}

        public FileWrapper(String itemId, String documentId, String name, String createdById) {
            this.itemId = itemId;
            this.documentId = documentId;
            this.name = name;
            this.createdById = createdById;
        }
    }
    //Use this ResponseWrapper to pass parameters on the front and back ends
    public class ResponseWrapper {

        @AuraEnabled public Object returnData {get; set;}

        @AuraEnabled public String currentUserProfile {get; set;}

        @AuraEnabled public Boolean isSuccess {get; set;}

        @AuraEnabled public String stateCode {get; set;}

        @AuraEnabled public String errorMsg {get; set;}

        @AuraEnabled public Integer errorLineNumbber {get; set;}
    }
    //Use this AddressWrapper to recording address
    public class AddressWrapper {
        @AuraEnabled public String addressId {get; set;}

        @AuraEnabled public String address1 {get; set;}

        @AuraEnabled public String address2 {get; set;}

        @AuraEnabled public String name {get; set;}

        @AuraEnabled public String city {get; set;}

        @AuraEnabled public String country {get; set;}

        @AuraEnabled public String state {get; set;}

        @AuraEnabled public String postalCode {get; set;}

        @AuraEnabled public String province {get; set;}

    }

    //Use this wrcWrapper for Warranty Return Claim
    public class wrcWrapper {
        @AuraEnabled public String requestNumber {get; set;}

        @AuraEnabled public String customerId {get; set;}

        @AuraEnabled public String debitMemoId {get; set;}

        @AuraEnabled public String recordId {get; set;} // 230215, add by jet

        @AuraEnabled public String approvalStatus {get; set;} // 230215, add by jet

        @AuraEnabled public String returnGoodsStatus {get; set;}

        @AuraEnabled public String creditMemoStatus {get; set;}

        @AuraEnabled public String requestStatus {get; set;}

        @AuraEnabled public String customerName {get; set;}

        @AuraEnabled public String customerNumber {get; set;}

        @AuraEnabled public String contactName {get; set;}
        
        @AuraEnabled public String requestInitiator {get; set;}

        @AuraEnabled public String requestInitiatorEmail {get; set;}

        @AuraEnabled public String insideSalesName {get;set;}

        @AuraEnabled public String order {get; set;}

        @AuraEnabled public String creditMemoNumber {get; set;}
        @AuraEnabled public List<URLWrapper> creditMemoList {get; set;}

        @AuraEnabled public String returnOrderNumberInEBS {get; set;}
        @AuraEnabled public List<URLWrapper> returnOrderList {get; set;}

        // More Informaton'''''''''''''''''''''''''''''''''''''''''
        @AuraEnabled public String contactEmailAddress {get; set;}
        
        @AuraEnabled public AddressWrapper storeLocation {get; set;}
        
        @AuraEnabled public String contactPhoneNumber {get; set;}

        @AuraEnabled public String chervonorWarehousePurchase {get; set;}

        @AuraEnabled public String customerReferenceNumber {get; set;}

        @AuraEnabled public AddressWrapper warehouseLocation {get; set;}

        @AuraEnabled public String notes {get; set;}

        @AuraEnabled public AddressWrapper billingAddress {get; set;}

        @AuraEnabled public AddressWrapper shippingAddress {get; set;}

        @AuraEnabled public AddressWrapper storeAddress {get; set;}

        @AuraEnabled public String paymentMethod {get; set;}

        @AuraEnabled public String debitMemoNumber {get; set;}

        @AuraEnabled public Decimal debitMemoAmount {get; set;}

        @AuraEnabled public String nextApprover {get; set;}

        @AuraEnabled public List<wrcItemWrapper> returnItems {get; set;}

        @AuraEnabled public List<FileWrapper> attachments {get; set;}
        public wrcWrapper() {
            this.returnItems = new List<wrcItemWrapper>();
            this.returnOrderList = new List<URLWrapper>();
            this.creditMemoList = new List<URLWrapper>();
        }
    }

    //Use this wrcItemWrapper for Warranty Return Claim Item
    public class wrcItemWrapper {
        @AuraEnabled public String recordId {get; set;}

        @AuraEnabled public String productModel {get; set;}

        @AuraEnabled public String productDescription {get; set;}

        @AuraEnabled public String productBrand {get; set;}

        @AuraEnabled public Decimal productInvoicePrice {get; set;}

        @AuraEnabled public String customerPONumber {get; set;}

        @AuraEnabled public Decimal productQuantity {get; set;}

        @AuraEnabled public Decimal productSubtotal {get; set;}

        @AuraEnabled public Decimal creditMemoIssued {get; set;}

        @AuraEnabled public String productECPDate {get; set;}

        @AuraEnabled public String productECRDate {get; set;}

        @AuraEnabled public String productDIForRTV {get; set;}

        @AuraEnabled public String productSerialNumber {get; set;}

        @AuraEnabled public String productReturnReason {get; set;}

        @AuraEnabled public String productReturnReasonRemark {get; set;}

        @AuraEnabled public String productUnit {get; set;}

        @AuraEnabled public Decimal wrTotalQuantity {get; set;}

        @AuraEnabled public Decimal totalQuantity {get; set;}

        @AuraEnabled public List<ImageWrapper> endConsumerPurchaseProof {get; set;}

        @AuraEnabled public List<ImageWrapper> endConsumerReturnProof {get; set;}

        @AuraEnabled public List<ImageWrapper> returnReasonProof {get; set;}

        public wrcItemWrapper() {
            this.EndConsumerPurchaseProof = new List<ImageWrapper>();
            this.EndConsumerReturnProof = new List<ImageWrapper>();
            this.returnReasonProof = new List<ImageWrapper>();
        }
    }

    public class URLWrapper {
        @AuraEnabled public String label {get; set;}
        @AuraEnabled public String id {get; set;}
    }

    /**
     * @description: reverse order item image wrapper class
     */
    public class ImageWrapper {
        @AuraEnabled public String itemId {get; set;}

        @AuraEnabled public String attachmentId {get; set;}
        
        @AuraEnabled public String documentId {get; set;}

        @AuraEnabled public String name {get; set;}

        @AuraEnabled public String createdById {get; set;}

        public ImageWrapper(String itemId, String attachmentId, String documentId, String name, String createdById) {
            this.itemId = itemId;
            this.attachmentId = attachmentId;
            this.documentId = documentId;
            this.name = name;
            this.createdById = createdById;
        }
    }
}