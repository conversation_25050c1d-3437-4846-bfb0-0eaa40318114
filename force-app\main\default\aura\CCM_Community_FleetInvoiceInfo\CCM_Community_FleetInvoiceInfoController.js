({
    // add haibo: french
    doInit:function(component, event, helper){
        let yesOrNoList = [
            {'label': $A.get("$Label.c.CCM_Portal_YES"), 'value': true},
            {'label': $A.get("$Label.c.CCM_Portal_NO"), 'value': false}
        ];
        component.set('v.yesOrNoList', yesOrNoList);
    },
    handleFilesChange:function(component, event, helper){
        //默认是单文件上传
        var uploadedFiles = event.getSource().get("v.files")[0];
        helper.uploadReceiptHelper(component, uploadedFiles);
    },
    deleteFiles: function(component, event, helper){
        var selectedItem = event.currentTarget;// this will give current element
        var rowIndex = selectedItem.dataset.row;// this will give the count row index
        // console.log('rowIndex', rowIndex);
        helper.deleteReceiptHelper(component, rowIndex);
    },
    doGetCondition: function(component, event, helper){
        var addressIdListStr = component.get("v.addressIdListStr");
        // 分割字符串成为数组
        let arr1 = addressIdListStr.split(", ");
        // 使用 map 方法给每个元素添加单引号，并转换为字符串形式
        var filterStr = '(' + arr1.map(item => `'${item}'`).join(',') + ')';
        var filterCondition = [
            {
                "FieldName": "Id",
                "Condtion": "IN",
                "Value": filterStr
            }
        ];
        var filterConditionStr = JSON.stringify(filterCondition);
        var addressCondition = component.get('v.addressCondition');
        if(addressCondition != filterConditionStr){
            component.set('v.addressCondition', filterConditionStr);
        }

        var shipToAddressIdListStr = component.get("v.shipToAddressIdListStr");
        let arr2 = shipToAddressIdListStr.split(", ");
        // 使用 map 方法给每个元素添加单引号，并转换为字符串形式
        var shipToFilterStr = '(' + arr2.map(item => `'${item}'`).join(',') + ')';
        var shipToFilterCondition = [
            {
                "FieldName": "Id",
                "Condtion": "IN",
                "Value": shipToFilterStr
            }
        ];
        var shipToFilterConditionStr = JSON.stringify(shipToFilterCondition);
        var shipToAddressCondition = component.get('v.shipToAddressCondition');
        if(shipToAddressCondition != shipToFilterConditionStr){
            component.set('v.shipToAddressCondition', shipToFilterConditionStr);
        }
    },
    changeBillToAddressId: function(component, event, helper){
        var billToAddressName = component.get('v.fleetClaim.billToAddressName');
        if($A.util.isEmpty(billToAddressName)){
            component.set('v.fleetClaim.billToAddressId', null);
        }
    },
    changeShipToAddressId: function(component) {
        var shipToAddressName = component.get('v.fleetClaim.shipToAddressName');
        if($A.util.isEmpty(shipToAddressName)){
            component.set('v.fleetClaim.shipToAddressId', null);
        }
    },
    yesOrNoChange: function(component, event, helper){
        var value = event.getSource().get('v.value');
        if(value == 'true'){
            value = true;
        }else if(value == 'false'){
            value = false;
        }
        component.set('v.fleetClaim.deliverAtOnce', value);
    }
})