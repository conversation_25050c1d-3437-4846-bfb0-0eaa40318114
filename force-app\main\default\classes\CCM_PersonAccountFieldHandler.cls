public with sharing class CCM_PersonAccountField<PERSON>andler implements Triggers.Handler{
    public void handle() {
        List<RecordType> rd = [SELECT Id,Name,DeveloperName, SobjectType,IsPersonType
                                  FROM RecordType
                                  WHERE SobjectType='Account' AND IsPersonType=True AND DeveloperName='PersonAccount' limit 1];
        if (Trigger.isBefore && Trigger.isInsert) {
            for(Account account : (List<Account>)Trigger.new) {
                if (!rd.isEmpty() && account.RecordTypeId == rd[0].Id) {
                    account.Site_Origin__pc = account.Site_Origin__c;
                }
            }
        }

        if (Trigger.isBefore && Trigger.isInsert) {
            for(Account account : (List<Account>)Trigger.new) {
                if (account.Buying_Group__c == null && (account.RecordTypeId != CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID && account.RecordTypeId != CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID)) {
                    account.Buying_Group__c = '001Ul000008AaAUIA0';
                }
                if(account.Online_Selling__c == null || account.Online_Selling__c == ''){
                    account.Online_Selling__c = 'N/A';
                }
                if(account.PaymentMethod__c == null || account.PaymentMethod__c == ''){
                    account.PaymentMethod__c = 'N/A';
                }
            }
        }

        if (Trigger.isBefore && Trigger.isUpdate) {
            for(Account account : (List<Account>)Trigger.new) {
                if (account.Buying_Group__c == null && (account.RecordTypeId != CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID && account.RecordTypeId != CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID)) {
                    account.Buying_Group__c = '001Ul000008AaAUIA0';
                }
                if(account.Online_Selling__c == null || account.Online_Selling__c == ''){
                    account.Online_Selling__c = 'N/A';
                }
                if(account.PaymentMethod__c == null || account.PaymentMethod__c == ''){
                    account.PaymentMethod__c = 'N/A';
                }

            }
        }

        if (Trigger.isBefore && Trigger.isUpdate) {
            for(Account account : (List<Account>)Trigger.new) {
                Account oldAccount = (Account)Trigger.oldMap.get(account.Id);
                if (!rd.isEmpty() && account.RecordTypeId == rd[0].Id) {
                    if (account.Site_Origin__c != oldAccount.Site_Origin__c) {
                        account.Site_Origin__pc = account.Site_Origin__c;
                    }
                }
            }
        }
    }
}