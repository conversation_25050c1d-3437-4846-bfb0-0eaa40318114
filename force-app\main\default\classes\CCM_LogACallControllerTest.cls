/**
 * Test class for CCM_LogACallController
 */
@IsTest
public class CCM_LogACallControllerTest {

    @TestSetup
    static void makeData(){
        CCM_DealerLocationHandler2.isRun = false;
        Account acc = new Account();
        acc = (Account)CCM_TESTDataUtil.createSobject(acc, 'Channel');

        Account storeLocation = new Account();
        storeLocation.Related_Entity__c = acc.Id;
        storeLocation.Name = 'storeLocation Test';
        storeLocation = (Account)CCM_TESTDataUtil.createSobject(storeLocation, 'Existing_Store_Location');

        Contact con = new Contact();
        con.AccountId = acc.Id;
        con = (Contact)CCM_TESTDataUtil.createSobject(con, '');

        Account_Address__c address = new Account_Address__c();
        address.Customer__c = acc.Id;
        address = (Account_Address__c)CCM_TESTDataUtil.createSobject(address, 'Shipping_Address');

        ContentVersion cv = new ContentVersion();
        cv.Title = 'Test Document';
        cv.PathOnClient = 'TestDocument.pdf';
        cv.VersionData = Blob.valueOf('Test Content');
        cv.IsMajorVersion = true;
        Insert cv;
        
        CCM_Lead_Validation_Rules_Handler.isRun = false;
        Lead l = new Lead();
        l.LastName = 'test';
        insert l;

        con = new Contact();
        con.Prospect__c = l.Id;
        con = (Contact)CCM_TESTDataUtil.createSobject(con, '');

        address = new Account_Address__c();
        address.Prospect__c = l.Id;
        address = (Account_Address__c)CCM_TESTDataUtil.createSobject(address, 'Shipping_Address');

        CCM_Campaign_Validation_Rules_Handler.isRun = false;
        Campaign cam = new Campaign();
        cam.Name = 'campaign test';
        cam.Planned__c = 'Planned';
        cam.Expected_Budget__c = 1000;
        cam.Approval_Status__c = 'Approved';
        cam.Brands__c = 'EGO';
        cam.Target_Group__c = 'Dealer';
        cam.StartDate = Date.today();
        cam.EndDate = Date.today().addDays(5);
        insert cam;
    }


    @IsTest
    static void getSourceTest(){
        Test.startTest();
        Account acc = [SELECT Id FROM Account LIMIT 1];
        CCM_LogACallController.getSource(acc.Id, '');
        CCM_LogACallController.getStores(acc.Id, '');
        CCM_LogACallController.getStores(acc.Id, 'storeLocation Test');

        Lead l = [SELECT Id FROM Lead LIMIT 1];
        CCM_LogACallController.getSource(l.Id, '');

        Campaign cam = [SELECT Id FROM Campaign LIMIT 1];
        CCM_LogACallController.getSource(cam.Id, '');

        Test.stopTest();
    }


    @IsTest
    static void saveCallTest(){
        Test.startTest();
        Map<String, Object> paramMap = new Map<String, Object>();
        Account acc = [SELECT Id FROM Account LIMIT 1];
        paramMap.put('accountId', acc.Id);

        Contact con = [SELECT Id FROM Contact LIMIT 1];
        paramMap.put('contactId', con.Id);

        // Account_Address__c address = [SELECT Id FROM Account_Address__c LIMIT 1];
        // paramMap.put('storeVisitId', address.Id);

        paramMap.put('subject', 'Call');
        paramMap.put('comments', 'test');
        paramMap.put('visitDate', '2023-09-27');
        paramMap.put('actionItems', 'test');
        paramMap.put('callSubject', 'Product');
        paramMap.put('purpose', 'Product Demonstration');

        List<ContentDocument> conDocuments = [SELECT Id FROM ContentDocument];
        List<String> documentIds = new List<String>();
        for(ContentDocument conDocument : conDocuments) {
            documentIds.add(conDocument.Id);
        }
        paramMap.put('documentIds', documentIds);
        String param = JSON.serialize(paramMap);
        CCM_LogACallController.saveCall(param);

        CCM_LogACallController.getVersionDataUrl(documentIds, acc.Id);
        Task t = [SELECT Id FROM Task LIMIT 1];
        CCM_LogACallController.getContentVersions(t.Id);
        Test.stopTest();
    }


    @IsTest
    static void deleteFileTest(){  
        Test.startTest();
        List<ContentDocument> conDocuments = [SELECT Id FROM ContentDocument];
        for(ContentDocument doc : conDocuments) {
            CCM_LogACallController.deleteFile(doc.Id);
        }
        Test.stopTest();
    }
}