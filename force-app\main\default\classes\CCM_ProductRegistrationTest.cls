/**
 * @description CCM_ProductRegistration类的完整测试类
 * <AUTHOR> Assistant
 * @date 2024
 *
 * 测试覆盖范围：
 * - setupProductRegistration 方法
 * - WarrantyMasterProduct 方法
 * - changeMasterProduct 方法
 * - checkSNAndUpdateIndicator 方法
 * - findDealerHaveSameSerialNum 方法
 * - findProjectsAccordingSerialNum 方法
 * - isV5OrFC2 方法
 * - saveProductRegistration 方法
 * - SearchPartsListByMasterProduct 方法
 * - SearchPartsListByProduct 方法
 * - SearchProduct 方法
 * - SearchModelNumber 方法
 * - saveSurvey 方法
 * - 内部类测试
 */
@IsTest
public class CCM_ProductRegistrationTest {

    /**
     * @description 测试数据设置方法
     */
    @TestSetup
    static void setupTestData() {
        // 创建测试账户
        Account testAccount = TestDataFactory.createAccount();
        testAccount.Name = '测试客户账户';
        testAccount.Customer_Type2__c = '经销商';
        testAccount.Distributor_or_Dealer__c = 'Dealer';
        testAccount.ORG_Code__c = 'CCA';
        insert testAccount;

        // 创建联系人
        Contact testContact = TestDataFactory.createContact();
        testContact.AccountId = testAccount.Id;
        testContact.LastName = '测试联系人';
        insert testContact;

        // 创建用户
        Profile portalProfile = [SELECT Id FROM Profile WHERE Name = 'Customer Community User' LIMIT 1];
        User testUser = new User(
            FirstName = '测试',
            LastName = '用户',
            Email = '<EMAIL>',
            Username = 'testuser' + System.currentTimeMillis() + '@test.com',
            Alias = 'tuser',
            TimeZoneSidKey = 'America/Los_Angeles',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            ProfileId = portalProfile.Id,
            ContactId = testContact.Id
        );
        insert testUser;

        // 创建产品记录类型
        Map<String, String> productRTMap = QueryUtils.getRecordTypeMapBySObjectType('Product2');

        // 创建主产品
        Product2 masterProduct = new Product2();
        masterProduct.Name = '测试主产品';
        masterProduct.ProductCode = 'LM2011';
        masterProduct.Brand_Name__c = 'EGO';
        masterProduct.Source__c = 'PIM';
        masterProduct.IsActive = true;
        masterProduct.Kit__c = true;
        if (productRTMap.containsKey('Kit')) {
            masterProduct.RecordTypeId = productRTMap.get('Kit');
        }
        insert masterProduct;

        // 创建子产品
        Product2 subProduct = new Product2();
        subProduct.Name = '测试子产品';
        subProduct.ProductCode = 'LM2012';
        subProduct.Brand_Name__c = 'EGO';
        subProduct.Source__c = 'PIM';
        subProduct.IsActive = true;
        if (productRTMap.containsKey('Product')) {
            subProduct.RecordTypeId = productRTMap.get('Product');
        }
        insert subProduct;

        // 创建零件产品
        Product2 partsProduct = new Product2();
        partsProduct.Name = '测试零件';
        partsProduct.ProductCode = 'LM2013';
        partsProduct.Brand_Name__c = 'EGO';
        partsProduct.Source__c = 'PIM';
        partsProduct.IsActive = true;
        partsProduct.Wearing_Parts__c = true;
        if (productRTMap.containsKey('Parts')) {
            partsProduct.RecordTypeId = productRTMap.get('Parts');
        }
        insert partsProduct;

        // 创建Kit_Item__c记录
        Kit_Item__c kitItem = TestDataFactory.createKitItem(masterProduct.Id, subProduct.Id);
        kitItem.Sequence__c = '001';
        insert kitItem;

        // 创建零件Kit_Item__c记录
        Kit_Item__c partsKitItem = new Kit_Item__c();
        partsKitItem.Kit__c = masterProduct.Id;
        partsKitItem.Parts__c = partsProduct.Id;
        partsKitItem.Wearable_Parts__c = true;
        partsKitItem.Warranty_Day__c = '365';
        partsKitItem.Valid_Hours__c = 2.5;
        if (QueryUtils.getRecordTypeMapBySObjectType('Kit_Item__c').containsKey('Products_and_Parts')) {
            partsKitItem.RecordTypeId = QueryUtils.getRecordTypeMapBySObjectType('Kit_Item__c').get('Products_and_Parts');
        }
        insert partsKitItem;

        // 创建保修记录
        Warranty__c warranty = TestDataFactory.createWarranty();
        warranty.AccountCustomer__c = testAccount.Id;
        warranty.Master_Product__c = masterProduct.Id;
        warranty.Brand_Name__c = 'EGO';
        warranty.Product_Use_Type2__c = 'Residential';
        insert warranty;

        // 创建保修项目
        Warranty_Item__c warrantyItem = TestDataFactory.createWarrantyItem(warranty.Id);
        warrantyItem.Product__c = subProduct.Id;
        warrantyItem.Product_Code__c = 'LM2012';
        warrantyItem.Serial_Number__c = 'TEST123456789';
        insert warrantyItem;

        // 创建项目
        Project__c project = new Project__c();
        project.Brand_Name__c = 'EGO';
        project.Product_Code__c = 'LM2012';
        project.Star_Time__c = Date.today().addDays(-30);
        project.Deadline__c = Date.today().addDays(30);
        project.Send_Email__c = false;
        project.Recal_lReason__c = '测试召回原因';
        insert project;

        // 创建项目序列号
        Project_SN__c projectSN = new Project_SN__c();
        projectSN.Project__c = project.Id;
        projectSN.Star_SN__c = 'TEST123456789';
        projectSN.End_SN__c = 'TEST123456789';
        insert projectSN;

        // 创建价格手册
        Pricebook2 testPricebook = TestDataFactory.createPriceBook();

        // 创建标准价格手册条目
        PricebookEntry standardEntry = TestDataFactory.createPricebookEntry(
            Test.getStandardPricebookId(),
            partsProduct.Id,
            100.00
        );

        // 创建自定义价格手册条目
        PricebookEntry customEntry = TestDataFactory.createPricebookEntry(
            testPricebook.Id,
            partsProduct.Id,
            80.00
        );

        // 创建客户品牌价格手册映射
        Customer_Brand_Pricebook_Mapping__c pricebookMapping = new Customer_Brand_Pricebook_Mapping__c();
        pricebookMapping.Name = 'CA-Direct Dealer Price for Parts';
        pricebookMapping.Type__c = 'Service';
        pricebookMapping.Is_Active__c = true;
        pricebookMapping.Price_Book__c = testPricebook.Id;
        insert pricebookMapping;
    }

    /**
     * @description 测试setupProductRegistration方法 - 正常情况
     */
    @IsTest
    static void testSetupProductRegistration_Success() {
        // 获取测试数据
        User testUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1];

        Test.startTest();
        System.runAs(testUser) {
            String result = CCM_ProductRegistration.setupProductRegistration(testUser.Id);

            // 验证返回结果
            System.assertNotEquals(null, result, '返回结果不应为空');

            Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
            System.assert(resultMap.containsKey('CustomerId'), '结果应包含CustomerId');
            System.assert(resultMap.containsKey('Name'), '结果应包含Name');
            System.assert(resultMap.containsKey('customerTypeList'), '结果应包含customerTypeList');
        }
        Test.stopTest();
    }

    /**
     * @description 测试setupProductRegistration方法 - 测试环境
     */
    @IsTest
    static void testSetupProductRegistration_TestEnvironment() {
        Account testAccount = [SELECT Id FROM Account WHERE Name = '测试客户账户' LIMIT 1];

        Test.startTest();
        String result = CCM_ProductRegistration.setupProductRegistration(testAccount.Id);

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');

        Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
        System.assert(resultMap.containsKey('CustomerId'), '结果应包含CustomerId');
        System.assert(resultMap.containsKey('Name'), '结果应包含Name');
        System.assert(resultMap.containsKey('customerTypeList'), '结果应包含customerTypeList');
        Test.stopTest();
    }

    /**
     * @description 测试WarrantyMasterProduct方法 - 正常情况
     */
    @IsTest
    static void testWarrantyMasterProduct_Success() {
        Test.startTest();
        String result = CCM_ProductRegistration.WarrantyMasterProduct('EGO', 'LM');

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');

        List<Object> resultList = (List<Object>) JSON.deserializeUntyped(result);
        System.assert(resultList.size() > 0, '应该返回产品列表');
        Test.stopTest();
    }

    /**
     * @description 测试WarrantyMasterProduct方法 - 空过滤条件
     */
    @IsTest
    static void testWarrantyMasterProduct_EmptyFilter() {
        Test.startTest();
        String result = CCM_ProductRegistration.WarrantyMasterProduct('EGO', '');

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');
        Test.stopTest();
    }

    /**
     * @description 测试changeMasterProduct方法 - 正常情况
     */
    @IsTest
    static void testChangeMasterProduct_Success() {
        Product2 masterProduct = [SELECT Id FROM Product2 WHERE ProductCode = 'LM2011' LIMIT 1];

        Test.startTest();
        String result = CCM_ProductRegistration.changeMasterProduct(masterProduct.Id);

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');

        Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
        System.assert(resultMap.containsKey('productCode'), '结果应包含productCode');
        System.assert(resultMap.containsKey('proList'), '结果应包含proList');
        Test.stopTest();
    }

    /**
     * @description 测试changeMasterProduct方法 - 空产品ID
     */
    @IsTest
    static void testChangeMasterProduct_NullProductId() {
        Test.startTest();
        String result = CCM_ProductRegistration.changeMasterProduct(null);

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');

        Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
        System.assert(resultMap.containsKey('Message'), '结果应包含错误消息');
        System.assertEquals('Product Id can not be null', resultMap.get('Message'), '错误消息应正确');
        Test.stopTest();
    }

    /**
     * @description 测试changeMasterProduct方法 - 空字符串产品ID
     */
    @IsTest
    static void testChangeMasterProduct_EmptyProductId() {
        Test.startTest();
        String result = CCM_ProductRegistration.changeMasterProduct('');

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');

        Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
        System.assert(resultMap.containsKey('Message'), '结果应包含错误消息');
        System.assertEquals('Product Id can not be null', resultMap.get('Message'), '错误消息应正确');
        Test.stopTest();
    }

    /**
     * @description 测试checkSNAndUpdateIndicator方法 - 正常情况
     */
    @IsTest
    static void testCheckSNAndUpdateIndicator_Success() {
        Product2 masterProduct = [SELECT Id, ProductCode FROM Product2 WHERE ProductCode = 'LM2011' LIMIT 1];

        // 创建测试数据
        CCM_ProductRegistration.WarrantyItemWrapper wrapper = new CCM_ProductRegistration.WarrantyItemWrapper(
            masterProduct.ProductCode,
            new List<CCM_ProductRegistration.Product3>()
        );

        Warranty_Item__c testItem = new Warranty_Item__c();
        testItem.Product_Code__c = 'LM2012';
        testItem.Serial_Number__c = 'VALID123456789';

        CCM_ProductRegistration.Product3 product3 = new CCM_ProductRegistration.Product3(testItem);
        wrapper.proList.add(product3);

        String proListStr = JSON.serialize(wrapper);

        Test.startTest();
        String result = CCM_ProductRegistration.checkSNAndUpdateIndicator('EGO', proListStr);

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');

        CCM_ProductRegistration.WarrantyItemWrapper resultWrapper =
            (CCM_ProductRegistration.WarrantyItemWrapper) JSON.deserialize(result, CCM_ProductRegistration.WarrantyItemWrapper.class);
        System.assertNotEquals(null, resultWrapper, '结果包装器不应为空');
        System.assert(resultWrapper.proList.size() > 0, '产品列表不应为空');
        Test.stopTest();
    }

    /**
     * @description 测试checkSNAndUpdateIndicator方法 - 召回项目序列号
     */
    @IsTest
    static void testCheckSNAndUpdateIndicator_RecallProject() {
        Product2 masterProduct = [SELECT Id, ProductCode FROM Product2 WHERE ProductCode = 'LM2011' LIMIT 1];

        // 创建测试数据
        CCM_ProductRegistration.WarrantyItemWrapper wrapper = new CCM_ProductRegistration.WarrantyItemWrapper(
            masterProduct.ProductCode,
            new List<CCM_ProductRegistration.Product3>()
        );

        Warranty_Item__c testItem = new Warranty_Item__c();
        testItem.Product_Code__c = 'LM2012';
        testItem.Serial_Number__c = 'TEST123456789'; // 这个序列号在项目中

        CCM_ProductRegistration.Product3 product3 = new CCM_ProductRegistration.Product3(testItem);
        wrapper.proList.add(product3);

        String proListStr = JSON.serialize(wrapper);

        Test.startTest();
        String result = CCM_ProductRegistration.checkSNAndUpdateIndicator('EGO', proListStr);

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');

        CCM_ProductRegistration.WarrantyItemWrapper resultWrapper =
            (CCM_ProductRegistration.WarrantyItemWrapper) JSON.deserialize(result, CCM_ProductRegistration.WarrantyItemWrapper.class);
        System.assertNotEquals(null, resultWrapper, '结果包装器不应为空');
        System.assert(resultWrapper.proList.size() > 0, '产品列表不应为空');

        // 验证召回项目标记
        CCM_ProductRegistration.Product3 resultProduct = resultWrapper.proList[0];
        System.assertEquals(true, resultProduct.inRecallProject, '应该标记为召回项目');
        Test.stopTest();
    }

    /**
     * @description 测试findDealerHaveSameSerialNum方法 - 存在相同序列号
     */
    @IsTest
    static void testFindDealerHaveSameSerialNum_Exists() {
        Account testAccount = [SELECT Id FROM Account WHERE Name = '测试客户账户' LIMIT 1];

        Test.startTest();
        Boolean result = CCM_ProductRegistration.findDealerHaveSameSerialNum('TEST123456789', testAccount.Id);

        // 验证返回结果
        System.assertEquals(true, result, '应该找到相同的序列号');
        Test.stopTest();
    }

    /**
     * @description 测试findDealerHaveSameSerialNum方法 - 不存在相同序列号
     */
    @IsTest
    static void testFindDealerHaveSameSerialNum_NotExists() {
        Account testAccount = [SELECT Id FROM Account WHERE Name = '测试客户账户' LIMIT 1];

        Test.startTest();
        Boolean result = CCM_ProductRegistration.findDealerHaveSameSerialNum('NOTEXIST123456789', testAccount.Id);

        // 验证返回结果
        System.assertEquals(false, result, '不应该找到相同的序列号');
        Test.stopTest();
    }

    /**
     * @description 测试isV5OrFC2方法 - V5格式
     */
    @IsTest
    static void testIsV5OrFC2_V5Format() {
        Test.startTest();
        Boolean result = CCM_ProductRegistration.isV5OrFC2('ABC1DEF');

        // 验证返回结果
        System.assertEquals(true, result, 'V5格式应该返回true');
        Test.stopTest();
    }

    /**
     * @description 测试isV5OrFC2方法 - 非V5格式
     */
    @IsTest
    static void testIsV5OrFC2_NonV5Format() {
        Test.startTest();
        Boolean result = CCM_ProductRegistration.isV5OrFC2('ABCADEF');

        // 验证返回结果
        System.assertEquals(false, result, '非V5格式应该返回false');
        Test.stopTest();
    }

    /**
     * @description 测试isV5OrFC2方法 - 空字符串
     */
    @IsTest
    static void testIsV5OrFC2_EmptyString() {
        Test.startTest();
        Boolean result = CCM_ProductRegistration.isV5OrFC2('');

        // 验证返回结果
        System.assertEquals(false, result, '空字符串应该返回false');
        Test.stopTest();
    }

    /**
     * @description 测试isV5OrFC2方法 - null值
     */
    @IsTest
    static void testIsV5OrFC2_NullValue() {
        Test.startTest();
        Boolean result = CCM_ProductRegistration.isV5OrFC2(null);

        // 验证返回结果
        System.assertEquals(false, result, 'null值应该返回false');
        Test.stopTest();
    }

    /**
     * @description 测试SearchPartsListByMasterProduct方法 - 正常情况
     */
    @IsTest
    static void testSearchPartsListByMasterProduct_Success() {
        Product2 masterProduct = [SELECT Id FROM Product2 WHERE ProductCode = 'LM2011' LIMIT 1];
        User testUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1];

        Test.startTest();
        System.runAs(testUser) {
            String result = CCM_ProductRegistration.SearchPartsListByMasterProduct(masterProduct.Id, '');

            // 验证返回结果
            System.assertNotEquals(null, result, '返回结果不应为空');

            Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
            System.assert(resultMap.containsKey('PartsList'), '结果应包含PartsList');
        }
        Test.stopTest();
    }

    /**
     * @description 测试SearchPartsListByMasterProduct方法 - 带过滤条件
     */
    @IsTest
    static void testSearchPartsListByMasterProduct_WithFilter() {
        Product2 masterProduct = [SELECT Id FROM Product2 WHERE ProductCode = 'LM2011' LIMIT 1];
        User testUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1];

        Test.startTest();
        System.runAs(testUser) {
            String result = CCM_ProductRegistration.SearchPartsListByMasterProduct(masterProduct.Id, 'LM');

            // 验证返回结果
            System.assertNotEquals(null, result, '返回结果不应为空');

            Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
            System.assert(resultMap.containsKey('PartsList'), '结果应包含PartsList');
        }
        Test.stopTest();
    }

    /**
     * @description 测试SearchPartsListByProduct方法 - 正常情况
     */
    @IsTest
    static void testSearchPartsListByProduct_Success() {
        Product2 subProduct = [SELECT Id FROM Product2 WHERE ProductCode = 'LM2012' LIMIT 1];
        User testUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1];

        Test.startTest();
        System.runAs(testUser) {
            String result = CCM_ProductRegistration.SearchPartsListByProduct(subProduct.Id, '');

            // 验证返回结果
            System.assertNotEquals(null, result, '返回结果不应为空');
        }
        Test.stopTest();
    }

    /**
     * @description 测试SearchPartsListByProduct方法 - 带过滤条件
     */
    @IsTest
    static void testSearchPartsListByProduct_WithFilter() {
        Product2 subProduct = [SELECT Id FROM Product2 WHERE ProductCode = 'LM2012' LIMIT 1];
        User testUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1];

        Test.startTest();
        System.runAs(testUser) {
            String result = CCM_ProductRegistration.SearchPartsListByProduct(subProduct.Id, 'LM');

            // 验证返回结果
            System.assertNotEquals(null, result, '返回结果不应为空');
        }
        Test.stopTest();
    }

    /**
     * @description 测试SearchProduct方法 - 正常情况
     */
    @IsTest
    static void testSearchProduct_Success() {
        Test.startTest();
        String result = CCM_ProductRegistration.SearchProduct('LM');

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');

        Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
        System.assert(resultMap.containsKey('value'), '结果应包含value');
        Test.stopTest();
    }

    /**
     * @description 测试SearchProduct方法 - 法语环境
     */
    @IsTest
    static void testSearchProduct_French() {
        // 创建法语产品
        Product2 frenchProduct = new Product2();
        frenchProduct.Name = 'Test French Product';
        frenchProduct.ProductCode = 'FR001';
        frenchProduct.Product_Name_French__c = 'Produit Test Français';
        frenchProduct.Source__c = 'PIM';
        frenchProduct.IsActive = true;
        frenchProduct.RecordTypeId = QueryUtils.getRecordTypeIdBySobjectTypeAndDeveloperName('Product2', 'Product');
        insert frenchProduct;

        Test.startTest();
        String result = CCM_ProductRegistration.SearchProduct('FR');

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');

        Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
        System.assert(resultMap.containsKey('value'), '结果应包含value');
        Test.stopTest();
    }

    /**
     * @description 测试SearchModelNumber方法 - 正常情况
     */
    @IsTest
    static void testSearchModelNumber_Success() {
        Test.startTest();
        String result = CCM_ProductRegistration.SearchModelNumber('LM');

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');

        Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
        System.assert(resultMap.containsKey('value'), '结果应包含value');
        Test.stopTest();
    }

    /**
     * @description 测试saveProductRegistration方法 - 正常情况
     */
    @IsTest
    static void testSaveProductRegistration_Success() {
        Product2 masterProduct = [SELECT Id FROM Product2 WHERE ProductCode = 'LM2011' LIMIT 1];
        Account testAccount = [SELECT Id FROM Account WHERE Name = '测试客户账户' LIMIT 1];
        User testUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1];

        // 创建产品注册数据
        Map<String, Object> productRegistration = new Map<String, Object>();
        productRegistration.put('masterProduct', masterProduct.Id);
        productRegistration.put('purchasePlace', 'Home Depot');
        productRegistration.put('purchaseDate', String.valueOf(Date.today()));
        productRegistration.put('purchaseUseType', 'Residential');
        productRegistration.put('brand', 'EGO');
        productRegistration.put('lostReceipt', false);

        // 创建保修项目数据
        CCM_ProductRegistration.WarrantyItemWrapper wrapper = new CCM_ProductRegistration.WarrantyItemWrapper(
            'LM2011',
            new List<CCM_ProductRegistration.Product3>()
        );

        Warranty_Item__c testItem = new Warranty_Item__c();
        testItem.Product_Code__c = 'LM2012';
        testItem.Serial_Number__c = 'NEW123456789';

        CCM_ProductRegistration.Product3 product3 = new CCM_ProductRegistration.Product3(testItem);
        wrapper.proList.add(product3);

        String proListStr = JSON.serialize(wrapper);

        Test.startTest();
        System.runAs(testUser) {
            String result = CCM_ProductRegistration.saveProductRegistration(
                JSON.serialize(productRegistration),
                proListStr,
                null
            );

            // 验证返回结果
            System.assertNotEquals(null, result, '返回结果不应为空');

            Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
            System.assert(resultMap.containsKey('isSuccess'), '结果应包含isSuccess');
        }
        Test.stopTest();
    }

    /**
     * @description 测试saveProductRegistration方法 - 丢失收据情况
     */
    @IsTest
    static void testSaveProductRegistration_LostReceipt() {
        Product2 masterProduct = [SELECT Id FROM Product2 WHERE ProductCode = 'LM2011' LIMIT 1];
        Account testAccount = [SELECT Id FROM Account WHERE Name = '测试客户账户' LIMIT 1];
        User testUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1];

        // 创建产品注册数据
        Map<String, Object> productRegistration = new Map<String, Object>();
        productRegistration.put('masterProduct', masterProduct.Id);
        productRegistration.put('purchasePlace', 'Home Depot');
        productRegistration.put('purchaseDate', String.valueOf(Date.today()));
        productRegistration.put('purchaseUseType', 'Residential');
        productRegistration.put('brand', 'EGO');
        productRegistration.put('lostReceipt', true);

        // 创建保修项目数据
        CCM_ProductRegistration.WarrantyItemWrapper wrapper = new CCM_ProductRegistration.WarrantyItemWrapper(
            'LM2011',
            new List<CCM_ProductRegistration.Product3>()
        );

        Warranty_Item__c testItem = new Warranty_Item__c();
        testItem.Product_Code__c = 'LM2012';
        testItem.Serial_Number__c = 'LOST123456789';

        CCM_ProductRegistration.Product3 product3 = new CCM_ProductRegistration.Product3(testItem);
        wrapper.proList.add(product3);

        String proListStr = JSON.serialize(wrapper);

        Test.startTest();
        System.runAs(testUser) {
            String result = CCM_ProductRegistration.saveProductRegistration(
                JSON.serialize(productRegistration),
                proListStr,
                null
            );

            // 验证返回结果
            System.assertNotEquals(null, result, '返回结果不应为空');

            Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
            System.assert(resultMap.containsKey('isSuccess'), '结果应包含isSuccess');
        }
        Test.stopTest();
    }

    /**
     * @description 测试saveSurvey方法 - 正常情况
     */
    @IsTest
    static void testSaveSurvey_Success() {
        Warranty__c warranty = [SELECT Id FROM Warranty__c LIMIT 1];

        // 创建调查请求数据
        Map<String, Object> surveyRequest = new Map<String, Object>();
        surveyRequest.put('warrantyId', warranty.Id);
        surveyRequest.put('surveyData', new List<Object>());

        Test.startTest();
        try {
            String result = CCM_ProductRegistration.saveSurvey(JSON.serialize(surveyRequest));

            // 验证返回结果
            System.assertNotEquals(null, result, '返回结果不应为空');
        } catch (CCM_CustomException e) {
            // 测试环境下预期的异常
            System.assert(e.getMessage().contains('test'), '应该抛出测试异常');
        }
        Test.stopTest();
    }

    /**
     * @description 测试内部类 - parts类
     */
    @IsTest
    static void testPartsClass() {
        Test.startTest();
        CCM_ProductRegistration.parts testParts = new CCM_ProductRegistration.parts();

        // 设置属性
        testParts.kitItemId = 'testKitItemId';
        testParts.partsId = 'testPartsId';
        testParts.showName = '测试零件名称';
        testParts.Name = '测试零件';
        testParts.ProductCode = 'PART001';
        testParts.ItemNumber = 'ITEM001';
        testParts.price = 100.00;
        testParts.LaborTime = 2.5;
        testParts.isWearable = true;
        testParts.partsWearable = true;
        testParts.wearablePeriodCommercial = '365天';
        testParts.wearablePeriodResidential = '180天';
        testParts.warrantyDate = 365;
        testParts.fakePrice = 80.00;

        // 验证属性设置
        System.assertEquals('testKitItemId', testParts.kitItemId, 'kitItemId应该正确设置');
        System.assertEquals('testPartsId', testParts.partsId, 'partsId应该正确设置');
        System.assertEquals('测试零件名称', testParts.showName, 'showName应该正确设置');
        System.assertEquals('测试零件', testParts.Name, 'Name应该正确设置');
        System.assertEquals('PART001', testParts.ProductCode, 'ProductCode应该正确设置');
        System.assertEquals('ITEM001', testParts.ItemNumber, 'ItemNumber应该正确设置');
        System.assertEquals(100.00, testParts.price, 'price应该正确设置');
        System.assertEquals(2.5, testParts.LaborTime, 'LaborTime应该正确设置');
        System.assertEquals(true, testParts.isWearable, 'isWearable应该正确设置');
        System.assertEquals(true, testParts.partsWearable, 'partsWearable应该正确设置');
        System.assertEquals('365天', testParts.wearablePeriodCommercial, 'wearablePeriodCommercial应该正确设置');
        System.assertEquals('180天', testParts.wearablePeriodResidential, 'wearablePeriodResidential应该正确设置');
        System.assertEquals(365, testParts.warrantyDate, 'warrantyDate应该正确设置');
        System.assertEquals(80.00, testParts.fakePrice, 'fakePrice应该正确设置');
        Test.stopTest();
    }

    /**
     * @description 测试内部类 - SelectOptions类
     */
    @IsTest
    static void testSelectOptionsClass() {
        Test.startTest();
        CCM_ProductRegistration.SelectOptions testOptions = new CCM_ProductRegistration.SelectOptions();

        // 设置属性
        testOptions.Id = 'testId';
        testOptions.Name = '测试选项';
        testOptions.ProductCode = 'OPT001';

        // 验证属性设置
        System.assertEquals('testId', testOptions.Id, 'Id应该正确设置');
        System.assertEquals('测试选项', testOptions.Name, 'Name应该正确设置');
        System.assertEquals('OPT001', testOptions.ProductCode, 'ProductCode应该正确设置');
        Test.stopTest();
    }

    /**
     * @description 测试内部类 - Product3类
     */
    @IsTest
    static void testProduct3Class() {
        Test.startTest();
        Warranty_Item__c testItem = new Warranty_Item__c();
        testItem.Product_Code__c = 'TEST001';
        testItem.Serial_Number__c = 'SN123456789';

        CCM_ProductRegistration.Product3 testProduct3 = new CCM_ProductRegistration.Product3(testItem);

        // 验证默认值
        System.assertEquals(testItem, testProduct3.warrantyItem, 'warrantyItem应该正确设置');
        System.assertEquals(true, testProduct3.isSelect, 'isSelect默认应该为true');
        System.assertEquals(true, testProduct3.isFormatCorrect, 'isFormatCorrect默认应该为true');
        System.assertEquals(false, testProduct3.inRecallProject, 'inRecallProject默认应该为false');
        System.assertEquals(false, testProduct3.hasReplace, 'hasReplace默认应该为false');
        System.assertNotEquals(null, testProduct3.replaceCodeList, 'replaceCodeList不应该为null');
        System.assertNotEquals(null, testProduct3.replaceProMap, 'replaceProMap不应该为null');

        // 设置其他属性
        testProduct3.snFormatErrorMessage = '序列号格式错误';
        testProduct3.isSelect = false;
        testProduct3.hasReplace = true;
        testProduct3.isFormatCorrect = false;
        testProduct3.inRecallProject = true;
        testProduct3.surveyId = 'survey123';
        testProduct3.surveyTitle = '测试调查';
        testProduct3.surveyComments = '测试评论';

        // 验证属性设置
        System.assertEquals('序列号格式错误', testProduct3.snFormatErrorMessage, 'snFormatErrorMessage应该正确设置');
        System.assertEquals(false, testProduct3.isSelect, 'isSelect应该正确设置');
        System.assertEquals(true, testProduct3.hasReplace, 'hasReplace应该正确设置');
        System.assertEquals(false, testProduct3.isFormatCorrect, 'isFormatCorrect应该正确设置');
        System.assertEquals(true, testProduct3.inRecallProject, 'inRecallProject应该正确设置');
        System.assertEquals('survey123', testProduct3.surveyId, 'surveyId应该正确设置');
        System.assertEquals('测试调查', testProduct3.surveyTitle, 'surveyTitle应该正确设置');
        System.assertEquals('测试评论', testProduct3.surveyComments, 'surveyComments应该正确设置');
        Test.stopTest();
    }

    /**
     * @description 测试内部类 - WarrantyItemWrapper类
     */
    @IsTest
    static void testWarrantyItemWrapperClass() {
        Test.startTest();
        List<CCM_ProductRegistration.Product3> testProList = new List<CCM_ProductRegistration.Product3>();

        Warranty_Item__c testItem = new Warranty_Item__c();
        testItem.Product_Code__c = 'TEST001';
        testItem.Serial_Number__c = 'SN123456789';

        CCM_ProductRegistration.Product3 testProduct3 = new CCM_ProductRegistration.Product3(testItem);
        testProList.add(testProduct3);

        CCM_ProductRegistration.WarrantyItemWrapper testWrapper =
            new CCM_ProductRegistration.WarrantyItemWrapper('WRAP001', testProList);

        // 验证属性设置
        System.assertEquals('WRAP001', testWrapper.productCode, 'productCode应该正确设置');
        System.assertEquals(testProList, testWrapper.proList, 'proList应该正确设置');
        System.assertEquals(1, testWrapper.proList.size(), 'proList大小应该为1');
        Test.stopTest();
    }

    /**
     * @description 测试内部类 - comboBoxOptions类
     */
    @IsTest
    static void testComboBoxOptionsClass() {
        Test.startTest();
        CCM_ProductRegistration.comboBoxOptions testOptions = new CCM_ProductRegistration.comboBoxOptions();

        // 设置属性
        testOptions.label = '测试标签';
        testOptions.value = 'testValue';

        // 验证属性设置
        System.assertEquals('测试标签', testOptions.label, 'label应该正确设置');
        System.assertEquals('testValue', testOptions.value, 'value应该正确设置');
        Test.stopTest();
    }

    /**
     * @description 测试JSON序列化和反序列化
     */
    @IsTest
    static void testJSONSerialization() {
        Test.startTest();

        // 创建测试数据
        Warranty_Item__c testItem = new Warranty_Item__c();
        testItem.Product_Code__c = 'JSON001';
        testItem.Serial_Number__c = 'JSONSN123456789';

        CCM_ProductRegistration.Product3 testProduct3 = new CCM_ProductRegistration.Product3(testItem);
        testProduct3.snFormatErrorMessage = 'JSON测试错误';
        testProduct3.isSelect = false;

        List<CCM_ProductRegistration.Product3> testProList = new List<CCM_ProductRegistration.Product3>();
        testProList.add(testProduct3);

        CCM_ProductRegistration.WarrantyItemWrapper testWrapper =
            new CCM_ProductRegistration.WarrantyItemWrapper('JSON001', testProList);

        // 序列化
        String jsonString = JSON.serialize(testWrapper);
        System.assertNotEquals(null, jsonString, 'JSON序列化结果不应为空');
        System.assert(jsonString.contains('JSON001'), 'JSON应包含productCode');

        // 反序列化
        CCM_ProductRegistration.WarrantyItemWrapper deserializedWrapper =
            (CCM_ProductRegistration.WarrantyItemWrapper) JSON.deserialize(jsonString, CCM_ProductRegistration.WarrantyItemWrapper.class);

        // 验证反序列化结果
        System.assertNotEquals(null, deserializedWrapper, '反序列化结果不应为空');
        System.assertEquals('JSON001', deserializedWrapper.productCode, 'productCode应该正确反序列化');
        System.assertEquals(1, deserializedWrapper.proList.size(), 'proList大小应该正确反序列化');
        System.assertEquals('JSON测试错误', deserializedWrapper.proList[0].snFormatErrorMessage, 'snFormatErrorMessage应该正确反序列化');
        System.assertEquals(false, deserializedWrapper.proList[0].isSelect, 'isSelect应该正确反序列化');

        Test.stopTest();
    }

    /**
     * @description 测试customerInfo方法 - 单参数版本
     */
    @IsTest
    static void testCustomerInfo_SingleParam() {
        Test.startTest();
        String result = CCM_ProductRegistration.customerInfo('<EMAIL>');

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');
        Test.stopTest();
    }

    /**
     * @description 测试customerInfo方法 - 双参数版本
     */
    @IsTest
    static void testCustomerInfo_DoubleParam() {
        Test.startTest();
        String result = CCM_ProductRegistration.customerInfo('<EMAIL>', '123456789');

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');

        Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
        System.assert(resultMap.containsKey('result'), '结果应包含result');
        Test.stopTest();
    }

    /**
     * @description 测试customerInfo方法 - 黑名单邮箱
     */
    @IsTest
    static void testCustomerInfo_BlacklistEmail() {
        Test.startTest();
        // 使用可能在黑名单中的邮箱格式
        String result = CCM_ProductRegistration.customerInfo('<EMAIL>', '123456789');

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');

        Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
        System.assert(resultMap.containsKey('result'), '结果应包含result');
        Test.stopTest();
    }

    /**
     * @description 测试checkSNAndUpdateIndicator方法 - FLEX品牌
     */
    @IsTest
    static void testCheckSNAndUpdateIndicator_FLEX() {
        Product2 masterProduct = [SELECT Id, ProductCode FROM Product2 WHERE ProductCode = 'LM2011' LIMIT 1];

        // 创建测试数据
        CCM_ProductRegistration.WarrantyItemWrapper wrapper = new CCM_ProductRegistration.WarrantyItemWrapper(
            masterProduct.ProductCode,
            new List<CCM_ProductRegistration.Product3>()
        );

        Warranty_Item__c testItem = new Warranty_Item__c();
        testItem.Product_Code__c = 'LM2012';
        testItem.Serial_Number__c = 'FLEX123456789'; // FLEX格式序列号

        CCM_ProductRegistration.Product3 product3 = new CCM_ProductRegistration.Product3(testItem);
        wrapper.proList.add(product3);

        String proListStr = JSON.serialize(wrapper);

        Test.startTest();
        String result = CCM_ProductRegistration.checkSNAndUpdateIndicator('FLEX', proListStr);

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');

        CCM_ProductRegistration.WarrantyItemWrapper resultWrapper =
            (CCM_ProductRegistration.WarrantyItemWrapper) JSON.deserialize(result, CCM_ProductRegistration.WarrantyItemWrapper.class);
        System.assertNotEquals(null, resultWrapper, '结果包装器不应为空');
        Test.stopTest();
    }

    /**
     * @description 测试checkSNAndUpdateIndicator方法 - Skil品牌
     */
    @IsTest
    static void testCheckSNAndUpdateIndicator_Skil() {
        Product2 masterProduct = [SELECT Id, ProductCode FROM Product2 WHERE ProductCode = 'LM2011' LIMIT 1];

        // 创建测试数据
        CCM_ProductRegistration.WarrantyItemWrapper wrapper = new CCM_ProductRegistration.WarrantyItemWrapper(
            masterProduct.ProductCode,
            new List<CCM_ProductRegistration.Product3>()
        );

        Warranty_Item__c testItem = new Warranty_Item__c();
        testItem.Product_Code__c = 'LM2012';
        testItem.Serial_Number__c = 'SKIL12345'; // Skil格式序列号

        CCM_ProductRegistration.Product3 product3 = new CCM_ProductRegistration.Product3(testItem);
        wrapper.proList.add(product3);

        String proListStr = JSON.serialize(wrapper);

        Test.startTest();
        String result = CCM_ProductRegistration.checkSNAndUpdateIndicator('Skil', proListStr);

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');

        CCM_ProductRegistration.WarrantyItemWrapper resultWrapper =
            (CCM_ProductRegistration.WarrantyItemWrapper) JSON.deserialize(result, CCM_ProductRegistration.WarrantyItemWrapper.class);
        System.assertNotEquals(null, resultWrapper, '结果包装器不应为空');
        Test.stopTest();
    }

    /**
     * @description 测试checkSNAndUpdateIndicator方法 - SkilSaw品牌
     */
    @IsTest
    static void testCheckSNAndUpdateIndicator_SkilSaw() {
        Product2 masterProduct = [SELECT Id, ProductCode FROM Product2 WHERE ProductCode = 'LM2011' LIMIT 1];

        // 创建测试数据
        CCM_ProductRegistration.WarrantyItemWrapper wrapper = new CCM_ProductRegistration.WarrantyItemWrapper(
            masterProduct.ProductCode,
            new List<CCM_ProductRegistration.Product3>()
        );

        Warranty_Item__c testItem = new Warranty_Item__c();
        testItem.Product_Code__c = 'LM2012';
        testItem.Serial_Number__c = 'SKILSAW12'; // SkilSaw格式序列号

        CCM_ProductRegistration.Product3 product3 = new CCM_ProductRegistration.Product3(testItem);
        wrapper.proList.add(product3);

        String proListStr = JSON.serialize(wrapper);

        Test.startTest();
        String result = CCM_ProductRegistration.checkSNAndUpdateIndicator('SkilSaw', proListStr);

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');

        CCM_ProductRegistration.WarrantyItemWrapper resultWrapper =
            (CCM_ProductRegistration.WarrantyItemWrapper) JSON.deserialize(result, CCM_ProductRegistration.WarrantyItemWrapper.class);
        System.assertNotEquals(null, resultWrapper, '结果包装器不应为空');
        Test.stopTest();
    }

    /**
     * @description 测试checkSNAndUpdateIndicator方法 - EGO品牌16位序列号
     */
    @IsTest
    static void testCheckSNAndUpdateIndicator_EGO16Digit() {
        Product2 masterProduct = [SELECT Id, ProductCode FROM Product2 WHERE ProductCode = 'LM2011' LIMIT 1];

        // 创建测试数据
        CCM_ProductRegistration.WarrantyItemWrapper wrapper = new CCM_ProductRegistration.WarrantyItemWrapper(
            masterProduct.ProductCode,
            new List<CCM_ProductRegistration.Product3>()
        );

        Warranty_Item__c testItem = new Warranty_Item__c();
        testItem.Product_Code__c = 'LM2012';
        testItem.Serial_Number__c = 'EGO1234567890123'; // EGO 16位序列号

        CCM_ProductRegistration.Product3 product3 = new CCM_ProductRegistration.Product3(testItem);
        wrapper.proList.add(product3);

        String proListStr = JSON.serialize(wrapper);

        Test.startTest();
        String result = CCM_ProductRegistration.checkSNAndUpdateIndicator('EGO', proListStr);

        // 验证返回结果
        System.assertNotEquals(null, result, '返回结果不应为空');

        CCM_ProductRegistration.WarrantyItemWrapper resultWrapper =
            (CCM_ProductRegistration.WarrantyItemWrapper) JSON.deserialize(result, CCM_ProductRegistration.WarrantyItemWrapper.class);
        System.assertNotEquals(null, resultWrapper, '结果包装器不应为空');
        Test.stopTest();
    }

    /**
     * @description 测试WarrantyMasterProduct方法 - Global用户
     */
    @IsTest
    static void testWarrantyMasterProduct_GlobalUser() {
        // 创建Global用户
        User globalUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1];
        globalUser.Country_of_Origin__c = 'Global';
        update globalUser;

        Test.startTest();
        System.runAs(globalUser) {
            String result = CCM_ProductRegistration.WarrantyMasterProduct('EGO', 'LM');

            // 验证返回结果
            System.assertNotEquals(null, result, '返回结果不应为空');

            List<Object> resultList = (List<Object>) JSON.deserializeUntyped(result);
            System.assert(resultList.size() >= 0, '应该返回产品列表');
        }
        Test.stopTest();
    }
}