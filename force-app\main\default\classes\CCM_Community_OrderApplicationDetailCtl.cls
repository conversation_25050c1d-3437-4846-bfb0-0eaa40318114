/**
 * @description       :
 * <AUTHOR> <EMAIL>
 * @group             :
 * @last modified on  : 07-12-2024
 * @last modified by  : <EMAIL>
**/
public without sharing class CCM_Community_OrderApplicationDetailCtl {
    public static String sqlPoPartsWithoutConvertCurrency = 'SELECT Id, '
            + 'Name, '
            + 'Billing_Address__c, '
            + 'Billing_Address__r.Name, '
            + 'Billing_Address__r.Address1__c, '
            + 'Billing_Address__r.Address2__c, '
            + 'Billing_Address__r.Country__c, '
            + 'Billing_Address__r.State__c, '
            + 'Billing_Address__r.City__c, '
            + 'Billing_Address__r.Postal_Code__c, '
            + 'Billing_Address__r.Contact__c, '
            + 'Billing_Address__r.Contact__r.Name, '
            + 'Billing_Address_Name__c, '
            + 'Shipping_Address__c, '
            + 'Shipping_Address__r.Name, '
            + 'Shipping_Address_Name__c, '
            + 'Shipping_Address__r.Address1__c, '
            + 'Shipping_Address__r.Address2__c, '
            + 'Shipping_Address__r.Country__c, '
            + 'Shipping_Address__r.State__c, '
            + 'Shipping_Address__r.City__c, '
            + 'Shipping_Address__r.Postal_Code__c, '
            + 'Shipping_Address__r.Contact__c, '
            + 'Shipping_Address__r.Contact__r.Name, '
            + 'Shipping_Address__r.X2nd_Tier_Dealer__r.OwnerId, '
            + 'Customer__r.OwnerId,'
            + 'Additional_Shipping_Street__c,'
            + 'Additional_Shipping_City__c,'
            + 'Additional_Shipping_Country__c,'
            + 'Additional_Shipping_Province__c,'
            + 'Additional_Shipping_Postal_Code__c,'
            + 'Additional_Contact_Name__c,'
            + 'Additional_Contact_Phone__c,'
            + 'Additional_Contact_Email__c,'
            + 'Credit_Authorization__c,'
            + 'Is_DropShip__c, '
            + 'Is_Alternative_Address__c, '
            + 'Shipping_priority__c, '
            + 'Shipping_Method__c, '
            + 'Shipping_By__c,'
            + 'tolabel(Shipping_By__c) shippingByLabel, '
            + 'Delivery_Supplier__c, '
            + 'Sync_Status__c, '
            + 'Customer__c, '
            + 'Customer__r.Name, '
            + 'Customer__r.AccountNumber, '
            + 'Customer__r.Distributor_or_Dealer__c, '
            + 'Customer__r.Customer_Cluster__c, '
            + 'Customer__r.CurrencyIsoCode, '
            + 'Customer__r.Shipment_Priority__c, '
            + 'Customer__r.Owner.Name,'
            + 'tolabel(Customer__r.Sales_Rep__c) salesRepLabel, '
            + 'Sales_Agency__r.Alias_Formula__c, '
            + 'Status__c, '
            // add haibo: french
            + 'tolabel(Status__c) orderStatusLabel,'
            + 'Approval_Status__c,'
            + 'Is_Delegate__c, '
            + 'Submit_Date__c, '
            + 'Total_quantity__c, '
            + 'Total_Price__c, '
            + 'Total_Amount__c, '
            + 'Notes__c, '
            + 'Email__c, '
            + 'Phone__c, '
            + 'Fax_Number__c, '
            + 'Customer_PO_Num__c,'
            + 'Expected_Delivery_Date__c,'
            + 'Customer_Freight_Account__c,'
            + 'Step__c,'
            + 'Payment_Term__c,'
            + 'Freight_Term__c,'
            + 'tolabel(Payment_Term__c) paymentTermLabel, '
            + 'tolabel(Freight_Term__c) freightTermLabel, '
            + 'Handling_Fee__c,'
            + 'Actual_Freight_Fee__c,'
            + 'Freight_Fee__c,'
            + 'Freight_Fee_Waived__c,'
            + 'Freight_Fee_To_Be_Waived__c,'
            + 'Extra_Freight_Fee_To_Be_Waived__c,'
            + 'Freight_Target_Fee__c,'
            + 'Sales_Rep__c,'
            + 'Sales_Group__c,'
            + 'Sales_Manager__c,'
            + 'Sales_Manager__r.Name,'
            + 'Product_Price__c,'
            + 'CurrencyIsoCode,'
            + 'Brand_Scope__c,'
            + 'Promotion__c,'
            + 'Discount__c,'
            + 'Promotion_Code__c,'
            + 'IsDeleted,'
            + 'Discount_Amount__c, '
            + 'Actual_Total_Product_Amount__c, '
            + 'Sync_Message__c, '
            + 'Order_Type__c,'
            + 'CreatedDate,'
            + 'CreatedBy.Name,'
            + 'RecordType.DeveloperName, '
            + 'RecordType.Name, '
            + 'Authorized_Brand_Payment_Term__c, '
            + 'Payment_Term_Promotion_Code__c, '
            + 'Whole_Order_Promotion_Code__c, '
            + 'Payment_Term_Promotion__c, '
            + 'Whole_Order_Promotion__c, '
            + 'ORG_ID__c,'
            + 'GST__c,'
            + 'HST__c,'
            + 'PST__c,'
            + 'QST__c,'
            + 'Dropship_Customer__c,'
            + 'Total_Tax__c,'
            + 'Surcharge_Amount__c,'
            + '(SELECT Id, '
            + '     Name, '
            + '     Brand__c, '
            + '     Product__c, '
            + '     ProductCode__c, '
            + '     Product__r.Name, '
            // add haibo: product french
            + '     Product__r.Product_Name_French__c, '
            + '     Product__r.Description, '
            + '     Product__r.Item_Number__c, '
            + '     Product__r.ProductCode, '
            + '     Product__r.Brand_Name__c, '
            + '     Product__r.Short_description__c, '
            + '     Product__r.SF_Description__c,'
            + '     Product__r.SF_Description_French__c,'
            + '     Product__r.Weight__c,'
            + '     Product__r.OverSize__c,'
            + '     Product__r.CS_Exchange_Rate__c,'
            + '     Quantity__c, '
            + '     Gross_Weight__c,'
            + '     Is_Over_Size_Product__c,'
            + '     List_Price__c, '
            + '     Unit_Price__c, '
            + '     Discount_Amount__c, '
            + '     MSRP__c, '
            + '     Sub_Total__c, '
            + '     Whole_Order_Promotion_Code__c, '
            + '     Promotion__r.Promotion_Code_For_External__c, '
            + '     Promo_Discount_Amount__c, '
            + '     Whole_Order_Promo_Discount_Amount__c, '
            + '     PromotionName__c, '
            + '     Promotion__c, '
            + '     Regular_Promotion_Window__c, '
            + '     Promotion_Rule_Name__c, '
            + '     Whole_Order_Promotion__r.Promotion_Code_For_External__c, '
            + '     Is_Initial__c, '
            + '     Ship_Date__c '
            + '     FROM Purchase_Order_Items__r) '
            + 'FROM Purchase_Order__c '
            + 'WHERE IsDeleted = false ';

    public static String sqlOrderPartsWithoutConvertCurrency = 'SELECT Id, Name, '
            + 'Account_Text__c, '
            + 'ActivatedById, '
            + 'ActivatedDate, '
            + 'Brand__c, '
            + 'Carrier_Code__c, '
            + 'CreatedById, '
            + 'CreatedBy.Name,'
            + 'CreatedDate, '
            + 'CurrencyIsoCode, '
            + 'AccountId, '
            + 'Account.Name, '
            + 'Purchase_Order__r.Discount_Amount__c, '
            + 'Account.AccountNumber, '
            + 'Account.Owner.Name, '
            + 'Account.Distributor_or_Dealer__c, '
            + 'Account.Customer_Cluster__c, '
            + 'Account.CurrencyIsoCode, '
            + 'Account.Shipment_Priority__c, '
            + 'Customer_Freight_Account__c, '
            + 'BillTo__c, '
            + 'ShipTo__c, '
            + 'Total_Amount__c, '
            + 'Dropship_Address1__c, '
            + 'Dropship_City__c, '
            + 'Dropship_Country__c, '
            + 'Dropship_State__c, '
            + 'Dropship_ZIP__c, '
            + 'Dropship_Name__c, '
            + 'Telephone_Number__c, '
            + 'Date_Order__c, '
            + 'IsDeleted, '
            + 'ERP_Last_Update_Time__c, '
            + 'ERP_Status__c, '
            + 'Error_Message__c, '
            + 'Expected_Delivery_Date__c, '
            + 'External_PO_NO__c, '
            + 'Free_Delivery__c, '
            + 'Freight_Term__c, '
            // add haibo: french
            + 'tolabel(Freight_Term__c) freightTermLabel, '
            + 'Handling_Fee__c, '
            + 'Feright_Fee__c, '
            + 'Is_Cancelled__c, '
            + 'LastModifiedDate, '
            + 'Notes__c, '
            + 'TotalAmount, '
            + 'Order_Date__c, '
            + 'OrderNumber, '
            + 'OrderReferenceNumber, '
            + 'EffectiveDate, '
            + 'Order_Type__c, '
            + 'Type, '
            + 'PoDate, '
            + 'PO_Number__c, '
            + 'Payment_Method__c, '
            + 'Payment_Term__c, '
            // add haibo: french
            + 'tolabel(Payment_Term__c) paymentTermLabel, '
            + 'Process_Status__c, '
            + 'Purchase_Order__c, '
            + 'Purchase_Order__r.Name, '
            + 'Purchase_Order__r.Submit_Date__c, '
            + 'Purchase_Order__r.Is_Delegate__c, '
            + 'Purchase_Order__r.Sync_Status__c, '
            + 'Purchase_Order__r.Sync_Message__c, '
            + 'Purchase_Order__r.Is_DropShip__c, '
            + 'Purchase_Order__r.Brand_Scope__c, '
            + 'Purchase_Order__r.Shipping_By__c,'
            + 'tolabel(Purchase_Order__r.Shipping_By__c) shippingByLabel, '
            + 'Purchase_Order__r.Email__c, '
            + 'Purchase_Order__r.Additional_Contact_Email__c, '
            + 'Purchase_Order__r.Total_Quantity__c, '
            + 'Purchase_Order__r.Payment_Term_Promotion_Code__c, '
            + 'Purchase_Order__r.Whole_Order_Promotion_Code__c, '
            + 'Purchase_Order__r.Product_Price__c, '
            + 'Purchase_Order_Text__c, '
            + 'RecordTypeId, '
            + 'IsReductionOrder, '
            + 'Sales_Channel__c, '
            + 'Sales_Rep__c, '
            + 'Sales_Group__c,'
            + 'tolabel(Sales_Group__c) salesGroupLabel,'
            + 'SendConfirm__c, '
            + 'SendShippingNumber__c, '
            + 'Shipping_Method__c, '
            + 'Shipping_Priority__c, '
            + 'Order_Status__c, '
            + 'Order_Source__c, '
            + 'Order_OracleID__c, '
            + 'Order_Number__c, '
            + 'tolabel(Order_Status__c) orderStatusLabel,'
            + 'Submit_Date__c, '
            + 'IsHold__c, '
            + 'Hold_Reason__c, '
            + 'Org_Code__c, '
            + 'GST__c,'
            + 'PST__c,'
            + 'HST__c,'
            + 'QST__c,'
            + 'Surcharge_Amount__c,'
            + 'RecordType.DeveloperName, '
            + 'RecordType.Name, '
            + 'Sales_Agency__r.Alias_Formula__c, '
            + 'Account.RecordType.DeveloperName, '
            + '(SELECT CurrencyIsoCode,IsDeleted,Hold_Reason__c,Line_Status__c, Line_Status_New__c, Line_Type__c,Order__c,Name,OrderLine_OracleID__c,Order_Quantity__c,Original_Order_Text__c,Price__c,Price_Book__c,Product__c,Promation_Code__c,Purchase_Order_Item__c,Id,Request_Date__c,Reverse_Quantity__c,Ship_Date__c,isHold__c,Product__r.Item_Number__c,Product__r.Short_description__c,Product__r.CS_Exchange_Rate__c,Product__r.ProductCode,Product__r.Brand_Name__c, Product__r.SF_Description_French__c, Product__r.SF_Description__c, Product__r.Name, Product__r.Product_Name_French__c, Product__r.RecordType.DeveloperName,List_Price__c FROM Order_Items_Order__r) '
            + 'FROM Order '
            + 'WHERE IsDeleted = false ';


    public static String poQueryStr = 'SELECT Id, '
                    + 'Name, '
                    + 'Billing_Address__c, '
                    + 'Billing_Address__r.Name, '
                    + 'Billing_Address__r.Address1__c, '
                    + 'Billing_Address__r.Address2__c, '
                    + 'Billing_Address__r.Country__c, '
                    + 'Billing_Address__r.State__c, '
                    + 'Billing_Address__r.City__c, '
                    + 'Billing_Address__r.Postal_Code__c, '
                    + 'Billing_Address__r.Contact__c, '
                    + 'Billing_Address__r.Contact__r.Name, '
                    + 'Billing_Address_Name__c, '
                    + 'Shipping_Address__c, '
                    + 'Shipping_Address__r.Name, '
                    + 'Shipping_Address_Name__c, '
                    + 'Shipping_Address__r.Address1__c, '
                    + 'Shipping_Address__r.Address2__c, '
                    + 'Shipping_Address__r.Country__c, '
                    + 'Shipping_Address__r.State__c, '
                    + 'Shipping_Address__r.City__c, '
                    + 'Shipping_Address__r.Postal_Code__c, '
                    + 'Shipping_Address__r.Contact__c, '
                    + 'Shipping_Address__r.Contact__r.Name, '
                    + 'Shipping_Address__r.X2nd_Tier_Dealer__r.OwnerId, '
                    + 'Additional_Shipping_Street__c,'
                    + 'Additional_Shipping_City__c,'
                    + 'Additional_Shipping_Country__c,'
                    + 'Additional_Shipping_Province__c,'
                    + 'Additional_Shipping_Postal_Code__c,'
                    + 'Additional_Contact_Name__c,'
                    + 'Additional_Contact_Phone__c,'
                    + 'Additional_Contact_Email__c,'
                    + 'Credit_Authorization__c,'
                    + 'Is_DropShip__c, '
                    + 'Is_Alternative_Address__c, '
                    + 'Shipping_priority__c, '
                    + 'Shipping_Method__c, '
                    + 'Shipping_By__c,'
                    + 'tolabel(Shipping_By__c) shippingByLabel, '
                    + 'Delivery_Supplier__c, '
                    + 'Sync_Status__c, '
                    + 'Dropship_Customer__c,'
                    + 'Customer__c, '
                    + 'Customer__r.Name, '
                    + 'Customer__r.AccountNumber, '
                    + 'Customer__r.Distributor_or_Dealer__c, '
                    + 'Customer__r.Customer_Cluster__c, '
                    + 'Customer__r.CurrencyIsoCode, '
                    + 'Customer__r.Shipment_Priority__c, '
                    + 'Customer__r.Owner.Name,'
                    + 'tolabel(Customer__r.Sales_Rep__c) salesRepLabel, '
                    + 'Sales_Agency__r.Alias_Formula__c, '
                    + 'Status__c, '
                    // add haibo: french
                    + 'tolabel(Status__c) orderStatusLabel,'
                    + 'Approval_Status__c,'
                    + 'Is_Delegate__c, '
                    + 'Submit_Date__c, '
                    + 'Total_quantity__c, '
                    + 'convertCurrency(Total_Price__c), '
                    + 'convertCurrency(Total_Amount__c), '
                    + 'Notes__c, '
                    + 'Email__c, '
                    + 'Phone__c, '
                    + 'Fax_Number__c, '
                    + 'Customer_PO_Num__c,'
                    + 'Expected_Delivery_Date__c,'
                    + 'Customer_Freight_Account__c,'
                    + 'Step__c,'
                    + 'Payment_Term__c,'
                    + 'Freight_Term__c,'
                    + 'tolabel(Payment_Term__c) paymentTermLabel, '
                    + 'tolabel(Freight_Term__c) freightTermLabel, '
                    + 'convertCurrency(Handling_Fee__c),'
                    + 'convertCurrency(Actual_Freight_Fee__c),'
                    + 'convertCurrency(Freight_Fee__c),'
                    + 'convertCurrency(Freight_Fee_Waived__c),'
                    + 'convertCurrency(Freight_Fee_To_Be_Waived__c),'
                    + 'convertCurrency(Extra_Freight_Fee_To_Be_Waived__c),'
                    + 'convertCurrency(Freight_Target_Fee__c),'
                    + 'Sales_Rep__c,'
                    + 'Sales_Group__c,'
                    + 'Sales_Manager__c,'
                    + 'Sales_Manager__r.Name,'
                    + 'Product_Price__c,'
                    + 'CurrencyIsoCode,'
                    + 'Brand_Scope__c,'
                    + 'Promotion__c,'
                    + 'convertCurrency(Discount__c),'
                    + 'Promotion_Code__c,'
                    + 'IsDeleted,'
                    + 'convertCurrency(Discount_Amount__c), '
                    + 'convertCurrency(Actual_Total_Product_Amount__c), '
                    + 'Sync_Message__c, '
                    + 'Order_Type__c,'
                    + 'CreatedDate,'
                    + 'CreatedBy.Name,'
                    + 'CreatedById,'
                    + 'RecordType.DeveloperName, '
                    + 'RecordType.Name, '
                    + 'Authorized_Brand_Payment_Term__c, '
                    + 'Payment_Term_Promotion_Code__c, '
                    + 'Whole_Order_Promotion_Code__c, '
                    + 'Payment_Term_Promotion__c, '
                    + 'Whole_Order_Promotion__c, '
                    + 'ORG_ID__c,'
                    + 'Is_Sales_Manager_Modified__c,'
                    + 'Is_Notified_Inside_Sales__c,'
                    + 'convertCurrency(GST__c),'
                    + 'convertCurrency(HST__c),'
                    + 'convertCurrency(PST__c),'
                    + 'convertCurrency(QST__c),'
                    + 'convertCurrency(Total_Tax__c),'
                    + 'convertCurrency(Surcharge_Amount__c),'
                    + '(SELECT Id, '
                    + '     Name, '
                    + '     Brand__c, '
                    + '     Product__c, '
                    + '     ProductCode__c, '
                    + '     Product__r.Name, '
                    // add haibo: product french
                    + '     Product__r.Product_Name_French__c, '
                    + '     Product__r.Description, '
                    + '     Product__r.Item_Number__c, '
                    + '     Product__r.ProductCode, '
                    + '     Product__r.Brand_Name__c, '
                    + '     Product__r.Short_description__c, '
                    + '     Product__r.SF_Description__c,'
                    // add haibo: product french
                    + '     Product__r.SF_Description_French__c, '
                    + '     Product__r.Weight__c,'
                    + '     Product__r.OverSize__c,'
                    + '     Product__r.CS_Exchange_Rate__c,'
                    + '     Quantity__c, '
                    + '     Gross_Weight__c,'
                    + '     Is_Over_Size_Product__c,'
                    + '     convertCurrency(List_Price__c), '
                    + '     convertCurrency(Unit_Price__c), '
                    + '     convertCurrency(Discount_Amount__c), '
                    + '     convertCurrency(MSRP__c), '
                    + '     convertCurrency(Sub_Total__c), '
                    + '     Whole_Order_Promotion_Code__c, '
                    + '     Promotion__r.Promotion_Code_For_External__c, '
                    + '     convertCurrency(Promo_Discount_Amount__c), '
                    + '     convertCurrency(Whole_Order_Promo_Discount_Amount__c), '
                    + '     PromotionName__c, '
                    + '     Promotion__c, '
                    + '     Regular_Promotion_Window__c, '
                    + '     Promotion_Rule_Name__c, '
                    + '     Whole_Order_Promotion__r.Promotion_Code_For_External__c, '
                    + '     Is_Initial__c, '
                    + '     Ship_Date__c '
                    + '     FROM Purchase_Order_Items__r) '
                    + 'FROM Purchase_Order__c '
                    + 'WHERE IsDeleted = false ';

    public static String orderQueryStr = 'SELECT Id, Name, '
                    + 'Account_Text__c, '
                    + 'ActivatedById, '
                    + 'ActivatedDate, '
                    + 'Brand__c, '
                    + 'Carrier_Code__c, '
                    + 'CreatedById, '
                    + 'CreatedBy.Name,'
                    + 'CreatedDate, '
                    + 'CurrencyIsoCode, '
                    + 'AccountId, '
                    + 'Account.Name, '
                    + 'Purchase_Order__r.Discount_Amount__c, '
                    + 'Account.AccountNumber, '
                    + 'Account.Owner.Name, '
                    + 'Account.Distributor_or_Dealer__c, '
                    + 'Account.Customer_Cluster__c, '
                    + 'Account.CurrencyIsoCode, '
                    + 'Account.Shipment_Priority__c, '
                    + 'Customer_Freight_Account__c, '
                    + 'BillTo__c, '
                    + 'ShipTo__c, '
                    + 'convertCurrency(Total_Amount__c), '
                    + 'Dropship_Address1__c, '
                    + 'Dropship_City__c, '
                    + 'Dropship_Country__c, '
                    + 'Dropship_State__c, '
                    + 'Dropship_ZIP__c, '
                    + 'Dropship_Name__c, '
                    + 'Telephone_Number__c, '
                    + 'Date_Order__c, '
                    + 'IsDeleted, '
                    + 'ERP_Last_Update_Time__c, '
                    + 'ERP_Status__c, '
                    + 'Error_Message__c, '
                    + 'Expected_Delivery_Date__c, '
                    + 'External_PO_NO__c, '
                    + 'Free_Delivery__c, '
                    + 'Freight_Term__c, '
                    // add haibo: french
                    + 'tolabel(Freight_Term__c) freightTermLabel, '
                    + 'convertCurrency(Handling_Fee__c), '
                    + 'convertCurrency(Feright_Fee__c), '
                    + 'Is_Cancelled__c, '
                    + 'LastModifiedDate, '
                    + 'Notes__c, '
                    + 'convertCurrency(TotalAmount), '
                    + 'Order_Date__c, '
                    + 'OrderNumber, '
                    + 'OrderReferenceNumber, '
                    + 'EffectiveDate, '
                    + 'Order_Type__c, '
                    + 'Type, '
                    + 'PoDate, '
                    + 'PO_Number__c, '
                    + 'Payment_Method__c, '
                    + 'Payment_Term__c, '
                    // add haibo: french
                    + 'tolabel(Payment_Term__c) paymentTermLabel, '
                    + 'Process_Status__c, '
                    + 'Sample_Order__c, '
                    + 'Sample_Order__r.Name, '
                    + 'Purchase_Order__c, '
                    + 'Purchase_Order__r.Name, '
                    + 'Purchase_Order__r.Submit_Date__c, '
                    + 'Purchase_Order__r.Is_Delegate__c, '
                    + 'Purchase_Order__r.Sync_Status__c, '
                    + 'Purchase_Order__r.Sync_Message__c, '
                    + 'Purchase_Order__r.Is_DropShip__c, '
                    + 'Purchase_Order__r.Brand_Scope__c, '
                    + 'Purchase_Order__r.Shipping_By__c,'
                    + 'tolabel(Purchase_Order__r.Shipping_By__c) shippingByLabel, '
                    + 'Purchase_Order__r.Email__c, '
                    + 'Purchase_Order__r.Additional_Contact_Email__c, '
                    + 'Purchase_Order__r.Total_Quantity__c, '
                    + 'Purchase_Order__r.Payment_Term_Promotion_Code__c, '
                    + 'Purchase_Order__r.Whole_Order_Promotion_Code__c, '
                    + 'convertCurrency(Purchase_Order__r.Product_Price__c), '
                    + 'Purchase_Order_Text__c, '
                    + 'RecordTypeId, '
                    + 'IsReductionOrder, '
                    + 'Sales_Channel__c, '
                    + 'Sales_Rep__c, '
                    + 'Sales_Group__c,'
                    + 'tolabel(Sales_Group__c) salesGroupLabel,'
                    + 'SendConfirm__c, '
                    + 'SendShippingNumber__c, '
                    + 'Shipping_Method__c, '
                    + 'Shipping_Priority__c, '
                    + 'Order_Status__c, '
                    + 'Order_Source__c, '
                    + 'Order_OracleID__c, '
                    + 'Order_Number__c, '
                    + 'tolabel(Order_Status__c) orderStatusLabel,'
                    + 'Submit_Date__c, '
                    + 'IsHold__c, '
                    + 'Hold_Reason__c, '
                    + 'Org_Code__c, '
                    + 'convertCurrency(GST__c),'
                    + 'convertCurrency(HST__c),'
                    + 'convertCurrency(PST__c),'
                    + 'convertCurrency(QST__c),'
                    + 'convertCurrency(Surcharge_Amount__c),'
                    + 'RecordType.DeveloperName, '
                    + 'RecordType.Name, '
                    + 'Sales_Agency__r.Alias_Formula__c, '
                    + 'Account.RecordType.DeveloperName, '
                    // add haibo: french
                    + '(SELECT CurrencyIsoCode,IsDeleted,Hold_Reason__c,Line_Status__c, Line_Status_New__c, Line_Type__c,Order__c,Name,OrderLine_OracleID__c,Order_Quantity__c,Original_Order_Text__c,convertCurrency(Price__c),Price_Book__c,Product__c,Promation_Code__c,Purchase_Order_Item__c,Id,Request_Date__c,Reverse_Quantity__c,Ship_Date__c,isHold__c,Product__r.Item_Number__c,Product__r.Short_description__c,Product__r.ProductCode,Product__r.Brand_Name__c,Product__r.SF_Description__c, Product__r.SF_Description_French__c,Product__r.CS_Exchange_Rate__c,Product__r.Name, Product__r.Product_Name_French__c, Product__r.RecordType.DeveloperName,convertCurrency(List_Price__c) FROM Order_Items_Order__r) '
                + 'FROM Order '
                + 'WHERE IsDeleted = false ';



    // @mark ,napoleon , 23-1-9, get purchase_order__c.recordtype.developername = Place_Parts_Order info;
    @AuraEnabled
    public static String getPurchasePartsOrder(String recordId){
        Boolean isSuccess = true;
        InitData initD = new InitData();
        initD.isInnerUser = Util.isInnerUser();
        if (String.isNotBlank(recordId)){
            sqlPoPartsWithoutConvertCurrency += 'AND Id = \'' + recordId + '\'';

            List<Purchase_Order__c> poList = Database.query(sqlPoPartsWithoutConvertCurrency);
            if (poList != null && poList.size() > 0){
                Purchase_Order__c poData = poList[0];
                OrderInfo orderInf = retOrderMappingInfo(poData,null, null);
                initD.order = orderInf.order;
                initD.orderItems = orderInf.orderItems;
                initD.currentStep = orderInf.currentStep;
                initD.customerType = orderInf.order.customerType;
                initD.customerCluster = orderInf.order.customerCluster;
                initD.defaultPaymentTerm = orderInf.order.defaultPaymentTerm;
                initD.paymentTermOptions = Util.getPaymentTermOptions(initD.customerType, poData.ORG_ID__c);
                initD.attachmentSourceId = recordId;
                Map<String, Freight_Term__mdt> freightRuleMap = Util.getFreightTermMap();
                Map<String, Payment_Term__mdt> paymentRuleMap = Util.getPaymentTermMap();
                if (String.isNotBlank(poData.Freight_Term__c)){
                    Freight_Term__mdt fterm = freightRuleMap.get(poData.Freight_Term__c);
                    initD.freightTermVal = fterm.Description__c;
                }

                if (String.isNotBlank(poData.Payment_Term__c)){
                    Payment_Term__mdt pterm = paymentRuleMap.get(poData.Payment_Term__c);
                    initD.paymentTermVal = pterm.Description__c;
                }

                if (initD.isInnerUser == true && poData.Status__c == 'Submitted'){
                    if (poData.Is_Delegate__c == true){
                        initD.isShowSyncBtn = true;
                        initD.isShowEditBtn = false;
                    }else if (poData.Is_Delegate__c == false){
                        initD.isShowSyncBtn = true;
                        initD.isShowEditBtn = true;
                    }
                }
            }
        }else{
            isSuccess = false;
            initD.errorMsg = 'No found order record.';
        }
        return JSON.serialize(initD);
    }

    // add end


    // add, napoleon , 23-1-19, get parts order's order information in partsorder_detail_page cmp.
    @AuraEnabled
    public static String getOrderPartsInfo(String recordId){
        Boolean isSuccess = true;
        InitData initD = new InitData();
        initD.isInnerUser = Util.isInnerUser();
        if (String.isNotBlank(recordId)){
            sqlOrderPartsWithoutConvertCurrency += 'AND Id = \'' + recordId + '\'';
            List<Order> orderList = Database.query(sqlOrderPartsWithoutConvertCurrency);
            if (orderList != null && orderList.size() > 0){
                Order orderData = orderList[0];
                OrderInfo orderInf = retOrderMappingInfo(null,orderData, initD);
                initD.order = orderInf.order;
                initD.orderItems = orderInf.orderItems;
                initD.currentStep = orderInf.currentStep;
                initD.attachmentSourceId = orderData.Purchase_Order__c;
                Map<String, Freight_Term__mdt> freightRuleMap = Util.getFreightTermMap();
                Map<String, Payment_Term__mdt> paymentRuleMap = Util.getPaymentTermMap();
                if (String.isNotBlank(orderData.Freight_Term__c)){
                    Freight_Term__mdt fterm = freightRuleMap.get(orderData.Freight_Term__c);
                    initD.freightTermVal = fterm.Description__c;
                }

                if (String.isNotBlank(orderData.Payment_Term__c)){
                    Payment_Term__mdt pterm = paymentRuleMap.get(orderData.Payment_Term__c);
                    initD.paymentTermVal = pterm.Description__c;
                }

                initD.shipmentInfo = retShipmentMappingInfo(recordId, initD);
                initD.invoice = retInvoiceMappingInfo(recordId);
            }
        }else{
            isSuccess = false;
            initD.errorMsg = 'No found order record.';
        }
        return JSON.serialize(initD);
    }

    // add end


    //获取初始化Purchase Order Information
    @AuraEnabled
    public static String getData(String recordId){
        Boolean isSuccess = true;
        InitData initD = new InitData();
        initD.isInnerUser = Util.isInnerUser();
        initD.currentUserProfile = [SELECT Id, Name FROM Profile WHERE Id = :UserInfo.getProfileId()].Name;
        Boolean isSalesAgency = false;
        List<User> lstCurUser = [SELECT Id,Username,Profile.Name,UserRole.DeveloperName
                                 FROM User
                                 WHERE ID = :UserInfo.getUserId() LIMIT 1];
        if(!lstCurUser.isEmpty()) {
            if(Label.CCM_Order_Delegation_Available_Username_List.split(CCM_Constants.SEMICOLON_REGEXP_SPACE_IGNORED).contains(lstCurUser[0].Username)) {
                isSalesAgency = true;
            }
        }
        if (String.isNotBlank(recordId)){
            //initD.paymentTermOptions = Util.getPaymentTermOptions();
            String objName = Util.findObjectNameFromRecordIdPrefix(recordId);
            //recordId来之Purchase Order的场景
            if (objName == 'Purchase_Order__c'){
                poQueryStr += 'AND Id = \'' + recordId + '\'';
                List<Purchase_Order__c> poList = Database.query(poQueryStr);
                if (poList != null && poList.size() > 0){
                    Purchase_Order__c poData = poList[0];
                    OrderInfo orderInf = retOrderMappingInfo(poData,null, null);
                    User UserProfile1 = [SELECT id,Profile.Name FROM User WHERE ID = : poList[0].CreatedById];
                    initD.CreatedById = UserProfile1.Profile.Name;
                    initD.order = orderInf.order;
                    initD.orderItems = orderInf.orderItems;
                    initD.currentStep = orderInf.currentStep;
                    initD.customerType = orderInf.order.customerType;
                    initD.customerCluster = orderInf.order.customerCluster;
                    initD.defaultPaymentTerm = orderInf.order.defaultPaymentTerm;
                    initD.paymentTermOptions = Util.getPaymentTermOptions(initD.customerType, poData.ORG_ID__c);
                    initD.attachmentSourceId = recordId;
                    // Added By Anony 23.1.10 ---Start
                    initD.isNotifiedInsideSales = poData.Is_Notified_Inside_Sales__c;
                    initD.isSalesManagerModified = poData.Is_Sales_Manager_Modified__c;
                    // Added By Anony 23.1.10 ---End
                    Map<String, Freight_Term__mdt> freightRuleMap = Util.getFreightTermMap();
                    Map<String, Payment_Term__mdt> paymentRuleMap = Util.getPaymentTermMap();
                    if (String.isNotBlank(poData.Freight_Term__c)){
                        Freight_Term__mdt fterm = freightRuleMap.get(poData.Freight_Term__c);
                        initD.freightTermVal = fterm.Description__c;
                    }

                    if (String.isNotBlank(poData.Payment_Term__c)){
                        Payment_Term__mdt pterm = paymentRuleMap.get(poData.Payment_Term__c);
                        // add haibo: french
                        if(UserInfo.getLanguage() == Label.CCM_Portal_French){
                            initD.paymentTermVal = pterm.Description_French__c;
                        }else {
                            initD.paymentTermVal = pterm.Description__c;
                        }
                    }

                    /*if (poData.Status__c == 'Submitted' && poData.Approval_Status__c == 'Pending for Approval'){
                        initD.isApprovalMode = Util.isCurrentApprover(recordId);
                    }*/

                    if (initD.isInnerUser == true && poData.Status__c == 'Submitted'){
                        if (poData.Is_Delegate__c == true){
                            initD.isShowSyncBtn = true;
                            initD.isShowEditBtn = false;
                        }else if (poData.Is_Delegate__c == false){
                            initD.isShowSyncBtn = true;
                            initD.isShowEditBtn = true;
                        }
                        if(isSalesAgency) {
                            initD.isShowSyncBtn = false;
                        }
                    }

                    initD.showTax = CCM_PurchaseOrderUtil.needCalculateTax(poData.Customer__r.AccountNumber);
                }
            }//recordId来之正式Order的场景
            else if (objName == 'Order'){
                orderQueryStr += 'AND Id = \'' + recordId + '\'';
                List<Order> orderList = Database.query(orderQueryStr);
                if (orderList != null && orderList.size() > 0){
                    Order orderData = orderList[0];
                    OrderInfo orderInf = retOrderMappingInfo(null,orderData, initD);
                    User UserProfile = [SELECT id,Profile.Name FROM User WHERE ID = : orderList[0].CreatedById];
                    initD.CreatedById = UserProfile.Profile.Name;
                    initD.order = orderInf.order;
                    initD.orderItems = orderInf.orderItems;
                    initD.currentStep = orderInf.currentStep;
                    initD.attachmentSourceId = orderData.Purchase_Order__c;
                    Map<String, Freight_Term__mdt> freightRuleMap = Util.getFreightTermMap();
                    Map<String, Payment_Term__mdt> paymentRuleMap = Util.getPaymentTermMap();
                    if (String.isNotBlank(orderData.Freight_Term__c)){
                        Freight_Term__mdt fterm = freightRuleMap.get(orderData.Freight_Term__c);
                        initD.freightTermVal = fterm.Description__c;
                    }

                    if (String.isNotBlank(orderData.Payment_Term__c)){
                        Payment_Term__mdt pterm = paymentRuleMap.get(orderData.Payment_Term__c);
                        // add haibo: french
                        if(UserInfo.getLanguage() == Label.CCM_Portal_French){
                            initD.paymentTermVal = pterm.Description_French__c;
                        }else {
                            initD.paymentTermVal = pterm.Description__c;
                        }
                    }

                    initD.shipmentInfo = retShipmentMappingInfo(recordId, initD);
                    initD.invoice = retInvoiceMappingInfo(recordId);
                    initD.showTax = CCM_PurchaseOrderUtil.needCalculateTax(orderData.Account.AccountNumber);
                }
            }
        }else{
            isSuccess = false;
            initD.errorMsg = 'No found order record.';
        }
        return JSON.serialize(initD);
    }

    //根据RecordId获取Object Name
    @AuraEnabled
    public static String getObjectName(String recordId){
        String objName = Util.findObjectNameFromRecordIdPrefix(recordId);
        return objName;
    }

    //Order header/order item Mapping
    public static OrderInfo retOrderMappingInfo(Purchase_Order__c quotation, Order order, InitData initD){
        //判断当前用户为Inner User还是Community User
        Boolean isInnerUser = Util.isInnerUser();
        OrderInfo orderInfo = new OrderInfo();
        OrderData oData = new OrderData();

        Map<String, String> orderTypeMap = new Map<String, String>();
        // edit haibo: french
        orderTypeMap.put('Place_Order', Label.CCM_Portal_Order);
        orderTypeMap.put('Place_Parts_Order', Label.CCM_Portal_PartsOrder);

        List<OrderItemData> orderItems = new List<OrderItemData>();
        if (quotation != null && order == null){
            oData.Id = quotation.Id;
            oData.DropshipCustomer = quotation.Dropship_Customer__c;
            oData.Authorization = quotation.Credit_Authorization__c;
            //oData.manageAccountId = quotation.Shipping_Address__r.X2nd_Tier_Dealer__r.OwnerId;
            oData.manageAccountId = quotation.Customer__r.OwnerId; // @mark austin, 2023-03-20, update modify button display, by customer owner
            oData.isDelegated = quotation.Is_Delegate__c;
            // add haibo: french
            oData.isDelegatedOrder = quotation.Is_Delegate__c == true ? Label.CCM_Portal_YES : Label.CCM_Portal_NO;
            oData.brandScopeName = quotation.Brand_Scope__c;
            oData.customerId = quotation.Customer__c;
            oData.customerPONum = quotation.Customer_PO_Num__c;
            oData.customeName = quotation.Customer__r.Name;
            oData.customerType = quotation.Customer__r.Distributor_or_Dealer__c;
            oData.customerCluster = quotation.Customer__r.Customer_Cluster__c;
            oData.customerCurrency = quotation.Customer__r.CurrencyIsoCode;
            oData.salesRep = (String)quotation.Customer__r.get('salesRepLabel');
            if(quotation.Sales_Agency__c != null){
                oData.salesAgency = quotation.Sales_Agency__r.Alias_Formula__c;
            }
            oData.salesGroup = quotation.Sales_Group__c;
            oData.shippingPriority = quotation.Shipping_priority__c;
            // edit haibo: french
            // oData.orderStatus = quotation.Status__c;
            oData.orderStatus = (String)quotation.get('orderStatusLabel');
            oData.orderApprovalStatus = quotation.Approval_Status__c;
            oData.shippingBy = quotation.Shipping_By__c;
            oData.shippingByLabel = (String)quotation.get('shippingByLabel');
            oData.shippingMethod = quotation.Shipping_Method__c;
            oData.deliverySupplier = quotation.Delivery_Supplier__c;
            oData.freightAccount = quotation.Customer_Freight_Account__c;
            oData.expectedDeliveryDate = quotation.Expected_Delivery_Date__c;
            oData.buyerEmail = quotation.Email__c;
            oData.notes = quotation.Notes__c;
            oData.billingAddressId = quotation.Billing_Address__c;
            oData.billingAddressName = quotation.Billing_Address_Name__c;

            oData.billingAddress1 = quotation.Billing_Address__r.Address1__c;
            oData.billingAddress2 = quotation.Billing_Address__r.Address2__c;
            oData.billingAddressCity = quotation.Billing_Address__r.City__c;
            oData.billingAddressCountry = quotation.Billing_Address__r.Country__c;
            oData.billingAddressState = quotation.Billing_Address__r.State__c;
            oData.billingAddressPostalCode = quotation.Billing_Address__r.Postal_Code__c;
            oData.billingAddressContactName = quotation.Billing_Address__r.Contact__r.Name;
            if (quotation.Is_Alternative_Address__c == true){
                oData.additionalShipAddressStreet = quotation.Additional_Shipping_Street__c;
                oData.additionalShipAddressCity = quotation.Additional_Shipping_City__c;
                oData.additionalShipAddressCountry = quotation.Additional_Shipping_Country__c;
                oData.additionalShipAddressProvince = quotation.Additional_Shipping_Province__c;
                oData.additionalShipAddressPostCode = quotation.Additional_Shipping_Postal_Code__c;
                oData.additionalContactName = quotation.Additional_Contact_Name__c;
                oData.additionalContactPhone = quotation.Additional_Contact_Phone__c;
                oData.additionalContactEmail = quotation.Additional_Contact_Email__c;
            }else{
                oData.shippingAddressId = quotation.Shipping_Address__c;
                oData.shippingAddressName = quotation.Shipping_Address_Name__c;

                oData.shippingAddress1 = quotation.Shipping_Address__r.Address1__c;
                oData.shippingAddress2 = quotation.Shipping_Address__r.Address2__c;
                oData.shippingAddressCity = quotation.Shipping_Address__r.City__c;
                oData.shippingAddressCountry = quotation.Shipping_Address__r.Country__c;
                oData.shippingAddressState = quotation.Shipping_Address__r.State__c;
                oData.shippingAddressPostalCode = quotation.Shipping_Address__r.Postal_Code__c;
                oData.shippingAddressContactName = quotation.Shipping_Address__r.Contact__r.Name;
            }
            oData.isDropShip = quotation.Is_DropShip__c;
            /*if (quotation.Is_DropShip__c == true || quotation.Is_Alternative_Address__c == true){
                oData.isDropShipOrder = 'Yes';
            }else{
                oData.isDropShipOrder = 'No';
            }*/
            // add haibo: french
            oData.isDropShipOrder = (quotation.Order_Type__c == 'CNA Dropship Order' || quotation.Order_Type__c == 'CA Dropship Order')? Label.CCM_Portal_YES : Label.CCM_Portal_NO;
            oData.isAlternativeAddress = quotation.Is_Alternative_Address__c;
            /*oData.salesManagerName = quotation.Sales_Manager__r.Name;*/
            oData.salesManagerName = quotation.Customer__r.Owner.Name;
            oData.totalQuantity = quotation.Total_Quantity__c;
            oData.productPrice = quotation.Product_Price__c;
            oData.freightFee = quotation.Freight_Fee__c;
            oData.freightFeeWaived = quotation.Freight_Fee_Waived__c;
            oData.freightFeeToBeWaived = quotation.Freight_Fee_To_Be_Waived__c;
            oData.extraFreightFeeToBeWaived = quotation.Extra_Freight_Fee_To_Be_Waived__c;
            oData.freightTargetFee = quotation.Freight_Target_Fee__c;
            oData.handingFee = quotation.Handling_Fee__c;
            oData.totalAmount = quotation.Total_Amount__c;
            oData.discountAmt = quotation.Discount_Amount__c == null ? 0.00 : quotation.Discount_Amount__c;
            oData.actualTotalProdAmt = quotation.Actual_Total_Product_Amount__c == null ? 0.00 : quotation.Actual_Total_Product_Amount__c;
            oData.syncStatus = quotation.Sync_Status__c;
            oData.syncMessage = quotation.Sync_Message__c;

            oData.submitDate = quotation.Submit_Date__c == null ? '' : quotation.Submit_Date__c.format('MM-dd-yyyy');
            oData.createdDate = quotation.CreatedDate == null ? '' : quotation.CreatedDate.format('MM-dd-yyyy');
            oData.createdBy = quotation.CreatedBy.Name;
            oData.orderType = orderTypeMap.get(quotation.RecordType.DeveloperName);
            oData.poNumber = quotation.Name;
            oData.accountNumber = quotation.Customer__r == null ? '' : quotation.Customer__r.AccountNumber;
            oData.paymentTermValue = quotation.Payment_Term__c;
            oData.defaultPaymentTerm = quotation.Authorized_Brand_Payment_Term__c;

            //whole order promotion & payment term promotion
            oData.wholeOrderPromoCode = quotation.Whole_Order_Promotion_Code__c;
            oData.wholeOrderPromotionId = quotation.Whole_Order_Promotion__c;
            oData.paymentTermPromoCode = quotation.Payment_Term_Promotion_Code__c;
            oData.paymentTermPromotionId = quotation.Payment_Term_Promotion__c;

            //Canada Tax
            if(quotation.ORG_ID__c == 'CCA'){
                oData.QST = quotation.QST__c != null ? quotation.QST__c : 0.00;
                oData.PST = quotation.PST__c != null ? quotation.PST__c : 0.00;
                oData.GST = quotation.GST__c != null ? quotation.GST__c : 0.00;
                oData.HST = quotation.HST__c != null ? quotation.HST__c : 0.00;
            }

            //ORG Code
            oData.orgCode = quotation.ORG_ID__c;

            //Surcharge
            oData.surchargeAmt = quotation.Surcharge_Amount__c == null ? 0.00 : quotation.Surcharge_Amount__c;

            if (isInnerUser){
                if (quotation.Is_Delegate__c == false){
                    if (quotation.Status__c == 'Draft'){
                        orderInfo.currentStep = 1;
                    }else if (quotation.Status__c == 'Submitted'){
                        orderInfo.currentStep = 2;
                    }
                }else if(quotation.Is_Delegate__c == true){
                    if (quotation.Status__c == 'Draft'){
                        orderInfo.currentStep = 1;
                    }else if (quotation.Status__c == 'Submitted'){
                        orderInfo.currentStep = 2;
                    }
                    /*else if (quotation.Status__c == 'Submitted' && quotation.Approval_Status__c == 'Draft'){
                        orderInfo.currentStep = 2;
                    }else if (quotation.Status__c == 'Submitted' &&
                            (quotation.Approval_Status__c =='Approved' || quotation.Status__c == 'Rejected')){
                        orderInfo.currentStep = 3;
                    }else{
                        orderInfo.currentStep = 2;
                    }*/
                }
            }else{
                if (quotation.Status__c == 'Draft'){
                    orderInfo.currentStep = 1;
                }else if (quotation.Status__c == 'Submitted' && quotation.Sync_Status__c == null){
                    orderInfo.currentStep = 2;
                }else if (quotation.Status__c == 'Submitted' &&
                        (quotation.Sync_Status__c != null)){
                    orderInfo.currentStep = 3;
                }
            }


            //Order Item information
            if (quotation.Purchase_Order_Items__r != null && quotation.Purchase_Order_Items__r.size() > 0){
                for (Purchase_Order_Item__c poItem : quotation.Purchase_Order_Items__r){
                    OrderItemData orderItem = new OrderItemData();
                    orderItem.id = poItem.Id;
                    orderItem.productId = poItem.Product__c;
                    orderItem.productItemNum = poItem.Product__r.Item_Number__c;
                    // add haibo: french
                    if(UserInfo.getLanguage() == Label.CCM_Portal_French){
                        orderItem.productName = poItem.Product__r.SF_Description_French__c;
                    }else {
                        orderItem.productName = poItem.Product__r.SF_Description__c;
                    }
                    // add haibo: french
                    if(oData.orderType == Label.CCM_Portal_PartsOrder){
                        // add haibo: french
                        if(UserInfo.getLanguage() == Label.CCM_Portal_French){
                            orderItem.productName = poItem.Product__r.Product_Name_French__c == null ? poItem.Product__r.Name : poItem.Product__r.Product_Name_French__c;
                        }else {
                            orderItem.productName = poItem.Product__r.Name;
                        }
                    }
                    orderItem.productCode = poItem.Product__r.ProductCode;
                    orderItem.brandName = poItem.Brand__c;
                    String year ='';
                    String month ='';
                    String days ='';
                    String dtShipDate = '';
                    if(poItem.Ship_Date__c != null){
                        year = String.valueOf(poItem.Ship_Date__c.year());
                        month = String.valueOf(poItem.Ship_Date__c.month());
                        days = String.valueOf(poItem.Ship_Date__c.day());
                        if(month.length() == 1){
                            month = '0'+ month;
                        }
                        if(days.length() == 1){
                            days = '0'+ days;
                        }
                        dtShipDate = month + '-' + days + '-' + year;
                    }
                    orderItem.shipDate = dtShipDate;
                    orderItem.quantity = poItem.Quantity__c;
                    orderItem.grossWeight = poItem.Product__r.Weight__c;
                    orderItem.isOverSize = poItem.Product__r.OverSize__c;
                    orderItem.unitPrice = poItem.Unit_Price__c;
                    orderItem.listPrice = poItem.List_Price__c;
                    orderItem.subTotal = poItem.Sub_Total__c;
                    // calvin start
                    orderItem.caseQty = poItem.Product__r.CS_Exchange_Rate__c == null ? 1 : poItem.Product__r.CS_Exchange_Rate__c;
                    // end


                    orderItem.discountAmt = poItem.Discount_Amount__c == null ? 0.00 : poItem.Discount_Amount__c;

                    //promotion discount
                    orderItem.promoDiscountAmt = poItem.Promo_Discount_Amount__c == null ? 0.00 : poItem.Promo_Discount_Amount__c;
                    orderItem.wholeOrderPromoDiscountAmt = poItem.Whole_Order_Promo_Discount_Amount__c == null ? 0.00 : poItem.Whole_Order_Promo_Discount_Amount__c;
                    orderItem.promotionCode = poItem.Promotion__r.Promotion_Code_For_External__c;
                    orderItem.promotionId = poItem.Promotion__c;
                    orderItem.windowId = poItem.Regular_Promotion_Window__c;
                    orderItem.ruleName = poItem.Promotion_Rule_Name__c;
                    orderItem.wholeOrderPromotionId = poItem.Whole_Order_Promotion__c;
                    orderItem.wholeOrderPromotionCode = poItem.Whole_Order_Promotion__r.Promotion_Code_For_External__c;
                    orderItem.isInitial = poItem.Is_Initial__c;

                    orderItems.add(orderItem);
                }
            }
        }else if (quotation == null && order != null){
            if (order.Purchase_Order__r != null){
                oData.isDelegated = order.Purchase_Order__r.Is_Delegate__c;
                // add haibo: french
                oData.isDelegatedOrder = order.Purchase_Order__r.Is_Delegate__c == true ? Label.CCM_Portal_YES : Label.CCM_Portal_NO;
                oData.brandScopeName = order.Purchase_Order__r.Brand_Scope__c;
                /*oData.shippingBy = order.Purchase_Order__r.Shipping_By__c;
                oData.shippingByLabel = (String)order.Purchase_Order__r.get('shippingByLabel');*/
                oData.buyerEmail = order.Purchase_Order__r.Email__c;
                oData.isDropShipOrder = (order.Order_Type__c == 'CNA Dropship Order' || order.Order_Type__c == 'CA Dropship Order') ? 'Yes' : 'No';
                oData.discountAmt = order.Purchase_Order__r.Discount_Amount__c == null ? 0.00 : order.Purchase_Order__r.Discount_Amount__c;
            }
            oData.brandScopeName = oData.brandScopeName == null ? order.Brand__c : oData.brandScopeName;
            oData.orderId = order.Id;
            oData.orderNumber = order.OrderNumber;
            oData.customerId = order.AccountId;
            oData.customerPONum = order.PO_Number__c;
            oData.customeName = order.Account.Name;
            oData.customerType = order.Account.Distributor_or_Dealer__c;
            oData.customerCluster = order.Account.Customer_Cluster__c;
            oData.customerCurrency = order.Account.CurrencyIsoCode;
            oData.salesRep = (String)order.Sales_Rep__c;
            oData.syncStatus = order.Purchase_Order__r.Sync_Status__c;
            oData.syncMessage = order.Purchase_Order__r.Sync_Message__c;
            if(order.Sales_Agency__c != null){
                oData.salesAgency = order.Sales_Agency__r.Alias_Formula__c;
            }
            oData.salesGroup = order.Sales_Group__c != null ? (String)order.get('salesGroupLabel') : '';
            oData.shippingPriority = order.Shipping_Priority__c;
            // add haibo: french
            oData.orderStatus = (String)order.get('orderStatusLabel');
            if (!isInnerUser && (order.Order_Status__c == 'Booked' || order.Order_Status__c == 'ENTERED')){
                // add haibo: french
                oData.orderStatus = Label.CCM_Portal_Submitted;
            }
            oData.orderStatusLabel = order.Order_Status__c != null ? (String)order.get('orderStatusLabel') : '';
            oData.orderSource = order.Order_Source__c;
            oData.shippingMethod = order.Shipping_Method__c;
            oData.shippingBy = String.isNotBlank(order.Customer_Freight_Account__c) ? 'Customer' : 'Chervon';
            oData.shippingByLabel = String.isNotBlank(order.Customer_Freight_Account__c) ? 'Collect' : 'Prepaid';
            oData.deliverySupplier = order.Carrier_Code__c;
            oData.freightAccount = order.Customer_Freight_Account__c;
            oData.expectedDeliveryDate = order.Expected_Delivery_Date__c;
            oData.notes = order.Notes__c;

            oData.submitDate = order.Purchase_Order__r.Submit_Date__c == null ? order.CreatedDate.format('MM-dd-yyyy') : order.Purchase_Order__r.Submit_Date__c.format('MM-dd-yyyy');
            oData.createdDate = order.Date_Order__c == null ? order.CreatedDate.format('MM-dd-yyyy') : order.Date_Order__c.format('MM-dd-yyyy');
            oData.createdBy = order.CreatedBy.Name;
            oData.orderType = orderTypeMap.get(order.RecordType.DeveloperName);
            oData.customerRecordType = order.Account.RecordType.DeveloperName;
            oData.poNumber = order.Purchase_Order__r == null? '' : order.Purchase_Order__r.Name;
            if (order.Order_Type__c == 'CNA Sample Order Only') {
                oData.sampleOrderId = order.Sample_Order__c;
                oData.poNumber = order.Sample_Order__c == null? '' : order.Sample_Order__r.Name;
            }
            oData.accountNumber = order.Account == null? '' : order.Account.AccountNumber;

            if (String.isNotBlank(order.BillTo__c)){
                Address_With_Program__c billingAddress = Util.getAddressInfo(order.BillTo__c);
                oData.billingAddressId = billingAddress.Account_Address__c;

                oData.billingAddress1 = billingAddress.Account_Address__r.Address1__c;
                oData.billingAddress2 = billingAddress.Account_Address__r.Address2__c;
                oData.billingAddressCity = billingAddress.Account_Address__r.City__c;//changed by Zoe ********

                oData.billingAddressCountry = billingAddress.Account_Address__r.Country__c;
                oData.billingAddressState = billingAddress.Account_Address__r.State__c;
                oData.billingAddressPostalCode = billingAddress.Account_Address__r.Postal_Code__c;
                oData.billingAddressContactName = billingAddress.Account_Address__r.Contact__r.Name;
            }
            Boolean isAlternativeAddress = String.isNotBlank(order.Dropship_Address1__c) ? true : false;
            if (isAlternativeAddress){
                oData.additionalShipAddressStreet = order.Dropship_Address1__c;
                oData.additionalShipAddressCity = order.Dropship_City__c;
                oData.additionalShipAddressCountry = order.Dropship_Country__c;
                oData.additionalShipAddressProvince = order.Dropship_State__c;
                oData.additionalShipAddressPostCode = order.Dropship_ZIP__c;
                oData.additionalContactName = order.Dropship_Name__c;
                oData.additionalContactPhone = order.Telephone_Number__c;
                if (order.Purchase_Order__r != null){
                    oData.additionalContactEmail = order.Purchase_Order__r.Additional_Contact_Email__c;
                }
                oData.shiptoName = order.Dropship_Name__c;
            }else{
                if (String.isNotBlank(order.ShipTo__c)){
                    Address_With_Program__c shippingAddress = Util.getAddressInfo(order.ShipTo__c);
                    oData.shippingAddressId = shippingAddress.Account_Address__c;
                    oData.shippingAddressName = shippingAddress.Account_Address__r.Address1__c;
                    oData.shippingAddress1 = shippingAddress.Account_Address__r.Address1__c;
                    oData.shippingAddress2 = shippingAddress.Account_Address__r.Address2__c;

                    oData.shippingAddressCity = shippingAddress.Account_Address__r.City__c;
                    oData.shippingAddressCountry = shippingAddress.Account_Address__r.Country__c;
                    oData.shippingAddressState = shippingAddress.Account_Address__r.State__c;
                    oData.shippingAddressPostalCode = shippingAddress.Account_Address__r.Postal_Code__c;
                    oData.shippingAddressContactName = shippingAddress.Account_Address__r.Contact__r.Name;
                    oData.shiptoName = shippingAddress.Account_Address__r.Name;
                }
            }
            oData.isAlternativeAddress = isAlternativeAddress;
            oData.salesManagerName = order.Account.Owner.Name;
            oData.freightFee = order.Feright_Fee__c;
            oData.handingFee = order.Handling_Fee__c;
            oData.totalAmount = order.Total_Amount__c;
            oData.isHold = order.IsHold__c == 'Y' ? Label.CCM_Portal_YES : Label.CCM_Portal_NO;
            oData.holdReason = order.Hold_Reason__c;
            oData.orderCancelledType = (order.Order_Type__c == 'CNA Return - Sales & Inv.Adj.' || order.Order_Type__c == 'CA Return - Sales & Inv.Adj.') ? 'Return' : 'Standard';
            oData.orderSubType = order.Order_Type__c;
            oData.orderOracleId = order.Order_Number__c;
            oData.isCancelled = order.Is_Cancelled__c == true ? Label.CCM_Portal_YES : Label.CCM_Portal_NO;

            //whole order promotion & payment term promotion
            oData.wholeOrderPromoCode = order.Purchase_Order__r.Whole_Order_Promotion_Code__c;
            oData.paymentTermPromoCode = order.Purchase_Order__r.Payment_Term_Promotion_Code__c;

            //Canad Tax
            if(order.Org_Code__c == 'CCA'){
                oData.QST = order.QST__c != null ? order.QST__c : 0.00;
                oData.PST = order.PST__c != null ? order.PST__c : 0.00;
                oData.GST = order.GST__c != null ? order.GST__c : 0.00;
                oData.HST = order.HST__c != null ? order.HST__c : 0.00;
            }
            // order's currencyIsoCode
            oData.currencyIsoCode = order.CurrencyIsoCode;
            //ORG Code
            oData.orgCode = order.Org_Code__c;

            //Surcharge
            oData.surchargeAmt = order.Surcharge_Amount__c == null ? 0.00 : order.Surcharge_Amount__c;

            if (order.Order_Type__c == 'CNA Return - Sales & Inv.Adj.' || order.Order_Type__c == 'CA Return - Sales & Inv.Adj.'){
                oData.orderType = 'Return Goods';
                if (order.Order_Status__c == 'CLOSED'){
                    orderInfo.currentStep = 3;
                }else{
                    orderInfo.currentStep = 2;
                }
            }else{
                if (isInnerUser){
                    if (order.Order_Status__c == 'Booked'){
                        orderInfo.currentStep = 3;
                    }else if (order.Order_Status__c == 'Order Processing'){
                        orderInfo.currentStep = 4;
                    }else if (order.Order_Status__c == 'Partial Shipment'){
                        orderInfo.currentStep = 5;
                    }else if (order.Order_Status__c == 'Ship Complete'){
                        orderInfo.currentStep = 6;
                    }else if (order.Order_Status__c == 'Cancelled'){
                        orderInfo.currentStep = 4;
                    }
                }else{
                    if (order.Order_Status__c == 'Booked' || order.Order_Status__c == 'Order Processing'){
                        orderInfo.currentStep = 3;
                    }else if (order.Order_Status__c == 'Partial Shipment'){
                        orderInfo.currentStep = 4;
                    }else if (order.Order_Status__c == 'Ship Complete'){
                        orderInfo.currentStep = 5;
                    }else if (order.Order_Status__c == 'Cancelled'){
                        orderInfo.currentStep = 4;
                    }
                }
            }

            //Order Item information
            Integer totalQuantity = 0;
            Decimal prodTotalAmount = 0.00;
            if (order.Order_Items_Order__r != null && order.Order_Items_Order__r.size() > 0){
                for (Order_Item__c oItem : order.Order_Items_Order__r){
                    OrderItemData orderItem = new OrderItemData();
                    orderItem.id = oItem.Id;
                    orderItem.productId = oItem.Product__c;
                    // calvin start
                    orderItem.caseQty = oItem.Product__r.CS_Exchange_Rate__c == null ? 1 : oItem.Product__r.CS_Exchange_Rate__c;
                    // end


                    orderItem.productItemNum = oItem.Product__r.Item_Number__c;
                    // add haibo: french
                    if(UserInfo.getLanguage() == Label.CCM_Portal_French){
                        orderItem.productName = oItem.Product__r.SF_Description_French__c;
                    }else {
                        orderItem.productName = oItem.Product__r.SF_Description__c;
                    }
                    // add haibo: french
                    if(oData.orderType == Label.CCM_Portal_PartsOrder){
                        // add haibo: french
                        if(UserInfo.getLanguage() == Label.CCM_Portal_French){
                            orderItem.productName = oItem.Product__r.Product_Name_French__c == null ? oItem.Product__r.Name : oItem.Product__r.Product_Name_French__c;
                        }else {
                            orderItem.productName = oItem.Product__r.Name;
                        }
                    }
                    orderItem.productCode = oItem.Product__r.ProductCode;
                    orderItem.brandName = oItem.Product__r.Brand_Name__c;
                    String year ='';
                    String month ='';
                    String days ='';
                    String dtShipDate = '';
                    if (oItem.Ship_Date__c != null){
                        year = String.valueOf(oItem.Ship_Date__c.year());
                        month = String.valueOf(oItem.Ship_Date__c.month());
                        days = String.valueOf(oItem.Ship_Date__c.day());
                        if(month.length() == 1){
                            month = '0'+ month;
                        }
                        if(days.length() == 1){
                            days = '0'+ days;
                        }
                        dtShipDate = month + '-' + days + '-' + year;
                    }
                    orderItem.shipDate = dtShipDate;
                    orderItem.promotionCode = oItem.Promation_Code__c;
                    // add currencyIsoCode into orderItem
                    orderItem.currencyIsoCode = oItem.currencyIsoCode;
                    orderItem.quantity = oItem.Order_Quantity__c;
                    orderItem.unitPrice = oItem.Price__c;
                    orderItem.listPrice = oItem.List_Price__c;
                    orderItem.subTotal = oItem.Order_Quantity__c * oItem.Price__c;
                    if (oItem.isHold__c == 'Y'){
                        if (String.isNotBlank(oItem.Hold_Reason__c)){
                            orderItem.holdStatus = 'Hold'+ ' - '+ oItem.Hold_Reason__c;
                        }else{
                            orderItem.holdStatus = 'Hold';
                        }
                    }
                    orderItem.lineStatus = oItem.Line_Status_New__c;
                    orderItems.add(orderItem);
                    totalQuantity = Integer.valueOf(totalQuantity + oItem.Order_Quantity__c);
                    prodTotalAmount = prodTotalAmount + orderItem.subTotal;

                    if(oItem.Product__r.RecordType.DeveloperName == 'Amware_Merchandising'){
                        initD.hasAmwareProducts = true;
                    }
                }
            }
            oData.totalQuantity = totalQuantity;//TODO--->产品数量
            oData.productPrice = prodTotalAmount; //TODO--->产品总价
            /*oData.totalAmount = prodTotalAmount + order.Feright_Fee__c + order.Handling_Fee__c;*/ //TODO--->订单总价

        }
        orderInfo.order = oData;
        orderInfo.orderItems = orderItems;
        System.debug('orderInfo,all,' + orderInfo);
        return orderInfo;
    }

    /**
     * Send Email to inside sales
     * add by austin
     */
    @AuraEnabled
    public static String sendEmail (String recordId){
        String emailStatus = '';
        try{
                Purchase_Order__c po = [select id, name, customer__r.name, dropship_customer__r.name from Purchase_Order__c where id=: recordId];
                String EmailTemplate = 'Notify_inside_sales';
                Map<String, String> mapMailParam = new Map<String, String>();
                String toAddress = '<EMAIL>';
                List<String> strToAddress = new List<String>();
                // strToAddress.Add(toAddress);
                // 23.1.4 Added by Anony(Just SIT) --- Start
                strToAddress.Add('<EMAIL>');
                strToAddress.Add('<EMAIL>');
                // 23.1.4 Added by Anony(Just SIT) --- End
                // user.Contact.Account.Owner.Email;
                List<EmailTemplate> et = [SELECT id, HtmlValue, Subject, Body FROM EmailTemplate WHERE DeveloperName =: EmailTemplate];

                String url = Url.getOrgDomainUrl().toExternalForm();
                mapMailParam.put('{!Url}', url + '/lightning/n/Order_Apply_Detail_Page?0.recordId=' + recordId);
                mapMailParam.put('{!poname}', po.name);
                mapMailParam.put('{!1st}', po.customer__r.name);
                mapMailParam.put('{!2st}', po.dropship_customer__r.name);
                if(et.size() > 0){
                    Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
                    List<OrgWideEmailAddress> listAddresses = [
                        SELECT
                            Id, DisplayName, Address
                        FROM
                            OrgWideEmailAddress
                        WHERE
                            DisplayName = :'SFDC Notification'
                    ];
                    if (listAddresses.size() > 0) {
                        mail.setOrgWideEmailAddressId(listAddresses[0].Id);
                    }

                    //Setting EmailTemeplate
                    mail.setTemplateId(et[0].id);

                    //Setting Recipient Email Addresses
                    mail.setToAddresses(strToAddress);

                    String strSubject = '';
                    String strTextBody = '';
                    String strHtmlBody = '';

                    if (mapMailParam != null) {
                        strSubject = et[0].subject;
                        strTextBody = et[0].body;
                        strHtmlBody = et[0].HtmlValue;
                        for (String parmKey : mapMailParam.keySet()) {
                            strSubject = strSubject.replace(parmKey, mapMailParam.get(parmKey));
                            if(String.isNotBlank(strTextBody)){
                            strTextBody = strTextBody.replace(parmKey, mapMailParam.get(parmKey));
                            }
                            if (String.isNotBlank(strHtmlBody)) {
                                strHtmlBody = strHtmlBody.replace(parmKey, mapMailParam.get(parmKey));
                            }
                        }
                        //Setting the subject
                        mail.setSubject(strSubject);

                        //Setting the Email Content
                        mail.setPlainTextBody(strTextBody);

                        //Setting the HtmlBody
                        mail.setHtmlBody(strHtmlBody);
                    }
                    Log__c b = new Log__c(Name = 'CCM_Community_OrderApplicationDetailCtl', Error_Message__c = strToAddress[0] + ' ' + strTextBody + 'strHtmlBody' + strHtmlBody);
                    insert b;

                    mail.setSaveAsActivity(false);
                    mail.setUseSignature(false);
                    mail.setCharset('UTF-8');

                    //Send Email
                    List<Messaging.SingleEmailMessage> allmsg = new List<Messaging.SingleEmailMessage>();
                    allmsg.add(mail);
                    Messaging.SendEmailResult[] results = Messaging.sendEmail(allmsg, false);
                    if(results[0].isSuccess()){
                        emailStatus = 'OK';
                    }else {
                        emailStatus = 'NG';
                    }
                    Log__c c = new Log__c(Name = 'CCM_Community_OrderApplicationDetailCtl result', Error_Message__c = '' +  results);
                    insert c;
		            //Added By Anony 23.1.10 ---Start
                    // 修改通知Inside Sales状态
                    Purchase_Order__c objPruchaseOrder = [SELECT Id,Is_Notified_Inside_Sales__c FROM Purchase_Order__c WHERE Id = :recordId LIMIT 1];
                    objPruchaseOrder.Is_Notified_Inside_Sales__c = true;
                    update objPruchaseOrder;
                    Log__c objLog = new Log__c(Name = 'Have Notified Inside Sales',Error_Message__c = 'Notify Status:\n' + objPruchaseOrder);
                    insert objLog;
                    //Added By Anony 23.1.10 ---End
                }
        }catch(Exception ex){
            Log__c d = new Log__c(Name = 'CCM_Community_OrderApplicationDetailCtl'+ex.getLineNumber(), Error_Message__c = ex.getMessage());
            insert d;
            emailStatus = 'NG';
        }

        return emailStatus;

    }

    //Shipment header/shipment item Mapping
    public static List<ShipmentInfo> retShipmentMappingInfo(String orderId, InitData initD){
        List<ShipmentInfo> shipmentInfoList= new List<ShipmentInfo>();
        if (String.isNotBlank(orderId)){
            List<Shipment__c> shipments = [
                    SELECT Id, CurrencyIsoCode, IsDeleted, Delivery_Address__c,
                           Delivery_City__c, Delivery_Country__c, Delivery_Postal_Code__c,
                           Delivery_State__c, Forecast_Date__c, Order__c, OwnerId,
                           Receipt_Address__c, Receipt_City__c, Receipt_Country__c,
                           Receipt_Postal_Code__c, Receipt_State__c, Ship_Date__c,
                           Ship_Method__c, Ship_OracleID__c, Name, Shipper__c,
                           Tracking_Number__c, Weight__c, WeightUnit__c,
                           (SELECT IsDeleted,Item_Quantity_in_Order__c,Item_Quantity_in_Shipment__c,Product__c,Id,Name,Shipment__c,UPC_code__c,Product__r.Short_description__c,Product__r.ProductCode,Product__r.Name,Product__r.SF_Description__c,Product__r.RecordType.DeveloperName FROM Shipment_Items__r WHERE IsDeleted = false)
                    FROM Shipment__c
                    WHERE Order__c = :orderId
                    AND IsDeleted = false];
            if (shipments != null && shipments.size() > 0){
                for (Shipment__c shipment : shipments){
                    ShipmentInfo shipmentInfo = new ShipmentInfo();
                    ShipmentData shipmentData = new ShipmentData();
                    List<ShipmentItemData> shipmentItemDataList = new List<ShipmentItemData>();
                    //Shipment Header
                    shipmentData.trackingNum = shipment.Tracking_Number__c;
                    Datetime dtShipdate = (Datetime)shipment.Ship_Date__c;
                    shipmentData.shipDate = dtShipdate ?. format('MM-dd-yyyy');

                    Datetime dtForecastDate = (Datetime)shipment.Forecast_Date__c;
                    shipmentData.forecastDate = dtForecastDate ?. format('MM-dd-yyyy');

                    shipmentData.shipper = shipment.Shipper__c;
                    shipmentData.shipMethod = shipment.Ship_Method__c;
                    shipmentData.weight = String.valueOf(shipment.Weight__c);
                    shipmentData.weightUnit = shipment.WeightUnit__c;
                    shipmentData.deliveryAddress = shipment.Delivery_Address__c;
                    shipmentData.deliveryCountry = shipment.Delivery_Country__c;
                    shipmentData.deliveryState = shipment.Delivery_State__c;
                    shipmentData.deliveryCity = shipment.Delivery_City__c;
                    shipmentData.deliveryPostalCode = shipment.Delivery_Postal_Code__c;
                    shipmentData.receiptAddress = shipment.Receipt_Address__c;
                    shipmentData.receiptCountry = shipment.Receipt_Country__c;
                    shipmentData.receiptState = shipment.Receipt_State__c;
                    shipmentData.receiptCity = shipment.Receipt_City__c;
                    shipmentData.receiptPostalCode = shipment.Receipt_Postal_Code__c;

                    //Shipment Items
                    Map<String, ShipmentItemData> productCodeItemsMap = new Map<String, ShipmentItemData>();
                    if (shipment.Shipment_Items__r != null && shipment.Shipment_Items__r.size() > 0){
                        for (Shipment_Item__c shipmentItem : shipment.Shipment_Items__r){
                            ShipmentItemData shipmentItemData = new ShipmentItemData();
                            shipmentItemData.itemQtyInOrder = Integer.valueOf(shipmentItem.Item_Quantity_in_Order__c);
                            shipmentItemData.itemQtyInShipment = shipmentItem.Item_Quantity_in_Shipment__c;
                            shipmentItemData.upcCode = shipmentItem.UPC_code__c;
                            shipmentItemData.productName = shipmentItem.Product__r.SF_Description__c;
                            shipmentItemData.productModelNum = shipmentItem.Product__r.ProductCode;
                            shipmentItemDataList.add(shipmentItemData);

                            if(!productCodeItemsMap.containsKey(shipmentItem.Product__r.ProductCode)) {
                                ShipmentItemData newItemData = new ShipmentItemData();
                                newItemData.itemQtyInOrder = 0;
                                newItemData.itemQtyInShipment = 0;
                                newItemData.upcCode = '';
                                newItemData.productName = '';
                                newItemData.productModelNum = shipmentItem.Product__r.ProductCode;
                                productCodeItemsMap.put(shipmentItem.Product__r.ProductCode, newItemData);
                            }
                            ShipmentItemData temp = productCodeItemsMap.get(shipmentItem.Product__r.ProductCode);
                            temp.itemQtyInOrder += shipmentItemData.itemQtyInOrder;
                            temp.itemQtyInShipment += shipmentItemData.itemQtyInShipment;
                            temp.upcCode = shipmentItem.UPC_code__c;
                            temp.productName = shipmentItem.Product__r.SF_Description__c;
                            temp.productModelNum = shipmentItem.Product__r.ProductCode;
                            productCodeItemsMap.put(shipmentItem.Product__r.ProductCode, temp);

                            if(shipmentItem.Product__r.RecordType.DeveloperName == 'Amware_Merchandising'){
                                initD.hasAmwareShipments = true;
                            }
                        }
                        shipmentInfo.shipmentItemSummary = productCodeItemsMap.values();
                    }

                    shipmentInfo.shipment = shipmentData;
                    shipmentInfo.shipmentItems = shipmentItemDataList;
                    shipmentInfoList.add(shipmentInfo);
                }
            }

        }

        return shipmentInfoList;
    }

    //Invoice information Mapping
    public static List<InvoiceData> retInvoiceMappingInfo(String orderId){
        List<InvoiceData> invoiceInfoList = new List<InvoiceData>();
        if (String.isNotBlank(orderId)){
            // return currencyIsoCode and remove convertyCurrency
            List<Invoice__c> invoiceList = [
                    SELECT Customer__c,IsDeleted,Delivery_Number__c,Invoice_Date__c,
                           Name,Invoice_Number__c,Number_of_Shipping_Units__c,Order__c,
                           Order_Date__c,PO_Number__c,Total_Amount__c,Shipment__c,
                           Invoice_Status__c,isPaid__c,Tracking_NO__c,Total_Remaining__c,CurrencyIsoCode
                    FROM Invoice__c
                    WHERE IsDeleted = false
                    AND Order__c =: orderId];
            if (invoiceList != null && invoiceList.size() > 0){
                for (Invoice__c invoice : invoiceList){
                    InvoiceData invoiceData = new InvoiceData();
                    invoiceData.invoiceId = invoice.Id;
                    invoiceData.deliveryNum = invoice.Delivery_Number__c;
                    invoiceData.invoiceNum = invoice.Invoice_Number__c;
                    invoiceData.invoiceStatus = invoice.Invoice_Status__c;
                    String isPaidVal = invoice.isPaid__c == true ? 'Yes' : 'No';
                    invoiceData.isPaid = isPaidVal;
                    invoiceData.numOfShippingUnits = String.valueOf(invoice.Number_of_Shipping_Units__c);
                    Datetime dtOrderDate = (Datetime)invoice.Order_Date__c;
                    invoiceData.orderDate = dtOrderDate ?. format('MM-dd-yyyy');
                    invoiceData.totalDue = String.valueOf(invoice.Total_Amount__c);
                    invoiceData.currencyIsoCode = String.valueOf(invoice.CurrencyIsoCode);
                    invoiceData.totalRemaining = String.valueOf(invoice.Total_Remaining__c);
                    invoiceData.trackingNum = invoice.Tracking_NO__c;
                    invoiceInfoList.add(invoiceData);
                }
            }
        }
        return invoiceInfoList;
    }

    //保存订单信息
    @AuraEnabled
    public static String saveData(String recordId, String orderInfo, String orderItemsInfo){
        RespData resp = new RespData();
        if (String.isNotBlank(recordId)){
            if (String.isNotBlank(orderInfo)){
                Boolean isPO = Util.findObjectNameFromRecordIdPrefix(recordId) == 'Purchase_Order__c' ? true : false;
                if (isPO){
                    OrderData po = (OrderData)JSON.deserialize(orderInfo, OrderData.class);
                    Purchase_Order__c purchOrder = new Purchase_Order__c();
                    purchOrder.Id = recordId;
                    purchOrder.Shipping_By__c = po.shippingBy;
                    purchOrder.Shipping_priority__c = po.shippingPriority;
                    purchOrder.Freight_Fee__c = po.freightFee;
                    purchOrder.Freight_Fee_Waived__c = po.freightFeeWaived;
                    purchOrder.Freight_Fee_To_Be_Waived__c = po.freightFeeToBeWaived;
                    purchOrder.Extra_Freight_Fee_To_Be_Waived__c = po.extraFreightFeeToBeWaived;
                    purchOrder.Delivery_Supplier__c = po.deliverySupplier;
                    purchOrder.Customer_Freight_Account__c = po.freightAccount;
                    purchOrder.Customer_PO_Num__c = po.customerPONum;
                    purchOrder.Expected_Delivery_Date__c = po.expectedDeliveryDate;
                    purchOrder.Email__c = po.buyerEmail;
                    purchOrder.Notes__c = po.notes;
                    purchOrder.Total_Quantity__c = po.totalQuantity;
                    purchOrder.Handling_Fee__c = po.handingFee;
                    purchOrder.Product_Price__c = po.productPrice;
                    purchOrder.Actual_Total_Product_Amount__c = po.actualTotalProdAmt;
                    /*purchOrder.Is_DropShip__c = po.isDropShip;*/
                    purchOrder.Credit_Authorization__c = po.Authorization;
                    purchOrder.Is_Alternative_Address__c = po.isAlternativeAddress;
                    purchOrder.Payment_Term__c = po.paymentTermValue;
                    purchOrder.CurrencyIsoCode = po.customerCurrency;
                    if (po.isAlternativeAddress){
                        if(po.orgCode == 'CCA'){
                            purchOrder.Order_Type__c = 'CA Dropship Order';
                        }else{
                            purchOrder.Order_Type__c = 'CNA Dropship Order';
                        }
                    }else if(po.isAlternativeAddress == false && po.isDropShip == false){
                        if(po.orgCode == 'CCA'){
                            purchOrder.Order_Type__c = 'CA Sales Order - CAD';
                        }else{
                            purchOrder.Order_Type__c = 'CNA Sales Order - USD';
                        }
                    }
                    if (po.isAlternativeAddress == true){
                        purchOrder.Additional_Shipping_Street__c = po.additionalShipAddressStreet;
                        purchOrder.Additional_Shipping_City__c = po.additionalShipAddressCity;
                        purchOrder.Additional_Shipping_Country__c = po.additionalShipAddressCountry;
                        purchOrder.Additional_Shipping_Province__c = po.additionalShipAddressProvince;
                        purchOrder.Additional_Shipping_Postal_Code__c = po.additionalShipAddressPostCode;
                        purchOrder.Additional_Contact_Name__c = po.additionalContactName;
                        purchOrder.Additional_Contact_Phone__c = po.additionalContactPhone;
                        purchOrder.Additional_Contact_Email__c = po.additionalContactEmail;
                    }else{
                        purchOrder.Shipping_Address__c = po.shippingAddressId;
                    }
                    purchOrder.Billing_Address__c = po.billingAddressId;
                    purchOrder.Discount_Amount__c = po.discountAmt;

                    //whole order promotion and payment term promotion
                    purchOrder.Whole_Order_Promotion__c = po.wholeOrderPromotionId;
                    purchOrder.Payment_Term_Promotion__c = po.paymentTermPromotionId;

                    //Surcharge Amount
                    Decimal customerSurchargeRate = [SELECT Customer__r.Surcharge__c FROM Purchase_Order__c WHERE Id=:recordId].Customer__r.Surcharge__c;
                    // if(customerSurchargeRate != null && customerSurchargeRate > 0){
                    //     purchOrder.Surcharge_Amount__c = purchOrder.Discount_Amount__c!=null ? ((purchOrder.Product_Price__c - Math.abs(purchOrder.Discount_Amount__c)) * customerSurchargeRate/100).setScale(2,System.RoundingMode.HALF_UP) : 0.00;
                    // }
                    String accNumber = null;
                    List<Account> accNumberList = [SELECT AccountNumber FROM ACCOUNT WHERE Id =: po.customerId];
                    if(po.orgCode == 'CCA' ){
                        //CA handling fee
                        Decimal productPrice = purchOrder.Product_Price__c!=null?purchOrder.Product_Price__c:0;
                        if(accNumberList.size() > 0){
                            accNumber = accNumberList[0].AccountNumber;
                        }
                        if(productPrice < 250  && accNumber != '1627' &&  accNumber != '1664' && accNumber != null){
                            purchOrder.Handling_Fee__c = productPrice * 0.1;
                        }
                        Boolean needCalculateTax = CCM_PurchaseOrderUtil.needCalculateTax(accNumber);
                        //CA Tax
                        if(needCalculateTax) {
                            caculateTotalTax(purchOrder);
                        }
                    }

                    List<Purchase_Order_Item__c> poItemsExsit = [SELECT Id FROM Purchase_Order_Item__c WHERE Purchase_Order__c = :recordId];
                    List<Purchase_Order_Item__c> updPOItems = new List<Purchase_Order_Item__c>();
                    //Total surcharge amount
                    Decimal totalSurchargeAmount = 0.00;
                    if (String.isNotBlank(orderItemsInfo)){
                        List<OrderItemData> poItems = (List<OrderItemData>)JSON.deserialize(orderItemsInfo, List<OrderItemData>.class);
                        for (OrderItemData poItem : poItems){
                            Purchase_Order_Item__c purchOrderItem = new Purchase_Order_Item__c();
                            // if (String.isNotBlank(poItem.id)){
                            //     purchOrderItem.Id = poItem.id;
                            // }else{
                            //     purchOrderItem.Purchase_Order__c = recordId;
                            // }
                            purchOrderItem.Purchase_Order__c = recordId;
                            purchOrderItem.ProductCode__c = poItem.productCode;
                            purchOrderItem.Brand__c = poItem.brandName;
                            purchOrderItem.Product__c = poItem.productId;
                            if (poItem.shipDate != null){
                                if(String.isNotBlank(poItem.id)) {
                                    List<String> expectedDeliveryDateList = poItem.shipDate.split('-');
                                    String expectedDeliveryDate = expectedDeliveryDateList[2] + '-' + expectedDeliveryDateList[0] + '-' + expectedDeliveryDateList[1];
                                    purchOrderItem.Ship_Date__c = Date.valueOf(expectedDeliveryDate);
                                }
                                else {
                                    purchOrderItem.Ship_Date__c = Date.valueOf(poItem.shipDate);
                                }
                            }
                            purchOrderItem.Quantity__c = poItem.quantity;
                            purchOrderItem.Unit_Price__c = poItem.unitPrice;
                            purchOrderItem.List_Price__c = poItem.listPrice;
                            purchOrderItem.Sub_Total__c = poItem.subTotal;
                            purchOrderItem.Discount_Amount__c = poItem.discountAmt;
                            purchOrderItem.Gross_Weight__c = poItem.grossWeight == null ? 0.00 : poItem.grossWeight;
                            if(poItem.isOverSize != null){
                                purchOrderItem.Is_Over_Size_Product__c = poItem.isOverSize;
                            }
                            if(String.isNotEmpty(poItem.promotionCode)){
                                purchOrderItem.PromotionName__c = poItem.promotionCode;
                            }
                            if(String.isNotEmpty(poItem.promotionId)){
                                purchOrderItem.Promotion__c = poItem.promotionId;
                            }
                            if(String.isNotEmpty(poItem.windowId)){
                                purchOrderItem.Regular_Promotion_Window__c = poItem.windowId;
                            }
                            if(String.isNotEmpty(poItem.ruleName)){
                                purchOrderItem.Promotion_Rule_Name__c = poItem.ruleName;
                            }
                            if(String.isNotEmpty(poItem.wholeOrderPromotionId)){
                                purchOrderItem.Whole_Order_Promotion__c = poItem.wholeOrderPromotionId;
                            }
                            if(poItem.promoDiscountAmt != null){
                                purchOrderItem.Promo_Discount_Amount__c = poItem.promoDiscountAmt;
                            }
                            if(poItem.wholeOrderPromoDiscountAmt != null){
                                purchOrderItem.Whole_Order_Promo_Discount_Amount__c = poItem.wholeOrderPromoDiscountAmt;
                            }
                            if(poItem.isInitial != null){
                                purchOrderItem.Is_Initial__c = poItem.isInitial;
                            }
                            //surcharge amount
                            if(customerSurchargeRate != null && customerSurchargeRate > 0){
                                purchOrderItem.Surcharge_Amount__c = (purchOrderItem.Unit_Price__c * purchOrderItem.Quantity__c * customerSurchargeRate/100).setScale(2,System.RoundingMode.HALF_UP);
                                totalSurchargeAmount+=purchOrderItem.Surcharge_Amount__c;
                            }

                            //CA Org Code
                            if(po.orgCode == 'CCA'){
                                purchOrderItem.ORG_Code__c = 'CCA';
                                purchOrderItem.Line_Type__c = 'CA General Line';
                            }

                            updPOItems.add(purchOrderItem);
                        }
                    }

                    //total surcharge amount
                    if(customerSurchargeRate != null && customerSurchargeRate > 0){
                        purchOrder.Surcharge_Amount__c = totalSurchargeAmount;
                    }

                    SavePoint sp = Database.setSavepoint();
                    try{
                        update purchOrder;
                        upsert updPOItems;
                        if(poItemsExsit.size() > 0){
                            delete poItemsExsit;
                        }
                        resp.isSuccess = true;
                    }catch(DmlException ex){
                        Database.rollback(sp);
                        System.debug(LoggingLevel.INFO, '*** ex.getDmlMessage(0): ' + ex.getDmlMessage(0));
                    }
                }
            }
        }else{
            resp.isSuccess = false;
            resp.errorMsg = 'No found record!';
        }

        return JSON.serialize(resp);
    }

    //提交审批结果
    /*@AuraEnabled
    public static String doApproval(String poId, String approvalResult, String approvalComments){
        System.debug(LoggingLevel.INFO, '*** approvalResult: ' + approvalResult);
        System.debug(LoggingLevel.INFO, '*** approvalComments: ' + approvalComments);
        Purchase_Order__c po = new Purchase_Order__c(
                Id = poId,
                Approval_Comments__c = approvalComments
            );

        Savepoint sp = Database.setSavepoint();
        try {
            update po;
            Boolean result = Util.doApprovalAction(poId, approvalResult, approvalComments);
            if (!result) {
                return 'FAIL';
            }
        } catch (Exception e) {
            Database.rollback(sp);
            System.debug(LoggingLevel.INFO, '*** e.getStackTraceString(): ' + e.getStackTraceString() + 'e.message:' + e.getMessage());
            return e.getStackTraceString();
        }
        return 'SUCCESS';

    }*/

    //同步订单到EBS
    @AuraEnabled
    public static String syncToEBS(String recordId){
        String syncRes = CCM_Service.pushOrderInfo(recordId);
        if (syncRes == 'S'){
            Purchase_Order__c updPO = [SELECT Id, Sync_Date__c, Sync_Status__c FROM Purchase_Order__c WHERE Id =: recordId];
            updPO.Sync_Status__c = 'In Processing';
            updPO.Sync_Date__c = Datetime.now();
            update updPO;
        }
        return syncRes;
    }

    public static Decimal getFreihtChargeAmount(Decimal productAmount, String orgCode, String brand) {
        Decimal freightFee = 0.00;
        if(orgCode != 'CCA'){
            List<Freight_Charges__mdt> freightChargeList = [SELECT Order_Value_From__c, Order_Value_To__c, Ground_Shipping__c FROM Freight_Charges__mdt];
            if (freightChargeList != null && freightChargeList.size() > 0) {
                for (Freight_Charges__mdt fc : freightChargeList) {
                    if (productAmount >= fc.Order_Value_From__c && productAmount <= fc.Order_Value_To__c) {
                        freightFee = fc.Ground_Shipping__c;
                        break;
                    }
                }
            }
        }else if(orgCode == 'CCA'){
            List<CA_Freight_Charges__mdt> freightChargeList = [SELECT Order_Value_From__c, Order_Value_To__c, Ground_Shipping__c FROM CA_Freight_Charges__mdt];
            if(brand.contains('EGO') || productAmount < 750){
                if (freightChargeList != null && freightChargeList.size() > 0) {
                    for (CA_Freight_Charges__mdt fc : freightChargeList) {
                        if (productAmount >= fc.Order_Value_From__c && productAmount <= fc.Order_Value_To__c) {
                            freightFee = fc.Ground_Shipping__c;
                            break;
                        }
                    }
                }
            }
        }

        return freightFee;
    }

    //计算Freight Fee
    @AuraEnabled
    public static Decimal calculateFreightFee(String orderInfo, String orderItemsInfo){
        Decimal freightFeeAmt = 0.00;
        List<Decimal> prodWeightList = new List<Decimal>();
        Decimal overSizeAmount = 0.00;
        Decimal prodWeightTotal = 0.00;
        //运费系数
        Decimal freightFactor = Util.getFreightFeeFactor();

        if (String.isNotBlank(orderInfo) && String.isNotBlank(orderItemsInfo)){
            String shipperCountryCode = 'US';
            String shipperPostalCode = '38115';
            String recipientCountryCode = 'US';
            String recipientPostalCode = '38017';

            OrderData po = (OrderData)JSON.deserialize(orderInfo, OrderData.class);
            if (po.productPrice >= po.freightTargetFee){
                if(po.orgCode == 'CCA'){
                    shipperPostalCode = CCM_SystemConstants.CA_WAREHOUSE_ZIPCODE;
                }else{
                    if (po.brandScopeName == 'EGO'){
                        shipperPostalCode = CCM_SystemConstants.EGO_WAREHOUSE_ZIPCODE;
                    }else{
                        shipperPostalCode = CCM_SystemConstants.SKIL_SKILSAW_FLEX_WAREHOUSE_ZIPCODE;
                    }
                }

                if (po.isAlternativeAddress == false){
                    recipientCountryCode = po.shippingAddressCountry;
                    recipientPostalCode = po.shippingAddressPostalCode;
                }else{
                    recipientCountryCode = po.additionalShipAddressCountry;
                    recipientPostalCode = po.additionalShipAddressPostCode;
                }

                List<OrderItemData> poItems = (List<OrderItemData>)JSON.deserialize(orderItemsInfo, List<OrderItemData>.class);
                for (OrderItemData poItem : poItems){
                    Decimal weight = poItem.grossWeight == null ? 0.00 : poItem.grossWeight;
                    Decimal subWeight = poItem.quantity * weight;
                    prodWeightTotal = prodWeightTotal + subWeight;
                    prodWeightList.add(subWeight);
                    if (poItem.isOverSize == true){
                        overSizeAmount = overSizeAmount + (poItem.quantity * CCM_SystemConstants.OVER_SIZE_PRODUCT_FREIGHT_FEE);
                    }
                }
                if (prodWeightTotal >= 150){
                    prodWeightList = new List<Decimal>{150};
                }
                Decimal freightFee = 0.00;
                if (Test.isRunningTest()){
                    freightFee =10;
                }else{
                    freightFee = Util.getFreightFeeAmt(shipperCountryCode,shipperPostalCode,recipientCountryCode,recipientPostalCode,prodWeightList);
                }

                //订单总重量高于等于150磅时，显示的运费等于 FedEx 接口返回的 【运费金额 * (订单总重量/150磅） *  运费系数】
                //订单总重量低于150磅时，显示的运费等于 FedEx 接口返回的 【运费金额 * 运费系数】
                if (prodWeightTotal >= 150){
                    freightFee = freightFee * (prodWeightTotal / 150) * (freightFactor / 100);
                }else {
                    freightFee = freightFee * (freightFactor / 100);
                }
                Decimal actualFreightFeeAmt = freightFee + overSizeAmount;
                freightFeeAmt = actualFreightFeeAmt.setScale(2);
            }

            if (po.productPrice > 2000 && po.orgCode != 'CCA'){
                freightFeeAmt = po.productPrice * Decimal.valueOf(Label.CCM_Freight_Fee_By_Product_Amt_Over_2000) / 100;
            }else{
                freightFeeAmt = getFreihtChargeAmount(po.productPrice, po.orgCode, po.brandScopeName);
            }

        }

        return freightFeeAmt;
    }

    /**
     * add by austin
     * 获取user profile name
     */
    @AuraEnabled
    public static String userInfo(String userId){
        User currentUser = [SELECT Id, Contact.AccountId, Profile.Name, UserRole.DeveloperName FROM User WHERE Id = :userId];
        String profileName = currentUser.Profile.Name;
        return profileName;
    }

    //Order Header
    public class OrderData{
        @AuraEnabled public String Id {get; set;}
        @AuraEnabled public String DropshipCustomer {get; set;}
        @AuraEnabled public Boolean isDelegated {get; set;}
        @AuraEnabled public String isDelegatedOrder {get; set;}
        @AuraEnabled public String brandScopeName {get; set;}
        @AuraEnabled public String customerId {get; set;}
        @AuraEnabled public String customerPONum {get; set;}
        @AuraEnabled public String customeName {get; set;}
        @AuraEnabled public String customerType {get; set;}
        @AuraEnabled public String customerCluster {get; set;}
        @AuraEnabled public String customerCurrency {get; set;}
        @AuraEnabled public String shippingPriority {get; set;}
        @AuraEnabled public String orderStatus {get; set;}
        @AuraEnabled public String orderStatusLabel {get; set;}
        @AuraEnabled public String orderSource {get; set;}
        @AuraEnabled public String orderApprovalStatus {get; set;}
        @AuraEnabled public String paymentTermLabel {get; set;}
        @AuraEnabled public String freightTermLabel {get; set;}
        @AuraEnabled public String shippingBy {get; set;}
        @AuraEnabled public String shippingByLabel {get; set;}
        @AuraEnabled public String shippingMethod {get; set;}
        @AuraEnabled public String deliverySupplier {get; set;}
        @AuraEnabled public String freightAccount {get; set;}
        @AuraEnabled public Date expectedDeliveryDate {get; set;}
        @AuraEnabled public String buyerEmail {get; set;}
        @AuraEnabled public String notes {get; set;}
        @AuraEnabled public String billingAddressId {get; set;}
        @AuraEnabled public String billingAddressName {get; set;}
        @AuraEnabled public String billingAddress1 {get; set;}
        @AuraEnabled public String billingAddress2 {get; set;}
        @AuraEnabled public String billingAddressCity {get; set;}
        @AuraEnabled public String billingAddressCountry {get; set;}
        @AuraEnabled public String billingAddressState {get; set;}
        @AuraEnabled public String billingAddressPostalCode {get; set;}
        @AuraEnabled public String billingAddressContactName {get; set;}
        @AuraEnabled public String shippingAddressId {get; set;}
        @AuraEnabled public String shiptoName {get;set;}
        @AuraEnabled public String shippingAddressName {get; set;}
        @AuraEnabled public String shippingAddress1 {get; set;}
        @AuraEnabled public String shippingAddress2 {get; set;}
        @AuraEnabled public String shippingAddressCity {get; set;}
        @AuraEnabled public String shippingAddressCountry {get; set;}
        @AuraEnabled public String shippingAddressState {get; set;}
        @AuraEnabled public String shippingAddressPostalCode {get; set;}
        @AuraEnabled public String shippingAddressContactName {get; set;}
        @AuraEnabled public String additionalShipAddressStreet {get; set;}
        @AuraEnabled public String additionalShipAddressCity {get; set;}
        @AuraEnabled public String additionalShipAddressCountry {get; set;}
        @AuraEnabled public String additionalShipAddressProvince {get; set;}
        @AuraEnabled public String additionalShipAddressPostCode {get; set;}
        @AuraEnabled public String additionalContactName{get; set;}
        @AuraEnabled public String additionalContactPhone {get; set;}
        @AuraEnabled public String additionalContactEmail {get; set;}
        @AuraEnabled public String orderType {get; set;}
        @AuraEnabled public String customerRecordType {get; set;}
        @AuraEnabled public Boolean isDropShip {get; set;}
        @AuraEnabled public Boolean isAlternativeAddress {get; set;}
        @AuraEnabled public String salesManagerName {get; set;}
        @AuraEnabled public Decimal totalQuantity {get; set;}
        @AuraEnabled public Decimal productPrice {get; set;}
        @AuraEnabled public Decimal freightFee {get; set;}
        @AuraEnabled public Decimal freightFeeWaived {get; set;}
        @AuraEnabled public Decimal freightFeeToBeWaived {get; set;}
        @AuraEnabled public Decimal extraFreightFeeToBeWaived {get; set;}
        @AuraEnabled public Decimal freightTargetFee {get; set;}
        @AuraEnabled public Decimal handingFee {get; set;}
        @AuraEnabled public Decimal totalAmount {get; set;}
        @AuraEnabled public String promotionId {get; set;}
        @AuraEnabled public String promotionCode {get; set;}
        @AuraEnabled public String salesRep {get; set;}
        @AuraEnabled public String salesGroup {get; set;}
        @AuraEnabled public String isHold {get; set;}
        @AuraEnabled public String holdReason {get; set;}
        @AuraEnabled public String isDropShipOrder {get; set;}
        @AuraEnabled public String syncStatus {get; set;}
        @AuraEnabled public Decimal discountAmt {get; set;}
        @AuraEnabled public Decimal actualTotalProdAmt {get; set;}
        @AuraEnabled public String syncMessage {get; set;}
        @AuraEnabled public String orderCancelledType {get; set;}
        @AuraEnabled public String orderSubType {get; set;}
        @AuraEnabled public String orderNumber {get; set;}
        @AuraEnabled public String orderOracleId {get; set;}
        @AuraEnabled public String orderId {get; set;}
        @AuraEnabled public String isCancelled {get; set;}
        @AuraEnabled public String salesAgency {get; set;}
        @AuraEnabled public String paymentTermValue {get; set;}
        @AuraEnabled public String defaultPaymentTerm {get; set;}

        // Add by Sharon
        @AuraEnabled public String createdBy {get; set;}
        @AuraEnabled public String createdDate {get; set;}
        @AuraEnabled public String submitDate {get; set;}
        @AuraEnabled public String brands {get; set;}
        @AuraEnabled public String poNumber {get; set;}
        @AuraEnabled public String accountNumber {get; set;}

        //whole order & payment term promotion
        @AuraEnabled public String wholeOrderPromoCode {get; set;}
        @AuraEnabled public String paymentTermPromoCode {get; set;}
        @AuraEnabled public String wholeOrderPromotionId {get; set;}
        @AuraEnabled public String paymentTermPromotionId {get; set;}

        //Canada Tax
        @AuraEnabled public Decimal QST {get; set;}
        @AuraEnabled public Decimal PST {get; set;}
        @AuraEnabled public Decimal GST {get; set;}
        @AuraEnabled public Decimal HST {get; set;}

        //ORG Code
        @AuraEnabled public String orgCode {get; set;}

        //Surcharge
        @AuraEnabled public Decimal surchargeAmt {get; set;}

        //add by Austin
        @AuraEnabled public String manageAccountId {get; set;}
        @AuraEnabled public String Authorization {get; set;}

        @AuraEnabled public String sampleOrderId {get; set;}

        //add,napoleon, 23-1-19,add currencyIsoCode to return wrapper data
        @AuraEnabled public String currencyIsoCode {get; set;}

        public OrderData(){
            this.isDelegated = false;
            this.totalQuantity = 0;
            this.productPrice = 0.00;
            this.freightFee = 0.00;
            this.freightFeeWaived = 0.00;
            this.handingFee = 0.00;
            this.totalAmount = 0.00;
            this.extraFreightFeeToBeWaived = 0.00;
            this.isDropShip = false;
            this.isAlternativeAddress = false;
            this.discountAmt = 0.00;
            this.actualTotalProdAmt = 0.00;
            this.isDropShipOrder = 'No';
            this.isDelegatedOrder = 'No';
            this.surchargeAmt = 0.00;
        }
    }

    //Order Item
    public class OrderItemData{
        @AuraEnabled public String id {get; set;}
        @AuraEnabled public String productId {get; set;}
        // calvin start
        @AuraEnabled public Decimal caseQty {get; set;}
        // end

        @AuraEnabled public String productItemNum {get; set;}
        @AuraEnabled public String productName {get; set;}
        @AuraEnabled public String productCode {get; set;}
        @AuraEnabled public String brandName {get; set;}
        @AuraEnabled public String shipDate {get; set;}
        @AuraEnabled public String requestDate {get; set;}
        @AuraEnabled public Decimal quantity {get; set;}
        @AuraEnabled public Decimal unitPrice {get; set;}
        @AuraEnabled public Decimal listPrice {get; set;}
        @AuraEnabled public Decimal discountAmt {get; set;}
        @AuraEnabled public Decimal subTotal {get; set;}
        @AuraEnabled public String promotionCode {get; set;}
        @AuraEnabled public String holdStatus {get; set;}
        @AuraEnabled public Decimal grossWeight {get; set;}
        @AuraEnabled public Boolean isOverSize {get; set;}
        @AuraEnabled public String lineStatus {get;set;}

        //promotion discount & whole order promotion discount
        @AuraEnabled public Decimal promoDiscountAmt {get; set;}
        @AuraEnabled public Decimal wholeOrderPromoDiscountAmt {get; set;}
        @AuraEnabled public String ruleName {get; set;}
        @AuraEnabled public Boolean isInitial {get; set;}
        @AuraEnabled public String wholeOrderPromotionCode {get; set;}
        @AuraEnabled public String promotionId {get; set;}
        @AuraEnabled public String windowId {get; set;}
        @AuraEnabled public String wholeOrderPromotionId {get; set;}

        // add, napoleon, 23-1-19, add currencyIsoCode to return orderitem
        @AuraEnabled public String currencyIsoCode {get; set;}

        public OrderItemData(){
            this.quantity = 0;
            this.unitPrice = 0.00;
            this.listPrice = 0.00;
            this.subTotal = 0.00;
            this.discountAmt = 0.00;
            this.grossWeight = 0.00;
            this.isOverSize = false;
            this.promoDiscountAmt = 0.00;
            this.wholeOrderPromoDiscountAmt = 0.00;
            this.isInitial = false;
        }
    }

    //Shipment Header
    public class ShipmentData{
        @AuraEnabled public String trackingNum {get; set;} //快递单号
        @AuraEnabled public String shipDate {get; set;} //发货日期
        @AuraEnabled public String forecastDate {get; set;} //预计到货时间
        @AuraEnabled public String shipper {get; set;} //发运商
        @AuraEnabled public String shipMethod {get; set;} //发货方式
        @AuraEnabled public String weight {get; set;} //重量
        @AuraEnabled public String weightUnit {get; set;} //重量单位
        @AuraEnabled public String deliveryAddress {get; set;} //发货地址
        @AuraEnabled public String deliveryCountry {get; set;}
        @AuraEnabled public String deliveryState {get; set;}
        @AuraEnabled public String deliveryCity {get; set;}
        @AuraEnabled public String deliveryPostalCode {get; set;}
        @AuraEnabled public String receiptAddress {get; set;} //收货地址
        @AuraEnabled public String receiptCountry {get; set;}
        @AuraEnabled public String receiptState {get; set;}
        @AuraEnabled public String receiptCity {get; set;}
        @AuraEnabled public String receiptPostalCode {get; set;}
    }

    //Shipment Item
    public class ShipmentItemData{
        @AuraEnabled public Integer itemQtyInOrder {get; set;} //Item 订单数量
        @AuraEnabled public Decimal itemQtyInShipment {get; set;} //Item 本次发货数量
        @AuraEnabled public String upcCode {get; set;} //物料标识
        @AuraEnabled public String productName {get; set;} //产品Short Description
        @AuraEnabled public String productModelNum {get; set;} //产品Model Number
    }

    //Invoice Header
    public class InvoiceData{
        @AuraEnabled public String invoiceId {get; set;}
        @AuraEnabled public String deliveryNum {get; set;}
        @AuraEnabled public String invoiceNum {get; set;}
        @AuraEnabled public String invoiceStatus {get; set;}
        @AuraEnabled public String isPaid {get; set;}
        @AuraEnabled public String numOfShippingUnits {get; set;}
        @AuraEnabled public String orderDate {get; set;}
        @AuraEnabled public String totalDue {get; set;}
        // return invoice's currencyIsoCode
        @AuraEnabled public String currencyIsoCode {get; set;}
        @AuraEnabled public String totalRemaining {get; set;}
        @AuraEnabled public String trackingNum {get; set;}
    }

    public class ShipmentInfo {
        public ShipmentData shipment {get; set;}
        public List<ShipmentItemData> shipmentItemSummary {get; set;}
        public List<ShipmentItemData> shipmentItems {get; set;}
        /*public List<InvoiceData> invoice {get; set;}*/

        public ShipmentInfo(){
            this.shipment = new ShipmentData();
            this.shipmentItems = new List<ShipmentItemData>();
            /*this.invoice = new List<InvoiceData>();*/
        }
    }

    public class OrderInfo {
        public OrderData order {get; set;}
        public List<OrderItemData> orderItems {get; set;}
        public Integer currentStep {get; set;}

        public OrderInfo(){
            this.order = new OrderData();
            this.orderItems = new List<OrderItemData>();
            this.currentStep = 1;
        }
    }

    public class InitData{
        @AuraEnabled public String customerId {get; set;}
        @AuraEnabled public String CreatedById {get; set;}
        @AuraEnabled public OrderData order {get; set;}
        @AuraEnabled public List<OrderItemData> orderItems {get; set;}
        @AuraEnabled public List<ShipmentInfo> shipmentInfo {get; set;}
        @AuraEnabled public List<InvoiceData> invoice {get; set;}
        @AuraEnabled public Integer currentStep {get; set;}
        @AuraEnabled public String paymentTermVal {get; set;}
        @AuraEnabled public String freightTermVal {get; set;}
        @AuraEnabled public Boolean isSuccess {get; set;}
        @AuraEnabled public String errorMsg {get; set;}
        @AuraEnabled public Boolean isApprovalMode {get; set;}
        @AuraEnabled public Boolean isShowEditAndSyncBtn {get; set;}
        @AuraEnabled public Boolean isShowEditBtn {get; set;}
        @AuraEnabled public Boolean isShowSyncBtn {get; set;}
        @AuraEnabled public Boolean isInnerUser {get; set;}
        @AuraEnabled public String customerType {get; set;}
        @AuraEnabled public String customerCluster {get; set;}
        @AuraEnabled public String customerOrgCode {get; set;} // @mark ,napoleon, 23-1-1
        @AuraEnabled public String defaultPaymentTerm {get; set;}
        @AuraEnabled public List<Util.PTSelectItem> paymentTermOptions {get; set;}
        @AuraEnabled public Boolean hasAmwareProducts {get;set;}
        @AuraEnabled public Boolean hasAmwareShipments {get;set;}
        @AuraEnabled public String currentUserProfile {get; set;}
        // Added By Anony 23.1.10 ---Start
        @AuraEnabled public Boolean isNotifiedInsideSales {get; set;}
        @AuraEnabled public Boolean isSalesManagerModified {get; set;}
        // Added By Anony 23.1.10 ---End
        @AuraEnabled public String Authorization {get; set;}
        @AuraEnabled public Boolean showTax {get;set;}

        /**
         * @description This property is used for fetching Purchase Order attachments.
         */
        @AuraEnabled
        public Id attachmentSourceId { get; set; }
        public InitData(){
            this.order = new OrderData();
            this.orderItems = new List<OrderItemData>();
            this.shipmentInfo = new List<ShipmentInfo>();
            this.invoice = new List<InvoiceData>();
            this.paymentTermOptions = new List<Util.PTSelectItem>();
            this.currentStep = 1;
            this.paymentTermVal = '';
            this.freightTermVal = '';
            this.isSuccess = false;
            this.errorMsg = '';
            this.isApprovalMode = false;
            this.isInnerUser = false;
            this.isShowEditAndSyncBtn = false;
            this.isShowEditBtn = false;
            this.isShowSyncBtn = false;
            this.customerType = '';
            this.hasAmwareProducts = false;
            this.hasAmwareShipments = false;
            // Added By Anony 23.1.10 ---Start
            this.isNotifiedInsideSales = false;
            this.isSalesManagerModified = false;
            // Added By Anony 23.1.10 ---End
        }
    }

    public class RespData {
        @AuraEnabled public Boolean isSuccess {get; set;}
        @AuraEnabled public String errorMsg {get; set;}

        public RespData(){
            this.isSuccess = false;
            this.errorMsg = '';
        }
    }

    /**
     * @description Caculate Canada taxes for purchase order.
     */
    public static void caculateTotalTax(Purchase_Order__c po){
        //CA Province Tax Rate
        Map<String, CA_Tax_Rate_By_Province__mdt> rateMap = Util.getCanadaTaxRateMap();

        CA_Tax_Rate_By_Province__mdt taxRate;
        if(po.Is_Alternative_Address__c){
            taxRate = rateMap.get(po.Additional_Shipping_Province__c);
        }else{
            //Shipping Address
            Account_Address__c shipAddress = [
                SELECT
                State__c
                FROM Account_Address__c
                WHERE Id=:po.Shipping_Address__c
            ];
            taxRate = rateMap.get(shipAddress.State__c);
        }
        if(taxRate != null){
            Decimal baseAmount = 0;
            if(po.Product_Price__c != null){
                baseAmount+=po.Product_Price__c;
            }
            if(po.Freight_Fee__c !=null){
                baseAmount+=po.Freight_Fee__c;
            }
            if(po.Handling_Fee__c !=null){
                baseAmount+=po.Handling_Fee__c;
            }
            if(po.Freight_Fee_Waived__c !=null){
                baseAmount-=Math.abs(po.Freight_Fee_Waived__c);
            }
            if(po.Extra_Freight_Fee_To_Be_Waived__c !=null){
                baseAmount-=Math.abs(po.Extra_Freight_Fee_To_Be_Waived__c);
            }
            if(po.Discount_Amount__c !=null){
                baseAmount-=Math.abs(po.Discount_Amount__c);
            }
            if(po.Surcharge_Amount__c !=null){
                baseAmount+=po.Surcharge_Amount__c;
            }

            po.GST__c = (baseAmount*taxRate.GST__c/100).setScale(2,System.RoundingMode.HALF_UP);
            po.HST__c = (baseAmount*taxRate.HST__c/100).setScale(2,System.RoundingMode.HALF_UP);
            po.PST__c = (baseAmount*taxRate.PST__c/100).setScale(2,System.RoundingMode.HALF_UP);
            po.QST__c = (baseAmount*taxRate.QST__c/100).setScale(2,System.RoundingMode.HALF_UP);
            po.Total_Tax__c = (baseAmount*(taxRate.Total_Rate__c-taxRate.PST__c)/100).setScale(2,System.RoundingMode.HALF_UP);
        }else{
            po.GST__C = null;
            po.HST__c = null;
            po.PST__c = null;
            po.QST__c = null;
            po.Total_Tax__c = null;
        }
    }
}