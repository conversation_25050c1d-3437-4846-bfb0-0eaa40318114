/**
 * <AUTHOR>
 * @date 2024-02-28
 * @description batch job to process order interface data
 */
public with sharing class CCM_ProcessOrderInterfaceBatch implements Database.Batchable<SObject>{
    public String query;
    public CCM_ProcessOrderInterfaceBatch() {
        this.query = 'SELECT Id FROM OrderInterface__c WHERE Return_Code__c = \'NA\'';
    }

    public CCM_ProcessOrderInterfaceBatch(String query) {
        this.query = query;
    }

    public Database.QueryLocator start(Database.BatchableContext objBC) {
        return Database.getQueryLocator(this.query);
    }

    public void execute(Database.BatchableContext objBC, List<SObject> scope) {
        List<OrderInterface__c> orderInterfaceList = (List<OrderInterface__c>)scope;
        Set<String> orderInterfaceIds = new Set<String>();
        for(OrderInterface__c oi : orderInterfaceList) {
            orderInterfaceIds.add(oi.Id);
        }

        List<OrderInterface__c> oiList = [SELECT Attribute10__c, Attribute1__c, Attribute2__c, Attribute3__c, Attribute4__c,
        Attribute5__c, Attribute6__c, Attribute7__c, Attribute8__c, Attribute9__c, BillTo__c, Carrier_Code__c, CurrencyCode__c,
        Customer_Freight_Account__c, Customer__c, Date_Order__c, Dropship_Address1__c, Dropship_Address2__c, Dropship_City__c,
        Dropship_Country__c,Dropship_Name__c,Dropship_State__c,Dropship_ZIP__c,Error_Message__c,Error_Msg__c,
        Expected_Delivery_Date__c,Feright_Fee__c,Freight_Term__c,Handling_Fee__c,Hold_Reason__c,IsHold__c,Notes__c,
        Order_Number__c,Order_OracleID__c,Order_Source__c,Order_Status__c,Order_Type__c,Org_Code__c,PO_Number__c,Payment_Term__c,
        Price_List__c,Sales_Channel__c,Sales_Rep__c,Salesforce_PurchaseOrder_Number__c,ShipTo__c,Shipping_Method__c,
        Shipping_Priority__c,Telephone_Number__c, (SELECT Cannel_Quantity__c,Line_Attribute10__c,Line_Attribute1__c,Line_Attribute2__c,
        Line_Attribute3__c,Line_Attribute4__c,Line_Attribute5__c,Line_Attribute6__c,Line_Attribute7__c,Line_Attribute8__c,
        Line_Attribute9__c,Line_Hold_Reason__c,Line_IsHold__c,Line_Price_List__c,Line_Status__c,Line_Type__c,
        OrderInterface__c,OrderLine_OracleID__c,Order_Quantity__c,Price__c,Product__c,Promation_Code__c,Request_Date__c,
        Reverse_Quantity__c,Salesforce_PurchaseOrderLine_Number__c,Ship_Date__c FROM OrderItemInterface__r) FROM OrderInterface__c WHERE Id IN :orderInterfaceIds];

        CCM_OrderInterfaceUtil.processDataFromInterface(oiList);
        
    }

    public void finish(Database.BatchableContext objBC) {
        if(!System.Test.isRunningTest()) {
            Database.executeBatch(new CCM_ProcessInvoiceInterfaceBatch(), 1);
        }
    }
}