public with sharing class CCM_ChannelCustomerFieldUpdate implements Triggers.Handler{
    private List<Account> newRecords = (List<Account>)Trigger.new;
    private Map<Id, Account> oldMap = (Map<Id, Account>)Trigger.oldMap;

    public static Boolean isRun = true;
    public void handle() {
        if(!isRun) {
            return;
        }
        List<Sales_Program__c> authorBrandUpdatedList = new List<Sales_Program__c>();
        for(Account newRecord : this.newRecords) {
            if(newRecord.RecordTypeId == CCM_Contants.CHANNEL_RECORDTYPEID && newRecord.Risk_Code__c == 'H' && newRecord.Risk_Code__c != oldMap.get(newRecord.Id).Risk_Code__c){
                List<Sales_Program__c> spList = [SELECT Id,Payment_Term__c,Payment_Term_Description__c FROM Sales_Program__c WHERE Customer__c = :newRecord.Id];
                newRecord.Credit_Limit__c = 1;
                if(spList.size() > 0){
                    for(Sales_Program__c sp : spList){
                        sp.Payment_Term__c = 'NA032';
                        sp.Payment_Term_Description__c = 'Make ensures full payment before shipment of the goods.';
                        authorBrandUpdatedList.add(sp);
                    }
                }
            }
        }
        if(authorBrandUpdatedList.size() > 0){
            update authorBrandUpdatedList;
        }
    }
}