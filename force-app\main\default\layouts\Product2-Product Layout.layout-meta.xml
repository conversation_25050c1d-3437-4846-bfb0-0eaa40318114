<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Product Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>IsActive</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ProductCode</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Product_Key_Words__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Warranty_Year__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PIM_ExternalID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Sellable__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Sellable_CCA__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Chervon_Survey__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EBS_Category_1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EBS_Category_2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EBS_Category_3__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CS_Exchange_Rate__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Unavailable_For_Select__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Country_of_Origin__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Item_Number__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Product_unit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EBS_Status__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Full_Pallet_Quantity__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>New_Product__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FilterType__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EBS_ExternalID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EBS_Description_of_Category_1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EBS_Description_of_Category_2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EBS_Description_of_Category_3__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Brand_Series__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Product Type</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Brand_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RecordTypeId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ProductModel__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Category_3__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Product_Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Category_1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Category_2__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Warranty and Price Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Warranty_Period__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>THD_store_warranty_period__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Amazon_Store_warranty_period__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Dealer_Grainger_Store_warranty_period__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Repairable__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Landed_Cost_1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Pick_Up__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Pick_up_Fee__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Base_Cost__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Source__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Package Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Length__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Width__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Height__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Product_Weight__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Weight_unit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Weight__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Unique_Key__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExternalID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExternalId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <relatedList>RelatedStandardPriceList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedPricebookEntryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Hierarchy__c</fields>
        <fields>Category__c</fields>
        <fields>Description_of_Category__c</fields>
        <relatedList>Product_Category__c.Product__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>RECORDTYPE</fields>
        <fields>Parts__c</fields>
        <fields>ExplosionID__c</fields>
        <relatedList>Kit_Item__c.Product__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>Add</excludeButtons>
        <excludeButtons>CreateNewProduct</excludeButtons>
        <fields>PRODUCT2.NAME</fields>
        <fields>PRODUCT2.CUSTOMER_PRODUCT_ID</fields>
        <fields>PRODUCT2.ACTIVE</fields>
        <fields>Category_1__c</fields>
        <relatedList>Product2.Parent_Product__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Sub_storage__c</fields>
        <fields>Available_Inventory__c</fields>
        <fields>Inventory_On_Hand__c</fields>
        <fields>Description__c</fields>
        <relatedList>Storage__c.Product__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Project__c.Product__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Kit_Item__c.Kit__c</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hC000000JAUpG</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
