/**
 * @description Controller for Announcement
 */
public without sharing class CCM_PromotionAnnounceCtl {

    public static final String THUMBNAIL_IMAGE = 'Thumbnail image';
    public static final String BANNER_IMAGE = 'Banner image';
    public static final String ATTACHMENT = 'Promotion Packet';

    /**
     * @description Get Promotion Detail
     */
    @AuraEnabled
    public static String getPromotionAttachment(String promotionId) {
        String thumbNail = ATTACHMENT;
        // get all image
        List<Promotion_Photo__c> photoList = [SELECT Id, Promotion__c, Photo_Category__c, ContentVersionId__c, SEQUENCE__C FROM Promotion_Photo__c WHERE Promotion__c = :promotionId AND Photo_Category__c = :thumbNail ORDER BY SEQUENCE__C ASC ];
        if(photoList.size()==0) {
            return '';
        } else {
            return Label.Salesforce_Base_URL + CCM_PromotionUtil.IMAGE_BASE_URL + photoList[0].ContentVersionId__c;
        }
    }

    /**
     * @description Get userProfile
     * update by Austin
     */
    @AuraEnabled
    public static Map<String, Boolean> getUserProfile(){
        List<User> user = [SELECT Profile.Name  FROM User WHERE Id =: UserInfo.getUserId() ];
        Map<String, Boolean> isSecondTier = new Map<String, Boolean>();
        if (user.size() > 0) {
            if (user[0].Profile.Name == 'Partner Community 2nd tier dealer sales') {
                isSecondTier.put('isSecondTier', false);
                return isSecondTier;
            }
        }
        isSecondTier.put('isSecondTier', true);
        User currentUser = [SELECT Id, ContactId, Contact.AccountId, Contact.Account.Distributor_or_Dealer__c, Profile.Name FROM User WHERE Id =: UserInfo.getUserId()];
        if (currentUser.Contact != null && currentUser.Contact.AccountId != null) {
            Account acc = [SELECT Id,ORG_Code__c FROM Account WHERE Id = :currentUser.Contact.AccountId];
            if(acc.ORG_Code__c == 'CCA'){
                isSecondTier.put('isCCA', true);
            }else{
                isSecondTier.put('isCCA', false);
            }
        }
        return isSecondTier;
    }

    /**
     * @description Brand display
     * update by Austin
     */
    @AuraEnabled
    public static BrandsWrapper getFirstTireBrands(){
        Set<String> firstCustomerId = new Set<String>();
        Set<String> brands = new Set<String>();
        BrandsWrapper results = new BrandsWrapper();
        User currentUser = [SELECT Id, ContactId, Contact.AccountId, Contact.Account.Distributor_or_Dealer__c, Profile.Name FROM User WHERE Id =: UserInfo.getUserId()];
        if (currentUser.Contact != null && currentUser.Contact.AccountId != null) {
            //modify by austin ********,取一级customerId
            if (currentUser.Profile.Name.contains('Partner Community 2nd Tier Dealer Sales')) {
                Map<String, String> topCustomer = CCM_Dropship_Quotation_DetailCtl.getTopCustomerInfo(new List<String>{currentUser.Contact.AccountId});
                for (String firstKey : topCustomer.keySet()) {
                    firstCustomerId.add(firstKey);
                }
                if (firstCustomerId.size() > 0) {
                    List<Sales_Program__c> firstauthorizedBrands = [SELECT Id, Customer__c, Customer_Type__c, Brands__c, Price_Book__c, Approval_Status__c,RecordType.DeveloperName
                                                                    FROM Sales_Program__c
                                                                    WHERE Customer__c IN: firstCustomerId
                                                                    AND (Approval_Status__c = 'Approved' OR Approval_Status__c = '')
                                                                    AND RecordType.DeveloperName IN ('Dropship_Sales_Standard', 'Dropship_Sales_Customized')];
                    if (firstauthorizedBrands.size() > 0) {
                        for(Sales_Program__c authorizedBrand : firstauthorizedBrands){
                            if(String.isNotBlank(authorizedBrand.Brands__c)){
                                brands.add(authorizedBrand.Brands__c);
                            }
                        }
                    }
                }
            }else {
                // firstCustomerId.add(currentUser.Contact.AccountId);
                Util.SelectItem brandScopeItem = new Util.SelectItem();
                Map<String, Util.SelectItem> brandOptMap = Util.getSelectOptMap(new Sales_Program__c(), 'Brands__c');
                for (String key : brandOptMap.keySet()) {
                    // brandScopeList.add(brandOptMap.get(key));
                    brands.add(brandOptMap.get(key).value);
                }
            }
            if (brands.size() > 0 ) {
                for (String brand : brands) {
                    results.brandsList.add(brand);
                }
            }
        }
        return results;
    }

    /**
     * @description: get picklist by record type
     */
    @AuraEnabled
    public static List<PromotionTypePickListWrapper> getPickListValueByRecordType(){
        List<PromotionTypePickListWrapper> wrapperList = new List<PromotionTypePickListWrapper>();
        User currentUser = [SELECT ContactId FROM User WHERE Id = :UserInfo.getUserId()];
        Boolean isPortalUser = false;
        if (currentUser != null && currentUser.ContactId != null) {
            isPortalUser = true;
        }
        Schema.DescribeFieldResult fieldDescribe = Promotion2__c.Promotion_Type_for_External__c.getDescribe();
        List<CCM_PromotionUtil.PromotionPickWrapper> sellInPromoTypeList = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        sellInPromoTypeList = CCM_PromotionUtil.getPickListMap(fieldDescribe, sellInPromoTypeList);

        // get promotion type by sell in recordtype
        List<CCM_PromotionUtil.PromotionPickWrapper> availableSellInPromoTypeList = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        if (isPortalUser) {
            for (CCM_PromotionUtil.PromotionPickWrapper promotionTypeWrapper : sellInPromoTypeList) {
                if (promotionTypeWrapper.Id != 'Payment Term Promo' && promotionTypeWrapper.Id != 'Whole Order Promo' && promotionTypeWrapper.Id != 'Buy More Save More') {//Change by Zoe,for french portal 20280827
                    availableSellInPromoTypeList.add(promotionTypeWrapper);
                }
            }
        } else {
            availableSellInPromoTypeList = sellInPromoTypeList;
        }
        PromotionTypePickListWrapper sellInPickList = new PromotionTypePickListWrapper();
        sellInPickList.recordType = CCM_PromotionUtil.PROMOTION_SELLIN_RECORDTYPE_APINAME;
        sellInPickList.picklistList = availableSellInPromoTypeList;
        wrapperList.add(sellInPickList);

        // get promotion type by sell through recordtype
        List<CCM_PromotionUtil.PromotionPickWrapper> sellThroughPromoTypeList = availableSellInPromoTypeList.clone();
        String[] sellThroughType = new String[] {CCM_PromotionUtil.PROMOTION_TYPE_BOGO_NAME, CCM_PromotionUtil.PROMOTION_TYPE_PRICE_DISCOUNT_NAME};
        for( Integer i=sellThroughPromoTypeList.size()-1 ; i>=0; i-- ) {
            CCM_PromotionUtil.PromotionPickWrapper pick = sellThroughPromoTypeList.get(i);
            if(sellThroughType[0].equalsIgnoreCase(pick.Id) || sellThroughType[1].equalsIgnoreCase(pick.Id)) {//Change by Zoe,for french portal 20280827
                continue;
            } else {
                sellThroughPromoTypeList.remove(i);
            }
        }
        CCM_PromotionUtil.PromotionPickWrapper objBMSMType = new CCM_PromotionUtil.PromotionPickWrapper();
        objBMSMType.Id = 'Buy More Save More';
        objBMSMType.Name = 'Buy More Save More';
        sellThroughPromoTypeList.add(objBMSMType);
        PromotionTypePickListWrapper sellThroughPickList = new PromotionTypePickListWrapper();
        sellThroughPickList.recordType = CCM_PromotionUtil.PROMOTION_SELLTHROUGH_RECORDTYPE_APINAME;
        sellThroughPickList.picklistList = sellThroughPromoTypeList;
        wrapperList.add(sellThroughPickList);
        return wrapperList;
    }

    /**
     * @description: check is Channel (requirement changed) both BEAM and Channel can create standard promotion
     */
    public static Boolean checkIsChannel(String promotionId) {
        List<Promotion2__c> promotionList = [SELECT Id, Owner.Profile.Name FROM Promotion2__c WHERE Id = :promotionId ];
        if(promotionList.size()>0) {
            if(promotionList[0].Owner.Profile.Name == 'BEAM' || promotionList[0].Owner.Profile.Name == 'Channel') {
                return true;
            }
        }
        return false;
    }

    /**
     * add by jet
     */
    @AuraEnabled
    public static PromotionDetailWrapper getPromotionDetail(String promotionWindowId) {
        PromotionDetailWrapper result = new PromotionDetailWrapper();

        Contact con = getCustomerIdCNA();
        Id customerId = con.AccountId;
        String orgCode = con.Account.ORG_Code__c;

        if(orgCode == 'CCA') {
            result = getPromotionDetailCCA(promotionWindowId);
        } else {
            result = getPromotionDetailCNA(promotionWindowId, customerId);
        }

        return result;
    }

    /**
     * add by jet
     */
    @AuraEnabled
    public static PromotionDetailWrapper getPromotionDetailCNA(String promotionWindowId, String customerId) {

        PromotionDetailWrapper result = new PromotionDetailWrapper();
        // Modified By Anony:Promotion Issue 40 fix. ---Start 23.1.23
        String currentUserProfileName = CCM_PromotionUtil.getCurrentProfileName();
        // Modified By Anony:Promotion Issue 40 fix. ---End 23.1.23

        // Set<String> topCustomerIds = new Set<String>();
        Set<Id> accIds = new Set<Id>();
        Map<String, Set<String>> x1x2Map = new Map<String, Set<String>>();

        //get all promotion window
        List<Promotion_Window__c> windowList = [
            SELECT Id, Promotion__c, Start_Date_End_Date__c, Announcement_Date__c,
                Start_Date__c, End_Date__c, Claim_End_Date__c,
                // add haibo: promotion (french) 翻译
                Promotion__r.Name, Promotion__r.Promotion_Name_French__c, Promotion__r.Promo_Code__c,
                Promotion__r.Is_DropShip_Promotion__c,
                Promotion__r.Non_DropShip_Promotion__c,
                Promotion__r.Brands__c,
                Promotion__r.Promotion_Code_For_External__c,
                Promotion__r.Promotion_Type_for_External__c,
                // add haibo: promotion (french) 翻译
                Promotion__r.Promo_Details__c, Promotion__r.Promotion_Details_French__c, Promotion__r.Promotion_Type__c,
                Promotion__r.RecordType.DeveloperName
            FROM Promotion_Window__c
            WHERE Id =:promotionWindowId
        ];

        if(windowList.size() == 0 ) {
            return null;
        }
        // Set<Id> accIds = new Set<Id>();
        // Modified By Anony:Promotion Issue 40 fix. ---Start 23.1.23
        if (currentUserProfileName.contains('Community')) {
            // is 2nd tier dealer
            if (currentUserProfileName.contains('2nd')) {
                CCM_PromotionUtil.SalesHierarchyRelatedWrapper salesHierarchyData = CCM_PromotionUtil.getSalesHierarchyData();
                x1x2Map = salesHierarchyData.x1x2Map;
                // 找到该customer对应的一级
                for (String x1Customer : x1x2Map.keySet()) {
                    for(String secondId : x1x2Map.get(x1Customer)) {
                        if (customerId.equals(secondId)) {
                            accIds.add(x1Customer);
                        }
                    }
                }
            }
            // is not 2nd tier dealer
            else{
                accIds.add(customerId);
            }
            // Modified By Anony:Promotion Issue 40 fix. ---End 23.1.23
        } else {
            // 查到了channel customer
            Set<Id> customerIds = CCM_PromotionUtil.getCustomer();
            for (Promotion_Target_Customer__c tc : [SELECT Promotion__c, Customer__c FROM Promotion_Target_Customer__c WHERE Customer__c IN :customerIds  AND Promotion__c = :windowList[0].Promotion__c AND Promotion__r.Promotion_Status__c != 'Closed'])  {
                accIds.add(tc.Customer__c);
            }
            System.debug('*** accIds: ' + accIds);
            if (accIds.size() == 0) {
                return null;
            }
        }

        //Get the chosen promotion window
        Promotion_Window__c window = windowList[0];
        String promotionId = window.Promotion__c;
        String promotionType = window.Promotion__r.Promotion_Type__c == null ? '' : window.Promotion__r.Promotion_Type__c;
        String recordType = window.Promotion__r.RecordType.DeveloperName;

        Boolean isDirectDealer = window.Promotion__r.Non_DropShip_Promotion__c;
        Boolean isDropship = window.Promotion__r.Is_DropShip_Promotion__c;
        List<String> brands = window.Promotion__r.Brands__c.split(';');
        Set<String> authPriceBookIds = new Set<String>();
        if (isDropship && !isDirectDealer) {
            Map<Id, List<CCM_PromotionUtil.PriceBookWrapper>> customerPriceBooks = CCM_PromotionWithoutSharingCtl.getDropShipPriceBookCNA(accIds, brands);
            System.debug('customerPriceBooks====='+ customerPriceBooks);
            for (Id accId : accIds) {
                if(customerPriceBooks.get(accId) == null){
                   continue;
                }
                for (CCM_PromotionUtil.PriceBookWrapper pbw : customerPriceBooks.get(accId)) {
                    authPriceBookIds.add(pbw.pricebookId);
                }
            }
        } else {
            for (Sales_Program__c sp : [SELECT Id, Brands__c, Price_Book__c, Customer__c, Customer__r.Name
                                            FROM Sales_Program__c
                                            WHERE Approval_Status__c = 'Approved'
                                            AND Price_Book__r.IsActive = true
                                            AND Customer__r.RecordType.DeveloperName = 'Channel'
                                            AND RecordType.DeveloperName != 'Service'
                                            AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME
                                            AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_NAME
                                            AND Customer__c IN :accIds
                                            AND Brands__c IN :brands]) {
                authPriceBookIds.add(sp.Price_Book__c);
            }
        }
        System.debug('=======authPriceBookIds'+ authPriceBookIds);
        //Get smaller images for promotion
        String thumbNail = THUMBNAIL_IMAGE;
        List<Promotion_Photo__c> photoList = [SELECT Id, Promotion__c, Photo_Category__c, ContentVersionId__c, SEQUENCE__C FROM Promotion_Photo__c WHERE Promotion__c = :promotionId AND Photo_Category__c = :thumbNail ORDER BY SEQUENCE__C ASC ];
        Set<Id> cvIds = new Set<Id>();
        for(Promotion_Photo__c photo : photoList){
            cvIds.add(photo.ContentVersionId__c);
        }

        thumbNail = ATTACHMENT;
        List<Promotion_Photo__c> attachmentList = [SELECT Id, Promotion__c, Photo_Category__c, ContentVersionId__c, SEQUENCE__C FROM Promotion_Photo__c WHERE Promotion__c = :promotionId AND Photo_Category__c = :thumbNail ORDER BY SEQUENCE__C ASC ];

        //Get price list
        List<Promotion_Performance__c> priceList;

        Date today = Date.today();
        String recordTypeId = CCM_PromotionUtil.PERFORMANCE_STANDARD_RECORDTYPE_ID;
        priceList = [SELECT Id,
                            Promotion_Window__c,
                            Customer__c,
                            Product__c,
                            Product__r.Name,
                            Product__r.ProductCode,
                            Product__r.SF_Description__c,
                            MSRP__c,
                            PriceList__c,
                            PriceList__r.Name,
                            WW_Tier2__c,
                            Amount_Off__c,
                            Promo_Sales_Discount__c,
                            Product__r.Full_Pallet_Quantity__c,
                            Promo_Sales_Chervon_Promo_Invoice__c,
                            Additional_Discount__c,
                            Line_Total__c, //= Promo_Sales_Chervon_Promo_Invoice__c * Product__r.Full_Pallet_Quantity__c
                            Promo_Sales_Chervon_Net_Down_Price__c,
                            PMAPP__c,
                            Promo_Credit__c,
                            Promo_Sales_Chervon_Funding__c,
                            Pricing_Start_Date__c,
                            Pricing_End_Date__c,
                            Model_Type__c,
                            Model_Number__c
                        FROM Promotion_Performance__c
                        WHERE Promotion_Window__c = :promotionWindowId
                        AND RecordTypeId = :recordTypeId
                        AND PriceList__c IN :authPriceBookIds
                        AND ( (Pricing_Start_Date__c <= :today AND Pricing_End_Date__c >= :today)
                              OR
                              (Pricing_Start_Date__c <= :today AND Pricing_End_Date__c = null)
                            )
                        ORDER BY Promotion_Window__c, Customer__r.Name, Model_Type__c, PriceList__r.Name, Product__r.ProductCode, WW_Tier2__c, Promo_Sales_Chervon_Promo_Invoice__c DESC, Pricing_Start_Date__c];


        //Fill promotion detail infor
        result.promotionId          = promotionId;
        result.promotionWindowId    = promotionWindowId;
        // add haibo: 添加 promotion (French)法语翻译字段
        if (UserInfo.getLanguage() == Label.CCM_Portal_French) {
            result.promotionName = window.Promotion__r.Promotion_Name_French__c;
        } else {
            result.promotionName = window.Promotion__r.Name;
        }
        result.promotionCode        = window.Promotion__r.Promotion_Code_For_External__c;
        // add haibo: 添加 promotion (French)法语翻译字段
        if (UserInfo.getLanguage() == Label.CCM_Portal_French) {
            result.promotionDetail = window.Promotion__r.Promotion_Details_French__c;
        } else {
            result.promotionDetail = window.Promotion__r.Promo_Details__c;
        }
        result.promotionType        = window.Promotion__r.Promotion_Type__c;
        result.timeWindow           = window.Start_Date_End_Date__c;
        result.promotionRecordType  = window.Promotion__r.RecordType.DeveloperName;
        result.promotionTypeExt     = window.Promotion__r.Promotion_Type_for_External__c;

        if(today > window.End_Date__c && today <= window.Claim_End_Date__c) {
            result.isClaim = true;
        }
        if ( window.Announcement_Date__c <= today && today < window.Start_Date__c ) {
            //soon
            result.isComingSoon = true;
        }
        result.photoUrlList = new List<String>();
        for(Id cvId : cvIds) {
            result.photoUrlList.add(Label.Salesforce_Base_URL + CCM_PromotionUtil.IMAGE_BASE_URL + cvId);
        }

        result.attachmentUrlList = new List<String>();
        for(Promotion_Photo__c attachment : attachmentList) {
            result.attachmentUrlList.add(Label.Salesforce_Base_URL + CCM_PromotionUtil.IMAGE_BASE_URL + attachment.ContentVersionId__c);
        }

        //promotion -> rules -> offering
        List<Promotion_Rule__c> ruleList = [SELECT Id FROM Promotion_Rule__c WHERE Promotion__c = :promotionId ];
        Set<Id> ruleIds = new Set<Id>();
        for(Promotion_Rule__c rule : ruleList) {
            ruleIds.add(rule.Id);
        }
        List<Promotion_Offering__c> offeringList =[SELECT Id, RecordType.DeveloperName FROM Promotion_Offering__c WHERE Promotion_Rule__c IN :ruleIds ];
        String offeringType = '';
        if( offeringList.size() > 0 ) {
            offeringType = offeringList[0].RecordType.DeveloperName;
        }

        Boolean isSameProduct = false;
        //For specific free good(same as promo product), add one more filed "net down price"
        if(recordType == CCM_PromotionUtil.PROMOTION_SELLIN_RECORDTYPE_APINAME
            && offeringType == 'Specific_Free_Good'
            && CCM_PromotionUtil.checkSameProduct(promotionId)){
            isSameProduct = true;
        }

        //Get displayed fields and headers for promotion price list
        PromoOfferHeaders__mdt finalConfig = null;

        String headerName = '';
        if (recordType == 'Sell_In_Promotion') {
            headerName = 'Sell_In_Promo_Detail';
        } else {
            headerName = 'Sell_Through_Promo_Detail';
        }
        List<PromoOfferHeaders__mdt> headersConfig = [SELECT Fields__c, Headers__c FROM PromoOfferHeaders__mdt WHERE DeveloperName = :headerName ];

        if (headersConfig.size() > 0) {
            finalConfig = headersConfig[0];
        }
        if(finalConfig == null ) {
            return result;
        }
        //Example: String header = 'model;description;msrp;pricing;amountOff;promoInvoice';
        String header = finalConfig.Fields__c;
        if(isSameProduct) {
            header += ';netDownPrice';
        }

        //Example: String headerLabel = 'Model;Description;MSRP;Pricing;Amount Off;Promo Invoice';
        String headerLabel = finalConfig.Headers__c;
        if(isSameProduct) {
            headerLabel += ';Net Down Price';
        }

        //Get all content for price list
        //fields and headers are used by front end to choose which field to display
        result.priceList = new List<CCM_PromotionUtil.PriceListWrapper>();
        result.priceListOffering = new List<CCM_PromotionUtil.PriceListWrapper>();

        Set<String> buyModelNumberSet = new Set<String>();
        Set<String> freeGoodsModelNumberSet = new Set<String>();
        Boolean hasPMAPP = false;
        for(Promotion_Performance__c priceListOne : priceList) {
            CCM_PromotionUtil.PriceListWrapper onePriceListWrapper = CCM_PromotionUtil.generatePriceListWrapper(priceListOne);
            if (onePriceListWrapper.pmapp != null && onePriceListWrapper.pmapp != '') {
                if (Decimal.valueOf(onePriceListWrapper.pmapp) > 0) {
                    hasPMAPP = true;
                }
            }
            if (onePriceListWrapper.modelType == 'Buy') {
                if (!buyModelNumberSet.contains(priceListOne.Model_Number__c) && authPriceBookIds.contains(priceListOne.PriceList__c)) {
                    result.priceList.add(onePriceListWrapper);
                    buyModelNumberSet.add(priceListOne.Model_Number__c);
                }
            } else if (onePriceListWrapper.modelType == 'Free Goods' && authPriceBookIds.contains(priceListOne.PriceList__c)) {
                if (!freeGoodsModelNumberSet.contains(priceListOne.Model_Number__c)) {
                    result.priceListOffering.add(onePriceListWrapper);
                    freeGoodsModelNumberSet.add(priceListOne.Model_Number__c);
                }
            } else {
                if (!buyModelNumberSet.contains(priceListOne.Model_Number__c)) {
                    result.priceList.add(onePriceListWrapper);
                    buyModelNumberSet.add(priceListOne.Model_Number__c);
                }
                if (!freeGoodsModelNumberSet.contains(priceListOne.Model_Number__c)) {
                    result.priceListOffering.add(onePriceListWrapper);
                    freeGoodsModelNumberSet.add(priceListOne.Model_Number__c);
                }
            }
        }
        // validate if need show pmapp
        if (recordType == 'Sell_In_Promotion' && hasPMAPP) {
            header = header.replace(';msrp;', ';msrp;pmapp;');
            headerLabel = headerLabel.replace(';MSRP;', ';MSRP;PMAPP;');
        }
        // offering price list header
        result.priceListHeaderOffering = new List<String>(header.split(';'));
        result.priceListHeaderLabelOffering = new List<String>(headerLabel.split(';'));

        // threshold price list header
        if (window.Promotion__r.Promotion_Type__c == 'Full Pallet Promo') {
            header = header.replace(';description;', ';description;palletQty;');
            headerLabel = headerLabel.replace(';Description;', ';Description;Pallet Qty;');
        }

        result.priceListHeader = new List<String>(header.split(';'));
        result.priceListHeaderLabel = new List<String>(headerLabel.split(';'));

        return result;
    }

    /**
     * @description Get Promotion Detail
     *
     */
    @AuraEnabled
    public static PromotionDetailWrapper getPromotionDetailCCA(String promotionWindowId) {
        PromotionDetailWrapper result = new PromotionDetailWrapper();

        Id customerId = getCustomerId();


        //get all promotion window
        List<Promotion_Window__c> windowList = [SELECT Id, Promotion__c, Start_Date_End_Date__c, Announcement_Date__c,
                                                Start_Date__c, End_Date__c, Claim_End_Date__c,
                                                // add haibo: promotion (french) 翻译
                                                Promotion__r.Name, Promotion__r.Promotion_Name_French__c, Promotion__r.Promo_Code__c,
                                                Promotion__r.Is_DropShip_Promotion__c,
                                                Promotion__r.Brands__c,
                                                Promotion__r.Non_DropShip_Promotion__c, //Bug fix by Zoe on 2024-8-22 during french portal testing ,origial bug
                                                Promotion__r.Promotion_Code_For_External__c,
                                                Promotion__r.Promotion_Type_for_External__c,
                                                // add haibo: promotion (french) 翻译
                                                Promotion__r.Promo_Details__c, Promotion__r.Promotion_Details_French__c, Promotion__r.Promotion_Type__c,
                                                Promotion__r.RecordType.DeveloperName
                                                FROM Promotion_Window__c WHERE Id =:promotionWindowId ];
        if(windowList.size() == 0 ) {
            return null;
        }
        Set<Id> accIds = new Set<Id>();
        if ([SELECT Name FROM Profile WHERE Id = :UserInfo.getProfileId()].Name.contains('Community')) {
            accIds.add(customerId);
        } else {
            Set<Id> customerIds = CCM_PromotionUtil.getCustomer();
            for (Promotion_Target_Customer__c tc : [SELECT Promotion__c, Customer__c FROM Promotion_Target_Customer__c WHERE Customer__c IN :customerIds  AND Promotion__c = :windowList[0].Promotion__c AND Promotion__r.Promotion_Status__c != 'Closed'])  {
                accIds.add(tc.Customer__c);
            }
            System.debug('*** accIds: ' + accIds);
            if (accIds.size() == 0) {
                return null;
            }
        }


        //Get the chosen promotion window
        Promotion_Window__c window = windowList[0];
        String promotionId = window.Promotion__c;
        String promotionType = window.Promotion__r.Promotion_Type__c == null ? '' : window.Promotion__r.Promotion_Type__c;
        String recordType = window.Promotion__r.RecordType.DeveloperName;

        Boolean isDirectDealer = window.Promotion__r.Non_DropShip_Promotion__c;
        Boolean isDropship = window.Promotion__r.Is_DropShip_Promotion__c;
        List<String> brands = window.Promotion__r.Brands__c.split(';');
        Set<String> authPriceBookIds = new Set<String>();
        if (isDropship && !isDirectDealer) {
            Map<Id, List<CCM_PromotionUtil.PriceBookWrapper>> customerPriceBooks = CCM_PromotionUtil.getDropShipPriceBook(accIds, brands);
            for (Id accId : accIds) {
                for (CCM_PromotionUtil.PriceBookWrapper pbw : customerPriceBooks.get(accId)) {
                    authPriceBookIds.add(pbw.pricebookId);
                }
            }
        } else {
            for (Sales_Program__c sp : [SELECT Id, Brands__c, Price_Book__c, Customer__c, Customer__r.Name
                                            FROM Sales_Program__c
                                            WHERE Approval_Status__c = 'Approved'
                                            AND Price_Book__r.IsActive = true
                                            AND Customer__r.RecordType.DeveloperName = 'Channel'
                                            AND RecordType.DeveloperName != 'Service'
                                            AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME
                                            AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_NAME
                                            AND Customer__c IN :accIds
                                            AND Brands__c IN :brands]) {
                authPriceBookIds.add(sp.Price_Book__c);
            }
        }

        //Get smaller images for promotion
        String thumbNail = THUMBNAIL_IMAGE;
        List<Promotion_Photo__c> photoList = [SELECT Id, Promotion__c, Photo_Category__c, ContentVersionId__c, SEQUENCE__C FROM Promotion_Photo__c WHERE Promotion__c = :promotionId AND Photo_Category__c = :thumbNail ORDER BY SEQUENCE__C ASC ];
        Set<Id> cvIds = new Set<Id>();
        for(Promotion_Photo__c photo : photoList){
            cvIds.add(photo.ContentVersionId__c);
        }

        thumbNail = ATTACHMENT;
        List<Promotion_Photo__c> attachmentList = [SELECT Id, Promotion__c, Photo_Category__c, ContentVersionId__c, SEQUENCE__C FROM Promotion_Photo__c WHERE Promotion__c = :promotionId AND Photo_Category__c = :thumbNail ORDER BY SEQUENCE__C ASC ];

        //Get price list
        List<Promotion_Performance__c> priceList;

        Date today = Date.today();
        String recordTypeId = CCM_PromotionUtil.PERFORMANCE_STANDARD_RECORDTYPE_ID;
        priceList = [SELECT Id,
                            Promotion_Window__c,
                            Customer__c,
                            Product__c,
                            Product__r.Name,
                            Product__r.ProductCode,
                            Product__r.SF_Description__c,
                            MSRP__c,
                            PriceList__c,
                            PriceList__r.Name,
                            WW_Tier2__c,
                            Amount_Off__c,
                            Promo_Sales_Discount__c,
                            Product__r.Full_Pallet_Quantity__c,
                            Promo_Sales_Chervon_Promo_Invoice__c,
                            Additional_Discount__c, //Bug fix by Zoe on 2024-8-22 during french portal testing ,origial bug
                            Line_Total__c, //= Promo_Sales_Chervon_Promo_Invoice__c * Product__r.Full_Pallet_Quantity__c
                            Promo_Sales_Chervon_Net_Down_Price__c,
                            PMAPP__c,
                            Promo_Credit__c,
                            Promo_Sales_Chervon_Funding__c,
                            Pricing_Start_Date__c,
                            Pricing_End_Date__c,
                            Model_Type__c,
                            Model_Number__c
                        FROM Promotion_Performance__c
                        WHERE Promotion_Window__c = :promotionWindowId
                        AND RecordTypeId = :recordTypeId
                        AND PriceList__c IN :authPriceBookIds
                        AND ( (Pricing_Start_Date__c <= :today AND Pricing_End_Date__c >= :today)
                              OR
                              (Pricing_Start_Date__c <= :today AND Pricing_End_Date__c = null)
                            )
                        ORDER BY Promotion_Window__c, Customer__r.Name, Model_Type__c, PriceList__r.Name, Product__r.ProductCode, WW_Tier2__c, Promo_Sales_Chervon_Promo_Invoice__c DESC, Pricing_Start_Date__c];


        //Fill promotion detail infor
        result.promotionId          = promotionId;
        result.promotionWindowId    = promotionWindowId;
        // add haibo: 添加 promotion (French)法语翻译字段
        if (UserInfo.getLanguage() == Label.CCM_Portal_French) {
            result.promotionName = window.Promotion__r.Promotion_Name_French__c;
        } else {
            result.promotionName = window.Promotion__r.Name;
        }
        result.promotionCode        = window.Promotion__r.Promotion_Code_For_External__c;
        // add haibo: 添加 promotion (French)法语翻译字段
        if (UserInfo.getLanguage() == Label.CCM_Portal_French) {
            result.promotionDetail = window.Promotion__r.Promotion_Details_French__c;
        } else {
            result.promotionDetail = window.Promotion__r.Promo_Details__c;
        }
        result.promotionType        = window.Promotion__r.Promotion_Type__c;
        result.timeWindow           = window.Start_Date_End_Date__c;
        result.promotionRecordType  = window.Promotion__r.RecordType.DeveloperName;
        result.promotionTypeExt     = window.Promotion__r.Promotion_Type_for_External__c;
        //Change by Zoe for French Portal 20240913
        Schema.DescribeFieldResult picklistField = Promotion2__c.Promotion_Type_for_External__c.getDescribe();
        List<Schema.PicklistEntry> picklistValues = picklistField.getPicklistValues();

        for(Schema.PicklistEntry entry : picklistValues) {
            if(entry.getValue() ==result.promotionTypeExt) {
                result.promotionTypeExtFR     = entry.getLabel();
            }
        }

        if(today > window.End_Date__c && today <= window.Claim_End_Date__c) {
            result.isClaim = true;
        }
        if ( window.Announcement_Date__c <= today && today < window.Start_Date__c ) {
            //soon
            result.isComingSoon = true;
        }
        result.photoUrlList = new List<String>();
        for(Id cvId : cvIds) {
            result.photoUrlList.add(Label.Salesforce_Base_URL + CCM_PromotionUtil.IMAGE_BASE_URL + cvId);
        }

        result.attachmentUrlList = new List<String>();
        for(Promotion_Photo__c attachment : attachmentList) {
            result.attachmentUrlList.add(Label.Salesforce_Base_URL + CCM_PromotionUtil.IMAGE_BASE_URL + attachment.ContentVersionId__c);
        }

        //promotion -> rules -> offering
        List<Promotion_Rule__c> ruleList = [SELECT Id FROM Promotion_Rule__c WHERE Promotion__c = :promotionId ];
        Set<Id> ruleIds = new Set<Id>();
        for(Promotion_Rule__c rule : ruleList) {
            ruleIds.add(rule.Id);
        }
        List<Promotion_Offering__c> offeringList =[SELECT Id, RecordType.DeveloperName FROM Promotion_Offering__c WHERE Promotion_Rule__c IN :ruleIds ];
        String offeringType = '';
        if( offeringList.size() > 0 ) {
            offeringType = offeringList[0].RecordType.DeveloperName;
        }

        Boolean isSameProduct = false;
        //For specific free good(same as promo product), add one more filed "net down price"
        if(recordType == CCM_PromotionUtil.PROMOTION_SELLIN_RECORDTYPE_APINAME
            && offeringType == 'Specific_Free_Good'
            && CCM_PromotionUtil.checkSameProduct(promotionId)){
            isSameProduct = true;
        }

        //Get displayed fields and headers for promotion price list
        PromoOfferHeaders__mdt finalConfig = null;

        String headerName = '';
        if (recordType == 'Sell_In_Promotion') {
            headerName = 'Sell_In_Promo_Detail';
        } else {
            headerName = 'Sell_Through_Promo_Detail';
        }
        // add haibo: french
        List<PromoOfferHeaders__mdt> headersConfig = [SELECT Fields__c, Headers__c, Headers_French__c FROM PromoOfferHeaders__mdt WHERE DeveloperName = :headerName ];

        if (headersConfig.size() > 0) {
            finalConfig = headersConfig[0];
        }
        if(finalConfig == null ) {
            return result;
        }
        //Example: String header = 'model;description;msrp;pricing;amountOff;promoInvoice';
        String header = finalConfig.Fields__c;
        if(isSameProduct) {
            header += ';netDownPrice';
        }

        //Example: String headerLabel = 'Model;Description;MSRP;Pricing;Amount Off;Promo Invoice';
        String headerLabel = '';
        if(UserInfo.getLanguage() == Label.CCM_Portal_French){
            headerLabel = finalConfig.Headers_French__c;
        }else {
            headerLabel = finalConfig.Headers__c;
        }
        if(isSameProduct) {
            // add haibo: french
            headerLabel += ';' + Label.CCM_Portal_NetDownPrice;
        }

        //Get all content for price list
        //fields and headers are used by front end to choose which field to display
        result.priceList = new List<CCM_PromotionUtil.PriceListWrapper>();
        result.priceListOffering = new List<CCM_PromotionUtil.PriceListWrapper>();

        Set<String> buyModelNumberSet = new Set<String>();
        Set<String> freeGoodsModelNumberSet = new Set<String>();
        Boolean hasPMAPP = false;
        for(Promotion_Performance__c priceListOne : priceList) {
            CCM_PromotionUtil.PriceListWrapper onePriceListWrapper = CCM_PromotionUtil.generatePriceListWrapper(priceListOne);
            if (onePriceListWrapper.pmapp != null && onePriceListWrapper.pmapp != '') {
                if (Decimal.valueOf(onePriceListWrapper.pmapp) > 0) {
                    hasPMAPP = true;
                }
            }
            if (onePriceListWrapper.modelType == 'Buy') {
                if (!buyModelNumberSet.contains(priceListOne.Model_Number__c) && authPriceBookIds.contains(priceListOne.PriceList__c)) {
                    result.priceList.add(onePriceListWrapper);
                    buyModelNumberSet.add(priceListOne.Model_Number__c);
                }
            } else if (onePriceListWrapper.modelType == 'Free Goods' && authPriceBookIds.contains(priceListOne.PriceList__c)) {
                if (!freeGoodsModelNumberSet.contains(priceListOne.Model_Number__c)) {
                    result.priceListOffering.add(onePriceListWrapper);
                    freeGoodsModelNumberSet.add(priceListOne.Model_Number__c);
                }
            } else {
                if (!buyModelNumberSet.contains(priceListOne.Model_Number__c)) {
                    result.priceList.add(onePriceListWrapper);
                    buyModelNumberSet.add(priceListOne.Model_Number__c);
                }
                if (!freeGoodsModelNumberSet.contains(priceListOne.Model_Number__c)) {
                    result.priceListOffering.add(onePriceListWrapper);
                    freeGoodsModelNumberSet.add(priceListOne.Model_Number__c);
                }
            }
        }
        // validate if need show pmapp
        if (recordType == 'Sell_In_Promotion' && hasPMAPP) {
            header = header.replace(';msrp;', ';msrp;pmapp;');
            headerLabel = headerLabel.replace(';MSRP;', ';MSRP;PMAPP;');
        }
        // offering price list header
        result.priceListHeaderOffering = new List<String>(header.split(';'));
        result.priceListHeaderLabelOffering = new List<String>(headerLabel.split(';'));

        // threshold price list header
        if (window.Promotion__r.Promotion_Type__c == 'Full Pallet Promo') {
            header = header.replace(';description;', ';description;palletQty;');
            headerLabel = headerLabel.replace(';Description;', ';Description;' + Label.CCM_Portal_PalletQty + ';');
        }

        result.priceListHeader = new List<String>(header.split(';'));
        result.priceListHeaderLabel = new List<String>(headerLabel.split(';'));

        return result;
    }

    /**
     * @description Get current customer id
     */
    private static Id getCustomerId() {
        Id userId = UserInfo.getUserId();
        List<User> userList = [ SELECT ContactId FROM User WHERE Id = :userId ];
        String contactId = userList[0].ContactId;
        List<Contact> contactList = [ SELECT AccountId FROM Contact WHERE Id = :contactId ];
        if (Test.isRunningTest()) {
            return [SELECT id FROM Account limit 1].id;
        }
        if(contactList.size()>0) {
            return contactList[0].AccountId;
        } else {
            return null;
        }
    }

    /**
     * @description Get current customer id
     * 2023-01-17 add by jet
     * CNA
     */
    private static Contact getCustomerIdCNA() {
        Id userId = UserInfo.getUserId();
        List<User> userList = [ SELECT ContactId FROM User WHERE Id = :userId ];
        String contactId = userList[0].ContactId;
        List<Contact> contactList = [ SELECT AccountId, Account.ORG_Code__c FROM Contact WHERE Id = :contactId ];
        if (Test.isRunningTest()) {
            return [ SELECT AccountId, Account.ORG_Code__c FROM Contact limit 1];
        }
        if(contactList.size()>0) {
            return contactList[0];
        } else {
            return null;
        }
    }

    /**
     * @description Get promotions ids by customer
     */
    private static Set<Id> getPromotionByCustomers(String recordType, Set<Id> customerIds) {
        // 参数中的customerIds可能来自查询，这种集合是只读的，需要重新创建一个新的实例。
        customerIds = new Set<Id>(customerIds);
        Set<Id> result = new Set<Id>();
        Set<String> recordTypes = new Set<String>();
        if (recordType == '') {
            recordTypes.add(CCM_PromotionUtil.PROMOTION_SELLIN_RECORDTYPE_APINAME);
            recordTypes.add(CCM_PromotionUtil.PROMOTION_SELLTHROUGH_RECORDTYPE_APINAME);
        } else {
            recordTypes.add(recordType);
        }
        AggregateResult[] groupedResults = [SELECT Promotion__c
                                            FROM Promotion_Target_Customer__c
                                            WHERE (Customer__c IN :customerIds OR Top_Customer__c IN: customerIds)
                                            AND Promotion__r.RecordType.DeveloperName IN :recordTypes AND Promotion__r.Promotion_Status__c != 'Closed' GROUP BY Promotion__c];
        for (AggregateResult ar : groupedResults)  {
            result.add((Id)ar.get('Promotion__c'));
        }
        return result;
    }

    /**
     * @description Get default home page for announcement, by default SELL IN
     */
    @AuraEnabled
    public static CategoriedPromotionsWrapper getDefaultAll() {
        return getAllPromotions(CCM_PromotionUtil.PROMOTION_SELLIN_RECORDTYPE_APINAME);
    }

    /**
     * @description Get home page content for announcement by SELL IN OR SELL THROUGH
     */
    @AuraEnabled
    public static CategoriedPromotionsWrapper getAllPromotions(String recordType) {
        Id customerId = getCustomerId();
        Account customer = [SELECT Id, ORG_Code__c FROM Account WHERE Id=:customerId];
        CategoriedPromotionsWrapper result = getAllPromotionsByCustomers(recordType, new Set<Id> {customerId});
        result.isCCA = customer.ORG_Code__c == 'CCA';
        return result;
    }

    /**
     * @description Get default home page for announcement, by default SELL IN
     */
    @AuraEnabled
    public static CategoriedPromotionsWrapper getDefaultAllForSalesCloud() {
        return getAllPromotionsForSalesCloud(CCM_PromotionUtil.PROMOTION_SELLIN_RECORDTYPE_APINAME);
    }

    /**
     * @description Get home page content for announcement by SELL IN OR SELL THROUGH
     */
    @AuraEnabled
    public static CategoriedPromotionsWrapper getAllPromotionsForSalesCloud(String recordType) {
        Set<Id> customerIds = CCM_PromotionUtil.getCustomer();
        CategoriedPromotionsWrapper result = getAllPromotionsByCustomers(recordType, customerIds);
        return result;
    }

    /**
     * @descprition: get all promotions by recordtype and customer
     */
    private static CategoriedPromotionsWrapper getAllPromotionsByCustomers(String recordType, Set<Id> customerIds) {
        CategoriedPromotionsWrapper result = new CategoriedPromotionsWrapper();
        result.type = recordType;
        System.debug('===recordType==='+recordType);
        Set<Id> customerCanUsedPromotions = getPromotionByCustomers(recordType, customerIds);
        System.debug('===customerCanUsedPromotions==='+customerCanUsedPromotions);
        //Get All promotions, not including closed and invisible for current customer
        Date todayDate = Date.today();
        List<Promotion_Window__c> qualifiedWindows = new List<Promotion_Window__c>();
        if (CCM_PromotionUtil.PROMOTION_SELLIN_RECORDTYPE_APINAME == recordType) {
            qualifiedWindows = [SELECT Id, Promotion__c, Start_Date__c, End_Date__c, Announcement_Date__c, Claim_End_Date__c, Promotion__r.RecordType.DeveloperName
                                                        FROM Promotion_Window__c
                                                        WHERE Promotion_Window_Status2__c IN ('Published','In Progress')
                                                        AND Availability_in_Dealer_Portal__c ='Available in Portal'
                                                        AND Promotion__r.RecordType.DeveloperName = :recordType
                                                        AND Promotion__c IN :customerCanUsedPromotions
                                                        ORDER BY End_Date__c ASC ];
        } else if (CCM_PromotionUtil.PROMOTION_SELLTHROUGH_RECORDTYPE_APINAME == recordType) {
            qualifiedWindows = [SELECT Id, Promotion__c, Start_Date__c, End_Date__c, Announcement_Date__c, Claim_End_Date__c, Promotion__r.RecordType.DeveloperName
                                                        FROM Promotion_Window__c
                                                        WHERE Promotion_Window_Status2__c IN ('Published', 'In Progress', 'Claim Period')
                                                        AND Availability_in_Dealer_Portal__c = 'Available in Portal'
                                                        AND Promotion__r.RecordType.DeveloperName = :recordType
                                                        AND Promotion__c IN :customerCanUsedPromotions
                                                        ORDER BY End_Date__c ASC ];
        }
        Set<Id> promotionIds = new Set<Id>();
        for(Promotion_Window__c window : qualifiedWindows){
            promotionIds.add(window.Promotion__c);
        }

        //Get all photo belong to promotinos
        List<Promotion_Photo__c> photoList = [SELECT Id, Promotion__c, Photo_Category__c, ContentVersionId__c, SEQUENCE__C FROM Promotion_Photo__c WHERE Promotion__c IN :promotionIds ORDER BY Promotion__c, Photo_Category__c, SEQUENCE__C ASC ];
        Set<Id> cvIds = new Set<Id>();
        //Promotion -> banner/thumbnail -> photo content version
        Map<Id, Map<String, Id>> promotionPhotoMap = new Map<Id, Map<String, Id>>();
        for(Promotion_Photo__c photo : photoList){
            if(!promotionPhotoMap.containsKey(photo.Promotion__c)){
                Map<String, Id> tmpMap = new Map<String, Id>();
                promotionPhotoMap.put(photo.Promotion__c, tmpMap);
            }
            try{
                if(!promotionPhotoMap.get(photo.Promotion__c).containsKey(photo.Photo_Category__c)) {
                    promotionPhotoMap.get(photo.Promotion__c).put(photo.Photo_Category__c, photo.ContentVersionId__c);
                    cvIds.add(photo.ContentVersionId__c);
                }
            } catch(System.StringException se){
                //photo id is not correct
            }
        }
        for (Id promotionId : promotionIds) {
            if (!promotionPhotoMap.containsKey(promotionId)) {
                promotionPhotoMap.put(promotionId, new Map<String, Id>());
            }
        }

        //Get all photos belong to Future Panel
        //It will be used to as a limitation
        String bannerImage = BANNER_IMAGE;
        List<Promotion_Photo__c> futurePanelPhotoList = [SELECT Id, Promotion__c, Photo_Category__c, ContentVersionId__c, SEQUENCE__C, Location__c FROM Promotion_Photo__c WHERE Promotion__c IN :promotionIds AND Photo_Category__c = :bannerImage AND Location__c INCLUDES ('Future Promotion') ORDER BY Promotion__c, Photo_Category__c, SEQUENCE__C ASC ];
        //<PromotionId, ContentVersionId>
        Map<Id, Id> futurePanelPhotoMap = new Map<Id,Id>();
        for(Promotion_Photo__c photo : futurePanelPhotoList){
            try{
                if(!futurePanelPhotoMap.containsKey(photo.Promotion__c)){
                    futurePanelPhotoMap.put(photo.Promotion__c, photo.ContentVersionId__c);
                }
            } catch(System.StringException se){
                //photo id not correct
            }
        }

        //Get Promotion Basic Info
        // add haibo: promotion (french) 翻译
        Map<Id, Promotion2__c> promotionMap = new Map<Id, Promotion2__c>([SELECT Id, Name, Promotion_Name_French__c, Promo_Code__c, Promotion_Code_For_External__c, Brands__c, Photo_Offer_Name__c, Promotion_Type_for_External__c FROM Promotion2__c WHERE Id IN :promotionIds ]);

        List<Promotion_Rule__c> ruleList = [SELECT Id, Promotion__c FROM Promotion_Rule__c WHERE Promotion__c IN :promotionIds ];
        Set<Id> ruleIds = new Set<Id>();
        //<RuleId, PromotionId>
        Map<Id, Id> promotionRuleMap = new Map<Id, Id>();
        for(Promotion_Rule__c rule : ruleList) {
            ruleIds.add(rule.Id);
            promotionRuleMap.put(rule.Id, rule.Promotion__c);
        }

        List<Promotion_Product__c> productList = [ SELECT Product__r.Id, Product__r.Name, Product__r.ProductCode, Promotion2_Threshold__r.Promotion_Rule__c  FROM Promotion_Product__c WHERE Promotion2_Threshold__r.Promotion_Rule__c IN :ruleIds ];
        //<promotionId, List<Product>>
        Map<Id, List<Promotion_Product__c>> promotionProductMap = new Map<Id, List<Promotion_Product__c>>();
        for(Promotion_Product__c product : productList ){
            Id promotionId = promotionRuleMap.get(product.Promotion2_Threshold__r.Promotion_Rule__c);
            if(!promotionProductMap.containsKey(promotionId)) {
                List<Promotion_Product__c> tmpList = new List<Promotion_Product__c>();
                promotionProductMap.put(promotionId, tmpList);
            }
            promotionProductMap.get(promotionId).add(product);
        }

        for(Promotion_Window__c window : qualifiedWindows){
            //Start_Date__c< :todayDate AND End_Date__c > :todayDate
            Date startDate = window.Start_Date__c;
            Date endDate = window.End_Date__c;
            Date announceDate = window.Announcement_Date__c;
            Promotion2__c promotionOne = promotionMap.get(window.Promotion__c);
            if( (startDate <= todayDate && todayDate <= endDate && window.Promotion__r.RecordType.DeveloperName == CCM_PromotionUtil.PROMOTION_SELLIN_RECORDTYPE_APINAME) ||
                (startDate <= todayDate && todayDate <= window.Claim_End_Date__c && window.Promotion__r.RecordType.DeveloperName == CCM_PromotionUtil.PROMOTION_SELLTHROUGH_RECORDTYPE_APINAME) ){
                //Future Panel
                PromotionWrapper wrapperBig = generateFuturePanelPhotoWrapper(promotionOne, window, futurePanelPhotoMap, promotionPhotoMap, BANNER_IMAGE, promotionProductMap);
                System.debug('===promotionOne==='+promotionOne);
                System.debug('===window==='+window);
                System.debug('===futurePanelPhotoMap==='+futurePanelPhotoMap);
                System.debug('===promotionPhotoMap==='+promotionPhotoMap);
                System.debug('===promotionProductMap==='+promotionProductMap);

                if(wrapperBig !=null)  {
                    result.bigSalesList.add(wrapperBig);
                }
                if(promotionOne.Brands__c.contains(CCM_PromotionUtil.BRAND_EGO)){
                    //EGO
                    PromotionWrapper wrapper = generatePhotoWrapper(promotionOne, window, futurePanelPhotoMap, promotionPhotoMap, THUMBNAIL_IMAGE, promotionProductMap);
                    result.egoList.add(wrapper);
                }
                if(promotionOne.Brands__c.contains(CCM_PromotionUtil.BRAND_SKIL)){
                    //SKIL/SKILSAW
                    PromotionWrapper wrapper = generatePhotoWrapper(promotionOne, window, futurePanelPhotoMap, promotionPhotoMap, THUMBNAIL_IMAGE, promotionProductMap);
                    result.skilList.add(wrapper);
                }
                if(promotionOne.Brands__c.contains(CCM_PromotionUtil.BRAND_FLEX)){
                    //FLEX
                    PromotionWrapper wrapper = generatePhotoWrapper(promotionOne, window, futurePanelPhotoMap, promotionPhotoMap, THUMBNAIL_IMAGE, promotionProductMap);
                    result.flexList.add(wrapper);
                }
            } else if( announceDate <= todayDate && todayDate < startDate ) {
                //soon
                //PromotionWrapper wrapper = generatePhotoWrapper(promotionOne,window, futurePanelPhotoMap, promotionPhotoMap, BANNER_IMAGE, promotionProductMap);
                PromotionWrapper wrapper = generatePhotoWrapper(promotionOne,window, futurePanelPhotoMap, promotionPhotoMap, THUMBNAIL_IMAGE, promotionProductMap);
                result.soonList.add(wrapper);
            }

        }

        // get first five image
        if (result.bigSalesList.size() > 5) {
            List<PromotionWrapper> newbigSalesList = new List<PromotionWrapper>();
            newbigSalesList.add(result.bigSalesList.get(0));
            newbigSalesList.add(result.bigSalesList.get(1));
            newbigSalesList.add(result.bigSalesList.get(2));
            newbigSalesList.add(result.bigSalesList.get(3));
            newbigSalesList.add(result.bigSalesList.get(4));
            result.bigSalesList = newbigSalesList;
        }
        // only display first five image in portal
        if (result.soonList.size() != 0 ) {
            List<PromotionWrapper> newsoonList = new List<PromotionWrapper>();
            for (Integer index = 0; index < result.soonList.size(); index++) {
                if (newsoonList.size() > 5) break;
                if (result.soonList.get(index).bannerImgUrl != null && result.soonList.get(index).bannerImgUrl != '') {
                    newsoonList.add(result.soonList.get(index));
                }
            }
            result.soonList = newsoonList;
        }
        return result;
    }

    /**
     * @description: get all promotions by recordtype and brand
     */
    @AuraEnabled
    public static List<PromotionWrapper> getViewAll(String recordType, String brand) {
        Id customerId = getCustomerId();
        List<PromotionWrapper> result = getViewAllByCustomers(recordType, brand, new Set<Id> {customerId});

        return result;
    }

    /**
     * @description: get all promotions by recordtype and brand in salesforce
     */
    @AuraEnabled
    public static List<PromotionWrapper> getViewAllForSalesCloud(String recordType, String brand) {
        Set<Id> customerIds = CCM_PromotionUtil.getCustomer();
        List<PromotionWrapper> result = getViewAllByCustomers(recordType, brand, customerIds);
        return result;
    }

    /**
     * @description: get all promotions by recordtype, brand and customer
     */
    private static List<PromotionWrapper> getViewAllByCustomers(String recordType, String brand, Set<Id> customerIds) {
        List<PromotionWrapper> result = new List<PromotionWrapper>();
        if(String.isBlank(brand)) {
            return result;
        } else {
            brand = brand.toUpperCase();
            if(brand.contains(CCM_PromotionUtil.BRAND_SKIL)) {
                brand = CCM_PromotionUtil.BRAND_SKIL;
            }
        }

        // get promotions from promotion target customer
        Set<Id> customerCanUsedPromotions = getPromotionByCustomers(recordType, customerIds);

        Date todayDate = Date.today();

        //Get All promotions, not including closed and invisible for current customer
        List<Promotion_Window__c> qualifiedWindows = new List<Promotion_Window__c>();
        if (CCM_PromotionUtil.PROMOTION_SELLIN_RECORDTYPE_APINAME == recordType) {
            qualifiedWindows = [SELECT Id, Promotion__c, Start_Date__c, End_Date__c, Announcement_Date__c, Claim_End_Date__c, Promotion__r.RecordType.DeveloperName
                                                        FROM Promotion_Window__c
                                                        WHERE Promotion_Window_Status2__c IN ('Published','In Progress')
                                                        AND Availability_in_Dealer_Portal__c='Available in Portal'
                                                        AND Promotion__r.RecordType.DeveloperName = :recordType
                                                        AND Promotion__c IN :customerCanUsedPromotions
                                                        ORDER BY End_Date__c ASC
                                                    ];
        } else if (CCM_PromotionUtil.PROMOTION_SELLTHROUGH_RECORDTYPE_APINAME == recordType) {
            qualifiedWindows = [SELECT Id, Promotion__c, Start_Date__c, End_Date__c, Announcement_Date__c, Claim_End_Date__c, Promotion__r.RecordType.DeveloperName
                                                        FROM Promotion_Window__c
                                                        WHERE Promotion_Window_Status2__c IN ('Published', 'In Progress', 'Claim Period')
                                                        AND Availability_in_Dealer_Portal__c = 'Available in Portal'
                                                        AND Promotion__r.RecordType.DeveloperName = :recordType
                                                        AND Promotion__c IN :customerCanUsedPromotions
                                                        ORDER BY End_Date__c ASC ];
        }
        Set<Id> promotionIds = new Set<Id>();
        for(Promotion_Window__c window : qualifiedWindows){
            promotionIds.add(window.Promotion__c);
        }

        String thumbnail = THUMBNAIL_IMAGE;
        //List<Promotion_Photo__c> photoList = [SELECT Id, Promotion__c, Photo_Category__c, ContentVersionId__c, SEQUENCE__C FROM Promotion_Photo__c WHERE Promotion__c IN :promotionIds AND Photo_Category__c = :thumbnail ORDER BY Promotion__c, Photo_Category__c, SEQUENCE__C ASC ];
        // get all image
        List<Promotion_Photo__c> photoList = [SELECT Id, Promotion__c, Photo_Category__c, ContentVersionId__c, SEQUENCE__C FROM Promotion_Photo__c WHERE Promotion__c IN :promotionIds ORDER BY Promotion__c, Photo_Category__c, SEQUENCE__C ASC ];

        Set<Id> cvIds = new Set<Id>();
        //<PromotionId, <photo category, ContentVersionId>>
        Map<Id, Map<String, Id>> promotionPhotoMap = new Map<Id, Map<String, Id>>();
        for(Promotion_Photo__c photo : photoList){
            if(!promotionPhotoMap.containsKey(photo.Promotion__c)){
                Map<String, Id> tmpMap = new Map<String, Id>();
                promotionPhotoMap.put(photo.Promotion__c, tmpMap);
            }
            try{
                if(!promotionPhotoMap.get(photo.Promotion__c).containsKey(photo.Photo_Category__c)) {
                    promotionPhotoMap.get(photo.Promotion__c).put(photo.Photo_Category__c, photo.ContentVersionId__c);
                    cvIds.add(photo.ContentVersionId__c);
                }
            }catch(System.StringException se) {
                //photo id not correct
            }
        }
        for (Id promotionId : promotionIds) {
            if (!promotionPhotoMap.containsKey(promotionId)) {
                promotionPhotoMap.put(promotionId, new Map<String, Id>());
            }
        }


        //Map<Id, ContentVersion> cvMap = new Map<Id, ContentVersion>( [SELECT Id, ContentDocumentId, Title, FileExtension FROM ContentVersion Where Id IN :cvIds ] );
        // get promotion
        // add haibo: promotion (french) 翻译
        Map<Id, Promotion2__c> promotionMap = new Map<Id, Promotion2__c>([SELECT Id, Name, Promotion_Name_French__c, Promo_Code__c, Promotion_Code_for_External__c, Promotion_Type_for_External__c, Brands__c, Photo_Offer_Name__c  FROM Promotion2__c WHERE Id IN :promotionIds ]);
        // get all promotion rules by promotions
        List<Promotion_Rule__c> ruleList = [SELECT Id, Promotion__c FROM Promotion_Rule__c WHERE Promotion__c IN :promotionIds ];
        Set<Id> ruleIds = new Set<Id>();
        //<RuleId, PromotionId>
        Map<Id, Id> promotionRuleMap = new Map<Id, Id>();
        for(Promotion_Rule__c rule : ruleList) {
            ruleIds.add(rule.Id);
            promotionRuleMap.put(rule.Id, rule.Promotion__c);
        }

        // get threshold product
        List<Promotion_Product__c> productList = [ SELECT Product__r.Id, Product__r.Name, Product__r.ProductCode, Promotion2_Threshold__r.Promotion_Rule__c  FROM Promotion_Product__c WHERE Promotion2_Threshold__r.Promotion_Rule__c IN :ruleIds ];
        //<promotionId, List<Product>>
        Map<Id, List<Promotion_Product__c>> promotionProductMap = new Map<Id, List<Promotion_Product__c>>();
        for(Promotion_Product__c product : productList ){
            Id promotionId = promotionRuleMap.get(product.Promotion2_Threshold__r.Promotion_Rule__c);
            if(!promotionProductMap.containsKey(promotionId)) {
                List<Promotion_Product__c> tmpList = new List<Promotion_Product__c>();
                promotionProductMap.put(promotionId, tmpList);
            }
            promotionProductMap.get(promotionId).add(product);
        }

        for(Promotion_Window__c window : qualifiedWindows){
            //Start_Date__c< :todayDate AND End_Date__c > :todayDate
            Date startDate = window.Start_Date__c;
            Date endDate = window.End_Date__c;
            Date announceDate = window.Announcement_Date__c;
            Promotion2__c promotionOne = promotionMap.get(window.Promotion__c);
            if( (startDate <= todayDate && todayDate <= endDate && window.Promotion__r.RecordType.DeveloperName == CCM_PromotionUtil.PROMOTION_SELLIN_RECORDTYPE_APINAME) ||
                (startDate <= todayDate && todayDate <= window.Claim_End_Date__c && window.Promotion__r.RecordType.DeveloperName == CCM_PromotionUtil.PROMOTION_SELLTHROUGH_RECORDTYPE_APINAME)){
                if(promotionOne.Brands__c.contains(brand)){
                    PromotionWrapper wrapper = generatePhotoWrapper(promotionOne, window, null, promotionPhotoMap, THUMBNAIL_IMAGE, promotionProductMap);
                    if(wrapper != null) {
                        result.add(wrapper);
                    }
                }
            }
        }
        return result;
    }

    /**
     * @description: generate promotionWrapper
     */
    private static PromotionWrapper generateFuturePanelPhotoWrapper(Promotion2__c promotionOne,
                                                                    Promotion_Window__c window,
                                                                    Map<Id, Id> futurePanelPhotoMap,
                                                                    Map<Id, Map<String, Id>> promotionPhotoMap,
                                                                    String category,
                                                                    Map<Id, List<Promotion_Product__c>> promotionProductMap ) {
        PromotionWrapper wrapper = new PromotionWrapper();
        wrapper.promotionId = promotionOne.Id;
        wrapper.promotionWindowId = window.Id;
        // add haibo: 添加 promotion (French)法语翻译字段
        if (UserInfo.getLanguage() == Label.CCM_Portal_French) {
            wrapper.promotionName = promotionOne.Promotion_Name_French__c;
        } else {
            wrapper.promotionName = promotionOne.Name;
        }
        wrapper.promotionCode = promotionOne.Promotion_Code_For_External__c;
        wrapper.expireDays = 0 ;
        wrapper.promotionTypeExt = promotionOne.Promotion_Type_for_External__c;
        wrapper.offeringName = promotionOne.Photo_Offer_Name__c;
        Date today = Date.today();
        if(today > window.End_Date__c && today <= window.Claim_End_Date__c) {
            wrapper.isClaim = true;
            wrapper.claimExpireDays = today.daysBetween(window.Claim_End_Date__c);
        }
        if(window.End_Date__c !=null ) {
            Integer days = today.daysBetween(window.End_Date__c);
            wrapper.expireDays = days;
        }

        // generate banner image url
        if(futurePanelPhotoMap.containsKey(promotionOne.Id) ){
            String cvId = futurePanelPhotoMap.get(promotionOne.Id);
            String url = '';
            if (String.isNotEmpty(cvId)) {
                url = Label.Salesforce_Base_URL + CCM_PromotionUtil.IMAGE_BASE_URL + cvId;
            }

            //wrapper.photoUrl = url;
            wrapper.bannerImgUrl = url;
            wrapper.cvId = cvId;
        } else {
            return null;//no suitable images
        }

        // generate photo image url
        if(promotionPhotoMap.containsKey(promotionOne.Id) ){
            String cvId = promotionPhotoMap.get(wrapper.promotionId).get(THUMBNAIL_IMAGE);
            String url = '';
            if (String.isNotEmpty(cvId)) {
                url = Label.Salesforce_Base_URL + CCM_PromotionUtil.IMAGE_BASE_URL + cvId;
            }
            wrapper.photoUrl = url;
            wrapper.cvId = cvId;
        }
        wrapper.productList = new List<ProductWrapper>();
        List<Promotion_Product__c> productList = promotionProductMap.get(wrapper.promotionId);
        if(productList!=null) {
            for(Promotion_Product__c product : productList) {
                ProductWrapper oneProduct =  new ProductWrapper();
                oneProduct.productId = product.Product__r.Id;
                oneProduct.productName = product.Product__r.Name;
                oneProduct.productCode = product.Product__r.ProductCode;
                wrapper.productList.add(oneProduct);
            }
        }
        return wrapper;
    }

    /**
     * @description generate promotionWrapper with banner image url and photo image url
     */
    private static PromotionWrapper generatePhotoWrapper(Promotion2__c promotionOne,
                                                        Promotion_Window__c window,
                                                        Map<Id, Id> futurePanelPhotoMap,
                                                        Map<Id, Map<String, Id>> promotionPhotoMap,
                                                        //Map<Id, ContentVersion> cvMap,
                                                        String category,
                                                        Map<Id, List<Promotion_Product__c>> promotionProductMap ) {
        PromotionWrapper wrapper = new PromotionWrapper();
        wrapper.promotionId = promotionOne.Id;
        wrapper.promotionWindowId = window.Id;
        // add haibo: 添加 promotion (French)法语翻译字段
        if (UserInfo.getLanguage() == Label.CCM_Portal_French) {
            wrapper.promotionName = promotionOne.Promotion_Name_French__c;
        } else {
            wrapper.promotionName = promotionOne.Name;
        }
        wrapper.promotionCode = promotionOne.Promotion_Code_For_External__c;
        wrapper.offeringName = promotionOne.Photo_Offer_Name__c;
        wrapper.expireDays = 0 ;
        wrapper.promotionTypeExt = promotionOne.Promotion_Type_for_External__c;
        Date today = Date.today();
        if(today > window.End_Date__c && today <= window.Claim_End_Date__c) {
            wrapper.isClaim = true;
            wrapper.claimExpireDays = today.daysBetween(window.Claim_End_Date__c);
        }
        if(window.End_Date__c !=null ) {
            Integer days = today.daysBetween(window.End_Date__c);
            wrapper.expireDays = days;
        }

        // generate photo and banner image url
        if(promotionPhotoMap.containsKey(promotionOne.Id) ){
            String cvId = promotionPhotoMap.get(wrapper.promotionId).get(BANNER_IMAGE);
            String url = '';
            if (String.isNotEmpty(cvId)) {
                url = Label.Salesforce_Base_URL + CCM_PromotionUtil.IMAGE_BASE_URL + cvId;
            }
            wrapper.bannerImgUrl = url;
            cvId = promotionPhotoMap.get(wrapper.promotionId).get(category);
            url = '';
            if (String.isNotEmpty(cvId)) {
                url = Label.Salesforce_Base_URL + CCM_PromotionUtil.IMAGE_BASE_URL + cvId;
            }
            wrapper.photoUrl = url;
            wrapper.cvId = cvId;
        } else {
            return null;//no suitable images
        }
        wrapper.productList = new List<ProductWrapper>();
        List<Promotion_Product__c> productList = promotionProductMap.get(wrapper.promotionId);
        if(productList!=null) {
            for(Promotion_Product__c product : productList) {
                ProductWrapper oneProduct =  new ProductWrapper();
                oneProduct.productId = product.Product__r.Id;
                oneProduct.productName = product.Product__r.Name;
                oneProduct.productCode = product.Product__r.ProductCode;
                wrapper.productList.add(oneProduct);
            }
        }
        return wrapper;
    }


    /**
     * @description: get banner image for home page in portal
     */
    @AuraEnabled
    public static List<PromotionWrapper> getAdvertisingBanners() {
        Id customerId = getCustomerId();
        Set<Id> customerCanUsedPromotions = getPromotionByCustomers('', new Set<Id> {customerId});

        //Get All promotions, not including closed and invisible for current customer
        List<PromotionWrapper> result = new List<PromotionWrapper>();
        Date todayDate = Date.today();
        List<Promotion_Window__c> qualifiedWindows= [ SELECT Id, Promotion__c
                                                        FROM Promotion_Window__c
                                                        WHERE Start_Date__c<= :todayDate
                                                        AND End_Date__c >= :todayDate
                                                        AND Promotion_Window_Status2__c IN ('Published','In Progress')
                                                        AND Availability_in_Dealer_Portal__c='Available in Portal'
                                                        AND Promotion__c IN :customerCanUsedPromotions
                                                        AND Promotion__r.RecordTypeId = :CCM_PromotionUtil.PROMOTION_SELLIN_RECORDTYPE_ID
                                                        ORDER BY End_Date__c ASC
                                                    ];
        Set<Id> promotionIds = new Set<Id>();
        Map<Id, Id> promotionWindowMap = new Map<Id, Id>();
        for(Promotion_Window__c window : qualifiedWindows){
            promotionIds.add(window.Promotion__c);
            promotionWindowMap.put(window.Promotion__c, window.Id);
        }

        String bannerImage = BANNER_IMAGE;
        //List<Promotion_Photo__c> photoList = [SELECT Id, Promotion__c, ContentVersionId__c, SEQUENCE__C FROM Promotion_Photo__c WHERE Promotion__c IN :promotionIds AND Photo_Category__c = :bannerImage AND Location__c INCLUDES ('Home Page')  ORDER BY Promotion__c, SEQUENCE__C ASC ];
        List<Promotion_Photo__c> photoList = [SELECT Id, Promotion__c, ContentVersionId__c, SEQUENCE__C FROM Promotion_Photo__c WHERE Promotion__c IN :promotionIds AND Photo_Category__c = :bannerImage AND Location__c INCLUDES ('Home Page')  ORDER BY CreatedDate DESC];
        Set<Id> cvIds = new Set<Id>();
        //<PromotionId, ContentVersionId>
        Map<Id, Id> promotionPhotoMap = new Map<Id, Id>();
        for(Promotion_Photo__c photo : photoList){
            try{
                cvIds.add(photo.ContentVersionId__c);
                if(!promotionPhotoMap.containsKey(photo.Promotion__c)){
                    promotionPhotoMap.put(photo.Promotion__c, photo.ContentVersionId__c);
                }
            } catch( System.StringException se) {
                //photo id not correct
            }
            System.debug(promotionPhotoMap);
        }
        // add haibo: promotion (french) 翻译
        Map<Id, Promotion2__c> promotionMap = new Map<Id, Promotion2__c> ([SELECT Id, Name, Promotion_Name_French__c, Promo_Code__c, Promotion_Code_For_External__c, Photo_Offer_Name__c  FROM Promotion2__c WHERE Id IN :promotionIds ]);

        // in portal home page, banner image sort by created date
        for (Id promotionId : promotionPhotoMap.keySet()) {
            PromotionWrapper wrapper = new PromotionWrapper();
            wrapper.promotionId = promotionMap.get(promotionId)?.Id;
            wrapper.promotionWindowId = promotionWindowMap.get(wrapper.promotionId);
            wrapper.promotionName = promotionMap.get(promotionId)?.Name;
            wrapper.promotionCode = promotionMap.get(promotionId)?.Promotion_Code_For_External__c;
            wrapper.offeringName = promotionMap.get(promotionId)?.Photo_Offer_Name__c;

            String cvId = promotionPhotoMap.get(promotionId);
            String url = Label.Salesforce_Base_URL + CCM_PromotionUtil.IMAGE_BASE_URL + cvId;
            wrapper.photoUrl = url;
            wrapper.cvId = cvId;

            if (result.size() > 3) {break;}
            result.add(wrapper);
        }

        return result;
    }

    /**
     * @description Wrapper Class for Promotion2__c
     */
    public class PromotionDetailWrapper {
        @AuraEnabled
        public String promotionId;

        @AuraEnabled
        public String promotionWindowId;

        @AuraEnabled
        public String promotionName;

        // add haibo: promotion (french) 翻译
        @AuraEnabled
        public String promotionNameFrench;

        @AuraEnabled
        public String promotionCode;

        @AuraEnabled
        public String promotionRecordType;

        @AuraEnabled
        public String promotionDetail;

        // add haibo: promotion (french) 翻译
        @AuraEnabled
        public String promotionDetailFrench;

        @AuraEnabled
        public String promotionType;

        @AuraEnabled
        public String promotionTypeExt;

        @AuraEnabled
        public String promotionTypeExtFR;

        @AuraEnabled
        public String timeWindow;

        @AuraEnabled
        public String offerringType;

        @AuraEnabled
        public Boolean isClaim;

        @AuraEnabled
        public Boolean isComingSoon;

        @AuraEnabled
        public List<String> photoUrlList;

        @AuraEnabled
        public List<String> attachmentUrlList;

        @AuraEnabled
        public List<CCM_PromotionUtil.PriceListWrapper> priceList;

        @AuraEnabled
        public List<CCM_PromotionUtil.PriceListWrapper> priceListOffering;

        @AuraEnabled
        public List<String> priceListHeader;

        @AuraEnabled
        public List<String> priceListHeaderLabel;

        @AuraEnabled
        public List<String> priceListHeaderOffering;

        @AuraEnabled
        public List<String> priceListHeaderLabelOffering;
    }

    /**
     * @description Wrapper Class for Promotion2__c
     */
    public class ProductWrapper {
        @AuraEnabled
        public String productId;

        @AuraEnabled
        public String productName;

        @AuraEnabled
        public String productCode;
    }

    /**
     * @description Wrapper Class for Promotion2__c
     */
    public class PromotionWrapper {
        @AuraEnabled
        public String promotionId;

        @AuraEnabled
        public String promotionWindowId;

        @AuraEnabled
        public String promotionName;

        // add haibo: promotion (french) 翻译
        @AuraEnabled
        public String promotionNameFrench;

        @AuraEnabled
        public String promotionCode;

        @AuraEnabled
        public String photoUrl;

        @AuraEnabled
        public String bannerImgUrl;

        @AuraEnabled
        public Integer expireDays;

        @AuraEnabled
        public String cvId;

        @AuraEnabled
        public String offeringName;

        @AuraEnabled
        public Boolean isClaim;

        @AuraEnabled
        public Integer claimExpireDays;

        @AuraEnabled
        public String promotionTypeExt;

        // @AuraEnabled
        // public String offeringType;

        // @AuraEnabled
        // public String offeringNumber;

        @AuraEnabled
        public List<ProductWrapper> productList;
    }

    /**
     * @description Wrapper Class for Promotion2__c
     */
    public class CategoriedPromotionsWrapper {
        @AuraEnabled
        public String type;

        @AuraEnabled
        public List<PromotionWrapper> bigSalesList = new List<PromotionWrapper>();

        @AuraEnabled
        public List<PromotionWrapper> egoList = new List<PromotionWrapper>();

        @AuraEnabled
        public List<PromotionWrapper> skilList = new List<PromotionWrapper>();

        @AuraEnabled
        public List<PromotionWrapper> flexList = new List<PromotionWrapper>();

        @AuraEnabled
        public List<PromotionWrapper> soonList = new List<PromotionWrapper>();

        @AuraEnabled
        public Boolean isCCA;
    }

    /**
     * @description: wrapper class for pick list by recordtype
     */
    public class PromotionTypePickListWrapper {
        @AuraEnabled
        public String recordType;

        @AuraEnabled
        public List<CCM_PromotionUtil.PromotionPickWrapper>  picklistList = new List<CCM_PromotionUtil.PromotionPickWrapper>();
    }

    /**
     * @description: wrapper class for Brands
     */
    public class BrandsWrapper{
        @AuraEnabled
        public List<String> brandsList = new List<String>();
    }

}