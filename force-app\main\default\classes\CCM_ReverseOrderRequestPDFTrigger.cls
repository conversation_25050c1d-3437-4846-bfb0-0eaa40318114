/**
 * @Author: <PERSON><PERSON>
 * @Description: This trigger for sent reverse order return form to createdBy.email when reverse order be approved;
 */
public with sharing class CCM_ReverseOrderRequestPDFTrigger implements Triggers.Handler {
    public void handle(){
        List<Id> objClaimId = new List<Id>();
        List<Id> approvedClaimIds = new List<Id>();
        Set<Id> objClaimFinanceId = new Set<Id>();
        Map<Id, Reverse_Order_Request__c> oldwrp = (Map<Id, Reverse_Order_Request__c>) Trigger.oldMap;
        for(Reverse_Order_Request__c wrp : (List<Reverse_Order_Request__c>)Trigger.new){
            if (wrp.Approval_Status__c != oldwrp.get(wrp.Id).Approval_Status__c){
                if(wrp.Approval_Status__c == 'Approved'){
                    // objClaimId.add(wrp.Id);
                    approvedClaimIds.add(wrp.Id);
                }
            } 
        }

        Map<Id, Boolean> needSendRGAMap = needSendRGA(approvedClaimIds);
        for(Id reqId : approvedClaimIds) {
            if(needSendRGAMap.get(reqId)) {
                objClaimId.add(reqId);
            }
        }

        if (objClaimId.size() > 0){
            Id strSObjId = objClaimId[0];
            CCM_RofPdfGenerateCtrl.generateROFPdf(strSObjId);
        }
    }

    private Map<Id, Boolean> needSendRGA(List<Id> requests) {
        Map<Id, Boolean> needSendRGAMap = new Map<Id, Boolean>();
        List<Reverse_Order_Request__c> roReqs = [SELECT External_Return_Reason__c, 
                                                       (SELECT Next_Step_Action__c FROM Reverse_Order_Items__r) 
                                                FROM Reverse_Order_Request__c WHERE Id IN :requests];
        for(Reverse_Order_Request__c roReq : roReqs) {
            Boolean needSendRGA = true;
            if(CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_MORE_THAN_I_ORDERED == roReq.External_Return_Reason__c || CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_WRONG_PRODUCT == roReq.External_Return_Reason__c) {
                for(Reverse_Order_Item__c item : roReq.Reverse_Order_Items__r) {
                    if(CCM_ReverseOrderUtil.NEXT_STEP_ACTION_BUY_THE_OVERAGE_PRODUCT == item.Next_Step_Action__c) {
                        needSendRGA = false;
                        break;
                    }
                }
            }
            if(CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_LESS_THAN_I_ORDERED == roReq.External_Return_Reason__c) {
                needSendRGA = false;
            }
            needSendRGAMap.put(roReq.Id, needSendRGA);
        }
        return needSendRGAMap;
    }
}