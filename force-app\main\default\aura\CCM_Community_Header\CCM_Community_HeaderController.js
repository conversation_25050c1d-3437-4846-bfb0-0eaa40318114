/**
 * Created by gluo006 on 8/21/2019.
 */
({
    doInit: function(component, event, helper){
        // add haibo: french
        if ($A.get("$Label.c.CCM_Portal_Language") == 'fr') {
            component.set('v.language', 'fr');
        } else {
            component.set('v.language', 'en_US');
        }
        var userInfo = $A.get("$SObjectType.CurrentUser");
        if(userInfo.Id){
            var userId = userInfo.Id.substr(0,15);
            component.set('v.userId', userId);
        }

        var action = component.get('c.getUserInfo');
        action.setCallback(this, $A.getCallback(function(response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var ret = response.getReturnValue();
                var data = JSON.parse(ret);
                component.set('v.userName', data.userName);
                component.set('v.companyName', data.companyName);
                component.set('v.accountNum', data.accountNumber);
                // add haibo: french
                component.set('v.isCCA', data.isCCA);
                if (data.profileName.includes($A.get("$Label.c.CCM_Portal_Service"))) {
                    component.set("v.isSales", false);
                }
                if(data.profileName.includes('Sales')){
                    if(data.profileName.includes('Partner Community Sales') ||  data.profileName.includes('Partner Community Login Sales')){
                        if(data.isCoOpTargetCustomer){
                            component.set('v.navigateBars', [
                                {'href': '/s/', 'name': $A.get("$Label.c.CCM_Portal_Home")},
                                {'href': '/s/orderinformation', 'name': $A.get("$Label.c.CCM_Portal_Order")},
                                {'href': '/s/promotion', 'name': $A.get("$Label.c.CCM_Portal_Promotion")},
                                {'href': '/s/productcatalogue', 'name': $A.get("$Label.c.CCM_Portal_ProductCategory")},
                                {'href': '/s/shipping-and-return-authorization-claims', 'name': $A.get("$Label.c.CCM_Portal_ShipmentAndReturnAuthorizationClaims")},
                                {'href': '/s/servicehome', 'name': $A.get("$Label.c.CCM_Portal_Service")},
                                {'href': '/s/invoice', 'name': $A.get("$Label.c.CCM_Portal_Invoice")},
                                {'href': '/s/co-op-program', 'name': $A.get("$Label.c.CCM_Portal_CoOpProgram")},
                                {'href': '/s/rebate', 'name': $A.get("$Label.c.CCM_Portal_RebateProgram")},
                                {'href': '/s/fleetprogramlist', 'name': $A.get("$Label.c.CCM_Portal_FleetProgram")},
                                {'href': '/s/myaccount', 'name': $A.get("$Label.c.CCM_Portal_MyAccount")},
                                {'href': '/s/resourcecenter', 'name': $A.get("$Label.c.CCM_Portal_ResourceCenter")}
                            ]);
                            if(data.isCCA){
                                component.set('v.navigateBars', [
                                    {'href': '/s/', 'name': $A.get("$Label.c.CCM_Portal_Home")},
                                    {'href': '/s/orderinformation', 'name': $A.get("$Label.c.CCM_Portal_Order")},
                                    {'href': '/s/promotion', 'name': $A.get("$Label.c.CCM_Portal_Promotion")},
                                    {'href': '/s/productcatalogue', 'name': $A.get("$Label.c.CCM_Portal_ProductCategory")},
                                    {'href': '/s/servicehome', 'name': $A.get("$Label.c.CCM_Portal_Service")},
                                    {'href': '/s/invoice', 'name': $A.get("$Label.c.CCM_Portal_Invoice")},
                                    {'href': '/s/co-op-program', 'name': $A.get("$Label.c.CCM_Portal_CoOpProgram")},
                                    {'href': '/s/rebate', 'name': $A.get("$Label.c.CCM_Portal_RebateProgram")},
                                    {'href': '/s/fleetprogramlist', 'name': $A.get("$Label.c.CCM_Portal_FleetProgram")},
                                    {'href': '/s/myaccount', 'name': $A.get("$Label.c.CCM_Portal_MyAccount")},
                                    {'href': '/s/resourcecenter', 'name': $A.get("$Label.c.CCM_Portal_ResourceCenter")}
                                ]);
                            }
                        }else{
                            component.set('v.navigateBars', [
                                {'href': '/s/', 'name': $A.get("$Label.c.CCM_Portal_Home")},
                                {'href': '/s/orderinformation', 'name': $A.get("$Label.c.CCM_Portal_Order")},
                                {'href': '/s/promotion', 'name': $A.get("$Label.c.CCM_Portal_Promotion")},
                                {'href': '/s/productcatalogue', 'name': $A.get("$Label.c.CCM_Portal_ProductCategory")},
                                {'href': '/s/shipping-and-return-authorization-claims', 'name': $A.get("$Label.c.CCM_Portal_ShipmentAndReturnAuthorizationClaims")},
                                {'href': '/s/servicehome', 'name': $A.get("$Label.c.CCM_Portal_Service")},
                                {'href': '/s/invoice', 'name': $A.get("$Label.c.CCM_Portal_Invoice")},
                                {'href': '/s/rebate', 'name': $A.get("$Label.c.CCM_Portal_RebateProgram")},
                                {'href': '/s/fleetprogramlist', 'name': $A.get("$Label.c.CCM_Portal_FleetProgram")},
                                {'href': '/s/myaccount', 'name': $A.get("$Label.c.CCM_Portal_MyAccount")},
                                {'href': '/s/resourcecenter', 'name': $A.get("$Label.c.CCM_Portal_ResourceCenter")}
                            ]);
                            if(data.isCCA){
								component.set('v.navigateBars', [
									{'href': '/s/', 'name': $A.get("$Label.c.CCM_Portal_Home")},
									{'href': '/s/orderinformation', 'name': $A.get("$Label.c.CCM_Portal_Order")},
									{'href': '/s/promotion', 'name': $A.get("$Label.c.CCM_Portal_Promotion")},
									{'href': '/s/productcatalogue', 'name': $A.get("$Label.c.CCM_Portal_ProductCategory")},
									{'href': '/s/servicehome', 'name': $A.get("$Label.c.CCM_Portal_Service")},
									{'href': '/s/invoice', 'name': $A.get("$Label.c.CCM_Portal_Invoice")},
									//{'href': '/s/co-op-program', 'name': 'Co-Op Program'},
									{'href': '/s/rebate', 'name': $A.get("$Label.c.CCM_Portal_RebateProgram")},
									{'href': '/s/fleetprogramlist', 'name': $A.get("$Label.c.CCM_Portal_FleetProgram")},
									{'href': '/s/myaccount', 'name': $A.get("$Label.c.CCM_Portal_MyAccount")},
									{'href': '/s/resourcecenter', 'name': $A.get("$Label.c.CCM_Portal_ResourceCenter")}
								]);
							}
                        }
                    }else if(data.profileName == 'Partner Community 2nd Tier Dealer Sales'){
                        if(data.EnableDropshipOrder == true){
                            component.set('v.navigateBars', [
                                {'href': '/s/', 'name': $A.get("$Label.c.CCM_Portal_Home")},
                                {'href': '/s/dropshiporder', 'name': 'Dropship Order'},
                                //{ "href": "/s/orderinformation", "name": $A.get("$Label.c.CCM_Portal_Order") },
                                {'href': '/s/promotion', 'name': $A.get("$Label.c.CCM_Portal_Promotion")},
                                // {'href': '/s/servicehome', 'name': $A.get("$Label.c.CCM_Portal_Service")},
                                {'href': '/s/productcatalogue', 'name': $A.get("$Label.c.CCM_Portal_ProductCategory")},
                                {'href': '/s/resourcecenter', 'name': $A.get("$Label.c.CCM_Portal_ResourceCenter")}
                            ]);
                        }else{
                            component.set('v.navigateBars', [
                                {'href': '/s/', 'name': $A.get("$Label.c.CCM_Portal_Home")},
                                //{'href': '/s/dropshiporder', 'name': 'Dropship Order'},
                                //{ "href": "/s/orderinformation", "name": $A.get("$Label.c.CCM_Portal_Order") },
                                {'href': '/s/promotion', 'name': $A.get("$Label.c.CCM_Portal_Promotion")},
                                // {'href': '/s/servicehome', 'name': $A.get("$Label.c.CCM_Portal_Service")},
                                {'href': '/s/productcatalogue', 'name': $A.get("$Label.c.CCM_Portal_ProductCategory")},
                                {'href': '/s/resourcecenter', 'name': $A.get("$Label.c.CCM_Portal_ResourceCenter")}
                            ]);
                        }
                    }
                    else if(data.profileName == 'Sales - Limited Access') {
                        component.set('v.navigateBars', [
                            {'href': '/s/', 'name': 'Home'},
                            {'href': '/s/orderinformation', 'name': 'Order'},
                            {'href': '/s/promotion', 'name': 'Promotion'},
                            {'href': '/s/productcatalogue', 'name': 'Product Category'},
                            {'href': '/s/shipping-and-return-authorization-claims', 'name': 'Shipment and Return Authorization Claims'},
                            {'href': '/s/servicehome', 'name': 'Service'},
                            {'href': '/s/invoice', 'name': 'Invoice'},
                            {'href': '/s/fleetprogramlist', 'name': 'Fleet Program'},
                            {'href': '/s/resourcecenter', 'name': 'Resource Center'}
                        ]);
                    }else{
                        component.set('v.navigateBars', [
                            {'href': '/s/', 'name': $A.get("$Label.c.CCM_Portal_Home")},
                            {'href': '/s/orderinformation', 'name': $A.get("$Label.c.CCM_Portal_Order")},
                            // {'href': '/s/promotion', 'name': $A.get("$Label.c.CCM_Portal_Promotion")},
                            {'href': '/s/productcatalogue', 'name': $A.get("$Label.c.CCM_Portal_ProductCategory")},
                            {'href': '/s/servicehome', 'name': $A.get("$Label.c.CCM_Portal_Service")},
                            {'href': '/s/invoice', 'name': $A.get("$Label.c.CCM_Portal_Invoice")},
                            {'href': '/s/rebate', 'name': $A.get("$Label.c.CCM_Portal_RebateProgram")},
                            {'href': '/s/fleetprogramlist', 'name': $A.get("$Label.c.CCM_Portal_FleetProgram")},
                            {'href': '/s/myaccount', 'name': $A.get("$Label.c.CCM_Portal_MyAccount")},
                            {'href': '/s/resourcecenter', 'name': $A.get("$Label.c.CCM_Portal_ResourceCenter")}
                        ]);
                        if(data.isCCA){
                            component.set('v.navigateBars', [
                                {'href': '/s/', 'name': $A.get("$Label.c.CCM_Portal_Home")},
                                {'href': '/s/orderinformation', 'name': $A.get("$Label.c.CCM_Portal_Order")},
                                // {'href': '/s/promotion', 'name': $A.get("$Label.c.CCM_Portal_Promotion")},
                                {'href': '/s/productcatalogue', 'name': $A.get("$Label.c.CCM_Portal_ProductCategory")},
                                {'href': '/s/servicehome', 'name': $A.get("$Label.c.CCM_Portal_Service")},
                                {'href': '/s/invoice', 'name': $A.get("$Label.c.CCM_Portal_Invoice")},
                                {'href': '/s/rebate', 'name': $A.get("$Label.c.CCM_Portal_RebateProgram")},
                                {'href': '/s/fleetprogramlist', 'name': $A.get("$Label.c.CCM_Portal_FleetProgram")},
                                {'href': '/s/myaccount', 'name': $A.get("$Label.c.CCM_Portal_MyAccount")},
                                {'href': '/s/resourcecenter', 'name': $A.get("$Label.c.CCM_Portal_ResourceCenter")}
                            ]);
                        }
                    }
                // @annotation,below else logic's snippects deal with other service profiles' tabs.
                }else{
                    if (data.profileName == 'Partner community 2nd tier store service' || data.profileName == 'Partner community login 2nd tier store service'){
                        component.set('v.navigateBars', [
                            { "href": "/s/orderinformation", "name": $A.get("$Label.c.CCM_Portal_Order") },
                            // {'href': '/s/promotion', 'name': $A.get("$Label.c.CCM_Portal_Promotion")},
                            {'href': '/s/servicehome', 'name': $A.get("$Label.c.CCM_Portal_Service")},
                            {'href': '/s/productcatalogue', 'name': $A.get("$Label.c.CCM_Portal_ProductCategory")},
                            {'href': '/s/resourcecenter', 'name': $A.get("$Label.c.CCM_Portal_ResourceCenter")}
                        ]);
                        if(data.isCCA){
                            component.set('v.navigateBars', [
                                { "href": "/s/orderinformation", "name": $A.get("$Label.c.CCM_Portal_Order") },
                                // {'href': '/s/promotion', 'name': $A.get("$Label.c.CCM_Portal_Promotion")},
                                //{'href': '/s/servicehome', 'name': $A.get("$Label.c.CCM_Portal_Service")},
                                {'href': '/s/productcatalogue', 'name': $A.get("$Label.c.CCM_Portal_ProductCategory")},
                                {'href': '/s/resourcecenter', 'name': $A.get("$Label.c.CCM_Portal_ResourceCenter")}
                            ]);
                        }
                        // update,napoeleon,23-1-1. reset the tabs to the next two profiles
                    }else if(data.profileName == 'Partner community login 2nd tier Distributor Service' || data.profileName == 'Partner community 2nd tier Distributor Service'){
                        component.set('v.navigateBars', [
                            //{ "href": "/s/orderinformation", "name": $A.get("$Label.c.CCM_Portal_Order") },
                            // {'href': '/s/promotion', 'name': $A.get("$Label.c.CCM_Portal_Promotion")},
                            {'href': '/s/servicehome', 'name': $A.get("$Label.c.CCM_Portal_Service")},
                            {'href': '/s/productcatalogue', 'name': $A.get("$Label.c.CCM_Portal_ProductCategory")},
                            {'href': '/s/resourcecenter', 'name': $A.get("$Label.c.CCM_Portal_ResourceCenter")}
                        ]);
                        if(data.isCCA){
                            component.set('v.navigateBars', [
                                {'href': '/s/servicehome', 'name': $A.get("$Label.c.CCM_Portal_Service")},
                                {'href': '/s/productcatalogue', 'name': $A.get("$Label.c.CCM_Portal_ProductCategory")},
                                {'href': '/s/resourcecenter', 'name': $A.get("$Label.c.CCM_Portal_ResourceCenter")}
                            ]);
                        }
                        
                    } else if (data.profileName === 'Partner Community Service' || data.profileName === 'Partner Community Login Service'){
                        component.set('v.navigateBars', [
                            {'href': '/s/', 'name': $A.get("$Label.c.CCM_Portal_Home")},
                            {'href': '/s/servicehome', 'name': $A.get("$Label.c.CCM_Portal_Service")},
                            {'href': '/s/productcatalogue', 'name': $A.get("$Label.c.CCM_Portal_ProductCategory")},
                            {'href': '/s/resourcecenter', 'name': $A.get("$Label.c.CCM_Portal_ResourceCenter")},
                            {'href': '/s/invoice', 'name': $A.get("$Label.c.CCM_Portal_Invoice")},
                            {"href": "/s/orderinformation", "name": $A.get("$Label.c.CCM_Portal_Order") }
                        ]);
                        if(data.isCCA){
                            component.set('v.navigateBars', [
                                {'href': '/s/', 'name': $A.get("$Label.c.CCM_Portal_Home")},
                                {'href': '/s/servicehome', 'name': $A.get("$Label.c.CCM_Portal_Service")},
                                {'href': '/s/productcatalogue', 'name': $A.get("$Label.c.CCM_Portal_ProductCategory")},
                                {'href': '/s/resourcecenter', 'name': $A.get("$Label.c.CCM_Portal_ResourceCenter")},
                                {'href': '/s/invoice', 'name': $A.get("$Label.c.CCM_Portal_Invoice")},
                                {"href": "/s/orderinformation", "name": $A.get("$Label.c.CCM_Portal_Order") }
                            ]);
                        }
                    } else {
                        component.set('v.navigateBars', [
                            {'href': '/s/', 'name': $A.get("$Label.c.CCM_Portal_Home")},
                            {'href': '/s/orderinformation', 'name': $A.get("$Label.c.CCM_Portal_Order")},
                            // {'href': '/s/promotion', 'name': $A.get("$Label.c.CCM_Portal_Promotion")},
                            {'href': '/s/productcatalogue', 'name': $A.get("$Label.c.CCM_Portal_ProductCategory")},
                            {'href': '/s/servicehome', 'name': $A.get("$Label.c.CCM_Portal_Service")},
                            {'href': '/s/invoice', 'name': $A.get("$Label.c.CCM_Portal_Invoice")},
                            {'href': '/s/resourcecenter', 'name': $A.get("$Label.c.CCM_Portal_ResourceCenter")}
                        ]);
                        if(data.isCCA){
                            component.set('v.navigateBars', [
                                {'href': '/s/', 'name': $A.get("$Label.c.CCM_Portal_Home")},
                                {'href': '/s/orderinformation', 'name': $A.get("$Label.c.CCM_Portal_Order")},
                                // {'href': '/s/promotion', 'name': $A.get("$Label.c.CCM_Portal_Promotion")},
                                {'href': '/s/productcatalogue', 'name': $A.get("$Label.c.CCM_Portal_ProductCategory")},
                                //{'href': '/s/servicehome', 'name': $A.get("$Label.c.CCM_Portal_Service")},
                                {'href': '/s/invoice', 'name': $A.get("$Label.c.CCM_Portal_Invoice")},
                                {'href': '/s/resourcecenter', 'name': $A.get("$Label.c.CCM_Portal_ResourceCenter")}
                            ]);
                        }
                    }

                }
                var navigateBars = component.get('v.navigateBars');
                var currentPage = window.location.href.split('site.com')[1];
                if (window.location.href.split('site.com')[1] == '/s/' && (data.profileName == 'Partner community 2nd tier store service' || data.profileName == 'Partner community login 2nd tier store service' || data.profileName == 'Partner community login 2nd tier Distributor Service' || data.profileName == 'Partner community 2nd tier Distributor Service')){
                    currentPage = navigateBars[0].href;
                }
                console.log('currentPage1--->'+currentPage);
                for(var i=0; i<navigateBars.length; i++){
                    if(navigateBars[i].href == currentPage){
                        navigateBars[i].active = true;
                    }
                }
                component.set('v.navigateBars', navigateBars);
                if (data.profileName == 'Partner community 2nd tier store service' || data.profileName == 'Partner community login 2nd tier store service' || data.profileName == 'Partner community login 2nd tier Distributor Service' || data.profileName == 'Partner community 2nd tier Distributor Service'){
                    if (window.location.href.split('site.com')[1] != currentPage && window.location.href.split('site.com')[1] == '/s/'){
                        window.location.href = currentPage;
                    }
                }
            }
        }));
        $A.enqueueAction(action);
    },
    handleNavigateMenuSelect: function(component, event){
        var selectedMenuItemValue = event.getParam("value");
        window.location.href = selectedMenuItemValue;
    },
    userLogout: function(component, event, helper){
          helper.setCookie('force-stream', '');
          helper.setCookie('force-proxy-stream', '');
          helper.setCookie('sid_Client', '');
          helper.setCookie('sid', '');
//        window.location.href = window.location.hostname + '/secur/logout.jsp'
    },
    // add haibo: french
    handleLanguageChange: function(component, event, helper){
        if (component.get('v.language') == 'fr' && $A.get("$Label.c.CCM_Portal_Language") != 'fr') {
            helper.switchLanguage('fr', component);
        } else if (component.get('v.language') != 'fr' && $A.get("$Label.c.CCM_Portal_Language") == 'fr') {
            helper.switchLanguage('en_US', component);
        }
    },
})