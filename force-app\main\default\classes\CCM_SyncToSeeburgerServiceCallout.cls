public without sharing class CCM_SyncToSeeburgerServiceCallout {
    public static String getAddressesByIds(Set<String> addressIdSet){
        CCM_SubInventorySelector.initSubInventoryConfigurations();
        String addreInfoStr = '';
        addreInfoStr += '[';
        List<String> approveMaplist = new List<String>();
        approveMaplist.add('');
        approveMaplist.add('Approved');

        Map<String,String> DropShipFreigtTermMap = Util.getDropShipFreightTermMap2();
        Map<String,String> specialDropShipFreigtTermMap = Util.getSpecialDropShipFreightTermMap();
        Map<String,String> specialDropShipPricebookMap = Util.getSpecialDropShipPricebookName();
        Map<String, Address_With_Program__c> idRecordMap = new Map<String, Address_With_Program__c>();
        String customerId = '';
        for(Address_With_Program__c addrePro : [SELECT Account_Address__r.State__c, Account_Address__r.Customer__c, Program__r.Brands__c FROM Address_With_Program__c WHERE Account_Address__c IN :addressIdSet 
                                                AND Account_Address__r.Approval_Status__c in :approveMaplist 
                                                AND Program__r.Approval_Status__c in :approveMaplist]) {
            idRecordMap.put(addrePro.Id, addrePro);
            customerId = addrePro.Account_Address__r.Customer__c;
        }
        Map<String, String> subInventoryMap = getSubInventory(idRecordMap, customerId);
        for (Address_With_Program__c addrePro : [SELECT Id,
                                                        Customer_Line_SF_ID__c,
                                                        Customer_Line_Oracle_ID__c,
                                                        Account_Address__r.Customer__r.Customer_SF_ID__c,
                                                        Account_Address__r.Customer__r.Customer_Oracle_ID__c,
                                                        Account_Address__r.ORG_ID__c,
                                                        Account_Address__r.RecordType.DeveloperName,
                                                        Account_Address__r.Primary__c,
                                                        Account_Address__r.Deliver_From__c,
                                                        Account_Address__r.Email_for_Invoicing__c,
                                                        Account_Address__r.Store_Number__c,
                                                        Account_Address__r.FLEX_Agency__r.Code__c, 
                                                        Account_Address__r.EGO_Agency__r.Code__c, 
                                                        Account_Address__r.SKIL_SKILSAW_Agency__r.Code__c,
                                                        Program__r.Sales_Group__c,
                                                        Account_Address__r.Sales_Agency__r.Code__c,
                                                        Account_Address__r.Customer__r.Distributor_or_Dealer__c,
                                                        Program__r.Authorized_Brand_Name_To_Oracle__c,
                                                        Account_Address__r.Address1__c,
                                                        Account_Address__r.Address2__c,
                                                        Account_Address__r.City__c,
                                                        Account_Address__r.State__c ,
                                                        Account_Address__r.Postal_Code__c,
                                                        Account_Address__r.Country__c,
                                                        Account_Address__r.NP_Bill_To__c,
                                                        Program__r.Brands__c,
                                                        Program__r.Payment_Term__c,
                                                        Program__r.Freight_Term__c,
                                                        Program__r.Order_Type__c,
                                                        Program__r.Trade_Discount__c,
                                                        Program__r.Warehouse_Discount__c,
                                                        Program__r.Payment_Discount__c,
                                                        Program__r.Co_Op_Discount__c,
                                                        Program__r.Volume_Rebate__c,
                                                        Program__r.Growth_Rebate__c,
                                                        Program__r.Freight_Allowance__c,
                                                        Program__r.Discount1__c,
                                                        Program__r.Discount2__c,
                                                        Program__r.Discount3__c,
                                                        Program__r.New_Store_Allowance__c,
                                                        Program__r.Price_Book__c,
                                                        Program__r.Price_Book__r.Name,
                                                        Account_Address__r.Customer__r.Name,
                                                        Account_Address__r.Name,
                                                        Status__c,
                                                        Account_Address__r.Customer__r.BillingState,
                                                        Account_Address__r.Customer__r.AccountNumber,
                                                        Special_Dropship_Address__c
                                                        FROM Address_With_Program__c 
                                                        WHERE Account_Address__c IN :addressIdSet
                                                        And Account_Address__r.Approval_Status__c in :approveMaplist
                                                        And Program__r.Approval_Status__c in :approveMaplist]) {
            Map<String,Object> addreProMap = new Map<String,Object>();
            addreInfoStr += '{';
            addreInfoStr += '"CUSTOMER_LINE_ID":"' +'Salesforce-' + addrePro.Id +'-' + System.now().getTime() + '",';
            addreInfoStr += addrePro.Customer_Line_SF_ID__c != null ? '"Customer_Line_SF_ID__c":"' + addrePro.Customer_Line_SF_ID__c + '",' : '"Customer_Line_SF_ID__c":"",';
            addreInfoStr += addrePro.Customer_Line_Oracle_ID__c != null ? '"Customer_Line_Oracle_ID__c":"' + addrePro.Customer_Line_Oracle_ID__c + '",' : '"Customer_Line_Oracle_ID__c":"",';
            addreInfoStr += addrePro.Account_Address__r.Customer__r.Customer_SF_ID__c != null ? '"Customer_SF_ID__c":"' + addrePro.Account_Address__r.Customer__r.Customer_SF_ID__c + '",' : '"Customer_SF_ID__c":"",';
            addreInfoStr += addrePro.Account_Address__r.Customer__r.Customer_Oracle_ID__c != null ? '"Customer_Oracle_ID__c":"' + addrePro.Account_Address__r.Customer__r.Customer_Oracle_ID__c + '",' : '"Customer_Oracle_ID__c":"",';
            addreInfoStr += addrePro.Account_Address__r.ORG_ID__c != null ? '"Region__c":"' + addrePro.Account_Address__r.ORG_ID__c + '",' : '"Region__c":"",';
            if (addrePro.Account_Address__r.RecordType.DeveloperName == 'Billing_Address' || addrePro.Account_Address__r.RecordType.DeveloperName == 'Dropship_Billing_Address'){
                addreInfoStr += '"BILLTO_FLAG__C":"true",';
            }else {
                addreInfoStr += '"BILLTO_FLAG__C":"false",';
            }
            if (addrePro.Account_Address__r.RecordType.DeveloperName == 'Shipping_Address' || addrePro.Account_Address__r.RecordType.DeveloperName == 'Dropship_Shipping_Address'){
                addreInfoStr += '"SHIPTO_FLAG__C":"true",';
            }else {
                addreInfoStr += '"SHIPTO_FLAG__C":"false",';
            }
            addreInfoStr += addrePro.Account_Address__r.Primary__c != null ? '"Primary__c":"' + addrePro.Account_Address__r.Primary__c + '",' : '"Primary__c":"",';
            addreInfoStr += addrePro.Account_Address__r.Email_for_Invoicing__c != null ? '"Email_for_Invoice__c":"' + addrePro.Account_Address__r.Email_for_Invoicing__c + '",' : '"Email_for_Invoice__c":"",';
            addreInfoStr += addrePro.Account_Address__r.Store_Number__c != null ? '"Store_Number__c":"' + addrePro.Account_Address__r.Store_Number__c + '",' : '"Store_Number__c":"",';
            addreInfoStr += addrePro.Program__r.Sales_Group__c != null ? '"Sales_Group__c":"' + addrePro.Program__r.Sales_Group__c + '",' : '"Sales_Group__c":"",';   
            
            if (addrePro.Account_Address__r.EGO_Agency__r.Code__c != null && addrePro.Account_Address__r.EGO_Agency__r.Code__c != 'No Sales Credit') {
                addreInfoStr += '"Sales_Rep__c":"' + addrePro.Account_Address__r.EGO_Agency__r.Code__c + '",';
            } else if (addrePro.Account_Address__r.SKIL_SKILSAW_Agency__r.Code__c != null && addrePro.Account_Address__r.SKIL_SKILSAW_Agency__r.Code__c != 'No Sales Credit') {
                addreInfoStr += '"Sales_Rep__c":"' + addrePro.Account_Address__r.SKIL_SKILSAW_Agency__r.Code__c + '",';
            } else if (addrePro.Account_Address__r.FLEX_Agency__r.Code__c != null && addrePro.Account_Address__r.FLEX_Agency__r.Code__c != 'No Sales Credit') {
                addreInfoStr += '"Sales_Rep__c":"' + addrePro.Account_Address__r.FLEX_Agency__r.Code__c + '",';
            } else {
                addreInfoStr += '"Sales_Rep__c":"",';
            }
            
            if (addrePro.Account_Address__r.RecordType.DeveloperName == 'Billing_Address'){
                if (addrePro.Account_Address__r.Customer__r.Distributor_or_Dealer__c == 'Distributor' || addrePro.Account_Address__r.Customer__r.Distributor_or_Dealer__c == 'Co-op Distributor'){
                    if(addrePro.Account_Address__r.NP_Bill_To__c == true){
                        addreInfoStr += '"Address1__c":"Northpoint Commercial Finance",';
                    }else{
                        addreInfoStr += '"Address1__c":"' + addrePro.Program__r.Authorized_Brand_Name_To_Oracle__c + ' warehouse' + '",';
                    }
                } else {
                    if(addrePro.Account_Address__r.NP_Bill_To__c == true){
                        addreInfoStr += '"Address1__c":"Northpoint Commercial Finance",';
                    }else{
                        addreInfoStr += '"Address1__c":"' + addrePro.Program__r.Authorized_Brand_Name_To_Oracle__c + '",';
                    }
                }
            }else if (addrePro.Account_Address__r.RecordType.DeveloperName == 'Shipping_Address' || addrePro.Account_Address__r.RecordType.DeveloperName == 'Dropship_Shipping_Address'){
                addreInfoStr += '"Address1__c":"' + addrePro.Account_Address__r.Name + '",';
            }else if (addrePro.Account_Address__r.RecordType.DeveloperName == 'Dropship_Billing_Address'){
                addreInfoStr += '"Address1__c":"' + addrePro.Program__r.Authorized_Brand_Name_To_Oracle__c + ' store & others' + '",';
            }else {
                addreInfoStr += '"Address1__c":"",';
            }

            addreInfoStr += addrePro.Account_Address__r.Address1__c != null ? '"Address2__c":"' + addrePro.Account_Address__r.Address1__c + '",' : '"Address2__c":"",';
            addreInfoStr += addrePro.Account_Address__r.Address2__c != null ? '"Address3__c":"' + addrePro.Account_Address__r.Address2__c + '",' : '"Address3__c":"",';
            addreInfoStr += addrePro.Account_Address__r.City__c != null ? '"City__c":"' + addrePro.Account_Address__r.City__c + '",' : '"City__c":"",';
            if (addrePro.Account_Address__r.ORG_ID__c != 'CCA') {
                addreInfoStr += addrePro.Account_Address__r.State__c != null ? '"State__c":"' + addrePro.Account_Address__r.State__c + '",' : '"State__c":"",';
            } else {
                addreInfoStr += '"State__c":"",';
            }
            // addreInfoStr += addrePro.Account_Address__r.State__c != null ? '"State__c":"' + addrePro.Account_Address__r.State__c + '",' : '"State__c":"",';
            addreInfoStr += addrePro.Account_Address__r.Postal_Code__c != null ? '"Postal_Code__c":"' + addrePro.Account_Address__r.Postal_Code__c + '",' : '"Postal_Code__c":"",';
            addreInfoStr += addrePro.Account_Address__r.Country__c != null ? '"Country__c":"' + addrePro.Account_Address__r.Country__c + '",' : '"Country__c":"",';
            
            if (addrePro.Account_Address__r.RecordType.DeveloperName == 'Billing_Address' || addrePro.Account_Address__r.RecordType.DeveloperName =='Dropship_Billing_Address'){
                addreInfoStr += '"Ship_VIA__c":"Ground Freight",';
            }else {
                addreInfoStr += '"Ship_VIA__c":"",';
            }
            addreInfoStr += addrePro.Program__r.Brands__c != null ? '"Brand__c":"' + addrePro.Program__r.Brands__c.toUpperCase() + '",' : '"Brand__c":"",';
            
            addreInfoStr += addrePro.Program__r.Payment_Term__c != null ? '"Payment_Term__c":"' + addrePro.Program__r.Payment_Term__c + '",' : '"Payment_Term__c":"",';

            String brandName = addrePro.Program__r.Brands__c;
            if(String.isNotBlank(brandName)){
                brandName = brandName.toUpperCase();
            }

            String fTerm = addrePro.Program__r.Freight_Term__c;
            if(addrePro.Account_Address__r.RecordType.DeveloperName == 'Dropship_Billing_Address'){
                //update by nick ********:special dropship will get price list and freight term from "Price And Terms Reference" 
                if (addrePro.Special_Dropship_Address__c == true) {
                    fTerm = specialDropShipFreigtTermMap.get(addrePro.Id) != null ? specialDropShipFreigtTermMap.get(addrePro.Id) : fTerm;
                } else {
                    fTerm = DropShipFreigtTermMap.get(brandName) != null ? DropShipFreigtTermMap.get(brandName) : fTerm;
                }
            }
            if (String.isEmpty(fTerm)) {
                addreInfoStr += '"Freight_Term__c":"",';
            } else {
                addreInfoStr += '"Freight_Term__c":"' + fTerm + '",';
            }
            
            
            addreInfoStr += addrePro.Program__r.Order_Type__c != null ? '"Order_Type__c":"' + addrePro.Program__r.Order_Type__c + '",' : '"Order_Type__c":"",';

            String pricelist = '';
            if(addrePro.Program__r.Price_Book__c == null){
                pricelist = addrePro.Account_Address__r.Customer__r.Name + ' Price List';
            }else{
                pricelist = addrePro.Program__r.Price_Book__r.Name;
            }            
            if(addrePro.Account_Address__r.RecordType.DeveloperName == 'Dropship_Billing_Address'){
                if (addrePro.Special_Dropship_Address__c == true) {
                    //update by nick ********: special dripship will get price book from 'Price And Terms Reference' which Special_Dropship_Address__c is true
                    pricelist = specialDropShipPricebookMap.get(addrePro.Id);
                } else {
                    pricelist = Util.getDropShipPricebookName(brandName, addrePro.Account_Address__r.ORG_ID__c);
                }
            }
            addreInfoStr += '"Price_List__c":"' + pricelist + '",';

            addreInfoStr += addrePro.Program__r.Trade_Discount__c != null ? '"Trade_Discount__c":"' + addrePro.Program__r.Trade_Discount__c + '",' : '"Trade_Discount__c":"",';
            addreInfoStr += addrePro.Program__r.Warehouse_Discount__c != null ? '"Warehouse_Discount__c":"' + addrePro.Program__r.Warehouse_Discount__c + '",' : '"Warehouse_Discount__c":"",';
            addreInfoStr += addrePro.Program__r.Payment_Discount__c != null ? '"Payment_Discount__c":"' + addrePro.Program__r.Payment_Discount__c + '",' : '"Payment_Discount__c":"",';
            addreInfoStr += addrePro.Program__r.Co_Op_Discount__c != null ? '"Co_Op_Discount__c":"' + addrePro.Program__r.Co_Op_Discount__c + '",' : '"Co_Op_Discount__c":"",';
            addreInfoStr += addrePro.Program__r.Volume_Rebate__c != null ? '"Volume_Rebate__c":"' + addrePro.Program__r.Volume_Rebate__c + '",' : '"Volume_Rebate__c":"",';
            addreInfoStr += addrePro.Program__r.Growth_Rebate__c != null ? '"Growth_Rebate__c":"' + addrePro.Program__r.Growth_Rebate__c + '",' : '"Growth_Rebate__c":"",';
            addreInfoStr += addrePro.Program__r.Freight_Allowance__c != null ? '"Freight_Allowance__c":"' + addrePro.Program__r.Freight_Allowance__c + '",' : '"Freight_Allowance__c":"",';
            addreInfoStr += addrePro.Program__r.Discount1__c != null ? '"Discount1__c":"' + addrePro.Program__r.Discount1__c + '",' : '"Discount1__c":"",';
            addreInfoStr += addrePro.Program__r.Discount2__c != null ? '"Discount2__c":"' + addrePro.Program__r.Discount2__c + '",' : '"Discount2__c":"",';
            addreInfoStr += addrePro.Program__r.Discount3__c != null ? '"Discount3__c":"' + addrePro.Program__r.Discount3__c + '",' : '"Discount3__c":"",';
            addreInfoStr += addrePro.Program__r.New_Store_Allowance__c != null ? '"New_Store_Allowance__c":"' + addrePro.Program__r.New_Store_Allowance__c + '",' : '"New_Store_Allowance__c":"",';
            addreInfoStr += addrePro.Status__c != null ? '"Status__c":"' + addrePro.Status__c + '",' : '"Status__c":"",';
            if (addrePro.Account_Address__r.ORG_ID__c == 'CCA') {
                String state = '';
                state = addrePro.Account_Address__r.State__c == 'ON' ? 'Ontario' : addrePro.Account_Address__r.State__c; //TODO
                addreInfoStr += addrePro.Account_Address__r.State__c != null ? '"Attribute1":"' + state + '",' : '"Attribute1":"",';
            } else {
                addreInfoStr += '"Attribute1":"",';
            }
            String subInventory = '';
            if (addrePro.Account_Address__r.RecordType.DeveloperName == 'Shipping_Address' || addrePro.Account_Address__r.RecordType.DeveloperName == 'Dropship_Shipping_Address'){
                subInventory = addrePro.Account_Address__r.Deliver_From__c;
                if(String.isBlank(subInventory)) {
                    if(subInventoryMap.containsKey(addrePro.Id)) {
                        subInventory = subInventoryMap.get(addrePro.Id);
                    }
                }
            }
            if(String.isBlank(subInventory)) {
                subInventory = '';
            }
            addreInfoStr += '"Attribute2":"' + subInventory + '",';
            addreInfoStr += '"Attribute3":"",';
            addreInfoStr += '"Attribute4":"",';
            addreInfoStr += '"Attribute5":"",';
            addreInfoStr += '"Attribute6":"",';
            addreInfoStr += '"Attribute7":"",';
            addreInfoStr += '"Attribute8":"",';
            addreInfoStr += '"Attribute9":"",';
            addreInfoStr += '"Attribute10":""';
            addreInfoStr += '},';
        }
        addreInfoStr = addreInfoStr.removeEnd(',') + ']';
        return addreInfoStr;
    }

    public static String getAddressInfoMapList(String addressId){
        CCM_SubInventorySelector.initSubInventoryConfigurations();
        String addreInfoStr = '';
        addreInfoStr += '[';
        List<String> approveMaplist = new List<String>();
        approveMaplist.add('');
        approveMaplist.add('Approved');

        Map<String,String> DropShipFreigtTermMap = Util.getDropShipFreightTermMap2();
        Map<String,String> specialDropShipFreigtTermMap = Util.getSpecialDropShipFreightTermMap();
        Map<String,String> specialDropShipPricebookMap = Util.getSpecialDropShipPricebookName();
        Map<String, Address_With_Program__c> idRecordMap = new Map<String, Address_With_Program__c>();
        String customerId = '';
        for(Address_With_Program__c addrePro : [SELECT Account_Address__r.State__c, Account_Address__r.Customer__c, Program__r.Brands__c FROM Address_With_Program__c WHERE Account_Address__c = :addressId 
                                                AND Account_Address__r.Approval_Status__c in :approveMaplist 
                                                AND Program__r.Approval_Status__c in :approveMaplist]) {
            idRecordMap.put(addrePro.Id, addrePro);
            customerId = addrePro.Account_Address__r.Customer__c;
        }
        Map<String, String> subInventoryMap = getSubInventory(idRecordMap, customerId);
        for (Address_With_Program__c addrePro : [SELECT Id,
                                                        Customer_Line_SF_ID__c,
                                                        Customer_Line_Oracle_ID__c,
                                                        Account_Address__r.Customer__r.Customer_SF_ID__c,
                                                        Account_Address__r.Customer__r.Customer_Oracle_ID__c,
                                                        Account_Address__r.ORG_ID__c,
                                                        Account_Address__r.RecordType.DeveloperName,
                                                        Account_Address__r.Primary__c,
                                                        Account_Address__r.Email_for_Invoicing__c,
                                                        Account_Address__r.Store_Number__c,
                                                        Account_Address__r.FLEX_Agency__r.Code__c, 
                                                        Account_Address__r.EGO_Agency__r.Code__c, 
                                                        Account_Address__r.SKIL_SKILSAW_Agency__r.Code__c,
                                                        Program__r.Sales_Group__c,
                                                        Account_Address__r.Sales_Agency__r.Code__c,
                                                        Account_Address__r.Customer__r.Distributor_or_Dealer__c,
                                                        Program__r.Authorized_Brand_Name_To_Oracle__c,
                                                        Account_Address__r.Address1__c,
                                                        Account_Address__r.Address2__c,
                                                        Account_Address__r.City__c,
                                                        Account_Address__r.State__c ,
                                                        Account_Address__r.Postal_Code__c,
                                                        Account_Address__r.Country__c,
                                                        Account_Address__r.Deliver_From__c,
                                                        Account_Address__r.NP_Bill_To__c,
                                                        Program__r.Brands__c,
                                                        Program__r.Payment_Term__c,
                                                        Program__r.Freight_Term__c,
                                                        Program__r.Order_Type__c,
                                                        Program__r.Trade_Discount__c,
                                                        Program__r.Warehouse_Discount__c,
                                                        Program__r.Payment_Discount__c,
                                                        Program__r.Co_Op_Discount__c,
                                                        Program__r.Volume_Rebate__c,
                                                        Program__r.Growth_Rebate__c,
                                                        Program__r.Freight_Allowance__c,
                                                        Program__r.Discount1__c,
                                                        Program__r.Discount2__c,
                                                        Program__r.Discount3__c,
                                                        Program__r.New_Store_Allowance__c,
                                                        Program__r.Price_Book__c,
                                                        Program__r.Price_Book__r.Name,
                                                        Program__r.RecordTypeId, 
                                                        Program__r.Customer__r.Distributor_or_Dealer__c, 
                                                        Account_Address__r.Customer__r.Name,
                                                        Account_Address__r.Name,
                                                        Status__c,
                                                        Account_Address__r.Customer__r.BillingState,
                                                        Account_Address__r.Customer__r.AccountNumber,
                                                        Special_Dropship_Address__c
                                                        FROM Address_With_Program__c 
                                                        WHERE Account_Address__c = :addressId
                                                        And Account_Address__r.Approval_Status__c in :approveMaplist
                                                        And Program__r.Approval_Status__c in :approveMaplist]) {
            Map<String,Object> addreProMap = new Map<String,Object>();
            addreInfoStr += '{';
            addreInfoStr += '"CUSTOMER_LINE_ID":"' +'Salesforce-' + addrePro.Id +'-' + System.now().getTime() + '",';
            addreInfoStr += addrePro.Customer_Line_SF_ID__c != null ? '"Customer_Line_SF_ID__c":"' + addrePro.Customer_Line_SF_ID__c + '",' : '"Customer_Line_SF_ID__c":"",';
            addreInfoStr += addrePro.Customer_Line_Oracle_ID__c != null ? '"Customer_Line_Oracle_ID__c":"' + addrePro.Customer_Line_Oracle_ID__c + '",' : '"Customer_Line_Oracle_ID__c":"",';
            addreInfoStr += addrePro.Account_Address__r.Customer__r.Customer_SF_ID__c != null ? '"Customer_SF_ID__c":"' + addrePro.Account_Address__r.Customer__r.Customer_SF_ID__c + '",' : '"Customer_SF_ID__c":"",';
            addreInfoStr += addrePro.Account_Address__r.Customer__r.Customer_Oracle_ID__c != null ? '"Customer_Oracle_ID__c":"' + addrePro.Account_Address__r.Customer__r.Customer_Oracle_ID__c + '",' : '"Customer_Oracle_ID__c":"",';
            addreInfoStr += addrePro.Account_Address__r.ORG_ID__c != null ? '"Region__c":"' + addrePro.Account_Address__r.ORG_ID__c + '",' : '"Region__c":"",';
            if (addrePro.Account_Address__r.RecordType.DeveloperName == 'Billing_Address' || addrePro.Account_Address__r.RecordType.DeveloperName == 'Dropship_Billing_Address'){
                addreInfoStr += '"BILLTO_FLAG__C":"true",';
            }else {
                addreInfoStr += '"BILLTO_FLAG__C":"false",';
            }
            // RecordType_Name__c和RecordType.DeveloperName
            if (addrePro.Account_Address__r.RecordType.DeveloperName == 'Shipping_Address' || addrePro.Account_Address__r.RecordType.DeveloperName == 'Dropship_Shipping_Address'){
                addreInfoStr += '"SHIPTO_FLAG__C":"true",';
            }else {
                addreInfoStr += '"SHIPTO_FLAG__C":"false",';
            }
            addreInfoStr += addrePro.Account_Address__r.Primary__c != null ? '"Primary__c":"' + addrePro.Account_Address__r.Primary__c + '",' : '"Primary__c":"",';
            addreInfoStr += addrePro.Account_Address__r.Email_for_Invoicing__c != null ? '"Email_for_Invoice__c":"' + addrePro.Account_Address__r.Email_for_Invoicing__c + '",' : '"Email_for_Invoice__c":"",';
            addreInfoStr += addrePro.Account_Address__r.Store_Number__c != null ? '"Store_Number__c":"' + addrePro.Account_Address__r.Store_Number__c + '",' : '"Store_Number__c":"",';
            addreInfoStr += addrePro.Program__r.Sales_Group__c != null ? '"Sales_Group__c":"' + addrePro.Program__r.Sales_Group__c + '",' : '"Sales_Group__c":"",';   
            
            if (addrePro.Account_Address__r.EGO_Agency__r.Code__c != null && addrePro.Account_Address__r.EGO_Agency__r.Code__c != 'No Sales Credit') {
                addreInfoStr += '"Sales_Rep__c":"' + addrePro.Account_Address__r.EGO_Agency__r.Code__c + '",';
            } else if (addrePro.Account_Address__r.SKIL_SKILSAW_Agency__r.Code__c != null && addrePro.Account_Address__r.SKIL_SKILSAW_Agency__r.Code__c != 'No Sales Credit') {
                addreInfoStr += '"Sales_Rep__c":"' + addrePro.Account_Address__r.SKIL_SKILSAW_Agency__r.Code__c + '",';
            } else if (addrePro.Account_Address__r.FLEX_Agency__r.Code__c != null && addrePro.Account_Address__r.FLEX_Agency__r.Code__c != 'No Sales Credit') {
                addreInfoStr += '"Sales_Rep__c":"' + addrePro.Account_Address__r.FLEX_Agency__r.Code__c + '",';
            } else {
                addreInfoStr += '"Sales_Rep__c":"",';
            }
            
            if (addrePro.Account_Address__r.RecordType.DeveloperName == 'Billing_Address'){
                if (addrePro.Account_Address__r.Customer__r.Distributor_or_Dealer__c == 'Distributor' || addrePro.Account_Address__r.Customer__r.Distributor_or_Dealer__c == 'Co-op Distributor'){
                    if(addrePro.Account_Address__r.NP_Bill_To__c == true){
                        addreInfoStr += '"Address1__c":"Northpoint Commercial Finance",';
                    }else{
                        addreInfoStr += '"Address1__c":"' + addrePro.Program__r.Authorized_Brand_Name_To_Oracle__c + ' warehouse' + '",';
                    }
                } else {
                    if(addrePro.Account_Address__r.NP_Bill_To__c == true){
                        addreInfoStr += '"Address1__c":"Northpoint Commercial Finance",';
                    }else{
                        addreInfoStr += '"Address1__c":"' + addrePro.Program__r.Authorized_Brand_Name_To_Oracle__c + '",';
                    }  
                }
            }else if (addrePro.Account_Address__r.RecordType.DeveloperName == 'Shipping_Address' || addrePro.Account_Address__r.RecordType.DeveloperName == 'Dropship_Shipping_Address'){
                addreInfoStr += '"Address1__c":"' + addrePro.Account_Address__r.Name + '",';
            }else if (addrePro.Account_Address__r.RecordType.DeveloperName == 'Dropship_Billing_Address'){
                addreInfoStr += '"Address1__c":"' + addrePro.Program__r.Authorized_Brand_Name_To_Oracle__c + ' store & others' + '",';
            }else {
                addreInfoStr += '"Address1__c":"",';
            }

            addreInfoStr += addrePro.Account_Address__r.Address1__c != null ? '"Address2__c":"' + addrePro.Account_Address__r.Address1__c + '",' : '"Address2__c":"",';
            addreInfoStr += addrePro.Account_Address__r.Address2__c != null ? '"Address3__c":"' + addrePro.Account_Address__r.Address2__c + '",' : '"Address3__c":"",';
            addreInfoStr += addrePro.Account_Address__r.City__c != null ? '"City__c":"' + addrePro.Account_Address__r.City__c + '",' : '"City__c":"",';
            if (addrePro.Account_Address__r.ORG_ID__c != 'CCA') {
                addreInfoStr += addrePro.Account_Address__r.State__c != null ? '"State__c":"' + addrePro.Account_Address__r.State__c + '",' : '"State__c":"",';
            } else {
                addreInfoStr += '"State__c":"",';
            }
            
            addreInfoStr += addrePro.Account_Address__r.Postal_Code__c != null ? '"Postal_Code__c":"' + addrePro.Account_Address__r.Postal_Code__c + '",' : '"Postal_Code__c":"",';
            addreInfoStr += addrePro.Account_Address__r.Country__c != null ? '"Country__c":"' + addrePro.Account_Address__r.Country__c + '",' : '"Country__c":"",';
            
            if (addrePro.Account_Address__r.RecordType.DeveloperName == 'Billing_Address' || addrePro.Account_Address__r.RecordType.DeveloperName =='Dropship_Billing_Address'){
                addreInfoStr += '"Ship_VIA__c":"Ground Freight",';
            }else {
                addreInfoStr += '"Ship_VIA__c":"",';
            }
            addreInfoStr += addrePro.Program__r.Brands__c != null ? '"Brand__c":"' + addrePro.Program__r.Brands__c.toUpperCase() + '",' : '"Brand__c":"",';
            if(addrePro.Account_Address__r.NP_Bill_To__c == true){
                addreInfoStr += '"Payment_Term__c":"NA014",';
            }else{
                addreInfoStr += addrePro.Program__r.Payment_Term__c != null ? '"Payment_Term__c":"' + addrePro.Program__r.Payment_Term__c + '",' : '"Payment_Term__c":"",';
            }
            

            String brandName = addrePro.Program__r.Brands__c;
            if(String.isNotBlank(brandName)){
                brandName = brandName.toUpperCase();
            }

            String fTerm = addrePro.Program__r.Freight_Term__c;
            if(addrePro.Account_Address__r.RecordType.DeveloperName == 'Dropship_Billing_Address'){
                //update by nick ********:special dropship will get price list and freight term from "Price And Terms Reference" 
                if (addrePro.Special_Dropship_Address__c == true) {
                    fTerm = specialDropShipFreigtTermMap.get(addrePro.Id) != null ? specialDropShipFreigtTermMap.get(addrePro.Id) : fTerm;
                } else {
                    fTerm = DropShipFreigtTermMap.get(brandName) != null ? DropShipFreigtTermMap.get(brandName) : fTerm;
                }
            }
            if (String.isEmpty(fTerm)) {
                addreInfoStr += '"Freight_Term__c":"",';
            } else {
                addreInfoStr += '"Freight_Term__c":"' + fTerm + '",';
            }
            // addreInfoStr += '"Freight_Term__c":"' + fTerm + '",';
            
            addreInfoStr += addrePro.Program__r.Order_Type__c != null ? '"Order_Type__c":"' + addrePro.Program__r.Order_Type__c + '",' : '"Order_Type__c":"",';

            String pricelist = '';
            if(addrePro.Program__r.Price_Book__c == null){
                pricelist = addrePro.Account_Address__r.Customer__r.Name + ' Price List';
            }else{
                pricelist = addrePro.Program__r.Price_Book__r.Name;
            }            
            if(addrePro.Account_Address__r.RecordType.DeveloperName == 'Dropship_Billing_Address'){
                if (addrePro.Special_Dropship_Address__c == true) {
                    //update by nick ********: special dripship will get price book from 'Price And Terms Reference' which Special_Dropship_Address__c is true
                    pricelist = specialDropShipPricebookMap.get(addrePro.Id);
                } else {
                    pricelist = Util.getDropShipPricebookName(brandName, addrePro.Account_Address__r.ORG_ID__c);
                }
            }
            if (CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_IDS.contains(addrePro.Program__r.RecordTypeId)) {
                if(addrePro.Account_Address__r.ORG_ID__c != 'CCA') {
                    if (addrePro.Program__r.Customer__r.Distributor_or_Dealer__c.contains('Dealer')) {
                        pricelist = 'CNA-Direct Dealer Price for Parts';
                    } else {
                        pricelist = 'CNA-Distributor Price for Parts';
                    }
                }
            }
            addreInfoStr += '"Price_List__c":"' + pricelist + '",';

            addreInfoStr += addrePro.Program__r.Trade_Discount__c != null ? '"Trade_Discount__c":"' + addrePro.Program__r.Trade_Discount__c + '",' : '"Trade_Discount__c":"",';
            addreInfoStr += addrePro.Program__r.Warehouse_Discount__c != null ? '"Warehouse_Discount__c":"' + addrePro.Program__r.Warehouse_Discount__c + '",' : '"Warehouse_Discount__c":"",';
            addreInfoStr += addrePro.Program__r.Payment_Discount__c != null ? '"Payment_Discount__c":"' + addrePro.Program__r.Payment_Discount__c + '",' : '"Payment_Discount__c":"",';
            addreInfoStr += addrePro.Program__r.Co_Op_Discount__c != null ? '"Co_Op_Discount__c":"' + addrePro.Program__r.Co_Op_Discount__c + '",' : '"Co_Op_Discount__c":"",';
            addreInfoStr += addrePro.Program__r.Volume_Rebate__c != null ? '"Volume_Rebate__c":"' + addrePro.Program__r.Volume_Rebate__c + '",' : '"Volume_Rebate__c":"",';
            addreInfoStr += addrePro.Program__r.Growth_Rebate__c != null ? '"Growth_Rebate__c":"' + addrePro.Program__r.Growth_Rebate__c + '",' : '"Growth_Rebate__c":"",';
            addreInfoStr += addrePro.Program__r.Freight_Allowance__c != null ? '"Freight_Allowance__c":"' + addrePro.Program__r.Freight_Allowance__c + '",' : '"Freight_Allowance__c":"",';
            addreInfoStr += addrePro.Program__r.Discount1__c != null ? '"Discount1__c":"' + addrePro.Program__r.Discount1__c + '",' : '"Discount1__c":"",';
            addreInfoStr += addrePro.Program__r.Discount2__c != null ? '"Discount2__c":"' + addrePro.Program__r.Discount2__c + '",' : '"Discount2__c":"",';
            addreInfoStr += addrePro.Program__r.Discount3__c != null ? '"Discount3__c":"' + addrePro.Program__r.Discount3__c + '",' : '"Discount3__c":"",';
            addreInfoStr += addrePro.Program__r.New_Store_Allowance__c != null ? '"New_Store_Allowance__c":"' + addrePro.Program__r.New_Store_Allowance__c + '",' : '"New_Store_Allowance__c":"",';
            addreInfoStr += addrePro.Status__c != null ? '"Status__c":"' + addrePro.Status__c + '",' : '"Status__c":"",';
            if (addrePro.Account_Address__r.ORG_ID__c == 'CCA') {
                String state = '';
                state = addrePro.Account_Address__r.State__c == 'ON' ? 'Ontario' : addrePro.Account_Address__r.State__c; //TODO
                addreInfoStr += addrePro.Account_Address__r.State__c != null ? '"Attribute1":"' + state + '",' : '"Attribute1":"",';
            } else {
                addreInfoStr += '"Attribute1":"",';
            }
            String subInventory = '';
            if (addrePro.Account_Address__r.RecordType.DeveloperName == 'Shipping_Address' || addrePro.Account_Address__r.RecordType.DeveloperName == 'Dropship_Shipping_Address'){
                subInventory = addrePro.Account_Address__r.Deliver_From__c;
                if(String.isBlank(subInventory)) {
                    if(subInventoryMap.containsKey(addrePro.Id)) {
                        subInventory = subInventoryMap.get(addrePro.Id);
                    }
                }
            }
            if(String.isBlank(subInventory)) {
                subInventory = '';
            }
            addreInfoStr += '"Attribute2":"' + subInventory + '",';
            addreInfoStr += '"Attribute3":"",';
            addreInfoStr += '"Attribute4":"",';
            addreInfoStr += '"Attribute5":"",';
            addreInfoStr += '"Attribute6":"",';
            addreInfoStr += '"Attribute7":"",';
            addreInfoStr += '"Attribute8":"",';
            addreInfoStr += '"Attribute9":"",';
            addreInfoStr += '"Attribute10":""';
            addreInfoStr += '},';
        }
        addreInfoStr = addreInfoStr.removeEnd(',') + ']';
        return addreInfoStr;
    }

    public static String getContactInfoMapList(List<String> contIdList) {
        String conInfoStr = '';
        String strKey;
        Set<String> setKey = new Set<String>();
        conInfoStr += '[';
        for (Address_Program_with_Contact__c cont : [
            SELECT Contact__c,
                Contact_SF_ID__c,
                Contact_Oracle_ID__c,
                Address_With_Program__r.Customer_Line_SF_ID__c,
                Address_With_Program__r.Customer_Line_Oracle_ID__c,
                Contact__r.Email,
                Contact__r.Phone,
                Contact__r.MobilePhone,
                Contact__r.FirstName,
                Contact__r.Fax,
                Contact__r.LastName,
                Status__c
            FROM Address_Program_with_Contact__c
            WHERE Id IN :contIdList
            ORDER BY Contact_Oracle_ID__c NULLS LAST
        ]) {
            // prettier-ignore
            if (String.isBlank(cont.Address_With_Program__r.Customer_Line_Oracle_ID__c) || String.isBlank(cont.Contact__c)) continue;
            strKey = cont.Address_With_Program__r.Customer_Line_Oracle_ID__c + cont.Contact__c;
            // prettier-ignore
            if (setKey.contains(strKey)) continue;
            setKey.add(strKey);
            conInfoStr += '{';
            conInfoStr += '"CONTACT_ID":"' + 'Salesforce-' + cont.Id + '-' + System.now().getTime() + '",';
            conInfoStr += cont.Contact_SF_ID__c != null ? '"Contact_SF_ID__c":"' + cont.Contact_SF_ID__c + '",' : '"Contact_SF_ID__c":"",';
            conInfoStr += cont.Contact_Oracle_ID__c != null ? '"Contact_Oracle_ID__c":"' + cont.Contact_Oracle_ID__c + '",' : '"Contact_Oracle_ID__c":"",';
            conInfoStr += cont.Address_With_Program__r.Customer_Line_SF_ID__c != null
                ? '"Customer_Line_SF_ID__c":"' + cont.Address_With_Program__r.Customer_Line_SF_ID__c + '",'
                : '"Customer_Line_SF_ID__c":"",';
            conInfoStr += cont.Address_With_Program__r.Customer_Line_Oracle_ID__c != null
                ? '"Customer_Line_Oracle_ID_c":"' + cont.Address_With_Program__r.Customer_Line_Oracle_ID__c + '",'
                : '"Customer_Line_Oracle_ID_c":"",';
            conInfoStr += cont.Contact__r.FirstName != null ? '"FirstName":"' + cont.Contact__r.FirstName + '",' : '"FirstName":"",';
            conInfoStr += cont.Contact__r.LastName != null ? '"LastName":"' + cont.Contact__r.LastName + '",' : '"LastName":"",';
            conInfoStr += cont.Contact__r.MobilePhone != null ? '"MobilePhone":"' + cont.Contact__r.MobilePhone + '",' : '"MobilePhone":"",';
            conInfoStr += cont.Contact__r.Phone != null ? '"Phone":"' + cont.Contact__r.Phone + '",' : '"Phone":"",';
            conInfoStr += cont.Contact__r.Fax != null ? '"Fax":"' + cont.Contact__r.Fax + '",' : '"Fax":"",';
            conInfoStr += cont.Contact__r.Email != null ? '"Email":"' + cont.Contact__r.Email + '",' : '"Email":"",';
            conInfoStr += cont.Status__c != null ? '"Status__c":"' + cont.Status__c + '",' : '"Status__c":"",';
            conInfoStr += '"Attribute1":"",';
            conInfoStr += '"Attribute2":"",';
            conInfoStr += '"Attribute3":"",';
            conInfoStr += '"Attribute4":"",';
            conInfoStr += '"Attribute5":"",';
            conInfoStr += '"Attribute6":"",';
            conInfoStr += '"Attribute7":"",';
            conInfoStr += '"Attribute8":"",';
            conInfoStr += '"Attribute9":"",';
            conInfoStr += '"Attribute10":""';
            conInfoStr += '},';
        }
        conInfoStr = conInfoStr.removeEnd(',') + ']';
        return conInfoStr;
    }

    public static String getAccountInfo(Account acct){
        // CCM_SubInventorySelector.initSubInventoryConfigurations();
        String currentYear = Date.today().year() + '';
        List<Customer_Profile__c> cpList = [SELECT Id, Summary_of_Potential_Actual_Sales__c,Payment_Behavior_Code__c FROM Customer_Profile__c WHERE Customer__c = :acct.Id AND Effective_Year__c = :currentYear];

        String acctInfoStr = '';
        acctInfoStr += '{';
        acctInfoStr += '"CUSTOMER_HEADER_ID":' + '"Salesforce-'+acct.Id +'-' + System.now().getTime() +'",';
        acctInfoStr += acct.Customer_SF_ID__c != null ? '"Customer_SF_ID__c":"'+ acct.Customer_SF_ID__c +'",' : '"Customer_SF_ID__c":"",';
        acctInfoStr += acct.Customer_Oracle_ID__c != null ? '"Customer_Oracle_ID__c":'+ '"'+acct.Customer_Oracle_ID__c +'",' : '"Customer_Oracle_ID__c":"",';
        acctInfoStr += acct.AccountNumber != null ? '"AccountNumber":"'+ acct.AccountNumber +'",' : '"AccountNumber":"",';
        acctInfoStr += acct.Customer_Cluster__c !=null ? '"Customer_Cluster__c":"'+ acct.Customer_Cluster__c +'",' : '"Customer_Cluster__c":"",';
        acctInfoStr += acct.Customer_Sub_Cluster__c !=null ? '"Customer_Sub_Cluster__c":"'+ acct.Customer_Sub_Cluster__c +'",' : '"Customer_Sub_Cluster__c":"",';
        acctInfoStr += acct.Ship_Complete__c !=null ? '"Ship_Complete__c":"'+ acct.Ship_Complete__c +'",' : '"Ship_Complete__c":"",';
        acctInfoStr += acct.Shipment_Priority__c !=null ? '"Shipment_Priority__c":"'+ acct.Shipment_Priority__c +'",' : '"Shipment_Priority__c":"",';
        acctInfoStr += acct.Sales_Channel__c !=null ? '"Sales_Channel__c":"'+ acct.Sales_Channel__c +'",' : '"Sales_Channel__c":"",';
        acctInfoStr += acct.Name !=null ? '"Name":"'+ acct.Name +'",' : '"Name":"",';
        acctInfoStr += acct.Credit_Limit__c !=null ? '"Cerdit__c":"'+ acct.Credit_Limit__c +'",' : '"Cerdit__c":"",';
        if(cpList.size() > 0){
            acctInfoStr += acct.Risk_Code__c !=null ? '"Risk_Code__c":"'+cpList[0].Payment_Behavior_Code__c+'-'+acct.Risk_Code__c +'",' : '"Risk_Code__c":"",';
        }else{
            acctInfoStr += acct.Risk_Code__c !=null ? '"Risk_Code__c":"'+ acct.Risk_Code__c +'",' : '"Risk_Code__c":"",';
        }
        
        acctInfoStr += '"Account_Type__c":' + '"External",';
        acctInfoStr += acct.CurrencyIsoCode !=null ? '"Currency":"'+ acct.CurrencyIsoCode +'",' : '"Currency":"",';
        acctInfoStr += acct.Invoicing_Method__c !=null ? '"Invoicing_Method__c":"'+ acct.Invoicing_Method__c +'",' : '"Invoicing_Method__c":"",';
        acctInfoStr += acct.Status__c !=null ? '"Status__c":"'+ acct.Status__c+'",' : '"Status__c":"",';
        acctInfoStr += acct.TaxID__c !=null ? '"Attribute1":"'+ acct.TaxID__c+'",' : '"Attribute1":"",';
        if(String.isBlank(acct.PaymentMethod__c) || acct.PaymentMethod__c.equalsIgnoreCase('N/A')) {
            acctInfoStr += '"Attribute2":"",';
        }
        else {
            acctInfoStr += '"Attribute2":"' + acct.PaymentMethod__c + '",';
        }
        acctInfoStr += cpList <> null && cpList.size() > 0 ? '"Attribute3":"' + cpList[0].Summary_of_Potential_Actual_Sales__c + '",' : '"Attribute3":"",';
        // String subInventory = getSubInventory(acct);
        acctInfoStr += '"Attribute4":"",';
        acctInfoStr += '"Attribute5":"",';
        acctInfoStr += '"Attribute6":"",';
        acctInfoStr += '"Attribute7":"",';
        acctInfoStr += '"Attribute8":"",';
        acctInfoStr += '"Attribute9":"",';
        acctInfoStr += '"Attribute10":""';
        acctInfoStr += '}';

        return acctInfoStr;
    }

    private static Map<String, String> getSubInventory(Map<String, Address_With_Program__c> idRecordMap, String customerId) {
        Map<String, String> subInventoryMap = new Map<String, String>();
        String ego_brand = 'EGO';
        Boolean hasEGOBrand = false;

        List<Sales_Program__c> authorizedBrandEGO = [SELECT Id FROM Sales_Program__c WHERE Customer__c = :customerId 
                                                     AND Approval_Status__c IN ('Approved', '') 
                                                     AND RecordTypeId IN (:CCM_Constants.SALES_PROGRAM_RECORD_TYPE_STANDARD_ID, :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_ID, :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_STANDARD_ID, :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_CUSTOMIZED_ID)
                                                     AND Brands__c = :ego_brand];
        if(!authorizedBrandEGO.isEmpty()) {
            hasEGOBrand = true;
        }

        if(hasEGOBrand) {
            for(String awpId : idRecordMap.keySet()) {
                Address_With_Program__c awp = idRecordMap.get(awpId);
                String subInventory = CCM_SubInventorySelector.getSubInventory(null, awp.Account_Address__r.State__c);
                if(String.isNotBlank(subInventory)) {
                    subInventoryMap.put(awpId, subInventory);
                }
            }
        }
        return subInventoryMap;
    }

    @future(callout=true)
    public static void syncToSeeburger(String param,String Type) {
        String response = syncData(param,Type);
        System.debug(LoggingLevel.INFO,'****** Response : ' + response);
    }

    public static String syncData(String param,String Type) {
        try{
            
            CCM_ValidationRelateListHandler.isRun = false;
            CCM_AccountUpdateHandler.isRun = false;
            CCM_AddressWithProgramUpdateHandler.isRun = false;
            CCM_AddProWithContactUpdateHandler.isRun = false;
            System.debug(LoggingLevel.INFO,'******开始同步 : ' + Type);
            
            String endPointName = 'chervon_seeburger_uat';                
            MeIntegration_Setting__mdt MeIS = [SELECT End_Point__c, Key__c FROM MeIntegration_Setting__mdt WHERE DeveloperName = : endPointName LIMIT 1];
            String endpoint = MeIS.End_Point__c + Type;
            String HeaderKey = 'Basic '+MeIS.Key__c;
            
            System.debug(LoggingLevel.INFO,'****** param data : ' + param);
            System.debug(LoggingLevel.INFO,'****** endPoint : ' + endPoint);

            HttpRequest req = new HttpRequest();
            HttpResponse res = new HttpResponse();

            req.setMethod('POST');
            req.setBody(param);
            req.setEndpoint(endPoint);
            req.setHeader('Authorization', HeaderKey);
            req.setHeader('Content-Type','application/json,charset=UTF-8');
            req.setTimeout(120000);

            if(!test.isRunningTest()){
                res = new Http().send(req);
                if (res.getStatusCode() != 200) {
                    System.debug(LoggingLevel.INFO, '*** res: ' + res);
                    return null;
                }
            }else{
                res.setBody('{"Process_Status":"Success","Process_Result":[{"SFDC_Id":"Cust-NA-111111111","Oracle_Id":"111111111"}]}');
            }
            System.debug(LoggingLevel.INFO,'****** res : ' + res);
            System.debug(LoggingLevel.INFO,'****** res body' + res.getBody());
            return res.getBody();
            
        }catch(Exception e){
            System.debug(LoggingLevel.INFO, '****** 接口异常 :');
            System.debug(LoggingLevel.INFO, '****** e.getMessage_e(): ' + e.getMessage());
            System.debug(LoggingLevel.INFO, '****** e.getLineNumber(): ' + e.getLineNumber());
            System.debug(LoggingLevel.INFO, '****** e.getCause(): ' + e.getCause());
            
            HttpResponse res = new HttpResponse();
            res.setBody('{"Process_Status": "Fail", "Process_Result": [{"Error_Message": "'+ e.getMessage()+'", "Error_Detail": "'+ e.getLineNumber() + ' : ' + e.getMessage() + '"}]}');
            return res.getBody();
        }
    }
}