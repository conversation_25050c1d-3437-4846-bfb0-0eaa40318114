<template>
    <template if:true={showSpinner}>
    <div
    class="spinner-wrapper"
    style="position: absolute; top: 0; bottom: 0; left: 0; right: 0"
    >
    <lightning-spinner
    alternative-text={label.CCM_Portal_Loading}
    size="medium"
    ></lightning-spinner>
    </div>
    </template>
    <div style="background: #fff" class="slds-p-vertical_small">
    <div class="width90 slds-container_center slds-m-bottom_medium">
    <lightning-progress-indicator
    current-step={currentStep}
    type="path"
    variant="base"
    >
    <template for:each={steps} for:item="step">
        <lightning-progress-step
            onclick={handleProgressStepClick}
            label={step.label}
            value={step.value}
            key={step.label}
        >
        </lightning-progress-step>
    </template>
    </lightning-progress-indicator>
    </div>
    <template if:true={isEdit}>
    <template if:true={firstStep}>
    <div class="width90 slds-container_center">
        <h2 class="title">{label.CCM_Portal_BasicInformation}</h2>
        <div>
            <div
                class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
            >
                <div class="slds-col slds-size_1-of-8">
                    {label.CCM_Portal_Customer}:
                </div>
                <template if:true={isInternal}>
                    <div class="slds-col slds-size_3-of-8">
                        <c-ccm-autocomplete
                            class="autoComplete customers"
                            required
                            label=""
                            default-value={selectedCustomer}
                            onselected={handleCustomerSelected}
                            onsearchinputchange={handleCustomerSearchInputChange}
                            data-type="single"
                            source={customerSource}
                            display-text={selectedCustomer.label}
                            title-field="AccountNumber"
                            content-field="label"
                            realtime-search={realtimeSearchCustomer}
                        >
                        </c-ccm-autocomplete>
                    </div>
                </template>
                <template if:false={isInternal}>
                    <div class="slds-col slds-size_3-of-8">
                        {customerName}
                    </div>
                </template>
            </div>
            <div
                class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
            >
                <div class="slds-col slds-size_1-of-8">
                    {label.CCM_Portal_PromotionCode}:
                </div>
                <div class="slds-col slds-size_2-of-8">
                    <!-- <lightning-input
                        type="text"
                        label="Promotion Code"
                        variant="label-hidden"
                        required
                        value={promotionCode}
                        onblur={handlePromotionCodeBlur}
                    >
                    </lightning-input> -->
                    <c-ccm-autocomplete
                        class="autoComplete promotionCode"
                        required
                        label=""
                        default-value={promotionCode}
                        onselected={handlePromotionCodeSelected}
                        onsearchinputchange={handlePromotionCodeSearchInputChange}
                        data-type="single"
                        source={promotionCodeSource}
                        display-text={promotionCode}
                        title-field="label"
                        content-field="description"
                    >
                    </c-ccm-autocomplete>
                </div>
            </div>

            <template if:true={promotionName}>
                <div
                    class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
                >
                    <div class="slds-col slds-size_1-of-8">
                        {label.CCM_Portal_PromotionName}:
                    </div>
                    <div class="slds-col slds-size_7-of-8">
                        {promotionName}
                    </div>
                </div>
            </template>

            <div
                class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
            >
                <div class="slds-col slds-size_1-of-8">
                    {label.CCM_Portal_PromotionWindows}:
                </div>
                <div class="slds-col">
                    <div class="ccm-pill-container">
                        <ul
                            class="slds-listbox slds-listbox_horizontal"
                            role="listbox"
                            aria-label="Selected Options:"
                            aria-orientation="horizontal"
                        >
                            <template
                                for:each={windows}
                                for:item="item"
                                for:index="index"
                            >
                                <template if:true={item.selected}>
                                    <li
                                        key={item.value}
                                        data-id={item.value}
                                        class="slds-listbox-item"
                                        role="presentation"
                                    >
                                        <span
                                            class="slds-pill window-pill window-selected"
                                            role="option"
                                            tabindex="0"
                                            aria-selected="true"
                                        >
                                            <span
                                                class="slds-pill__label"
                                                >{item.label}</span
                                            >
                                        </span>
                                    </li>
                                </template>
                                <template if:false={item.selected}>
                                    <li
                                        onclick={handleWindowClick}
                                        key={item.value}
                                        data-id={item.value}
                                        class="slds-listbox-item"
                                        role="presentation"
                                    >
                                        <span
                                            class="slds-pill window-pill"
                                            role="option"
                                            tabindex="0"
                                            aria-selected="true"
                                        >
                                            <span
                                                class="slds-pill__label"
                                                >{item.label}</span
                                            >
                                        </span>
                                    </li>
                                </template>
                            </template>
                        </ul>
                    </div>
                </div>
            </div>
            <div
                class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
            >
                <div class="slds-col slds-size_1-of-8">
                    {label.CCM_Portal_ReimbursementType}:
                </div>
                <div class="slds-col slds-size_2-of-8">
                    <lightning-combobox
                        name="type"
                        label="Type"
                        value={reimbursementType}
                        variant="label-hidden"
                        options={reimbursementTypes}
                        onchange={handleChangeReimbursementType}
                    >
                    </lightning-combobox>
                </div>
            </div>
            <div
                class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
            >
                <div class="slds-col slds-size_1-of-8">
                    {label.CCM_Portal_BillTo}:
                </div>
                <div class="slds-col slds-size_3-of-8">
                    <c-ccm-lwc-search-address
                        variant="label-hidden"
                        required
                        class="autoCompleteAddress"
                        label=""
                        onselected={handleAddressSelected}
                        source={addressSource}
                        onsearchinputchange={handleAddressSearchInputChange}
                    >
                    </c-ccm-lwc-search-address>
                    <template if:true={selectedAddress}>
                        <template if:true={selectedAddress.address}>
                            <lightning-formatted-address
                                street={selectedAddress.address.address1}
                                city={selectedAddress.address.city}
                                country={selectedAddress.address.country}
                                province={selectedAddress.address.state}
                                postal-code={selectedAddress.address.postalCode}
                            ></lightning-formatted-address>
                        </template>
                    </template>
                </div>
            </div>
            <div
                class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
            >
                <div class="slds-col slds-size_1-of-8">
                    {label.CCM_Portal_ShipTo}:
                </div>
                <div class="slds-col slds-size_3-of-8">
                    <c-ccm-lwc-search-address
                        variant="label-hidden"
                        required
                        class="shipToAutoCompleteAddress"
                        label=""
                        onselected={handleAddressSelected}
                        source={shipToAddressSource}
                        onsearchinputchange={handleAddressSearchInputChange}
                    >
                    </c-ccm-lwc-search-address>
                    <template if:true={shipToSelectedAddress}>
                        <template if:true={shipToSelectedAddress.address}>
                            <lightning-formatted-address
                                street={shipToSelectedAddress.address.address1}
                                city={shipToSelectedAddress.address.city}
                                country={shipToSelectedAddress.address.country}
                                province={shipToSelectedAddress.address.state}
                                postal-code={shipToSelectedAddress.address.postalCode}
                            ></lightning-formatted-address>
                        </template>
                    </template>
                </div>
            </div>
            <template if:true={isCCA}>
                <div
                    class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
                >
                    <div class="slds-col slds-size_1-of-8">
                        {label.CCM_Portal_GSTHST}:
                    </div>
                    <div class="slds-col slds-size_2-of-8">
                        <lightning-input
                            type="number"
                            step="0.01"
                            name="GSTOrHSTCurrency"
                            value={GSTOrHSTCurrency}
                            onchange={handleGSTOrHSTChange}
                            disabled = true
                            placeholder={label.CCM_Portal_TypeTheNumberOfItems}
                        ></lightning-input>
                    </div>
                </div>
                <div
                    class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
                >
                    <div class="slds-col slds-size_1-of-8">
                        {label.CCM_Portal_QST}:
                    </div>
                    <div class="slds-col slds-size_2-of-8">
                        <lightning-input
                            type="number"
                            step="0.01"
                            name="QSTCurrency"
                            value={QSTCurrency}
                            onchange={handleQSTChange}
                            disabled = true
                            placeholder={label.CCM_Portal_TypeTheNumberOfItems}
                        ></lightning-input>
                    </div>
                </div>
            </template>
            <div
                class="slds-grid slds-grid_vertical-align-top slds-m-bottom_small"
            >
                <div class="slds-col slds-size_1-of-8">
                    {label.CCM_Portal_ClaimDescription}:
                </div>
                <div class="slds-col slds-size_3-of-8">
                    <lightning-textarea
                        name="description"
                        variant="label-hidden"
                        label={label.CCM_Portal_ClaimDescription}
                        class="detail"
                        onblur={handleClaimDescriptionChange}
                        value={claimDescription}
                    >
                    </lightning-textarea>
                </div>
            </div>
        </div>

        <template if:true={hasThresholdProduct}>
            <div class="slds-m-vertical_small">
                <h2 class="title">{label.CCM_Portal_PromotionalItems}</h2>
                <table
                    class="slds-table slds-table_cell-buffer slds-table_bordered createClaimTable"
                >
                    <thead>
                        <tr class="slds-line-height_reset">
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 10%"
                            >
                                <div
                                    class="slds-truncate"
                                    title=""
                                >
                                    {label.CCM_Portal_Line}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div
                                    class="slds-truncate"
                                    title=""
                                >
                                    {label.CCM_Portal_Model}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_MSRP}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_PromotionPrice}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_SellInPrice}
                                </div>
                            </th>
                            <template if:false={isBMSM}>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div class="slds-truncate" title="">
                                        {label.CCM_Portal_ChervonFundedAmount}
                                    </div>
                                </th>
                            </template>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_SalePrice}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_Quantity}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_TotalSales}
                                </div>
                            </th>
                            <template if:false={isBMSM}>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div class="slds-truncate" title="">
                                        {label.CCM_Portal_ClaimAmount}
                                    </div>
                                </th>
                            </template>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <template
                            iterator:claim={claimItemsThreshold}
                        >
                            <tr key={claim.value.lineNo}>
                                <td scope="row">
                                    {claim.value.lineNo}
                                </td>
                                <td scope="row">
                                    <c-ccm-autocomplete
                                        class="autoComplete"
                                        required
                                        label=""
                                        default-value={claim.value.productCode}
                                        onselected={handleProductSelectedThreshold}
                                        onsearchinputchange={handleSearchInputChange}
                                        data-type="single"
                                        data-index={claim.index}
                                        source={productSourceThreshold}
                                        display-text={claim.value.productCode}
                                        title-field="label"
                                        content-field="description"
                                        realtime-search={realtimeSearch}
                                    >
                                    </c-ccm-autocomplete>
                                </td>
                                <td scope="row">
                                    <lightning-formatted-number
                                        value={claim.value.MSRP}
                                        format-style="currency"
                                        currency-code={currencyCode}
                                    >
                                    </lightning-formatted-number>
                                </td>
                                <td scope="row">
                                    <lightning-formatted-number
                                        value={claim.value.promoPrice}
                                        format-style="currency"
                                        currency-code={currencyCode}
                                    >
                                    </lightning-formatted-number>
                                </td>
                                <td scope="row">
                                    <lightning-formatted-number
                                        value={claim.value.sellInPrice}
                                        format-style="currency"
                                        currency-code={currencyCode}
                                    >
                                    </lightning-formatted-number>
                                </td>
                                <template if:false={isBMSM}>
                                    <td scope="row">
                                        <lightning-formatted-number
                                            value={claim.value.chervonFundedAmount}
                                            format-style="currency"
                                            currency-code={currencyCode}
                                        >
                                        </lightning-formatted-number>
                                    </td>
                                </template>
                                <td scope="row">
                                    <lightning-input
                                        variant="label-hidden"
                                        required
                                        data-index={claim.index}
                                        data-type="threshold"
                                        type="number"
                                        step="0.01"
                                        min={claim.value.salePriceMin}
                                        max={claim.value.salePriceMax}
                                        message-when-range-overflow={label.CCM_Portal_Invalidclaimamount}
                                        message-when-range-underflow={label.CCM_Portal_Invalidclaimamount}
                                        value={claim.value.salePrice}
                                        onblur={handleSalePriceBlur}
                                    >
                                    </lightning-input>
                                    <!-- <lightning-input
                                        variant="label-hidden"
                                        required
                                        data-index={claim.index}
                                        data-type="threshold"
                                        type="number"
                                        step="0.01"
                                        min={claim.value.salePriceMin}
                                        max={claim.value.salePriceMax}
                                        message-when-range-overflow={label.CCM_Portal_Invalidclaimamount}
                                        message-when-range-underflow={label.CCM_Portal_Invalidclaimamount}
                                        value={claim.value.salePrice}
                                        onchange={handleSalePriceChange}
                                        onblur={handleSalePriceBlur}>
                                    </lightning-input> -->
                                </td>
                                <td scope="row">
                                    <lightning-input
                                        variant="label-hidden"
                                        required
                                        data-index={claim.index}
                                        data-type="threshold"
                                        type="number"
                                        step="1"
                                        value={claim.value.quantity}
                                        onblur={handleQuantityChange}
                                    >
                                    </lightning-input>
                                </td>
                                <td>
                                    <lightning-formatted-number
                                        value={claim.value.scaleTotalSales}
                                        format-style="currency"
                                        currency-code={currencyCode}
                                    >
                                    </lightning-formatted-number>
                                </td>
                                <template if:false={isBMSM}>
                                    <td>
                                        <lightning-formatted-number
                                            value={claim.value.scaleAmount}
                                            format-style="currency"
                                            currency-code={currencyCode}
                                        >
                                        </lightning-formatted-number>
                                    </td>
                                </template>
                                <td>
                                    <lightning-icon
                                        icon-name="utility:delete"
                                        alternative-text="Delete"
                                        title="Delete"
                                        size="x-small"
                                        data-index={claim.index}
                                        data-type="threshold"
                                        onclick={handleDeleteClaimItem}
                                    >
                                    </lightning-icon>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
                <div class="slds-m-top_medium">
                    <lightning-button
                        variant="brand"
                        label={label.CCM_Portal_AddItemSymbol}
                        onclick={handleClickAddItemThreshold}
                    ></lightning-button>
                </div>
            </div>
        </template>

        <!-- add by austin,sell through whole order -->
        <template if:true={stWholeOrder}>
            <!-- <template if:true={woPromitionalDis}> -->
                <div class="slds-m-vertical_small">
                    <h2 class="title">{label.CCM_Portal_PromotionalItems}</h2>
                    <table
                        class="slds-table slds-table_cell-buffer slds-table_bordered createClaimTable"
                    >
                        <thead>
                            <tr class="slds-line-height_reset">
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 10%"
                                >
                                    <div
                                        class="slds-truncate"
                                        title=""
                                    >
                                        {label.CCM_Portal_Line}
                                    </div>
                                </th>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div
                                        class="slds-truncate"
                                        title=""
                                    >
                                        {label.CCM_Portal_Model}
                                    </div>
                                </th>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div class="slds-truncate" title="">
                                        {label.CCM_Portal_MSRP}
                                    </div>
                                </th>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div class="slds-truncate" title="">
                                        {label.CCM_Portal_SalePrice}
                                    </div>
                                </th>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div class="slds-truncate" title="">
                                        {label.CCM_Portal_Quantity}
                                    </div>
                                </th>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div class="slds-truncate" title="">
                                        {label.CCM_Portal_TotalSales}
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <template
                                iterator:claim={claimItemsThreshold}
                            >
                                <tr key={claim.value.lineNo}>
                                    <td scope="row">
                                        {claim.value.lineNo}
                                    </td>
                                    <td scope="row">
                                        <c-ccm-autocomplete
                                            class="autoComplete"
                                            required
                                            label=""
                                            default-value={claim.value.productCode}
                                            onselected={handleProductSelectedWholeOrderThreshold}
                                            onsearchinputchange={handleSearchInputChange}
                                            data-type="single"
                                            data-index={claim.index}
                                            source={productSourceThreshold}
                                            display-text={claim.value.productCode}
                                            title-field="label"
                                            content-field="description"
                                            realtime-search=true
                                        >
                                        </c-ccm-autocomplete>
                                    </td>
                                    <template if:true={claim.value.displayMSRP}>
                                        <td scope="row">
                                            <lightning-formatted-number
                                                value={claim.value.MSRP}
                                                format-style="currency"
                                                currency-code={currencyCode}
                                            >
                                            </lightning-formatted-number>
                                        </td>
                                    </template>
                                    <template if:false={claim.value.displayMSRP}>
                                        <td scope="row">
                                            {label.CCM_Portal_NA}
                                        </td>
                                    </template>
                                    <td scope="row">
                                        <lightning-input
                                            variant="label-hidden"
                                            required
                                            data-index={claim.index}
                                            data-type="threshold"
                                            type="number"
                                            step="0.01"
                                            value={claim.value.salePrice}
                                            onblur={handleSaleSellthroughPriceBlur}
                                        >
                                        </lightning-input>
                                    </td>
                                    <td scope="row">
                                        <lightning-input
                                            variant="label-hidden"
                                            required
                                            data-index={claim.index}
                                            data-type="threshold"
                                            type="number"
                                            step="1"
                                            value={claim.value.quantity}
                                            onblur={handleQuantityChangeWholeOrder}
                                        >
                                        </lightning-input>
                                    </td>
                                    <td>
                                        <lightning-formatted-number
                                            value={claim.value.scaleTotalSales}
                                            format-style="currency"
                                            currency-code={currencyCode}
                                        >
                                        </lightning-formatted-number>
                                    </td>
                                    <td>
                                        <lightning-icon
                                            icon-name="utility:delete"
                                            alternative-text={label.CCM_Portal_Delete}
                                            title={label.CCM_Portal_Delete}
                                            size="x-small"
                                            data-index={claim.index}
                                            data-type="threshold"
                                            onclick={handleDeleteClaimItem}
                                        >
                                        </lightning-icon>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                    <div class="slds-m-top_medium">
                        <lightning-button
                            variant="brand"
                            label={label.CCM_Portal_AddItemSymbol}
                            onclick={handleClickAddItemThreshold}
                        ></lightning-button>
                    </div>
                </div>
            </template>
        <!-- </template> -->
        <!-- end -->
        <template if:true={hasOfferingProduct}>
            <div class="slds-m-vertical_small">
                <h2 class="title">{label.CCM_Portal_FreeGoods}</h2>
                <table
                    class="slds-table slds-table_cell-buffer slds-table_bordered createClaimTable offeringTable"
                >
                    <thead>
                        <tr class="slds-line-height_reset">
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 10%"
                            >
                                <div
                                    class="slds-truncate"
                                    title=""
                                >
                                    {label.CCM_Portal_Line}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div
                                    class="slds-truncate"
                                    title=""
                                >
                                    {label.CCM_Portal_Model}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_MSRP}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_PromotionPrice}
                                </div>
                            </th>
                            <template if:false={isBMSM}>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div class="slds-truncate" title="">
                                        {label.CCM_Portal_ChervonFundedAmount}
                                    </div>
                                </th>
                            </template>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_SalePrice}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_Quantity}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_TotalSales}
                                </div>
                            </th>
                            <template if:false={isBMSM}>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div class="slds-truncate" title="">
                                        {label.CCM_Portal_ClaimAmount}
                                    </div>
                                </th>
                            </template>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <template
                            iterator:claim={claimItemsOffering}
                        >
                            <tr key={claim.value.lineNo}>
                                <td scope="row">
                                    {claim.value.lineNo}
                                </td>
                                <td scope="row">
                                    <c-ccm-autocomplete
                                        class="autoComplete"
                                        required
                                        label=""
                                        default-value={claim.value.productCode}
                                        onselected={handleProductSelectedOffering}
                                        onsearchinputchange={handleSearchInputChange}
                                        data-type="single"
                                        data-index={claim.index}
                                        source={productSourceOffering}
                                        display-text={claim.value.productCode}
                                        title-field="label"
                                        content-field="description"
                                        realtime-search={realtimeSearch}
                                    >
                                    </c-ccm-autocomplete>
                                </td>
                                <td scope="row">
                                    <lightning-formatted-number
                                        value={claim.value.MSRP}
                                        format-style="currency"
                                        currency-code={currencyCode}
                                    >
                                    </lightning-formatted-number>
                                </td>
                                <td scope="row">
                                    <lightning-formatted-number
                                        value={claim.value.promoPrice}
                                        format-style="currency"
                                        currency-code={currencyCode}
                                    >
                                    </lightning-formatted-number>
                                </td>
                                <template if:false={isBMSM}>
                                    <td scope="row">
                                        <lightning-formatted-number
                                            value={claim.value.chervonFundedAmount}
                                            format-style="currency"
                                            currency-code={currencyCode}
                                        >
                                        </lightning-formatted-number>
                                    </td>
                                </template>
                                <td scope="row">
                                    <template if:true={isPMAPP}>
                                        <lightning-input
                                            variant="label-hidden"
                                            required
                                            data-index={claim.index}
                                            data-type="offering"
                                            type="number"
                                            step="0.01"
                                            min={claim.value.salePriceMin}
                                            max={claim.value.salePriceMax}
                                            message-when-range-overflow={label.CCM_Portal_Invalidclaimamount}
                                            message-when-range-underflow={label.CCM_Portal_Invalidclaimamount}
                                            value={claim.value.salePrice}
                                            onblur={handleSalePriceBlur}
                                        >
                                        </lightning-input>
                                        <!-- <lightning-input
                                            variant="label-hidden"
                                            required
                                            data-index={claim.index}
                                            data-type="offering"
                                            type="number"
                                            step="0.01"
                                            min={claim.value.salePriceMin}
                                            max={claim.value.salePriceMax}
                                            message-when-range-overflow={label.CCM_Portal_Invalidclaimamount}
                                            message-when-range-underflow={label.CCM_Portal_Invalidclaimamount}
                                            value={claim.value.salePrice}
                                            onchange={handleSalePriceChange}
                                            onblur={handleSalePriceBlur}>
                                        </lightning-input> -->
                                    </template>
                                    <template if:false={isPMAPP}>
                                        <lightning-formatted-number
                                            value="0"
                                            format-style="currency"
                                            currency-code={currencyCode}
                                        >
                                        </lightning-formatted-number>
                                    </template>
                                </td>
                                <td scope="row">
                                    <lightning-input
                                        variant="label-hidden"
                                        required
                                        data-index={claim.index}
                                        data-type="offering"
                                        type="number"
                                        step="1"
                                        value={claim.value.quantity}
                                        onchange={handleQuantityChange}
                                    >
                                    </lightning-input>
                                </td>
                                <td>
                                    <template if:true={isPMAPP}>
                                        <lightning-formatted-number
                                            value={claim.value.scaleTotalSales}
                                            format-style="currency"
                                            currency-code={currencyCode}
                                        >
                                        </lightning-formatted-number>
                                    </template>
                                    <template if:false={isPMAPP}>
                                        <lightning-formatted-number
                                            value="0"
                                            format-style="currency"
                                            currency-code={currencyCode}
                                        >
                                        </lightning-formatted-number>
                                    </template>
                                </td>
                                <template if:false={isBMSM}>
                                    <td>
                                        <lightning-formatted-number
                                            value={claim.value.scaleAmount}
                                            format-style="currency"
                                            currency-code={currencyCode}
                                        >
                                        </lightning-formatted-number>
                                    </td>
                                </template>
                                <td>
                                    <lightning-icon
                                        icon-name="utility:delete"
                                        alternative-text={label.CCM_Portal_Delete}
                                        title={label.CCM_Portal_Delete}
                                        size="x-small"
                                        data-index={claim.index}
                                        data-type="offering"
                                        onclick={handleDeleteClaimItem}
                                    >
                                    </lightning-icon>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
                <div class="slds-m-top_medium">
                    <lightning-button
                        variant="brand"
                        label={label.CCM_Portal_AddItemSymbol}
                        onclick={handleClickAddItemOffering}
                    ></lightning-button>
                </div>
            </div>
        </template>
        <template if:true={showNextButton}>
            <div class="slds-m-top_medium">
                <div class="slds-grid slds-grid_align-end">
                    <div class="slds-col">
                        <template if:true={isInternal}>
                            <template
                                for:each={offeringSummaryList}
                                for:item="summary"
                                for:index="index"
                            >
                                <div
                                    key={summary.label}
                                    class="slds-text-align_right slds-p-right_small"
                                >
                                    {summary.label}:
                                </div>
                            </template>
                        </template>
                        <template if:true={isBMSM}>
                            <div class="slds-text-align_right slds-p-right_small slds-text-heading_small">{label.CCM_Portal_TotalSalesAmount_SellThroughClaim}:</div>
                            <div class="slds-text-align_right slds-p-right_small slds-text-heading_small">{label.CCM_Portal_TotalDiscountAmount}:</div>
                        </template>
                        <template if:false={isCCA}>
                            <div
                                class="slds-text-align_right slds-p-right_small slds-text-heading_small"
                            >
                                {label.CCM_Portal_TotalClaimAmount}:
                            </div>
                        </template>
                        <template if:true={isCCA}>
                            <div
                                class="slds-text-align_right slds-p-right_small slds-text-heading_small"
                            >
                                {label.CCM_Portal_TotalClaimAmountTaxExclusive}:
                            </div>
                            <div
                                class="slds-text-align_right slds-p-right_small slds-text-heading_small"
                            >
                                {label.CCM_Portal_TotalClaimAmountTaxInclusive}:
                            </div>
                        </template>
                    </div>
                    <div class="slds-col">
                        <template if:true={isInternal}>
                            <template
                                for:each={offeringSummaryList}
                                for:item="summary"
                                for:index="index"
                            >
                                <div key={summary.label}>
                                    <lightning-formatted-number
                                        value={summary.value}
                                        format-style="currency"
                                        currency-code={currencyCode}
                                    >
                                    </lightning-formatted-number>
                                </div>
                            </template>
                        </template>
                        <template if:true={isBMSM}>
                            <div class="slds-text-heading_small">
                                <lightning-formatted-number
                                    value={totalSalesAmount}
                                    format-style="currency"
                                    currency-code={currencyCode}
                                >
                                </lightning-formatted-number>
                            </div>
                            <div class="slds-text-heading_small">
                                <lightning-formatted-number
                                    value={totalDiscountAmount}
                                    format-style="currency"
                                    currency-code={currencyCode}
                                >
                                </lightning-formatted-number>
                            </div>
                        </template>
                        <template if:false={isCCA}>
                            <div class="slds-text-heading_small">
                                <lightning-formatted-number
                                    value={totalClaimAmount}
                                    format-style="currency"
                                    currency-code={currencyCode}
                                >
                                </lightning-formatted-number>
                            </div>
                        </template>
                        <template if:true={isCCA}>
                            <div class="slds-text-heading_small">
                                <lightning-formatted-number
                                    value={totalClaimAmount}
                                    format-style="currency"
                                    currency-code={currencyCode}
                                >
                                </lightning-formatted-number>
                            </div>
                            <div class="slds-text-heading_small">
                                <lightning-formatted-number
                                    value={totalClaimAmountInclusive}
                                    format-style="currency"
                                    currency-code={currencyCode}
                                >
                                </lightning-formatted-number>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
            <div
                class="slds-m-top_medium slds-grid slds-grid_align-end"
            >
                <lightning-button
                    variant="brand"
                    class="slds-m-left_medium"
                    label={saveButtonLabel}
                    onclick={handleClickSave}
                ></lightning-button>
            </div>
        </template>
    </div>
    </template>
    <template if:true={secondStep}>
    <div class="width90 slds-container_center">
        <!-- <template if:false={isBeam}> -->
        <h2 class="title">{label.CCM_Portal_UploadFiles}</h2>

        <div class="slds-m-top_medium slds-grid">
            <div class="slds-col slds-size_8-of-8">
                <h3 class="title-h3">
                    {label.CCM_Portal_PleaseuploadsupportingPOSDocumentation}
                </h3>
                <lightning-file-upload
                    label=""
                    variant="label-hidden"
                    name="fileUploader"
                    multiple="true"
                    accept={allowExtensions}
                    onuploadfinished={handleUploadFinished}
                >
                </lightning-file-upload>
            </div>
        </div>
        <!-- </template> -->

        <div class="slds-m-around_small">
            <h3 class="slds-card__header-title">{label.CCM_Portal_Files}</h3>
            <table
                class="slds-table slds-table_cell-buffer slds-table_bordered"
            >
                <thead>
                    <tr class="slds-line-height_reset">
                        <th
                            class="slds-text-title_caps"
                            scope="col"
                            style="width: 10%"
                        >
                            <div
                                class="slds-truncate"
                                title=""
                            >
                                {label.CCM_Portal_Line}
                            </div>
                        </th>
                        <th
                            class="slds-text-title_caps"
                            scope="col"
                            style="width: 50%"
                        >
                            <div
                                class="slds-truncate"
                                title=""
                            >
                            {label.CCM_Portal_File}
                            </div>
                        </th>
                        <th
                            class="slds-text-title_caps"
                            scope="col"
                            style="width: 20%"
                        >
                            <div
                                class="slds-truncate"
                                title=""
                            ></div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <template iterator:file={uploadedFiles}>
                        <tr key={file.value.documentId}>
                            <th scope="row">{file.value.lineNo}</th>
                            <th scope="row">
                                <div class="slds-truncate">
                                    <a
                                        onclick={navigateToFiles}
                                        data-docid={file.value.documentId}
                                        data-versionid={file.value.contentVersionId}
                                    >
                                        {file.value.name}
                                    </a>
                                    <!-- <a href={file.value.previewUrl} target="_blank">
                                        {file.value.name}
                                    </a> -->
                                </div>
                            </th>
                            <th scope="row">
                                <div class="slds-truncate">
                                    <!-- <template if:false={isBeam}> -->
                                    <lightning-button
                                        data-index={file.index}
                                        variant="neutral"
                                        class="slds-m-left_medium"
                                        label={label.CCM_Portal_Delete}
                                        data-docid={file.value.documentId}
                                        onclick={handleDeleteFile}
                                    ></lightning-button>
                                    <!-- </template> -->
                                </div>
                            </th>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>

        <div class="slds-m-around_small">
            <div
                class="slds-m-top_medium slds-grid slds-grid_align-end"
            >
                <lightning-button
                    variant="neutral"
                    class="slds-m-left_medium"
                    label={label.CCM_Portal_Prev}
                    onclick={handleClickEditClaim}
                ></lightning-button>
                <!-- <lightning-button
                    variant="neutral"
                    class="slds-m-left_medium"
                    label="Cancel"
                    onclick={handleClickCancel}
                ></lightning-button>
                <lightning-button
                    variant="brand"
                    class="slds-m-left_medium"
                    label="Save"
                    onclick={handleClickSaveClaim}
                ></lightning-button> -->

                <lightning-button
                    variant="brand"
                    class="slds-m-left_medium"
                    label={label.CCM_Portal_Submit}
                    onclick={handleClickSubmit}
                ></lightning-button>
            </div>
        </div>
    </div>
    </template>
    </template>

    <template if:false={isEdit}>
    <template if:true={firstStep}>
    <div class="width90 slds-container_center">
        <h2 class="title">{label.CCM_Portal_BasicInformation}</h2>
        <div>
            <div
                class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
            >
                <div class="slds-col slds-size_1-of-8">
                    {label.CCM_Portal_Customer}:
                </div>
                <template if:true={isInternal}>
                    <div class="slds-col slds-size_3-of-8">
                        {selectedCustomer.label}
                    </div>
                </template>
                <template if:false={isInternal}>
                    <div class="slds-col slds-size_3-of-8">
                        {customerName}
                    </div>
                </template>
            </div>
            <div
                class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
            >
                <div class="slds-col slds-size_1-of-8">
                    {label.CCM_Portal_PromotionCode}:
                </div>
                <div class="slds-col slds-size_2-of-8">
                    {promotionCode}
                </div>
            </div>
            <div
                class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
            >
                <div class="slds-col slds-size_1-of-8">
                    {label.CCM_Portal_PromotionName}:
                </div>
                <div class="slds-col slds-size_7-of-8">
                    {promotionName}
                </div>
            </div>
            <div
                class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
            >
                <div class="slds-col slds-size_1-of-8">
                    {label.CCM_Portal_PromotionWindows}:
                </div>
                <div class="slds-col">
                    <div class="ccm-pill-container">
                        <ul
                            class="slds-listbox slds-listbox_horizontal"
                            role="listbox"
                            aria-label="Selected Options:"
                            aria-orientation="horizontal"
                        >
                            <template
                                for:each={windows}
                                for:item="item"
                                for:index="index"
                            >
                                <template if:true={item.selected}>
                                    <li
                                        key={item.value}
                                        data-id={item.value}
                                        class="slds-listbox-item"
                                        role="presentation"
                                    >
                                        <span
                                            class="slds-pill window-pill window-selected"
                                            role="option"
                                            tabindex="0"
                                            aria-selected="true"
                                        >
                                            <span
                                                class="slds-pill__label"
                                                >{item.label}</span
                                            >
                                        </span>
                                    </li>
                                </template>
                                <template if:false={item.selected}>
                                    <li
                                        key={item.value}
                                        data-id={item.value}
                                        class="slds-listbox-item"
                                        role="presentation"
                                    >
                                        <span
                                            class="slds-pill window-pill"
                                            role="option"
                                            tabindex="0"
                                            aria-selected="true"
                                        >
                                            <span
                                                class="slds-pill__label"
                                                >{item.label}</span
                                            >
                                        </span>
                                    </li>
                                </template>
                            </template>
                        </ul>
                    </div>
                </div>
            </div>
            <div
                class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
            >
                <div class="slds-col slds-size_1-of-8">
                    {label.CCM_Portal_ReimbursementType}:
                </div>
                <div class="slds-col slds-size_2-of-8">
                    {reimbursementType}
                </div>
            </div>
            <div
                class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
            >
                <div class="slds-col slds-size_1-of-8">
                    {label.CCM_Portal_BillTo}:
                </div>
                <div class="slds-col slds-size_3-of-8">
                    <template if:true={selectedAddress}>
                        <template if:true={selectedAddress.address}>
                            <lightning-formatted-address
                                street={selectedAddress.address.address1}
                                city={selectedAddress.address.city}
                                country={selectedAddress.address.country}
                                province={selectedAddress.address.state}
                                postal-code={selectedAddress.address.postalCode}
                            ></lightning-formatted-address>
                        </template>
                    </template>
                </div>
            </div>
            <div
                class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
            >
                <div class="slds-col slds-size_1-of-8">
                    {label.CCM_Portal_ShipTo}:
                </div>
                <div class="slds-col slds-size_3-of-8">
                    <template if:true={shipToSelectedAddress}>
                        <template if:true={shipToSelectedAddress.address}>
                            <lightning-formatted-address
                                street={shipToSelectedAddress.address.address1}
                                city={shipToSelectedAddress.address.city}
                                country={shipToSelectedAddress.address.country}
                                province={shipToSelectedAddress.address.state}
                                postal-code={shipToSelectedAddress.address.postalCode}
                            ></lightning-formatted-address>
                        </template>
                    </template>
                </div>
            </div>

            <template if:true={isCCA}>
                <div
                    class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
                >
                    <div class="slds-col slds-size_1-of-8">
                        {label.CCM_Portal_GSTHST}:
                    </div>
                    <div class="slds-col slds-size_2-of-8">
                        <lightning-formatted-number
                            value={GSTOrHSTCurrency}
                            format-style="currency"
                            currency-code={currencyCode}
                        >
                        </lightning-formatted-number>
                    </div>
                </div>
                <div
                    class="slds-grid slds-grid_vertical-align-center slds-m-bottom_small"
                >
                    <div class="slds-col slds-size_1-of-8">
                        {label.CCM_Portal_QST}:
                    </div>
                    <div class="slds-col slds-size_2-of-8">
                        <lightning-formatted-number
                            value={QSTCurrency}
                            format-style="currency"
                            currency-code={currencyCode}
                        >
                        </lightning-formatted-number>
                    </div>
                </div>
            </template>

            <div
                class="slds-grid slds-grid_vertical-align-top slds-m-bottom_small"
            >
                <div class="slds-col slds-size_1-of-8">
                    {label.CCM_Portal_ClaimDescription}:
                </div>
                <div class="slds-col slds-size_3-of-8">
                    <lightning-formatted-text
                        value={claimDescription}
                        linkify
                    ></lightning-formatted-text>
                </div>
            </div>
            <div
                class="slds-grid slds-grid_vertical-align-top slds-m-bottom_small"
            >
                <div class="slds-col slds-size_1-of-8">
                    {label.CCM_Portal_ClaimStatus}:
                </div>
                <div class="slds-col slds-size_3-of-8">
                    {statusText}
                </div>
            </div>
            <template if:true={isRejected}>
                <div
                    class="slds-grid slds-grid_vertical-align-top slds-m-bottom_small"
                >
                    <div class="slds-col slds-size_1-of-8">
                        {label.CCM_Portal_Reason}:
                    </div>
                    <div class="slds-col slds-size_3-of-8">
                        <lightning-formatted-text
                            value={rejectReason}
                            linkify
                        ></lightning-formatted-text>
                    </div>
                </div>
            </template>
        </div>

        <template if:true={hasThresholdProduct}>
            <div class="slds-m-vertical_small">
                <h2 class="title">{label.CCM_Portal_PromotionalItems}</h2>
                <table
                    class="slds-table slds-table_cell-buffer slds-table_bordered createClaimTable"
                >
                    <thead>
                        <tr class="slds-line-height_reset">
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 10%"
                            >
                                <div
                                    class="slds-truncate"
                                    title=""
                                >
                                    {label.CCM_Portal_Line}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 50%"
                            >
                                <div
                                    class="slds-truncate"
                                    title=""
                                >
                                    {label.CCM_Portal_Model}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_MSRP}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_PromotionPrice}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_SellInPrice}
                                </div>
                            </th>
                            <template if:false={isBMSM}>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div class="slds-truncate" title="">
                                        {label.CCM_Portal_ChervonFundedAmount}
                                    </div>
                                </th>
                            </template>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_SalePrice}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_Quantity}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_TotalSales}
                                </div>
                            </th>
                            <template if:false={isBMSM}>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div class="slds-truncate" title="">
                                        {label.CCM_Portal_ClaimAmount}
                                    </div>
                                </th>
                            </template>
                        </tr>
                    </thead>
                    <tbody>
                        <template
                            iterator:claim={claimItemsThreshold}
                        >
                            <tr key={claim.value.lineNo}>
                                <td scope="row">
                                    {claim.value.lineNo}
                                </td>
                                <td scope="row">
                                    {claim.value.productCode}
                                </td>
                                <td scope="row">
                                    <lightning-formatted-number
                                        value={claim.value.MSRP}
                                        format-style="currency"
                                        currency-code={currencyCode}
                                    >
                                    </lightning-formatted-number>
                                </td>
                                <td scope="row">
                                    <lightning-formatted-number
                                        value={claim.value.promoPrice}
                                        format-style="currency"
                                        currency-code={currencyCode}
                                    >
                                    </lightning-formatted-number>
                                </td>
                                <td scope="row">
                                    <lightning-formatted-number
                                        value={claim.value.sellInPrice}
                                        format-style="currency"
                                        currency-code={currencyCode}
                                    >
                                    </lightning-formatted-number>
                                </td>
                                <template if:false={isBMSM}>
                                    <td scope="row">
                                        <lightning-formatted-number
                                            value={claim.value.chervonFundedAmount}
                                            format-style="currency"
                                            currency-code={currencyCode}
                                        >
                                        </lightning-formatted-number>
                                    </td>
                                </template>
                                <td scope="row">
                                    <lightning-formatted-number
                                        value={claim.value.salePrice}
                                        format-style="currency"
                                        currency-code={currencyCode}
                                    >
                                    </lightning-formatted-number>
                                </td>
                                <td scope="row">
                                    <lightning-formatted-number
                                        value={claim.value.quantity}
                                    >
                                    </lightning-formatted-number>
                                </td>
                                <td>
                                    <lightning-formatted-number
                                        value={claim.value.scaleTotalSales}
                                        format-style="currency"
                                        currency-code={currencyCode}
                                    >
                                    </lightning-formatted-number>
                                </td>
                                <template if:false={isBMSM}>
                                    <td>
                                        <lightning-formatted-number
                                            value={claim.value.scaleAmount}
                                            format-style="currency"
                                            currency-code={currencyCode}
                                        >
                                        </lightning-formatted-number>
                                    </td>
                                </template>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </template>
        <!-- add by austin,sell through whole order -->
        <template if:true={stWholeOrder}>
            <!-- <template if:true={woPromitionalDis}> -->
            <div class="slds-m-vertical_small">
                    <h2 class="title">{label.CCM_Portal_PromotionalItems}</h2>
                    <table
                        class="slds-table slds-table_cell-buffer slds-table_bordered createClaimTable"
                    >
                        <thead>
                            <tr class="slds-line-height_reset">
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 10%"
                                >
                                    <div
                                        class="slds-truncate"
                                        title=""
                                    >
                                        {label.CCM_Portal_Line}
                                    </div>
                                </th>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div
                                        class="slds-truncate"
                                        title=""
                                    >
                                        {label.CCM_Portal_Model}
                                    </div>
                                </th>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div class="slds-truncate" title="">
                                        {label.CCM_Portal_MSRP}
                                    </div>
                                </th>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div class="slds-truncate" title="">
                                        {label.CCM_Portal_SalePrice}
                                    </div>
                                </th>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div class="slds-truncate" title="">
                                        {label.CCM_Portal_Quantity}
                                    </div>
                                </th>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div class="slds-truncate" title="">
                                        {label.CCM_Portal_TotalSales}
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <template
                                iterator:claim={claimItemsThreshold}
                            >
                                <tr key={claim.value.lineNo}>
                                    <td scope="row">
                                        {claim.value.lineNo}
                                    </td>
                                    <td scope="row">
                                        <c-ccm-autocomplete
                                            class="autoComplete"
                                            required
                                            label=""
                                            default-value={claim.value.productCode}
                                            onselected={handleProductSelectedWholeOrderThreshold}
                                            onsearchinputchange={handleSearchInputChange}
                                            data-type="single"
                                            data-index={claim.index}
                                            source={productSourceThreshold}
                                            display-text={claim.value.productCode}
                                            title-field="label"
                                            content-field="description"
                                            realtime-search=true
                                        >
                                        </c-ccm-autocomplete>
                                    </td>
                                    <template if:true={claim.value.displayMSRP}>
                                        <td scope="row">
                                            <lightning-formatted-number
                                                value={claim.value.MSRP}
                                                format-style="currency"
                                                currency-code={currencyCode}
                                            >
                                            </lightning-formatted-number>
                                        </td>
                                    </template>
                                    <template if:false={claim.value.displayMSRP}>
                                        <td scope="row">
                                            NA{label.CCM_Portal_Loading}
                                        </td>
                                    </template>
                                    <td scope="row">
                                        <lightning-input
                                            variant="label-hidden"
                                            required
                                            data-index={claim.index}
                                            data-type="threshold"
                                            type="number"
                                            step="0.01"
                                            value={claim.value.salePrice}
                                            onblur={handleSaleSellthroughPriceBlur}
                                        >
                                        </lightning-input>
                                    </td>
                                    <td scope="row">
                                        <lightning-input
                                            variant="label-hidden"
                                            required
                                            data-index={claim.index}
                                            data-type="threshold"
                                            type="number"
                                            step="1"
                                            value={claim.value.quantity}
                                            onblur={handleQuantityChangeWholeOrder}
                                        >
                                        </lightning-input>
                                    </td>
                                    <td>
                                        <lightning-formatted-number
                                            value={claim.value.scaleTotalSales}
                                            format-style="currency"
                                            currency-code={currencyCode}
                                        >
                                        </lightning-formatted-number>
                                    </td>
                                    <td>
                                        <lightning-icon
                                            icon-name="utility:delete"
                                            alternative-text={label.CCM_Portal_Delete}
                                            title={label.CCM_Portal_Delete}
                                            size="x-small"
                                            data-index={claim.index}
                                            data-type="threshold"
                                            onclick={handleDeleteClaimItem}
                                        >
                                        </lightning-icon>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                    <div class="slds-m-top_medium">
                        <lightning-button
                            variant="brand"
                            label={label.CCM_Portal_AddItemSymbol}
                            onclick={handleClickAddItemThreshold}
                        ></lightning-button>
                    </div>
            </div>
        </template>
        <!-- </template> -->

        <template if:true={hasOfferingProduct}>
            <div class="slds-m-vertical_small">
                <h2 class="title">{label.CCM_Portal_FreeGoods}</h2>
                <table
                    class="slds-table slds-table_cell-buffer slds-table_bordered createClaimTable offeringTable"
                >
                    <thead>
                        <tr class="slds-line-height_reset">
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 10%"
                            >
                                <div
                                    class="slds-truncate"
                                    title=""
                                >
                                    {label.CCM_Portal_Line}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 50%"
                            >
                                <div
                                    class="slds-truncate"
                                    title=""
                                >
                                    {label.CCM_Portal_Model}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_MSRP}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_PromotionPrice}
                                </div>
                            </th>
                            <template if:false={isBMSM}>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div class="slds-truncate" title="">
                                        {label.CCM_Portal_ChervonFundedAmount}
                                    </div>
                                </th>
                            </template>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_SalePrice}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_Quantity}
                                </div>
                            </th>
                            <th
                                class="slds-text-title_caps"
                                scope="col"
                                style="width: 20%"
                            >
                                <div class="slds-truncate" title="">
                                    {label.CCM_Portal_TotalSales}
                                </div>
                            </th>
                            <template if:false={isBMSM}>
                                <th
                                    class="slds-text-title_caps"
                                    scope="col"
                                    style="width: 20%"
                                >
                                    <div class="slds-truncate" title="">
                                        {label.CCM_Portal_ClaimAmount}
                                    </div>
                                </th>
                            </template>
                        </tr>
                    </thead>
                    <tbody>
                        <template
                            iterator:claim={claimItemsOffering}
                        >
                            <tr key={claim.value.lineNo}>
                                <td scope="row">
                                    {claim.value.lineNo}
                                </td>
                                <td scope="row">
                                    {claim.value.productCode}
                                </td>
                                <td scope="row">
                                    <lightning-formatted-number
                                        value={claim.value.MSRP}
                                        format-style="currency"
                                        currency-code={currencyCode}
                                    >
                                    </lightning-formatted-number>
                                </td>
                                <td scope="row">
                                    <lightning-formatted-number
                                        value={claim.value.promoPrice}
                                        format-style="currency"
                                        currency-code={currencyCode}
                                    >
                                    </lightning-formatted-number>
                                </td>
                                <template if:false={isBMSM}>
                                    <td scope="row">
                                        <lightning-formatted-number
                                            value={claim.value.chervonFundedAmount}
                                            format-style="currency"
                                            currency-code={currencyCode}
                                        >
                                        </lightning-formatted-number>
                                    </td>
                                </template>
                                <td scope="row">
                                    <lightning-formatted-number
                                        value={claim.value.salePrice}
                                        format-style="currency"
                                        currency-code={currencyCode}
                                    >
                                    </lightning-formatted-number>
                                </td>
                                <td scope="row">
                                    <lightning-formatted-number
                                        value={claim.value.quantity}
                                    >
                                    </lightning-formatted-number>
                                </td>
                                <td>
                                    <lightning-formatted-number
                                        value={claim.value.scaleTotalSales}
                                        format-style="currency"
                                        currency-code={currencyCode}
                                    >
                                    </lightning-formatted-number>
                                </td>
                                <template if:false={isBMSM}>
                                    <td>
                                        <lightning-formatted-number
                                            value={claim.value.scaleAmount}
                                            format-style="currency"
                                            currency-code={currencyCode}
                                        >
                                        </lightning-formatted-number>
                                    </td>
                                </template>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </template>

        <template if:true={showInvoice}>
            <!-- Invoice Information -->
            <div class="slds-m-vertical_small">
                <!--section header-->
                <h2 class="title">{label.CCM_Portal_Invoice}</h2>
                <!--section body-->
                <template if:true={invoiceItems}>
                    <table   class="slds-table slds-table_cell-buffer slds-table_bordered createClaimTable offeringTable">
                        <thead>
                            <tr class="slds-line-height_reset">
                                <th scope="col">
                                    <div class="slds-truncate" title={label.CCM_Portal_Action} >
                                        {label.CCM_Portal_Action}
                                    </div>
                                </th>
                                <th scope="col">
                                    <div class="slds-truncate" title={label.CCM_Portal_InvoiceNumber} >
                                        {label.CCM_Portal_InvoiceNumber}
                                    </div>
                                </th>
                                <th scope="col">
                                    <div class="slds-truncate" title={label.CCM_Portal_InvoiceDate}>
                                        {label.CCM_Portal_InvoiceDate}
                                    </div>
                                </th>
                                <th scope="col">
                                    <div class="slds-truncate" title={label.CCM_Portal_Amount}>
                                        {label.CCM_Portal_Amount}
                                    </div>
                                </th>
                            </tr>
                                </thead>
                                <tbody>
                                    <template iterator:invoiceitem={invoiceItems}>
                                        <tr key={invoiceitem.value.Name}>
                                            <td scope="row">
                                                <lightning-icon icon-name="utility:preview" alternative-text="button icon"
                                                    title="View" size="x-small"
                                                    data-id={invoiceitem.value.Id}
                                                    class="clickable"
                                                    onclick={handleInvoiceView}>
                                                </lightning-icon>
                                            </td>
                                            <td scope="row">
                                                {invoiceitem.value.Name}
                                            </td>
                                            <td scope="row">
                                                {invoiceitem.value.Invoice_Date__c}
                                            </td>
                                            <td scope="row">
                                                <lightning-formatted-number value={invoiceitem.value.Total_Amount__c}
                                                    format-style="currency" currency-code={invoiceitem.value.CurrencyIsoCode} currency-display-as="code">
                                                </lightning-formatted-number>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </template>
            </div>
        </template>

        <template if:true={showNextButton}>
            <div class="slds-m-top_medium">
                <div class="slds-grid slds-grid_align-end">
                    <div class="slds-col">
                        <template if:true={isInternal}>
                            <template
                                for:each={offeringSummaryList}
                                for:item="summary"
                                for:index="index"
                            >
                                <div
                                    key={summary.label}
                                    class="slds-text-align_right slds-p-right_small"
                                >
                                    {summary.label}:
                                </div>
                            </template>
                        </template>
                        <template if:true={isBMSM}>
                            <div class="slds-text-align_right slds-p-right_small slds-text-heading_small">{label.CCM_Portal_TotalDiscountAmount}:</div>
                        </template>
                        <template if:false={isCCA}>
                            <div
                                class="slds-text-align_right slds-p-right_small slds-text-heading_small"
                            >
                                {label.CCM_Portal_TotalClaimAmount}:
                            </div>
                        </template>
                        <template if:true={isCCA}>
                            <div
                                class="slds-text-align_right slds-p-right_small slds-text-heading_small"
                            >
                                {label.CCM_Portal_TotalClaimAmountTaxExclusive}:
                            </div>
                            <div
                                class="slds-text-align_right slds-p-right_small slds-text-heading_small"
                            >
                                {label.CCM_Portal_TotalClaimAmountTaxInclusive}:
                            </div>
                        </template>
                    </div>
                    <div class="slds-col">
                        <template if:true={isInternal}>
                            <template
                                for:each={offeringSummaryList}
                                for:item="summary"
                                for:index="index"
                            >
                                <div key={summary.label}>
                                    <lightning-formatted-number
                                        value={summary.value}
                                        format-style="currency"
                                        currency-code={currencyCode}
                                    >
                                    </lightning-formatted-number>
                                </div>
                            </template>
                        </template>
                        <template if:true={isBMSM}>
                            <div class="slds-text-heading_small">
                                <lightning-formatted-number
                                    value={totalDiscountAmount}
                                    format-style="currency"
                                    currency-code={currencyCode}
                                >
                                </lightning-formatted-number>
                            </div>
                        </template>
                        <template if:false={isCCA}>
                            <div class="slds-text-heading_small">
                                <lightning-formatted-number
                                    value={totalClaimAmount}
                                    format-style="currency"
                                    currency-code={currencyCode}
                                >
                                </lightning-formatted-number>
                            </div>
                        </template>
                        <template if:true={isCCA}>
                            <div class="slds-text-heading_small">
                                <lightning-formatted-number
                                    value={totalClaimAmount}
                                    format-style="currency"
                                    currency-code={currencyCode}
                                >
                                </lightning-formatted-number>
                            </div>
                            <div class="slds-text-heading_small">
                                <lightning-formatted-number
                                    value={totalClaimAmountInclusive}
                                    format-style="currency"
                                    currency-code={currencyCode}
                                >
                                </lightning-formatted-number>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
            <div
                class="slds-m-top_medium slds-grid slds-grid_align-end"
            >
                <lightning-button
                    variant="brand"
                    class="slds-m-left_medium"
                    label={label.CCM_Portal_Next}
                    onclick={handleClickNext}
                ></lightning-button>
            </div>
        </template>
    </div>
    </template>
    <template if:true={secondStep}>
    <div class="width90 slds-container_center">
        <div class="slds-m-around_small">
            <table
                class="slds-table slds-table_cell-buffer slds-table_bordered"
            >
                <thead>
                    <tr class="slds-line-height_reset">
                        <th
                            class="slds-text-title_caps"
                            scope="col"
                            style="width: 10%"
                        >
                            <div
                                class="slds-truncate"
                                title=""
                            >
                                {label.CCM_Portal_Line}
                            </div>
                        </th>
                        <th
                            class="slds-text-title_caps"
                            scope="col"
                            style="width: 50%"
                        >
                            <div
                                class="slds-truncate"
                                title=""
                            >
                                {label.CCM_Portal_File}
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <template iterator:file={uploadedFiles}>
                        <tr key={file.value.documentId}>
                            <th scope="row">{file.value.lineNo}</th>
                            <th scope="row">
                                <div class="slds-truncate">
                                    <a
                                        onclick={navigateToFiles}
                                        data-docid={file.value.documentId}
                                        data-versionid={file.value.contentVersionId}
                                    >
                                        {file.value.name}
                                    </a>
                                </div>
                            </th>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>

        <div class="slds-m-around_small">
            <div
                class="slds-m-top_medium slds-grid slds-grid_align-end"
            >
                <lightning-button
                    variant="neutral"
                    class="slds-m-left_medium"
                    label={label.CCM_Portal_Prev}
                    onclick={handleClickEditClaim}
                ></lightning-button>
            </div>
        </div>
    </div>
    </template>
    </template>
    </div>
    </template>