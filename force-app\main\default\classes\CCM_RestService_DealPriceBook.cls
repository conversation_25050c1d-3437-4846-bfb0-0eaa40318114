/**********************************************************************
 * 
 *
 * @url: /services/apexrest/CCM_RestService_DealPricebook 
 * @data:
 *  {
    "Price_Book_OracleID":"1248818",
    "Name":"CNA-EGO-ACE Price List",
    "ATTRIBUTE1":"1129464,1249041",
    "Status":"Y",
    "CurrencyCode":"USD",
    "Org_Code":"CNA",
    "PriceBookLine":
        [{
        "Price_Book_Line_OracleID":"1338090",
        "Product":"HT2410",
        "Price":"99.83",
        "Start_Date":"2018-08-01 00:00:00",
        "End_Date":"2019-10-27 00:00:00"
        }]
    }
    //在Oracle里，同一个物料在同一个价格册会有多个Oracle Id，客户维护新价格的时候，会把老的价格册失效，创建新的一条的数据，但是在SFDC测，同一个物料在一本价格册只能是唯一的，更新价格册同步唯一键（由原来的Price_Book_Line_OracleID 变更成价格册Id + Product Model Number） Modified By Abby on 3/26/2020
    //添加新的接口参数ATTRIBUTE1，作为主副价格册的副价格册Oracle Id存储（多本价格册以逗号分隔） Modified By Abby on 7/16/2020

 * Test Class: CCM_RestService_DealPricebookTest
*************************************************************************/
@RestResource(urlMapping='/CCM_RestService_DealPricebook')
global with sharing class CCM_RestService_DealPriceBook {
    @HttpPost
    global static ResultObj doPost() {
        RestRequest req = RestContext.request;
        ReqestObj reqObj;
        ResultObj resObj = new ResultObj();
        try{
            System.debug(LoggingLevel.INFO, '*** req.requestBody.toString(): ' + req.requestBody.toString());
            reqObj = parse(req.requestBody.toString());
            System.debug(LoggingLevel.INFO, '*** req.requestBody(): ' + reqObj);
            String PriceBookOracleID = reqObj.Price_Book_OracleID;

            // servicePB 只有1本，Warranty and simple price list
            Service_PriceBook__mdt[] servicePB = [SELECT List_Head_Id__c FROM Service_PriceBook__mdt WHERE List_Head_Id__c =: Decimal.valueOf(PriceBookOracleID)];

            List<PriceBook2> PriceBookList = [SELECT Id FROM PriceBook2 WHERE Price_Book_OracleID__c = :PriceBookOracleID];
            System.debug(LoggingLevel.INFO, '*** PriceBookList: ' + Json.serialize(PriceBookList));
            // service book 逻辑
            if (servicePB.size() > 0) {
                // update
                if (PriceBookList.size() > 0) {
                    PriceBook2 pb = PriceBookList.get(0);
                    pb.Name = reqObj.Name;
                    if (String.isNotBlank(reqObj.ATTRIBUTE1)) {
                        pb.Contract_Price_Book_OracleID__c = reqObj.ATTRIBUTE1;
                    }
                    if (reqObj.Status == 'Y') {
                        pb.IsActive = true;
                    } else {
                        pb.IsActive = false;
                    }
                    pb.CurrencyCode__c = reqObj.CurrencyCode;
                    pb.Org_Code__c = reqObj.Org_Code;
                    pb.ExternalID__c = reqObj.Price_Book_OracleID;
                    if (String.isNotBlank(reqObj.START_DATE_ACTIVE)) {
                        pb.Start_Date__c = Datetime.valueOf(reqObj.START_DATE_ACTIVE);
                    }
                    if (String.isNotBlank(reqObj.COMMENTS)) {
                        pb.COMMENTS__c = reqObj.COMMENTS;
                    }
                    pb.End_Date__c = String.isNotBlank(reqObj.END_DATE_ACTIVE) ? Datetime.valueOf(reqObj.END_DATE_ACTIVE) : Datetime.valueOf('2099-12-31 00:00:00');
                    System.debug('*** pb : ' + pb);
                    // 上线后，修改这个label为True的值
                    if (Label.IS_ORM_EBS_CURRENT_PRICEBOOK.equals('True')) {
                        Database.update(pb,false);                                    
                    }
                // insert
                } else {
                    PriceBook2 pb = new PriceBook2();
                    pb.Price_Book_OracleID__c = reqObj.Price_Book_OracleID;
                    pb.Name = reqObj.Name;
                    if (String.isNotBlank(reqObj.ATTRIBUTE1)) {
                        pb.Contract_Price_Book_OracleID__c = reqObj.ATTRIBUTE1;
                    }
                    if (reqObj.Status == 'Y') {
                        pb.IsActive = true;
                    } else {
                        pb.IsActive = false;
                    }
                    pb.CurrencyCode__c = reqObj.CurrencyCode;
                    pb.Org_Code__c = reqObj.Org_Code;
                    pb.ExternalID__c = reqObj.Price_Book_OracleID;
                    pb.Start_Date__c = String.isNotBlank(reqObj.START_DATE_ACTIVE) ? Datetime.valueOf(reqObj.START_DATE_ACTIVE) :null;
                    pb.End_Date__c = String.isNotBlank(reqObj.END_DATE_ACTIVE) ? Datetime.valueOf(reqObj.END_DATE_ACTIVE) : Datetime.valueOf('2099-12-31 00:00:00');
                    pb.COMMENTS__c = reqObj.COMMENTS;   

                    // 上线后，修改这个label为True的值
                    if (Label.IS_ORM_EBS_CURRENT_PRICEBOOK.equals('True')) {
                        Database.insert(pb,false);                                    
                    } 
                }
                // 处理 pricebook entry
                if (reqObj.PriceBookLine != null && reqObj.PriceBookLine.size() > 0) {
                    String stander = '';
                    if(System.Test.isRunningTest()) {
                        stander = System.Test.getStandardPricebookId();
                    } else {
                        stander = [Select Id from Pricebook2 where IsStandard = true limit 1].Id;
                    }
                    
                    Set<String> newExternalIdSet = new Set<String>();
                    Set<String> existExternalIdSet = new Set<String>();
                    // 产品的 inventory item id。
                    Set<Decimal> setProdExternalids = new Set<Decimal>();

                    for (PriceBookLine data : reqObj.PriceBookLine) {
                        newExternalIdSet.add(String.valueOf(data.Price_Book_Line_OracleID));
                        newExternalIdSet.add(String.valueOf(data.Price_Book_Line_OracleID) + '-Standar');
                        
                        setProdExternalids.add(Decimal.valueOf(data.Product));
                    }
                    Map<String,Id> mapServiceProdId = new Map<String,Id>();
                    for (Product2 prod: [
                        select id,externalid__c
                        from Product2 where id != null
                        and ExternalId__c in :setProdExternalids
                        and country_of_origin__c = 'United States'
                        and Is_Kit_for_Product__c = false//Added by Zoe on 2025-2-24，service价格册页应该排除假kit
                    ]) {
                        mapServiceProdId.put(String.valueOf(prod.Externalid__c), prod.id);
                    }

                    for (PricebookEntry pbe : [SELECT Id,ExternalID__c FROM PricebookEntry 
                                                WHERE ExternalID__c in: newExternalIdSet 
                                                AND Product2.Country_of_Origin__c != 'Canada']) {
                        if (pbe.ExternalID__c != null && pbe.ExternalID__c != '') {
                            existExternalIdSet.add(pbe.ExternalID__c);
                        }
                    }

                    List<PricebookEntry> satanderPriceList = new List<PricebookEntry>();
                    List<PricebookEntry> priceEntryList = new List<PricebookEntry>();
                    
                    for (PriceBookLine data : reqObj.PriceBookLine) {
                        // 自定义价格册
                        Id idProd = null;
                        PricebookEntry pbe          = new PricebookEntry();
                        pbe.UseStandardPrice = false;
                        pbe.IsActive = true;
                        
                        pbe.ExternalID__c           = String.valueOf(data.Price_Book_Line_OracleID);
                        pbe.Price_Book_Line_OracleID__c = data.Price_Book_Line_OracleID;
                        pbe.Description__c          = data.DESCRIPTION;
                        pbe.Start_Date__c = String.isNotBlank(data.Start_Date) ? Datetime.valueOf(data.Start_Date) : null;
                        pbe.End_Date__c = String.isNotBlank(data.End_Date) ? Datetime.valueOf(data.End_Date) : null;
                        pbe.Expiration_date__c = String.isNotBlank(data.End_Date) ? Datetime.valueOf(data.End_Date) : null; 
                        pbe.Last_modified_time__c =String.isNotBlank(data.Last_modified_time) ? Datetime.valueOf(data.Last_modified_time) : null;                  
                        pbe.Price_List_Head_Name__c = reqObj.Name;
                        pbe.UnitPrice = String.isNotBlank(data.Price) ? Decimal.valueOf(data.Price) : null;
                        idProd = mapServiceProdId.get(data.Product);
                        if (!existExternalIdSet.contains(pbe.ExternalID__c)) {
                            pbe.CurrencyIsoCode = reqObj.CurrencyCode;
                            pbe.Pricebook2              = new Pricebook2(ExternalID__c=String.valueOf(PriceBookOracleID));
                            if (idProd != null) {
                                pbe.Product2Id              = idProd;
                            }
                        }
                        // 非空才添加产品价格
                        if (idProd != null) {
                            priceEntryList.add(pbe);
                        }
                        
                        // 标准价格册
                        PricebookEntry spbe          = new PricebookEntry();
                        spbe.ExternalID__c           = String.valueOf(data.Price_Book_Line_OracleID) +'-Standar';
                        spbe.Price_Book_Line_OracleID__c           = String.valueOf(data.Price_Book_Line_OracleID) +'-Standar';
                        spbe.UseStandardPrice = false;
                        spbe.IsActive = true;
                        
                        spbe.Description__c          = data.DESCRIPTION;
                        spbe.Start_Date__c = String.isNotBlank(data.Start_Date) ? Datetime.valueOf(data.Start_Date) : null;
                        spbe.End_Date__c = String.isNotBlank(data.End_Date) ? Datetime.valueOf(data.End_Date) : null;
                        spbe.Expiration_date__c = String.isNotBlank(data.End_Date) ? Datetime.valueOf(data.End_Date) : null; 
                        spbe.Last_modified_time__c =String.isNotBlank(data.Last_modified_time) ? Datetime.valueOf(data.Last_modified_time) : null;                  
                        spbe.Price_List_Head_Name__c = reqObj.Name;
                        spbe.UnitPrice = String.isNotBlank(data.Price) ? Decimal.valueOf(data.Price) : null;
                        
                        idProd = mapServiceProdId.get(data.Product);
                        if(!existExternalIdSet.contains(spbe.ExternalID__c)) {
                            spbe.CurrencyIsoCode = reqObj.CurrencyCode;
                            spbe.Pricebook2Id           = stander;
                            if (idProd != null) {
                                spbe.Product2Id             = idProd;
                            }
                        }
                        
                        if (idProd != null) {
                            satanderPriceList.add(spbe);
                        }
                    }
                    // 上线后，修改这个label为True的值
                    if (Label.IS_ORM_EBS_CURRENT_PRICEBOOK.equals('True')) {
                        List<Database.UpsertResult> ur1 = Database.upsert(satanderPriceList, PricebookEntry.ExternalID__c.getDescribe().getSObjectField(), false);
                        List<Database.UpsertResult> ur2 = Database.upsert(priceEntryList, PricebookEntry.ExternalID__c.getDescribe().getSObjectField(), false);

                        // 失败日志保存
                        Log__c logErr = new Log__c();
                        Log__c logErr2 = new Log__c();
                        Integer index1 = 0;
                        Integer index2 = 0;
                        Map<String,String> mapTmp1 = new Map<String,String>();
                        Map<String,String> mapTmp2 = new Map<String,String>();

                        for(Database.UpsertResult ur : ur1){
                            if (!ur.isSuccess()) {
                                index1 ++;
                                List<Database.Error> eList = ur.getErrors();
                                mapTmp1.put(String.valueOf(index1), eList.get(0).getMessage());
                            } 
                        }

                        for(Database.UpsertResult ur : ur2){
                            if (!ur.isSuccess()) {
                                index2 ++;
                                List<Database.Error> eList = ur.getErrors();
                                mapTmp2.put(String.valueOf(index2), eList.get(0).getMessage());
                            } 
                        }

                        List<Log__c> lstLogs = new List<Log__c>();
                        if (mapTmp1.keySet().size() > 0) {
                            logErr.Name = 'PriceBook Exception 1';
                            logErr.ApexName__c = 'CCM_RestService_DealPriceBook';
                            logErr.Error_Message__c = 'std:' + json.serialize(mapTmp1);

                            lstLogs.add(logErr);
                        }
                        if (mapTmp2.keySet().size() > 0) {
                            logErr2.Name = 'PriceBook Exception 2';
                            logErr2.ApexName__c = 'CCM_RestService_DealPriceBook';
                            logErr2.Error_Message__c = 'custom:' + json.serialize(mapTmp2);

                            lstLogs.add(logErr2);
                        }
                        if (lstLogs.size() > 0) {
                            Database.insert(lstLogs, false);
                        }
                    } 
                }

                resObj.returnCode = 'S';
                // 非 service book
            } else {
                List<String> productIdlist = new List<String>();
                Map<String,String> proMap = new Map<String,String>();
                List<String> priceBookProdIdList = new List<String>();
                if (reqObj.PriceBookLine != null && reqObj.PriceBookLine.size() > 0) {
                    for (PriceBookLine pbl : reqObj.PriceBookLine) {
                        if (!productIdlist.contains(pbl.Product)) {
                            productIdlist.add(pbl.Product);
                        }
                        if (!priceBookProdIdList.contains(pbl.Product)) {
                            priceBookProdIdList.add(pbl.Product);
                        }
                    }
                }
    
                List<String> standardprolist = new List<String>();
                PriceBook2 standardPricebook = [SELECT Id FROM Pricebook2 WHERE IsStandard = true LIMIT 1/* FOR UPDATE*/];
                // 2022-07-05 部分解决 query limit 报错问题
                for (PricebookEntry pbe : [SELECT Id, Product2Id, Pricebook2Id 
                                            FROM PricebookEntry 
                                            WHERE Pricebook2Id = :standardPricebook.Id
                                            AND Product2.ProductCode IN :productIdlist]) {
                    standardprolist.add(pbe.Product2Id);
                }
    
                List<Product2> prolist = [SELECT Id, ProductCode FROM Product2 
                                            WHERE ProductCode IN :productIdlist 
                                            AND source__c = 'PIM' 
                                            AND IsCreatedByCode__c = false 
                                            AND Product2.Country_of_Origin__c != 'Canada'
                                            AND Is_Kit_for_Product__c = false//Added by Zoe on 2025-2-24,查询真正的Bare tool，排除假kit
                                            AND Is_History_Product__c = :CCM_Constants.blHistoryProduct];
                for (Product2 p : prolist) {
                    proMap.put(p.ProductCode, p.Id);
                }
                if (PriceBookList.size() > 0) {
                    PriceBook2 pb = PriceBookList.get(0);
                    pb.Name = reqObj.Name;
                    pb.CurrencyCode__c = reqObj.CurrencyCode;
                    pb.Org_Code__c = reqObj.Org_Code;
                    if (reqObj.Status == 'Y') {
                        pb.IsActive = true;
                    } else {
                        pb.IsActive = false;
                    }
                    //赋值ATTRIBUTE1 Add by Abby on 7/16/2020
                    if (String.isNotBlank(reqObj.ATTRIBUTE1)){
                        pb.Contract_Price_Book_OracleID__c = reqObj.ATTRIBUTE1;
                    }
                    // 上线后，修改这个label为True的值
                    if (Label.IS_ORM_EBS_CURRENT_PRICEBOOK.equals('True')) {
                        Database.update(pb,false);
                    } 
                    List<PricebookEntry> pbelist = [SELECT id,Price_Book_Line_OracleID__c,ProductCode,Product2.ProductCode,Pricebook2Id 
                                                        FROM PricebookEntry 
                                                        WHERE Product2.ProductCode IN : priceBookProdIdList 
                                                        AND Pricebook2Id = :pb.Id
                                                        AND Product2.Source__c = 'PIM'
                                                        AND Product2.Country_of_Origin__c != 'Canada'];
                    List<PricebookEntry> pbeInsertList = new List<PricebookEntry>();
                    List<PricebookEntry> pbeUpdateList = new List<PricebookEntry>();
                    Map<String,PricebookEntry> pbeMap = new Map<String,PricebookEntry>();
                    for (PricebookEntry pbe : pbelist) { 
                        String prodKey = pb.Id + '-'+ pbe.Product2.ProductCode;
                        pbeMap.put(prodKey, pbe);
                    }
    
                    if (reqObj.PriceBookLine != null && reqObj.PriceBookLine.size() > 0) {
                        for (PriceBookLine pbl : reqObj.PriceBookLine) {
                            String productKey = pb.Id + '-'+ pbl.Product;
                            // update
                            if (pbeMap.get(productKey) != null) {
                                PricebookEntry pbe = pbeMap.get(productKey);
                                pbe.Price_Book_Line_OracleID__c = pbl.Price_Book_Line_OracleID;
                                pbe.UnitPrice = Decimal.valueOf(pbl.Price);
                                pbe.UseStandardPrice = false;
                                pbe.Start_Date__c = Datetime.valueOf(pbl.Start_Date);
                                if (String.isNotBlank(pbl.End_Date)) {
                                    pbe.End_Date__c = Datetime.valueOf(pbl.End_Date);
                                } else {
                                    pbe.End_Date__c = Datetime.valueOf('2099-12-31 00:00:00');
                                }                        
                                if (pbe.Start_Date__c.date() <= System.today() && pbe.End_Date__c.date() >= System.today()) {
                                    pbe.IsActive = true;
                                } else {
                                    pbe.IsActive = false;
                                }
                                if (pbe.Product2Id != null) {
                                    pbeUpdateList.add(pbe);
                                }
                            // insert
                            } else {
                                String proId = proMap.get(pbl.Product);
                                if (proId != null && proId != '' && standardprolist.contains(proId)) {
                                    PricebookEntry pbe = new PricebookEntry();
                                    pbe.Pricebook2Id = pb.Id;
                                    pbe.Price_Book_Line_OracleID__c = pbl.Price_Book_Line_OracleID;
                                    pbe.Product_Text__c = pbl.Product;
                                    pbe.Product2Id = proId;
                                    pbe.UnitPrice = Decimal.valueOf(pbl.Price);
                                    pbe.UseStandardPrice = false;
                                    pbe.Start_Date__c = Datetime.valueOf(pbl.Start_Date);
                                    if (String.isNotBlank(pbl.End_Date)) {
                                        pbe.End_Date__c = Datetime.valueOf(pbl.End_Date);
                                    } else {
                                        pbe.End_Date__c = Datetime.valueOf('2099-12-31 00:00:00');
                                    } 
                                    if (pbe.Start_Date__c.date() <= System.today() && pbe.End_Date__c.date() >= System.today()) {
                                        pbe.IsActive = true;
                                    } else {
                                        pbe.IsActive = false;
                                    }
                                    pbe.CurrencyIsoCode = pb.CurrencyCode__c;
                                    pbeInsertList.add(pbe);
                                }   
                            }
                        }
                    }
                    // 上线后，修改这个label为True的值
                    if (Label.IS_ORM_EBS_CURRENT_PRICEBOOK.equals('True')) {
                        Integer index1 = 0;
                        Integer index2 = 0;

                        Map<String,String> mapTmp1 = new Map<String,String>();
                        Map<String,String> mapTmp2 = new Map<String,String>();

                        if (pbeInsertList.size() > 0) {
                            // Database.insert(pbeInsertList, false)
                            Log__c logErr = new Log__c();

                            for (Database.SaveResult item : Database.insert(pbeInsertList, false)) {
                                if (!item.isSuccess()) {
                                    index1 ++;
                                    mapTmp1.put(String.valueOf(index1), json.serialize(item));
                                }
                            }
                            
                            if (mapTmp1.keySet().size() > 0) {
                                logErr.Name = 'PriceBook Exception 3';
                                logErr.ApexName__c = 'CCM_RestService_DealPriceBook';
                                logErr.Error_Message__c = 'insert:' + json.serialize(mapTmp1);
                                Database.insert(logErr, false);
                            }
                        }
                        if (pbeUpdateList.size() > 0) {
                            Log__c logErr = new Log__c();
                            for (Database.SaveResult item : Database.update(pbeUpdateList, false)) {
                                if (!item.isSuccess()) {
                                    index2 ++;
                                    mapTmp2.put(String.valueOf(index2), json.serialize(item));
                                }
                            }
                            
                            if (mapTmp2.keySet().size() > 0) {
                                logErr.Name = 'PriceBook Exception 4';
                                logErr.ApexName__c = 'CCM_RestService_DealPriceBook';
                                logErr.Error_Message__c = 'update:' + json.serialize(mapTmp2);
    
                                Database.insert(logErr, false);
                            }
                        }
                    } 
                    // 新建
                } else {
                    List<PricebookEntry> pbllist = new List<PricebookEntry>();
                    PriceBook2 pb = new PriceBook2();
                    pb.Price_Book_OracleID__c = reqObj.Price_Book_OracleID;
                    pb.Name = reqObj.Name;
                    pb.CurrencyCode__c = reqObj.CurrencyCode;
                    pb.Org_Code__c = reqObj.Org_Code;
                    if (reqObj.Status == 'Y') {
                        pb.IsActive = true;
                    } else {
                        pb.IsActive = false;
                    }
                    // 赋值ATTRIBUTE1 Add by Abby on 7/16/2020
                    if (String.isNotBlank(reqObj.ATTRIBUTE1)) {
                        pb.Contract_Price_Book_OracleID__c = reqObj.ATTRIBUTE1;
                    }
                    // 上线后，修改这个label为True的值
                    if (Label.IS_ORM_EBS_CURRENT_PRICEBOOK.equals('True')) {
                        Database.insert(pb,false);
                    } 
                    if (reqObj.PriceBookLine != null && reqObj.PriceBookLine.size() > 0) {
                        for (PriceBookLine pbl : reqObj.PriceBookLine) {
                            String proId = proMap.get(pbl.Product);
                            if (proId != null && proId != '' && standardprolist.contains(proId)) {
                                PricebookEntry pbe = new PricebookEntry();
                                pbe.Pricebook2Id = pb.Id;
                                pbe.Price_Book_Line_OracleID__c = pbl.Price_Book_Line_OracleID;
                                pbe.Product_Text__c = pbl.Product;
                                pbe.Product2Id = proId;
                                pbe.UnitPrice = Decimal.valueOf(pbl.Price);
                                pbe.UseStandardPrice = false;
                                pbe.Start_Date__c = Datetime.valueOf(pbl.Start_Date);
                                if (String.isNotBlank(pbl.End_Date)) {
                                    pbe.End_Date__c = Datetime.valueOf(pbl.End_Date);
                                } else {
                                    pbe.End_Date__c = Datetime.valueOf('2099-12-31 00:00:00');
                                } 
                                if (pbe.Start_Date__c.date() <= System.today() && pbe.End_Date__c.date() >= System.today()) {
                                    pbe.IsActive = true;
                                } else {
                                    pbe.IsActive = false;
                                }
                                pbe.CurrencyIsoCode = pb.CurrencyCode__c;
                                pbllist.add(pbe);
                            }                                            
                        }
                    }
                    if(pbllist.size() > 0){
                        Map<String,String> mapTmp1 = new Map<String,String>();
                        Integer index1 = 0;
                        // 上线后，修改这个label为True的值
                        if (Label.IS_ORM_EBS_CURRENT_PRICEBOOK.equals('True')) {
                            Database.SaveResult[] insertRes = Database.insert(pbllist,false);
                            Log__c logErr = new Log__c();
                            for (Database.SaveResult item : insertRes) {
                                if (!item.isSuccess()) {
                                    index1 ++;
                                    mapTmp1.put(String.valueOf(index1), json.serialize(item));
                                }
                            }
                            if (mapTmp1.keySet().size() > 0) {
                                logErr.Name = 'PriceBook Exception 5';
                                logErr.ApexName__c = 'CCM_RestService_DealPriceBook';
                                logErr.Error_Message__c = json.serialize(mapTmp1);
    
                                Database.insert(logErr, false);
                            }
                        } 
                    }
                } 
                resObj.returnCode = 'S';    
            }
        } catch (Exception e) {
            resObj.returnCode = 'F'; 
            resObj.returnMsg = e.getLineNumber()+'-line error:'+e.getMessage();
            System.debug(LoggingLevel.INFO, '*** e.getMessage(): '+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage());
            String logId = Util.logIntegration(
                'PriceBook Exception','CCM_RestService_DealPriceBook', 
                'POST',resObj.returnMsg,req.requestBody.toString(), 
                 JSON.serialize(resObj)
            );
            Util.pushExceptionEmail('Accept Price Book',logId,resObj.returnMsg);
            return resObj;
        }
        if (Label.CCM_needlog == 'Y') {
           Util.logIntegration('PriceBook log','CCM_RestService_DealPriceBook', 'POST', 
                               '',JSON.serialize(reqObj), JSON.serialize(resObj));
        } 
        System.debug(LoggingLevel.INFO, '*** resObj: ' + resObj);
        return resObj;
    }

    global class PriceBookLine {
        global String Product;
        global String End_Date;
        global String Price_Book_Line_OracleID;
        global String Start_Date;
        global String Price;
        global String Last_modified_time;
        global String Price_List_Head_Name;
        global String Description;
        global String Line_Attribute1;
        global String Line_Attribute2;
        global String Line_Attribute3;
        global String Line_Attribute4;
        global String Line_Attribute5;
        global String Line_Attribute6;
        global String Line_Attribute7;
        global String Line_Attribute8;
        global String Line_Attribute9;
        global String Line_Attribute10;
    }

    global class ReqestObj {
        global String Name;
        global String ATTRIBUTE1;
        global List<PriceBookLine> PriceBookLine;
        global String Price_Book_OracleID;
        global String Status;
        global String CurrencyCode;
        global String Org_Code;
        global String START_DATE_ACTIVE;
        global String END_DATE_ACTIVE;
        global String COMMENTS;
        global String ATTRIBUTE2;
        global String ATTRIBUTE3;
        global String ATTRIBUTE4;
        global String ATTRIBUTE5;
        global String ATTRIBUTE6;
        global String ATTRIBUTE7;
        global String ATTRIBUTE8;
        global String ATTRIBUTE9;
        global String ATTRIBUTE10;
    }

    global static ReqestObj parse(String jsonStr) {
        return (ReqestObj) JSON.deserialize(jsonStr, ReqestObj.class);
    } 

    global class ResultObj {
        global String returnCode;
        global String returnMsg;
    }
}