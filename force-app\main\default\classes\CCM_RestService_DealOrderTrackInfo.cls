/**********************************************************************
 * 
 *
 * @url: /services/apexrest/CCM_RestService_DealOrderTrackInfo
 * @data:
 * [{
        "SALESFORCE_ORDER":"PO# 201700003538",
        "ORDER_NUMBER":"1128127",
        "HEADER_ID":"333812",
        "PROCESS_STATUS":"S",
        "ERR_MSG":"",
        "LAST_UPDATE_DATE":"2017-05-31 00:04:10",
        "TRACKING_NO":"1ZX071280348947053,2847",
        "ATTRIBUTE1":"UPS GROUND"
}]
 *  
*************************************************************************/
@RestResource(urlMapping='/CCM_RestService_DealOrderTrackInfo')
global with sharing class CCM_RestService_DealOrderTrackInfo {
    @HttpPost
    global static ResultObj doPost() {
        Boolean boolIsDealingOrder;
        RestRequest req = RestContext.request;
        List<ReqestObj> reqObjList = new List<ReqestObj>();
        List<String> lstSyncErrorOracleId = Label.CCM_Order_Track_Info_Interface_Oracle_Id_4_Syncing_Error.split(CCM_Constants.SEMICOLON_REGEXP_SPACE_IGNORED);
        ResultObj resObj = new ResultObj();
        String errorContent = '';
        String resStr = '';
        Boolean hasErrorsInEBS = false;
        List<ReqestObj> newReqObjList = new List<ReqestObj>();
        Map<String,ReqestObj> reqObjMapS = new Map<String,ReqestObj>();
        Map<String,ReqestObj> reqObjMapE = new Map<String,ReqestObj>();
        try{
            System.debug(LoggingLevel.INFO, '*** req.requestBody.toString(): ' + req.requestBody.toString());
            resStr = req.requestBody.toString()
                .replaceAll(CCM_Constants.NEXT_LINE, CCM_Constants.EMPTY)
                .replaceAll(CCM_Constants.CARRIAGE_RETURN, CCM_Constants.EMPTY)
                .replaceAll(CCM_Constants.TAB, CCM_Constants.EMPTY);
            if(!resStr.startsWith('[')){
                resStr = '[' + resStr + ']';
            }
            reqObjList = parse(resStr);
            //ljh
            if (reqObjList != null && reqObjList.size() > 0) {
                for (ReqestObj reqObj : reqObjList) {
                    if(!reqObjMapS.containsKey(reqObj.SALESFORCE_ORDER)){
                        if (reqObj.PROCESS_STATUS == 'S') {
                            reqObjMapS.put(reqObj.SALESFORCE_ORDER, reqObj);
                        }else {
                            if (reqObjMapE.containsKey(reqObj.SALESFORCE_ORDER)) {
                                if (String.isNotBlank(reqObj.LAST_UPDATE_DATE)) {
                                  Datetime  mapETime = Datetime.valueOf(reqObjMapE.get(reqObj.SALESFORCE_ORDER).LAST_UPDATE_DATE);
                                  Datetime  objTime =  Datetime.valueOf(reqObj.LAST_UPDATE_DATE);
                                        if (mapETime <= objTime){
                                                reqObjMapE.put(reqObj.SALESFORCE_ORDER, reqObj);
                                            }
                                }
                            }else {
                                reqObjMapE.put(reqObj.SALESFORCE_ORDER, reqObj);
                            }
                        }
                    }

                }
            }

            Set <String> mapSSet = reqObjMapS.keySet();
            Set <String> mapESet = reqObjMapE.keySet();
            for (String key : mapSSet) {
                newReqObjList.add(reqObjMapS.get(key));
            }
            for (String key : mapESet) {
                if (!reqObjMapS.containsKey(key)) {
                    newReqObjList.add(reqObjMapE.get(key));
                }

            }
            //ljh
            resObj.Process_Result = new List<ReturnItem>();
            System.debug(LoggingLevel.INFO, '*** req.requestBody(): ' + newReqObjList);

            Set<String> orderIdSet = new Set<String>();
            Set<String> poNumberSet = new Set<String>();
            Set<String> warrantyReturnRequestSet = new Set<String>();
            Set<String> reverseOrderRequestSet = new Set<String>();

            List<Order> ol = new List<Order>();
            List<Purchase_Order__c> pol = new List<Purchase_Order__c>();
            List<Warranty_Return_Claim__c> warrantyReturnList = new List<Warranty_Return_Claim__c>();
            List<Reverse_Order_Request__c> reverseOrderRequestList = new List<Reverse_Order_Request__c>();

            Map<String,Order> oMap = new Map<String,Order>();
            Map<String,Purchase_Order__c> poMap = new Map<String,Purchase_Order__c>();
            Map<String, Warranty_Return_Claim__c> warrantyReturnMap = new Map<String, Warranty_Return_Claim__c>();
            // 处理 reverse order request track
            Map<String, Reverse_Order_Request__c> reverseOrderRequestMap = new Map<String, Reverse_Order_Request__c>();

            if (newReqObjList != null && newReqObjList.size() > 0) {
                //把Order和POrder分开
                for (ReqestObj reqObj : newReqObjList) {
                    if (String.isNotBlank(reqObj.SALESFORCE_ORDER)) {
                        String objectName = Util.findObjectNameFromRecordIdPrefix(reqObj.SALESFORCE_ORDER);
                        if (objectName == 'Order') {
                            orderIdSet.add(reqObj.SALESFORCE_ORDER);
                        } else if(reqObj.SALESFORCE_ORDER.startsWith('RA')) {
                            warrantyReturnRequestSet.add(reqObj.SALESFORCE_ORDER);
                        // track reverse order status
                        } else if (reqObj.SALESFORCE_ORDER.startsWith('RO')) {
                            reverseOrderRequestSet.add(reqObj.SALESFORCE_ORDER);
                        } else {
                            poNumberSet.add(reqObj.SALESFORCE_ORDER);
                        }
                    }
                }
            }

            if (newReqObjList != null && newReqObjList.size() > 0) {
                if (orderIdSet.size() > 0) {
                    List<Order> oList = [SELECT External_PO_NO__c,
                                        Order_Number__c,
                                        ERP_External_ID__c,
                                        Order_OracleID__c,
                                        Process_Status__c,
                                        ERP_Status__c,
                                        Error_Message__c,
                                        ERP_Last_Update_Time__c,
                                        Tracking_Number__c,
                                        Tracking_Company__c,
                                        Posting__c,
                                        Posted__c
                                        FROM Order
                                        WHERE Id IN :orderIdSet];
                    for (Order o : oList) {
                        oMap.put(o.Id,o);
                    }
                }

                if (poNumberSet.size() > 0) {
                    List<Purchase_Order__c> poList = [
                                        SELECT Name,
                                        Sync_Date__c,
                                        Sync_Message__c,
                                        Sync_Status__c
                                        FROM Purchase_Order__c
                                        WHERE Name IN :poNumberSet];
                    for (Purchase_Order__c po : poList) {
                        poMap.put(po.Name,po);
                    }
                }

                if(warrantyReturnRequestSet.size() > 0) {
                    for(Warranty_Return_Claim__c warrantyReturnClaim : [SELECT Id, Name FROM Warranty_Return_Claim__c WHERE Name IN :warrantyReturnRequestSet]) {
                        warrantyReturnMap.put(warrantyReturnClaim.Name, warrantyReturnClaim);
                    }
                }

                // deal reverse order request info
                if (reverseOrderRequestSet.size() > 0) {
                    for(Reverse_Order_Request__c reverseOrderRequestItem : [SELECT Id, Name FROM Reverse_Order_Request__c WHERE Name IN :reverseOrderRequestSet]) {
                        reverseOrderRequestMap.put(reverseOrderRequestItem.Name, reverseOrderRequestItem);
                    }
                }

                Set<String> salesforceOrderSet = new Set<String>();
                for (ReqestObj reqObj : newReqObjList) {
                    if (!salesforceOrderSet.contains(reqObj.SALESFORCE_ORDER)) {
                        if (String.isNotBlank(reqObj.SALESFORCE_ORDER)) {
                            String objectName = Util.findObjectNameFromRecordIdPrefix(reqObj.SALESFORCE_ORDER);
                            if (objectName == 'Order') {
                                boolIsDealingOrder = true;
                                if (oMap.get(reqObj.SALESFORCE_ORDER) != null) {
                                    Order o = oMap.get(reqObj.SALESFORCE_ORDER);
                                    o.Order_Number__c = reqObj.ORDER_NUMBER;
                                    o.ERP_External_ID__c = reqObj.HEADER_ID;
                                    o.Order_OracleID__c = lstSyncErrorOracleId.contains(reqObj.HEADER_ID) ? null : reqObj.HEADER_ID;
                                    if (reqObj.PROCESS_STATUS == 'W') {
                                        o.Process_Status__c = 'Wait';
                                        o.ERP_Status__c = 'Wait';
                                    }else if (reqObj.PROCESS_STATUS == 'S') {
                                        o.Process_Status__c = 'Success';
                                        o.ERP_Status__c = 'Success';
                                    }else {
                                        o.Process_Status__c = 'Error';
                                        o.ERP_Status__c = 'Error';
                                        hasErrorsInEBS = true;

                                        ReturnItem request = new ReturnItem();
                                        request.External_Id = reqObj.SALESFORCE_ORDER;
                                        request.Error_Message = 'This Order was failed processing in OMS';
                                        request.Error_Detail = '***The following error has occurred***' + reqObj.ERR_MSG;
                                        resObj.Process_Result.add(request);
                                    }
                                    o.Error_Message__c = reqObj.ERR_MSG;
                                    o.ERP_Last_Update_Time__c = String.isNotBlank(reqObj.LAST_UPDATE_DATE) ? Datetime.valueOf(reqObj.LAST_UPDATE_DATE) : null;
                                    o.Tracking_Number__c = reqObj.TRACKING_NO;
                                    //o.Tracking_Company__c = reqObj.ATTRIBUTE1;

                                    if (o.Posting__c == true) {
                                        o.Posting__c = false;
                                        o.Posted__c = true;
                                    }

                                    ol.add(o);

                                    salesforceOrderSet.add(reqObj.SALESFORCE_ORDER);
                                }
                                // warranty order update
                            } else if(reqObj.SALESFORCE_ORDER.startsWith('RA')) {
                                processWarrantyReturnRequest(warrantyReturnMap, reqObj, warrantyReturnList, resObj, salesforceOrderSet);
                            
                                // reverse order update
                            } else if (reqObj.SALESFORCE_ORDER.startsWith('RO')) {
                                processReverseOrderRequest(reverseOrderRequestMap, reqObj, reverseOrderRequestList, resObj, salesforceOrderSet);

                                // purchase order update
                            } else {
                                if (poMap.get(reqObj.SALESFORCE_ORDER) != null) {
                                    Purchase_Order__c o = poMap.get(reqObj.SALESFORCE_ORDER);
                                    if (reqObj.PROCESS_STATUS == 'W') {
                                        o.Sync_Status__c = 'Wait';
                                    }else if (reqObj.PROCESS_STATUS == 'S') {
                                        o.Sync_Status__c = 'Success';
                                    }else {
                                        //
                                        o.Sync_Status__c = 'Failed';
                                        hasErrorsInEBS = true;

                                        ReturnItem request = new ReturnItem();
                                        request.External_Id = reqObj.SALESFORCE_ORDER;
                                        request.Error_Message = 'This Order was failed processing in OMS';
                                        request.Error_Detail = '***The following error has occurred***' + reqObj.ERR_MSG;
                                        resObj.Process_Result.add(request);
                                    }
                                    o.Sync_Message__c = reqObj.ERR_MSG;
                                    o.Sync_Date__c = String.isNotBlank(reqObj.LAST_UPDATE_DATE) ? Datetime.valueOf(reqObj.LAST_UPDATE_DATE) : null;

                                    pol.add(o);

                                    salesforceOrderSet.add(reqObj.SALESFORCE_ORDER);
                                }
                            }
                        }
                    }
                }

                if(!ol.isEmpty()) {
                    updateRecords(ol, newReqObjList, resObj);
                }
                
                if(!pol.isEmpty()) {
                    updateRecords(pol, newReqObjList, resObj);
                }

                if(!warrantyReturnList.isEmpty()) {
                    updateRecords(warrantyReturnList, newReqObjList, resObj);
                }

                // 更新 reverse order
                if (!reverseOrderRequestList.isEmpty()) {
                    updateRecords(reverseOrderRequestList, newReqObjList, resObj);
                }

                if (resObj.Process_Result.size() == 0) {
                    resObj.Process_Status = 'Success';
                }else {
                    resObj.Process_Status = 'Fail';
                    String logId = Util.logIntegration('OrderTrack Exception','CCM_RestService_DealOrderTrackInfo','POST',JSON.serialize(resObj.Process_Result),JSON.serialize(newReqObjList), JSON.serialize(resObj));
                    // prettier-ignore
                    if (boolIsDealingOrder == true) pushExceptionEmail(logId, getMailErrorMessage(resObj));
                }

                System.debug(LoggingLevel.INFO, '*** : ' + JSON.serialize(resObj));
            }else{
                resObj.Process_Status = 'Fail';
                ReturnItem empty = new ReturnItem();
                empty.Error_Message = 'Empty JSON';
                empty.Error_Detail = 'Empty JSON';
                resObj.Process_Result.add(empty);
                String logId = Util.logIntegration('OrderTrack Exception','CCM_RestService_DealOrderTrackInfo','POST',JSON.serialize(resObj.Process_Result),JSON.serialize(newReqObjList), JSON.serialize(resObj));
                pushExceptionEmail(logId, getMailErrorMessage(resObj));
            }
        }catch (Exception e) {
            resObj.Process_Status = 'Fail';
            resObj.Process_Result = new List<ReturnItem>();
            for (ReqestObj reqObj : newReqObjList) {
                ReturnItem request = new ReturnItem();
                request.External_Id = reqObj.SALESFORCE_ORDER;
                request.Error_Message = 'This OrderTrack was failed saving in Salesforce';
                request.Error_Detail = '*** e.getMessage(): '+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage();
                resObj.Process_Result.add(request);
            }
            System.debug(LoggingLevel.INFO, '*** e.getMessage(): '+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage());
            Util.logIntegration(
                'Order Track Info Exception',
                'CCM_RestService_DealOrderTrackInfo',
                'doPost',
                e.getMessage() + CCM_Constants.NEXT_LINE + e.getStackTraceString(),
                req.requestBody.toString(),
                JSON.serialize(resObj)
            );
            String logId = Util.logIntegration('OrderTrack Exception','CCM_RestService_DealOrderTrackInfo','POST',
                                               JSON.serialize(resObj.Process_Result),JSON.serialize(newReqObjList), JSON.serialize(resObj));
            pushExceptionEmail(logId, getMailErrorMessage(resObj));
            return resObj;
        }
        if(Label.CCM_needlog == 'Y'){
           Util.logIntegration('OrderTrackInfo log','CCM_RestService_DealOrderTrackInfo', 'POST',
                               '',JSON.serialize(resStr), JSON.serialize(resObj));
        }
        System.debug(LoggingLevel.INFO, '*** resObj: ' + resObj);
        return resObj;
    }

    private static void processWarrantyReturnRequest(Map<String, Warranty_Return_Claim__c> warrantyReturnMap, ReqestObj reqObj, List<Warranty_Return_Claim__c> warrantyReturnList, ResultObj resObj, Set<String> salesforceOrderSet) {
        salesforceOrderSet.add(reqObj.SALESFORCE_ORDER);

        if(warrantyReturnMap.containsKey(reqObj.SALESFORCE_ORDER)) {
            Warranty_Return_Claim__c warrantyReturn = warrantyReturnMap.get(reqObj.SALESFORCE_ORDER);
            if(reqObj.PROCESS_STATUS == 'E') {
                warrantyReturn.Sync_Status__c = 'Failed';

                ReturnItem request = new ReturnItem();
                request.External_Id = reqObj.SALESFORCE_ORDER;
                request.Error_Message = 'This Order was failed processing in OMS';
                request.Error_Detail = '***The following error has occurred***' + reqObj.ERR_MSG;
                resObj.Process_Result.add(request);
            }
            else if(reqObj.PROCESS_STATUS == 'S') {
                warrantyReturn.Sync_Status__c = 'Success';
            }
            else {
                warrantyReturn.Sync_Status__c = 'Wait';
            }

            warrantyReturn.Sync_Message__c = reqObj.ERR_MSG;
            warrantyReturn.Sync_Date__c = String.isNotBlank(reqObj.LAST_UPDATE_DATE) ? Datetime.valueOf(reqObj.LAST_UPDATE_DATE) : null;

            if(warrantyReturnList == null) {
                warrantyReturnList = new List<Warranty_Return_Claim__c>();
            }
            warrantyReturnList.add(warrantyReturn);
        }
    }

    // 更新 reverse order request 的 status 字段
    private static void processReverseOrderRequest(Map<String, Reverse_Order_Request__c> reverseOrderRequestMap
                                                    , ReqestObj reqObj, List<Reverse_Order_Request__c> reverseOrderRequestList
                                                    , ResultObj resObj, Set<String> salesforceOrderSet) {
        //  判断去重
        salesforceOrderSet.add(reqObj.SALESFORCE_ORDER);

        if (reverseOrderRequestMap.containsKey(reqObj.SALESFORCE_ORDER)) {
            Reverse_Order_Request__c reverseOrderRequest = reverseOrderRequestMap.get(reqObj.SALESFORCE_ORDER);
            if (reqObj.PROCESS_STATUS == 'E') {
                ReturnItem request = new ReturnItem();
                request.External_Id = reqObj.SALESFORCE_ORDER;
                request.Error_Message = 'This Order was failed processing in OMS';
                request.Error_Detail = '***The following error has occurred***' + reqObj.ERR_MSG;
                resObj.Process_Result.add(request);
            }
            // 更新 error message 消息
            reverseOrderRequest.Sync_Message__c = reqObj.ERR_MSG;

            if (reverseOrderRequestList == null) {
                reverseOrderRequestList = new List<Reverse_Order_Request__c>();
            }
            reverseOrderRequestList.add(reverseOrderRequest);
        }
    }

    private static void updateRecords(List<SObject> records, List<ReqestObj> newReqObjList, ResultObj resObj) {
        Database.SaveResult[] saveResultList = Database.update(records,false);
        for (Integer i = 0 ; i < saveResultList.size() ; i++) {
            if (!saveResultList.get(i).isSuccess()) {
                Database.Error[] err = saveResultList.get(i).getErrors();
                ReturnItem request = new ReturnItem();
                request.External_Id = newReqObjList.get(i).SALESFORCE_ORDER;
                request.Error_Message = 'This OrderTrack was failed saving in Salesforce';
                request.Error_Detail = '***The following error has occurred***' + err.get(0).getStatusCode() + ' : ' +err.get(0).getMessage();
                resObj.Process_Result.add(request);
            }
        }
    }

    private static void pushExceptionEmail(Id idLog, String strRequest) {
        String strBody = '';
        Messaging.SingleEmailMessage objMessage;
        Set<Id> setSendToId = new Set<Id>();
        List<String> lstAdminEmail = Label.CCM_Order_Track_Info_Interface_Exception_Email_Receiver_Username_List.split(CCM_Constants.SEMICOLON_REGEXP_SPACE_IGNORED);
        List<Messaging.SingleEmailMessage> lstMessage;
        List<OrgWideEmailAddress> lstSender = [SELECT Id FROM OrgWideEmailAddress WHERE Address = :Label.CCM_Order_Track_Info_Interface_Exception_Email_Sender_Org_Wide_Address];
        // prettier-ignore
        if (lstSender.isEmpty() || lstAdminEmail.isEmpty()) return;
        for (User objU : [SELECT Id FROM User WHERE Username IN :lstAdminEmail]) {
            setSendToId.add(objU.Id);
        }
        // prettier-ignore
        if (setSendToId.isEmpty()) return;
        strBody = String.format(
            Label.CCM_Order_Track_Info_Interface_Exception_Email_Body,
            new List<String>{ Url.getSalesforceBaseUrl().toExternalForm() + '/' + (String.isBlank(idLog) ? '' : idLog), strRequest }
        );
        lstMessage = new List<Messaging.SingleEmailMessage>();
        for (Id idUser : setSendToId) {
            objMessage = new Messaging.SingleEmailMessage();
            objMessage.setSubject(Label.CCM_Order_Track_Info_Interface_Exception_Email_Subject);
            objMessage.setSaveAsActivity(false);
            objMessage.setTargetObjectId(idUser);
            objMessage.setHtmlBody(strBody);
            objMessage.setOrgWideEmailAddressId(lstSender[0].Id);
            lstMessage.add(objMessage);
        }
        try {
            // prettier-ignore
            if (!Test.isRunningTest()) Messaging.sendEmail(lstMessage);
            // prettier-ignore
            if (Test.isRunningTest()) throw new CCM_CustomException('test');
        } catch (Exception objE) {
            insert new Log__c(
                ApexName__c = 'CCM_RestService_DealOrderTrackInfo',
                Error_Message__c = objE.getMessage() + CCM_Constants.NEXT_LINE + objE.getStackTraceString(),
                Method__c = 'pushExceptionEmail',
                Name = 'Failed in sending Order Track Info Exception Emails at ' + Datetime.now().format(CCM_Constants.DATETIME_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS),
                ReqParam__c = strRequest,
                ResParam__c = JSON.serialize(lstMessage)
            );
        }
    }

    public static String getMailErrorMessage(ResultObj res){
        String errContent = '';
       	errContent += 'Process Status : Fail<br/>';
        if(res.Process_Result.size() > 0){
            for(ReturnItem Item : res.Process_Result){
            	errContent += 'External ID : ' + Item.External_Id + '<br/>';
            	errContent += 'Error Message : ' + Item.Error_Message +'<br/>';
            	errContent += 'Error Detail : '+ Item.Error_Detail +'<br/>';
            }
        }
        return errContent;
    }

    global class ReqestObj {
		global String SALESFORCE_ORDER;
		global String ORDER_NUMBER;
		global String HEADER_ID;
		global String PROCESS_STATUS;
		global String ERR_MSG;
		global String LAST_UPDATE_DATE;
		global String TRACKING_NO;
		global String ATTRIBUTE1;
        global String ATTRIBUTE2;
        global String ATTRIBUTE3;
        global String ATTRIBUTE4;
        global String ATTRIBUTE5;
        global String ATTRIBUTE6;
        global String ATTRIBUTE7;
        global String ATTRIBUTE8;
        global String ATTRIBUTE9;
        global String ATTRIBUTE10;
	}

	global static List<ReqestObj> parse(String jsonStr) {
		return (List<ReqestObj>) JSON.deserialize(jsonStr, List<ReqestObj>.class);
    }

    global class ResultObj {
        global String Process_Status;
        global List<ReturnItem> Process_Result;
    }

    global class ReturnItem {
        global String External_Id;
        global String Error_Message;
        global String Error_Detail;
    }
}