/**
 * <AUTHOR>
 * @date 2019-05-29
 * @description This Class is used for product lookup component in case and order in lightning.
 */
public class CCM_ProductLookUpController {
    public class ProductJson {
        public String Id { get; set; } //产品id
        public String Name { get; set; } //产品名称
        public String ProductCode {get; set;}
        public ProductJson(Product2 p) {
            this.Id = p.Id;
            this.Name = p.Name;
        }
        public ProductJson(String idValue, String nameValue) {
            this.Id = idValue;
            this.Name = nameValue;
        }
    }
    @AuraEnabled
    public static Map<Id, Storage__c> storageMap { get; set; }

    @AuraEnabled
    public static List<Product2> productLookUp(String caseType, String warrantyId, String brandName, String filterStr) {
        String filterStrTemp = '%' + filterStr + '%';

        //获取当前用户的所在国家
        User currentUser = SystemUtils.getCurrentUserInfo();
        String currentUserCountry = String.isNotBlank(currentUser.Country_of_Origin__c) ? currentUser.Country_of_Origin__c : 'Global';

        //获取warranty item中的product id
        Set<String> warrantyItemProductId = new Set<String>();
        Warranty__c warranty = WarrantyService.getWarrantySimpleByIDforCase(warrantyId);
        if (warranty != null) {
            warrantyItemProductId.add(warranty.Master_Product__c);
            for (Warranty_Item__c warrantyItem : warranty.Warranty_Items__r) {
                warrantyItemProductId.add(warrantyItem.Product__c);
            }

        }

        List<Product2> productList = new List<Product2>();
        List<Product2> warrantItemProductList = new List<Product2>();
        warrantItemProductList = ProductService.getProduct2ForLookupPage(warrantyItemProductId, currentUserCountry, brandName);
        // if(warrantItemProductList != null) {
        //     productList.addAll(warrantItemProductList);
        // }
        System.debug(LoggingLevel.INFO, '*** warrantItemProductList: ' + warrantItemProductList);
        //若有Warranty 不管什么case type都只能选择warranty item
        if (warranty != null) {
            for (Product2 pro : warrantItemProductList) {
                if ((pro.Name != null && pro.Name.contains(filterStr)) || (pro.ProductCode != null && pro.ProductCode.contains(filterStr))) {
                    productList.add(pro);
                }
            }
            Product2 warrantyProduct= [SELECT Id,Name,ProductCode,Country_of_Origin__c,
                                Description,Family,RecordType.Name, Brand_Name__c, Repairable__c
                            FROM Product2 WHERE Id != NULL
                            AND Is_History_Product__c = :CCM_Constants.blHistoryProduct
                            AND Id =: warranty.Master_Product__c LIMIT 1];

                productList.add(warrantyProduct);
        } else {
            productList = ProductService.getProduct2ForLookupPageFilter(filterStrTemp, currentUserCountry, brandName);
        }
        System.debug(LoggingLevel.INFO, '*** productList.size(): ' + productList);
        return productList;
    }
    @AuraEnabled
    public static Contact getCaseGeoContact(String caseId){
        Contact objCon = new Contact();
        List<Contact> geoConList = [SELECT Id,Mailinglatitude,MailingCountry,Mailinglongitude FROM Contact WHERE LastName=:caseId AND FirstName='GeolocationContactOrder' AND Mailinglatitude!=NULL AND Mailinglongitude!=NULL LIMIT 1];
        if(geoConList.size()>0){
           objCon = geoConList[0];
        }
        return objCon;
    }


    @AuraEnabled
    public static String InitProductList(String caseId, String warId, String brand, String type, String filterStr, Id idOrderRecordType) {
        System.debug('caseId:' + caseId);
        System.debug('warId:' + warId);
        System.debug('brand:' + brand);
        System.debug('type:' + type);
        System.debug('filterStr:' + filterStr);
        System.debug('idOrderRecordType:' + idOrderRecordType);

        Map<Id, Storage__c> replacedStorageMap = new Map<Id, Storage__c>();
        Map<Id, Storage__c> replacedStorageMap1 = new Map<Id, Storage__c>();
        Map<Id, List<Storage__c>> replacedStorageMapX = new Map<Id, List<Storage__c>>();
        List<Storage__c> allReplacedStorageList = new List<Storage__c>();
        Map<String, String> productSearchKeyMap = new Map<String, String>();
        String newFilterStr = '%' + filterStr.toLowerCase() + '%';
        Set<String> replacementProducts = new Set<String>();
        Set<String> replacementProductsX = new Set<String>();

        CCM_NewOrderController.getOrder(caseId, warId, idOrderRecordType);
        CCM_NewOrderController.getProductBook(brand, '', caseId);
        Integer checkResult = WebServiceUtil.checkWarrantyCanNewOrder(warId, caseId);

        Set<String> set_proids = CCM_NewOrderController.set_proids;
        System.debug('*** :' + JSON.serialize(set_proids));

        List<ProductJson> selectProduct = new List<ProductJson>();
        String tempJsonStr = '';
        List<SelectOption> optionList = new List<SelectOption>();
        Map<String, String> storageProductCodeMap = new Map<String, String>();
        Set<String> storageIdSet = new Set<String>();
        Set<String> proCodeSet = new Set<String>();
        Set<String> upgradeIdSet = new Set<String>();
        // Boolean isHaveProduct = false;
        Map<String, Boolean> isHaveProductMap = new Map<String, Boolean>();
        Map<String, Set<String>> proOptionMap = new Map<String,Set<String>>();
        Set<String> storageNameSet = new Set<String>();
        Boolean isCNA = true;
        User currentUser1 = [SELECT Id,Profile.Name FROM User WHERE Id=:UserInfo.getUserId() LIMIT 1];
        List<Contact> geoConList1 = [SELECT Id,Mailinglatitude,MailingCountry,Mailinglongitude FROM Contact WHERE LastName=:caseId AND FirstName='GeolocationContactOrder' AND Mailinglatitude!=NULL AND Mailinglongitude!=NULL LIMIT 1];
        if(geoConList1.size() >0){
            Location currentLocation = Location.newInstance(geoConList1[0].MailingLatitude,geoConList1[0].MailingLongitude);
            if(!(geoConList1[0].MailingCountry == 'US' || geoConList1[0].MailingCountry == 'United States')){
                isCNA = false;
            }
        }
        System.debug('*** isCNA:' + isCNA);

        Map<String, String> sameProductCodeMap = new Map<String, String>();
        for(Same_ProductCode__mdt temp : [select Original_ProductCode__c, New_ProductCode__c from Same_ProductCode__mdt]){
            sameProductCodeMap.put(temp.Original_ProductCode__c, temp.New_ProductCode__c);
        }
        System.debug('*** sameProductCodeMap:' + sameProductCodeMap);

        Set<Id> productIdSet = new Set<Id>();
        Set<String> productCodeSet = new Set<String>();
        if (CCM_NewOrderController.thisWarranty != null) {
            System.debug(LoggingLevel.INFO, '*** CCM_NewOrderController.thisWarranty : ' + CCM_NewOrderController.thisWarranty);
            System.debug(LoggingLevel.INFO, '*** CCCM_NewOrderController.thisWarranty.Warranty_Items__r : ' + CCM_NewOrderController.thisWarranty.Warranty_Items__r);

            for (Warranty_Item__c warrantyItem : CCM_NewOrderController.thisWarranty.Warranty_Items__r) {
                // prettier-ignore
                if (warrantyItem.Product__r.Brand_Name__c != brand && !Test.isRunningTest()) continue;
                if (type == null || (type == 'CNA Sample and Warranty Order') || (type == 'Recall Order' && CCM_NewOrderController.thisCase.Warranty_Item__c == warrantyItem.Id)) {
                    productIdSet.add(warrantyItem.Product__c);
                    productCodeSet.add(warrantyItem.Product__r.ProductCode + '-FC');
                    productCodeSet.add(warrantyItem.Product__r.ProductCode + '-RT');
                    productCodeSet.add(warrantyItem.Product__r.ProductCode + 'T');
                    productCodeSet.add(warrantyItem.Product__r.ProductCode + 'T-FC');
                    productCodeSet.add('CS-' + warrantyItem.Product__r.ProductCode + 'T');
                    productCodeSet.add('CS-' + warrantyItem.Product__r.ProductCode);
                    productCodeSet.add('CS1-' + warrantyItem.Product__r.ProductCode + '-FC');
                    // CS2- and CS3- products are not allowed for any profile
                    // if (!CCM_NewOrderController.currentUser.Profile.Name.endsWith('Customer Service')) {
                    //     productCodeSet.add('CS2-' + warrantyItem.Product__r.ProductCode + '-FC');
                    // }
                    productCodeSet.add(warrantyItem.Product__r.ProductCode);

                    if(sameProductCodeMap.containsKey(warrantyItem.Product__r.ProductCode)){
                        productCodeSet.add(sameProductCodeMap.get(warrantyItem.Product__r.ProductCode) + '-FC');
                        productCodeSet.add(sameProductCodeMap.get(warrantyItem.Product__r.ProductCode) + '-RT');
                        productCodeSet.add(sameProductCodeMap.get(warrantyItem.Product__r.ProductCode) + 'T');
                        productCodeSet.add(sameProductCodeMap.get(warrantyItem.Product__r.ProductCode) + 'T-FC');
                        productCodeSet.add('CS-' + sameProductCodeMap.get(warrantyItem.Product__r.ProductCode) + 'T');
                        productCodeSet.add('CS-' + sameProductCodeMap.get(warrantyItem.Product__r.ProductCode));
                        productCodeSet.add('CS1-' + sameProductCodeMap.get(warrantyItem.Product__r.ProductCode) + '-FC');
                        // CS2- and CS3- products are not allowed for any profile
                        // if (!CCM_NewOrderController.currentUser.Profile.Name.endsWith('Customer Service')) {
                        //     productCodeSet.add('CS2-' + sameProductCodeMap.get(warrantyItem.Product__r.ProductCode) + '-FC');
                        // }
                        productCodeSet.add(sameProductCodeMap.get(warrantyItem.Product__r.ProductCode));
                        }
                }
            }
            productCodeSet.add('2827826002');
        }
        if(checkResult == 200){
            Case thisCase = [SELECT Id,Product_Code__c FROM Case WHERE Id =:caseId Limit 1];
            List<Warranty_Item__c> warrItemList = [SELECT Id,Product__c,Product__r.ProductCode FROM Warranty_Item__c WHERE Warranty__c =:warId AND Product__r.ProductCode =:thisCase.Product_Code__c];
            for (Warranty_Item__c warrantyItem : warrItemList) {
                if (type == null || (type == 'CNA Sample and Warranty Order') || (type == 'Recall Order' && CCM_NewOrderController.thisCase.Warranty_Item__c == warrantyItem.Id)) {
                    productIdSet.add(warrantyItem.Product__c);
                    productCodeSet.add(warrantyItem.Product__r.ProductCode + '-FC');
                    productCodeSet.add(warrantyItem.Product__r.ProductCode + '-RT');
                    productCodeSet.add(warrantyItem.Product__r.ProductCode + 'T');
                    productCodeSet.add(warrantyItem.Product__r.ProductCode + 'T-FC');
                    productCodeSet.add('CS-' + warrantyItem.Product__r.ProductCode + 'T');
                    productCodeSet.add('CS-' + warrantyItem.Product__r.ProductCode);
                    productCodeSet.add('CS1-' + warrantyItem.Product__r.ProductCode + '-FC');
                    // CS2- and CS3- products are not allowed for any profile
                    // if (!CCM_NewOrderController.currentUser.Profile.Name.endsWith('Customer Service')) {
                    //     productCodeSet.add('CS2-' + warrantyItem.Product__r.ProductCode + '-FC');
                    // }
                    productCodeSet.add(warrantyItem.Product__r.ProductCode);

                    if(sameProductCodeMap.containsKey(warrantyItem.Product__r.ProductCode)){
                        productCodeSet.add(sameProductCodeMap.get(warrantyItem.Product__r.ProductCode) + '-FC');
                        productCodeSet.add(sameProductCodeMap.get(warrantyItem.Product__r.ProductCode) + '-RT');
                        productCodeSet.add(sameProductCodeMap.get(warrantyItem.Product__r.ProductCode) + 'T');
                        productCodeSet.add(sameProductCodeMap.get(warrantyItem.Product__r.ProductCode) + 'T-FC');
                        productCodeSet.add('CS-' + sameProductCodeMap.get(warrantyItem.Product__r.ProductCode) + 'T');
                        productCodeSet.add('CS-' + sameProductCodeMap.get(warrantyItem.Product__r.ProductCode));
                        productCodeSet.add('CS1-' + sameProductCodeMap.get(warrantyItem.Product__r.ProductCode) + '-FC');
                        // CS2- and CS3- products are not allowed for any profile
                        // if (!CCM_NewOrderController.currentUser.Profile.Name.endsWith('Customer Service')) {
                        //     productCodeSet.add('CS2-' + sameProductCodeMap.get(warrantyItem.Product__r.ProductCode) + '-FC');
                        // }
                        productCodeSet.add(sameProductCodeMap.get(warrantyItem.Product__r.ProductCode));
                    }
                }
            }
            productCodeSet.add('2827826002');

            Set<String> alternativeProducts = CCM_ServiceOrderUtil.isInAlternativeScope(caseId);
            productCodeSet.addAll(alternativeProducts);
        }
        System.debug(LoggingLevel.INFO, '*** type: ' + type);
        System.debug(LoggingLevel.INFO, '*** productIdSet: ' + productIdSet);
        System.debug(LoggingLevel.INFO, '*** productCodeSet: ' + productCodeSet);
        System.debug(LoggingLevel.INFO, '*** currentUser.Profile.Name: ' + CCM_NewOrderController.currentUser.Profile.Name);
        storageMap = new Map<Id, Storage__c>();

        //all the products with AO has to be excluded from the suggestion.
        //List<Product2> productsWithAO = new List<Product2>([SELECT Id FROM Product2 WHERE Description LIKE '%(AO%']);
        //Minimum Inventory and EBS status to exclude.
        Product_Lookup_Setting__mdt productSetting = [SELECT Inventory__c, EBS_Status_To_Exclude__c FROM Product_Lookup_Setting__mdt WHERE Label = 'Order Product'];
        // prettier-ignore
        if (idOrderRecordType == CCM_Constants.ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID) productSetting.Inventory__c = -1;
        List<String> ebsStatusToExclude = String.isNotEmpty(productSetting.EBS_Status_To_Exclude__c) ? productSetting.EBS_Status_To_Exclude__c.split(',') : new List<String>();
        System.debug('ebsStatusToExclude:' + ebsStatusToExclude);
        System.debug('productSetting:' + productSetting);
        if (CCM_NewOrderController.currentUser.Profile.Name.endsWith('Customer Service')) {
            List<Storage__c> storageList = new List<Storage__c>();
            List<Storage__c> storageSelectList = new List<Storage__c>();
            System.debug('***3');
            if (type == 'Recall Package') {
                //System.debug('enter if 123123');
                storageList = [
                    SELECT Id, Product__r.EBS_Status__c, Product__r.New_Product__c,
                    Product__r.New_Product__r.ProductCode, Product__r.IsActive, Available_Inventory__c
                    , Product__c, Product__r.Name, Product__r.ProductCode, Product__r.Brand_Name__c
                    , Product__r.Item_Number__c, Sub_storage__c
                    FROM Storage__c WHERE Id != NULL
                        AND (Product__r.New_Product__c != null OR (Product__r.IsActive = true
                        AND Available_Inventory__c >= :productSetting.Inventory__c
                        AND Product__r.EBS_Status__c NOT IN :ebsStatusToExclude))
                        AND Product__c IN :set_proids
                        AND Product__r.Source__c = 'EBS'
                        AND (Product__r.Name LIKE :newFilterStr
                                    OR Product__r.ProductCode LIKE :newFilterStr)
                        AND Product__r.Unavailable_For_Select__c = false
                        AND (NOT Product__r.Description  LIKE '%FD Only%')
                        ORDER BY Product__r.Search_Sequence__c];
            } else {
                for (Case objC : [SELECT Case_Type__c FROM Case WHERE Id = :caseId]) {
                    System.debug('enter else 1' + objC.Case_Type__c);
                    System.debug('enter else 2' + newFilterStr);
                    System.debug('enter else 3' + productSetting.Inventory__c);
                    if (objC.Case_Type__c == 'Non-warranty Order') {
                        storageSelectList = [
                            SELECT Id, Product__r.EBS_Status__c, Product__r.New_Product__c, Product__r.New_Product__r.ProductCode, Product__r.IsActive, Available_Inventory__c, Product__c, Product__r.Name, Product__r.ProductCode, Product__r.Brand_Name__c, Product__r.Item_Number__c, Sub_storage__c
                            FROM Storage__c
                            WHERE
                                (Product__r.New_Product__c != null OR (Product__r.IsActive = true
                                AND Available_Inventory__c >= :productSetting.Inventory__c
                                AND Product__r.EBS_Status__c NOT IN :ebsStatusToExclude))
                                AND Product__c IN :set_proids
                                AND Product__r.Source__c = 'EBS'
                                AND (Product__r.Name LIKE :newFilterStr
                                    OR Product__r.ProductCode LIKE :newFilterStr)
                                AND Product__r.Unavailable_For_Select__c = false
                                AND (NOT Product__r.Description  LIKE '%FD Only%')
                                ORDER BY Product__r.Search_Sequence__c ];
                        if (idOrderRecordType == CCM_Constants.ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID){
                            storageSelectList = [
                                SELECT Id, Product__r.EBS_Status__c, Product__r.New_Product__c, Product__r.New_Product__r.ProductCode, Product__r.IsActive, Available_Inventory__c, Product__c, Product__r.Name, Product__r.ProductCode, Product__r.Brand_Name__c, Product__r.Item_Number__c, Sub_storage__c
                                FROM Storage__c
                                WHERE
                                    (Product__r.New_Product__c != null OR (Product__r.IsActive = true
                                    AND Available_Inventory__c >= :productSetting.Inventory__c))
                                    AND Product__r.Source__c = 'EBS'
                                    AND (Product__r.Name LIKE :newFilterStr
                                        OR Product__r.ProductCode LIKE :newFilterStr)
                                    AND Product__r.Unavailable_For_Select__c = false
                                    AND (NOT Product__r.Description  LIKE '%FD Only%')
                                    ORDER BY Product__r.Search_Sequence__c];
                        }
                    } else {
                        storageSelectList = [
                            SELECT Id, Product__r.EBS_Status__c, Product__r.New_Product__c, Product__r.New_Product__r.ProductCode, Product__r.IsActive, Available_Inventory__c, Product__c, Product__r.Name, Product__r.ProductCode, Product__r.Brand_Name__c, Product__r.Item_Number__c, Sub_storage__c
                            FROM Storage__c
                            WHERE
                                (Product__c IN :productIdSet
                                OR Product__r.ProductCode IN :productCodeSet)
                                AND (Product__r.New_Product__c != null OR (Product__r.IsActive = true
                                AND Available_Inventory__c >= :productSetting.Inventory__c
                                AND Product__r.EBS_Status__c NOT IN :ebsStatusToExclude))
                                AND Product__c IN :set_proids
                                AND Product__r.Source__c = 'EBS'
                                AND (Product__r.Name LIKE :newFilterStr
                                    OR Product__r.ProductCode LIKE :newFilterStr)
                                AND Product__r.Unavailable_For_Select__c = false
                                AND (NOT Product__r.ProductCode LIKE '%FOR%')
                                AND (NOT Product__r.Description  LIKE '%FD Only%')
                                ORDER BY Product__r.Search_Sequence__c];

                        if (idOrderRecordType == CCM_Constants.ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID){
                            storageSelectList = [
                                SELECT Id, Product__r.EBS_Status__c, Product__r.New_Product__c, Product__r.New_Product__r.ProductCode, Product__r.IsActive, Available_Inventory__c, Product__c, Product__r.Name, Product__r.ProductCode, Product__r.Brand_Name__c, Product__r.Item_Number__c, Sub_storage__c
                                FROM Storage__c
                                WHERE
                                    (Product__c IN :productIdSet
                                    OR Product__r.ProductCode IN :productCodeSet)
                                    AND (Product__r.New_Product__c != null OR (Product__r.IsActive = true
                                    AND Available_Inventory__c >= :productSetting.Inventory__c))
                                    AND Product__r.Source__c = 'EBS'
                                    AND (Product__r.Name LIKE :newFilterStr
                                    OR Product__r.ProductCode LIKE :newFilterStr)
                                    AND Product__r.Unavailable_For_Select__c = false
                                    AND (NOT Product__r.ProductCode LIKE '%FOR%')
                                    AND (NOT Product__r.Description  LIKE '%FD Only%')
                                    ORDER BY Product__r.Search_Sequence__c];
                        }
                    }
                }
            }
            System.debug(LoggingLevel.INFO, '*** storageSelectList.size(): ' + storageSelectList.size());
            for(Storage__c st : storageSelectList) {
                // prettier-ignore
                if (st.Product__r.Brand_Name__c != brand) continue;
                if(st.Product__r.New_Product__c != null) {
                    productSearchKeyMap.put(st.Product__c, st.Product__r.New_Product__r.ProductCode);
                    //String tempProductCode = '%'+st.Product__r.New_Product__r.ProductCode+'%';
                    String tempProductCode = 'CS-'+st.Product__r.New_Product__r.ProductCode+'%';
                    replacementProductsX.add(tempProductCode);
                    // remove CS2 selection for Customer Service
                    // tempProductCode = 'CS2-'+st.Product__r.New_Product__r.ProductCode+'%';
                    // replacementProductsX.add(tempProductCode);
                    tempProductCode = '%'+st.Product__r.New_Product__r.ProductCode+'-FC';
                    replacementProductsX.add(tempProductCode);
                    replacementProductsX.add(st.Product__r.New_Product__r.ProductCode);

                    replacementProducts.add(st.Product__r.New_Product__c);
                }
                storageList.add(st);
            }
            System.debug(LoggingLevel.INFO, '*** storageList.size(): ' + storageList.size());

            if (idOrderRecordType == CCM_Constants.ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID){
                for(Storage__c strg : [SELECT Id,Product__r.EBS_Status__c, Product__r.New_Product__c, Product__c, Product__r.Name, Product__r.ProductCode, Product__r.Brand_Name__c,
                                   Product__r.Item_Number__c, Sub_storage__c, Product__r.IsActive, Available_Inventory__c
                                   FROM Storage__c
                                   WHERE
                                   Product__r.ProductCode LIKE :replacementProductsX
                                   AND Product__r.IsActive = true
                                   AND Available_Inventory__c >= :productSetting.Inventory__c
                                   AND Product__r.Source__c = 'EBS'
                                   AND Product__r.Country_of_Origin__c = 'United States'
                                   AND Product__r.Unavailable_For_Select__c = false
                                   AND (NOT Product__r.Description  LIKE '%FD Only%')
                                   AND Product__r.Recordtype.Name = 'Product' ORDER BY Product__r.Search_Sequence__c]) {
                                    replacedStorageMap.put(strg.Product__c, strg);
                                    replacedStorageMap1.put(strg.Id, strg);
                                    allReplacedStorageList.add(strg);
                    if(replacedStorageMapX.containsKey(strg.Product__c)) {
                        replacedStorageMapX.get(strg.Product__c).add(strg);
                    } else {
                        replacedStorageMapX.put(strg.Product__c, new List<Storage__c>{strg});
                    }
                }
            }else{
                //get the replaced products
                for(Storage__c strg : [SELECT Id,Product__r.EBS_Status__c, Product__r.New_Product__c, Product__c, Product__r.Name, Product__r.ProductCode, Product__r.Brand_Name__c,
                                    Product__r.Item_Number__c, Sub_storage__c, Product__r.IsActive, Available_Inventory__c
                                    FROM Storage__c
                                    WHERE
                                    Product__r.ProductCode LIKE :replacementProductsX
                                    AND Product__r.IsActive = true
                                    AND Available_Inventory__c >= :productSetting.Inventory__c
                                    AND Product__r.Source__c = 'EBS'
                                    AND Product__r.EBS_Status__c NOT IN :ebsStatusToExclude
                                    AND Product__r.Country_of_Origin__c = 'United States'
                                    AND Product__r.Unavailable_For_Select__c = false
                                    AND (NOT Product__r.Description  LIKE '%FD Only%')
                                    AND Product__r.Recordtype.Name = 'Product' ORDER BY Product__r.Search_Sequence__c]) {
                                        replacedStorageMap.put(strg.Product__c, strg);
                                        replacedStorageMap1.put(strg.Id, strg);
                                        allReplacedStorageList.add(strg);
                    if(replacedStorageMapX.containsKey(strg.Product__c)) {
                        replacedStorageMapX.get(strg.Product__c).add(strg);
                    } else {
                        replacedStorageMapX.put(strg.Product__c, new List<Storage__c>{strg});
                    }
                }
            }

            if (idOrderRecordType == CCM_Constants.ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID){
                for(Storage__c st : storageList) {
                    if((productIdSet.contains(st.Product__c)|| st.Product__r.ProductCode.contains('CS-') || st.Product__r.ProductCode.endsWith('T') || st.Product__r.ProductCode.endsWith('T(C)') || st.Product__r.ProductCode.endsWith('T-FC')) && st.Product__r.IsActive  && st.Available_Inventory__c > productSetting.Inventory__c ){
                        System.debug('*** st:' + st);
                        System.debug('*** st2:' + st.Sub_storage__c);
                        if(isCNA){
                            if(st.Sub_storage__c.contains('CNA')){
                                isHaveProductMap.put(st.Product__r.ProductCode, true);
                                // isHaveProduct = true;
                            }
                        }else{
                            if(st.Sub_storage__c.contains('CA')){
                                isHaveProductMap.put(st.Product__r.ProductCode, true);
                                // isHaveProduct = true;
                            }
                        }
                    }
                }
            }else{
                for(Storage__c st : storageList) {
                    if((productIdSet.contains(st.Product__c)|| st.Product__r.ProductCode.contains('CS-') || st.Product__r.ProductCode.endsWith('T') || st.Product__r.ProductCode.endsWith('T(C)') || st.Product__r.ProductCode.endsWith('T-FC')) && st.Product__r.IsActive  && st.Available_Inventory__c > productSetting.Inventory__c  && !ebsStatusToExclude.contains(st.Product__r.EBS_Status__c)){
                        System.debug('*** st:' + st);
                        System.debug('*** st2:' + st.Sub_storage__c);
                        if(isCNA){
                            if(st.Sub_storage__c.contains('CNA')){
                                isHaveProductMap.put(st.Product__r.ProductCode, true);
                                // isHaveProduct = true;
                            }
                        }else{
                            if(st.Sub_storage__c.contains('CA')){
                                isHaveProductMap.put(st.Product__r.ProductCode, true);
                                // isHaveProduct = true;
                            }
                        }
                    }
                }
            }
            System.debug('*** isHaveProductMap:' + isHaveProductMap);
            System.debug('*** storageList:' + JSON.serialize(storageList));
            System.debug('*** allReplacedStorageList:' + JSON.serialize(allReplacedStorageList));
            for(Storage__c st : storageList) {
                if(st.Product__r.New_Product__c == null || replacedStorageMapX.containsKey(st.Product__r.New_Product__c) == false) {
                    if (idOrderRecordType == CCM_Constants.ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID){
                        if(st.Product__r.IsActive = true  && (st.Available_Inventory__c > productSetting.Inventory__c || (st.Available_Inventory__c >= productSetting.Inventory__c && st.Product__r.ProductCode == 'SR211601'))) {
                            storageMap.put(st.Id, st);
                            optionList.add(new SelectOption(st.Id, st.Product__r.Name + '(' +
                                                            st.Product__r.ProductCode + ',' + st.Product__r.Item_Number__c + ') ' +
                                                            st.Sub_storage__c));
                            storageProductCodeMap.put(st.Id, st.Product__r.ProductCode);
                            String key = String.valueOf(st.Product__r.ProductCode) + ' ' + st.Sub_storage__c;
                            if(!proOptionMap.containsKey(key)) {
                                proOptionMap.put(key, new Set<String>());
                            }
                            proOptionMap.get(key).add(st.Id);
                            storageNameSet.add(st.Sub_storage__c);
                            proCodeSet.add(st.Product__r.ProductCode);
                        }
                    }else{
                        if(st.Product__r.IsActive = true  &&
                        ((st.Available_Inventory__c > productSetting.Inventory__c && !ebsStatusToExclude.contains(st.Product__r.EBS_Status__c)
                        ||(st.Available_Inventory__c >= productSetting.Inventory__c && st.Product__r.ProductCode == 'SR211601')))) {
                            storageMap.put(st.Id, st);
                            optionList.add(new SelectOption(st.Id, st.Product__r.Name + '(' +
                                                            st.Product__r.ProductCode + ',' + st.Product__r.Item_Number__c + ') ' +
                                                            st.Sub_storage__c));
                            storageProductCodeMap.put(st.Id, st.Product__r.ProductCode);
                            String key = String.valueOf(st.Product__r.ProductCode) + ' ' + st.Sub_storage__c;
                            if(!proOptionMap.containsKey(key)) {
                                proOptionMap.put(key, new Set<String>());
                            }
                            proOptionMap.get(key).add(st.Id);
                            storageNameSet.add(st.Sub_storage__c);
                            proCodeSet.add(st.Product__r.ProductCode);
                        }
                    }
                } else {
                    List<Storage__c> replacedStorageList = replacedStorageMapX.get(st.Product__r.New_Product__c);
                    if (replacedStorageList != null) {
                        if (idOrderRecordType == CCM_Constants.ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID){
                            if (st.Product__r.IsActive = true  && st.Available_Inventory__c > productSetting.Inventory__c) {
                                //storageMap.put(st.Id, st);
                                optionList.add(new SelectOption(st.Id, st.Product__r.Name + '(' +
                                                                st.Product__r.ProductCode + ',' + st.Product__r.Item_Number__c + ') ' +
                                                                st.Sub_storage__c));
                                storageProductCodeMap.put(st.Id, st.Product__r.ProductCode);
                                String key = String.valueOf(st.Product__r.ProductCode) + ' ' + st.Sub_storage__c;
                                if(!proOptionMap.containsKey(key)) {
                                    proOptionMap.put(key, new Set<String>());
                                }
                                proOptionMap.get(key).add(st.Id);
                                storageNameSet.add(st.Sub_storage__c);
                                proCodeSet.add(st.Product__r.ProductCode);
                            }
                        }else{
                            if (st.Product__r.IsActive = true  && st.Available_Inventory__c > productSetting.Inventory__c && !ebsStatusToExclude.contains(st.Product__r.EBS_Status__c)) {
                                //storageMap.put(st.Id, st);
                                optionList.add(new SelectOption(st.Id, st.Product__r.Name + '(' +
                                                                st.Product__r.ProductCode + ',' + st.Product__r.Item_Number__c + ') ' +
                                                                st.Sub_storage__c));
                                storageProductCodeMap.put(st.Id, st.Product__r.ProductCode);
                                String key = String.valueOf(st.Product__r.ProductCode) + ' ' + st.Sub_storage__c;
                                if(!proOptionMap.containsKey(key)) {
                                    proOptionMap.put(key, new Set<String>());
                                }
                                proOptionMap.get(key).add(st.Id);
                                storageNameSet.add(st.Sub_storage__c);
                                proCodeSet.add(st.Product__r.ProductCode);
                            }
                        }
                        if(!isHaveProductMap.containsKey(st.Product__r.ProductCode)){
                            for(Storage__c replacedStorage : allReplacedStorageList) {
                                System.debug('*** replacedStorage:' + replacedStorage);
                                String replacedProductCode = replacedStorage.Product__r.ProductCode;
                                // if(!replacedProductCode.containsIgnoreCase('(C)') && replacedProductCode.containsIgnoreCase(replacedStorageList[0]?.Product__r?.ProductCode)) {
                                // temporary change
                                if(!upgradeIdSet.contains(replacedStorage.Product__c+replacedStorage.Sub_storage__c)){
                                    if(replacedProductCode.containsIgnoreCase(replacedStorageList[0]?.Product__r?.ProductCode)) {
                                        optionList.add(new SelectOption(replacedStorage.Id, replacedStorage.Product__r.Name + '(' +
                                                                        replacedStorage.Product__r.ProductCode + ',' + replacedStorage.Product__r.Item_Number__c + ') ' +
                                                                        replacedStorage.Sub_storage__c));
                                        storageProductCodeMap.put(replacedStorage.Id, replacedStorage.Product__r.ProductCode);
                                        String key = String.valueOf(replacedStorage.Product__r.ProductCode) + ' ' + replacedStorage.Sub_storage__c;
                                        if(!proOptionMap.containsKey(key)) {
                                            proOptionMap.put(key, new Set<String>());
                                        }
                                        proOptionMap.get(key).add(replacedStorage.Id);
                                        storageNameSet.add(replacedStorage.Sub_storage__c);
                                        proCodeSet.add(replacedStorage.Product__r.ProductCode);
                                        upgradeIdSet.add(replacedStorage.Product__c+replacedStorage.Sub_storage__c);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } else {
            List<Storage__c> storageSelectList = new List<Storage__c>();
            List<Storage__c> storageList = new List<Storage__c>();
            if (idOrderRecordType == CCM_Constants.ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID){
                storageSelectList = [SELECT Id, Product__r.EBS_Status__c, Product__r.New_Product__c
                                              , Product__r.New_Product__r.ProductCode, Product__r.IsActive, Available_Inventory__c
                                              , Product__c, Product__r.Name, Product__r.ProductCode, Product__r.Brand_Name__c
                                              , Product__r.Item_Number__c, Sub_storage__c
                                    FROM Storage__c
                                    WHERE
                                    (Product__r.New_Product__c != null OR (Product__r.IsActive = true
                                    AND Available_Inventory__c >= :productSetting.Inventory__c))
                                    AND Product__r.Source__c = 'EBS'
                                    AND Product__r.RecordType.DeveloperName = 'Product'
                                    AND (Product__r.Name LIKE :newFilterStr
                                                    OR Product__r.ProductCode LIKE :newFilterStr)
                                    AND Product__r.Unavailable_For_Select__c = false
                                    AND (NOT Product__r.Description  LIKE '%FD Only%')
                                    ORDER BY Product__r.Search_Sequence__c ];

                for(Storage__c st : storageSelectList){
                    if (st.Product__r.Brand_Name__c != brand) continue;
                        if(st.Product__r.New_Product__c != null) {
                            replacementProducts.add(st.Product__r.New_Product__c);
                            String tempProductCode = 'CS-'+st.Product__r.New_Product__r.ProductCode+'%';
                            replacementProductsX.add(tempProductCode);
                            // CS2- and CS3- products are not allowed for any profile
                            // tempProductCode = 'CS2-'+st.Product__r.New_Product__r.ProductCode+'%';
                            // replacementProductsX.add(tempProductCode);
                            tempProductCode = '%'+st.Product__r.New_Product__r.ProductCode+'-FC';
                            replacementProductsX.add(tempProductCode);
                            replacementProductsX.add(st.Product__r.New_Product__r.ProductCode);
                            replacementProductsX.add(tempProductCode);
                        }
                    storageList.add(st);
                }
                System.debug('replacementProductsX:' + replacementProductsX);
                System.debug('storagelist:' + storageList);
                for(Storage__c strg : [SELECT Id, Product__r.EBS_Status__c,Product__r.New_Product__c, Product__c, Product__r.Name, Product__r.ProductCode, Product__r.Brand_Name__c,
                                                                                    Product__r.Item_Number__c, Sub_storage__c, Product__r.IsActive, Available_Inventory__c
                                                                                    FROM Storage__c
                                                                                    WHERE Product__r.ProductCode LIKE :replacementProductsX
                                                                                    AND Product__r.IsActive = true
                                                                                    AND Available_Inventory__c >= :productSetting.Inventory__c
                                                                                    AND Product__r.Source__c = 'EBS'
                                                                                    AND Product__r.Country_of_Origin__c = 'United States'
                                                                                    AND Product__r.Unavailable_For_Select__c = false
                                                                                    AND (NOT Product__r.Description  LIKE '%FD Only%')
                                                                                    AND Product__r.Recordtype.Name = 'Product' ORDER BY Product__r.Search_Sequence__c]) {
                            replacedStorageMap.put(strg.Product__c, strg);
                            replacedStorageMap1.put(strg.Id, strg);
                            allReplacedStorageList.add(strg);
                            if(replacedStorageMapX.containsKey(strg.Product__c)) {
                                replacedStorageMapX.get(strg.Product__c).add(strg);
                            }
                            else {
                                replacedStorageMapX.put(strg.Product__c, new List<Storage__c>{strg});
                            }
                        }
                System.debug('*** allReplacedStorageList:' + JSON.serialize(allReplacedStorageList));
                for(Storage__c st : storageList) {
                    if((productIdSet.contains(st.Product__c)|| st.Product__r.ProductCode.contains('CS-')|| st.Product__r.ProductCode.endsWith('T') || st.Product__r.ProductCode.endsWith('T(C)') || st.Product__r.ProductCode.endsWith('T-FC')) && st.Product__r.IsActive  && st.Available_Inventory__c > productSetting.Inventory__c){
                        if(isCNA){
                            if(st.Sub_storage__c.contains('CNA')){
                                isHaveProductMap.put(st.Product__r.ProductCode, true);
                                // isHaveProduct = true;
                            }
                        }else{
                            if(st.Sub_storage__c.contains('CA')){
                                isHaveProductMap.put(st.Product__r.ProductCode, true);
                                // isHaveProduct = true;
                            }
                        }

                    }
                }
                for(Storage__c st : storageList) {
                    if(st.Product__r.New_Product__c == null || replacedStorageMapX.containsKey(st.Product__r.New_Product__c) == false) {
                        if(st.Product__r.IsActive = true  && (st.Available_Inventory__c > productSetting.Inventory__c
                        || (st.Available_Inventory__c >= productSetting.Inventory__c && st.Product__r.ProductCode == 'SR211601'))) {
                            storageMap.put(st.Id, st);
                            optionList.add(new SelectOption(st.Id, st.Product__r.Name + '(' +
                                                            st.Product__r.ProductCode + ',' + st.Product__r.Item_Number__c + ') ' +
                                                            st.Sub_storage__c));
                            storageProductCodeMap.put(st.Id, st.Product__r.ProductCode);
                            String key = String.valueOf(st.Product__r.ProductCode) + ' ' + st.Sub_storage__c;
                            if(!proOptionMap.containsKey(key)) {
                                proOptionMap.put(key, new Set<String>());
                            }
                            proOptionMap.get(key).add(st.Id);
                            storageNameSet.add(st.Sub_storage__c);
                            proCodeSet.add(st.Product__r.ProductCode);
                        }
                    } else {
                        List<Storage__c> replacedStorageList = replacedStorageMapX.get(st.Product__r.New_Product__c);
                        if(replacedStorageList != null) {
                            if(st.Product__r.IsActive = true  && st.Available_Inventory__c > productSetting.Inventory__c) {
                                storageMap.put(st.Id, st);
                                optionList.add(new SelectOption(st.Id, st.Product__r.Name + '(' +
                                                                st.Product__r.ProductCode + ',' + st.Product__r.Item_Number__c + ') ' +
                                                                st.Sub_storage__c));
                                storageProductCodeMap.put(st.Id, st.Product__r.ProductCode);
                                String key = String.valueOf(st.Product__r.ProductCode) + ' ' + st.Sub_storage__c;
                                if(!proOptionMap.containsKey(key)) {
                                    proOptionMap.put(key, new Set<String>());
                                }
                                proOptionMap.get(key).add(st.Id);
                                storageNameSet.add(st.Sub_storage__c);
                                proCodeSet.add(st.Product__r.ProductCode);
                            }
                            if(!isHaveProductMap.containsKey(st.Product__r.ProductCode)){
                                for(Storage__c replacedStorage : allReplacedStorageList) {
                                    String replacedProductCode = replacedStorage.Product__r.ProductCode;
                                    // if(!replacedProductCode.containsIgnoreCase('(C)') && replacedProductCode.containsIgnoreCase(replacedStorageList[0]?.Product__r?.ProductCode)) {
                                    // temporary change
                                    if(!upgradeIdSet.contains(replacedStorage.Product__c+replacedStorage.Sub_storage__c)){
                                        if(replacedProductCode.containsIgnoreCase(replacedStorageList[0]?.Product__r?.ProductCode)) {
                                            optionList.add(new SelectOption(replacedStorage.Id, replacedStorage.Product__r.Name + '(' +
                                                                        replacedStorage.Product__r.ProductCode + ',' + replacedStorage.Product__r.Item_Number__c + ') ' +
                                                                        replacedStorage.Sub_storage__c));
                                            storageProductCodeMap.put(replacedStorage.Id, replacedStorage.Product__r.ProductCode);
                                            String key = String.valueOf(replacedStorage.Product__r.ProductCode) + ' ' + replacedStorage.Sub_storage__c;
                                            if(!proOptionMap.containsKey(key)) {
                                                proOptionMap.put(key, new Set<String>());
                                            }
                                            proOptionMap.get(key).add(replacedStorage.Id);
                                            storageNameSet.add(replacedStorage.Sub_storage__c);
                                            proCodeSet.add(replacedStorage.Product__r.ProductCode);
                                            upgradeIdSet.add(replacedStorage.Product__c+replacedStorage.Sub_storage__c);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                storageSelectList = [SELECT Id, Product__r.EBS_Status__c, Product__r.New_Product__c
                                              , Product__r.New_Product__r.ProductCode, Product__r.IsActive, Available_Inventory__c
                                              , Product__c, Product__r.Name, Product__r.ProductCode, Product__r.Brand_Name__c
                                              , Product__r.Item_Number__c, Sub_storage__c
                                    FROM Storage__c
                                    WHERE
                                    (Product__r.New_Product__c != null OR (Product__r.IsActive = true
                                    AND Available_Inventory__c >= :productSetting.Inventory__c
                                    AND Product__r.EBS_Status__c NOT IN :ebsStatusToExclude))
                                    AND Product__c IN :set_proids
                                    AND Product__r.Source__c = 'EBS'
                                    AND Product__r.RecordType.DeveloperName = 'Product'
                                    AND (Product__r.Name LIKE :newFilterStr
                                                    OR Product__r.ProductCode LIKE :newFilterStr)
                                    AND Product__r.Unavailable_For_Select__c = false
                                    AND (NOT Product__r.Description  LIKE '%FD Only%')
                                    ORDER BY Product__r.Search_Sequence__c ];

                for(Storage__c st : storageSelectList){
                    if (st.Product__r.Brand_Name__c != brand) continue;
                        if(st.Product__r.New_Product__c != null) {
                            replacementProducts.add(st.Product__r.New_Product__c);
                            String tempProductCode = 'CS-'+st.Product__r.New_Product__r.ProductCode+'%';
                            replacementProductsX.add(tempProductCode);
                            // CS2- and CS3- products are not allowed for any profile
                            // tempProductCode = 'CS2-'+st.Product__r.New_Product__r.ProductCode+'%';
                            // replacementProductsX.add(tempProductCode);
                            tempProductCode = '%'+st.Product__r.New_Product__r.ProductCode+'-FC';
                            replacementProductsX.add(tempProductCode);
                            replacementProductsX.add(st.Product__r.New_Product__r.ProductCode);
                            replacementProductsX.add(tempProductCode);
                        }
                    storageList.add(st);
                }
                System.debug('replacementProductsX:' + replacementProductsX);
                System.debug('storagelist:' + storageList);
                for(Storage__c strg : [SELECT Id, Product__r.EBS_Status__c,Product__r.New_Product__c, Product__c, Product__r.Name, Product__r.ProductCode, Product__r.Brand_Name__c,
                                                                                    Product__r.Item_Number__c, Sub_storage__c, Product__r.IsActive, Available_Inventory__c
                                                                                    FROM Storage__c
                                                                                    WHERE Product__r.ProductCode LIKE :replacementProductsX
                                                                                    AND Product__r.IsActive = true
                                                                                    AND Available_Inventory__c >= :productSetting.Inventory__c
                                                                                    AND Product__r.Source__c = 'EBS'
                                                                                    AND Product__r.EBS_Status__c NOT IN :ebsStatusToExclude
                                                                                    AND Product__r.Country_of_Origin__c = 'United States'
                                                                                    AND Product__r.Unavailable_For_Select__c = false
                                                                                    AND (NOT Product__r.Description  LIKE '%FD Only%')
                                                                                    AND Product__r.Recordtype.Name = 'Product' ORDER BY Product__r.Search_Sequence__c]) {
                    replacedStorageMap.put(strg.Product__c, strg);
                    replacedStorageMap1.put(strg.Id, strg);
                    allReplacedStorageList.add(strg);
                    if(replacedStorageMapX.containsKey(strg.Product__c)) {
                        replacedStorageMapX.get(strg.Product__c).add(strg);
                    }
                    else {
                        replacedStorageMapX.put(strg.Product__c, new List<Storage__c>{strg});
                    }
                }
                System.debug('*** allReplacedStorageList:' + JSON.serialize(allReplacedStorageList));
                for(Storage__c st : storageList) {
                    if((productIdSet.contains(st.Product__c)|| st.Product__r.ProductCode.contains('CS-')|| st.Product__r.ProductCode.endsWith('T') || st.Product__r.ProductCode.endsWith('T(C)') || st.Product__r.ProductCode.endsWith('T-FC')) && st.Product__r.IsActive  && st.Available_Inventory__c > productSetting.Inventory__c && !ebsStatusToExclude.contains(st.Product__r.EBS_Status__c)){
                        if(isCNA){
                            if(st.Sub_storage__c.contains('CNA')){
                                isHaveProductMap.put(st.Product__r.ProductCode, true);
                                // isHaveProduct = true;
                            }
                        }else{
                            if(st.Sub_storage__c.contains('CA')){
                                isHaveProductMap.put(st.Product__r.ProductCode, true);
                                // isHaveProduct = true;
                            }
                        }

                    }
                }
                for(Storage__c st : storageList) {
                    if(st.Product__r.New_Product__c == null || replacedStorageMapX.containsKey(st.Product__r.New_Product__c) == false) {
                        if(st.Product__r.IsActive = true  &&
                            ( (st.Available_Inventory__c > productSetting.Inventory__c && !ebsStatusToExclude.contains(st.Product__r.EBS_Status__c) )
                                || (st.Available_Inventory__c >= productSetting.Inventory__c && st.Product__r.ProductCode == 'SR211601') )) {
                            storageMap.put(st.Id, st);
                            optionList.add(new SelectOption(st.Id, st.Product__r.Name + '(' +
                                                            st.Product__r.ProductCode + ',' + st.Product__r.Item_Number__c + ') ' +
                                                            st.Sub_storage__c));
                            storageProductCodeMap.put(st.Id, st.Product__r.ProductCode);
                            String key = String.valueOf(st.Product__r.ProductCode) + ' ' + st.Sub_storage__c;
                            if(!proOptionMap.containsKey(key)) {
                                proOptionMap.put(key, new Set<String>());
                            }
                            proOptionMap.get(key).add(st.Id);
                            storageNameSet.add(st.Sub_storage__c);
                            proCodeSet.add(st.Product__r.ProductCode);
                        }
                    } else {
                        List<Storage__c> replacedStorageList = replacedStorageMapX.get(st.Product__r.New_Product__c);
                        if(replacedStorageList != null) {
                            if(st.Product__r.IsActive = true  && st.Available_Inventory__c > productSetting.Inventory__c && !ebsStatusToExclude.contains(st.Product__r.EBS_Status__c)) {
                                storageMap.put(st.Id, st);
                                optionList.add(new SelectOption(st.Id, st.Product__r.Name + '(' +
                                                                st.Product__r.ProductCode + ',' + st.Product__r.Item_Number__c + ') ' +
                                                                st.Sub_storage__c));
                                storageProductCodeMap.put(st.Id, st.Product__r.ProductCode);
                                String key = String.valueOf(st.Product__r.ProductCode) + ' ' + st.Sub_storage__c;
                                if(!proOptionMap.containsKey(key)) {
                                    proOptionMap.put(key, new Set<String>());
                                }
                                proOptionMap.get(key).add(st.Id);
                                storageNameSet.add(st.Sub_storage__c);
                                proCodeSet.add(st.Product__r.ProductCode);
                            }
                            if(!isHaveProductMap.containsKey(st.Product__r.ProductCode)){
                                for(Storage__c replacedStorage : allReplacedStorageList) {
                                    String replacedProductCode = replacedStorage.Product__r.ProductCode;
                                    // if(!replacedProductCode.containsIgnoreCase('(C)') && replacedProductCode.containsIgnoreCase(replacedStorageList[0]?.Product__r?.ProductCode)) {
                                    // temporary change
                                    if(!upgradeIdSet.contains(replacedStorage.Product__c+replacedStorage.Sub_storage__c)){
                                        if(replacedProductCode.containsIgnoreCase(replacedStorageList[0]?.Product__r?.ProductCode)) {
                                            optionList.add(new SelectOption(replacedStorage.Id, replacedStorage.Product__r.Name + '(' +
                                                                        replacedStorage.Product__r.ProductCode + ',' + replacedStorage.Product__r.Item_Number__c + ') ' +
                                                                        replacedStorage.Sub_storage__c));
                                            storageProductCodeMap.put(replacedStorage.Id, replacedStorage.Product__r.ProductCode);
                                            String key = String.valueOf(replacedStorage.Product__r.ProductCode) + ' ' + replacedStorage.Sub_storage__c;
                                            if(!proOptionMap.containsKey(key)) {
                                                proOptionMap.put(key, new Set<String>());
                                            }
                                            proOptionMap.get(key).add(replacedStorage.Id);
                                            storageNameSet.add(replacedStorage.Sub_storage__c);
                                            proCodeSet.add(replacedStorage.Product__r.ProductCode);
                                            upgradeIdSet.add(replacedStorage.Product__c+replacedStorage.Sub_storage__c);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        System.debug('*** optionList.size:' + optionList.size());
        System.debug('*** optionList:' + optionList);
        List<ProductJson> resultList = new List<ProductJson>();
        if(String.isNotEmpty(filterStr)){
            for(SelectOption option : optionList){
                ProductJson pjson = new ProductJson(option.getValue(),option.getLabel());
                pjson.ProductCode = storageProductCodeMap.get(option.getValue());
                String lower = pjson.Name.toLowerCase();
                String filterStrTemp = filterStr.toLowerCase();
                // Filter out CS2- and CS3- products for all profiles
                if ((lower.contains(filterStrTemp) || (replacedStorageMap1 != null && replacedStorageMap1.containsKey(pjson.Id)))
                    && !pjson.ProductCode.startsWith('CS2-') && !pjson.ProductCode.startsWith('CS3-')) {
                    resultList.add(pjson);
                }
            }
        }
        else {
            for (SelectOption option : optionList) {
                ProductJson pjson = new ProductJson(option.getValue(),option.getLabel());
                pjson.ProductCode = storageProductCodeMap.get(option.getValue());
                // Filter out CS2- and CS3- products for all profiles
                if (!pjson.ProductCode.startsWith('CS2-') && !pjson.ProductCode.startsWith('CS3-')) {
                    resultList.add(pjson);
                }
            }
        }
        system.debug(resultList);
        if(resultList.size()>0 && idOrderRecordType != CCM_Constants.ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID){
            system.debug('storageNameSet' + storageNameSet);
            system.debug('proOptionMap' + proOptionMap);
            String caseType = NULL;
            List<Case> lstCase = [SELECT Case_Type__c FROM Case WHERE Id=:caseId LIMIT 1];
            if(lstCase.size()>0){
                caseType = lstCase[0].Case_Type__c;
            }
            User currentUser = [SELECT Id,Profile.Name FROM User WHERE Id=:UserInfo.getUserId() LIMIT 1];
            List<Contact> geoConList = [SELECT Id,Mailinglatitude,MailingCountry,Mailinglongitude FROM Contact WHERE LastName=:caseId AND FirstName='GeolocationContactOrder' AND Mailinglatitude!=NULL AND Mailinglongitude!=NULL LIMIT 1];
            if(geoConList.size()>0){
                Location currentLocation = Location.newInstance(geoConList[0].MailingLatitude,geoConList[0].MailingLongitude);
                if(geoConList[0].MailingCountry == 'US' || geoConList[0].MailingCountry == 'United States'){
                    storageNameSet.remove('CA002');
                } else if(caseType!='Warranty Order' && storageNameSet.contains('CA002') && (geoConList[0].MailingCountry == 'CA' || geoConList[0].MailingCountry == 'Canada')){
                    storageNameSet.clear();
                    storageNameSet.add('CA002');
                    // storageNameSet.add('CNA01');
                    storageNameSet.add('CNA08');
                    storageNameSet.add('CNA02');
                }
                storageNameSet.remove('CNA05');
                storageNameSet.remove('CNA10');
                List<Account> warehouseList = [SELECT Id,Name FROM Account WHERE RecordType.Name='Warehouse' AND ShippingLatitude!=NULL AND ShippingLongitude!=NULL AND Name IN: storageNameSet ORDER BY DISTANCE(ShippingAddress,:currentLocation, 'mi') ASC];
                String countryStr = String.isNotEmpty(geoConList[0].MailingCountry) ? (geoConList[0].MailingCountry).toLowerCase() : NULL;
                if(currentUser.Profile.Name.endsWith('Customer Service')){
                    for(String proCode: proCodeSet){

                        if(countryStr == 'ca' || countryStr == 'canada'){
                            if(proOptionMap.containsKey(proCode + ' ' + 'CA002')){
                                if(proOptionMap.containsKey(proCode + ' ' + 'CA002')){
                                     storageIdSet.addAll(proOptionMap.get(proCode + ' ' + 'CA002'));
                                }

                                // if(proOptionMap.containsKey(proCode + ' ' + 'CNA01')){
                                //     storageIdSet.addAll(proOptionMap.get(proCode + ' ' + 'CNA01'));
                                // }
                                if(proOptionMap.containsKey(proCode + ' ' + 'CNA08')){
                                    storageIdSet.addAll(proOptionMap.get(proCode + ' ' + 'CNA08'));
                                }
                                if(proOptionMap.containsKey(proCode + ' ' + 'CNA02')){
                                storageIdSet.addAll(proOptionMap.get(proCode + ' ' + 'CNA02'));
                                }

                            }else{
                                for(Account warehouse: warehouseList){
                                    String proStorage = proCode + ' ' + warehouse.Name;
                                    if(caseType == 'Warranty Order'){
                                        if(proCode.startsWith('CS-') || proCode.endsWith('-FC') || proCode.startsWith('CS1-') ||
                                            proCode.startsWith('CS2-') || proCode.endsWith('T-FC') || proCode.endsWith('T') || proCode.endsWith('-RT')){
                                            String proNetCode = proCode.removeStart('CS-').removeEnd('T-FC').removeStart('CS1-');
                                            proNetCode = proNetCode.removeStart('CS2-').removeEnd('-FC').removeEnd('-RT').removeEnd('T');

                                            if(proOptionMap.containsKey(proNetCode +' ' + 'CA002')){
                                                break;
                                            }
                                        }
                                        else{
                                            if(proOptionMap.containsKey(proStorage)){
                                                storageIdSet.addAll(proOptionMap.get(proStorage));
                                                break;
                                            }
                                        }
                                    }else{
                                        if(proOptionMap.containsKey(proStorage)){
                                            storageIdSet.addAll(proOptionMap.get(proStorage));
                                            break;
                                        }
                                    }
                                }
                            }
                        }else{
                            for(Account warehouse: warehouseList){
                                    String proStorage = proCode + ' ' + warehouse.Name;
                                if(proOptionMap.containsKey(proStorage)){
                                    storageIdSet.addAll(proOptionMap.get(proStorage));
                                    break;

                                }
                            }
                        }
                    }
                }else{
                    //logic for CSR leader: display product in suggested storage order
                    for(String proCode: proCodeSet){

                        if(countryStr == 'ca' || countryStr == 'canada'){
                            if(proOptionMap.containsKey(proCode + ' ' + 'CA002')){
                                System.debug('proCode: ' + proCode);
                                System.debug('proOptionMap: ' + proOptionMap);
                                if(proOptionMap.containsKey(proCode + ' ' + 'CA002')){
                                    storageIdSet.addAll(proOptionMap.get(proCode + ' ' + 'CA002'));
                                }

                                // if(proOptionMap.containsKey(proCode + ' ' + 'CNA01')){
                                //     storageIdSet.addAll(proOptionMap.get(proCode + ' ' + 'CNA01'));
                                // }
                                if(proOptionMap.containsKey(proCode + ' ' + 'CNA08')){
                                    storageIdSet.addAll(proOptionMap.get(proCode + ' ' + 'CNA08'));
                                }
                                if(proOptionMap.containsKey(proCode + ' ' + 'CNA02')){
                                    storageIdSet.addAll(proOptionMap.get(proCode + ' ' + 'CNA02'));
                                }


                            }else{
                                for(Account warehouse: warehouseList){
                                    String proStorage = proCode + ' ' + warehouse.Name;
                                    if(proOptionMap.containsKey(proStorage)){
                                        storageIdSet.addAll(proOptionMap.get(proStorage));
                                    }
                                }
                            }
                        }else{
                            for(Account warehouse: warehouseList){
                                String proStorage = proCode + ' ' + warehouse.Name;
                                if(proOptionMap.containsKey(proStorage)){
                                    storageIdSet.addAll(proOptionMap.get(proStorage));
                                }
                            }
                        }
                    }
                }
                List<ProductJson> suggestedProList = new List<ProductJson>();
                for(String storageId: storageIdSet){
                    for(ProductJson result: resultList){
                        if(storageId == result.Id){
                            suggestedProList.add(result);
                            break;
                        }
                    }
                }
                if(suggestedProList.size()>0){
                    resultList.clear();
                    resultList.addAll(suggestedProList);
                }
            }
        }

        System.debug(LoggingLevel.INFO, '*** filterStr: ' + filterStr);
        System.debug(LoggingLevel.INFO, '*** resultList.size(): ' + resultList.size());
        System.debug(LoggingLevel.INFO, '*** selectProduct.size(): ' + selectProduct.size());
        tempJsonStr = JSON.serialize(resultList);
        return tempJsonStr;
    }


    public class SelectOptions {
        public String Id;
        public String Name;
        public SelectOptions() {
        }
        public SelectOptions(String strId, String strName) {
            this.Id = strId;
            this.Name = strName;
        }
    }

    //跟关联客户的Brand设置PurchasePlace字段的值
    @AuraEnabled
    public static String changePurchasePlace(String accId, String filter) {
        List<String> list_select = new List<String>();
        List<SelectOptions> selectList = new List<SelectOptions>();

        Account acc = AccountService.getAccountByID(accId);
        Set<String> brandNames = new Set<String>();
        if (acc.Record_Type_Name__c == 'PersonAccount') {
            for (Schema.PicklistEntry pc : Account.Product_Type__c.getDescribe().getPicklistValues()) {
                if (pc.isActive()) {
                    brandNames.add(pc.getValue());
                }
            }
        }

        List<String> recordTypeNameList = new List<String>();
        recordTypeNameList.add('Channel');
        Map<String, SelectOptions> selectListMap = new Map<String, SelectOptions>();
        if(filter <> null && filter <> ''){
            filter = '%'+filter+'%';
            for (Account accTemp : [SELECT Id, Record_Type_Name__c, Name FROM Account WHERE Record_Type_Name__c IN :recordTypeNameList AND (NOT Distributor_or_Dealer__c LIKE '%2nd Tier%') AND Name LIKE: filter]) {
                String show = accTemp.Name + ' ' + accTemp.Record_Type_Name__c.replaceAll('_', ' ');
                selectListMap.put(accTemp.Id + ' ' + show, new SelectOptions(accTemp.Id, show));
            }
            for (String show : selectListMap.keySet()) {
                selectList.add(selectListMap.get(show));
            }
        }

        if (selectList.size() == 0) {
            return null;
        }

        return JSON.serialize(selectList);
    }

    //生成Case上WarrantyNumber字段的值，替换lookup filter
    @AuraEnabled
    public static String CaseWarrantyNumber(String contactId, String filterStr) {
        List<SelectOptions> selectList = new List<SelectOptions>();
        String filterStrTemp = '%' + filterStr + '%';
        if (contactId != null) {
            for (Warranty__c warranty : [
                SELECT Id, Name, Master_Product_Code__c, Master_Product__r.Name
                FROM Warranty__c
                WHERE AccountCustomer__r.PersonContactId = :contactId AND (Master_Product_Code__c LIKE :filterStrTemp OR Master_Product__r.Name LIKE :filterStrTemp)
            ]) {
                selectList.add(new SelectOptions(warranty.Id, warranty.Name + ' ' + warranty.Master_Product__r.Name));
            }
        }
        return JSON.serialize(selectList);
    }

    //生成Case上Warranty_Item字段的值，替换lookup filter
    @AuraEnabled
    public static String CaseWarrantyItem(String projectCustomerId, String filterStr) {
        List<SelectOptions> selectList = new List<SelectOptions>();
        String filterStrTemp = '%' + filterStr + '%';
        if (projectCustomerId != null) {
            for (Warranty_Item__c warrantyItem : [
                SELECT Id, Name, Product__r.Name, Product__r.ProductCode,(SELECT Id FROM WarrantyItems__r WHERE Recordtype.name = 'Recall')
                FROM Warranty_Item__c
                WHERE warranty__c = :projectCustomerId AND (Product__r.Name LIKE :filterStrTemp OR Product__r.ProductCode LIKE :filterStrTemp)
            ]) {
                Boolean hasRecall = false;
                for(Case ca : warrantyItem.WarrantyItems__r){
                    hasRecall = true;
                }
                if(!hasRecall){
                    selectList.add(new SelectOptions(warrantyItem.Id, warrantyItem.Product__r.ProductCode + ' ' + warrantyItem.Product__r.Name));
                }

            }
        }
        return JSON.serialize(selectList);
    }

    //生成Case上Customer Project字段的值，替换lookup filter
    @AuraEnabled
    public static String CaseProjectCustomer(String contactId, String filterStr) {
        List<SelectOptions> selectList = new List<SelectOptions>();
        String filterStrTemp = '%' + filterStr + '%';
        if (contactId != null) {
            for(Warranty_Item__c wi : [SELECT Id,Project_Customer__c,Project_Customer__r.Name,Project_Customer__r.Customer__r.Name,Project_Customer__r.Project__r.Name
                                        FROM Warranty_Item__c
                                        WHERE id =: contactId AND Project_Customer__r.Project__r.IsValid__c = TRUE  AND (Project_Customer__r.Customer__r.Name LIKE :filterStrTemp OR Project_Customer__r.Project__r.Name LIKE :filterStrTemp) ]){

                 selectList.add(new SelectOptions(wi.Project_Customer__r.Id, wi.Project_Customer__r.Project__r.Name));
            }
            // for (Project_Customer__c projectCustomer : [
            //     SELECT Id, Name, Customer__r.Name, Project__r.Name
            //     FROM Project_Customer__c
            //     WHERE Customer__r.PersonContactId = :contactId AND Project__r.IsValid__c = TRUE AND (Customer__r.Name LIKE :filterStrTemp OR Project__r.Name LIKE :filterStrTemp)
            // ]) {

            // }
        }
        return JSON.serialize(selectList);
    }

    //生成Warranty上Master Product字段的值，替换lookup filter
    @AuraEnabled
    public static String WarrantyMasterProduct(String brandName, String filterStr) {
        Integer intQueryLimit = Integer.valueOf(Label.CCM_Warranty_Master_Product_List_Limit);
        List<SelectOptions> selectList = new List<SelectOptions>();
        String filterStrTemp = '%' + filterStr + '%';
        RecordType wrt = [SELECT Id FROM RecordType WHERE SobjectType = 'Product2' AND DeveloperName = 'Kit'];
        User u = [SELECT Id, Country_of_Origin__c FROM User WHERE Id = :UserInfo.getUserId()];
        if (brandName != null && u.Country_of_Origin__c != null) {
            if (u.Country_of_Origin__c == 'Global') {
                for (Product2 product : [
                    SELECT Id, Name, ProductCode
                    FROM Product2
                    WHERE
                        RecordTypeId = :wrt.Id
                        AND IsActive = TRUE
                        AND Brand_Name__c = :brandName
                        AND (Name LIKE :filterStrTemp
                        OR ProductCode LIKE :filterStrTemp)
                        AND Source__c = 'EBS'
                        AND Is_History_Product__c = :CCM_Constants.blHistoryProduct
                    LIMIT :intQueryLimit
                ]) {
                    // prettier-ignore
                    if (product.ProductCode?.startsWith('FOR_') == true) continue;
                    if (product.ProductCode?.startsWith('CS-') || product.ProductCode?.startsWith('CS2-') || product.ProductCode?.startsWith('CS3-')) continue;
                    selectList.add(new SelectOptions(product.Id, product.ProductCode + ' ' + product.Name));
                }
            } else {
                for (Product2 product : [
                    SELECT Id, Name, ProductCode
                    FROM Product2
                    WHERE
                        RecordTypeId = :wrt.Id
                        AND IsActive = TRUE
                        AND Brand_Name__c = :brandName
                        AND Country_of_Origin__c = :u.Country_of_Origin__c
                        AND (Name LIKE :filterStrTemp
                        OR ProductCode LIKE :filterStrTemp)
                        AND Source__c = 'EBS'
                        AND Is_History_Product__c = :CCM_Constants.blHistoryProduct
                    LIMIT :intQueryLimit
                ]) {
                    // prettier-ignore
                    if (product.ProductCode?.startsWith('FOR_') == true) continue;
                    if (product.ProductCode?.startsWith('CS-') || product.ProductCode?.startsWith('CS2-') || product.ProductCode?.startsWith('CS3-')) continue;
                    selectList.add(new SelectOptions(product.Id, product.ProductCode + ' ' + product.Name));
                }
            }
        }
        return JSON.serialize(selectList);
    }

    //CCM_NewOrder
    @AuraEnabled
    public static String getParts(String caseId, String warId, String brand, String type, String productId, String filterStr, Id orderRecordTypeId) {
        return CCM_NewOrderController.getParts(caseId, warId, brand, type, productId, filterStr, orderRecordTypeId);
    }
    public static void fortestcodecoverage() {
        for(Integer i = 0; i<10000; i++){
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
             i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
        }
    }
}