/**
 * Created by gluo006 on 9/15/2019.
 */
({
    getBrandList: function(component){
        var email = component.get('v.emailAddress');
        var action = component.get('c.GenerateBrandList');
        action.setParams({
          'email': email,
        });
        action.setCallback(this, function (response) {
          var state = response.getState();
          if (state === 'SUCCESS' ) {
              var result = JSON.parse(response.getReturnValue());
              if(result.Brand){
                  var brandList = result.Brand.split(';');
                  component.set('v.brandList', brandList);
              }
          }else{
              $A.get("e.force:showToast").setParams({
                  "title": $A.get("$Label.c.CCM_Portal_Error"),
                  "message": response.getError()[0].message,
                  "type": "Error"
              }).fire();
          }
        });
        $A.enqueueAction(action);
    },
    searchWarranty: function(component){
        console.log('searchWarranty_proid',component.get('v.productId'));
        var email = component.get('v.emailAddress');
        var action = component.get('c.SearchWarranty');
        action.setParams({
            'email': email,
            'brand': component.get('v.brand'),
            'serialNumber': component.get('v.serialNum'),
            'product': component.get('v.productId')
            //'product': component.get('v.productObj').Id ? component.get('v.productObj').Id : ''
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === 'SUCCESS' ) {
                var result = JSON.parse(response.getReturnValue());
                result.value.forEach(function(re){
                  if(re.Replacements__r){
                    re.ActualIndicator__c = 'Out of Warranty';
                  }
                  if(re.Warranty__r && re.Warranty__r.Purchase_Date__c){
                    let dateParts = re.Warranty__r.Purchase_Date__c.split('-');
                    let formattedDate = `${dateParts[1]}-${dateParts[2]}-${dateParts[0]}`;
                    re.Warranty__r.Purchase_Date__c = formattedDate;
                  }
                  if(re.Expiration_Date_New__c){
                    let dateParts = re.Expiration_Date_New__c.split('-');
                    let formattedDate = `${dateParts[1]}-${dateParts[2]}-${dateParts[0]}`;
                    re.Expiration_Date_New__c = formattedDate;
                  }
                })
                component.set('v.data', result.value);
                component.set('v.totalRecords', result.value.length);
            }else{
                $A.get("e.force:showToast").setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                    "message": response.getError()[0].message,
                    "type": "Error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    getElementRequiredError: function (component, ele, fieldType) {
        var element = component.find(ele);
        var requiredText = component.find(ele + '-error-required');
        var val = '';
        if(ele.indexOf('masterProduct') > -1){
            val = component.get('v.masterProductObj').Id;
        }else if(ele.indexOf('purchasePlace') > -1){
            val = component.get('v.purchasePlaceObj').Id;
        }else{
            val = element.get('v.value');
        }
        if(val !== undefined && val !== null){
            val = val.trim();
        }
        var valid;
        //validate the email format
//        if(ele.toUpperCase().indexOf('EMAIL') > -1){
//           valid = (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(val));
//        }else{
           if(val == '--None--'){
               valid = false;
           }else{
              valid = !!val;
           }
//        }
        if (valid) {
            $A.util.removeClass(element, 'field-error');
            if (requiredText) {
                $A.util.addClass(requiredText, 'slds-hide');
            }
        } else {
            $A.util.addClass(element, 'field-error');
            if (requiredText) {
                $A.util.removeClass(requiredText, 'slds-hide');
            }
        }
        return valid;
    },
})