<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>AccountHierarchy</excludeButtons>
    <excludeButtons>ChangeOwnerOne</excludeButtons>
    <excludeButtons>ChangeRecordType</excludeButtons>
    <excludeButtons>CreateCallList</excludeButtons>
    <excludeButtons>CreateSurveyInvitation</excludeButtons>
    <excludeButtons>DataDotComClean</excludeButtons>
    <excludeButtons>IncludeOffline</excludeButtons>
    <excludeButtons>PartnerScorecard</excludeButtons>
    <excludeButtons>PrintableView</excludeButtons>
    <excludeButtons>SendEmail</excludeButtons>
    <excludeButtons>XClean</excludeButtons>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Basic Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Customer_Class__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Phone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Phone_Ext__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Fax</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Website</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Status__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Customer_Owner__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Customer_Creation_Month__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Enable_Dropship_Order__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Distributor_or_Dealer__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Intended_Brand__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Online_Selling__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Buying_Group__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Record_Type_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ParentId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Surcharge__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Invoice Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>CurrencyIsoCode</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TaxID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Credit_Limit__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Invoicing_Method__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PaymentMethod__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Sales Organization</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Customer_Cluster__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Sales_Channel__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ORG_Code__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Customer_Sub_Cluster__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Sales_Group__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Approval_Status__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>AccountNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Director_Approver__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Customer_Oracle_ID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>LogACall</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewTask</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewEvent</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>EnableAsPartner</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Account.Sync_Customer</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <quickActionList>
        <quickActionListItems>
            <quickActionName>FeedItem.MobileSmartActions</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.ContentNote</quickActionName>
        </quickActionListItems>
    </quickActionList>
    <relatedLists>
        <relatedList>RelatedContentNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>AddAccountDefaultTeam</excludeButtons>
        <excludeButtons>DisplayAccountTeamEffectiveAccess</excludeButtons>
        <fields>MEMBER_NAME</fields>
        <fields>TEAM_MEMBER_ROLE</fields>
        <relatedList>RelatedAccountSalesTeam</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>RECORDTYPE</fields>
        <fields>Brands__c</fields>
        <fields>Approval_Status__c</fields>
        <fields>Payment_Term__c</fields>
        <fields>Freight_Term__c</fields>
        <fields>Price_Book__c</fields>
        <fields>Standard_Price_List__c</fields>
        <fields>Price_List__c</fields>
        <fields>Contract_Price_List_Name__c</fields>
        <relatedList>Sales_Program__c.Customer__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>ORDERS.ORDER_NUMBER</fields>
        <fields>Order_Source__c</fields>
        <fields>ORDER.RECORDTYPE</fields>
        <fields>Order_Type__c</fields>
        <fields>Order_Number__c</fields>
        <fields>Brand__c</fields>
        <fields>Total_Amount__c</fields>
        <fields>Sales_Rep__c</fields>
        <fields>ERP_Last_Update_Time__c</fields>
        <fields>Order_Date__c</fields>
        <relatedList>Order.Dropship_Customer__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Customer_PO_Num__c</fields>
        <fields>Order_Type__c</fields>
        <fields>Total_Quantity__c</fields>
        <fields>Product_Price__c</fields>
        <fields>Total_Amount__c</fields>
        <fields>Status__c</fields>
        <fields>Submit_Date__c</fields>
        <fields>Is_DropShip__c</fields>
        <relatedList>Purchase_Order__c.Dropship_Customer__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>X1st_tier_dealer__c</fields>
        <relatedList>Sales_Hierarchy__c.X2st_tier_dealer__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>X2nd_Tier_Dealer_Name__c</fields>
        <relatedList>Sales_Hierarchy__c.X1st_tier_dealer__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>UPDATEDBY_USER</fields>
        <fields>RECORDTYPE</fields>
        <fields>Address1__c</fields>
        <fields>Postal_Code__c</fields>
        <fields>City__c</fields>
        <fields>Country__c</fields>
        <fields>Account_Number__c</fields>
        <relatedList>Account_Address__c.x2nd_Tier_Dealer_for_Sales__c</relatedList>
    </relatedLists>
    <relatedLists>
        <customButtons>New_Customer_Of_Dealer</customButtons>
        <excludeButtons>New</excludeButtons>
        <fields>ACCOUNT.NAME</fields>
        <fields>Selling_Categories__c</fields>
        <fields>Selling_Brands__c</fields>
        <fields>Selling_Rider__c</fields>
        <fields>Service_Categories__c</fields>
        <relatedList>Account.Related_Entity__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>RECORDTYPE</fields>
        <fields>Address1__c</fields>
        <fields>City__c</fields>
        <fields>State__c</fields>
        <fields>Active__c</fields>
        <fields>Approval_Status__c</fields>
        <relatedList>Account_Address__c.Customer__c</relatedList>
        <sortField>RECORDTYPE</sortField>
        <sortOrder>Asc</sortOrder>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>RECORDTYPE</fields>
        <fields>Address1__c</fields>
        <fields>Postal_Code__c</fields>
        <fields>City__c</fields>
        <fields>Country__c</fields>
        <fields>Account_Number__c</fields>
        <relatedList>Account_Address__c.X2nd_Tier_Dealer__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>FULL_NAME</fields>
        <fields>CONTACT.TITLE</fields>
        <fields>CONTACT.EMAIL</fields>
        <fields>CONTACT.PHONE1</fields>
        <relatedList>RelatedContactList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Attachment_Type__c</fields>
        <relatedList>Attachment_Management__c.Customer__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Summary_of_Potential_Actual_Sales__c</fields>
        <fields>TM_Segmentation__c</fields>
        <fields>Effective_Year__c</fields>
        <fields>TM_Segmentation_Comments__c</fields>
        <fields>Segmentation__c</fields>
        <fields>Actual_Inputs__c</fields>
        <fields>YTD__c</fields>
        <relatedList>Customer_Profile__c.Customer__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <excludeButtons>New</excludeButtons>
        <fields>NAME</fields>
        <fields>Customer_PO_Num__c</fields>
        <fields>Order_Type__c</fields>
        <fields>Total_Quantity__c</fields>
        <fields>Product_Price__c</fields>
        <fields>Discount_Amount__c</fields>
        <fields>Total_Amount__c</fields>
        <fields>Status__c</fields>
        <fields>Submit_Date__c</fields>
        <fields>Is_DropShip__c</fields>
        <relatedList>Purchase_Order__c.Customer__c</relatedList>
        <sortField>Submit_Date__c</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <excludeButtons>New</excludeButtons>
        <fields>ORDERS.ORDER_NUMBER</fields>
        <fields>Order_Source__c</fields>
        <fields>ORDER.RECORDTYPE</fields>
        <fields>Order_Type__c</fields>
        <fields>Order_Number__c</fields>
        <fields>Brand__c</fields>
        <fields>Total_Amount__c</fields>
        <fields>Sales_Rep__c</fields>
        <fields>ERP_Last_Update_Time__c</fields>
        <fields>Order_Date__c</fields>
        <relatedList>RelatedOrderList</relatedList>
        <sortField>ORDERS.ORDER_NUMBER</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <excludeButtons>New</excludeButtons>
        <fields>NAME</fields>
        <fields>Invoice_Source__c</fields>
        <fields>Invoice_Number__c</fields>
        <fields>Order__c</fields>
        <fields>Invoice_Date__c</fields>
        <fields>Invoice_Status__c</fields>
        <fields>Tracking_NO__c</fields>
        <fields>Total_Due__c</fields>
        <relatedList>Invoice__c.Customer__c</relatedList>
        <sortField>Invoice_Source__c</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Co_Op_Program__c</fields>
        <relatedList>Co_Op_Target_Customer__c.Customer__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <excludeButtons>New</excludeButtons>
        <fields>NAME</fields>
        <fields>Brand__c</fields>
        <fields>Year__c</fields>
        <fields>Month__c</fields>
        <fields>Volume__c</fields>
        <relatedList>Customer_Sales_Volume_History__c.Customer__c</relatedList>
        <sortField>Year__c</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <excludeButtons>New</excludeButtons>
        <fields>NAME</fields>
        <fields>Year__c</fields>
        <fields>Sales_Volume__c</fields>
        <fields>Level__c</fields>
        <fields>Rebate_Amount__c</fields>
        <fields>LAST_UPDATE</fields>
        <relatedList>Customer_Rebate_History__c.Customer__c</relatedList>
        <sortField>Year__c</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <fields>CreatedDate</fields>
        <fields>StepStatus</fields>
        <fields>OriginalActor</fields>
        <fields>Actor</fields>
        <fields>Comments</fields>
        <relatedList>RelatedProcessHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>TASK.WHAT_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>TASK.LAST_UPDATE</fields>
        <relatedList>RelatedHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>FULL_NAME</fields>
        <fields>LEAD.COMPANY</fields>
        <fields>LEAD.PHONE</fields>
        <relatedList>Lead.Buying_Group__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>FULL_NAME</fields>
        <fields>LEAD.COMPANY</fields>
        <fields>LEAD.PHONE</fields>
        <relatedList>Lead.Related_Entity__c</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedEntityHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>CAMPAIGN.NAME</fields>
        <fields>Campaign_Type__c</fields>
        <fields>Sales_Manager__c</fields>
        <fields>Brands__c</fields>
        <fields>CAMPAIGN.START_DATE</fields>
        <fields>CAMPAIGN.END_DATE</fields>
        <fields>CAMPAIGN.STATUS</fields>
        <fields>CAMPAIGN.LAST_UPDATE</fields>
        <relatedList>RelatedAccountCampaignList</relatedList>
    </relatedLists>
    <relatedObjects>ParentId</relatedObjects>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h0h00000bWKee</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
