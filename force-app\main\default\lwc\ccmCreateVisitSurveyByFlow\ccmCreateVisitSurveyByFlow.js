import { LightningElement, track } from 'lwc';

export default class CcmCreateVisitSurveyByFlow extends LightningElement {
    @track recordId;
    renderedCallback() {
        //get recordId
		this.recordId = window.location.toString().slice(-18);
        const flow = this.template.querySelector('lightning-flow');
        const flowInputVariables = [{ name: 'recordId', type: 'String', value: this.recordId }];
        flow.startFlow('ACE_Store_Survey', flowInputVariables);
    }
}