<aura:component controller="CCM_Community_FleetEditCtl">

    <aura:attribute name="fleetClaim" type="Object" description="fleetClaim information"/>
    <aura:attribute name="readonly" type="Boolean" default="true"/>

    <lightning:spinner aura:id="spinner" class="slds-hide slds-is-fixed" alternativeText="Loading" size="medium" variant="brand"/>

    <article class="slds-card">
        <div class="slds-grid">
            <header class="slds-media slds-media_center slds-has-flexi-truncate">
                <div class="slds-media__body">
                    <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                        <span class="section-header-title slds-p-horizontal--small slds-truncate"
                              title="{!$Label.c.CCM_Portal_OwnerInformation}">
                            <span><strong>{!$Label.c.CCM_Portal_OwnerInformation}</strong></span>
                         </span>
                    </h2>
                </div>
            </header>
        </div>
        <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
            <lightning:layout multipleRows="true" horizontalAlign="space">
                <aura:renderIf isTrue="{!v.readonly}">
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                        <label class="slds-form-element__label">{!$Label.c.CCM_Portal_EmailAddress}:</label>
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        {!v.fleetClaim.endUserCustomer.emailAddress}
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                        <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Orgnizationname}:</label>
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        {!v.fleetClaim.endUserCustomer.orgName}
                    </lightning:layoutItem>

                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                        <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Firstname}:</label>
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        {!v.fleetClaim.endUserCustomer.firstName}
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                        <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Phone}:</label>
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        {!v.fleetClaim.endUserCustomer.phone}
                    </lightning:layoutItem>

                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                        <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Lastname}:</label>
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        {!v.fleetClaim.endUserCustomer.lastName}
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                        <label class="slds-form-element__label">{!$Label.c.CCM_Portal_City}:</label>
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        {!v.fleetClaim.endUserCustomer.city}
                    </lightning:layoutItem>

                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                        <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Country}:</label>
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        {!v.fleetClaim.endUserCustomer.country}
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                        <label class="slds-form-element__label">{!$Label.c.CCM_Portal_StateProvince}:</label>
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        {!v.fleetClaim.endUserCustomer.state}
                    </lightning:layoutItem>

                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                        <label class="slds-form-element__label">{!$Label.c.CCM_Portal_ZipPostalcode}:</label>
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        {!v.fleetClaim.endUserCustomer.zipPostalCode}
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                        <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Address}:</label>
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        {!v.fleetClaim.endUserCustomer.addressLine}
                    </lightning:layoutItem>
                    <aura:set attribute="else">
                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                            <label class="slds-form-element__label field-required">{!$Label.c.CCM_Portal_EmailAddress}:</label>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                            <div class="input-width">
                                <lightning:input aura:id="emailAddress"
                                                 value="{!v.fleetClaim.endUserCustomer.emailAddress}"
                                                 variant="label-hidden"
                                                 onblur="{!c.getCustomerInfoByEmail}"
                                                 messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan80bytes}"
                                                 maxlength="80" />
                            </div>
                            <div aura:id="emailAddress-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                            <label class="slds-form-element__label field-required">{!$Label.c.CCM_Portal_Orgnizationname}:</label>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                            <div class="input-width">
                                <lightning:input aura:id="orgName"
                                                 value="{!v.fleetClaim.endUserCustomer.orgName}"
                                                 variant="label-hidden"
                                                 messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}"
                                                 maxlength="255" />
                            </div>
                            <div aura:id="orgName-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </lightning:layoutItem>

                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                            <label class="slds-form-element__label field-required">{!$Label.c.CCM_Portal_Firstname}:</label>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                            <div class="input-width">
                                <lightning:input aura:id="firstName"
                                                 value="{!v.fleetClaim.endUserCustomer.firstName}"
                                                 variant="label-hidden"
                                                 messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan40bytes}"
                                                 maxlength="40" />
                            </div>
                            <div aura:id="firstName-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                            <label class="slds-form-element__label field-required">{!$Label.c.CCM_Portal_Phone}:</label>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                            <div class="input-width">
                                <lightning:input aura:id="phone"
                                                 value="{!v.fleetClaim.endUserCustomer.phone}"
                                                 variant="label-hidden"
                                                 messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan40bytes}"
                                                 maxlength="40"
                                                 pattern="[0-9]*"
                                                 messageWhenPatternMismatch="Enter numbers only, no symbols" />
                            </div>
                            <div aura:id="phone-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </lightning:layoutItem>

                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                            <label class="slds-form-element__label field-required">{!$Label.c.CCM_Portal_Lastname}:</label>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                            <div class="input-width">
                                <lightning:input aura:id="lastName"
                                                 value="{!v.fleetClaim.endUserCustomer.lastName}"
                                                 variant="label-hidden"
                                                 messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan80bytes}"
                                                 maxlength="80" />
                            </div>
                            <div aura:id="lastName-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                            <label class="slds-form-element__label field-required">{!$Label.c.CCM_Portal_City}:</label>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                            <div class="input-width">
                                <lightning:input aura:id="city"
                                                 value="{!v.fleetClaim.endUserCustomer.city}"
                                                 variant="label-hidden"
                                                 messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan40bytes}"
                                                 maxlength="40" />
                            </div>
                            <div aura:id="city-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </lightning:layoutItem>

                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                            <label class="slds-form-element__label field-required">{!$Label.c.CCM_Portal_Country}:</label>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                            <div class="input-width">
                                <lightning:input aura:id="country"
                                                 value="{!v.fleetClaim.endUserCustomer.country}"
                                                 variant="label-hidden"
                                                 onblur="{!c.getAddressInfoByZipCodeOrCountry}"
                                                 messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan80bytes}"
                                                 maxlength="80" />
                            </div>
                            <div aura:id="country-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                            <label class="slds-form-element__label field-required">{!$Label.c.CCM_Portal_StateProvince}:</label>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                            <div class="input-width">
                                <lightning:input aura:id="state"
                                                 value="{!v.fleetClaim.endUserCustomer.state}"
                                                 variant="label-hidden"
                                                 messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan80bytes}"
                                                 maxlength="80" />
                            </div>
                            <div aura:id="state-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </lightning:layoutItem>

                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                            <label class="slds-form-element__label field-required">{!$Label.c.CCM_Portal_ZipPostalcode}:</label>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                            <div class="input-width">
                                <lightning:input aura:id="zipPostalCode"
                                                 value="{!v.fleetClaim.endUserCustomer.zipPostalCode}"
                                                 variant="label-hidden"
                                                 onblur="{!c.getAddressInfoByZipCodeOrCountry}"
                                                 messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan20bytes}"
                                                 maxlength="20" />
                            </div>
                            <div aura:id="zipPostalCode-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                            <label class="slds-form-element__label field-required">{!$Label.c.CCM_Portal_Address}:</label>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                            <div class="input-width">
                                <lightning:input aura:id="addressLine"
                                                 value="{!v.fleetClaim.endUserCustomer.addressLine}"
                                                 variant="label-hidden"
                                                 messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}"
                                                 maxlength="255" />
                            </div>
                            <div aura:id="addressLine-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </lightning:layoutItem>
                    </aura:set>
                </aura:renderIf>
            </lightning:layout>
        </div>
    </article>
</aura:component>