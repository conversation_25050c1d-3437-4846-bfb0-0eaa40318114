({
    doInit : function(component, event, helper) {
        var stepNameList = [];
        stepNameList.push($A.get("$Label.c.CCM_Portal_SelectBrandProducts"));
        stepNameList.push($A.get("$Label.c.CCM_Portal_FillinPurchaseOrderInformation"));
        stepNameList.push($A.get("$Label.c.CCM_Portal_PreviewConfirmation"));

        component.set("v.stepNameList", stepNameList);

        var recordId = helper.getUrlParameter('recordId');

        var action = component.get("c.getData");

        if(recordId){
            action.setParam('recordId', recordId);
            component.set('v.isShowTerm', true);
        }

        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log('状态---------', state);
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if(results){
                    component.set('v.po', results.po);
                    component.set('v.showUpload', results.showUpload);
                    component.set('v.recordId', results.po.Id);
                    //YANKO FOR CCA Case Qty
                    results.poItems.forEach(function(e){
                        if(results.customerOrgCode != 'CCA'){
                            e.CS_Exchange_Rate__c = e.CS_Exchange_Rate__c ? e.CS_Exchange_Rate__c : (e.Product__r.CS_Exchange_Rate__c ? e.Product__r.CS_Exchange_Rate__c : 1);
                        }else{
                            e.CS_Exchange_Rate__c = 1;
                        }
                    });
                    component.set('v.orderItemList', results.poItems);
                    console.log("yanko orderitemlist = ",results.poItems);
                    component.set('v.customerId', results.customerId);

                    /*if(results.currentStep){
                        component.set("v.currentStep", results.currentStep);
                    }*/
                    // if (results.customerOrgCode != 'CCA') {
                    //     component.set('v.minSelectDate', results.minSelectDate);
                    // } else {
                    if (results.customerOrgCode == 'CCA') {
                        const today = new Date();
                        const dayOfWeek = today.getDay();
                        if (dayOfWeek == 5) {
                            today.setDate(today.getDate() + 3);
                        } else if (dayOfWeek == 6) {
                            today.setDate(today.getDate() + 2);
                        } else {
                            today.setDate(today.getDate() + 1);
                        }
                        const minDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
                        component.set('v.minSelectDate', minDate);
                        component.set('v.shipDateScope', results.po.Expected_Delivery_Date__c);
                    } else {
                        // let currentDate = new Date();
                        // let currentYear = currentDate.getFullYear();
                        // let currentMonth = String(currentDate.getMonth() + 1).padStart(2, '0');
                        // let currentday = String(currentDate.getDate() + 8).padStart(2, '0');
                        // var cnaDate = String(currentYear) + '-' + currentMonth + '-' + currentday;
                        component.set('v.minSelectDate', results.minSelectDate);
                        component.set('v.shipDateScope', results.minSelectDate);
                    }
                    // component.set('v.shipDateScope', results.po.Expected_Delivery_Date__c);
                    // if (!results.po.Expected_Delivery_Date__c && results.customerOrgCode != 'CCA') {
                    //     component.set('v.shipDateScope', results.minSelectDate);
                    // }
                    component.set('v.brandScopeOpt', results.brandScopeList);
                    component.set('v.paymentTermAllOpts', results.paymentTermOptions);
                    component.set('v.paymentTermValue', results.po.Payment_Term__c);
                    console.log('payment term value 1--->'+component.get('v.paymentTermValue'));
                    component.set('v.customerType', results.customerType);
                    component.set('v.customerCluster', results.customerCluster);
                    component.set('v.customerOrgCode', results.customerOrgCode);
                    component.set('v.surcharge', results.surcharge);
                    if (recordId){
                        component.set('v.brandScope', results.po.Brand_Scope__c);
                        var e = $A.get("e.c:CCM_SelectProductListEvt");
                        e.fire();
                    }else {
                        if (results.brandScopeList.length == 1){
                            component.set('v.brandScope', results.brandScopeList[0].value);
                        }
                    }
                }
            } else {
                var errors = response.getError();
            }
        });
        $A.enqueueAction(action);
    },
    nextStep: function(component, event, helper){
        var currentStep = component.get("v.currentStep");
        component.set("v.currentStep", currentStep + 1);
    },
    cancel: function(component){
        window.location.href = window.location.origin + '/s/orderinformation';
    },

    brandChange: function(component, event, helper) {
        console.log('测试', event.getParam('value'))
        var oldValue = event.getParam('oldValue');
        component.set('v.oldBrandName', oldValue);
        var val = event.getParam('value');
        var purchaseList = component.get("v.orderItemList");
        var purchaseOrder = component.get("v.po");
        var vaildOrder = false;
        component.set('v.isShowTerm', true);
        if (oldValue != val){
            helper.getPaymentFreightRule(component,event,helper);
        }
        purchaseList.forEach(function(ele){
            if (ele.Product__c != '' && ele.Product__c != undefined && ele.Quantity__c != '' && ele.Quantity__c != undefined) {
                vaildOrder = true;
            }
        });
        if (oldValue != null && oldValue != '' && val != oldValue && vaildOrder) {
            component.set("v.showEditModal", true);
            var content = $A.get("$Label.c.CCM_Portal_EditModalTips1") + ' ' + oldValue + ' ' +
                          $A.get("$Label.c.CCM_Portal_EditModalTips2") + ' '+ val + ' ' +
                          $A.get("$Label.c.CCM_Portal_EditModalTips3");
            component.set("v.modalContent",content);
        }

    },

    closeModal: function(component, event, helper) {
        component.set('v.brandScope', component.get('v.oldBrandName'));
        component.set("v.showEditModal", false);
    },

    confirmChange: function(component, event, helper) {
        var action = component.get("c.deleteChangedBrandPO");
        action.setParams({
            "poInfo": JSON.stringify(component.get('v.po'))
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                results = JSON.parse(results);
                if (!results.isSuccess) {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": $A.get("$Label.c.CCM_Portal_Error"),
                        "message": results.errorMsg,
                        "type": "Error"
                    });
                    toastEvent.fire();
                } else {
                    component.set("v.showEditModal", false);
                    component.set("v.orderItemList", []);
                    component.set("v.po", results.result);
                    component.set("v.isShowFreightFeeMsg", false);

                    helper.clearWholeOrderPromotion(component);
                    helper.getPaymentFreightRule(component,null,null);
                }
            } else {
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                    "message": response.getError(),
                    "type": "Error"
                });
                toastEvent.fire();
            }
        });
        $A.enqueueAction(action);
    },
    validatePaymentTerm: function(component) {
        let paymentTerm = component.find("required-Field");
        if (paymentTerm) {
            paymentTerm[0].reportValidity();
        }
    },
    setShipDate: function(component) {
        var expectedShipDate = component.find("expectedShipDate");
        expectedShipDate.focus();
    }
})