({
    doInit:function(component, event, helper){
        var defaultCondition = "";
        var isHistoryProduct = $A.get("$Label.c.Is_History_Product");

        // false 意味着 item switch to model
        if (isHistoryProduct == 'false') {
            defaultCondition = '[{"Value":"false","FieldName":"Is_History_Product__c","Condtion":"="},{"Value":"true","FieldName":"IsActive","Condtion":"="},{"Value":"PIM","FieldName":"Source__c","Condtion":"="},{"Value":"Parts","FieldName":"RecordType.Name","Condtion":"="},{"Value":"true","FieldName":"Sellable__c","Condtion":"="}]';
        } else {
            // true 意味着 item switch to model 回滚
            defaultCondition = '[{"Value":"true","FieldName":"IsActive","Condtion":"="},{"Value":"PIM","FieldName":"Source__c","Condtion":"="},{"Value":"Parts","FieldName":"RecordType.Name","Condtion":"="},{"Value":"true","FieldName":"Sellable__c","Condtion":"="}]';
        }
        component.set('v.prodCondition', defaultCondition);

        var host = window.location.origin;
        component.set('v.vfHost', host);
        component.set('v.orderTypeVal', component.get('v.orderTypeVal'));

        // add parts order selected,payment term & freight term value
        component.set('v.paymentTerm',component.get('v.paymentTermLabel'));
        component.set('v.freightTerm',component.get('v.freightTermLabel'));
        component.set('v.currencySymbol',component.get('v.currencySymbol'));
        // add end

        if (!component.get('v.orderItemList')){
            component.set('v.quotation.Total_Quantity__c', 0);
            component.set('v.quotation.Product_Price__c', 0.00);
            component.set('v.showFreeShippingMsg', false);
            component.set('v.isWaivedFreight', false);
        }else{
            var waivedFee = component.get('v.quotation.Freight_Fee_To_Be_Waived__c');
            console.log('waivedFee--->'+waivedFee);
            waivedFee = isNaN(waivedFee) ? 0 : waivedFee; 
            var freightTargetFee = component.get('v.quotation.Freight_Target_Fee__c');
            freightTargetFee = isNaN(freightTargetFee) ? 0 : freightTargetFee; 
            console.log('freightTargetFee--->'+freightTargetFee);
            if (freightTargetFee == 0){
                component.set('v.showFreeShippingMsg', false);
            }else{
                if (waivedFee > 0){
                    component.set('v.showFreeShippingMsg', true);
                    component.set('v.isWaivedFreight', false);
                }else{
                    component.set('v.showFreeShippingMsg', true);
                    component.set('v.isWaivedFreight', true);
                }
            }
        }

        helper.isInKobaltScope(component);

        if (component.get('v.brandScope')){
            component.set('v.isShowTerm', true);
            component.set('v.disableBtn', false);
        }

        //helper.generateCondtion(component, component.get('v.brandScope'));
    },
    highLightSequenceNo: function(component){
        var sequenceNo = component.find('sequenceNo').get('v.value');
        var explosiveDataList = component.get('v.explosiveDataList');
        var partsIndex, selectedSequenceNo;
        for(var i=0; i<explosiveDataList.length; i++){
            explosiveDataList[i].highLight = false;
            if(explosiveDataList[i].ExplosionID__c.includes(sequenceNo)){
              explosiveDataList[i].highLight = true;
              partsIndex = i;
              if(i>3){
                  selectedSequenceNo = explosiveDataList[i-3].ExplosionID__c;
              }
            }
        }
        if(selectedSequenceNo){
            document.getElementById(selectedSequenceNo).scrollIntoView();
        }
        component.set('v.explosiveDataList', explosiveDataList)
    },
    backToTop: function(component){
        document.getElementById('top').scrollIntoView();
    },
    rowFocus:function(component, event, helper) {
        var indexRow = event.currentTarget.id;
        component.set('v.operationRow', indexRow);
    },
    addItem:function(component, event, helper){
        var orderItemList = component.get('v.orderItemList');
        var newOrderItem =
        {
            "Quantity__c":'1',
            "Unit_Price__c":'0.00',
            "Sub_Total__c":'0.00'
        };
        orderItemList.push(newOrderItem);
        helper.generateCondtion(component, component.get('v.brand'));

        component.set('v.orderItemList', orderItemList);
    },
    onSelectProd:function(component, event, helper){
        component.set('v.productPriceRefreshComplete', false);
        var productId = event.getSource().get('v.value');

        // fix napoleon, 23-1-10, `component.get('v.operationRow')` is the index of the new row which adding item instead of `event.getSource().get('v.indexnum')`
        var index = component.get('v.operationRow');
        // fix end

        if(!productId){
            component.set('v.productPriceRefreshComplete', true);
            return;
        }
        var action = component.get("c.getInternalPriceBook");
        action.setParams({
            "prodId": productId,
            'customerId': component.get('v.customerId')
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log('state--->'+ state);
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                if (results){
                    var data = JSON.parse(results);
                    console.log("in partsorder:internal pricebookentry:", data);
                    if (data){
                        // add show warnning message when selected product's priceBookEntry is invalid;
                        var orderItemList = component.get('v.orderItemList');
                        console.log("in partsorder:orderItemList:", JSON.stringify(orderItemList));
                        
                        if(!data.priceBookEntry.Id){
                            var noPriceBookEvent = $A.get("e.force:showToast");
                            noPriceBookEvent.setParams({
                                "title": "Error!",
                                "message": "No PriceBookEntry.",
                                "type": "Error"
                            });
                            noPriceBookEvent.fire();
                            let deleteIndex = component.get('v.operationRow');
                            if(deleteIndex !== undefined){
                                orderItemList.splice(deleteIndex, 1);
                                component.set('v.orderItemList', orderItemList);
                                helper.calculateTotal(component);
                            }
                            console.log("orderItemList,",component.get('v.orderItemList'));
                            return;
                        }
                        // add end

                        component.set('v.priceBookEntry', data.priceBookEntry);
                        component.set('v.product', data.product);

                        var repeatParts = false;
                        if (data.product){
                            var repeatParts = false;
                            var i = 0
                            orderItemList.forEach(function(item){
                                if(item.Item_Number__c == data.product.ProductCode && i != index ){
                                    repeatParts = true;
                                }
                                   i+=1;
                            });
                            if(repeatParts){
                                var toastEvent = $A.get("e.force:showToast");
                                toastEvent.setParams({
                                    "title": "Error!",
                                    "message": "This part has been added to the list already.",
                                    "type": "Error"
                                });
                                toastEvent.fire();
                                orderItemList.splice(index, 1);
                                component.set('v.orderItemList', orderItemList);
                                return;
                            }
                            orderItemList[index].Brand_Name__c = data.product.Brand_Name__c;
                            orderItemList[index].Item_Number__c = data.product.ProductCode;
                            orderItemList[index].ProductCode =  data.product.ProductCode; 
                            orderItemList[index].Name = data.product.Name;


                        } else {
                            orderItemList[index].Brand_Name__c = '';
                            orderItemList[index].Item_Number__c = '';
                            orderItemList[index].ProductCode = '';
                        }
                        if(data.priceBookEntry.UnitPrice){
                            orderItemList[index].Current_Price__c = data.priceBookEntry.UnitPrice.toFixed(2);
                        }
                        var unitPrice = data.priceBookEntry.UnitPrice;
                        if(unitPrice){
                            orderItemList[index].Current_Price__c = unitPrice.toFixed(2);
                            orderItemList[index].Unit_Price__c = Number(unitPrice).toFixed(2);
                            var quantity = orderItemList[index].Quantity__c;
                            var subtotal = unitPrice * quantity;
                            orderItemList[index].Sub_Total__c = subtotal.toFixed(2);
                        }else{
                            orderItemList[index].Current_Price__c = '0.00';
                            orderItemList[index].Unit_Price__c = '0.00';
                            orderItemList[index].Sub_Total__c = '0.00';
                        }
                        component.set('v.orderItemList', orderItemList);
                    }
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }

            helper.calculateTotal(component);
            component.set('v.productPriceRefreshComplete', true);
        });
        $A.enqueueAction(action);
    },
    doSave : function(component, event, helper){
        if(helper.productPriceUpdateNotComplete(component)) {
            return;
        }
        // update,napoleon,check product item line, if there is an empty line, alter error before next step,stop next action.
        if(helper.checkProductItemLine(component, event, helper)){
            helper.doSaveAction(component, event, true);
        }
    },
    nextStep: function(component, event, helper){
        if(helper.productPriceUpdateNotComplete(component)) {
            return;
        }
        // update,napoleon,check product item line, if there is an empty line, alter error before next step,stop next action.
        if(helper.checkProductItemLine(component, event, helper)){
            helper.doSaveAction(component, event, false);
        }
    },
    previousStep: function(component){
        var currentStep = component.get('v.currentStep');
        component.set('v.currentStep', (currentStep - 1));
    },
    calculateSubTotal: function(component, event, helper){
        var orderItemList = component.get('v.orderItemList');
        if(orderItemList && orderItemList.length > 0){
            // var index = component.get('v.operationRow');
            var index = event.getSource().get('v.name');
            console.log('index:',index);
            var unitPrice = Number(orderItemList[index].Unit_Price__c);
            var quantity = orderItemList[index].Quantity__c;
            var subtotal = unitPrice * quantity;
            orderItemList[index].Sub_Total__c = subtotal.toFixed(2);
            component.set('v.orderItemList', orderItemList);
        }

        helper.calculateTotal(component);
    },
    handleDelete: function(component, event, helper){
        var orderItemList = component.get('v.orderItemList');
        var deleteIndex = component.get('v.operationRow');/*Number(event.target.getAttribute('data-index'));*/
        console.log('deleteIndex--->'+deleteIndex);
        if(deleteIndex != undefined){
            orderItemList.splice(deleteIndex, 1);
            component.set('v.orderItemList', orderItemList);
        }
        helper.calculateTotal(component);
    },
    openModal: function(component, event, helper){
        component.set('v.activeIndex', '');
        $A.util.removeClass(
              component.find('boomModal'),
              "slds-hide"
        );
        var mySwiper = new Swiper ('.swiper-container', {
            // Optional parameters
            direction: 'horizontal',
            loop: false,
            observer: true,
            observeParents: true,
            queueEndCallbacks: true,
            // Navigation arrows
            navigation: {
              nextEl: '.swiper-button-next',
              prevEl: '.swiper-button-prev',
            }
        });
    },
    afterScriptsLoaded: function(component, event, helper){

    },
    addToCartSingle: function(component, event, helper){
        var index = event.target.getAttribute('data-index');
        var explosiveDataList = component.get('v.explosiveDataList');
        var orderItemList = component.get('v.orderItemList');
        explosiveDataList[index].Quantity__c = '1';
        if(explosiveDataList[index].Parts__r.Current_Price__c){
            explosiveDataList[index].Unit_Price__c = explosiveDataList[index].Parts__r.Current_Price__c.toFixed(2);
            explosiveDataList[index].Current_Price__c = explosiveDataList[index].Parts__r.Current_Price__c.toFixed(2);
            explosiveDataList[index].Sub_Total__c = explosiveDataList[index].Parts__r.Current_Price__c;
        }
        explosiveDataList[index].Brand_Name__c = explosiveDataList[index].Parts__r.Brand_Name__c;
        explosiveDataList[index].Name = explosiveDataList[index].Parts__r.Name;
        explosiveDataList[index].ProductCode = explosiveDataList[index].Product__r.ProductCode;
        explosiveDataList[index].Item_Number__c = explosiveDataList[index].Parts__r.ProductCode;
        orderItemList.push(explosiveDataList[index]);
        component.set('v.orderItemList', orderItemList);
        $A.util.addClass(
              component.find('boomModal'),
              "slds-hide"
        );
        helper.calculateTotal(component);
    },
    addSelectedToCart: function(component, event, helper){
        var explosiveDataList = component.get('v.explosiveDataList');
        var orderItemList = component.get('v.orderItemList');
        var selectedParts = [];
        var isOneChecked = false;
        let isValid = helper.validateSellable(explosiveDataList);
        if(!isValid) {
            return;
        }
        explosiveDataList.forEach(function(item){
            if(item.isSelect){
                isOneChecked = true;
                item.Quantity__c = item.Quantity__c ? item.Quantity__c : '1';
                item.ProductCode = item.Product__r.ProductCode;
                item.Item_Number__c = item.Parts__r.ProductCode;
                item.Brand_Name__c = item.Parts__r.Brand_Name__c;
                item.Name = item.Parts__r.Name;
                item.Current_Price__c   = isNaN(item.Parts__r.Current_Price__c) ? 0.00 : Number(item.Parts__r.Current_Price__c).toFixed(2);
                item.Unit_Price__c      = isNaN(item.Parts__r.Current_Price__c) ? 0.00 : Number(item.Parts__r.Current_Price__c).toFixed(2);
                item.Sub_Total__c       = isNaN(item.Parts__r.Current_Price__c) ? 0.00 : Number(item.Parts__r.Current_Price__c).toFixed(2);
                orderItemList.push(item);
            }
        })
        if(!isOneChecked){
            return;
        }
        component.set('v.orderItemList', orderItemList);
        $A.util.addClass(
              component.find('boomModal'),
              "slds-hide"
        );
        helper.calculateTotal(component);
        component.set('v.productPriceRefreshComplete', true);
    },

    cancel: function(component){
        window.location.href = window.location.origin + '/s/orderinformation';
    },
    openModal: function(component, event, helper){
        component.set('v.activeIndex', '');
        $A.util.removeClass(
              component.find('boomModal'),
              "slds-hide"
        );
        var mySwiper = new Swiper ('.swiper-container', {
            // Optional parameters
            direction: 'horizontal',
            loop: false,
            observer: true,
            observeParents: true,
            queueEndCallbacks: true,
            // Navigation arrows
            navigation: {
              nextEl: '.swiper-button-next',
              prevEl: '.swiper-button-prev',
            }
        });
    },
    afterScriptsLoaded: function(component, event, helper){

    },
    getVersion: function(component, event, helper){
        var productId = event.getSource().get('v.value');
        component.set('v.explosiveProductId', productId);
        helper.GenerateVersionList(component, productId);
    },
    //explosive search
    handleSearch: function(component, event, helper){
        helper.searchParts(component);
    },
    refreshFlag: function(component, event, helper){
        var searchExplosiveProductCondition = "";
        var isHistoryProduct = $A.get("$Label.c.Is_History_Product");

        // false 意味着 item switch to model
        if (isHistoryProduct == 'false') {
            searchExplosiveProductCondition = '[{"Value":"false","FieldName":"Is_History_Product__c","Condtion":"="},{"Value":"true","FieldName":"IsActive","Condtion":"="},{"Value":"(\'{1}\')","FieldName":"Brand_Name__c","Condtion":"IN"},{"Value":"0120h000000Un4LAAS","FieldName":"RecordTypeId","Condtion":"="},{"Value":"PIM","FieldName":"Source__c","Condtion":"="},{"Value":"true","FieldName":"Sellable__c","Condtion":"="}]';
        } else {
        // true 意味着 item switch to model 回滚
            searchExplosiveProductCondition = '[{"Value":"true","FieldName":"IsActive","Condtion":"="},{"Value":"(\'{1}\')","FieldName":"Brand_Name__c","Condtion":"IN"},{"Value":"0120h000000Un4LAAS","FieldName":"RecordTypeId","Condtion":"="},{"Value":"PIM","FieldName":"Source__c","Condtion":"="},{"Value":"true","FieldName":"Sellable__c","Condtion":"="}]';
        }
        
        searchExplosiveProductCondition = searchExplosiveProductCondition.replace('{1}', component.get('v.explosiveBrand'));
        component.set('v.searchExplosiveProductCondition', searchExplosiveProductCondition);
    },
    onClickModalCancel: function(component){
        $A.util.addClass(
              component.find('boomModal'),
              "slds-hide"
        );
    },
    cancel: function(component){
        window.location.href = window.location.origin + '/lightning/n/Order_Apply_List';
    },
    handleUploadFinish: function(component, event, helper) {
        // const productInfos = component.find('uploadCmp').productInfos;
        const productInfosStr = event.getParam('data');
        let productInfos = JSON.parse(productInfosStr);
        let orderItemList = component.get('v.orderItemList');
        productInfos.forEach(item => {
            orderItemList.push(item);
        });
        component.set('v.orderItemList', orderItemList);
        helper.calculateTotal(component);
    }
})