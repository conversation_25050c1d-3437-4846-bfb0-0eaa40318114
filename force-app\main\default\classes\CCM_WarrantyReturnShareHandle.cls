public with sharing class CCM_WarrantyReturnShareHandle extends CCM_Core implements Triggers.Handler{
    private List<Log__c> lstLog;
    public void handle() {
        Set<Id> setCustomerId;
        Set<Integer> setEffectiveYear;
        List<Customer_Profile__c> lstValidCustomerProfile;
        Map<Id,Decimal> mapCustomerSumOrderAmount;
        Map<Id,Decimal> mapWarrantyReturnClaimSumOrderAmount;
        Map<String,Integer> custMap;
        lstLog = new List<Log__c>();
        setCustomerId = new Set<Id>();
        custMap = new Map<String,Integer>();
        setEffectiveYear = new Set<Integer>();
        lstValidCustomerProfile = new List<Customer_Profile__c>();
        mapCustomerSumOrderAmount = new Map<Id,Decimal>();
        mapWarrantyReturnClaimSumOrderAmount = new Map<Id,Decimal>();
        filterValidCustomerProfile(lstValidCustomerProfile, setCustomerId, setEffectiveYear);
        if(setCustomerId.size()>0){
            calculatePartsCostCal(setCustomerId,custMap);
            getCustomerOrderAmount(setCustomerId,mapCustomerSumOrderAmount);
            getWarrantyReturnClaimAmount(setCustomerId, mapWarrantyReturnClaimSumOrderAmount);
            calculateWarrantyReturnShare(lstValidCustomerProfile, mapCustomerSumOrderAmount,mapWarrantyReturnClaimSumOrderAmount,custMap);
            // calculateWarrantyReturnShare(lstValidCustomerProfile,custMap);
            // updateCustomerWebsite(setCustomerId);
            if (!lstLog.isEmpty()) {
                CCM_DmlUtils.doInsertIgnoreResults(lstLog, getClassName(), getMethodName());
            }
        }

    }

    private void calculateWarrantyReturnShare(
        List<Customer_Profile__c> lstValidCustomerProfile,
        Map<Id,Decimal> mapCustomerSumOrderAmount,
        Map<Id,Decimal> mapWarrantyReturnClaimSumOrderAmount,
        Map<String,Integer> custMap
    ) {
        Integer intCurrentYear;
        String strCurrentYear;
        // String strLastYearKey;
        List<String> lstKeyPart;
        for (Customer_Profile__c objCP : lstValidCustomerProfile) {
            String ReverseOrderkey = objCP.Customer__c + objCP.Effective_Year__c;
            if(custMap.containsKey(ReverseOrderkey)) {
                objCP.Reverse_Order_Count_YTD__c = custMap.get(ReverseOrderkey);
            } else {
                objCP.Reverse_Order_Count_YTD__c = 0;
            }
            if(mapCustomerSumOrderAmount.size() > 0 && mapCustomerSumOrderAmount.get(objCP.Customer__c) != null){
                Decimal OrderAmount = mapCustomerSumOrderAmount.get(objCP.Customer__c);
                Decimal ClaimOrderAmount = 0;
                if(mapWarrantyReturnClaimSumOrderAmount.containsKey(objCP.Customer__c)){
                    ClaimOrderAmount = mapWarrantyReturnClaimSumOrderAmount.get(objCP.Customer__c);
                }
                if(OrderAmount != 0){
                    objCP.Warranty_Return_Share__c = ((ClaimOrderAmount / OrderAmount) * 100).setScale(2);
                }
            }
        }
    }

    private void getWarrantyReturnClaimAmount(Set<Id> setCustomerId, Map<Id,Decimal> mapWarrantyReturnClaimSumOrderAmount) {
        Id idCustomer;
        if (setCustomerId.size() == 0) {
            return;
        }
        for (Warranty_Return_Claim_Item__c objAR : [
            SELECT Id,Order_Item__c,Order_Item__r.Invoice_Year__c,Order_Item__r.Price__c,Order_Item__r.Order_Quantity__c,Warranty_Return_Claim__r.Customer__c,Subtotal__c
            FROM Warranty_Return_Claim_Item__c
            WHERE Warranty_Return_Claim__r.Customer__c IN :setCustomerId
        ]) {
                idCustomer = objAR.Warranty_Return_Claim__r.Customer__c;
                if (!mapWarrantyReturnClaimSumOrderAmount.containsKey(idCustomer)) {
                    mapWarrantyReturnClaimSumOrderAmount.put(idCustomer, objAR.Subtotal__c);
                }else{
                    mapWarrantyReturnClaimSumOrderAmount.put(idCustomer, mapWarrantyReturnClaimSumOrderAmount.get(idCustomer)+objAR.Subtotal__c);
                }
        }
    }

    private void getCustomerOrderAmount(Set<Id> setCustomerId,Map<Id,Decimal> mapCustomerSumOrderAmount){
        Id idCustomer;
        for (Order_Item__c objOd : [Select Order__c,Order__r.Date_Order__c ,Price__c ,Order__r.AccountId,Order_Quantity__c
                                      FROM Order_Item__c
                                      WHERE Order__r.AccountId IN :setCustomerId
                                      AND Price__c >0
                                      AND Order_Quantity__c > 0
                                      AND Line_Status__c !='CANCELLED'
                                     ]) {
            idCustomer = objOd.Order__r.AccountId ;
            if (!mapCustomerSumOrderAmount.containsKey(idCustomer)) {
                mapCustomerSumOrderAmount.put(idCustomer, objOd.Price__c*objOd.Order_Quantity__c);
            }else{
                mapCustomerSumOrderAmount.put(idCustomer, mapCustomerSumOrderAmount.get(idCustomer)+objOd.Price__c*objOd.Order_Quantity__c);
            }
        }
    }

    // private void updateCustomerWebsite(Set<Id> setCustomerId){
    //     List<Account> accountList = [SELECT Id,Website,(SELECT Id,Effective_Year__c,Website__c FROM Customer_Profile__r ORDER BY Effective_Year__c DESC LIMIT 1) FROM Account WHERE Id IN :setCustomerId];
    //     List<Account> accountUpdateList = new List<Account>();
    //     for(Account acc : accountList){
    //         if(acc.Customer_Profile__r.size() > 0){
    //             acc.Website = acc.Customer_Profile__r[0].Website__c;
    //             accountUpdateList.add(acc);
    //         }
    //     }

    //     if(accountUpdateList.size() > 0){
    //         update accountUpdateList;
    //     }
    // }

    private void filterValidCustomerProfile(List<Customer_Profile__c> lstValidCustomerProfile, Set<Id> setCustomerId, Set<Integer> setEffectiveYear) {
        List<Customer_Profile__c> lstNew = (List<Customer_Profile__c>) Trigger.new;
        for (Customer_Profile__c objCP : lstNew) {
            if (String.isNotEmpty(objCP.Customer__c)) {
                setCustomerId.add(objCP.Customer__c);
            }
            setEffectiveYear.addAll(getCurrentAndLastIntegerYearFromEffectiveYear(objCP));
            lstValidCustomerProfile.add(objCP);
        }
    }

    private void calculatePartsCostCal(Set<Id> setCustomerId,Map<String,Integer> custMap) {
            List<AggregateResult > custList = [SELECT Customer__c CUSTOMER_ID,CALENDAR_YEAR(CreatedDate) REVERSEORDER_YEAR,COUNT(Id) REVERSEORDER_COUNT
                                           FROM Reverse_Order_Request__c
                                           WHERE  Customer__c=:setCustomerId
                                           GROUP By Customer__c,CALENDAR_YEAR(CreatedDate)];
            for (AggregateResult cu : custList) {
                String key = (String)cu.get('CUSTOMER_ID') + cu.get('REVERSEORDER_YEAR');
                custMap.put(key,(Integer)cu.get('REVERSEORDER_COUNT'));
            }
    }

    @TestVisible
    private Set<Integer> getCurrentAndLastIntegerYearFromEffectiveYear(Customer_Profile__c objCP) {
        final Integer intDefault = 0;
        Integer intEffectiveYearCurrent;
        Set<Integer> setEffectiveYearCurrentAndLast = new Set<Integer>();
        try {
            intEffectiveYearCurrent = String.isBlank(objCP.Effective_Year__c) ? intDefault : Integer.valueOf(objCP.Effective_Year__c);
            if (intEffectiveYearCurrent > intDefault) {
                setEffectiveYearCurrentAndLast.add(intEffectiveYearCurrent);
                setEffectiveYearCurrentAndLast.add(intEffectiveYearCurrent - 1);
            }
        } catch (Exception objE) {
            setEffectiveYearCurrentAndLast = new Set<Integer>();
            lstLog.add(
                new Log__c(
                    ApexName__c = getClassName(),
                    Error_Message__c = objE.getMessage(),
                    Name = 'Failed to get integer from Effective Year of a Customer Profile ' + objCP.Id,
                    Method__c = getMethodName(),
                    ReqParam__c = JSON.serialize(objCP)
                )
            );
        }
        return setEffectiveYearCurrentAndLast;
    }
}