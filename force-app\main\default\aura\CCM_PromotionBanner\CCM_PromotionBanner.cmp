<aura:component access="global" controller="CCM_PromotionAnnounceCtl" implements="forceCommunity:availableForAllPageTypes,flexipage:availableForAllPageTypes" >
    
    <aura:attribute name="currentData" type="List" default="[]"/>
    <aura:attribute name="totalRecords" type="Integer" default="0"/>
    <aura:handler name="init" value="{!this}" action="{!c.initData}"/>

    <aura:if isTrue="{! and(v.currentData, v.currentData.length > 0)}">
        <lightning:carousel>
            <aura:iteration items="{!v.currentData}" var="promotionPic" indexVar="index">
                <c:ccmCommunityPromotionBannerImage
                    src = "{!promotionPic.photoUrl}"
                    alternativeText = "{!promotionPic.promotionName}"
                    promotionId="{!promotionPic.promotionId}"
                    promotionWindowId="{!promotionPic.promotionWindowId}"
                    promotionCode="{!promotionPic.promotionCode}">
                </c:ccmCommunityPromotionBannerImage>
            </aura:iteration>
            <!-- add haibo: french -->
            <aura:if isTrue="{! ($Label.c.CCM_Portal_Language == 'fr')}">
                <lightning:carouselImage
                        src = "{!$Resource.Chervon_Carousel_Image_French+'/ChervonCarouselImageFrench/Chervon1.jpg'}"
                        alternativeText = "First card accessible description.">
                    </lightning:carouselImage>
                <aura:set attribute="else">
                    <lightning:carouselImage
                        src = "{!$Resource.Chervon_Carousel_Image+'/ChervonCarouselImage/Chervon1.jpg'}"
                        alternativeText = "First card accessible description.">
                    </lightning:carouselImage>
                    <!-- <lightning:carouselImage
                        src = "{!$Resource.Chervon_Carousel_Image+'/ChervonCarouselImage/Chervon2.jpg'}"
                        alternativeText = "First card accessible description.">
                    </lightning:carouselImage> -->
                </aura:set>
            </aura:if>
        </lightning:carousel>
        <aura:set attribute="else">
            <lightning:carousel>
                <!-- add haibo: french -->
                <aura:if isTrue="{! ($Label.c.CCM_Portal_Language == 'fr')}">
                    <lightning:carouselImage
                        src = "{!$Resource.Chervon_Carousel_Image_French+'/ChervonCarouselImageFrench/Chervon1.jpg'}"
                        alternativeText = "First card accessible description.">
                    </lightning:carouselImage>
                    <aura:set attribute="else">
                        <!-- <lightning:carouselImage
                                src = "/sfc/servlet.shepherd/version/download/068030000007C99AAE"
                                alternativeText = "First card accessible description.">
                            </lightning:carouselImage>
                            <c:ccmCommunityPromotionBannerImage
                                src = "{!$Resource.Chervon_Carousel_Image+'/ChervonCarouselImage/Chervon1.jpg'}"
                                alternativeText = "First card accessible description.">
                            </c:ccmCommunityPromotionBannerImage>
                         -->
                        <lightning:carouselImage
                            src = "{!$Resource.Chervon_Carousel_Image+'/ChervonCarouselImage/Chervon1.jpg'}"
                            alternativeText = "First card accessible description.">
                        </lightning:carouselImage>
                        <!-- <lightning:carouselImage
                            src = "{!$Resource.Chervon_Carousel_Image+'/ChervonCarouselImage/Chervon2.jpg'}"
                            alternativeText = "First card accessible description.">
                        </lightning:carouselImage> -->
                        <!-- <lightning:carouselImage
                            src = "{!$Resource.Chervon_Carousel_Image+'/ChervonCarouselImage/Flex01.png'}"
                            alternativeText = "First card accessible description.">
                        </lightning:carouselImage>
                        <lightning:carouselImage
                            src = "{!$Resource.Chervon_Carousel_Image+'/ChervonCarouselImage/Flex02.png'}"
                            alternativeText = "First card accessible description.">
                        </lightning:carouselImage>
                        <lightning:carouselImage
                            src = "{!$Resource.Chervon_Carousel_Image+'/ChervonCarouselImage/SKIL01.png'}"
                            alternativeText = "First card accessible description.">
                        </lightning:carouselImage>
                        <lightning:carouselImage
                            src = "{!$Resource.Chervon_Carousel_Image+'/ChervonCarouselImage/SKIL02.png'}"
                            alternativeText = "First card accessible description.">
                        </lightning:carouselImage> -->
                    </aura:set>
                </aura:if>
                
            </lightning:carousel>
        </aura:set>
    </aura:if>
</aura:component>