({
    doInit : function(component, event, helper) {
        var recordId = component.get("v.recordId");
        if(!recordId){
            recordId = helper.getUrlParameter('recordId');
        }

        var columns = [
            {label: $A.get("$Label.c.CCM_Portal_Index"), fieldName: 'Name'},
            {label: $A.get("$Label.c.CCM_Portal_Brand"), fieldName: 'Product__r.Brand_Name__c'},
            {label: $A.get("$Label.c.CCM_Portal_Product"), fieldName: 'Product__r.Name'},
            {label: $A.get("$Label.c.CCM_Portal_Qty"), fieldName: 'Quantity__c'},
            {label: $A.get("$Label.c.CCM_Portal_ListPrice"), fieldName: 'List_Price__c',type: "currency", typeAttributes: { currencyCode: 'USD', maximumSignificantDigits: 5}},
            {label: $A.get("$Label.c.CCM_Portal_YourPrice"), fieldName: 'Unit_Price__c',type: "currency", typeAttributes: { currencyCode: 'USD', maximumSignificantDigits: 5}}
        ];
        component.set('v.columns', columns);

        var action = component.get("c.getData");
        if(recordId){
            action.setParam('recordId', recordId);
        }
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if(results){
                    
                    // update,napoelon,23-1-1, add, currencySymbol & isCCA
                    component.set('v.currencySymbol', results.customerCurrencyIsoCode);
                    let isCCA = results.po.ORG_ID__c === "CCA" ? true : false;
                    component.set('v.isCCA', isCCA);
                    // end
                    component.set('v.showTax', results.showTax);
                    component.set('v.quotation', results.po);
                    component.set('v.quotation.Freight_Fee_Waived__c', helper.currencyFormat(component.get('v.quotation.Freight_Fee_Waived__c')))
                    component.set('v.quotation.Discount__c', helper.currencyFormat(component.get('v.quotation.Discount__c')))
                    component.set('v.paymentTermLabel', results.paymentTermVal);
                    component.set('v.freightTermLabel', results.freightTermVal);
                    component.set('v.orderItemList', results.poItems);
                    component.set('v.customerId', results.customerId);
                    component.set('v.isDelegate', results.po.Is_Delegate__c);
                    console.log('isDelegate--->'+ results.po.Is_Delegate__c);
                    if (results.po.Freight_Fee__c == null){
                        component.set('v.quotation.Freight_Fee__c', 0.00);
                    }
                    if (results.po.Handing_Fee__c == null){
                        component.set('v.quotation.Handing_Fee__c', 0.00);
                    }
                    if(!results.po.Total_Quantity__c){
                        component.set('v.quotation.Total_Quantity__c', 0);
                    }
                    component.set('v.brandScopeOpt', results.brandScopeList);
                    component.set('v.recordId', recordId);
                }
            } else {
                var errors = response.getError();
            }
        });
        $A.enqueueAction(action);
    },

    previousStep: function(component){
        var currentStep = component.get("v.currentStep");
        component.set("v.currentStep", currentStep - 1);
    },
    backHome: function(component){
        window.location.href = window.location.origin + (location.host.indexOf("lightning") < 0 ? "/s/orderinformation" : "/lightning/n/Order_Apply_List");
    },
    doSubmit: function(component){
        var recordId = component.get("v.recordId");
        var action = component.get("c.submitData");
        action.setParam('recordId', recordId);
        action.setParam('isDelegate', component.get('v.isDelegate'));
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                if(results === 'Success'){
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": $A.get("$Label.c.CCM_Portal_Success"),
                        "message": $A.get("$Label.c.CCM_Portal_OrderSubmitTips"),
                        "type": "success"
                    });
                    toastEvent.fire();

                    setTimeout(function () {
                        if (location.host.indexOf('lightning') < 0) {
                            window.location.href = window.location.origin + '/s/orderinformation';
                        }else{
                            var url = window.location.origin + '/lightning/n/Order_Apply_List';
                            window.open(url,'_self');
                        }
                    }, 3000);
                }
            } else {
                var errors = response.getError();
            }
        });
        $A.enqueueAction(action);
    }
})