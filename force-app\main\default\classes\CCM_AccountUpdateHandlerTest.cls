@IsTest
private class CCM_AccountUpdateHandlerTest {
    @IsTest
    static void testApprovalStatusUpdate() {
        Test.startTest();
        User objFinanceUser = [SELECT Id FROM User WHERE Profile.Name = 'Finance' AND UserRoleId != NULL AND IsActive = TRUE LIMIT 1];
        System.runAs(objFinanceUser) {
            Account objChannelCustomer = new Account(Name = 'test', RecordTypeId = CCM_Contants.CHANNEL_RECORDTYPEID, Approval_Status__c = 'Pending for Approval');
            insert objChannelCustomer;
            objChannelCustomer.Approval_Status__c = 'Approved';
            update objChannelCustomer;
        }
        Test.stopTest();
    }
    @IsTest
    static void testFieldSetUpdate() {
        Test.startTest();
        User objFinanceUser = [SELECT Id FROM User WHERE Profile.Name = 'Finance' AND UserRoleId != NULL AND IsActive = TRUE LIMIT 1];
        System.runAs(objFinanceUser) {
            Account objChannelCustomer = new Account(Name = 'test', RecordTypeId = CCM_Contants.CHANNEL_RECORDTYPEID, Credit_Limit__c = 1);
            insert objChannelCustomer;
            objChannelCustomer.Credit_Limit__c = 2;
            update objChannelCustomer;
        }
        Test.stopTest();
    }
}