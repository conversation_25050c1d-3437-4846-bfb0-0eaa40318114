({
    getCustomerInfoHelper: function(component){
        var self = this;
        var endUserCustomer = component.get("v.fleetClaim.endUserCustomer");
        var email = endUserCustomer.emailAddress;
        if($A.util.isEmpty(email)){
            return;
        }
        var action = component.get("c.getCustomerInfo");
        action.setParam("email", email);
        self.showEle(component, 'spinner');
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var auraResponse = response.getReturnValue();
                if(auraResponse.code === 200){
                    endUserCustomer.id = auraResponse.data.id;
                    endUserCustomer.productType = auraResponse.data.productType;
                    endUserCustomer.eligibleForFleet = auraResponse.data.eligibleForFleet;
                    if(!$A.util.isEmpty(auraResponse.data.emailAddress)){
                        endUserCustomer.emailAddress = auraResponse.data.emailAddress;
                    }
                    if(!$A.util.isEmpty(auraResponse.data.firstName)){
                        endUserCustomer.firstName = auraResponse.data.firstName;
                    }
                    if(!$A.util.isEmpty(auraResponse.data.lastName)){
                        endUserCustomer.lastName = auraResponse.data.lastName;
                    }
                    if(!$A.util.isEmpty(auraResponse.data.country)){
                        endUserCustomer.country = auraResponse.data.country;
                    }
                    if(!$A.util.isEmpty(auraResponse.data.zipPostalCode)){
                        endUserCustomer.zipPostalCode = auraResponse.data.zipPostalCode;
                    }
                    if(!$A.util.isEmpty(auraResponse.data.orgName)){
                        endUserCustomer.orgName = auraResponse.data.orgName;
                    }
                    if(!$A.util.isEmpty(auraResponse.data.phone)){
                        endUserCustomer.phone = auraResponse.data.phone;
                    }
                    if(!$A.util.isEmpty(auraResponse.data.city)){
                        endUserCustomer.city = auraResponse.data.city;
                    }
                    if(!$A.util.isEmpty(auraResponse.data.state)){
                        endUserCustomer.state = auraResponse.data.state;
                    }
                    if(!$A.util.isEmpty(auraResponse.data.addressLine)){
                        endUserCustomer.addressLine = auraResponse.data.addressLine;
                    }
                    component.set('v.fleetClaim.endUserCustomer', endUserCustomer);
                }else{
                    self.showToast(null, auraResponse.message, "error");
                }
            } else {
                self.showToast(null, JSON.stringify(response.getError()), "error");
            }
            self.hideEle(component, 'spinner');
        });
        $A.enqueueAction(action);
    },
    getAddressInfoHelper: function(component){
        var self = this;
        var endUserCustomer = component.get("v.fleetClaim.endUserCustomer");
        var zipPostalCode = endUserCustomer.zipPostalCode;
        var country = endUserCustomer.country;
        if($A.util.isEmpty(country) || $A.util.isEmpty(zipPostalCode)){
            return;
        }
        var action = component.get('c.getAddressInfo');
        action.setParams({
            'zipPostalCode': zipPostalCode,
            'country': country
        });
        self.showEle(component, 'spinner');
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === 'SUCCESS' ) {
                var auraResponse = response.getReturnValue();
                if(auraResponse.code === 200){
                    var result = auraResponse.data;
                    if(!$A.util.isEmpty(result)){
                        var resultList = result.split(',');
                        if(resultList.length >= 1){
                            //city
                            endUserCustomer.city = resultList[0];
                        }
                        if(resultList.length >= 2){
                            //state
                            endUserCustomer.state = resultList[1];
                        }
                        if(resultList.length >= 3){
                            //country
                            endUserCustomer.country = resultList[2];
                        }
                        component.set('v.fleetClaim.endUserCustomer', endUserCustomer);
                    }
                }else{
                    self.showToast(null, auraResponse.message, "error");
                }
            }else{
                self.showToast(null, JSON.stringify(response.getError()), "error");
            }
            self.hideEle(component, 'spinner');
        });
        $A.enqueueAction(action);
    },
    showToast: function(title, message,type) {
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams({
            "title": title,
            "message": message,
            "type": type
        });
        toastEvent.fire();
    },
    showEle: function(component, ele){
        $A.util.removeClass(
            component.find(ele),
            "slds-hide"
        );
    },
    hideEle: function(component, ele){
        $A.util.addClass(
            component.find(ele),
            "slds-hide"
        );
    }
})