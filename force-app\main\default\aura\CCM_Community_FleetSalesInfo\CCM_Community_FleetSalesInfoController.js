({
    doInit: function (component, event, helper) {
		// add haibo: french
		window.setTimeout(function () {
			console.log("页面加载完成后延迟执行函数！");
			var fleetItemList = component.get("v.fleetClaim.fleetItemList");
			var fleetItemList1 = component.get("v.fleetItemList");
			component.set("v.fleetClaim.fleetItemList", []);
			if (fleetItemList1.length == 0) {
				component.set("v.fleetItemList", fleetItemList);
            }
            var fleetClaimList = component.get("v.fleetClaimList");
            if (fleetItemList !== undefined && fleetItemList.length > 0) {
                fleetItemList.forEach((item) => {
                    if (item.qtyPurchased !== undefined && item.qtyPurchased !== null && item.qtyPurchased > 0) {
                        fleetClaimList.push(item);
                    }

                });
                component.set("v.fleetClaimList", fleetClaimList);
                component.set("v.fleetClaim.fleetItemList", fleetClaimList);
            }
			// var fleetItemList = component.get('v.fleetClaim.fleetItemList');
			// component.set("v.fleetClaim.fleetItemList", []);
			// component.set("v.fleetItemList", fleetItemList);
		}, 2000);
        component.set("v.options", [
			{
				label: $A.get("$Label.c.CCM_Portal_IndustrialProfessionalCommercial"),
				value: "Industrial/Professional/Commercial"
			},
			{
				label: $A.get("$Label.c.CCM_Portal_Residential"),
				value: "Residential"
			},
			{
				label: $A.get("$Label.c.CCM_Portal_Rental"),
				value: "Rental"
			}
		]);
	},


    qtyPurchasedChange: function(component, event, helper){
        var fleetProgramRule = component.get("v.fleetProgramRule");
        var maximumDiscountCriteria = fleetProgramRule.maximumDiscountCriteria/100;//百分数
        let discountReturnRules = fleetProgramRule.discountReturnRules;
        var discountReturn = fleetProgramRule.discountReturn/100;//百分数
        var rowIndex = event.getSource().get("v.name");
        console.log('rowIndex', rowIndex);
        var fleetClaim = component.get('v.fleetClaim');
        var fleetItemList = fleetClaim.fleetItemList;
        var fleetItem = fleetItemList[rowIndex];
        if($A.util.isEmpty(fleetItem.qtyPurchased)){
            fleetItem.qtyPurchased = 0;
        }
        //预填值
        if(!maximumDiscountCriteria || maximumDiscountCriteria < 0) {
            maximumDiscountCriteria = 0;
        }
        let productTypeDiscount = undefined;
        if(discountReturnRules) {
            let rules = discountReturnRules.filter(rule => rule['productType'] == fleetItem['productType']);
            if(rules && rules.length > 0) {
                productTypeDiscount = rules[0]['proportion']/100;
            }
        }
        if($A.util.isEmpty(fleetItem.unitSalesPrice) && fleetItem.qtyPurchased >= 1){
            if(maximumDiscountCriteria > 0) {
                fleetItem.unitSalesPrice = Math.round(fleetItem.msrp * (1 - maximumDiscountCriteria) * 100) / 100;
            }
        }
        fleetItem.total = Math.round(fleetItem.msrp * fleetItem.qtyPurchased * 100) / 100;
        if(fleetItem.unitSalesPrice) {
            fleetItem.totalSalesPrice = Math.round(fleetItem.unitSalesPrice * fleetItem.qtyPurchased * 100) / 100;
        }
        if(maximumDiscountCriteria > 0) {
            fleetItem.fleetDiscount = Math.round(fleetItem.total * maximumDiscountCriteria * 100) / 100;
        }
        if(maximumDiscountCriteria > 0 && fleetItem.totalSalesPrice > (fleetItem.total - fleetItem.fleetDiscount).toFixed(2)){
            fleetItem.dealerRebate = 0;
        }else{
            if(productTypeDiscount) {
                fleetItem.dealerRebate = Math.round(fleetItem.total * discountReturn * productTypeDiscount * 100) / 100;
            }
            else {
                fleetItem.dealerRebate = Math.round(fleetItem.total * discountReturn * 100) / 100;
            }
        }

        fleetClaim.totalSalesAmount = 0;
        fleetClaim.totalRetailPrice = 0;
        fleetClaim.fleetDiscount = 0;
        fleetClaim.estimatedCreditReturn = 0;

        for (var i = fleetItemList.length - 1; i >= 0; i--) {
            if(!$A.util.isEmpty(fleetItemList[i].total)){
                fleetClaim.totalSalesAmount += fleetItemList[i].total;
            }

            if(!$A.util.isEmpty(fleetItemList[i].totalSalesPrice)){
                fleetClaim.totalRetailPrice += fleetItemList[i].totalSalesPrice;
            }

            if(!$A.util.isEmpty(fleetItemList[i].fleetDiscount)){
                fleetClaim.fleetDiscount += fleetItemList[i].fleetDiscount;
            }

            if(!$A.util.isEmpty(fleetItemList[i].dealerRebate)){
                fleetClaim.estimatedCreditReturn += fleetItemList[i].dealerRebate;
            }
        }
        component.set("v.fleetClaimList", fleetClaim.fleetItemList);
        component.set("v.fleetClaim", fleetClaim);
    },
    showWarrantyInfo: function(component, event, helper){
        component.set('v.showWarrantyInfo', true);
    },
    addItem: function (component, event, helper) {
        var fleetItemList = component.get('v.fleetClaim.fleetItemList');
        var fleetItemList1 = component.get('v.fleetItemList');
        component.set("v.fleetClaim.fleetItemList", []);
        if (fleetItemList1.length == 0) {
            component.set("v.fleetItemList", fleetItemList);
            var fleetClaimList1 = component.get("v.fleetClaimList");
            if (fleetItemList !== undefined && fleetItemList.length > 0) {
                fleetItemList.forEach((item) => {
                    if (item.qtyPurchased !== undefined && item.qtyPurchased !== null && item.qtyPurchased > 0) {
                        fleetClaimList1.push(item);
                    }

                });
                component.set("v.fleetClaimList", fleetClaimList1);
                component.set("v.fleetClaim.fleetItemList", fleetClaimList1);
            }
		}
        var fleetClaimList = component.get("v.fleetClaimList");
        var newfleetItem = {
            "kitCode": "",
            "filteredItems":[]
        };
        fleetClaimList.push(newfleetItem);
        component.set("v.fleetClaimList", []);
        component.set("v.fleetClaimList", fleetClaimList);
        component.set("v.fleetClaim.fleetItemList", fleetClaimList);
    },
    handleSearch: function(component, event, helper) {

        var index = event.getSource().get('v.name');
        var searchKey = event.getSource().get("v.value").toLowerCase();
        var fleetClaimList = component.get('v.fleetClaimList');
        var kitCodeList = [];
        fleetClaimList.forEach(item => {
            kitCodeList.push(item.kitCode);
        });
        var items = component.get('v.fleetItemList');
        var filteredItems = items.filter(function(item) {
            return (item.kitCode.toLowerCase().includes(searchKey) || item.kitName.toLowerCase().includes(searchKey)) && kitCodeList.indexOf(item.kitCode) < 0;
        });
        fleetClaimList[index].filteredItems = filteredItems;
        component.set("v.fleetClaimList", fleetClaimList);
    },
    selectItem: function(component, event, helper) {
        var selectedValue = event.currentTarget.dataset.value;
        var index = event.currentTarget.getAttribute('data-index');
        var fleetItemList = component.get('v.fleetItemList');
        var fleetClaimList = component.get('v.fleetClaimList');
        var currentFleet = [];
        // fleetItemList.forEach(item1 => {
        //     delete item1.dealerRebate;
        //     delete item1.filteredItems;
        //     delete item1.fleetDiscount;
        //     delete item1.qtyPurchased;
        //     delete item1.total;
        //     delete item1.totalSalesPrice;
        //     delete item1.unitSalesPrice;
        // });
        fleetItemList.forEach(item => {
            if(selectedValue == item.kitCode){
                currentFleet = item;
            }
        });
        fleetClaimList[index] = currentFleet;
        fleetClaimList[index].filteredItems = [];
        component.set("v.fleetClaimList", []);
        component.set("v.fleetClaimList", fleetClaimList);
        component.set("v.fleetClaim.fleetItemList", fleetClaimList);

    },
    handleDelete: function(component, event, helper) {
        var rowIndex = event.currentTarget.getAttribute('data-index');
        var fleetClaimList = component.get('v.fleetClaimList');
        fleetClaimList[rowIndex].qtyPurchased = 0;

        component.set("v.fleetClaimList", fleetClaimList);
        component.set("v.fleetClaim.fleetItemList", fleetClaimList);
        var fleetProgramRule = component.get("v.fleetProgramRule");
        var maximumDiscountCriteria = fleetProgramRule.maximumDiscountCriteria/100;//百分数
        let discountReturnRules = fleetProgramRule.discountReturnRules;
        var discountReturn = fleetProgramRule.discountReturn/100;//百分数
        console.log('rowIndex', rowIndex);
        var fleetClaim = component.get('v.fleetClaim');
        var fleetItemList = fleetClaim.fleetItemList;
        var fleetItem = fleetItemList[rowIndex];
        if($A.util.isEmpty(fleetItem.qtyPurchased)){
            fleetItem.qtyPurchased = 0;
        }
        //预填值
        if(!maximumDiscountCriteria || maximumDiscountCriteria < 0) {
            maximumDiscountCriteria = 0;
        }
        let productTypeDiscount = undefined;
        if(discountReturnRules) {
            let rules = discountReturnRules.filter(rule => rule['productType'] == fleetItem['productType']);
            if(rules && rules.length > 0) {
                productTypeDiscount = rules[0]['proportion']/100;
            }
        }
        if($A.util.isEmpty(fleetItem.unitSalesPrice) && fleetItem.qtyPurchased >= 1){
            if(maximumDiscountCriteria > 0) {
                fleetItem.unitSalesPrice = Math.round(fleetItem.msrp * (1 - maximumDiscountCriteria) * 100) / 100;
            }
        }
        fleetItem.total = Math.round(fleetItem.msrp * fleetItem.qtyPurchased * 100) / 100;
        if(fleetItem.unitSalesPrice) {
            fleetItem.totalSalesPrice = Math.round(fleetItem.unitSalesPrice * fleetItem.qtyPurchased * 100) / 100;
        }
        if(maximumDiscountCriteria > 0) {
            fleetItem.fleetDiscount = Math.round(fleetItem.total * maximumDiscountCriteria * 100) / 100;
        }
        if(maximumDiscountCriteria > 0 && fleetItem.totalSalesPrice > (fleetItem.total - fleetItem.fleetDiscount).toFixed(2)){
            fleetItem.dealerRebate = 0;
        }else{
            if(productTypeDiscount) {
                fleetItem.dealerRebate = Math.round(fleetItem.total * discountReturn * productTypeDiscount * 100) / 100;
            }
            else {
                fleetItem.dealerRebate = Math.round(fleetItem.total * discountReturn * 100) / 100;
            }
        }

        fleetClaim.totalSalesAmount = 0;
        fleetClaim.totalRetailPrice = 0;
        fleetClaim.fleetDiscount = 0;
        fleetClaim.estimatedCreditReturn = 0;

        for (var i = fleetItemList.length - 1; i >= 0; i--) {
            if(!$A.util.isEmpty(fleetItemList[i].total)){
                fleetClaim.totalSalesAmount += fleetItemList[i].total;
            }

            if(!$A.util.isEmpty(fleetItemList[i].totalSalesPrice)){
                fleetClaim.totalRetailPrice += fleetItemList[i].totalSalesPrice;
            }

            if(!$A.util.isEmpty(fleetItemList[i].fleetDiscount)){
                fleetClaim.fleetDiscount += fleetItemList[i].fleetDiscount;
            }

            if(!$A.util.isEmpty(fleetItemList[i].dealerRebate)){
                fleetClaim.estimatedCreditReturn += fleetItemList[i].dealerRebate;
            }
        }
        component.set("v.fleetClaim", fleetClaim);
        component.set("v.fleetClaimList", fleetClaim.fleetItemList);
        var fleetClaimList2 = component.get('v.fleetClaimList');
        fleetClaimList2.splice(rowIndex, 1);
        component.set("v.fleetClaimList", fleetClaimList2);
        component.set("v.fleetClaim.fleetItemList", fleetClaimList2);

    }



})