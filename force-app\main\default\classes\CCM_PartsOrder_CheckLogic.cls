public with sharing class CCM_PartsOrder_CheckLogic {
    public CCM_PartsOrder_CheckLogic() {

    }

    public static ReturnMessage checkAccountEligible(Account customer) {
        System.debug('customerid:' + customer.Id);
        Set<Id> availableRecordTyps = new Set<Id>();
        availableRecordTyps.addAll(CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SALES_IDS);
        availableRecordTyps.addAll(CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_IDS);

        List<Sales_Program__c> authBrands = [SELECT Customer__c, RecordType.DeveloperName, Approval_Status__c 
                                             FROM Sales_Program__c 
                                             WHERE Customer__c = :customer.Id 
                                             AND RecordTypeId IN :availableRecordTyps
                                             AND Approval_Status__c = 'Approved'];
        
        
        if(authBrands.isEmpty()) {
            ReturnMessage returnMessage = new ReturnMessage();
            returnMessage.IsSuccess = false;
            returnMessage.ErrorMessage = 'Customer you selected can not be delegated to place parts order,because of this customer doesn\'t have available authorized brand';
            return returnMessage;
        }
        return null;
    }


    public static ReturnMessage checkProductExist(Set<String> allProductCodes, Set<String> findProductCodes) {
        allProductCodes.removeAll(findProductCodes);
        if(!allProductCodes.isEmpty()) { 
            List<String> temps = new List<String>();
            temps.addAll(allProductCodes);
            ReturnMessage returnMessage = new ReturnMessage();
            returnMessage.IsSuccess = false;
            returnMessage.ErrorMessage = String.format('Parts Stock Number {0} not Exist.', new List<String>{String.join(temps, '; ')});
            return returnMessage;
        }
        return null;
    }

    public static List<ErrorDetail> checkProductExistWithDetail(Set<String> allProductCodes, Set<String> findProductCodes) {
        List<ErrorDetail> errorDetails = new List<ErrorDetail>();
        allProductCodes.removeAll(findProductCodes);
        if(!allProductCodes.isEmpty()) { 
            for(String productCode : allProductCodes) {
                ErrorDetail errDetail = new ErrorDetail();
                errDetail.ProductCode = productCode;
                errDetail.ErrorMsg = 'Parts Stock Number not Exist.';
                errorDetails.add(errDetail);
            }
        }
        return errorDetails;
    }

    public class ErrorDetail {
        public String ProductCode {get;set;}
        public String ErrorMsg {get;set;}
    }


    public class ReturnMessage {
        public Boolean IsSuccess {get;set;}
        public String ErrorMessage {get;set;}
    }
}