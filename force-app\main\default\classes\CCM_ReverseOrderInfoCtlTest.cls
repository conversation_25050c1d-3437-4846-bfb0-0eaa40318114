/**
 * CCM_ReverseOrderInfoCtl of test class
 */
@IsTest
public class CCM_ReverseOrderInfoCtlTest {
    @TestSetup
    static void testSetUpData() {
        Test.startTest();
        // CCM_SalesOrgValidationHandler.isRun = false;
        // CCM_UpdateBrandSeriesOnOrderHandler.isRun = false;
        CCM_SharingUtil.isSharingOnly = true;
        CCM_SharingUtil.isSharingCalculation = true;
        CCM_ContactUpdateHandler.isRun = false;
        CCM_StopUpdatingPimSellableHandler.boolToRun = false;
        CCM_UpaftCtrl.inFutureContext = true;
        CCM_TaxUpdateOrderItemHandler.boolToRun = false;
        
        String recordId = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' AND DeveloperName = 'Channel' LIMIT 1].Id;
        Account account = new Account();
        account.Name = 'Test';
        account.RecordTypeId = recordId;
        account.Distributor_or_Dealer__c = '2nd Tier Dealer';
        account.AccountNumber = 'yyy111';
        account.TaxID__c = 'testTax';
        account.ORG_Code__c = 'CNA';
        insert account;

        Contact contact = new Contact();
        contact.AccountId = account.Id;
        contact.LastName = 'xxx';
        contact.FirstName = 'YYY';
        contact.OwnerId = UserInfo.getUserId();
        insert contact;

        User userPartner = [SELECT Id,Email,ContactId FROM User WHERE Id = :UserInfo.getUserId()];
        userPartner.ContactId = contact.Id;

        //String addressRecordId = [SELECT Id FROM RecordType WHERE SobjectType = 'Account_Address__c' AND DeveloperName = 'Dropship_Shipping_Address' LIMIT 1].Id;
        Account_Address__c add = new Account_Address__c();
        add.Name = 'add test';
        add.Customer__c = account.Id;
        //add.RecordTypeId = CCM_Constants.ADDRESS_DROPSHIP_SHIPPING_ADDRESS_RECORD_TYPE_ID;
        add.X2nd_Tier_Dealer__c = account.Id;
        add.Active__c = true;
        add.Approval_Status__c = 'Approved';
        add.Contact__c = contact.Id;
        insert add;


        Pricebook2 pb = new Pricebook2();
        pb.Name = 'Standard Price Book';
        pb.IsActive = true;
        pb.Contract_Price_Book_OracleID__c = '11';
        pb.Price_Book_OracleID__c = '11';
        pb.Org_Code__c = 'CNA';
        insert pb;


        Product2 pro = new Product2();
        pro.Name = 'Test';
        pro.Brand_Name__c = 'EGO';
        pro.ProductCode = '1234567';
        pro.IsActive = true;
        pro.Source__c = 'PIM';
        pro.Description = pro.SF_Description__c;
        insert pro;

        Product2 pro2 = new Product2();
        pro2.Name = 'Test123';
        pro2.Brand_Name__c = 'EGO';
        pro2.ProductCode = '1234567';
        pro2.IsActive = true;
        pro2.Source__c = 'EBS';
        insert pro2;
        pro2.Description = pro2.SF_Description__c;
        update pro2;


        /*PricebookEntry spbe = new PricebookEntry();
        spbe.IsActive = true;
        spbe.Product2Id = pro.Id;
        spbe.UnitPrice = 1000;
        spbe.Pricebook2Id = Test.getStandardPricebookId();
        spbe.UseStandardPrice = false;
        insert spbe;*/

        PricebookEntry pbe = new PricebookEntry();
        pbe.IsActive = true;
        pbe.Product2Id = pro.Id;
        pbe.UnitPrice = 1000;
        pbe.Pricebook2Id = pb.Id;
        pbe.UseStandardPrice = false;
        insert pbe;

        Sales_Program__c salesProgram = new Sales_Program__c();
        salesProgram.Customer__c = account.Id;
        salesProgram.Approval_Status__c = 'Approved';
        salesProgram.Price_Book__c = pb.Id;
        salesProgram.Authorized_Brand_Name_To_Oracle__c = 'EGO_2020_Test';
        salesProgram.RecordTypeId = CCM_Contants.SALES_STANDARD_PROGRAM_RECORDTYPEID;
        salesProgram.Brands__c = 'EGO';
        salesProgram.ORG_Code__c= 'CNA';
        salesProgram.Payment_Term__c = 'NA001';
        salesProgram.Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.';
        insert salesProgram;

        Purchase_Order__c objPO = new Purchase_Order__c(Customer__c = account.Id);
        insert objPO;

        Address_With_Program__c addPro = new Address_With_Program__c();
        addPro.Account_Address__c = add.Id;
        addPro.Program__c = salesProgram.Id;
        insert addPro;
        OrderTriggerHandle.isRun = false;
        //创建Order数据
        Order o = TestDataFactory.createOrder();
        o.AccountId = account.Id;
        o.Order_Number__c = 'yyy';
        o.Order_Status__c = 'Partial Shipment';
        o.BillTo__c = addPro.Id;
        o.ShipTo__c = addPro.Id;
        o.Purchase_Order__c = objPO.Id;
        o.Price_Book__c = pb.Id;
        o.PO_Number__c = 'CCC-11111';
        o.Org_Code__c = 'CNA';
        insert o;

        Order_Item__c orderItem = new Order_Item__c();
        orderItem.Order__c = o.Id;
        orderItem.Product__c = pro.Id;
        orderItem.Line_Status__c = 'INVOICED';
        insert orderItem;
        Test.stopTest();
        Invoice__c inv = new Invoice__c();
        inv.Order__c = o.Id;
        inv.Delivery_Number__c = 'ship00001';
        insert inv;

        Invoice_Item__c invItem = new Invoice_Item__c();
        invItem.Invoice__c = inv.Id;
        insert invItem;

        Shipment__c st1 = new Shipment__c();
        st1.Order__c = o.Id;
        st1.Ship_OracleID__c = 'ship00001';
        st1.Ship_Date__c = Date.today().addDays(-10);
        insert st1;

        Shipment_Item__c shipmentItem = new Shipment_Item__c();
        shipmentItem.Shipment__c = st1.Id;
        shipmentItem.Product__c = pro.Id;
        shipmentItem.Item_Quantity_in_Shipment__c = 1;
        insert shipmentItem;


        Reverse_Order_Request__c reverseOrderRequest = new Reverse_Order_Request__c();
        reverseOrderRequest.Customer__c = account.Id;
        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.Submitted_Date__c = Date.newInstance(2021, 07, 20);
        //reverseOrderRequest.Billing_Address__c = add.Id;
        reverseOrderRequest.Order__c = o.Id;
        reverseOrderRequest.Customer_Contact_Email__c = '<EMAIL>;<EMAIL>';
        reverseOrderRequest.ShipTo__c = addPro.Id;
        reverseOrderRequest.BillTo__c = addPro.Id;
        insert reverseOrderRequest;

        Reverse_Order_Item__c reverseOrderItem = new Reverse_Order_Item__c();
        reverseOrderItem.Reverse_Order_Request__c = reverseOrderRequest.Id;
        reverseOrderItem.Brand__c = 'EGO';
        reverseOrderItem.Order_Product_Type__c = 'Overage';
        reverseOrderItem.Next_Step_Action__c = 'I want to buy the overage product';
        insert reverseOrderItem;

        ContentVersion contentVersionTest = new ContentVersion(
                Title = 'Contract',
                PathOnClient = 'Contract.pdf',
                VersionData = Blob.valueOf('Test Content'),
                IsMajorVersion = true
        );
        insert contentVersionTest;
        ContentDocumentLink cdl = New ContentDocumentLink();
        List<ContentDocument> documents = [
                SELECT Id, Title, LatestPublishedVersionId
                FROM ContentDocument
        ];
        cdl.LinkedEntityId = reverseOrderRequest.id;
        cdl.ContentDocumentId = documents[0].Id;
        cdl.ShareType = 'V';
        insert cdl;


    }

    @IsTest
    static void testGetReverseOrderNumberByStatus() {
        Test.startTest();
        Reverse_Order_Request__c req = [SELECT Id, Customer__c FROM Reverse_Order_Request__c WHERE Customer__r.Name = 'Test' LIMIT 1];
        CCM_ReverseOrderInfoCtl.getReverseOrderNumberByStatus(req.Customer__c);
        req.Approval_Status__c = 'Approved';
        update req;
        CCM_ReverseOrderInfoCtl.getReverseOrderNumberByStatus(req.Customer__c);
        req.Approval_Status__c = 'Recalled';
        update req;
        CCM_ReverseOrderInfoCtl.getReverseOrderNumberByStatus(req.Customer__c);
        req.Approval_Status__c = 'Submitted for Approval';
        update req;
        CCM_ReverseOrderInfoCtl.getReverseOrderNumberByStatus(req.Customer__c);
        CCM_ReverseOrderInfoCtl.getHelpTexts();
        Test.stopTest();
    }

    @IsTest
    static void testGetProducts() {
        Test.startTest();
        Account account = [SELECT Id FROM Account LIMIT 1];
        Product2 product2 = [SELECT Id,SF_Description__c FROM Product2 WHERE Source__c = 'PIM' LIMIT 1];
        CCM_ReverseOrderInfoCtl.getProducts(account.Id, product2.SF_Description__c, 'yyy');

        Product2 product22 = [SELECT Id,SF_Description__c FROM Product2 WHERE Source__c = 'EBS' LIMIT 1];
        CCM_ReverseOrderInfoCtl.getProducts(account.Id, product22.SF_Description__c, 'yyy');
        CCM_ReverseOrderInfoCtl.getProducts(account.Id, product2.SF_Description__c, '');
        Test.stopTest();
    }

    @IsTest
    static void testGetProductPrice1() {
        Test.startTest();
        Purchase_Order__c purchaseOrder = [SELECT Id,Is_DropShip__c FROM Purchase_Order__c LIMIT 1];
        Account account = [SELECT Id FROM Account LIMIT 1];
        Product2 product2 = [SELECT Id FROM Product2 WHERE Source__c = 'PIM'];

        CCM_ReverseOrderInfoCtl.getProductPrice(product2.Id, account.Id, purchaseOrder.Id);
        Test.stopTest();
    }

    @IsTest
    static void testGetProductPrice2() {
        Test.startTest();
        Purchase_Order__c purchaseOrder = [SELECT Id,Is_DropShip__c FROM Purchase_Order__c LIMIT 1];
        Account account = [SELECT Id FROM Account LIMIT 1];
        Product2 product2 = [SELECT Id FROM Product2 WHERE Source__c = 'PIM'];
        purchaseOrder.Is_DropShip__c = true;
        update purchaseOrder;
        CCM_ReverseOrderInfoCtl.getProductPrice(product2.Id, account.Id, purchaseOrder.Id);
        Test.stopTest();
    }

    @IsTest
    static void testGetOrder() {

        Test.startTest();
        Order order = [SELECT Id FROM Order LIMIT 1];
        CCM_ReverseOrderInfoCtl.getOrder('yyy', 'yyy');
        CCM_ReverseOrderInfoCtl.getOrder(order.Id, 'yyy');
        Test.stopTest();
    }

    @IsTest
    static void testGetAddress1() {
        Account account = [SELECT Id,Distributor_or_Dealer__c FROM Account LIMIT 1];
        Test.startTest();
        CCM_ReverseOrderInfoCtl.getAddress(null);
        CCM_ReverseOrderInfoCtl.getAddress(account.Id);
        CCM_ReverseOrderInfoCtl.getShipping('test', account.Id);
        Test.stopTest();
    }

    @IsTest
    static void testSaveReverseOrder() {
        Test.startTest();
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id FROM Reverse_Order_Request__c LIMIT 1];
        Account account = [SELECT Id,Distributor_or_Dealer__c FROM Account LIMIT 1];
        Order order = [SELECT Id FROM Order LIMIT 1];
        Reverse_Order_Item__c reverseOrderItem = [SELECT Id FROM Reverse_Order_Item__c LIMIT 1];
        Account_Address__c accountAddress = [SELECT Id FROM Account_Address__c LIMIT 1];
        CCM_ReverseOrderInfoCtl.ReverseOrderWrapper reverseOrderWrapper = new CCM_ReverseOrderInfoCtl.ReverseOrderWrapper();
        reverseOrderWrapper.recordId = reverseOrderRequest.Id;
        reverseOrderWrapper.externalReturnReason = CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_LESS_THAN_I_ORDERED;
        reverseOrderWrapper.customerId = account.Id;
        reverseOrderWrapper.returnFreightFee = 100;
        reverseOrderWrapper.customerContactEmails = '<EMAIL>';
        reverseOrderWrapper.remark = 'yyy';
        reverseOrderWrapper.orderId = order.Id;

        List<String> brandList = new List<String>();
        brandList.add('EGO');
        brandList.add('SKIL');
        reverseOrderWrapper.brands = brandList;

        List<CCM_ReverseOrderInfoCtl.ReverseOrderItemWrapper> reverseOrderItemWrapperList = new List<CCM_ReverseOrderInfoCtl.ReverseOrderItemWrapper>();
        CCM_ReverseOrderInfoCtl.ReverseOrderItemWrapper reverseOrderItemWrapper = new CCM_ReverseOrderInfoCtl.ReverseOrderItemWrapper();
        reverseOrderItemWrapper.recordId = reverseOrderItem.Id;
        reverseOrderItemWrapperList.add(reverseOrderItemWrapper);

        reverseOrderWrapper.takeItems = reverseOrderItemWrapperList;
        reverseOrderWrapper.returnItems = reverseOrderItemWrapperList;

        CCM_ReverseOrderInfoCtl.AddressWrapper addressWrapper = new CCM_ReverseOrderInfoCtl.AddressWrapper();
        addressWrapper.addressId = accountAddress.Id;
        reverseOrderWrapper.billingAddress = addressWrapper;
        reverseOrderWrapper.IsAlternativeAddress = true;

        CCM_ReverseOrderInfoCtl.saveReverseOrder(JSON.serialize(reverseOrderWrapper));
        reverseOrderWrapper.IsAlternativeAddress = false;
        CCM_ReverseOrderInfoCtl.saveReverseOrder(JSON.serialize(reverseOrderWrapper));
        Test.stopTest();
    }

    @IsTest
    static void testUpdateReverseOrderOPS() {
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id FROM Reverse_Order_Request__c LIMIT 1];
        Test.startTest();
        CCM_ReverseOrderInfoCtl.updateReverseOrderOPS(reverseOrderRequest.Id, 'OPS confirmed – Warehouse', 'yyy');
        CCM_ReverseOrderInfoCtl.updateReverseOrderOPS(reverseOrderRequest.Id, 'OPS confirme', 'yyy');

        reverseOrderRequest.Reverse_Order_Type__c = 'Wrong Product';
        update reverseOrderRequest;
        CCM_ReverseOrderInfoCtl.updateReverseOrderOPS(reverseOrderRequest.Id, 'OPS confirme', 'yyy');
        Test.stopTest();
    }
    @IsTest
    static void testUpdateReverseOrderOPS1() {
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id FROM Reverse_Order_Request__c LIMIT 1];
        Test.startTest();
        reverseOrderRequest.Reverse_Order_Type__c = 'Overage';
        update reverseOrderRequest;
        CCM_ReverseOrderInfoCtl.updateReverseOrderOPS(reverseOrderRequest.Id, 'OPS Not Confirmed', 'yyy');
        reverseOrderRequest.Reverse_Order_Type__c = 'Shortage';
        update reverseOrderRequest;
        CCM_ReverseOrderInfoCtl.updateReverseOrderOPS(reverseOrderRequest.Id, 'OPS Not Confirmed', 'yyy');
        Test.stopTest();
    }

    @IsTest
    static void testGetReverseOrderInfo1() {
        Test.startTest();
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id FROM Reverse_Order_Request__c LIMIT 1];
        reverseOrderRequest.Is_Alternative_Address__c = true;
        update reverseOrderRequest;
        CCM_ReverseOrderInfoCtl.getReverseOrderInfo(reverseOrderRequest.Id);
        Test.stopTest();
    }

    @IsTest
    static void testGetReverseOrderInfo2() {
        Test.startTest();
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id FROM Reverse_Order_Request__c LIMIT 1];
        reverseOrderRequest.Is_Alternative_Address__c = false;
        update reverseOrderRequest;
        CCM_ReverseOrderInfoCtl.getReverseOrderInfo(reverseOrderRequest.Id);
        Test.stopTest();
    }

    @IsTest
    static void testSubmitForApproval() {

        Test.startTest();
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id FROM Reverse_Order_Request__c LIMIT 1];
        reverseOrderRequest.External_Return_Reason__c = CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_WITH_NO_REASON;
        CCM_ReverseOrderInfoCtl.submitForApproval(reverseOrderRequest.Id);
        update reverseOrderRequest;
        CCM_ReverseOrderInfoCtl.submitForApproval(reverseOrderRequest.Id);
        CCM_ReverseOrderInfoCtl.recallForApproval(reverseOrderRequest.Id);
        CCM_ReverseOrderInfoCtl.recallForApproval('343234yyy');
        Test.stopTest();
    }

    @IsTest
    static void testSendNotificationAndEmail() {

        Test.startTest();
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id FROM Reverse_Order_Request__c LIMIT 1];
        CCM_ReverseOrderInfoCtl.sendNotificationAndEmail(reverseOrderRequest.Id, '<EMAIL>', 'yyy', 'yyy', UserInfo.getUserId(), 'yyy', 'yyy');
        Test.stopTest();
    }

    @IsTest
    static void testGetAllReverseOrderByCustomer() {

        Test.startTest();
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id,Reverse_Order_Request_Number__c,Reverse_Order_Type__c,
                Return_Goods_Status__c,Credit_Memo_Status__c,Reverse_Order_Request_Status__c FROM Reverse_Order_Request__c LIMIT 1];
        Account account = [SELECT Id,Name,AccountNumber FROM Account LIMIT 1];
        reverseOrderRequest.Reverse_Order_Request_Number__c = 'COC-11111';
        reverseOrderRequest.Reverse_Order_Type__c = 'Overage';
        reverseOrderRequest.Return_Goods_Status__c = 'Received';
        reverseOrderRequest.Credit_Memo_Status__c = 'Issued';
        reverseOrderRequest.Reverse_Order_Request_Status__c = 'Approved';
        update reverseOrderRequest;
        CCM_ReverseOrderInfoCtl.getAllReverseOrderByCustomer('', '', '', '', account.Name, account.AccountNumber, 'Overage',
                'Received','Issued', 'Approved', '', '', 0, 0);
        Test.stopTest();
    }

    @IsTest
    static void testDeleteItems() {

        Test.startTest();
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id FROM Reverse_Order_Request__c LIMIT 1];
        List<ContentDocument> documents = [
                SELECT Id, Title, LatestPublishedVersionId
                FROM ContentDocument
        ];
        List<String> deleteItemList = new List<String>();
        deleteItemList.add(reverseOrderRequest.Id);
        CCM_ReverseOrderInfoCtl.deleteItems(deleteItemList);
        CCM_ReverseOrderInfoCtl.deleteItems(null);
        Test.stopTest();
    }
    @IsTest
    static void testDeleteContentDocument() {

        Test.startTest();
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id FROM Reverse_Order_Request__c LIMIT 1];
        List<ContentDocument> documents = [
                SELECT Id, Title, LatestPublishedVersionId
                FROM ContentDocument
        ];
        CCM_ReverseOrderInfoCtl.deleteContentDocument(documents[0].Id);
        CCM_ReverseOrderInfoCtl.deleteReverseOrder(reverseOrderRequest.Id);
        CCM_ReverseOrderInfoCtl.deleteReverseOrder('111yyy');
        Test.stopTest();
    }

    @isTest
    static void testupdateReverseOrderDeliveryNumber() {
        Test.startTest();
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id FROM Reverse_Order_Request__c LIMIT 1];
        CCM_ReverseOrderInfoCtl.updateReverseOrderDeliveryNumber(reverseOrderRequest.Id, '12345');
        Test.stopTest();
    }

    @isTest
    static void testupdateReverseOrderTasks() {
        Test.startTest();
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id FROM Reverse_Order_Request__c LIMIT 1];
        CCM_ReverseOrderInfoCtl.updateReverseOrderTasks(reverseOrderRequest.Id, 'test', 'buy');
        CCM_ReverseOrderInfoCtl.updateReverseOrderTasks(reverseOrderRequest.Id, 'test', 'return');
        Test.stopTest();
    }

/*
    @isTest
    static void testgenerateAddressWrapper() {
        Test.startTest();
        CCM_ReverseOrderInfoCtl.generateAddressWrapper(false, [SELECT Id FROM Account LIMIT 1].Id, new List<String>{CCM_Constants.ADDRESS_BILLING_ADDRESS_RECORD_TYPE_ID, CCM_Constants.ADDRESS_DROPSHIP_BILLING_ADDRESS_RECORD_TYPE_ID, CCM_Constants.ADDRESS_DROPSHIP_SHIPPING_ADDRESS_RECORD_TYPE_ID, CCM_Constants.ADDRESS_SHIPPING_ADDRESS_RECORD_TYPE_ID});
        Test.stopTest();
    }
    */

    @isTest
    static void testgetInvoice() {
        Test.startTest();
        List<Order> orderList = [SELECT Id FROM Order];
        CCM_ReverseOrderInfoCtl.getInvoice(orderList[0].Id, '');
        Test.stopTest();
    }

    @isTest
    static void testgetShipmentItems() {
        Test.startTest();
        List<Shipment__c> shipList = [SELECT Id FROM Shipment__c];
        List<Order> orderList = [SELECT Id FROM Order];
        CCM_ReverseOrderInfoCtl.getShipmentItems(shipList[0].id, orderList[0].Id, '');
        Test.stopTest();
    }

    @isTest
    static void testgetProductCodeByOrderItemId() {
        Test.startTest();
        List<Order_Item__c> itemList = [SELECT Id, Order__c FROM Order_Item__c];
        CCM_ReverseOrderUtil.getProductCodeByOrderItemId(itemList[0].Id);
        List<Reverse_Order_Item__c> rItemList = [SELECT Id FROM Reverse_Order_Item__c];
        CCM_ReverseOrderUtil.deleteReverseOrderItems(rItemList);
        CCM_ReverseOrderUtil.getInvoices(itemList[0].Order__c);
        Test.stopTest();
    }

    @isTest
    static void testgetShipmentDays() {
        Test.startTest();
        List<Shipment__c> shipList = [SELECT Id,Ship_OracleID__c FROM Shipment__c];
        CCM_ReverseOrderInfoCtl.getShipmentDays(shipList[0].Ship_OracleID__c);
        Test.stopTest();
    }

    @isTest
    static void testgetInternalPriceBook() {
        Test.startTest();
        List<Reverse_Order_Request__c> req = [SELECT Id, Customer__c FROM Reverse_Order_Request__c WHERE Customer__r.Name = 'Test' LIMIT 1];
        List<PricebookEntry> pbe = [SELECT Id, Product2Id FROM PricebookEntry];
        CCM_ReverseOrderInfoCtl.getInternalPriceBook(pbe[0].Product2Id, req[0].Customer__c);
        Test.stopTest();

    }

    @isTest
    static void testgetDropShipInternalPriceBook() {
        Test.startTest();
        List<Reverse_Order_Request__c> req = [SELECT Id, Customer__c FROM Reverse_Order_Request__c WHERE Customer__r.Name = 'Test' LIMIT 1];
        List<PricebookEntry> pbe = [SELECT Id, Product2Id FROM PricebookEntry];
        CCM_ReverseOrderInfoCtl.getDropShipInternalPriceBook(pbe[0].Product2Id, req[0].Customer__c);
        Test.stopTest();
    }

    @IsTest
    static void testgetAllImage() {
        List<String> itemIds = new List<String>();
        for(Reverse_Order_Request__c roq : [select Id from Reverse_Order_Request__c]) {
            itemIds.add(roq.Id);
        }
        CCM_ReverseOrderInfoCtl.getAllImage(itemIds);
    }
}