({
    getQueryVariable: function (variable) {
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i=0;i<vars.length;i++) {
                var pair = vars[i].split("=");
                if(pair[0] == variable || pair[0] == ('0.' + variable)){
                    return pair[1];
                }
        }
        return(false);
    },
    getCustomerFreightTerm: function (component) {
        var shippingByOption;
        var labelValue = $A.get("$Label.c.CCM_Freight_Term_List");
        var freightTermList = labelValue.split(";");
        let billAddressId = component.get('v.quotation.Billing_Address__c');
        let customerId = component.get('v.customerId');
        let brand = component.get('v.brandScope');
        let action = component.get('c.getCustomerFreightTerm');
        action.setParams({
            'addressId': billAddressId,
            'customerId': customerId,
            'brandName': brand
        });
        action.setCallback(this, function(response){
            let state = response.getState();
            if (state === 'SUCCESS') {
                let freightTerm = response.getReturnValue();
                if (!component.get('v.isCCA')) {

                    if (freightTerm == 'COLLECT') {
                        shippingByOption = [{ 'label': 'Collect', 'value': 'Customer' }];
                        component.set("v.isCollect", true);
                        component.set("v.isThirdPartyBilling", false);
                        component.set("v.isShowShippingBy", true);
                        component.set("v.shippingByOptions", shippingByOption);
                    } else if (freightTerm == 'THIRD_PARTY') {
                        shippingByOption = [{ 'label': 'Third Party Billing', 'value': 'Third Party Billing' }];
                        component.set("v.isCollect", false);
                        component.set("v.isShowShippingBy", true);
                        component.set("v.isThirdPartyBilling", true);
                        component.set("v.shippingByOptions", shippingByOption);
                    } else if (freightTermList.indexOf(freightTerm) > -1) {
                        shippingByOption = [{ 'label': 'Prepaid', 'value': 'Chervon' }, { 'label': 'Third Party Billing', 'value': 'Third Party Billing' }];
                        component.set("v.isCollect", false);
                        component.set("v.isShowShippingBy", true);
                        component.set("v.isThirdPartyBilling", false);
                        component.set("v.shippingByOptions", shippingByOption);
                    } else {
                        shippingByOption = [];
                        component.set("v.isCollect", false);
                        component.set("v.isThirdPartyBilling", false);
                        component.set("v.isShowShippingBy", true);
                        component.set("v.shippingByOptions", shippingByOption);
                    }
                    component.set('v.quotation.Shipping_By__c', component.get('v.shippingBy'));
                } else {
                    if (freightTermList.indexOf(freightTerm) > -1) {
                        component.set("v.quotation.Shipping_By__c", 'Chervon');
                        component.set("v.isDisableShippingBy", true);
                    } else if (freightTerm == 'COLLECT') {
                        component.set("v.quotation.Shipping_By__c", 'Customer');
                        component.set("v.isDisableShippingBy", true);
                    } else {
                        component.set("v.isDisableShippingBy", false);
                    }
                }
            }
        });
        $A.enqueueAction(action);
    },

    saveDataFun: function(component, currentStep, isSave){
        var isCAA = component.get('v.isCCA');
        if(isCAA){
            var selectedAddress = component.get('v.selectedShippingAddressItem');
            if(selectedAddress
                && selectedAddress.Country__c != 'CA'
                && component.get('v.quotation.Shipping_Address_Name__c') != undefined){
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                    "message": $A.get("$Label.c.CCM_Portal_PleaseselectCanadaaddressinshippingaddress"),
                    "type": "Error",
                    "mode": "pester",
                    "duration": "500"
                });
                toastEvent.fire();
                return;
            }
        }

        component.set('v.isBusy', true);
        var quotationD = component.get('v.quotation');
        if (isSave == false){
            var result = Validator.pass(component.find("required-Field"));
            if(!result){
                component.set('v.isBusy', false);
                return;
            }

            quotationD.Is_Alternative_Address__c = component.get('v.isAlternativeAddress');
            if (quotationD.Is_Alternative_Address__c == false){
                component.set('v.requireShipAddrFlag', true);
                var result1 = Validator.pass(component.find("required-shipping"));
                if(!result1){
                    component.set('v.isBusy', false);
                    return;
                }
            }
            //Check Email Address: Email Format (eg. <EMAIL>)
            var regExpEmailformat = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            var emailField = component.find("txtEmail");
            var emailFieldValue = emailField.get("v.value");
            if(!$A.util.isEmpty(emailFieldValue)){
                if(emailFieldValue.match(regExpEmailformat)){
                    emailField.set("v.errors", [{message: null}]);
                    $A.util.removeClass(emailField, 'slds-has-error');
                }else{
                    $A.util.addClass(emailField, 'slds-has-error');
                    emailField.set("v.errors", [{message: $A.get("$Label.c.CCM_Portal_PleaseEnteraValidEmailAddress")}]);
                }
            }
        }

        quotationD.Freight_Fee__c = component.get('v.actualShippingFee');
        quotationD.Freight_Fee_Waived__c = component.get('v.freeShipping');
        quotationD.Handling_Fee__c = component.get('v.handlingFee');
        var isDisplaySection = component.get('v.isDisplaySection');
        if(isDisplaySection){
            if(component.get('v.quotation.ShipTo_Name__c') && component.get('v.quotation.ShipTo_Name__c').length > 30){
                component.set('v.isBusy', false);
                return;
            }
            if(component.get('v.quotation.State__c') && component.get('v.showStateMessage')){
                component.set('v.isBusy', false);
                return;
            }
            if(component.get('v.quotation.Address_Line1__c') && component.get('v.quotation.Address_Line1__c').length > 30){
                component.set('v.isBusy', false);
                return;
            }
            if(component.get('v.quotation.Zip_Code__c') && component.get('v.quotation.Zip_Code__c').length > 30){
                component.set('v.isBusy', false);
                return;
            }
            if(component.get('v.quotation.Address_Line2__c') && component.get('v.quotation.Address_Line2__c').length > 30){
                component.set('v.isBusy', false);
                return;
            }
            if(component.get('v.quotation.City__c') && component.get('v.quotation.City__c').length > 30){
                component.set('v.isBusy', false);
                return;
            }
            if(component.get('v.quotation.Phone_Number__c') && component.get('v.quotation.Phone_Number__c').length > 30){
                component.set('v.isBusy', false);
                return;
            }
            quotationD.Order_Type__c ='CNA Export Order';
        }else{
            quotationD.ShipTo_Name__c = null;
            quotationD.State__c = null;
            quotationD.Address_Line1__c = null;
            quotationD.Zip_Code__c = null;
            quotationD.Address_Line2__c = null;
            quotationD.City__c = null;
            quotationD.Phone_Number__c = null;
        }


        // var orderTypeVal = component.get('v.orderTypeVal');
        // if (orderTypeVal == 'CNA Dropship Order'){
        //     quotationD.Order_Type__c = 'CNA Dropship Order';
        // }else{
        //     quotationD.Order_Type__c = 'CNA Sales Order - USD';
        // }

        if (component.get('v.isAlternativeAddress') == true){
            quotationD.Shipping_Address__c = null;
        }else{
            quotationD.Additional_Shipping_Street__c = null;
            quotationD.Additional_Shipping_City__c = null;
            quotationD.Additional_Shipping_Country__c = null;
            quotationD.Additional_Shipping_Province__c = null;
            quotationD.Additional_Shipping_Postal_Code__c = null;
            quotationD.Additional_Contact_Name__c = null;
            quotationD.Additional_Contact_Phone__c = null;
            quotationD.Additional_Contact_Email__c = null;
            //给Sales Agency赋值
            quotationD.Sales_Agency__c = component.get('v.salesAgencyId');
        }
        var action = component.get("c.saveData");
        action.setParam('poString', JSON.stringify(quotationD));
        action.setParam('poItemString', null);
        action.setParam('currentStep', currentStep);
        action.setParam('orderTypeVal', component.get('v.orderTypeVal'));
        action.setCallback(this, function (response, component) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if(results){
                    component.set('v.quotation', results.po);
                    component.set('v.recordId', results.po.Id);
                    component.set('v.actualShippingFee', results.po.Freight_Fee__c);
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": $A.get("$Label.c.CCM_Portal_Success"),
                        "message": $A.get("$Label.c.CCM_Portal_Saved"),
                        "type": "success"
                    });
                    toastEvent.fire();
                    if(results.currentStep){
                        component.set("v.currentStep", results.currentStep);
                    }
                }
            } else {
                component.set('v.isBusy', false);
                var errors = response.getError();
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Warning"),
                    "message": errors[0].message,
                    "type": "Warning"
                });
                toastEvent.fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    generateCondtion : function(component, brandName) {
        var brands= [];
        brands = brandName.split('&');

        var defaultCondition = '[{"Value":"Published","FieldName":"Status__c","Condtion":"="}]';

        /*var defaultCondition = '[{"Value":"Published","FieldName":"Status__c","Condtion":"="},{"Value":"(\'{1}\')","FieldName":"Brands__c","Condtion":"includes"}]';

        defaultCondition= defaultCondition.replace('{1}', brands.join('\',\''));
        console.log('defaultCondition promotion--->'+defaultCondition);
*/
        component.set('v.promotionCondition', defaultCondition);
    },
    doChangeShippingAddressHandler: function(component, event, helper){

        component.set('v.isBusy', true);
        var isCCA = component.get('v.isCCA');
        var zeroNum = 0.00;
        var productPriceAmt = Number(component.get('v.productAmount'));
        var freightFeeRuleAmt = Number(component.get('v.freightTermRuleFee'));
        if (component.get('v.isAlternativeAddress') == false){
            if(!isCCA){
                component.set('v.handlingFee', Number(zeroNum).toFixed(2));
            }
        }else{
            if (productPriceAmt >= freightFeeRuleAmt){
                component.set('v.handlingFee', Number(zeroNum).toFixed(2));
            }else{
                if(!isCCA){
                    var handlingFeeFactor = $A.get("$Label.c.CCM_Handling_Fee_Charge_Percent");
                    var handlingFeeVal = productPriceAmt * Number(handlingFeeFactor) / 100;
                    component.set('v.handlingFee', handlingFeeVal.toFixed(2));
                }
            }
        }
        var currentUrl = window.location.href;
        var isSite = false;
        if(currentUrl.includes('https://chervon--newuat.sandbox.my.site.com') || currentUrl.includes('https://chervon.my.site.com')){
            isSite = true;
        }
        var addressCountry = component.get('v.quotation.Additional_Shipping_Country__c');
        if (addressCountry!= null && !isSite) {
            if(addressCountry != 'US' && !isCCA){
                component.set('v.isDisplaySection', true);
            }else{
                component.set('v.isDisplaySection', false);
            }
        } else {
            this.selectAddressCountry(component);
        }
        var action = component.get("c.getFreihtFeeAmount");
        action.setParam('recordId', component.get('v.recordId'));
        action.setParam('quotation', JSON.stringify(component.get('v.quotation')));
        action.setParam('isAlternativeAddress', component.get('v.isAlternativeAddress'));
        action.setParam('freightTermRuleFee', component.get('v.freightTermRuleFee'));
        action.setParam('productAmount', productPriceAmt);
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log('state test--->'+state);
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if(results){
                    component.set('v.salesAgencyAlias', results.salesAgencyAlias);
                    component.set('v.salesAgencyId', results.salesAgencyId);
                    console.log('sales agency alias--->'+ results.salesAgencyAlias);
                    component.set('v.actualShippingFee', Number(results.freightFeeAmt).toFixed(2));
                    console.log('actualShippingFee--->'+ Number(results.freightFeeAmt).toFixed(2));

                    if (productPriceAmt >= freightFeeRuleAmt){
                        component.set('v.freeShipping', Number(results.freightFeeAmt).toFixed(2));
                    }else{
                        component.set('v.freeShipping', Number(zeroNum).toFixed(2));
                    }
                    if (results.isFreeFreightFee) {
                        component.set('v.freeShipping', Number(zeroNum).toFixed(2));
                    }
                }
            } else {
                var errors = response.getError();
                console.log('errors');
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    selectAddressCountry: function(component){
        var action = component.get("c.getCountry");
        var currentUrl = window.location.href;
        var isSite = false;
        if(currentUrl.includes('https://chervon--newuat.sandbox.my.site.com') || currentUrl.includes('https://chervon.my.site.com')){
            isSite = true;
        }
        action.setParam('addressId', component.get('v.quotation.Shipping_Address__c'));
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                var org = component.get('v.isCCA');
                if(results && !isSite){
                    var addressCountry = results;
                    if (addressCountry != 'US' && !org) {
                        component.set('v.isDisplaySection', true);
                    } else {
                        component.set('v.isDisplaySection', false);
                    }
                }
            } else {
                var errors = response.getError();
                console.log('errors');
            }
        });
        $A.enqueueAction(action);
    },
    checkHasOrder: function(component) {
        let addressId = component.get('v.quotation.Shipping_Address__c')
        if(addressId) {
            let action = component.get('c.checkAvailablePaymentTermPromo');
            let customerId = component.get('v.quotation').Customer__c;
            let isDropship = component.get('v.quotation').Is_DropShip__c;
            let paymentTermPromotionCode = component.get('v.quotation').Payment_Term_Promotion_Code__c;
            action.setParams({
                'addressId': addressId,
                'customerId': customerId,
                'isDropShip': isDropship,
                'paymentTermPromoCode': paymentTermPromotionCode
            });
            action.setCallback(this, function (response) {
                let state = response.getState();
                if (state === "SUCCESS") {
                    let results = response.getReturnValue();
                    if(results) {
                        component.set('v.needCheckOrder', false);
                        var toastEvent = $A.get("e.force:showToast");
                        toastEvent.setParams({
                            "type": $A.get("$Label.c.CCM_Portal_Success"),
                            "title": $A.get("$Label.c.CCM_Portal_PaymentTermPromoAvailable"),
                            "message": $A.get("$Label.c.CCM_Portal_PaymentPromoTips1") + ': ' + results + ' ' + $A.get("$Label.c.CCM_Portal_PaymentPromoTips2"),
                            "duration": 10000
                        });
                        toastEvent.fire();
                    }
                } else {
                    let errors = response.getError();
                    console.log('errors',errors);
                }
            });
            $A.enqueueAction(action);
        }
    }
})