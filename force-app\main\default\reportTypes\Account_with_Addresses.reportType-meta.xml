<?xml version="1.0" encoding="UTF-8"?>
<ReportType xmlns="http://soap.sforce.com/2006/04/metadata">
    <baseObject>Account</baseObject>
    <category>other</category>
    <deployed>true</deployed>
    <description>Account with Addresses</description>
    <join>
        <join>
            <outerJoin>true</outerJoin>
            <relationship>Addresses_With_Program__r</relationship>
        </join>
        <outerJoin>false</outerJoin>
        <relationship>Account_Address__r</relationship>
    </join>
    <label>Account with Addresses</label>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>true</checkedByDefault>
            <field>Name</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Salutation</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FirstName</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastName</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TextName</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Type</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RecordType</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Parent</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BillingAddress</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BillingStreet</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BillingCity</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BillingState</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BillingPostalCode</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BillingCountry</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BillingLatitude</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BillingLongitude</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BillingGeocodeAccuracy</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ShippingAddress</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ShippingStreet</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ShippingCity</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ShippingState</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ShippingPostalCode</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ShippingCountry</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ShippingLatitude</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ShippingLongitude</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ShippingGeocodeAccuracy</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Phone</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Fax</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AccountNumber</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Website</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sic</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Industry</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AnnualRevenue</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>NumberOfEmployees</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Ownership</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TickerSymbol</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Description</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Rating</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Site</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CurrencyIsoCode</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Owner</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastActivityDate</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>PersonContact</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Jigsaw</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AccountSource</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SicDesc</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Shipping_Address2__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Billing_Address2__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Shipping_Address3__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Billing_Address3__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ADRSCODE__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Accepts_Marketing__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Address_2__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Address_3__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AutoCreated__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BYO__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CUSTCLAS__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CUSTNMBR__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>City__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Company__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contact_Name__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Create_Shopify_Account__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Date_entered_into_Dealer_Locator__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Dealer_Distributor__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Dealer_Name__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Dealer_Number__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Division__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Email_List_Sign_Up__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Email__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FAX_SQL_c__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Get_Satisfaction_User_Dashboard_URL_Man__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Image_URL__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Is_Person_Account__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Last_Order_ID__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Last_Order_Name__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Last_Updated__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>MC_Subscriber__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Market_Name__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Market__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>MarketingOptIn__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Multipass_Identifier__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Other_Company__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>PHONE1_SQL__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Password__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Phone_2__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Phone_3__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Problem_Customer__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Region__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Region_number__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Registration_Date__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SHIPMTHD_SQL__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SHIPMTHD__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SHRTNAME__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Shop_Labor_Rate__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Shopify_ID__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>State_1__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>State__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Store_Name__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Store_number__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Street_Address__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Tags__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Total_Spent__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Username__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Verified_Email__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Web_URL__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ZIP_Code__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>of_Acitions__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Onlykey1__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Num_of_Warrantites__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Onlykey2__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Onlykey3__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>of_Kits__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Duplicate__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>numberOfHour__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>interval_of_Registration__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FirstNameTemp__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastNameTemp__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SalutationTemp__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Registered_Product_Categories__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Registered_Product_Code__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Record_Type_Name__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EighteenId__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Lawn_Size__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>send_marketing_emails__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Brand_Name__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SentWelcomeEmail__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Brand_Influencer__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EGO_password__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EGO_username__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Product_Type__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Site_Origin__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SkilSaw_password__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SkilSaw_username__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Skil_password__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Skil_username__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>of_Warrantites_Skil__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>of_Warrantites_EGO__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>of_Warrantites_SkilSaw__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>of_Warrantites_Hammerhead__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Customer_Name__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EGOLastModifiedDate__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SkilLastModifiedDate__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SkilSawLastModifiedDate__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SkilsawNumberOfHour__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>egoNumberOfHour__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>skilNumberOfHour__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Affiliate_To_Buying_Marketing_Group__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Annual_Revenue_Code__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Approval_Comments__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Approval_Date__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Approval_Status__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Approval_Submit_Time__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Approved_By__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Buying_Group__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Cerdit__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Credit_Limit__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Current_Points_Plus_Status__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Cust_Header_ID__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Customer_Class__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Customer_Cluster__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Customer_Oracle_ID__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Customer_Organization__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Customer_SF_ID__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Customer_Sub_Cluster__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Deliver_From__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Director_Approver__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Distributor_or_Dealer__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Intended_Brand__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Invoicing_Method__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>IsLeadMapToAccount__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Is_Buying_Group_Marking_Group__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Min_Order_Value_for_Prepaid__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Online_Selling__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Operation_Time_From__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Operation_Time_To__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Order_Lead_Time__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Order_Transmission__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Payment_Lead_Time__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Phone_Ext__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Price_List__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Risk_Code__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SBA_Effective_Date__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SBA_Expiry_Date__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SBA_Ref_Nr__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SBA_Signed__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sales_Brands__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sales_Channel__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sales_Group__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sales_Rep__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Service_Brands__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Ship_Complete__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Shipment_Priority__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Status__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Store_Counts__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Store_Covered_Region__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Store_Size__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Vendor_Number__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Is_Sales_Track_Customer__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Founded_Date__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Is_Key_Account__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Prior_Points_Plus_Status__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Joined_Date__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Customer_Owner__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Owner__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ReceiveInfoValue__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>VisitWebsiteValue__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SentEGOWelcomeEmail__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SentSKILWelcomeEmail__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SentSkilSawWelcomeEmail__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Eligible_For_Fleet__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Organization_Name__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin_From__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Trade__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FLEX_password__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FLEX_username__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Flex_Amount__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>is_flex_rule__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SentFLEXWelcomeEmail__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Trade_Other__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sales_Agencies__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sales_Managers__c</field>
            <table>Account</table>
        </columns>
        <masterLabel>Accounts</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Owner</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>true</checkedByDefault>
            <field>Name</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CurrencyIsoCode</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RecordType</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Active__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Address1__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Address2__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Approval_Comments__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Approval_Status__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Approval_Submit_Time__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Approved_Date__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>City__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contact2__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contact3__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contact4__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contact5__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contact__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Country__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Email_for_Invoicing__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Invoicing_Method__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ORG_ID__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Postal_Code__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Primary__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Prospect__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sales_Director__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sales_Group__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sales_Rep__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Search_Layout_Field__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Ship_VIA__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>State__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Store_Number__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Dropship_Contact2__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Dropship_Contact3__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Dropship_Contact4__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Dropship_Contact5__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Dropship_Contact__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Help_Text__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RecordType_Name__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sales_Agency_Contact__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sales_Agency__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EDI__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Account_Number__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>UpdateFlag__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>pushFlag__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Address_18_bit_OrcId__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>X2nd_Tier_Dealer__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Is_2nd_Tier_Dealer__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EGO_Agency__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FLEX_Agency__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SKIL_SKILSAW_Agency__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sales_Agencies__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sales_Managers__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Account_Owner_Role__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Account_Sales_Group__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contact__c.Email</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contact__c.Phone</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contact__c.FirstName</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contact__c.LastName</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contact__c.MobilePhone</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contact__c.Title</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contact__c.Functional_Role__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <masterLabel>Address (Customer)</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Customer_Line_Oracle_ID__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contact_Oracle_ID__c</field>
            <table>Account.Account_Address__r</table>
        </columns>
        <masterLabel>Address</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>has_customer_profile__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Reverse_Order_Count__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SKU__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>No__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TaxID__c</field>
            <table>Account</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>PaymentMethod__c</field>
            <table>Account</table>
        </columns>
        <masterLabel>Customers</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Program__c.RecordType</field>
            <table>Account.Account_Address__r.Addresses_With_Program__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Program__c.RecordType.DeveloperName</field>
            <table>Account.Account_Address__r.Addresses_With_Program__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Program__c.Brands__c</field>
            <table>Account.Account_Address__r.Addresses_With_Program__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Program__c.Labor_Rate__c</field>
            <table>Account.Account_Address__r.Addresses_With_Program__r</table>
        </columns>
        <masterLabel>Billing Addresses With Authorized Brand</masterLabel>
    </sections>
</ReportType>
