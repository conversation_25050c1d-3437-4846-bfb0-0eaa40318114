({
    doinit: function (component, event, helper) {
        var selectedTabId = helper.getUrlParameter("selectedTab");
        if (selectedTabId) {
            component.set("v.tabId", selectedTabId);
        }
        // helper.initExportTypeList(component);
        component.set("v.currencySymbol", $A.get("$Locale.currencyCode"));
        // add haibo: french
        let invoiceDateRangeOption = [
            {'label': $A.get("$Label.c.CCM_Portal_Last30Days"), 'value': 'Last 30 Days'},
            {'label': $A.get("$Label.c.CCM_Portal_Last90Days"), 'value': 'Last 90 Days'},
            {'label': $A.get("$Label.c.CCM_Portal_Last1Year"), 'value': 'Last 1 Year'}
        ];
        let overdueOption = [
            {'label': $A.get("$Label.c.CCM_Portal_YES"), 'value': 'YES'},
            {'label': $A.get("$Label.c.CCM_Portal_NO"), 'value': 'NO'}
        ];
        component.set('v.invoiceDateRangeOption', invoiceDateRangeOption);
        component.set('v.overdueOption', overdueOption);
        var columns = [
            { label: $A.get("$Label.c.CCM_Portal_MemoNumber"), fieldName: "invoiceNumber" },
            { label: $A.get("$Label.c.CCM_Portal_CustomerPONumber"), fieldName: "poNumber" },
            {
                label: $A.get("$Label.c.CCM_Portal_OrderNumber"),
                fieldName: "orderNumber",
                children: [
                    {
                        type: "lightning:formattedUrl",
                        attributes: {
                            value: "${orderURL}",
                            label: "${orderNumber}",
                            target: "_blank",
                            class: "orderLinkStyle"
                        }
                    }
                ]
            },
            { label: $A.get("$Label.c.CCM_Portal_InvoiceCreatedDate"), fieldName: "invoiceDate" },
            /*{label: 'Gl Date', fieldName: 'glDate'},*/
            { label: $A.get("$Label.c.CCM_Portal_DueDate"), fieldName: "dueDate" },
            {
                label: $A.get("$Label.c.CCM_Portal_InvoiceAmount"),
                fieldName: "originalAmountStr",
                type: "lightning:formattedNumber",
                typeAttributes: {
                    currencyCode: "USD",
                    maximumSignificantDigits: 5
                }
            },
            {
                label: $A.get("$Label.c.CCM_Portal_PaidAmount"),
                fieldName: "paidAmountStr",
                type: "lightning:formattedNumber",
                typeAttributes: {
                    currencyCode: "USD",
                    maximumSignificantDigits: 5
                }
            },
            {
                label: $A.get("$Label.c.CCM_Portal_OutstandingAmount"),
                fieldName: "dueRemainingAmountStr",
                type: "lightning:formattedNumber",
                typeAttributes: {
                    currencyCode: "USD",
                    maximumSignificantDigits: 5
                }
            },
            // {label: 'Overdue', fieldName: 'isOverdue', class: "displayStyle"},
            {
                label: $A.get("$Label.c.CCM_Portal_Overdue"),
                type: "lightning:formattedText",
                attributes: {
                    value: "${isOverdue}",
                    class: "${displayStyle}"
                }
            },
            { label: $A.get("$Label.c.CCM_Portal_OverdueDays"), fieldName: "overdueDays" }
        ];
        component.set("v.columns", columns);

        var columns1 = [
            { label: $A.get("$Label.c.CCM_Portal_InvoiceNumber"), fieldName: "invoiceNumber" },
            { label: $A.get("$Label.c.CCM_Portal_CustomerPONumber"), fieldName: "poNumber" },
            { label:$A.get("$Label.c.CCM_Portal_InvoiceDate"), fieldName: "invoiceDate" },
            {
                label: $A.get("$Label.c.CCM_Portal_DueAmount"),
                fieldName: "paidAmountStr",
                type: "lightning:formattedNumber",
                typeAttributes: {
                    currencyCode: "USD",
                    maximumSignificantDigits: 5
                }
            },
            {
                label: $A.get("$Label.c.CCM_Portal_RemainingAmount"),
                fieldName: "dueRemainingAmountStr",
                type: "lightning:formattedNumber",
                typeAttributes: {
                    currencyCode: "USD",
                    maximumSignificantDigits: 5
                }
            }
        ];
        component.set("v.columns1", columns1);

        if (component.get("v.currencySymbol") == 'CAD') {
            var columns3 = [
                {
                    label: $A.get("$Label.c.CCM_Portal_Action"),
                    width: "50px",
                    children: [
                        {
                            type: "lightning:buttonIcon",
                            attributes: {
                                value: "${invoiceAndCreditList}",
                                variant: "bare",
                                iconName: "utility:preview",
                                alternativeText: "View",
                                onclick: component.getReference("c.doView")
                            }
                        }
                    ]
                },
                // { label: "Transaction ID", fieldName: "transactionId" },
                { label: $A.get("$Label.c.CCM_Portal_PaymentID"), fieldName: "id" },
                { label: $A.get("$Label.c.CCM_Portal_PaidBy"), fieldName: "createdById" },
                { label: $A.get("$Label.c.CCM_Portal_PaymentStatus"), fieldName: "paypalStatus" },
                { label: $A.get("$Label.c.CCM_Portal_TransactionStatus"), fieldName: "transactionStatus" },
                { label: $A.get("$Label.c.CCM_Portal_TransactionDate"), fieldName: "transactionDate" },
                { label: $A.get("$Label.c.CCM_Portal_PaymentMethod"), fieldName: "paymentMethod" },
                { label: $A.get("$Label.c.CCM_Portal_CardLastFourDigits"), fieldName: "cardNumber" },
                {
                    label: $A.get("$Label.c.CCM_Portal_PaymentAmount"),
                    children: [
                        {
                            type: "lightning:formattedNumber",
                            attributes: {
                                value: "${paidAmout}",
                                currencyCode: "CAD",
                                currencyDisplayAs: "code",
                                style: "currency"
                                // minimumFractionDigits:2
                            }
                        }
                    ]
                },
                { label: $A.get("$Label.c.CCM_Portal_Remark"), fieldName: "remark" }
            ];
        } else {
            var columns3 = [
                {
                    label: $A.get("$Label.c.CCM_Portal_Action"),
                    width: "50px",
                    children: [
                        {
                            type: "lightning:buttonIcon",
                            attributes: {
                                value: "${invoiceAndCreditList}",
                                variant: "bare",
                                iconName: "utility:preview",
                                alternativeText: "View",
                                onclick: component.getReference("c.doView")
                            }
                        }
                    ]
                },
                // { label: "Transaction ID", fieldName: "transactionId" },
                { label: $A.get("$Label.c.CCM_Portal_PaymentID"), fieldName: "id" },
                { label: $A.get("$Label.c.CCM_Portal_PaidBy"), fieldName: "createdById" },
                { label: $A.get("$Label.c.CCM_Portal_PaymentStatus"), fieldName: "paypalStatus" },
                { label: $A.get("$Label.c.CCM_Portal_TransactionStatus"), fieldName: "transactionStatus" },
                { label: $A.get("$Label.c.CCM_Portal_TransactionDate"), fieldName: "transactionDate" },
                { label: $A.get("$Label.c.CCM_Portal_PaymentMethod"), fieldName: "paymentMethod" },
                { label: $A.get("$Label.c.CCM_Portal_CardLastFourDigits"), fieldName: "cardNumber" },
                {
                    label: $A.get("$Label.c.CCM_Portal_PaymentAmount"),
                    children: [
                        {
                            type: "lightning:formattedNumber",
                            attributes: {
                                value: "${paidAmout}",
                                currencyCode: "USD",
                                currencyDisplayAs: "code",
                                style: "currency"
                                // minimumFractionDigits:2
                            }
                        }
                    ]
                },
                { label: $A.get("$Label.c.CCM_Portal_Remark"), fieldName: "remark" }
            ];
        }

        component.set("v.columns3", columns3);

        helper.getObjectRecords(component, event, helper);
    },
    doView: function (component, event, helper) {
        // console.log("===========doview===========");
        // console.log(component.get("v.currentPaymentData"));
        // console.log(event.getSource().get("v.value").subtotal);
        // console.log(event.getSource().get("v.value").invoiceList);
        // console.log(event.getSource().get("v.value").creditList);
        // console.log(event.getSource().get("v.value").paidAmout);
        // var payment = event.getSource().get("v.value");
        var subtotal = event.getSource().get("v.value").subtotal;
        var totalPay = event.getSource().get("v.value").paidAmout;
        var isFirstPay = event.getSource().get("v.value").isFirstPay;
        var invoiceList = event.getSource().get("v.value").invoiceList;
        var creditList = event.getSource().get("v.value").creditList;
        var items = [];
        if (creditList.length > 0) {
            for (var i = 0; i < creditList.length; i++) {
                var credit = creditList[i];
                var item = {
                    label:
                        credit.invoiceNumber +
                        " (-" +
                        credit.currencyType +
                        Math.abs(credit.originalAmt) +
                        ")",
                    value: credit.originalAmt,
                    id: credit.id,
                    currencyType: credit.currencyType,
                    invoiceNumber: credit.invoiceNumber,
                    invoiceType: credit.invoiceType
                };
                items.push(item);
            }
            component.set("v.isCreditSelect", true);
        } else {
            component.set("v.isCreditSelect", false);
        }
        component.set("v.isFirstPay", isFirstPay);
        component.set("v.totalPay", totalPay);
        component.set("v.subtotal", subtotal);
        component.set("v.selectCreditList", items);
        component.set("v.selectInvoicesList", invoiceList);
        component.set("v.isShowPayment", true);
    },
    pageChange: function (component, event, helper) {
        // var pageNumber = event.getParam("pageNumber");
        // component.set("v.pageNumber",pageNumber);
        // helper.getInvoiceRecords(component, event, helper);
        // event.stopPropagation();
        var pageNumber = event.getParam("pageNumber");
        // console.log("PageNumber" + pageNumber);
        if (component.get("v.tabId") == "Invoice") {
            component.set("v.pageNumber", pageNumber);
            helper.getInvoiceRecords(component, event, helper);
        }
        if (component.get("v.tabId") == "CreditMemo") {
            component.set("v.pageNumber2", pageNumber);
            helper.getCreditMemoRecords(component, event, helper);
        }
        if (component.get("v.tabId") == "TransactionHistory") {
            component.set("v.pageNumber3", pageNumber);
            helper.getPaymentRecords(component, event, helper);
        }
        event.stopPropagation();
    },
    pageCountChange: function (component, event, helper) {
        // var pageCount = event.getParam("pageCount");
        // component.set("v.pageCount",pageCount);
        // component.set("v.pageNumber", 1);
        // helper.getInvoiceRecords(component, event, helper);
        // event.stopPropagation();
        var pageCount = event.getParam("pageCount");
        // console.log("pageCount" + pageCount);
        if (component.get("v.tabId") == "Invoice") {
            component.set("v.pageCount", pageCount);
            component.set("v.pageNumber", 1);
            helper.getInvoiceRecords(component, event, helper);
        }
        if (component.get("v.tabId") == "CreditMemo") {
            component.set("v.pageCount1", pageCount);
            component.set("v.pageNumber2", 1);
            helper.getCreditMemoRecords(component, event, helper);
        }
        if (component.get("v.tabId") == "TransactionHistory") {
            component.set("v.pageCount3", pageCount);
            component.set("v.pageNumber3", 1);
            helper.getPaymentRecords(component, event, helper);
        }
        event.stopPropagation();
    },
    handleSearch: function (component, event, helper) {
        component.set("v.pageNumber", 1);
        helper.getInvoiceRecords(component, event, helper, false);
    },
    doReset: function (component, event, helper) {
        component.set("v.invoiceDateRange", "");
        component.set("v.overdueStr", "");
        component.set("v.pageNumber", 1);
        helper.getInvoiceRecords(component, event, helper, false);
    },
    handleChangeTab: function (component, event, helper) {
        var isfirstChange = component.get("v.isFirstChange");
        if (isfirstChange) {
            helper.getCreditMemoRecords(component, event, helper);
            helper.getPaymentRecords(component, event, helper);
            component.set("v.isFirstChange", false);
        }
    },
    doExport: function(component, event, helper) {
        component.set('v.showExport', true);
    },
    doCancelExport: function(component, event, helper) {
        component.set('v.showExport', false);
    },
    handleExportDebitMemo: function(component, event, helper) {
        component.set('v.exportType', 'Debit Memo');
        helper.handleExportConfirm(component);
    },
    handleExportCreditMemo: function(component, event, helper) {
        component.set('v.exportType', 'Credit Memo');
        helper.handleExportConfirm(component);
    },
    handleExportTransactionHistory: function(component, event, helper) {
        component.set('v.exportType', 'Transaction History');
        helper.handleExportConfirm(component);
    }
});