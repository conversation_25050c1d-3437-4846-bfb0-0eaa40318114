@IsTest
private class CCM_Community_FleetEditCtlTest {

    /**
     * @description 测试数据设置 - 创建测试所需的基础数据
     */
    @TestSetup
    static void setupTestData(){
        Test.startTest();
        User admin = [SELECT Id, Username, UserRoleId FROM User WHERE Profile.Name = 'System Administrator'  AND UserRoleId != NULL AND IsActive = TRUE LIMIT 1];
        Profile profilePartner = [
            SELECT Id 
            FROM Profile 
            WHERE Name = 'Partner Community Sales' 
            LIMIT 1
        ];

        User userPartner;
        System.runAs(admin){
            Account customer = new Account();
            customer.Name = 'Test Customer';
            customer.AccountNumber = '*********';
            customer.TaxID__c = 'test';
            insert customer;

            Contact con = new Contact();
            con.FirstName = 'test';
            con.LastName = 'Contact';
            con.AccountId = customer.Id;
            con.Phone = '*********0';
            con.Email = System.now().getTime() + '@test.com';
            insert con;

            userPartner = new User(
                Email = '<EMAIL>',
                ProfileId = profilePartner.Id,
                Username = '<EMAIL>',
                Alias = 'Test',
                TimeZoneSidKey = 'America/New_York',
                EmailEncodingKey = 'ISO-8859-1',
                LocaleSidKey = 'en_US',
                LanguageLocaleKey = 'en_US',
                ContactId = con.Id,
                FirstName = 'Firstname',
                LastName = 'Lastname'
            );

            insert userPartner;

            Fleet_Program_Rule__c rule = new Fleet_Program_Rule__c();
            rule.Deliver_At_Once__c = true;
            rule.Maximum_Discount_Criteria__c = 15;
            rule.Discount_Return__c = 7.5;
            rule.Year__c = String.valueOf(Date.today().year());

            insert rule;

            Fleet_Program_Target_Customer__c tc = new Fleet_Program_Target_Customer__c();
            tc.Fleet_Program_Rule__c = rule.id;
            tc.Customer__c = customer.id;
            insert tc;

            Product2 pro = new Product2();
            pro.Name = 'Test';
            pro.Brand_Name__c = 'EGO';
            pro.RecordTypeId = CCM_Constants.PRODUCT_PRODUCT_RECORD_TYPE_ID;
            pro.ProductCode = '1234567';
            pro.IsActive = true;
            pro.Source__c = 'PIM';
            pro.Country_of_Origin__c = 'United States';
            insert pro;

            Product2 pro2 = new Product2();
            pro2.Name = 'Test';
            pro2.Brand_Name__c = 'EGO';
            pro2.RecordTypeId = CCM_Constants.PRODUCT_PRODUCT_RECORD_TYPE_ID;
            pro2.ProductCode = '12345670';
            pro2.IsActive = true;
            pro2.Source__c = 'PIM';
            pro2.Country_of_Origin__c = 'United States';
            insert pro2;

            Product2 pro3 = new Product2();
            pro3.Name = 'Test';
            pro3.Brand_Name__c = 'EGO';
            pro3.RecordTypeId = CCM_Constants.PRODUCT_PRODUCT_RECORD_TYPE_ID;
            pro3.ProductCode = '12345670';
            pro3.IsActive = true;
            pro3.Source__c = 'PIM';
            pro3.Country_of_Origin__c = 'United States';
            insert pro3;
        
            Product2 pkit = new Product2();
            pkit.Name = 'Test';
            pkit.Brand_Name__c = 'EGO';
            pkit.RecordTypeId = CCM_Constants.PRODUCT_KIT_RECORD_TYPE_ID;
            pkit.ProductCode = '1234567';
            pkit.IsActive = true;
            pkit.Source__c = 'PIM';
            pkit.Country_of_Origin__c = 'United States';
            insert pkit;

            Product2 pkit2 = new Product2();
            pkit2.Name = 'Test';
            pkit2.Brand_Name__c = 'EGO';
            pkit2.RecordTypeId = CCM_Constants.PRODUCT_KIT_RECORD_TYPE_ID;
            pkit2.ProductCode = '1234567---';
            pkit2.IsActive = true;
            pkit2.Source__c = 'PIM';
            pkit2.Country_of_Origin__c = 'United States';
            insert pkit2;

            Program_Rule_Product_Relationship__c relationship = new Program_Rule_Product_Relationship__c();
            relationship.Fleet_Program_Rule__c = rule.Id;
            relationship.Kit__c = pkit.Id;
            insert relationship;

            Program_Rule_Product_Relationship__c relationship2 = new Program_Rule_Product_Relationship__c();
            relationship2.Fleet_Program_Rule__c = rule.Id;
            relationship2.Kit__c = pkit2.Id;
            insert relationship2;

            Kit_Item__c item = new Kit_Item__c();
            item.Kit__c = pkit.Id;
            item.Product__c = pro.Id;
            item.Status__c = 'A';
            item.Sequence__c = 'xxxx';
            insert item;

            Kit_Item__c item2 = new Kit_Item__c();
            item2.Kit__c = pkit.Id;
            item2.Product__c = pro2.Id;
            item2.Status__c = 'A';
            item2.Sequence__c = 'xxxx';
            insert item2;

            Kit_Item__c item3 = new Kit_Item__c();
            item3.Kit__c = pkit.Id;
            item3.Product__c = pro3.Id;
            item3.Status__c = 'A';
            item3.Sequence__c = 'xxxx';
            insert item3;

            Pricebook2 pb = new Pricebook2();
            pb.IsActive = true;
            pb.Name = 'CNA-EGO-MSRP';
            insert pb;

            PricebookEntry pbe = new PricebookEntry();
            pbe.IsActive = true;
            pbe.Product2Id = pkit2.Id;
            pbe.UnitPrice = 1000;
            pbe.Pricebook2Id = pb.Id;
            pbe.UseStandardPrice = false;
            insert pbe;
        }
        Test.stopTest();
    }
    /**
     * @description 测试车队申请初始化和保存功能 - 正常流程测试
     */
    @IsTest
    static void testFleetClaimInitAndSave() {
        // 获取测试用户
        List<User> userPartner = [
            SELECT Id
            FROM User
            WHERE Username = '<EMAIL>'
            LIMIT 1
        ];

        Test.startTest();

        // 测试初始化数据获取
        AuraResponseEntity aura = CCM_Community_FleetEditCtl.getInitData(userPartner[0].Id, null);
        System.assertNotEquals(null, aura, '初始化数据不应为空');
        System.assertEquals(true, aura.isSuccess, '初始化应该成功');

        // 获取测试产品数据
        List<Product2> kitList = [
            SELECT Id
            FROM Product2
            WHERE ProductCode = '1234567'
            AND RecordType.DeveloperName = 'Kit'
            LIMIT 1
        ];

        List<Product2> proList = [
            SELECT Id
            FROM Product2
            WHERE ProductCode = '12345670'
            AND RecordType.DeveloperName = 'Product'
            LIMIT 1
        ];

        // 构建车队申请数据
        Map<String, Object> dataMap = (Map<String, Object>)aura.data;
        CCM_Community_FleetEditCtl.FleetClaim fleetClaim = (CCM_Community_FleetEditCtl.FleetClaim)dataMap.get('fleetClaim');
        fleetClaim.fleetItemList = new List<CCM_Community_FleetEditCtl.FleetItem>();
        fleetClaim.fleetItemList.add(new CCM_Community_FleetEditCtl.FleetItem());
        fleetClaim.fleetItemList[0].qtyPurchased = 1;
        fleetClaim.fleetItemList[0].warrantyList = new List<CCM_Community_FleetEditCtl.Warranty>();

        // 创建保修项目
        CCM_Community_FleetEditCtl.WarrantyItem warrantyItem = new CCM_Community_FleetEditCtl.WarrantyItem();
        warrantyItem.fakeId = 'test_warranty_item_id';
        warrantyItem.kitId = kitList[0].Id;
        warrantyItem.productId = proList[0].Id;
        warrantyItem.serialNumber = 'TEST12312312313';

        CCM_Community_FleetEditCtl.Warranty warranty = new CCM_Community_FleetEditCtl.Warranty();
        warranty.fakeId = 'test_warranty_id';
        warranty.kitId = kitList[0].Id;
        warranty.warrantyItemList = new List<CCM_Community_FleetEditCtl.WarrantyItem>();
        warranty.warrantyItemList.add(warrantyItem);

        fleetClaim.fleetItemList[0].warrantyList.add(warranty);

        // 设置终端用户信息
        fleetClaim.endUserCustomer = new CCM_Community_FleetEditCtl.AccountInfo();
        fleetClaim.endUserCustomer.lastName = '王';
        fleetClaim.endUserCustomer.firstName = '测试';
        fleetClaim.endUserCustomer.emailAddress = '<EMAIL>';

        // 测试保存车队申请
        aura = CCM_Community_FleetEditCtl.saveFleetClaim(JSON.serialize(fleetClaim), 'Save');
        System.assertEquals(true, aura.isSuccess, '保存车队申请应该成功');

        fleetClaim = (CCM_Community_FleetEditCtl.FleetClaim)aura.data;
        System.assertNotEquals(null, fleetClaim.id, '保存后应该有ID');

        // 测试获取车队申请数据
        AuraResponseEntity claimDataResult = CCM_Community_FleetEditCtl.getFleetClaimData(fleetClaim.id);
        System.assertEquals(true, claimDataResult.isSuccess, '获取车队申请数据应该成功');

        // 测试序列号检查和指示器更新
        AuraResponseEntity snCheckResult = CCM_Community_FleetEditCtl.checkSNAndUpdateIndicator(JSON.serialize(fleetClaim.fleetItemList));
        System.assertEquals(true, snCheckResult.isSuccess, '序列号检查应该成功');

        // 测试客户信息获取
        AuraResponseEntity customerResult = CCM_Community_FleetEditCtl.getCustomerInfo('<EMAIL>');
        System.assertEquals(true, customerResult.isSuccess, '获取客户信息应该成功');

        Test.stopTest();
    }
    /**
     * @description 测试文件上传和删除功能
     */
    @IsTest
    static void testFileUploadAndDelete() {
        // 获取测试用户
        List<User> userPartner = [
            SELECT Id
            FROM User
            WHERE Username = '<EMAIL>'
            LIMIT 1
        ];

        Test.startTest();

        // 初始化数据
        AuraResponseEntity aura = CCM_Community_FleetEditCtl.getInitData(userPartner[0].Id, null);

        List<Product2> kitList = [
            SELECT Id
            FROM Product2
            WHERE ProductCode = '1234567'
            AND RecordType.DeveloperName = 'Kit'
            LIMIT 1
        ];

        List<Product2> proList = [
            SELECT Id
            FROM Product2
            WHERE ProductCode = '12345670'
            AND RecordType.DeveloperName = 'Product'
            LIMIT 1
        ];

        // 构建车队申请数据
        Map<String, Object> dataMap = (Map<String, Object>)aura.data;
        CCM_Community_FleetEditCtl.FleetClaim fleetClaim = (CCM_Community_FleetEditCtl.FleetClaim)dataMap.get('fleetClaim');
        fleetClaim.fleetItemList = new List<CCM_Community_FleetEditCtl.FleetItem>();
        fleetClaim.fleetItemList.add(new CCM_Community_FleetEditCtl.FleetItem());
        fleetClaim.fleetItemList[0].qtyPurchased = 1;
        fleetClaim.fleetItemList[0].warrantyList = new List<CCM_Community_FleetEditCtl.Warranty>();

        CCM_Community_FleetEditCtl.WarrantyItem warrantyItem = new CCM_Community_FleetEditCtl.WarrantyItem();
        warrantyItem.fakeId = 'test_warranty_item_id_2';
        warrantyItem.kitId = kitList[0].Id;
        warrantyItem.productId = proList[0].Id;
        warrantyItem.serialNumber = 'TEST98765432109';

        CCM_Community_FleetEditCtl.Warranty warranty = new CCM_Community_FleetEditCtl.Warranty();
        warranty.fakeId = 'test_warranty_id_2';
        warranty.kitId = kitList[0].Id;
        warranty.warrantyItemList = new List<CCM_Community_FleetEditCtl.WarrantyItem>();
        warranty.warrantyItemList.add(warrantyItem);

        fleetClaim.fleetItemList[0].warrantyList.add(warranty);

        fleetClaim.endUserCustomer = new CCM_Community_FleetEditCtl.AccountInfo();
        fleetClaim.endUserCustomer.lastName = '李';
        fleetClaim.endUserCustomer.firstName = '测试';
        fleetClaim.endUserCustomer.emailAddress = '<EMAIL>';

        // 保存车队申请
        aura = CCM_Community_FleetEditCtl.saveFleetClaim(JSON.serialize(fleetClaim), 'Save');
        fleetClaim = (CCM_Community_FleetEditCtl.FleetClaim)aura.data;

        // 测试文件上传功能
        AuraResponseEntity uploadResult = CCM_Community_FleetEditCtl.uploadFile(fleetClaim.id, 'test_file_content', 'test_file.pdf');
        System.assertEquals(true, uploadResult.isSuccess, '文件上传应该成功');

        // 测试文件删除功能
        AuraResponseEntity deleteResult = CCM_Community_FleetEditCtl.deleteFile('test_content_document_id');
        System.assertEquals(true, deleteResult.isSuccess, '文件删除应该成功');

        Test.stopTest();
    }
    /**
     * @description 测试序列号验证功能 - 包含各种边界条件
     */
    @IsTest
    static void testVerifySerialNumber() {
        Test.startTest();

        // 测试空值情况
        System.assertEquals(false, CCM_Community_FleetEditCtl.verifySerialNumber(null, null, null, null),
                           '空值参数应该返回false');

        // 测试空配置映射的情况
        System.assertEquals(true, CCM_Community_FleetEditCtl.verifySerialNumber('EGO', '123', new Map<String, System_Configuration__c>(), null),
                           '空配置映射应该返回true');

        // 测试有效的序列号格式
        System.assertEquals(true, CCM_Community_FleetEditCtl.verifySerialNumber('EGO', '123', new Map<String, System_Configuration__c>{
            'EGO_v0' => new System_Configuration__c(
                Name = 'EGO_v0',
                RegExp__c = '[0-9]{3}'
            )
        }, null), '符合正则表达式的序列号应该返回true');

        // 测试无效的序列号格式
        System.assertEquals(false, CCM_Community_FleetEditCtl.verifySerialNumber('EGO', 'abc', new Map<String, System_Configuration__c>{
            'EGO_v0' => new System_Configuration__c(
                Name = 'EGO_v0',
                RegExp__c = '[0-9]{3}'
            )
        }, null), '不符合正则表达式的序列号应该返回false');

        // 测试不同品牌的序列号验证
        System.assertEquals(true, CCM_Community_FleetEditCtl.verifySerialNumber('Skil', 'SKL*********', new Map<String, System_Configuration__c>{
            'Skil_v1' => new System_Configuration__c(
                Name = 'Skil_v1',
                RegExp__c = 'SKL[0-9]{9}'
            )
        }, null), 'Skil品牌序列号验证应该成功');

        Test.stopTest();
    }

    /**
     * @description 测试序列号检查功能 - 包含不同品牌和规则的测试
     */
    @IsTest
    static void testCheckSerialNumber(){
        Test.startTest();

        // 测试Skil品牌序列号检查
        CCM_Community_FleetEditCtl.WarrantyItem item = new CCM_Community_FleetEditCtl.WarrantyItem();
        item.serialNumber = '123';
        item.kitBrand = 'Skil';
        item.productCode = 'SKIL001';
        CCM_Community_FleetEditCtl.checkSerialNumber(item, new Map<String, Warranty_Rules__c>(),
                                                    new Map<String,System_Configuration__c>(),
                                                    new List<Project_SN__c>());
        System.assertNotEquals(null, item.serialNumber, 'Skil序列号检查后应该保持原值');

        // 测试SkilSaw品牌序列号检查
        item = new CCM_Community_FleetEditCtl.WarrantyItem();
        item.serialNumber = '*********';
        item.kitBrand = 'SkilSaw';
        item.productCode = 'SKILSAW001';
        CCM_Community_FleetEditCtl.checkSerialNumber(item, new Map<String, Warranty_Rules__c>(),
                                                    new Map<String,System_Configuration__c>(),
                                                    new List<Project_SN__c>());
        System.assertNotEquals(null, item.serialNumber, 'SkilSaw序列号检查后应该保持原值');

        // 创建保修规则用于测试
        Warranty_Rules__c rule = new Warranty_Rules__c();
        rule.NA_FC_Model__c = 'ABCD';
        rule.NA_Model__c = 'ABCD';
        rule.Name = 'Test_Rule_ABCD';
        insert rule;

        // 测试EGO品牌序列号检查 - 有效格式
        item = new CCM_Community_FleetEditCtl.WarrantyItem();
        item.serialNumber = '1ABCD6789101213';
        item.kitBrand = 'EGO';
        item.productCode = 'ABCD';
        CCM_Community_FleetEditCtl.checkSerialNumber(item, new Map<String, Warranty_Rules__c>{'ABCD'=>rule},
                                                    new Map<String,System_Configuration__c>(),
                                                    new List<Project_SN__c>());
        System.assertEquals('1ABCD6789101213', item.serialNumber, 'EGO有效序列号应该保持原值');

        // 测试EGO品牌序列号检查 - 另一种格式
        item = new CCM_Community_FleetEditCtl.WarrantyItem();
        item.serialNumber = '11ABCD7891012134';
        item.kitBrand = 'EGO';
        item.productCode = 'ABCD';
        CCM_Community_FleetEditCtl.checkSerialNumber(item, new Map<String, Warranty_Rules__c>{'ABCD'=>rule},
                                                    new Map<String,System_Configuration__c>(),
                                                    new List<Project_SN__c>());
        System.assertEquals('11ABCD7891012134', item.serialNumber, 'EGO另一种格式序列号应该保持原值');

        Test.stopTest();
    }

    /**
     * @description 测试包装类的构造和属性设置
     */
    @IsTest
    static void testWrapperClasses(){
        Test.startTest();

        // 测试FleetClaim包装类
        CCM_Community_FleetEditCtl.FleetClaim claim = new CCM_Community_FleetEditCtl.FleetClaim();
        claim.id = 'test_claim_id';
        claim.name = '测试车队申请';
        claim.claimPackName = '测试申请包';
        claim.fleetProgramRuleId = 'test_rule_id';
        claim.approvalStatus = '待审批';
        claim.isPaidLabel = '未支付';
        claim.currencyCode = 'USD';
        claim.salesDateStr = '2024-01-01';
        claim.brand = 'EGO';
        claim.lstPrimaryUseOption = new List<String>{'商业用途', '个人用途'};
        claim.primaryUse = '商业用途';
        claim.fleetItemList = new List<CCM_Community_FleetEditCtl.FleetItem>();
        claim.totalSalesAmount = 1000.00;
        claim.fleetDiscount = 100.00;
        claim.totalRetailPrice = 1100.00;
        claim.estimatedCreditReturn = 50.00;
        claim.deliverAtOnce = true;
        claim.deliverAtOnceLabel = '一次性交付';
        claim.billToAddressId = 'test_address_id';
        claim.billToAddressName = '测试地址';
        claim.addressIdListStr = 'addr1,addr2';
        claim.haveApprovalHistory = false;
        claim.invoiceNumber = 'INV-001';

        System.assertEquals('test_claim_id', claim.id, 'FleetClaim ID应该正确设置');
        System.assertEquals('测试车队申请', claim.name, 'FleetClaim名称应该正确设置');

        // 测试AccountInfo包装类
        CCM_Community_FleetEditCtl.AccountInfo info = new CCM_Community_FleetEditCtl.AccountInfo();
        info.id = 'test_account_id';
        info.name = '测试账户';
        info.lastName = '张';
        info.firstName = '三';
        info.country = '中国';
        info.zipPostalCode = '100000';
        info.orgName = '测试公司';
        info.phone = '***********';
        info.city = '北京';
        info.state = '北京市';
        info.addressLine = '测试街道123号';
        info.emailAddress = '<EMAIL>';
        info.productType = 'EGO';
        info.eligibleForFleet = true;
        info.source = '社区';

        System.assertEquals('test_account_id', info.id, 'AccountInfo ID应该正确设置');
        System.assertEquals('张', info.lastName, 'AccountInfo姓氏应该正确设置');

        // 测试FileInfo包装类
        CCM_Community_FleetEditCtl.FileInfo fileInfo = new CCM_Community_FleetEditCtl.FileInfo();
        fileInfo.contentId = 'test_content_id';
        fileInfo.uploadFileName = '测试文件.pdf';
        fileInfo.contentUrl = '/test/file/url';
        fileInfo.contentDocumentId = 'test_doc_id';
        fileInfo.islinkedEntity = true;

        System.assertEquals('test_content_id', fileInfo.contentId, 'FileInfo内容ID应该正确设置');
        System.assertEquals('测试文件.pdf', fileInfo.uploadFileName, 'FileInfo文件名应该正确设置');

        // 测试FleetItem包装类
        CCM_Community_FleetEditCtl.FleetItem fleetItem = new CCM_Community_FleetEditCtl.FleetItem();
        fleetItem.id = 'test_item_id';
        fleetItem.fakeId = 'fake_item_id';
        fleetItem.kitId = 'test_kit_id';
        fleetItem.kitCode = 'KIT001';
        fleetItem.kitName = '测试套件';
        fleetItem.msrp = 500.00;
        fleetItem.qtyPurchased = 2;
        fleetItem.total = 1000.00;
        fleetItem.fleetDiscount = 50.00;
        fleetItem.dealerRebate = 25.00;
        fleetItem.unitSalesPrice = 475.00;
        fleetItem.totalSalesPrice = 950.00;
        fleetItem.productType = '工具';

        System.assertEquals('test_item_id', fleetItem.id, 'FleetItem ID应该正确设置');
        System.assertEquals(2, fleetItem.qtyPurchased, 'FleetItem购买数量应该正确设置');

        // 测试Warranty包装类
        CCM_Community_FleetEditCtl.Warranty warranty = new CCM_Community_FleetEditCtl.Warranty();
        warranty.index = 1;
        warranty.id = 'test_warranty_id';
        warranty.fakeId = 'fake_warranty_id';
        warranty.kitId = 'test_kit_id';
        warranty.kitCode = 'KIT001';
        warranty.kitName = '测试套件';
        warranty.purchaseDate = '2024-01-01';
        warranty.createdByFleet = true;
        warranty.checked = false;

        System.assertEquals(1, warranty.index, 'Warranty索引应该正确设置');
        System.assertEquals(true, warranty.createdByFleet, 'Warranty创建标志应该正确设置');

        // 测试WarrantyItem包装类
        CCM_Community_FleetEditCtl.WarrantyItem warrantyItem = new CCM_Community_FleetEditCtl.WarrantyItem();
        warrantyItem.id = 'test_warranty_item_id';
        warrantyItem.fakeId = 'fake_warranty_item_id';
        warrantyItem.kitId = 'test_kit_id';
        warrantyItem.kitCode = 'KIT001';
        warrantyItem.kitName = '测试套件';
        warrantyItem.kitBrand = 'EGO';
        warrantyItem.productId = 'test_product_id';
        warrantyItem.productCode = 'PROD001';
        warrantyItem.productName = '测试产品';
        warrantyItem.productModel = 'MODEL001';
        warrantyItem.productType = '工具';
        warrantyItem.sequence = 'SEQ001';
        warrantyItem.haveSequenceProduct = true;
        warrantyItem.serialNumber = 'SN*********';

        System.assertEquals('test_warranty_item_id', warrantyItem.id, 'WarrantyItem ID应该正确设置');
        System.assertEquals('SN*********', warrantyItem.serialNumber, 'WarrantyItem序列号应该正确设置');

        Test.stopTest();
    }

    /**
     * @description 测试提交审批功能
     */
    @IsTest
    static void testSubmitForApproval() {
        // 获取测试用户
        List<User> userPartner = [
            SELECT Id
            FROM User
            WHERE Username = '<EMAIL>'
            LIMIT 1
        ];

        Test.startTest();

        // 初始化数据并创建车队申请
        AuraResponseEntity aura = CCM_Community_FleetEditCtl.getInitData(userPartner[0].Id, null);

        List<Product2> kitList = [
            SELECT Id
            FROM Product2
            WHERE ProductCode = '1234567'
            AND RecordType.DeveloperName = 'Kit'
            LIMIT 1
        ];

        List<Product2> proList = [
            SELECT Id
            FROM Product2
            WHERE ProductCode = '12345670'
            AND RecordType.DeveloperName = 'Product'
            LIMIT 1
        ];

        Map<String, Object> dataMap = (Map<String, Object>)aura.data;
        CCM_Community_FleetEditCtl.FleetClaim fleetClaim = (CCM_Community_FleetEditCtl.FleetClaim)dataMap.get('fleetClaim');
        fleetClaim.fleetItemList = new List<CCM_Community_FleetEditCtl.FleetItem>();
        fleetClaim.fleetItemList.add(new CCM_Community_FleetEditCtl.FleetItem());
        fleetClaim.fleetItemList[0].qtyPurchased = 1;
        fleetClaim.fleetItemList[0].warrantyList = new List<CCM_Community_FleetEditCtl.Warranty>();

        CCM_Community_FleetEditCtl.WarrantyItem warrantyItem = new CCM_Community_FleetEditCtl.WarrantyItem();
        warrantyItem.fakeId = 'test_warranty_item_submit';
        warrantyItem.kitId = kitList[0].Id;
        warrantyItem.productId = proList[0].Id;
        warrantyItem.serialNumber = 'SUBMIT*********';

        CCM_Community_FleetEditCtl.Warranty warranty = new CCM_Community_FleetEditCtl.Warranty();
        warranty.fakeId = 'test_warranty_submit';
        warranty.kitId = kitList[0].Id;
        warranty.warrantyItemList = new List<CCM_Community_FleetEditCtl.WarrantyItem>();
        warranty.warrantyItemList.add(warrantyItem);

        fleetClaim.fleetItemList[0].warrantyList.add(warranty);

        fleetClaim.endUserCustomer = new CCM_Community_FleetEditCtl.AccountInfo();
        fleetClaim.endUserCustomer.lastName = '王';
        fleetClaim.endUserCustomer.firstName = '提交测试';
        fleetClaim.endUserCustomer.emailAddress = '<EMAIL>';

        // 测试提交审批
        aura = CCM_Community_FleetEditCtl.saveFleetClaim(JSON.serialize(fleetClaim), 'Submit');
        System.assertEquals(true, aura.isSuccess, '提交审批应该成功');

        fleetClaim = (CCM_Community_FleetEditCtl.FleetClaim)aura.data;
        System.assertEquals('Pending Approval', fleetClaim.approvalStatus, '提交后状态应该是待审批');

        Test.stopTest();
    }
}