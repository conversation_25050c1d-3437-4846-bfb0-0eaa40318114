public without sharing class CCM_Community_FleetListCtl {

    public class FleetClaimFilter{
        @AuraEnabled public Integer min;
        @AuraEnabled public Integer max;
        @AuraEnabled public String userId;
        @AuraEnabled public String channelCustomerId;
    }

    @AuraEnabled
    public static AuraResponseEntity getFleetClaimList(Integer pageNumber, Integer pageCount, String filterStr){
        AuraResponseEntity auraResponse = new AuraResponseEntity();
        try{
            FleetClaimFilter fleetClaimFilter = (FleetClaimFilter)JSON.deserialize(filterStr, FleetClaimFilter.class);
            fleetClaimFilter.channelCustomerId = CCM_PortalPageUtil.getCustomerByUser(fleetClaimFilter.userId);
            if(String.isNotBlank(fleetClaimFilter.channelCustomerId)){
                //请勿去掉Integer.valueOf格式转换，不然会报错，Internal Salesforce.com Error
                fleetClaimFilter.min = (Integer.valueOf(pageNumber) - 1) * Integer.valueOf(pageCount);
                fleetClaimFilter.max = Integer.valueOf(pageNumber) * Integer.valueOf(pageCount) -1;

                List<Fleet_Claim__c> fleetClaimObjList = getFleetClaimObjList(fleetClaimFilter);

                List<CCM_Community_FleetEditCtl.FleetClaim> fleetClaimList = getFleetClaimList(fleetClaimFilter, fleetClaimObjList);

                Map<String, Object> dataMap = new Map<String, Object>();
                dataMap.put('dataList', fleetClaimList);
                dataMap.put('totalRecords', fleetClaimObjList.size());

                auraResponse.data = dataMap;
            }
        }catch(Exception ex){
            Quota_Generate_Utils.exceptionLog(ex, 'CCM_Community_FleetListCtl', 'getFleetClaimList');
            auraResponse.code = 400;
            auraResponse.message = 'Cause:' + ex.getCause() + '; Line:' + ex.getLineNumber() + '; Message:' + ex.getMessage() + '; Type:' + ex.getTypeName() + '; StackTrace:' + ex.getStackTraceString();
        }
        return auraResponse;
    }

    private static List<Fleet_Claim__c> getFleetClaimObjList(FleetClaimFilter fleetClaimFilter){
        String queryStr =
            // add haibo: french
            ' SELECT Id, Name, Brand_Name__c, Deliver_At_Once__c, Approval_Status__c, toLabel(Approval_Status__c) approvalStatusLabel, Is_Paid__c, ' +
            ' CurrencyIsoCode, Estimated_Credit_Return__c, Fleet_Discount__c, Product_Use_Type__c,Invoice_Item__c,Invoice_Item__r.Invoice_Number__c, ' +
            ' Sales_Date__c, Total_Retail_Price__c, Total_Sales_Amount__c, Claim_Pack__c, Claim_Pack__r.Name, ' +
            ' End_User_Customer__c, ' +
            ' End_User_Customer__r.LastName, ' +
            ' End_User_Customer__r.Firstname, ' +
            ' End_User_Customer__r.Organization_Name__c, ' +
            ' End_User_Customer__r.PersonEmail, ' +
            ' End_User_Customer__r.Phone, ' +
            ' End_User_Customer__r.Shippingcountry, ' +
            ' End_User_Customer__r.Product_Type__c, ' +
            ' End_User_Customer__r.ShippingStreet, ' +
            ' End_User_Customer__r.ShippingPostalCode, ' +
            ' End_User_Customer__r.ShippingCity, ' +
            ' End_User_Customer__r.ShippingState, ' +
            ' End_User_Customer__r.Eligible_For_Fleet__c ' +
            ' FROM Fleet_Claim__c ';
        String filterStr = '';
        if(String.isNotBlank(fleetClaimFilter.channelCustomerId)){
            String channelCustomerId = fleetClaimFilter.channelCustomerId;
            filterStr += ' AND Channel_Customer__c = :channelCustomerId ';
        }
        if(String.isNotBlank(filterStr)){
            //将第一个 'AND' 替换 'WHERE'
            queryStr += ' WHERE ' + filterStr.removeStart(' AND ');
        }
        queryStr += ' ORDER BY CreatedDate DESC LIMIT 50000 ';
        return Database.query(queryStr);
    }

    private static List<CCM_Community_FleetEditCtl.FleetClaim> getFleetClaimList(FleetClaimFilter fleetClaimFilter, List<Fleet_Claim__c> fleetClaimObjList){
        List<CCM_Community_FleetEditCtl.FleetClaim> fleetClaimList = new List<CCM_Community_FleetEditCtl.FleetClaim>();

        for(Integer i = 0 ; i <= fleetClaimObjList.size() - 1; i++){
            //分页
            if(i >= fleetClaimFilter.min && i <= fleetClaimFilter.max){
                Fleet_Claim__c fleetClaimObj = fleetClaimObjList.get(i);

                CCM_Community_FleetEditCtl.FleetClaim  fleetClaim = getFleetClaim(fleetClaimObj);

                fleetClaimList.add(fleetClaim);
            }
        }

        return fleetClaimList;
    }

    private static CCM_Community_FleetEditCtl.FleetClaim getFleetClaim(Fleet_Claim__c fleetClaimObj){
        CCM_Community_FleetEditCtl.FleetClaim fleetClaim = new CCM_Community_FleetEditCtl.FleetClaim();

        fleetClaim.id = fleetClaimObj.Id;
        fleetClaim.name = fleetClaimObj.Name;
        if(fleetClaimObj.Claim_Pack__c != null){
            fleetClaim.claimPackName = fleetClaimObj.Claim_Pack__r.Name;
        }else if(fleetClaimObj.Invoice_Item__c != null){
            fleetClaim.claimPackName = fleetClaimObj.Invoice_Item__r.Invoice_Number__c;
        }
        // add haibo: french
        if(String.isNotBlank(fleetClaimObj.Approval_Status__c)){
            fleetClaim.approvalStatus = String.valueOf(fleetClaimObj.get('approvalStatusLabel'));
        }else{
            fleetClaim.approvalStatus = Label.CCM_Portal_Draft;
        }

        if(fleetClaim.approvalStatus == 'Approved' || fleetClaim.approvalStatus == 'Approuvé'){
            fleetClaim.isPaidLabel = CCM_Community_FleetEditCtl.yesOrNoMap.get(fleetClaimObj.Is_Paid__c);
        }

        if(String.isNotBlank(fleetClaimObj.End_User_Customer__c)){
            fleetClaim.endUserCustomer = CCM_Community_FleetEditCtl.getAccountInfo(fleetClaimObj.End_User_Customer__r);
        }

        fleetClaim.brand = fleetClaimObj.Brand_Name__c;
        fleetClaim.primaryUse = fleetClaimObj.Product_Use_Type__c;
        fleetClaim.totalSalesAmount = fleetClaimObj.Total_Sales_Amount__c;
        fleetClaim.fleetDiscount = fleetClaimObj.Fleet_Discount__c;
        fleetClaim.totalRetailPrice = fleetClaimObj.Total_Retail_Price__c;
        fleetClaim.estimatedCreditReturn = fleetClaimObj.Estimated_Credit_Return__c;
        // fleetClaim.deliverAtOnce = fleetClaimObj.Deliver_At_Once__c;
        // fleetClaim.deliverAtOnceLabel = CCM_Community_FleetEditCtl.yesOrNoMap.get(fleetClaimObj.Deliver_At_Once__c);
        fleetClaim.currencyCode = fleetClaimObj.CurrencyIsoCode;

        return fleetClaim;
    }

    @AuraEnabled
    public static AuraResponseEntity getAvailableFleetPrograms(){
        AuraResponseEntity auraResponse = new AuraResponseEntity();
        String fleetProgramInfos = '';
        String accId = CCM_PortalPageUtil.getCustomerByUser(UserInfo.getUserId());
        if(String.isNotBlank(accId)){
            String orgCode = '';
            for(Account acc : [SELECT ORG_Code__c FROM Account WHERE Id = :accId]) {
                orgCode = acc.ORG_Code__c;
            }
            if(orgCode != CCM_Constants.ORG_CODE_CCA) {
                orgCode  = CCM_Constants.ORG_CODE_CNA;
            }
            String yearStr = String.valueOf(Date.today().year());

            List<Fleet_Program_Rule__c> rules = [SELECT Id, Program_Code__c, Program_Code_French__c FROM Fleet_Program_Rule__c WHERE Actived__c = true AND Year__c = :yearStr AND Org_Code__c = :orgCode];
            // List<Fleet_Program_Target_Customer__c> targetCustomers = [SELECT Customer__c, Fleet_Program_Rule__c, Fleet_Program_Rule__r.Program_Code__c FROM Fleet_Program_Target_Customer__c
            //                                                           WHERE Customer__c = :accId AND Fleet_Program_Rule__r.Actived__c = true AND Fleet_Program_Rule__r.Year__c = :yearStr];
            List<FleetProgramInfo> infos = new List<FleetProgramInfo>();
            for(Fleet_Program_Rule__c rule : rules) {
                FleetProgramInfo info = new FleetProgramInfo();
                // add haibo: french
                if(UserInfo.getLanguage() == Label.CCM_Portal_French){
                    info.label = rule.Program_Code_French__c;
                }else {
                    info.label = rule.Program_Code__c;
                }
                info.value = rule.Id;
                infos.add(info);
            }
            fleetProgramInfos = JSON.serialize(infos);
        }
        auraResponse.data = fleetProgramInfos;
        return auraResponse;
    }

    public class FleetProgramInfo {
        @AuraEnabled
        public String label {get;set;}
        @AuraEnabled
        public String value {get;set;}
    }
}