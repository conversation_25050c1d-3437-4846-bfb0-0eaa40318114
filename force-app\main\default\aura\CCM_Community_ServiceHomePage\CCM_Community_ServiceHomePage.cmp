<!--
 - Created by gluo006 on 8/20/2019.
 -->

<aura:component implements="forceCommunity:availableForAllPageTypes"
                description="CCM_Community_ServiceHomePage"
                controller="CCM_ServiceHome">
    <aura:attribute name="pending" type="String" default="0"/>
    <aura:attribute name="approved" type="String" default="0"/>
    <aura:attribute name="rejected" type="String" default="0"/>
    <aura:attribute name="inProgress" type="String" default="0"/>
    <aura:attribute name="issued" type="String" default="0"/>
    <aura:attribute name="paid" type="String" default="0"/>
    <aura:attribute name="columns" type="List" default="[]"/>
    <aura:attribute name="currentOrders" type="List" default="[]"/>
    <aura:attribute name="claimColumns" type="List" default="[]"/>
    <aura:attribute name="currentClaims" type="List" default="[]"/>
    <!-- 分页相关属性：页号、每页记录数、总记录数 -->
    <aura:attribute name="claimPageNumber" type="String" default="1"/>
    <aura:attribute name="orderPageNumber" type="String" default="1"/>
    <aura:attribute name="claimPageCount" type="String" default="10"/>
    <aura:attribute name="orderPageCount" type="String" default="10"/>
    <aura:attribute name="userId" type="String"/>

    <aura:attribute name="tabId" type="String" default="Claim" />
    <aura:attribute name="pageCount" type="String" default="10" />
    <aura:attribute name="claimTotalRecords" type="String" default="5" />
    <aura:attribute name="orderTotalRecords" type="String" default="0" />
    <aura:handler name="change" value="{!v.tabId}" action="{!c.handleChange}"/>
    <aura:attribute name="isDistributor" type="Boolean" default="false" />

    <aura:attribute name="isShowWarrantyModel" type="Boolean" default="false" />

    <aura:attribute name="isShowRejectComments" type="Boolean" default="false" />
    <aura:attribute name="rejectedComments" type="String" default="" />
    <aura:attribute name="tooltipTop" type="String" />
    <aura:attribute name="tooltipLeft" type="String" />

    <aura:attribute name="allowClaim" type="Boolean" default="true" />

    <aura:handler name="pageChange" event="c:CCM_ListPageFooter_PageChangeEvt" action="{!c.pageChange}" />
    <aura:handler name="pageCountChange" event="c:CCM_ListPageFooter_PageCountChangeEvt" action="{!c.pageCountChange}" />

    <aura:attribute name="newDocuments" type="List" default=""/>
    <aura:handler name="init" value="{!this}" action="{!c.init}"/>

    <div class="slds-grid slds-grid_align-space">
        <div class="slds-grid slds-grid--align-center slds-size--1-of-1">
            <div class="slds-col slds-wrap ccm_gridSpace leftCon">
                <article class="slds-card">
                    <div class="slds-grid">
                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                            <div class="slds-media__body">
                                <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade title-wrap">
                                <span class="section-header-title slds-p-horizontal--small slds-truncate title-content"
                                      title="{!$Label.c.CCM_Portal_ClaimInformation}">
                                        <span><strong>{!$Label.c.CCM_Portal_ClaimInformation}</strong></span>
                                </span>

                                    <a href="/s/allclaim" class="more">{!$Label.c.CCM_Portal_More}<lightning:icon iconName="utility:chevronright" size="xx-small"/></a>

                                </h2>
                            </div>
                        </header>
                    </div>
                    <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                        <div class="slds-grid slds-wrap slds-grid_align-space">
                            <c:CCM_Community_BigNumberAndIcon icon="utility:shield" text="{!$Label.c.CCM_Portal_Submitted}" num="{!v.pending}" />
                            <c:CCM_Community_BigNumberAndIcon icon="utility:approval" text="{!$Label.c.CCM_Portal_Approved}" num="{!v.approved}" />
                            <c:CCM_Community_BigNumberAndIcon icon="utility:redo" text="{!$Label.c.CCM_Portal_Rejected}" num="{!v.rejected}"   borderRight="false"/>
                        </div>
                    </div>

                </article>

                <aura:if isTrue="{!v.isShowWarrantyModel}">
                <article class="slds-card">
                    <div class="slds-grid">
                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                            <div class="slds-media__body">
                                <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade title-wrap">
                                <span class="section-header-title slds-p-horizontal--small slds-truncate title-content"
                                      title="{!$Label.c.CCM_Portal_ClaimPaymentInformation}">
                                        <span><strong>{!$Label.c.WarrantyRegistrationInformation}</strong></span>
                                 </span>
                                    <a href="/s/allwarranty" class="more">{!$Label.c.CCM_Portal_More}<lightning:icon iconName="utility:chevronright" size="xx-small"/></a>
                                </h2>
                            </div>
                        </header>
                    </div>
                </article>
            </aura:if>

                <article class="slds-card">
                    <div class="slds-grid">
                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                            <div class="slds-media__body">
                                <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade title-wrap">
                                <span class="section-header-title slds-p-horizontal--small slds-truncate title-content"
                                      title="{!$Label.c.CCM_Portal_ClaimPaymentInformation}">
                                        <span><strong>{!$Label.c.CCM_Portal_ClaimPaymentInformation}</strong></span>
                                 </span>
                                    <aura:if isTrue="{!!v.isDistributor}">
                                    	<a href="/s/myaccount?selectedTab=CreditMemo" class="more">{!$Label.c.CCM_Portal_More}<lightning:icon iconName="utility:chevronright" size="xx-small"/></a>
                                    </aura:if>
                                </h2>
                            </div>
                        </header>
                    </div>
                    <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                        <div class="slds-grid slds-wrap slds-grid_align-space">
                            <c:CCM_Community_BigNumberAndIcon icon="utility:multi_select_checkbox" text="{!$Label.c.CCM_Portal_Issued}" num="{!v.issued}"/>
                            <c:CCM_Community_BigNumberAndIcon icon="utility:edit_form" text="{!$Label.c.CCM_Portal_InProgress}" num="{!v.inProgress}"/>
                            <c:CCM_Community_BigNumberAndIcon icon="utility:moneybag" text="{!$Label.c.CCM_Portal_Paid}" num="{!v.paid}" borderRight="false"/>
                        </div>
                    </div>
                </article>
                <article class="slds-card">
                    <div class="slds-grid">
                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                            <div class="slds-media__body">
                                <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade">
                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                      title="{!$Label.c.CCM_Portal_ServiceClaimProcedures}">
                                        <span><strong>{!$Label.c.CCM_Portal_ServiceClaimProcedures}</strong></span>
                                 </span>
                                </h2>
                            </div>
                        </header>
                    </div>
                    <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                        <ul class="slds-wrap quickLinks">
                            <li class="slds-p-vertical_small slds-border_bottom"><p>{!$Label.c.CCM_Portal_Step1}:</p><a href="/s/productregistration"><lightning:icon iconName="utility:record_update" size="x-small" class="slds-m-right_medium"/>{!$Label.c.CCM_Portal_ProductRegistration} <lightning:icon iconName="utility:chevronright" size="x-small" class="slds-float_right"/></a></li>
                            <li class="slds-p-vertical_small slds-border_bottom"><p>{!$Label.c.CCM_Portal_Step2}:</p><a href="/s/warrantylookup"><lightning:icon iconName="utility:display_text" size="x-small" class="slds-m-right_medium"/>{!$Label.c.CCM_Portal_QuickWarrantyLookup} <lightning:icon iconName="utility:chevronright" size="x-small" class="slds-float_right"/></a></li>
                            <li class="slds-p-vertical_small slds-border_bottom"><p>{!$Label.c.CCM_Portal_Step3}:</p><a href="/s/eligibilitycheck"><lightning:icon iconName="utility:record_update" size="x-small" class="slds-m-right_medium"/>{!$Label.c.CCM_Portal_EligibilityCheck}<lightning:icon iconName="utility:chevronright" size="x-small" class="slds-float_right"/></a></li>
                            <aura:if isTrue="{!v.allowClaim}">
                                <li class="slds-p-vertical_small slds-border_bottom"><p>{!$Label.c.CCM_Portal_Step4}:</p><a href="/s/warrantyclaim"><lightning:icon iconName="utility:new_direct_message" size="x-small" class="slds-m-right_medium"/>{!$Label.c.CCM_Portal_NewClaim} <lightning:icon iconName="utility:chevronright" size="x-small" class="slds-float_right"/></a></li>
                            </aura:if>
                        </ul>
                    </div>
                </article>
                <article class="slds-card">
                    <div class="slds-grid">
                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                            <div class="slds-media__body">
                                <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                      title="{!$Label.c.CCM_Portal_QuickLinks}">
                                        <span><strong>{!$Label.c.CCM_Portal_QuickLinks}</strong></span>
                                 </span>
                                </h2>
                            </div>
                        </header>
                    </div>
                    <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                        <ul class="slds-wrap quickLinks">
                            <aura:if isTrue="{!!v.isDistributor}">
                            <li class="slds-p-vertical_small slds-border_bottom"><a href="/s/myaccount?selectedTab=CreditMemo"><lightning:icon iconName="utility:paste" size="x-small" class="slds-m-right_medium"/>{!$Label.c.CCM_Portal_InvoicesOverview}<lightning:icon iconName="utility:chevronright" size="x-small" class="slds-float_right" /></a></li>
                            </aura:if>
                            <li class="slds-p-vertical_small slds-border_bottom"><a href="/s/resourcecenter?selectedTab=KnowledgeBase"><lightning:icon iconName="utility:warning" size="x-small" class="slds-m-right_medium"/>{!$Label.c.CCM_Portal_KnowledgeBase} <lightning:icon iconName="utility:chevronright" size="x-small" class="slds-float_right" /></a></li>
                        </ul>
                    </div>
                </article>
            </div>
            <div class="slds-col slds-wrap rightCon">
                <lightning:tabset selectedTabId="{!v.tabId}">
                    <lightning:tab label="{!$Label.c.CCM_Portal_ClaimHistory}" id="Claim">
                        <div class="slds-grid">
                            <header class="slds-media slds-media_center slds-has-flexi-truncate">
                                <div class="slds-media__body">
                                    <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                        <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                              title="{!$Label.c.CCM_Portal_ClaimHistory}">&nbsp;
                                         </span>
                                    </h2>
                                </div>
                            </header>
                        </div>
                        <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                            <div class="slds-wrap">
                                <c:CCM_DataTable columns="{!v.claimColumns}" data="{!v.currentClaims}" />
                                <aura:if isTrue="{!v.currentClaims.length > 1}">
                                    <div class="slds-text-align_right">
                                        <c:CCM_ListPageFooter totalRecords="{!v.claimTotalRecords}" pageCount="{!v.claimPageCount}"/>
                                    </div>
                                </aura:if>
                            </div>
                        </div>
                    </lightning:tab>
                    <lightning:tab label="{!$Label.c.CCM_Portal_OrderHistory}" id="order">
                        <div class="slds-grid">
                            <header class="slds-media slds-media_center slds-has-flexi-truncate">
                                <div class="slds-media__body">
                                    <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                        <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                              title="{!$Label.c.CCM_Portal_OrderHistory}">&nbsp;
                                         </span>
                                    </h2>
                                </div>
                            </header>
                        </div>
                        <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                            <div class="slds-wrap">
                                <c:CCM_DataTable columns="{!v.columns}" data="{!v.currentOrders}" />
                                <aura:if isTrue="{!v.currentOrders.length > 1}">
                                    <div class="slds-text-align_right">
                                        <c:CCM_ListPageFooter totalRecords="{!v.orderTotalRecords}" pageCount="{!v.orderPageCount}"/>
                                    </div>
                                </aura:if>
                            </div>
                        </div>
                    </lightning:tab>
                </lightning:tabset>
                <article class="slds-card">
                    <div class="slds-grid">
                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                            <div class="slds-media__body">
                                <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                      title="{!$Label.c.CCM_Portal_NewDocuments}">
                                        <span><strong>{!$Label.c.CCM_Portal_NewDocuments}</strong></span>
                                 </span>
                                </h2>
                            </div>
                        </header>
                    </div>
                    <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                        <ul class="slds-wrap newDocuments slds-p-around_medium">
                            <aura:iteration items="{!v.newDocuments}" var="document">
                                <li class="slds-p-vertical--xxx-small"><span class="slds-m-right_x-small">{!document.createdate}</span><a target="_blank" onclick="{!c.openDocument}" data-fileId="{!document.Id}">{!document.name}</a></li>
                            </aura:iteration>
                        </ul>
                    </div>

                </article>
            </div>
        </div>
    </div>
    <aura:if isTrue="{!v.isShowRejectComments}">
        <div style="{!'max-width:500px;padding-left:2rem;padding-top:5rem;position:absolute;Top:' + v.tooltipTop + 'px' + ';Left:' + v.tooltipLeft + 'px'}">
            <div class="slds-popover slds-popover_tooltip slds-nubbin_bottom-left" role="tooltip" id="help" style="position:absolute;top:-4px;left:35px">
                <div style="position:absolute; right: 1px;top: 1px;">
                    <lightning:buttonIcon iconName="utility:close" variant="bare-inverse"  size="small" class="slds-modal__close" title="" alternativeText="" onclick="{!c.closeTooltip}"/>
                </div>
                <div style="margin-top:10px" class="slds-popover__body">{!v.rejectedComments}</div>
            </div>
        </div>
    </aura:if>
</aura:component>