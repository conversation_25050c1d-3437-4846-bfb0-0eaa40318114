global without sharing class CCM_SelectListPriceAndDateBatch implements Database.Batchable<sObject>{
    private String query;
    private Set<String> purchaseOrderIdSet;
    global CCM_SelectListPriceAndDateBatch(Set<String> purchaseOrderIdSet) {
        this.purchaseOrderIdSet = purchaseOrderIdSet;
        this.query = 'SELECT Id,List_Price_Date__c,List_Price__c,Product__c,Purchase_Order__c,Purchase_Order__r.Customer__c,Purchase_Order__r.Is_Delegate__c,Purchase_Order__r.Is_DropShip__c,Purchase_Order__r.RecordTypeId FROM Purchase_Order_Item__c WHERE Purchase_Order__c IN :purchaseOrderIdSet';
    }

    global Database.QueryLocator start(Database.BatchableContext bc) {
        return Database.getQueryLocator(query);
    }

    global void execute(Database.BatchableContext BC, list<Purchase_Order_Item__c> scope) {
        if(scope != null && scope.size() > 0){
            for(Purchase_Order_Item__c pi : scope){
                PricebookEntry priceBookEntry = new PricebookEntry();
                if(pi.Purchase_Order__r.RecordTypeId == CCM_Constants.PURCHASE_ORDER_RECORD_TYPE_PLACE_ORDER_ID){
                    if(pi.Purchase_Order__r.Is_Delegate__c){
                        priceBookEntry = getPriceBook(pi.Product__c,pi.Purchase_Order__r.Customer__c,pi.Purchase_Order__r.Is_DropShip__c);
                    }else{
                        priceBookEntry = getPortalPriceBook(pi.Product__c,pi.Purchase_Order__r.Customer__c,pi.Purchase_Order__r.Is_DropShip__c);
                    }
                    if(priceBookEntry != null){
                        pi.List_Price_Date__c = Date.valueOf(System.today());
                        pi.List_Price__c = priceBookEntry.UnitPrice;
                    }
                }else{
                    if(pi.Purchase_Order__r.Is_Delegate__c){
                        priceBookEntry = getInternalPartsPriceBook(pi.Product__c,pi.Purchase_Order__r.Customer__c);
                    }else{
                        priceBookEntry = getPartsOrderPriceBook(pi.Product__c,pi.Purchase_Order__r.Customer__c);
                    }
                    if(priceBookEntry != null){
                        pi.List_Price_Date__c = Date.valueOf(System.today());
                        pi.List_Price__c = priceBookEntry.UnitPrice;
                    }
                }
            }
            update scope;
        }
    }

    global void finish(Database.BatchableContext BC) {
        List<Purchase_Order__c> purchaseOrderList = [SELECT Id,RecordTypeId FROM Purchase_Order__c WHERE Id IN :purchaseOrderIdSet];
        for(Purchase_Order__c po : purchaseOrderList){
            if(po.RecordTypeId == CCM_Constants.PURCHASE_ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID){
                system.enqueueJob(new CCM_callPushOrderToEBSQueue(po.Id)); 
            }
        }
    }

    global static PricebookEntry getPriceBook(String prodId, String customerId, Boolean orderType) {
        if (orderType) {
            Account customer = [SELECT Id, ORG_Code__c FROM Account WHERE Id =: customerid];
            Boolean isCCA = customer.ORG_Code__c == 'CCA'? true : false;
            if(isCCA){
                return getDropShipInternalPriceBook(prodId, customerId);
            }else{
                return getDropShipInternalPriceBookCNA(prodId, customerId);
            }
        } else {
            return getInternalPriceBook(prodId, customerId, 'Sales');
        }
    }

    global static PricebookEntry getPortalPriceBook(String prodId, String customerId, Boolean orderType) {
        if (orderType) {
            return getDropShipInternalPriceBookCNA(prodId, customerId);
        } else {
            return getInternalPriceBook(prodId, customerId, 'Sales');
        }
    }

    global static PricebookEntry getDropShipInternalPriceBook(String prodId, String customerId){
        PricebookEntry priceBookEntry = new PricebookEntry();
        if (String.isNotBlank(prodId)){
            List<Product2> prodInfo = [SELECT Id, Country_of_Origin__c,Brand_Name__c,Item_Number__c,ProductCode,CS_Exchange_Rate__c,Weight__c,OverSize__c FROM Product2 WHERE Id =:prodId];
            if (prodInfo != null && prodInfo.size() > 0){
                String brandName = prodInfo[0].Brand_Name__c;
                List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
                prodEntryList = Util.getDropShipPricebookEntryByProdId(prodId, brandName, customerId);
                if (prodEntryList != null && prodEntryList.size() > 0){
                    priceBookEntry  = prodEntryList[0];
                }
            }
        }
        return priceBookEntry;
    }

    global static PricebookEntry getDropShipInternalPriceBookCNA(String prodId, String customerId){
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        PricebookEntry priceBookEntry = new PricebookEntry();
        if (String.isNotBlank(prodId)){
            List<Product2> prodInfo = [SELECT Id ,Country_of_Origin__c,Brand_Name__c,Item_Number__c,CS_Exchange_Rate__c,ProductCode,Weight__c,OverSize__c FROM Product2 WHERE Id =:prodId];
            if (prodInfo != null && prodInfo.size() > 0){
                String brandName = prodInfo[0].Brand_Name__c;
                if (String.isNotBlank(customerId)){
                    List<Sales_Program__c> authBrandList = [
                            SELECT Id, Customer__c, Brands__c, Customer__r.Distributor_or_Dealer__c,
                                   Approval_Status__c, IsDeleted, Price_Book_Mapping__r.Name,
                                   Price_Book__c, Contract_Price_List_Name__c, Contract_Price_Book__c
                            FROM Sales_Program__c
                            WHERE Customer__c =: customerId
                            AND Approval_Status__c = 'Approved'
                            AND Brands__c =: prodInfo[0].Brand_Name__c
                            AND RecordType.DeveloperName IN ('Dropship_Sales_Standard', 'Dropship_Sales_Customized')
                            AND IsDeleted = false LIMIT 1];
                    if (authBrandList != null && authBrandList.size() > 0){
                        Sales_Program__c authBrandInfo = authBrandList[0];
                        Boolean isContract = false;
                        if (authBrandInfo.Price_Book_Mapping__r.Name == 'Contract Price List'
                            && String.isNotBlank(authBrandInfo.Contract_Price_Book__c)){
                            isContract = true;
                        }
                        //更新特殊客户有主副价格册的逻辑 Modified By Abby On 7/10/2020
                        prodEntryList = Util.getPriceBookEntryByProdId(prodId, authBrandInfo.Price_Book__c, isContract);
                        System.debug(LoggingLevel.INFO, '*** prodEntryList TEST: ' + prodEntryList);
                        //如果当前的产品不再当前的价格册里，就去查找price book entry下对应产品价格最低的的标准价格册
                        if (prodEntryList == null || prodEntryList.size() == 0){
                            prodEntryList = Util.getStandardPricebookEntry(prodId,brandName,customerId);
                            System.debug(LoggingLevel.INFO, '*** prodEntry2: ' + prodEntryList);
                        }

                        if (prodEntryList != null && prodEntryList.size() > 0){
                            priceBookEntry  = prodEntryList[0];
                        }
                    }
                }
            }
        }
        return priceBookEntry;
    }

    global static PricebookEntry getInternalPriceBook(String prodId, String customerId, String type){
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        PricebookEntry priceBookEntry = new PricebookEntry();
        if (String.isNotBlank(prodId)){
            List<Product2> prodInfo = [
                                        SELECT Id,Country_of_Origin__c, Brand_Name__c,CS_Exchange_Rate__c
                                        ,ProductCode,Weight__c,OverSize__c
                                        ,Item_Number__c
                                        FROM Product2 WHERE Id != NULL
                                        AND Id =:prodId
                                        AND Is_History_Product__C = :CCM_Constants.blHistoryProduct
                                    ];
            if (prodInfo != null && prodInfo.size() > 0){
                if (String.isNotBlank(customerId)){
                    List<Sales_Program__c> authBrandList = getAuthorizedBrands(customerId, prodInfo[0].Brand_Name__c, type);
                    if (authBrandList != null && authBrandList.size() > 0){
                        Sales_Program__c authBrandInfo = authBrandList[0];
                        Boolean isContract = false;
                        if (authBrandInfo.Price_Book_Mapping__r.Name == 'Contract Price List'
                            && String.isNotBlank(authBrandInfo.Contract_Price_Book__c)){
                            isContract = true;
                        }
                        //更新特殊客户有主副价格册的逻辑 Modified By Abby On 7/10/2020
                        prodEntryList = Util.getPriceBookEntryByProdId(prodId, authBrandInfo.Price_Book__c, isContract);
                        System.debug(LoggingLevel.INFO, '*** prodEntryList TEST: ' + prodEntryList);
                        //剔除PricebookEntry:{}这种情况
                        for(Integer i = 0;i<prodEntryList.size(); i++){
                            if(prodEntryList[i].Id == null){
                                prodEntryList.remove(i);
                            }
                        }
                        //如果当前的产品不再当前的价格册里，就去查找当前brand & Customer Type下的标准价格册（取Customize 和 Standard的合集产品）
                        if (prodEntryList == null || prodEntryList.size() == 0){
                            prodEntryList = Util.getStandardPricebookEntryByProdId(prodId, authBrandInfo.Brands__c,
                                authBrandInfo.Customer__r.Distributor_or_Dealer__c,'Sales');
                            System.debug(LoggingLevel.INFO, '*** prodEntry2: ' + prodEntryList);
                        }

                        if (prodEntryList != null && prodEntryList.size() > 0){
                            priceBookEntry  = prodEntryList[0];
                        }
                    }
                }
            }
        }

        return PricebookEntry;
    }

    // global static PricebookEntry getPriceBook(String prodId,String customerId){
    //     PricebookEntry priceBookEntry = new PricebookEntry();
    //     if (String.isNotBlank(prodId)){
    //         List<Product2> prodInfo = [SELECT Id, Country_of_Origin__c,Brand_Name__c,CS_Exchange_Rate__c,Item_Number__c,ProductCode,Weight__c,OverSize__c FROM Product2 WHERE Id =:prodId];
    //         if (prodInfo != null && prodInfo.size() > 0){

    //             if (String.isNotBlank(customerId)){
    //                 List<Sales_Program__c> authBrandList = getAuthorizedBrands(customerId, prodInfo[0].Brand_Name__c, 'Sales');
    //                 if (authBrandList != null && authBrandList.size() > 0){
    //                     Sales_Program__c authBrandInfo = authBrandList[0];
    //                     Boolean isContract = false;
    //                     if (authBrandInfo.Price_Book_Mapping__r.Name == 'Contract Price List'
    //                         && String.isNotBlank(authBrandInfo.Contract_Price_Book__c)){
    //                         isContract = true;
    //                     }
    //                     PricebookEntry prodEntry = new PricebookEntry();
    //                     List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
    //                     //更新特殊客户有主副价格册的逻辑 Modified By Abby On 7/10/2020
    //                     /*prodEntryList = Util.getPriceBookEntryByProdId(prodId, authBrandInfo.Price_Book__c);*/
    //                     prodEntryList = Util.getPriceBookEntryByProdId(prodId, authBrandInfo.Price_Book__c, isContract);
    //                     System.debug(LoggingLevel.INFO, '*** prodEntry1: ' + prodEntryList);
    //                     //如果当前的产品不再当前的价格册里，就去查找当前brand & Customer Type下的标准价格册（取Customize 和 Standard的合集产品）
    //                     if (prodEntryList == null || prodEntryList.size() == 0){
    //                         prodEntryList = Util.getStandardPricebookEntryByProdId(prodId, authBrandInfo.Brands__c,
    //                             authBrandInfo.Customer__r.Distributor_or_Dealer__c,'Sales');
    //                         System.debug(LoggingLevel.INFO, '*** prodEntry2: ' + prodEntryList);
    //                     }
    //                     if (prodEntryList != null && prodEntryList.size() > 0){
    //                         prodEntry = prodEntryList[0];
    //                     }
    //                     priceBookEntry = prodEntry;
    //                 }
    //             }
    //         }
    //     }

    //     return priceBookEntry;
    // }


    // global static PricebookEntry getFirstTierPriceBook(String prodId, String customerId) {
    //     PricebookEntry priceBookEntry = new PricebookEntry();
    //     if (String.isNotBlank(prodId)) {
    //         List<Product2> prodInfo = [SELECT Id, Brand_Name__c,Item_Number__c,ProductCode
    //                                     ,CS_Exchange_Rate__c,Weight__c,OverSize__c FROM Product2 WHERE Id =:prodId
    //                                     AND Is_History_Product__c = :CCM_Constants.blHistoryProduct];
    //         if (prodInfo != null && prodInfo.size() > 0){
    //             if (String.isNotBlank(customerId)){
    //                 List<Sales_Program__c> authBrandList = [
    //                         SELECT Id, Customer__c, Brands__c, Customer__r.Distributor_or_Dealer__c,
    //                                Approval_Status__c, IsDeleted, Price_Book_Mapping__r.Name,
    //                                Price_Book__c, Contract_Price_List_Name__c, Contract_Price_Book__c
    //                         FROM Sales_Program__c
    //                         WHERE Customer__c =: customerId
    //                         AND Approval_Status__c = 'Approved'
    //                         AND Brands__c =: prodInfo[0].Brand_Name__c
    //                         AND RecordType.DeveloperName IN ('Dropship_Sales_Standard', 'Dropship_Sales_Customized')
    //                         AND IsDeleted = false LIMIT 1];
    //                 if (authBrandList != null && authBrandList.size() > 0){
    //                     Sales_Program__c authBrandInfo = authBrandList[0];
    //                     Boolean isContract = false;
    //                     if (authBrandInfo.Price_Book_Mapping__r.Name == 'Contract Price List'
    //                         && String.isNotBlank(authBrandInfo.Contract_Price_Book__c)){
    //                         isContract = true;
    //                     }
    //                     PricebookEntry prodEntry = new PricebookEntry();
    //                     List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
    //                     //更新特殊客户有主副价格册的逻辑 Modified By Abby On 7/10/2020
    //                     /*prodEntryList = Util.getPriceBookEntryByProdId(prodId, authBrandInfo.Price_Book__c);*/
    //                     prodEntryList = Util.getPriceBookEntryByProdId(prodId, authBrandInfo.Price_Book__c, isContract);
    //                     System.debug(LoggingLevel.INFO, '*** prodEntry1: ' + prodEntryList);
    //                     //如果当前的产品不再当前的价格册里，就去查找当前brand & Customer Type下的标准价格册（取Customize 和 Standard的合集产品）
    //                     if (prodEntryList == null || prodEntryList.size() == 0){
    //                         prodEntryList = Util.getStandardPricebookEntryByProdId(prodId, authBrandInfo.Brands__c,
    //                             authBrandInfo.Customer__r.Distributor_or_Dealer__c,'Sales');
    //                         System.debug(LoggingLevel.INFO, '*** prodEntry2: ' + prodEntryList);
    //                     }

    //                     if (prodEntryList != null && prodEntryList.size() > 0){
    //                         prodEntry = prodEntryList[0];
    //                     }
    //                     priceBookEntry = prodEntry;
    //                 }
    //             }
    //         }
    //     }

    //     return priceBookEntry;
    // }

    global static List<Sales_Program__c> getAuthorizedBrands(String customerId, String brand, String type) {
        List<Sales_Program__c> authBrandMatchList = new List<Sales_Program__c>();
        List<Sales_Program__c> authBrandList = [
                    SELECT Id, Customer__c, Brands__c, Customer__r.Distributor_or_Dealer__c,
                            Approval_Status__c, IsDeleted, Price_Book_Mapping__r.Name,
                            Price_Book__c, Contract_Price_List_Name__c, Contract_Price_Book__c,
                            RecordType.DeveloperName
                    FROM Sales_Program__c
                    WHERE Customer__c =: customerId
                    AND Approval_Status__c = 'Approved'
                    AND Brands__c =: brand];

        for(Sales_Program__c authBrand : authBrandList) {
            if(type == 'Sales') {
                if(authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_STANDARD_DEVELOPER_NAME || authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_DEVELOPER_NAME) {
                    authBrandMatchList.add(authBrand);
                }
            }
            if(type == 'Service') {
                if(authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_DEVELOPER_NAME || authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME || authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME) {
                    authBrandMatchList.add(authBrand);
                }
            }
            if(type == 'Dropship Sales') {
                if(authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_STANDARD_DEVELOPER_NAME || authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_CUSTOMIZED_DEVELOPER_NAME) {
                    authBrandMatchList.add(authBrand);
                }
            }
        }
        if(!authBrandMatchList.isEmpty()) {
            return authBrandMatchList;
        }
        return authBrandList;
    }

    global static  PricebookEntry getPartsOrderPriceBook(String prodId,String customerId) {
        PricebookEntry priceBookEntry = new PricebookEntry();
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        if (String.isNotBlank(prodId)){
            // 传入 的 productid 就是新的 product，is_history_product__c = false
            List<Product2> prodInfo = [
                                        SELECT Id,Name, Brand_Name__c,Item_Number__c,ProductCode
                                        FROM Product2 WHERE Id != NULL
                                        AND Id =:prodId
                                        AND Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                    ];
            if (prodInfo != null && prodInfo.size() > 0){
                if (String.isNotBlank(customerId)){
                    List<Account> accList = [SELECT Id,Distributor_or_Dealer__c,AccountNumber,ORG_Code__c  FROM Account WHERE Id =: customerId];
                    String priceBookId = '';
                    //Add by wells start at 2023-01-03====
                    if(accList.size() > 0 && accList[0].ORG_Code__c=='CCA'){
                        // AND Name = 'CCA-Distributor Price for Parts' // remove name filter condition
                        priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != null AND ORG_Code__c = 'CCA' AND Type__c = 'Service' AND Is_Active__c = true AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c LIMIT 1].Price_Book__c;
                    } else {
                        //Add by wells end=at 2023-01-03===
                        if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Dealer')){
                            priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Direct Dealer Price for Parts' AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c  LIMIT 1].Price_Book__c;
                        }else if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Distributor')){
                            priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Distributor Price for Parts' AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c LIMIT 1].Price_Book__c;
                        }
                        if(accList.size() > 0 && accList[0].AccountNumber == 'B10127'){
                            priceBookId =  [SELECT Id FROM Pricebook2 WHERE Name = 'CNA-MSRP for Parts' LIMIT 1].Id;
                        }
                    }
                    prodEntryList = Util.getPriceBookEntryByProdIdWithOutConvertCurrency(prodId, priceBookId);

                    if(accList[0].AccountNumber == 'B10127'){
                        if (prodEntryList != null && prodEntryList.size() > 0 && prodEntryList[0].UnitPrice != 0){
                            prodEntryList[0].UnitPrice = prodEntryList[0].UnitPrice * 0.7;
                            priceBookEntry  = prodEntryList[0];
                        }
                    }else{
                        if (prodEntryList != null && prodEntryList.size() > 0 && prodEntryList[0].UnitPrice != 0){
                            priceBookEntry  = prodEntryList[0];
                        }
                    }

                }
            }
        }
        return priceBookEntry;
    }

    global static  PricebookEntry getInternalPartsPriceBook(String prodId, String customerId){
        PricebookEntry priceBookEntry = new PricebookEntry();
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        if (String.isNotBlank(prodId)){
            List<Product2> prodInfo = [SELECT Id,Name, Brand_Name__c,Item_Number__c,ProductCode
                                        FROM Product2 WHERE Id != NULL
                                        AND Id =:prodId
                                        AND Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                        ];
            if (prodInfo != null && prodInfo.size() > 0){


                if (String.isNotBlank(customerId)){

                    List<Account> accList = [
                                            SELECT Id,Distributor_or_Dealer__c,AccountNumber,ORG_Code__c
                                            FROM Account WHERE Id != NULL
                                            AND Id =: customerId];
                                            //add Org_Code for price book by wells at 2023-01-03
                    String priceBookId = '';
                    //Add by wells start at 2023-01-03====
                    if(accList.size() > 0 && accList[0].ORG_Code__c=='CCA'){
                        priceBookId =  [
                                        SELECT Id,Price_Book__c
                                        FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != null
                                        AND ORG_Code__c = 'CCA'
                                        AND Type__c = 'Service'
                                        AND Is_Active__c = true
                                        AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c
                                        LIMIT 1].Price_Book__c;
                    } else{
                    //Add by wells end=at 2023-01-03===
                        if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Dealer')){
                            priceBookId =  [
                                            SELECT Id, Price_Book__c
                                            FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != NULL
                                            AND Type__c = 'Service'
                                            AND Is_Active__c = true
                                            AND Name = 'CNA-Direct Dealer Price for Parts'
                                            AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c
                                            LIMIT 1].Price_Book__c;
                        }else if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Distributor')){
                            priceBookId =  [
                                            SELECT Id, Price_Book__c
                                            FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != NULL
                                            AND Type__c = 'Service'
                                            AND Is_Active__c = true
                                            AND Name = 'CNA-Distributor Price for Parts'
                                            AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c
                                            LIMIT 1].Price_Book__c;
                        }
                        if(accList.size() > 0 && accList[0].AccountNumber == 'B10127'){
                            priceBookId =  [SELECT Id FROM Pricebook2 WHERE Name = 'CNA-MSRP for Parts' LIMIT 1].Id;
                        }
                    }
                    prodEntryList = Util.getPriceBookEntryByProdIdWithOutConvertCurrency(prodId, priceBookId);

                    if(accList[0].AccountNumber == 'B10127'){
                        if (prodEntryList != null && prodEntryList.size() > 0 && prodEntryList[0].UnitPrice != 0){
                            prodEntryList[0].UnitPrice = prodEntryList[0].UnitPrice * 0.7;
                            priceBookEntry  = prodEntryList[0];
                        }
                    }else{
                        if (prodEntryList != null && prodEntryList.size() > 0 && prodEntryList[0].UnitPrice != 0){
                            priceBookEntry  = prodEntryList[0];
                        }
                    }
                }
            }
        }

        return priceBookEntry;
    }
}