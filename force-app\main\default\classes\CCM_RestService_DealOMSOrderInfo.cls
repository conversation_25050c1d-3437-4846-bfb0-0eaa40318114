/**********************************************************************
 *
 *
 * @url: /services/apexrest/CCM_RestService_DealOMSOrderInfo
 * @data:
 *  {
    "Order_OracleID":"123",
    "Order_Number":"",
    "Salesforce_PurchaseOrder_Number": "C000001",
    "Customer": "B00240",
    "PO_Number": "1234576",
    "ShipTo": "22",
    "BillTo": "33",
    "Order_Type": "AA",
    "Price_List": "CC",
    "Sales_Rep": "DD" ,
    "Date_Order": "2019-10-24 00:00:00",
    "CurrencyCode": "USD",
    "Payment_Term": "2",
    "Freight_Term": "3",
    "Shipping_Method": "4",
    "Sales_Channel": "5",
    "Shipping_Priority": "6",
    "Notes": "7",
    "Expected_Delivery_Date": "2019-10-10 00:00:00",
    "Carrier_Code": "9",
    "Customer_Freight_Account": "10",
    "Org_Code": "CNA",
    "Handling_Fee": "123",
    "Feright_Fee": "3",
    "Order_Status": "1",
    "Error_Message": "",
    "Dropship_Name": "6",
    "Dropship_Address1": "7",
    "Dropship_Address2": "8",
    "Telephone_Number": "9",
    "Dropship_Country": "10",
    "Dropship_ZIP": "11",
    "Dropship_State": "123",
    "Dropship_City": "3",
    "Attribute1": "4",
    "Attribute2": "5",//whole order promotion code
    "Attribute3": "6",//payment term promotion code
    "Attribute4": "7",//GST
    "Attribute5": "8",//HST
    "Attribute6": "8",//QST
    "OrderLine": [
        {
            "OrderLine_OracleID": "1",
            "Salesforce_PurchaseOrderLine_Number": "1",
            "Product": "SPT2008-05",
            "Order_Quantity": "1",
            "Reverse_Quantity": "1",
            "Cannel_Quantity": "1",
            "Line_Price_List": "1",
            "Price": "1",
            "Request_Date": "2019-12-31 00:00:00",
            "Ship_Date": "2019-12-31 00:00:00",
            "Line_Type": "1",
            "Promation_Code": "2",
            "isHold":"Y",
            "Hold_Reason":"1111",
            "Line_Status": "3",
            "Line_Attribute1": "4",
            "Line_Attribute2": "5",
            "Line_Attribute3": "6",//promotion code
            "Line_Attribute4": "7",
            "Line_Attribute5": "8"
        }
    ]
}
*************************************************************************/
@RestResource(urlMapping='/CCM_RestService_DealOMSOrderInfo')
global with sharing class CCM_RestService_DealOMSOrderInfo {
    // @HttpPost
    // global static ResultObj doPost() {
    // 	RestRequest req = RestContext.request;
    // 	ReqestObj reqObj;
    // 	ResultObj resObj = new ResultObj();
    //     try{
    //         System.debug(LoggingLevel.INFO, '*** req.requestBody.toString(): ' + req.requestBody.toString());
    //         reqObj = parse(req.requestBody.toString());
    //         String OrderOracleID = reqObj.Order_OracleID;
    //         String sfpNumber = reqObj.Salesforce_PurchaseOrder_Number;
    //         List<Messaging.SingleEmailMessage> mailsToSend = new List<Messaging.SingleEmailMessage>();
    //         if(CCM_Service.isValid(sfpNumber,Order.sObjectType)){
    //             Order order2 = [select id from Order where id = :sfpNumber];
    //             order2.Order_OracleID__c = OrderOracleID;
    //             order2.Order_Number__c = reqObj.Order_Number;
    //             order2.IsHold__c = reqObj.IsHold;
    //             order2.Hold_Reason__c = reqObj.Hold_Reason;
    //             update order2;
    //         }else{
    //             // 处理 order 逻辑
    //             List<Order> orderlist = [
    //                                         select id,Handling_Fee__c
    //                                         ,Feright_Fee__c,Total_Amount__c, Dropship_Customer__c
    //                                         ,Whole_Order_Promotion_Code__c
    //                                         ,Payment_Term_Promotion_Code__c
    //                                         ,Whole_Order_Promotion_Window__c
    //                                         ,Payment_Term_Promotion_Window__c
    //                                         ,Org_Code__c
    //                                         ,CurrencyIsoCode
    //                                         from Order where id != null
    //                                         and Order_OracleID__c = :OrderOracleID
    //                                         FOR UPDATE
    //                                         ];

    //             String Salesforce_PurchaseOrder_Number = reqObj.Salesforce_PurchaseOrder_Number;
    //             // query promotion and promotion window information on purchase order - update on 2021-05-05
    //             List<Purchase_Order__c> polist = new List<Purchase_Order__c>();
    //             if (reqObj.Order_Type != 'CNA Sample Order Only') {
    //                 polist = [select id,Sync_Status__c,Sync_Message__c,RecordType.DeveloperName,Discount_Amount__c,Total_Amount__c,Freight_Fee__c ,Handling_Fee__c, Customer__r.OwnerId, Customer__r.Owner.SenderEmail,Customer__r.Owner.Email,Actual_Freight_Fee__c,
    //                                 Payment_Term_Promotion__c,Whole_Order_Promotion__c,Whole_Order_Promotion_Window__c, Payment_Term_Promotion_Window__c,ORG_ID__c, Dropship_Customer__c,
    //                                 (SELECT id,Whole_Order_Promotion__c,Whole_Order_Promotion__r.Promotion_Code_For_External__c,Promotion__r.Promotion_Code_For_External__c,Promotion__c,
    //                                 Regular_Promotion_Window__c
    //                                 FROM Purchase_Order_Items__r)
    //                                 from Purchase_Order__c
    //                                 where Name = :Salesforce_PurchaseOrder_Number];
    //             }
    //             List<Purchase_Order_Item__c> allpoItemList = new List<Purchase_Order_Item__c>();
    //             List<Sample_Order__c> sampleList = new List<Sample_Order__c>();
    //             if (reqObj.Order_Type == 'CNA Sample Order Only') {
    //                 sampleList = [SELECT Id, OwnerId, Name, CurrencyIsoCode, RecordTypeId, Sales_Group__c, Contact_Name__c, Contact_Phone__c, Contact_Email__c, Street__c, City__c, State__c, Sample_Order_Number__c,
    //                                     Zip_Code__c, Selected_Plan__c, Order_Status__c, Total_Amount__c, Reason__c, Demo_Free_Goods_Program__c, Task_Owner__c, Expected_Delivery_Date__c, Requestor_Email__c, Requester_Phone__c,
    //                                     Initial_Submitter__c, Initial_Submitter_Role__c, Regional_Manager__c, Submit_Time__c,
    //                                     (SELECT Id, Name, Product__c, Quantity__c, MSRP_Price__c, Invoice_Price__c, Sample_Order__c, Subtotal__c, Demo_Plan_Item__c, Price_Book__c, Demo_Plan__c
    //                                         FROM Sample_Order_Items__r)
    //                                 FROM Sample_Order__c WHERE Name = :Salesforce_PurchaseOrder_Number];
    //             }
    //             List<Sample_Order_Item__c> allsoItemList = new List<Sample_Order_Item__c>();
    //             Map<String,Id> promoCodePromotionMap = new Map<String,Id>();
    //             Map<String,Id> wholeCodePromotionMap = new Map<String,Id>();
    //             Map<String,Id> promoCodePromotionWindowMap = new Map<String,Id>();
    //             List<Promotion_Window__c> promotionWindowList = new List<Promotion_Window__c>();
    //             String PRecordTypeName = '';
    //             Decimal discount = 0;
    //             if(polist.size() > 0){
    //                 PRecordTypeName = polist.get(0).RecordType.DeveloperName;
    //                 if(polist.get(0).Discount_Amount__c != null){
    //                     discount = polist.get(0).Discount_Amount__c;
    //                 }
    //                 allpoItemList=polist.get(0).Purchase_Order_Items__r;
    //                 if (allpoItemList.size()>0) {
    //                     for(Purchase_Order_Item__c poi:allpoItemList){
    //                         // store promotion information - update on 2021-05-05
    //                         wholeCodePromotionMap.put(poi.Whole_Order_Promotion__r.Promotion_Code_For_External__c,poi.Whole_Order_Promotion__c);
    //                         promoCodePromotionMap.put(poi.Promotion__r.Promotion_Code_For_External__c,poi.Promotion__c);
    //                         promoCodePromotionWindowMap.put(poi.Promotion__r.Promotion_Code_For_External__c,poi.Regular_Promotion_Window__c);
    //                     }
    //                 }
    //             }
    //             // 失败有值，成功没值。先失败，后成功，应该覆盖历史值
    //             String errorMsg = reqObj.Error_Message;
    //             // 更新 purchase,sample order 的状态和 error message
    //             if(String.isNotBlank(errorMsg)){
    //                 // purchase order
    //                 if (reqObj.Order_Type != 'CNA Sample Order Only') {
    //                     if(polist.size() > 0){
    //                         Purchase_Order__c po2 = polist.get(0);
    //                         po2.Sync_Message__c = errorMsg;
    //                         po2.Sync_Status__c = 'Failed';
    //                         update po2;
    //                         resObj.returnCode = 'S';
    //                         return resObj;
    //                     } else {
    //                         resObj.returnCode = 'F';
    //                         resObj.returnMsg = 'Can not find the error original purchase order!';
    //                         return resObj;
    //                     }
    //                     // sample order
    //                 } else {
    //                     if(samplelist.size() > 0){
    //                         resObj.returnCode = 'S';
    //                         return resObj;
    //                     }else{
    //                         resObj.returnCode = 'F';
    //                         resObj.returnMsg = 'Can not find the error original purchase order!';
    //                         return resObj;
    //                     }
    //                 }

    //             } else {
    //                 if (polist.size() > 0) {
    //                     Purchase_Order__c po2 = polist.get(0);
    //                     po2.Sync_Message__c = '';
    //                     po2.Sync_Status__c = 'Success';
    //                     update po2;
    //                 }
    //             }

    //             //update by nick 20200518
    //             String OrderStatus = reqObj.Order_Status;
    //             String OrderType = reqObj.Order_Type;
    //             // reverse order
    //             if (OrderType == 'CNA Return - Sales & Inv.Adj.' || OrderType == 'CA Return - Sales & Inv.Adj.') {
    //                 if (OrderStatus == 'AWAITING_RETURN') {
    //                     OrderStatus = 'Booked';
    //                 }
    //             } else {
    //                 if (OrderStatus == 'CLOSED') {
    //                     OrderStatus = 'Ship Complete';
    //                 }

    //                 if (OrderStatus == 'ENTERED') {
    //                     OrderStatus = 'Booked';
    //                 }

    //                 if(OrderStatus == 'BOOKED'){
    //                     if(reqObj.OrderLine != null && reqObj.OrderLine.size() > 0){
    //                         Integer orderItemSize = reqObj.OrderLine.size();
    //                         Integer orderProcessingIndex = 0;
    //                         Integer shipCompleteIndex = 0;
    //                         Integer cancelledIndex = 0;
    //                         Integer partialShipmentIndex = 0;
    //                         // partial 标志位， 如果这个为 true，证明有可能是 partial shipment
    //                         Boolean blPartial = false;
    //                         for(OrderLine ol : reqObj.OrderLine){
    //                             String lineStatus = ol.Line_Status;
    //                             if(lineStatus == 'PICKED' || lineStatus == 'AWAITING_SHIPPING' ){
    //                                 orderProcessingIndex += 1;
    //                                 blPartial =  true;
    //                             }
    //                             if(lineStatus == 'INVOICED' || lineStatus == 'SHIPPED'){
    //                                 shipCompleteIndex += 1;
    //                                 partialShipmentIndex += 1;
    //                             }
    //                             if(lineStatus == 'CANCELLED'){
    //                                 cancelledIndex += 1;
    //                                 blPartial = true;
    //                             }
    //                         }
    //                         if((orderProcessingIndex == orderItemSize) || (orderProcessingIndex > 0
    //                             && orderProcessingIndex + cancelledIndex == orderItemSize)){
    //                             OrderStatus = 'Order Processing';
    //                         } else if(shipCompleteIndex == orderItemSize){
    //                             OrderStatus = 'Ship Complete';
    //                         } else if(cancelledIndex == orderItemSize){
    //                             OrderStatus = 'Cancelled';
    //                             // Partial shipment = 部分发运，完成数 + 取消数 < 订单总数，订单包含了  PICKED || AWAITING_SHIPPING
    //                         } else if(partialShipmentIndex > 0 && blPartial == true && (shipCompleteIndex + cancelledIndex) < orderItemSize){
    //                             OrderStatus = 'Partial Shipment';
    //                         } else if(shipCompleteIndex > 0 && (shipCompleteIndex + cancelledIndex) == orderItemSize) {
    //                             OrderStatus = 'Ship Complete';
    //                         }
    //                     }
    //                 }
    //             }
    //             List<String> purchaseOrderLineID = new List<String>();
    //             Map<String,Id> purchaseOrderLineMap = new Map<String,Id>();
    //             List<String> proIdlist = new List<String>();
    //             Map<String,Id> proMap = new Map<String,Id>();
    //             List<String> pricebookIdlist = new List<String>();
    //             Map<String,Id> pricebookMap = new Map<String,Id>();
    //             List<String> orderlineIDlist = new List<String>();

    //             Map<String,Id> proEBSMap = new Map<String,Id>();
    //             Set<String> allPromotionCodeSet = new Set<String>();
    //             Set<String> brandSet = new Set<String>();

    //             Decimal totalAmount = 0;
    //             if(reqObj.OrderLine != null && reqObj.OrderLine.size() > 0){
    //                 for(OrderLine ol : reqObj.OrderLine){
    //                     if(!purchaseOrderLineID.contains(ol.Salesforce_PurchaseOrderLine_Number)){
    //                         purchaseOrderLineID.add(ol.Salesforce_PurchaseOrderLine_Number);
    //                     }
    //                     if(!proIdlist.contains(ol.Product)){
    //                         proIdlist.add(ol.Product);
    //                     }
    //                     if(!pricebookIdlist.contains(ol.Line_Price_List)){
    //                         pricebookIdlist.add(ol.Line_Price_List);
    //                     }
    //                     if(!orderlineIDlist.contains(ol.OrderLine_OracleID)){
    //                         orderlineIDlist.add(ol.OrderLine_OracleID);
    //                     }
    //                     if(String.isNotBlank(ol.Price) && String.isNotBlank(ol.Order_Quantity)){
    //                         Decimal lineAmount = Decimal.valueOf(ol.Price) * Decimal.valueOf(ol.Order_Quantity);
    //                         totalAmount = totalAmount + lineAmount;
    //                     }
    //                     if(String.isNotBlank(ol.Promation_Code)){
    //                         allPromotionCodeSet.add(ol.Promation_Code);
    //                     }
    //                 }
    //             }
    //             if(String.isNotBlank(reqObj.Attribute2)){
    //                 allPromotionCodeSet.add(reqObj.Attribute2);
    //             }
    //             if(String.isNotBlank(reqObj.Attribute3)){
    //                 allPromotionCodeSet.add(reqObj.Attribute3);
    //             }
    //             List<Purchase_Order_Item__c> poilist = new List<Purchase_Order_Item__c>();
    //             List<Sample_Order_Item__c> soilist = new List<Sample_Order_Item__c>();
    //             Map<String,Id> sampleOrderLineMap = new Map<String,Id>();
    //             if (reqObj.Order_Type != 'CNA Sample Order Only') {
    //                 poilist = [select id,Name,Whole_Order_Promotion__c,Whole_Order_Promotion__r.Promotion_Code_For_External__c from Purchase_Order_Item__c where Name in :purchaseOrderLineID];
    //                 for(Purchase_Order_Item__c poi : poilist){
    //                     purchaseOrderLineMap.put(poi.Name, poi.Id);
    //                 }
    //             } else if (reqObj.Order_Type == 'CNA Sample Order Only') {
    //                 soilist = [SELECT Id FROM Sample_Order_Item__c WHERE Name IN :purchaseOrderLineID];
    //                 for(Sample_Order_Item__c poi : soilist){
    //                     sampleOrderLineMap.put(poi.Name, poi.Id);
    //                 }
    //             }
    //             List<Product2> prolist = [select id,ProductCode,Name,Source__c, Brand_Name__c
    //                     from Product2 where ProductCode in :proIdlist
    //                     and Source__c = 'PIM'
    //                     AND IsCreatedByCode__c = false
    //                     AND Is_History_Product__c = :CCM_Constants.blHistoryProduct];
    //             for(Product2 pro : prolist){
    //                 if(!pro.Name.contains('AO ONLY')){
    //                     proMap.put(pro.ProductCode, pro.Id);
    //                     brandSet.add(String.isEmpty(pro.Brand_Name__c) ? null : pro.Brand_Name__c.toUpperCase());
    //                 }
    //             }

    //             List<Product2> proEBSlist = [select id,ProductCode,Name,Source__c, Brand_Name__c
    //                     from Product2 where ProductCode in :proIdlist
    //                     and Source__c = 'EBS'
    //                     AND IsCreatedByCode__c = false
    //                     AND Is_History_Product__c = :CCM_Constants.blHistoryProduct];
    //             for(Product2 pro : proEBSlist){
    //                 if(!pro.Name.contains('AO ONLY')){
    //                     proEBSMap.put(pro.ProductCode, pro.Id);
    //                     brandSet.add(String.isEmpty(pro.Brand_Name__c) ? null : pro.Brand_Name__c.toUpperCase());
    //                 }
    //             }

    //             List<PriceBook2> pricebooklist = [select id,Price_Book_OracleID__c,Name from PriceBook2 where Name in :pricebookIdlist];
    //             for(PriceBook2 pb : pricebooklist){
    //                 pricebookMap.put(pb.Name, pb.Id);
    //             }
    //             // upsert order 对象记录
    //             if(orderlist.size() > 0){
    //                 Order o = orderlist.get(0);
    //                 Boolean isPlaceOrder = false;
    //                 if(PRecordTypeName == 'Place_Parts_Order'){
    //                     o.RecordTypeId = CCM_Contants.PLACE_PARTS_ORDER_RECORDTYPEID;
    //                 }else{
    //                     //update by austin,给order dropship customer赋值
    //                     o.RecordTypeId = CCM_Contants.PLACE_ORDER_RECORDTYPEID;
    //                     o.Dropship_Customer__c = polist.size() > 0 ? polist.get(0).Dropship_Customer__c : null;
    //                     isPlaceOrder = true;
    //                 }

    //                 o.Order_Number__c = reqObj.Order_Number;
    //                 if(polist.size() > 0){
    //                     o.Purchase_Order__c = polist.get(0).Id;
    //                 }
    //                 o.Purchase_Order_Text__c = Salesforce_PurchaseOrder_Number;

    //                 // if reqObj.Attribute7 start with 'RO' set reverse order request to order
    //                 List<Reverse_Order_Item__c> reverseItems = new List<Reverse_Order_Item__c>();
    //                 Reverse_Order_Request__c reverseRequest;
    //                 if (String.isNotEmpty(reqObj.Attribute7)
    //                     && reqObj.Attribute7.startsWith('RO')
    //                     && (reqObj.Order_Type == 'CNA Return - Sales & Inv.Adj.'
    //                         || reqObj.Order_Type == 'CA Return - Sales & Inv.Adj.')) {
    //                     List<Reverse_Order_Request__c> reverseOrderRequests = [SELECT Id,
    //                                                             Reverse_Order_Type__c,
    //                                                             CreatedById,
    //                                                             CreatedBy.Profile.Name,
    //                                                             CreatedBy.Email,
    //                                                             Customer__r.Owner.Email,
    //                                                             (SELECT Id,
    //                                                                 Qty__c,
    //                                                                 Next_Step_Action__c,
    //                                                                 Product2__c,
    //                                                                 Product2__r.ProductCode,
    //                                                                 Warehouse_Received_Product_Amount__c,
    //                                                                 Warehouse_Received_Qty__c,
    //                                                                 Warehouse_Received_Subtotal__c,
    //                                                                 Warehouse_Received_Total_Quantity__c,
    //                                                                 Warehouse_Return_Number__c
    //                                                                 FROM Reverse_Order_Items__r)
    //                                                         FROM Reverse_Order_Request__c
    //                                                         WHERE Reverse_Order_Request_Number__c = :reqObj.Attribute7];
    //                     o.Reverse_Order_Request__c = reverseOrderRequests[0].Id;
    //                     reverseRequest = reverseOrderRequests[0];
    //                     reverseRequest.Return_Order_Number__c = reqObj.Order_OracleID;
    //                     reverseItems = reverseOrderRequests[0].Reverse_Order_Items__r;
    //                 }

    //                 String customerAccountNumber = reqObj.Customer;
    //                 List<Account> acclist = [select id from Account where AccountNumber = :customerAccountNumber];
    //                 if(acclist.size() > 0){
    //                     o.AccountId = acclist.get(0).Id;
    //                     if (reqObj.Order_Type != 'CNA Sample Order Only') {
    //                         initialPromotionInfos(promotionWindowList, o.AccountId, allPromotionCodeSet);
    //                     }
    //                 }
    //                 if(isPlaceOrder && acclist.size() == 0){
    //                     String eMsg = Label.CCM_ErrorMsg_NotFindCustomer;
    //                     throw new OrderException(eMsg.replace('{0}', 'Order').replace('{1}', reqObj.Order_OracleID).replace('{2}', reqObj.Customer));
    //                 }
    //                 o.Account_Text__c = customerAccountNumber;
    //                 o.EffectiveDate = Date.today();
    //                 o.PO_Number__c = reqObj.PO_Number;
    //                 String shipTo = reqObj.ShipTo;
    //                 List<Address_With_Program__c> shipAPlist = [select id, Account_Address__c,
    //                                                     Account_Address__r.Store_Location__c, Account_Address__r.EGO_Agency__c,
    //                                                     Account_Address__r.SKIL_SKILSAW_Agency__c,
    //                                                     Account_Address__r.FLEX_Agency__c
    //                                                     from Address_With_Program__c where Customer_Line_Oracle_ID__c = :shipTo];
    //                 if(shipAPlist.size() > 0){
    //                     o.ShipTo__c = shipAPlist.get(0).Id;
    //                     o.Store_Location__c = shipAPlist.get(0).Account_Address__c != null ? shipAPlist.get(0).Account_Address__r.Store_Location__c : null;
    //                 }
    //                 o.ShipTo_Text__c = shipTo;
    //                 String billTo = reqObj.BillTo;
    //                 List<Address_With_Program__c> billAPlist = [select id,Program__r.OwnerId,Program__c
    //                                                     ,Program__r.Sales_Group__c
    //                                                     from Address_With_Program__c where Customer_Line_Oracle_ID__c = :billTo];
    //                 if(billAPlist.size() > 0){
    //                     o.BillTo__c = billAPlist.get(0).Id;
    //                     if(billAPlist.get(0).Program__c != null){
    //                         o.OwnerId = billAPlist.get(0).Program__r.OwnerId;
    //                     }

    //                     o.Sales_Group__c = billAPlist.get(0).Program__r.Sales_Group__c;
    //                 }
    //                 o.BillTo_Text__c = billTo;
    //                 o.Order_Type__c = reqObj.Order_Type;
    //                 String PriceList = reqObj.Price_List;
    //                 List<PriceBook2> pricebooklist2 = [select id,Price_Book_OracleID__c,Name from PriceBook2 where Name = :PriceList];
    //                 if(pricebooklist2.size() > 0){
    //                     o.Price_Book__c = pricebooklist2.get(0).Id;
    //                 }
    //                 o.Price_Book_Text__c = PriceList;
    //                 o.Sales_Rep__c = reqObj.Sales_Rep;
    //                 // get Sales Agency by sales_rep Code
    //                 if (String.isNotBlank(reqObj.Sales_Rep)){
    //                     Id agencyId = Util.getSalesAgencyIdByOracleId(reqObj.Sales_Rep);
    //                     o.Sales_Agency__c = agencyId;
    //                 }
    //                 if (String.isEmpty(reqObj.Org_Code) || reqObj.Org_Code == CCM_Constants.ORG_CODE_CNA && shipAPlist.size() > 0) {
    //                     if (brandSet.size() == 1 && brandSet.contains('EGO') && String.isNotEmpty(shipAPlist[0].Account_Address__r.EGO_Agency__c)) {
    //                         o.Sales_Agency__c = shipAPlist[0].Account_Address__r.EGO_Agency__c;
    //                     } else if (brandSet.size() >=1 && !brandSet.contains('EGO') && (String.isNotEmpty(shipAPlist[0].Account_Address__r.SKIL_SKILSAW_Agency__c) || String.isNotEmpty(shipAPlist[0].Account_Address__r.FLEX_Agency__c))) {
    //                         o.Sales_Agency__c = String.isNotEmpty(shipAPlist[0].Account_Address__r.SKIL_SKILSAW_Agency__c) ? shipAPlist[0].Account_Address__r.SKIL_SKILSAW_Agency__c : shipAPlist[0].Account_Address__r.FLEX_Agency__c;
    //                     }
    //                 }
    //                 o.Date_Order__c = String.isNotBlank(reqObj.Date_Order) ? Datetime.valueOf(reqObj.Date_Order) : null;
    //                 o.CurrencyIsoCode = reqObj.CurrencyCode;
    //                 o.Payment_Term__c = reqObj.Payment_Term;
    //                 o.Freight_Term__c = reqObj.Freight_Term;
    //                 o.Shipping_Method__c = reqObj.Shipping_Method;
    //                 o.Sales_Channel__c = reqObj.Sales_Channel;
    //                 o.Shipping_Priority__c = reqObj.Shipping_Priority;
    //                 o.Notes__c = reqObj.Notes;
    //                 o.Expected_Delivery_Date__c = String.isNotBlank(reqObj.Expected_Delivery_Date) ? Date.valueOf(reqObj.Expected_Delivery_Date) : null;
    //                 o.Carrier_Code__c = reqObj.Carrier_Code;
    //                 o.Customer_Freight_Account__c = reqObj.Customer_Freight_Account;
    //                 o.Handling_Fee__c = String.isNotBlank(reqObj.Handling_Fee) ? Decimal.valueOf(reqObj.Handling_Fee) : null;
    //                 o.Feright_Fee__c = String.isNotBlank(reqObj.Feright_Fee) ? Decimal.valueOf(reqObj.Feright_Fee) : null;
    //                 o.Dropship_Name__c = reqObj.Dropship_Name;
    //                 o.Dropship_Address1__c = reqObj.Dropship_Address1;
    //                 o.Dropship_Address2__c = reqObj.Dropship_Address2;
    //                 o.Telephone_Number__c = reqObj.Telephone_Number;
    //                 o.Dropship_Country__c = reqObj.Dropship_Country;
    //                 o.Dropship_ZIP__c = reqObj.Dropship_ZIP;
    //                 o.Dropship_State__c = reqObj.Dropship_State;
    //                 o.Dropship_City__c = reqObj.Dropship_City;
    //                 o.Org_Code__c = reqObj.Org_Code;
    //                 o.Order_Status__c = OrderStatus;
    //                 o.IsHold__c = reqObj.IsHold;
    //                 o.Hold_Reason__c = reqObj.Hold_Reason;
    //                 String source = reqObj.Order_Source;
    //                 source = OrderSourceMap.get(source) != null ? OrderSourceMap.get(source) : source;
    //                 o.Order_Source__c = source;
    //                 List<Warranty_Return_Claim__c> warrantyReturnList = [SELECT Id FROM Warranty_Return_Claim__c WHERE Name = :reqObj.Salesforce_PurchaseOrder_Number];
    //                 o.Warranty_Return_Request__c = warrantyReturnList.size() > 0 ? warrantyReturnList[0].Id : null;
    //                 if (sampleList != null && sampleList.size() > 0){
    //                     o.Sample_Order__c = sampleList[0].Id;
    //                 }

    //                 if(String.isNotBlank(reqObj.Handling_Fee)){
    //                     totalAmount = totalAmount + Decimal.valueOf(reqObj.Handling_Fee);
    //                 }
    //                 if(String.isNotBlank(reqObj.Feright_Fee)){
    //                     totalAmount = totalAmount + Decimal.valueOf(reqObj.Feright_Fee);
    //                 }

    //                 /* 2021-06-09 Fix total amount issue in Order detail page.*/
    //                 if(PRecordTypeName == 'Place_Parts_Order'){
    //                     totalAmount = totalAmount - discount;
    //                 }

    //                 //whole order&payment term promotion
    //                 if(String.isNotBlank(reqObj.Attribute2)){
    //                     o.Whole_Order_Promotion_Code__c = reqObj.Attribute2;
    //                     populatePromotionByCode(null, o, reqObj.Attribute2, promotionWindowList, true, false);
    //                 }
    //                 if(String.isNotBlank(reqObj.Attribute3)){
    //                     o.Payment_Term_Promotion_Code__c = reqObj.Attribute3;
    //                     populatePromotionByCode(null, o, reqObj.Attribute3, promotionWindowList, false, true);
    //                 }

    //                 o.Total_Amount__c = totalAmount;
    //                 o.Status = 'Draft';

    //                 //CA tax
    //                 if(String.isNotBlank(reqObj.Attribute4)){
    //                     o.GST__c = Decimal.valueOf(reqObj.Attribute4);
    //                     o.Total_Amount__c+=o.GST__c;
    //                 }
    //                 if(String.isNotBlank(reqObj.Attribute5)){
    //                     o.HST__c = Decimal.valueOf(reqObj.Attribute5);
    //                     o.Total_Amount__c+=o.HST__c;
    //                 }
    //                 if(String.isNotBlank(reqObj.Attribute6)){
    //                     o.QST__c = Decimal.valueOf(reqObj.Attribute6);
    //                     o.Total_Amount__c+=o.QST__c;
    //                 }

    //                 //Surcharge Amount
    //                 if(String.isNotBlank(reqObj.Attribute8)){
    //                     o.Surcharge_Amount__c = Decimal.valueOf(reqObj.Attribute8);
    //                     o.Total_Amount__c+=o.Surcharge_Amount__c;
    //                 }

    //                 update o;

    //                 List<Order_Item__c> oilist = [select id,OrderLine_OracleID__c from Order_Item__c
    //                                             where OrderLine_OracleID__c in :orderlineIDlist
    //                                             And Order__c = :o.Id];
    //                 List<Order_Item__c> oiInsertList = new List<Order_Item__c>();
    //                 List<Order_Item__c> oiUpdateList = new List<Order_Item__c>();
    //                 Map<String,Order_Item__c> orderItemMap = new Map<String,Order_Item__c>();
    //                 for(Order_Item__c oi : oilist){
    //                     orderItemMap.put(oi.OrderLine_OracleID__c, oi);
    //                 }

    //                 Decimal reverseReturnProductQty = 0;
    //                 Set<String> reverseOrderLineStatus = new Set<String>();
    //                 if(reqObj.OrderLine != null && reqObj.OrderLine.size() > 0){
    //                     for(OrderLine ol : reqObj.OrderLine){
    //                         if(orderItemMap.get(ol.OrderLine_OracleID)!=null){
    //                             Order_Item__c oi = orderItemMap.get(ol.OrderLine_OracleID);
    //                             oi.Order__c = o.Id;
    //                             oi.CurrencyIsoCode = o.CurrencyIsoCode;
    //                             oi.Sample_Order_Item__c = sampleOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) != null ? sampleOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) : null;
    //                             if(purchaseOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) != null){
    //                                 oi.Purchase_Order_Item__c = purchaseOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number);
    //                             }
    //                             if(String.isBlank(ol.Product) && isPlaceOrder && (ol.Line_Attribute5 == 'FG' || ol.Line_Attribute5 == 'MKT')){
    //                                 String eMsg = Label.CCM_ErrorMsg_LackOfProductInfo;
    //                                 throw new OrderException(eMsg.replace('{0}', 'Order').replace('{1}', ol.OrderLine_OracleID));
    //                             }
    //                             if(proMap.get(ol.Product) != null){
    //                                 oi.Product__c = proMap.get(ol.Product);
    //                             }else if(proEBSMap.get(ol.Product) != null){
    //                                 oi.Product__c = proEBSMap.get(ol.Product);
    //                             }else if(isPlaceOrder && (ol.Line_Attribute5 == 'FG' || ol.Line_Attribute5 == 'MKT')){
    //                                 String eMsg = Label.CCM_ErrorMsg_NotFindProduct;
    //                                 throw new OrderException(eMsg.replace('{0}', 'Order').replace('{1}', ol.OrderLine_OracleID).replace('{2}', ol.Product));
    //                             }
    //                             oi.Product_Text__c = ol.Product;
    //                             oi.Order_Quantity__c = String.isNotBlank(ol.Order_Quantity) ? Decimal.valueOf(ol.Order_Quantity) : null;
    //                             oi.Reverse_Quantity__c = String.isNotBlank(ol.Reverse_Quantity) ? Integer.valueOf(ol.Reverse_Quantity) : null;
    //                             oi.Cannel_Quantity__c = String.isNotBlank(ol.Cannel_Quantity) ? Integer.valueOf(ol.Cannel_Quantity) : null;
    //                             oi.Price__c = String.isNotBlank(ol.Price) ? Decimal.valueOf(ol.Price) : null;
    //                             oi.Item_Type__c = ol.Line_Attribute5;
    //                             oi.List_Price__c = String.isNotBlank(ol.Line_Attribute6) ? Decimal.valueOf(ol.Line_Attribute6) : null;
    //                             oi.Request_Date__c = String.isNotBlank(ol.Request_Date) ? Datetime.valueOf(ol.Request_Date) : null;
    //                             oi.Ship_Date__c = String.isNotBlank(ol.Ship_Date) ? Date.valueOf(ol.Ship_Date) : null;
    //                             oi.Line_Type__c = ol.Line_Type;
    //                             if(pricebookMap.get(ol.Line_Price_List) != null){
    //                                 oi.Price_Book__c = pricebookMap.get(ol.Line_Price_List);
    //                             }
    //                             oi.Price_Book_Text__c = ol.Line_Price_List;

    //                             Set<String> codeList = new Set<String>();
    //                             if(String.isNotBlank(ol.Promation_Code)){
    //                                 codeList.add(ol.Promation_Code);
    //                             }

    //                             if(String.isNotBlank(o.Whole_Order_Promotion_Code__c)){
    //                                 codeList.add(o.Whole_Order_Promotion_Code__c);
    //                             }

    //                             if(String.isNotBlank(o.Payment_Term_Promotion_Code__c)){
    //                                 codeList.add(o.Payment_Term_Promotion_Code__c);
    //                             }

    //                             if(codeList.size() >0){
    //                                 oi.Promation_Code__c = String.join(new List<String>(codeList), ',');
    //                             }else{
    //                                 oi.Promation_Code__c = ol.Promation_Code;
    //                             }
    //                             //populate promotion on order item - update on 2021-05-05
    //                             if(String.isNotBlank(ol.Promation_Code)){
    //                                 populatePromotionByCode(oi, o, ol.Promation_Code, promotionWindowList, false, false);
    //                             }
    //                             oi.Line_Status__c = ol.Line_Status;
    //                             oi.isHold__c = ol.Line_IsHold;
    //                             oi.Hold_Reason__c = ol.Line_Hold_Reason;

    //                             // add 2021-07-13
    //                             oi.Selling_Warehouse__c = ol.Line_Attribute4;

    //                             //populate org code for order item
    //                             oi.Org_Code__c = o.Org_Code__c;

    //                             //line surcharge amount
    //                             if(String.isNotBlank(ol.Line_Attribute7)){
    //                                 oi.Surcharge_Amount__c = Decimal.valueOf(ol.Line_Attribute7);
    //                             }
    //                             if(String.isNotBlank(ol.Line_Attribute8)){
    //                                 oi.Business_Purpose__c = String.valueOf(ol.Line_Attribute8);
    //                             }

    //                             oiUpdateList.add(oi);
    //                         } else {
    //                             Order_Item__c oi = new Order_Item__c();
    //                             oi.Order__c = o.Id;
    //                             oi.CurrencyIsoCode = o.CurrencyIsoCode;
    //                             oi.OrderLine_OracleID__c = ol.OrderLine_OracleID;
    //                             if(purchaseOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) != null){
    //                                 oi.Purchase_Order_Item__c = purchaseOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number);
    //                             }
    //                             oi.Sample_Order_Item__c = sampleOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) != null ? sampleOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) : null;
    //                             if(String.isBlank(ol.Product) && isPlaceOrder && (ol.Line_Attribute5 == 'FG' || ol.Line_Attribute5 == 'MKT')){
    //                                 String eMsg = Label.CCM_ErrorMsg_LackOfProductInfo;
    //                                 throw new OrderException(eMsg.replace('{0}', 'Order').replace('{1}', ol.OrderLine_OracleID));
    //                             }
    //                             if(proMap.get(ol.Product) != null){
    //                                 oi.Product__c = proMap.get(ol.Product);
    //                             }else if(proEBSMap.get(ol.Product) != null){
    //                                 oi.Product__c = proEBSMap.get(ol.Product);
    //                             }else if(isPlaceOrder && (ol.Line_Attribute5 == 'FG' || ol.Line_Attribute5 == 'MKT')){
    //                                 String eMsg = Label.CCM_ErrorMsg_NotFindProduct;
    //                                 throw new OrderException(eMsg.replace('{0}', 'Order').replace('{1}', ol.OrderLine_OracleID).replace('{2}', ol.Product));
    //                             }
    //                             oi.Product_Text__c = ol.Product;
    //                             oi.Order_Quantity__c = String.isNotBlank(ol.Order_Quantity) ? Decimal.valueOf(ol.Order_Quantity) : null;
    //                             oi.Reverse_Quantity__c = String.isNotBlank(ol.Reverse_Quantity) ? Integer.valueOf(ol.Reverse_Quantity) : null;
    //                             oi.Cannel_Quantity__c = String.isNotBlank(ol.Cannel_Quantity) ? Integer.valueOf(ol.Cannel_Quantity) : null;
    //                             oi.Price__c = String.isNotBlank(ol.Price) ? Decimal.valueOf(ol.Price) : null;
    //                             oi.Item_Type__c = ol.Line_Attribute5;
    //                             oi.List_Price__c = String.isNotBlank(ol.Line_Attribute6) ? Decimal.valueOf(ol.Line_Attribute6) : null;
    //                             oi.Request_Date__c = String.isNotBlank(ol.Request_Date) ? Datetime.valueOf(ol.Request_Date) : null;
    //                             oi.Ship_Date__c = String.isNotBlank(ol.Ship_Date) ? Date.valueOf(ol.Ship_Date) : null;
    //                             oi.Line_Type__c = ol.Line_Type;
    //                             if(pricebookMap.get(ol.Line_Price_List) != null){
    //                                 oi.Price_Book__c = pricebookMap.get(ol.Line_Price_List);
    //                             }
    //                             oi.Price_Book_Text__c = ol.Line_Price_List;

    //                             Set<String> codeList = new Set<String>();
    //                             if(String.isNotBlank(ol.Promation_Code)){
    //                                 codeList.add(ol.Promation_Code);
    //                             }

    //                             if(String.isNotBlank(o.Whole_Order_Promotion_Code__c)){
    //                                 codeList.add(o.Whole_Order_Promotion_Code__c);
    //                             }

    //                             if(String.isNotBlank(o.Payment_Term_Promotion_Code__c)){
    //                                 codeList.add(o.Payment_Term_Promotion_Code__c);
    //                             }

    //                             if(codeList.size() >0){
    //                                 oi.Promation_Code__c = String.join(new List<String>(codeList), ',');
    //                             }else{
    //                                 oi.Promation_Code__c = ol.Promation_Code;
    //                             }

    //                             // populate promotion on order item - update on 2021-05-05
    //                             if(String.isNotBlank(ol.Promation_Code)){
    //                                 populatePromotionByCode(oi, o, ol.Promation_Code, promotionWindowList, false, false);
    //                             }
    //                             oi.Whole_Order_Promotion_Window__c = o.Whole_Order_Promotion_Window__c;
    //                             oi.Payment_Term_Promotion_Window__c = o.Payment_Term_Promotion_Window__c;
    //                             oi.Line_Status__c = ol.Line_Status;
    //                             oi.isHold__c = ol.Line_IsHold;
    //                             oi.Hold_Reason__c = ol.Line_Hold_Reason;

    //                             // add 2021-07-13
    //                             oi.Selling_Warehouse__c = ol.Line_Attribute4;

    //                             //populate org code for order item
    //                             oi.Org_Code__c = o.Org_Code__c;

    //                             //line surcharge amount
    //                             if(String.isNotBlank(ol.Line_Attribute7)){
    //                                 oi.Surcharge_Amount__c = Decimal.valueOf(ol.Line_Attribute7);
    //                             }

    //                             if(String.isNotBlank(ol.Line_Attribute8)){
    //                                 oi.Business_Purpose__c = String.valueOf(ol.Line_Attribute8);
    //                             }

    //                             oiInsertList.add(oi);
    //                         }
    //                     }
    //                 }


    //                 if(oiInsertList.size() > 0){
    //                     insert oiInsertList;
    //                 }
    //                 if(oiUpdateList.size() > 0){
    //                     update oiUpdateList;
    //                 }

    //                 // reverse order 场景
    //                 if (String.isNotEmpty(reqObj.Attribute7) && reqObj.Attribute7.startsWith('RO')) {
    //                     reverseReturnProductQty = 0;
    //                     for (Reverse_Order_Item__c item : reverseItems) {
    //                         item.Warehouse_Received_Qty__c = 0;
    //                         for (Order_Item__c oi: [SELECT Id, Order_Quantity__c, Line_Status__c
    //                                 , Product_Text__c
    //                                 FROM Order_Item__c WHERE Order__r.Reverse_Order_Request__c = :o.Reverse_Order_Request__c]) {
    //                             if (item.Product2__r.ProductCode == oi.Product_Text__c) {
    //                                 reverseOrderLineStatus.add(oi.Line_Status__c);
    //                                 if (oi.Line_Status__c == 'RETURNED' || oi.Line_Status__c == 'CLOSED') {
    //                                     item.Warehouse_Received_Qty__c += oi.Order_Quantity__c == null ? 0 : oi.Order_Quantity__c.abs();
    //                                     reverseReturnProductQty += item.Warehouse_Received_Qty__c;
    //                                 }
    //                             }
    //                         }
    //                     }

    //                     Decimal reverseProductQty = 0;
    //                     for (Reverse_Order_Item__c item : reverseItems) {
    //                         reverseProductQty += item.Qty__c;
    //                     }

    //                     Set<String> receivedStatus = new Set<String>{'CANCELLED', 'RETURNED', 'CLOSED'};
    //                     if (reverseRequest.Reverse_Order_Type__c != 'Shortage') {
    //                         if (reverseReturnProductQty < reverseProductQty) {
    //                             reverseRequest.Return_Goods_Status__c = 'Partial Received';
    //                             if (receivedStatus.containsAll(reverseOrderLineStatus)) {
    //                                 reverseRequest.Return_Goods_Status__c = 'Received';
    //                                 reverseRequest.Completed_In_API__c = true;
    //                             }
    //                         }
    //                         if (reverseReturnProductQty == reverseProductQty) {
    //                             reverseRequest.Return_Goods_Status__c = 'Received';
    //                             reverseRequest.Completed_In_API__c = true;
    //                         }
    //                     } else {
    //                         reverseRequest.Return_Goods_Status__c = 'N/A';
    //                         if (reverseReturnProductQty == reverseProductQty) {
    //                             reverseRequest.Completed_In_API__c = true;
    //                         }
    //                     }
    //                     if (reverseOrderLineStatus.size() == 1
    //                         && reverseOrderLineStatus.contains('AWAITING_RETURN')
    //                         && reverseReturnProductQty == 0) {
    //                         reverseRequest.Return_Goods_Status__c = 'N/A';
    //                         reverseRequest.Completed_In_API__c = false;
    //                     }
    //                     update reverseRequest;
    //                     update reverseItems;
    //                 }

    //                 if (polist.size() > 0){
    //                     Purchase_Order__c po1 = polist.get(0);
    //                     po1.Sync_Status__c = 'Success';
    //                     update po1;
    //                 }
    //             }else{
    //                 List<Order_Item__c> oilist = new List<Order_Item__c>();
    //                 Order o = new Order();
    //                 Boolean isPlaceOrder = false;
    //                 if(PRecordTypeName == 'Place_Parts_Order'){
    //                     o.RecordTypeId = CCM_Contants.PLACE_PARTS_ORDER_RECORDTYPEID;
    //                 }else{
    //                     o.RecordTypeId = CCM_Contants.PLACE_ORDER_RECORDTYPEID;
    //                     o.Dropship_Customer__c = polist.size() > 0 ? polist.get(0).Dropship_Customer__c : null;
    //                     isPlaceOrder = true;
    //                 }

    //                 // if reqObj.Attribute7 start with 'RO' set reverse order request to order
    //                 List<Reverse_Order_Item__c> reverseItems = new List<Reverse_Order_Item__c>();
    //                 Reverse_Order_Request__c reverseRequest;
    //                 // reverse order 的场景
    //                 if (String.isNotEmpty(reqObj.Attribute7)
    //                     && reqObj.Attribute7.startsWith('RO')
    //                     && (reqObj.Order_Type == 'CNA Return - Sales & Inv.Adj.'
    //                         || reqObj.Order_Type == 'CA Return - Sales & Inv.Adj.')) {
    //                     List<Reverse_Order_Request__c> reverseOrderRequests = [SELECT Id,
    //                                                                            			Reverse_Order_Type__c,
    //                                                                                     CreatedBy.Profile.Name,
    //                                                                                     CreatedBy.Email,
    //                                                                                     Customer__r.Owner.Email,
    //                                                                                     (SELECT Id,
    //                                                                                         Qty__c,
    //                                                                                         Next_Step_Action__c,
    //                                                                                         Product2__c,
    //                                                                                         Product2__r.ProductCode,
    //                                                                                         Warehouse_Received_Product_Amount__c,
    //                                                                                         Warehouse_Received_Qty__c,
    //                                                                                         Warehouse_Received_Subtotal__c,
    //                                                                                         Warehouse_Received_Total_Quantity__c,
    //                                                                                         Warehouse_Return_Number__c
    //                                                                                         FROM Reverse_Order_Items__r)
    //                                                                                 FROM Reverse_Order_Request__c
    //                                                                                 WHERE Reverse_Order_Request_Number__c = :reqObj.Attribute7];
    //                     o.Reverse_Order_Request__c = reverseOrderRequests[0].Id;
    //                     reverseRequest = reverseOrderRequests[0];
    //                     reverseRequest.Return_Order_Number__c = reqObj.Order_OracleID;
    //                     reverseItems = reverseOrderRequests[0].Reverse_Order_Items__r;
    //                 }

    //                 o.Order_OracleID__c = OrderOracleID;
    //                 o.Order_Number__c = reqObj.Order_Number;
    //                 if(polist.size() > 0){
    //                     o.Purchase_Order__c = polist.get(0).Id;
    //                 }
    //                 if(sampleList != null && sampleList.size() > 0){
    //                     o.Sample_Order__c = sampleList[0].id;
    //                 }
    //                 o.Purchase_Order_Text__c = Salesforce_PurchaseOrder_Number;
    //                 String customerAccountNumber = reqObj.Customer;
    //                 List<Account> acclist = [select id from Account where AccountNumber = :customerAccountNumber];
    //                 if(acclist.size() > 0){
    //                     o.AccountId = acclist.get(0).Id;
    //                     if (reqObj.Order_Type != 'CNA Sample Order Only') {
    //                         initialPromotionInfos(promotionWindowList, o.AccountId, allPromotionCodeSet);
    //                     }
    //                 }
    //                 if(isPlaceOrder && acclist.size() == 0){
    //                     String eMsg = Label.CCM_ErrorMsg_NotFindCustomer;
    //                     throw new OrderException(eMsg.replace('{0}', 'Order').replace('{1}', reqObj.Order_OracleID).replace('{2}', reqObj.Customer));
    //                 }
    //                 o.Account_Text__c = customerAccountNumber;
    //                 o.EffectiveDate = Date.today();
    //                 o.PO_Number__c = reqObj.PO_Number;
    //                 String shipTo = reqObj.ShipTo;
    //                 List<Address_With_Program__c> shipAPlist = [select id, Account_Address__c, Account_Address__r.Store_Location__c, Account_Address__r.EGO_Agency__c, Account_Address__r.SKIL_SKILSAW_Agency__c, Account_Address__r.FLEX_Agency__c from Address_With_Program__c where Customer_Line_Oracle_ID__c = :shipTo];
    //                 if(shipAPlist.size() > 0){
    //                     o.ShipTo__c = shipAPlist.get(0).Id;
    //                     o.Store_Location__c = shipAPlist.get(0).Account_Address__c != null ? shipAPlist.get(0).Account_Address__r.Store_Location__c : null;
    //                 }
    //                 o.ShipTo_Text__c = shipTo;
    //                 String billTo = reqObj.BillTo;
    //                 List<Address_With_Program__c> billAPlist = [select id,Program__r.OwnerId,Program__c,Program__r.Sales_Group__c from Address_With_Program__c where Customer_Line_Oracle_ID__c = :billTo];
    //                 if(billAPlist.size() > 0){
    //                     o.BillTo__c = billAPlist.get(0).Id;
    //                     if(billAPlist.get(0).Program__c != null){
    //                         o.OwnerId = billAPlist.get(0).Program__r.OwnerId;
    //                     }

    //                     o.Sales_Group__c = billAPlist.get(0).Program__r.Sales_Group__c;
    //                 }
    //                 o.BillTo_Text__c = billTo;
    //                 o.Order_Type__c = reqObj.Order_Type;
    //                 String PriceList = reqObj.Price_List;
    //                 List<PriceBook2> pricebooklist2 = [select id,Price_Book_OracleID__c,Name from PriceBook2 where Name = :PriceList];
    //                 if(pricebooklist2.size() > 0){
    //                     o.Price_Book__c = pricebooklist2.get(0).Id;
    //                 }
    //                 o.Price_Book_Text__c = PriceList;
    //                 o.Sales_Rep__c = reqObj.Sales_Rep;
    //                 // get Sales Agency by sales_rep Code
    //                 if (String.isNotBlank(reqObj.Sales_Rep)){
    //                     Id agencyId = Util.getSalesAgencyIdByOracleId(reqObj.Sales_Rep);
    //                     o.Sales_Agency__c = agencyId;
    //                 }
    //                 if (String.isEmpty(reqObj.Org_Code) || reqObj.Org_Code == CCM_Constants.ORG_CODE_CNA && shipAPlist.size() > 0) {
    //                     if (brandSet.size() == 1 && brandSet.contains('EGO') && String.isNotEmpty(shipAPlist[0].Account_Address__r.EGO_Agency__c)) {
    //                         o.Sales_Agency__c = shipAPlist[0].Account_Address__r.EGO_Agency__c;
    //                     } else if (brandSet.size() >=1 && !brandSet.contains('EGO') && (String.isNotEmpty(shipAPlist[0].Account_Address__r.SKIL_SKILSAW_Agency__c) || String.isNotEmpty(shipAPlist[0].Account_Address__r.FLEX_Agency__c))) {
    //                         o.Sales_Agency__c = String.isNotEmpty(shipAPlist[0].Account_Address__r.SKIL_SKILSAW_Agency__c) ? shipAPlist[0].Account_Address__r.SKIL_SKILSAW_Agency__c : shipAPlist[0].Account_Address__r.FLEX_Agency__c;
    //                     }
    //                 }
    //                 o.Date_Order__c = String.isNotBlank(reqObj.Date_Order) ? Datetime.valueOf(reqObj.Date_Order) : null;
    //                 o.CurrencyIsoCode = reqObj.CurrencyCode;
    //                 o.Payment_Term__c = reqObj.Payment_Term;
    //                 o.Freight_Term__c = reqObj.Freight_Term;
    //                 o.Shipping_Method__c = reqObj.Shipping_Method;
    //                 o.Sales_Channel__c = reqObj.Sales_Channel;
    //                 o.Shipping_Priority__c = reqObj.Shipping_Priority;
    //                 o.Notes__c = reqObj.Notes;
    //                 o.Expected_Delivery_Date__c = String.isNotBlank(reqObj.Expected_Delivery_Date) ? Date.valueOf(reqObj.Expected_Delivery_Date) : null;
    //                 o.Carrier_Code__c = reqObj.Carrier_Code;
    //                 o.Customer_Freight_Account__c = reqObj.Customer_Freight_Account;
    //                 o.Handling_Fee__c = String.isNotBlank(reqObj.Handling_Fee) ? Decimal.valueOf(reqObj.Handling_Fee) : null;
    //                 o.Feright_Fee__c = String.isNotBlank(reqObj.Feright_Fee) ? Decimal.valueOf(reqObj.Feright_Fee) : null;
    //                 o.Dropship_Name__c = reqObj.Dropship_Name;
    //                 o.Dropship_Address1__c = reqObj.Dropship_Address1;
    //                 o.Dropship_Address2__c = reqObj.Dropship_Address2;
    //                 o.Telephone_Number__c = reqObj.Telephone_Number;
    //                 o.Dropship_Country__c = reqObj.Dropship_Country;
    //                 o.Dropship_ZIP__c = reqObj.Dropship_ZIP;
    //                 o.Dropship_State__c = reqObj.Dropship_State;
    //                 o.Dropship_City__c = reqObj.Dropship_City;
    //                 o.Org_Code__c = reqObj.Org_Code;
    //                 o.Order_Status__c = OrderStatus;
    //                 o.IsHold__c = reqObj.IsHold;
    //                 o.Hold_Reason__c = reqObj.Hold_Reason;
    //                 String source = reqObj.Order_Source;
    //                 source = OrderSourceMap.get(source) != null ? OrderSourceMap.get(source) : source;
    //                 o.Order_Source__c = source;
    //                 List<Warranty_Return_Claim__c> warrantyReturnList = [SELECT Id FROM Warranty_Return_Claim__c WHERE Name = :reqObj.Salesforce_PurchaseOrder_Number];
    //                 o.Warranty_Return_Request__c = warrantyReturnList.size() > 0 ? warrantyReturnList[0].Id : null;

    //                 if(String.isNotBlank(reqObj.Handling_Fee)){
    //                     totalAmount = totalAmount + Decimal.valueOf(reqObj.Handling_Fee);
    //                 }
    //                 if(String.isNotBlank(reqObj.Feright_Fee)){
    //                     totalAmount = totalAmount + Decimal.valueOf(reqObj.Feright_Fee);
    //                 }
    //                 if(PRecordTypeName == 'Place_Parts_Order'){
    //                     totalAmount = totalAmount - discount;
    //                 }
    //                 o.Total_Amount__c = totalAmount;
    //                 o.Status = 'Draft';

    //                 //whole order&payment term promotion
    //                 if(String.isNotBlank(reqObj.Attribute2)){
    //                     o.Whole_Order_Promotion_Code__c = reqObj.Attribute2;
    //                     populatePromotionByCode(null, o, reqObj.Attribute2, promotionWindowList, true, false);
    //                 }
    //                 if(String.isNotBlank(reqObj.Attribute3)){
    //                     o.Payment_Term_Promotion_Code__c = reqObj.Attribute3;
    //                     populatePromotionByCode(null, o, reqObj.Attribute3, promotionWindowList, false, true);
    //                 }

    //                 //CA tax
    //                 if(String.isNotBlank(reqObj.Attribute4)){
    //                     o.GST__c = Decimal.valueOf(reqObj.Attribute4);
    //                     o.Total_Amount__c+=o.GST__c;
    //                 }
    //                 if(String.isNotBlank(reqObj.Attribute5)){
    //                     o.HST__c = Decimal.valueOf(reqObj.Attribute5);
    //                     o.Total_Amount__c+=o.HST__c;
    //                 }
    //                 if(String.isNotBlank(reqObj.Attribute6)){
    //                     o.QST__c = Decimal.valueOf(reqObj.Attribute6);
    //                     o.Total_Amount__c+=o.QST__c;
    //                 }

    //                 //Surcharge Amount
    //                 if(String.isNotBlank(reqObj.Attribute8)){
    //                     o.Surcharge_Amount__c = Decimal.valueOf(reqObj.Attribute8);
    //                     o.Total_Amount__c+=o.Surcharge_Amount__c;
    //                 }
    //                 // 23.12.27 增加9997客户仅针对SF中已存在的Sample Order才创建Order的逻辑
    //                 if(customerAccountNumber == '9997'){
    //                     if(o.Sample_Order__c != null){
    //                         insert o;
    //                     }
    //                 // 23.12.27 end
    //                 }else{
    //                     insert o;
    //                 }

    //                 Decimal reverseReturnProductQty = 0;
    //                 Set<String> reverseOrderLineStatus = new Set<String>();
    //                 if(reqObj.OrderLine != null && reqObj.OrderLine.size() > 0){
    //                     for(OrderLine ol : reqObj.OrderLine){
    //                         Order_Item__c oi = new Order_Item__c();
    //                         oi.Order__c = o.Id;
    //                         oi.CurrencyIsoCode = o.CurrencyIsoCode;
    //                         oi.OrderLine_OracleID__c = ol.OrderLine_OracleID;
    //                         if(purchaseOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) != null){
    //                             oi.Purchase_Order_Item__c = purchaseOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number);
    //                         }
    //                         oi.Sample_Order_Item__c = sampleOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) != null ? sampleOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) : null;
    //                         if(String.isBlank(ol.Product) && isPlaceOrder && (ol.Line_Attribute5 == 'FG' || ol.Line_Attribute5 == 'MKT')){
    //                             String eMsg = Label.CCM_ErrorMsg_LackOfProductInfo;
    //                             throw new OrderException(eMsg.replace('{0}', 'Order').replace('{1}', ol.OrderLine_OracleID));
    //                         }
    //                         if(proMap.get(ol.Product) != null){
    //                             oi.Product__c = proMap.get(ol.Product);
    //                         }else if(proEBSMap.get(ol.Product) != null){
    //                             oi.Product__c = proEBSMap.get(ol.Product);
    //                         }else if(isPlaceOrder && (ol.Line_Attribute5 == 'FG' || ol.Line_Attribute5 == 'MKT')){
    //                             String eMsg = Label.CCM_ErrorMsg_NotFindProduct;
    //                             throw new OrderException(eMsg.replace('{0}', 'Order').replace('{1}', ol.OrderLine_OracleID).replace('{2}', ol.Product));
    //                         }
    //                         oi.Product_Text__c = ol.Product;
    //                         oi.Order_Quantity__c = String.isNotBlank(ol.Order_Quantity) ? Decimal.valueOf(ol.Order_Quantity) : null;
    //                         oi.Reverse_Quantity__c = String.isNotBlank(ol.Reverse_Quantity) ? Integer.valueOf(ol.Reverse_Quantity) : null;
    //                         oi.Cannel_Quantity__c = String.isNotBlank(ol.Cannel_Quantity) ? Integer.valueOf(ol.Cannel_Quantity) : null;
    //                         oi.Price__c = String.isNotBlank(ol.Price) ? Decimal.valueOf(ol.Price) : null;
    //                         oi.Item_Type__c = ol.Line_Attribute5;
    //                         oi.List_Price__c = String.isNotBlank(ol.Line_Attribute6) ? Decimal.valueOf(ol.Line_Attribute6) : null;
    //                         oi.Request_Date__c = String.isNotBlank(ol.Request_Date) ? Datetime.valueOf(ol.Request_Date) : null;
    //                         oi.Ship_Date__c = String.isNotBlank(ol.Ship_Date) ? Date.valueOf(ol.Ship_Date) : null;
    //                         oi.Line_Type__c = ol.Line_Type;
    //                         if(pricebookMap.get(ol.Line_Price_List) != null){
    //                             oi.Price_Book__c = pricebookMap.get(ol.Line_Price_List);
    //                         }
    //                         oi.Price_Book_Text__c = ol.Line_Price_List;

    //                         Set<String> codeList = new Set<String>();
    //                         if(String.isNotBlank(ol.Promation_Code)){
    //                             codeList.add(ol.Promation_Code);
    //                         }
    //                         if(String.isNotBlank(o.Whole_Order_Promotion_Code__c)){
    //                             codeList.add(o.Whole_Order_Promotion_Code__c);
    //                         }

    //                         if(String.isNotBlank(o.Payment_Term_Promotion_Code__c)){
    //                             codeList.add(o.Payment_Term_Promotion_Code__c);
    //                         }

    //                         if(codeList.size() >0){
    //                             oi.Promation_Code__c = String.join(new List<String>(codeList), ',');
    //                         }else{
    //                             oi.Promation_Code__c = ol.Promation_Code;
    //                         }


    //                         // populate promotion on order item - update on 2021-05-05
    //                         if(String.isNotBlank(ol.Promation_Code)){
    //                             populatePromotionByCode(oi, o, ol.Promation_Code, promotionWindowList, false, false);
    //                         }
    //                         oi.Whole_Order_Promotion_Window__c = o.Whole_Order_Promotion_Window__c;
    //                         oi.Payment_Term_Promotion_Window__c = o.Payment_Term_Promotion_Window__c;
    //                         oi.Line_Status__c = ol.Line_Status;
    //                         oi.isHold__c = ol.Line_IsHold;
    //                         oi.Hold_Reason__c = ol.Line_Hold_Reason;

    //                         // add 2021-07-13
    //                         oi.Selling_Warehouse__c = ol.Line_Attribute4;
    //                         // get reverse order item info, if Attribute7 start witho "RO"
    //                         if (String.isNotEmpty(reqObj.Attribute7) && reqObj.Attribute7.startsWith('RO')) {
    //                             for (Reverse_Order_Item__c item : reverseItems) {
    //                                 if (item.Product2__r.ProductCode == oi.Product_Text__c) {
    //                                     reverseOrderLineStatus.add(oi.Line_Status__c);
    //                                     if (oi.Line_Status__c == 'RETURNED' || oi.Line_Status__c == 'CLOSED') {
    //                                         if (item.Warehouse_Received_Qty__c == null) {
    //                                             item.Warehouse_Received_Qty__c = 0;
    //                                         }
    //                                         item.Warehouse_Received_Qty__c += oi.Order_Quantity__c == null ? 0 : oi.Order_Quantity__c.abs();
    //                                         reverseReturnProductQty += item.Warehouse_Received_Qty__c;
    //                                     }
    //                                 }
    //                             }
    //                         }

    //                         //populate org code for order item
    //                         oi.Org_Code__c = o.Org_Code__c;

    //                         //line surcharge amount
    //                         if(String.isNotBlank(ol.Line_Attribute7)){
    //                             oi.Surcharge_Amount__c = Decimal.valueOf(ol.Line_Attribute7);
    //                         }

    //                         if(String.isNotBlank(ol.Line_Attribute8)){
    //                             oi.Business_Purpose__c = String.valueOf(ol.Line_Attribute8);
    //                         }

    //                         oilist.add(oi);
    //                     }
    //                 }
    //                 if (String.isNotEmpty(reqObj.Attribute7) && reqObj.Attribute7.startsWith('RO')) {
    //                     Decimal reverseProductQty = 0;
    //                     for (Reverse_Order_Item__c item : reverseItems) {
    //                         reverseProductQty += item.Qty__c;
    //                     }

    //                     Set<String> receivedStatus = new Set<String>{'CANCELLED', 'RETURNED', 'CLOSED'};
    //                     if (reverseRequest.Reverse_Order_Type__c != 'Shortage') {
    //                         if (reverseReturnProductQty < reverseProductQty) {
    //                             reverseRequest.Return_Goods_Status__c = 'Partial Received';
    //                             if (receivedStatus.containsAll(reverseOrderLineStatus)) {
    //                                 reverseRequest.Return_Goods_Status__c = 'Received';
    //                                 reverseRequest.Completed_In_API__c = true;
    //                             }
    //                         }
    //                         if (reverseReturnProductQty == reverseProductQty) {
    //                             reverseRequest.Return_Goods_Status__c = 'Received';
    //                             reverseRequest.Completed_In_API__c = true;
    //                         }
    //                     } else {
    //                         reverseRequest.Return_Goods_Status__c = 'N/A';
    //                         if (reverseReturnProductQty == reverseProductQty) {
    //                             reverseRequest.Completed_In_API__c = true;
    //                         }
    //                     }
    //                     if (reverseOrderLineStatus.size() == 1 && reverseOrderLineStatus.contains('AWAITING_RETURN') && reverseReturnProductQty == 0) {
    //                         reverseRequest.Return_Goods_Status__c = 'N/A';
    //                         reverseRequest.Completed_In_API__c = false;
    //                     }
    //                     update reverseRequest;
    //                     update reverseItems;
    //                 }
    //                 insert oilist;
    //                 if(polist.size() > 0){
    //                     Purchase_Order__c po1 = polist.get(0);
    //                     po1.Sync_Status__c = 'Success';
    //                     update po1;
    //                 	insertAMWareMerchandisingOrderItems(OrderStatus, polist.get(0), o.Id);
    //                 }
    //             }


    //             if (String.isNotEmpty(reqObj.Salesforce_PurchaseOrder_Number)
    //                 && reqObj.Salesforce_PurchaseOrder_Number.startsWith('RA')
    //                 && (reqObj.Order_Type == 'CNA Return Order - Warranty'
    //                     || reqObj.Order_Type == 'CA Return Order - Warranty')) {
    //                 handleWarrantyReturnOrder(reqObj.Salesforce_PurchaseOrder_Number);
    //             }
    //             if(reqObj.Order_Type == 'CNA Sample Order Only') {
    //                 dealSampleOrderInventory(reqObj.Salesforce_PurchaseOrder_Number, reqObj.OrderLine, OrderStatus);
    //             }
    //         }

    //         resObj.returnCode = 'S';
    //     }catch (OrderException e){
    //         resObj.returnCode = 'E';
    //         resObj.returnMsg = e.getMessage();
    //         String logId = Util.logIntegration(
    //             'Order Exception','CCM_RestService_DealOMSOrderInfo',
    //             'POST',resObj.returnMsg,JSON.serialize(reqObj),
    //              JSON.serialize(resObj)
    //         );
    //         return resObj;
    //     }catch (Exception e) {
    //         resObj.returnCode = 'F';
    //         resObj.returnMsg = e.getLineNumber() + '-line error:' + e.getMessage();
    //         System.debug(LoggingLevel.INFO, '*** e.getMessage(): '+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage());
    //         String logId = Util.logIntegration(
    //             'Order Exception','CCM_RestService_DealOMSOrderInfo',
    //             'POST',resObj.returnMsg,JSON.serialize(reqObj),
    //              JSON.serialize(resObj)
    //         );
    //         Util.pushExceptionEmail('Accept Order Info',logId,resObj.returnMsg);
    //         return resObj;
    //     }
    //     if(Label.CCM_needlog == 'Y'){
    //        Util.logIntegration('Order log','CCM_RestService_DealOMSOrderInfo', 'POST',
    //                            '',JSON.serialize(reqObj), JSON.serialize(resObj));
    //     }
    //     System.debug(LoggingLevel.INFO, '*** resObj: ' + resObj);
    //     return resObj;
    // }

    @HttpPost
    global static ResultObj doPost() {
        ResultObj resObj = new ResultObj();
        try {
            RestRequest req = RestContext.request;
            List<ReqestObj> datas = parse(req.requestBody.toString());
            CCM_OrderInterfaceUtil.storeDataFromInterface(datas);
            resObj.returnCode = 'S';
        }
        catch(Exception e) {
            resObj.returnCode = 'F';
            resObj.returnMsg = e.getLineNumber() + '-line error:' + e.getMessage();
            Util.logIntegration(
                'Order Exception','CCM_RestService_DealOMSOrderInfo',
                'POST', resObj.returnMsg, resObj.returnMsg,
                resObj.returnMsg);
        }
        return resObj;
    }

    global static void processImp(ReqestObj reqObj, OrderInterface__c oiData) {
        try {
            String OrderOracleID = reqObj.Order_OracleID;
            String sfpNumber = reqObj.Salesforce_PurchaseOrder_Number;
            List<Messaging.SingleEmailMessage> mailsToSend = new List<Messaging.SingleEmailMessage>();
            if(CCM_Service.isValid(sfpNumber,Order.sObjectType)){
                Order order2 = [select id from Order where id = :sfpNumber];
                order2.Order_OracleID__c = OrderOracleID;
                order2.Order_Number__c = reqObj.Order_Number;
                order2.IsHold__c = reqObj.IsHold;
                order2.Hold_Reason__c = reqObj.Hold_Reason;
                update order2;
            }else{
                // 处理 order 逻辑
                List<Order> orderlist = [
                                            select id,Handling_Fee__c
                                            ,Feright_Fee__c,Total_Amount__c, Dropship_Customer__c
                                            ,Whole_Order_Promotion_Code__c
                                            ,Payment_Term_Promotion_Code__c
                                            ,Whole_Order_Promotion_Window__c
                                            ,Payment_Term_Promotion_Window__c
                                            ,Org_Code__c
                                            ,CurrencyIsoCode
                                            from Order where id != null
                                            and Order_OracleID__c = :OrderOracleID
                                            FOR UPDATE
                                            ];

                String Salesforce_PurchaseOrder_Number = reqObj.Salesforce_PurchaseOrder_Number;
                // query promotion and promotion window information on purchase order - update on 2021-05-05
                List<Purchase_Order__c> polist = new List<Purchase_Order__c>();
                if (reqObj.Order_Type != 'CNA Sample Order Only') {
                    polist = [select id,Sync_Status__c,Sync_Message__c,RecordType.DeveloperName,Discount_Amount__c,Total_Amount__c,Freight_Fee__c ,Handling_Fee__c, Customer__r.OwnerId, Customer__r.Owner.SenderEmail,Customer__r.Owner.Email,Actual_Freight_Fee__c,
                                    Payment_Term_Promotion__c,Whole_Order_Promotion__c,Whole_Order_Promotion_Window__c, Payment_Term_Promotion_Window__c,ORG_ID__c, Dropship_Customer__c,
                                    (SELECT id,Whole_Order_Promotion__c,Whole_Order_Promotion__r.Promotion_Code_For_External__c,Promotion__r.Promotion_Code_For_External__c,Promotion__c,
                                    Regular_Promotion_Window__c
                                    FROM Purchase_Order_Items__r)
                                    from Purchase_Order__c
                                    where Name = :Salesforce_PurchaseOrder_Number];
                }
                List<Purchase_Order_Item__c> allpoItemList = new List<Purchase_Order_Item__c>();
                List<Sample_Order__c> sampleList = new List<Sample_Order__c>();
                if (reqObj.Order_Type == 'CNA Sample Order Only') {
                    sampleList = [SELECT Id, OwnerId, Name, CurrencyIsoCode, RecordTypeId, Sales_Group__c, Contact_Name__c, Contact_Phone__c, Contact_Email__c, Street__c, City__c, State__c, Sample_Order_Number__c,
                                        Zip_Code__c, Selected_Plan__c, Order_Status__c, Total_Amount__c, Reason__c, Demo_Free_Goods_Program__c, Task_Owner__c, Expected_Delivery_Date__c, Requestor_Email__c, Requester_Phone__c,
                                        Initial_Submitter__c, Initial_Submitter_Role__c, Regional_Manager__c, Submit_Time__c,
                                        (SELECT Id, Name, Product__c, Quantity__c, MSRP_Price__c, Invoice_Price__c, Sample_Order__c, Subtotal__c, Demo_Plan_Item__c, Price_Book__c, Demo_Plan__c
                                            FROM Sample_Order_Items__r)
                                    FROM Sample_Order__c WHERE Name = :Salesforce_PurchaseOrder_Number];
                }
                List<Sample_Order_Item__c> allsoItemList = new List<Sample_Order_Item__c>();
                Map<String,Id> promoCodePromotionMap = new Map<String,Id>();
                Map<String,Id> wholeCodePromotionMap = new Map<String,Id>();
                Map<String,Id> promoCodePromotionWindowMap = new Map<String,Id>();
                List<Promotion_Window__c> promotionWindowList = new List<Promotion_Window__c>();
                String PRecordTypeName = '';
                Decimal discount = 0;
                if(polist.size() > 0){
                    PRecordTypeName = polist.get(0).RecordType.DeveloperName;
                    if(polist.get(0).Discount_Amount__c != null){
                        discount = polist.get(0).Discount_Amount__c;
                    }
                    allpoItemList=polist.get(0).Purchase_Order_Items__r;
                    if (allpoItemList.size()>0) {
                        for(Purchase_Order_Item__c poi:allpoItemList){
                            // store promotion information - update on 2021-05-05
                            wholeCodePromotionMap.put(poi.Whole_Order_Promotion__r.Promotion_Code_For_External__c,poi.Whole_Order_Promotion__c);
                            promoCodePromotionMap.put(poi.Promotion__r.Promotion_Code_For_External__c,poi.Promotion__c);
                            promoCodePromotionWindowMap.put(poi.Promotion__r.Promotion_Code_For_External__c,poi.Regular_Promotion_Window__c);
                        }
                    }
                }
                // 失败有值，成功没值。先失败，后成功，应该覆盖历史值
                String errorMsg = reqObj.Error_Message;
                // 更新 purchase,sample order 的状态和 error message
                if(String.isNotBlank(errorMsg)){
                    // purchase order
                    if (reqObj.Order_Type != 'CNA Sample Order Only') {
                        if(polist.size() > 0){
                            Purchase_Order__c po2 = polist.get(0);
                            po2.Sync_Message__c = errorMsg;
                            po2.Sync_Status__c = 'Failed';
                            update po2;
                            oiData.Return_Code__c = 'S';
                            // resObj.returnCode = 'S';
                            // return resObj;
                        } else {
                            oiData.Return_Code__c = 'F';
                            oiData.Error_Msg__c = 'Can not find the error original purchase order!';
                            // resObj.returnCode = 'F';
                            // resObj.returnMsg = 'Can not find the error original purchase order!';
                            // return resObj;
                        }
                        // sample order
                    } else {
                        if(samplelist.size() > 0){
                            // resObj.returnCode = 'S';
                            oiData.Return_Code__c = 'S';
                            // return resObj;
                        }else{
                            oiData.Return_Code__c = 'F';
                            oiData.Error_Msg__c = 'Can not find the error original purchase order!';
                            // resObj.returnCode = 'F';
                            // resObj.returnMsg = 'Can not find the error original purchase order!';
                            // return resObj;
                        }
                    }

                } else {
                    if (polist.size() > 0) {
                        Purchase_Order__c po2 = polist.get(0);
                        po2.Sync_Message__c = '';
                        po2.Sync_Status__c = 'Success';
                        update po2;
                    }
                }

                //update by nick 20200518
                String OrderStatus = reqObj.Order_Status;
                String OrderType = reqObj.Order_Type;
                // reverse order
                if (OrderType == 'CNA Return - Sales & Inv.Adj.' || OrderType == 'CA Return - Sales & Inv.Adj.') {
                    if (OrderStatus == 'AWAITING_RETURN') {
                        OrderStatus = 'Booked';
                    }
                } else {
                    if (OrderStatus == 'CLOSED') {
                        OrderStatus = 'Ship Complete';
                    }

                    if (OrderStatus == 'ENTERED') {
                        OrderStatus = 'Booked';
                    }

                    if(OrderStatus == 'BOOKED'){
                        if(reqObj.OrderLine != null && reqObj.OrderLine.size() > 0){
                            Integer orderItemSize = reqObj.OrderLine.size();
                            Integer orderProcessingIndex = 0;
                            Integer shipCompleteIndex = 0;
                            Integer cancelledIndex = 0;
                            Integer partialShipmentIndex = 0;
                            // partial 标志位， 如果这个为 true，证明有可能是 partial shipment
                            Boolean blPartial = false;
                            for(OrderLine ol : reqObj.OrderLine){
                                String lineStatus = ol.Line_Status;
                                if(lineStatus == 'PICKED' || lineStatus == 'AWAITING_SHIPPING' ){
                                    orderProcessingIndex += 1;
                                    blPartial =  true;
                                }
                                if(lineStatus == 'INVOICED' || lineStatus == 'SHIPPED'){
                                    shipCompleteIndex += 1;
                                    partialShipmentIndex += 1;
                                }
                                if(lineStatus == 'CANCELLED'){
                                    cancelledIndex += 1;
                                    blPartial = true;
                                }
                            }
                            if((orderProcessingIndex == orderItemSize) || (orderProcessingIndex > 0
                                && orderProcessingIndex + cancelledIndex == orderItemSize)){
                                OrderStatus = 'Order Processing';
                            } else if(shipCompleteIndex == orderItemSize){
                                OrderStatus = 'Ship Complete';
                            } else if(cancelledIndex == orderItemSize){
                                OrderStatus = 'Cancelled';
                                // Partial shipment = 部分发运，完成数 + 取消数 < 订单总数，订单包含了  PICKED || AWAITING_SHIPPING
                            } else if(partialShipmentIndex > 0 && blPartial == true && (shipCompleteIndex + cancelledIndex) < orderItemSize){
                                OrderStatus = 'Partial Shipment';
                            } else if(shipCompleteIndex > 0 && (shipCompleteIndex + cancelledIndex) == orderItemSize) {
                                OrderStatus = 'Ship Complete';
                            }
                        }
                    }
                }
                List<String> purchaseOrderLineID = new List<String>();
                Map<String,Id> purchaseOrderLineMap = new Map<String,Id>();
                List<String> proIdlist = new List<String>();
                Map<String,Id> proMap = new Map<String,Id>();
                List<String> pricebookIdlist = new List<String>();
                Map<String,Id> pricebookMap = new Map<String,Id>();
                List<String> orderlineIDlist = new List<String>();

                Map<String,Id> proEBSMap = new Map<String,Id>();
                Set<String> allPromotionCodeSet = new Set<String>();
                Set<String> brandSet = new Set<String>();

                Decimal totalAmount = 0;
                if(reqObj.OrderLine != null && reqObj.OrderLine.size() > 0){
                    for(OrderLine ol : reqObj.OrderLine){
                        if(!purchaseOrderLineID.contains(ol.Salesforce_PurchaseOrderLine_Number)){
                            purchaseOrderLineID.add(ol.Salesforce_PurchaseOrderLine_Number);
                        }
                        if(!proIdlist.contains(ol.Product)){
                            proIdlist.add(ol.Product);
                        }
                        if(!pricebookIdlist.contains(ol.Line_Price_List)){
                            pricebookIdlist.add(ol.Line_Price_List);
                        }
                        if(!orderlineIDlist.contains(ol.OrderLine_OracleID)){
                            orderlineIDlist.add(ol.OrderLine_OracleID);
                        }
                        if(String.isNotBlank(ol.Price) && String.isNotBlank(ol.Order_Quantity)){
                            Decimal lineAmount = Decimal.valueOf(ol.Price) * Decimal.valueOf(ol.Order_Quantity);
                            totalAmount = totalAmount + lineAmount;
                        }
                        if(String.isNotBlank(ol.Promotion_Code)){
                            allPromotionCodeSet.add(ol.Promotion_Code);
                        }
                    }
                }
                if(String.isNotBlank(reqObj.Attribute2)){
                    allPromotionCodeSet.add(reqObj.Attribute2);
                }
                if(String.isNotBlank(reqObj.Attribute3)){
                    allPromotionCodeSet.add(reqObj.Attribute3);
                }
                List<Purchase_Order_Item__c> poilist = new List<Purchase_Order_Item__c>();
                List<Sample_Order_Item__c> soilist = new List<Sample_Order_Item__c>();
                Map<String,Id> sampleOrderLineMap = new Map<String,Id>();
                if (reqObj.Order_Type != 'CNA Sample Order Only') {
                    poilist = [select id,Name,Whole_Order_Promotion__c,Whole_Order_Promotion__r.Promotion_Code_For_External__c from Purchase_Order_Item__c where Name in :purchaseOrderLineID];
                    for(Purchase_Order_Item__c poi : poilist){
                        purchaseOrderLineMap.put(poi.Name, poi.Id);
                    }
                } else if (reqObj.Order_Type == 'CNA Sample Order Only') {
                    soilist = [SELECT Id FROM Sample_Order_Item__c WHERE Name IN :purchaseOrderLineID];
                    for(Sample_Order_Item__c poi : soilist){
                        sampleOrderLineMap.put(poi.Name, poi.Id);
                    }
                }
                List<Product2> prolist = [select id,ProductCode,Name,Source__c, Brand_Name__c
                        from Product2 where ProductCode in :proIdlist
                        and Source__c = 'PIM'
                        AND IsCreatedByCode__c = false
                        AND Is_History_Product__c = :CCM_Constants.blHistoryProduct];
                for(Product2 pro : prolist){
                    if(!pro.Name.contains('AO ONLY')){
                        proMap.put(pro.ProductCode, pro.Id);
                        brandSet.add(String.isEmpty(pro.Brand_Name__c) ? null : pro.Brand_Name__c.toUpperCase());
                    }
                }

                List<Product2> proEBSlist = [select id,ProductCode,Name,Source__c, Brand_Name__c
                        from Product2 where ProductCode in :proIdlist
                        and Source__c = 'EBS'
                        AND IsCreatedByCode__c = false
                        AND Is_History_Product__c = :CCM_Constants.blHistoryProduct];
                for(Product2 pro : proEBSlist){
                    if(!pro.Name.contains('AO ONLY')){
                        proEBSMap.put(pro.ProductCode, pro.Id);
                        brandSet.add(String.isEmpty(pro.Brand_Name__c) ? null : pro.Brand_Name__c.toUpperCase());
                    }
                }

                List<PriceBook2> pricebooklist = [select id,Price_Book_OracleID__c,Name from PriceBook2 where Name in :pricebookIdlist];
                for(PriceBook2 pb : pricebooklist){
                    pricebookMap.put(pb.Name, pb.Id);
                }
                // upsert order 对象记录
                if(orderlist.size() > 0){
                    Order o = orderlist.get(0);
                    Boolean isPlaceOrder = false;
                    if(PRecordTypeName == 'Place_Parts_Order'){
                        o.RecordTypeId = CCM_Contants.PLACE_PARTS_ORDER_RECORDTYPEID;
                    }else{
                        //update by austin,给order dropship customer赋值
                        o.RecordTypeId = CCM_Contants.PLACE_ORDER_RECORDTYPEID;
                        o.Dropship_Customer__c = polist.size() > 0 ? polist.get(0).Dropship_Customer__c : null;
                        isPlaceOrder = true;
                    }

                    o.Order_Number__c = reqObj.Order_Number;
                    if(polist.size() > 0){
                        o.Purchase_Order__c = polist.get(0).Id;
                    }
                    o.Purchase_Order_Text__c = Salesforce_PurchaseOrder_Number;

                    // if reqObj.Attribute7 start with 'RO' set reverse order request to order
                    List<Reverse_Order_Item__c> reverseItems = new List<Reverse_Order_Item__c>();
                    Reverse_Order_Request__c reverseRequest;
                    List<Invoice_Item__c> invoiceItemList;
                    Boolean isHaveInvoice = false;
                    Decimal invoiceNumber = 0;
                    if (String.isNotEmpty(reqObj.Attribute7)
                        && reqObj.Attribute7.startsWith('RO')
                        && (reqObj.Order_Type == 'CNA Return - Sales & Inv.Adj.'
                            || reqObj.Order_Type == 'CA Return - Sales & Inv.Adj.')) {
                        List<Reverse_Order_Request__c> reverseOrderRequests = [SELECT Id,
                                                                Reverse_Order_Type__c,
                                                                CreatedById,
                                                                CreatedBy.Profile.Name,
                                                                CreatedBy.Email,
                                                                Customer__r.Owner.Email,
                                                                Return_Goods_Status__c,
                                                                (SELECT Id,
                                                                    Qty__c,
                                                                    Next_Step_Action__c,
                                                                    Product2__c,
                                                                    Product2__r.ProductCode,
                                                                    Warehouse_Received_Product_Amount__c,
                                                                    Warehouse_Received_Qty__c,
                                                                    Warehouse_Received_Subtotal__c,
                                                                    Warehouse_Received_Total_Quantity__c,
                                                                    Warehouse_Return_Number__c
                                                                    FROM Reverse_Order_Items__r)
                                                            FROM Reverse_Order_Request__c
                                                            WHERE Reverse_Order_Request_Number__c = :reqObj.Attribute7];
                        invoiceItemList = [SELECT id,Qty_Extended_Number__c FROM Invoice_Item__c WHERE Invoice__r.Reverse_Order_Request__r.Name =:reqObj.Attribute7];
                        if(invoiceItemList.size() > 0){
                            isHaveInvoice = true;
                            for(Invoice_Item__c il : invoiceItemList){
                                invoiceNumber += il.Qty_Extended_Number__c.abs();
                            }
                        }
                        o.Reverse_Order_Request__c = reverseOrderRequests[0].Id;
                        reverseRequest = reverseOrderRequests[0];
                        reverseRequest.Return_Order_Number__c = reqObj.Order_OracleID;
                        reverseItems = reverseOrderRequests[0].Reverse_Order_Items__r;
                    }

                    String customerAccountNumber = reqObj.Customer;
                    Boolean isACE = false;
                    Set<String> aceCustomersSet = new Set<String>();
                    aceCustomersSet.addAll(Label.CCM_WarrantyReturn_ACECustomers.split(';'));
                    if(aceCustomersSet.contains(customerAccountNumber)) {
                        isACE = true;
                    }

                    List<Account> acclist = [select id from Account where AccountNumber = :customerAccountNumber];
                    if(acclist.size() > 0){
                        o.AccountId = acclist.get(0).Id;
                        if (reqObj.Order_Type != 'CNA Sample Order Only') {
                            initialPromotionInfos(promotionWindowList, o.AccountId, allPromotionCodeSet);
                        }
                    }
                    if(isPlaceOrder && acclist.size() == 0){
                        String eMsg = Label.CCM_ErrorMsg_NotFindCustomer;
                        throw new OrderException(eMsg.replace('{0}', 'Order').replace('{1}', reqObj.Order_OracleID).replace('{2}', reqObj.Customer));
                    }
                    o.Account_Text__c = customerAccountNumber;
                    o.EffectiveDate = Date.today();
                    o.PO_Number__c = reqObj.PO_Number;
                    String shipTo = reqObj.ShipTo;
                    List<Address_With_Program__c> shipAPlist = [select id, Account_Address__c,
                                                        Account_Address__r.Store_Location__c, Account_Address__r.EGO_Agency__c,
                                                        Account_Address__r.SKIL_SKILSAW_Agency__c,
                                                        Account_Address__r.FLEX_Agency__c
                                                        from Address_With_Program__c where Customer_Line_Oracle_ID__c = :shipTo];
                    if(shipAPlist.size() > 0){
                        o.ShipTo__c = shipAPlist.get(0).Id;
                        o.Store_Location__c = shipAPlist.get(0).Account_Address__c != null ? shipAPlist.get(0).Account_Address__r.Store_Location__c : null;
                    }
                    o.ShipTo_Text__c = shipTo;
                    String billTo = reqObj.BillTo;
                    List<Address_With_Program__c> billAPlist = [select id,Program__r.OwnerId,Program__c
                                                        ,Program__r.Sales_Group__c
                                                        from Address_With_Program__c where Customer_Line_Oracle_ID__c = :billTo];
                    if(billAPlist.size() > 0){
                        o.BillTo__c = billAPlist.get(0).Id;
                        if(billAPlist.get(0).Program__c != null){
                            o.OwnerId = billAPlist.get(0).Program__r.OwnerId;
                        }

                        o.Sales_Group__c = billAPlist.get(0).Program__r.Sales_Group__c;
                    }
                    o.BillTo_Text__c = billTo;
                    o.Order_Type__c = reqObj.Order_Type;
                    String PriceList = reqObj.Price_List;
                    List<PriceBook2> pricebooklist2 = [select id,Price_Book_OracleID__c,Name from PriceBook2 where Name = :PriceList];
                    if(pricebooklist2.size() > 0){
                        o.Price_Book__c = pricebooklist2.get(0).Id;
                    }
                    o.Price_Book_Text__c = PriceList;
                    o.Sales_Rep__c = reqObj.Sales_Rep;
                    // get Sales Agency by sales_rep Code
                    if (String.isNotBlank(reqObj.Sales_Rep)){
                        Id agencyId = Util.getSalesAgencyIdByOracleId(reqObj.Sales_Rep);
                        o.Sales_Agency__c = agencyId;
                    }
                    if (String.isEmpty(reqObj.Org_Code) || reqObj.Org_Code == CCM_Constants.ORG_CODE_CNA && shipAPlist.size() > 0) {
                        if (brandSet.size() == 1 && brandSet.contains('EGO') && String.isNotEmpty(shipAPlist[0].Account_Address__r.EGO_Agency__c)) {
                            o.Sales_Agency__c = shipAPlist[0].Account_Address__r.EGO_Agency__c;
                        } else if (brandSet.size() >=1 && !brandSet.contains('EGO') && (String.isNotEmpty(shipAPlist[0].Account_Address__r.SKIL_SKILSAW_Agency__c) || String.isNotEmpty(shipAPlist[0].Account_Address__r.FLEX_Agency__c))) {
                            o.Sales_Agency__c = String.isNotEmpty(shipAPlist[0].Account_Address__r.SKIL_SKILSAW_Agency__c) ? shipAPlist[0].Account_Address__r.SKIL_SKILSAW_Agency__c : shipAPlist[0].Account_Address__r.FLEX_Agency__c;
                        }
                    }
                    o.Date_Order__c = String.isNotBlank(reqObj.Date_Order) ? Datetime.valueOf(reqObj.Date_Order) : null;
                    o.CurrencyIsoCode = reqObj.CurrencyCode;
                    o.Payment_Term__c = reqObj.Payment_Term;
                    o.Freight_Term__c = reqObj.Freight_Term;
                    o.Shipping_Method__c = reqObj.Shipping_Method;
                    o.Sales_Channel__c = reqObj.Sales_Channel;
                    o.Shipping_Priority__c = reqObj.Shipping_Priority;
                    o.Notes__c = reqObj.Notes;
                    o.Expected_Delivery_Date__c = String.isNotBlank(reqObj.Expected_Delivery_Date) ? Date.valueOf(reqObj.Expected_Delivery_Date) : null;
                    o.Carrier_Code__c = reqObj.Carrier_Code;
                    o.Customer_Freight_Account__c = reqObj.Customer_Freight_Account;
                    o.Handling_Fee__c = String.isNotBlank(reqObj.Handling_Fee) ? Decimal.valueOf(reqObj.Handling_Fee) : null;
                    o.Feright_Fee__c = String.isNotBlank(reqObj.Freight_Fee) ? Decimal.valueOf(reqObj.Freight_Fee) : null;
                    o.Dropship_Name__c = reqObj.Dropship_Name;
                    o.Dropship_Address1__c = reqObj.Dropship_Address1;
                    o.Dropship_Address2__c = reqObj.Dropship_Address2;
                    o.Telephone_Number__c = reqObj.Telephone_Number;
                    o.Dropship_Country__c = reqObj.Dropship_Country;
                    o.Dropship_ZIP__c = reqObj.Dropship_ZIP;
                    o.Dropship_State__c = reqObj.Dropship_State;
                    o.Dropship_City__c = reqObj.Dropship_City;
                    o.Org_Code__c = reqObj.Org_Code;
                    o.Order_Status__c = OrderStatus;
                    o.IsHold__c = reqObj.IsHold;
                    o.Hold_Reason__c = reqObj.Hold_Reason;
                    String source = reqObj.Order_Source;
                    source = OrderSourceMap.get(source) != null ? OrderSourceMap.get(source) : source;
                    o.Order_Source__c = source;
                    List<Warranty_Return_Claim__c> warrantyReturnList = [SELECT Id FROM Warranty_Return_Claim__c WHERE Name = :reqObj.Salesforce_PurchaseOrder_Number];
                    o.Warranty_Return_Request__c = warrantyReturnList.size() > 0 ? warrantyReturnList[0].Id : null;
                    if (sampleList != null && sampleList.size() > 0){
                        o.Sample_Order__c = sampleList[0].Id;
                    }

                    if(String.isNotBlank(reqObj.Handling_Fee)){
                        totalAmount = totalAmount + Decimal.valueOf(reqObj.Handling_Fee);
                    }
                    if(String.isNotBlank(reqObj.Freight_Fee)){
                        totalAmount = totalAmount + Decimal.valueOf(reqObj.Freight_Fee);
                    }

                    /* 2021-06-09 Fix total amount issue in Order detail page.*/
                    if(PRecordTypeName == 'Place_Parts_Order'){
                        totalAmount = totalAmount - discount;
                    }

                    //whole order&payment term promotion
                    if(String.isNotBlank(reqObj.Attribute2)){
                        o.Whole_Order_Promotion_Code__c = reqObj.Attribute2;
                        populatePromotionByCode(null, o, reqObj.Attribute2, promotionWindowList, true, false);
                    }
                    if(String.isNotBlank(reqObj.Attribute3)){
                        o.Payment_Term_Promotion_Code__c = reqObj.Attribute3;
                        populatePromotionByCode(null, o, reqObj.Attribute3, promotionWindowList, false, true);
                    }

                    o.Total_Amount__c = totalAmount;
                    o.Status = 'Draft';

                    //CA tax
                    if(String.isNotBlank(reqObj.Attribute4)){
                        o.GST__c = Decimal.valueOf(reqObj.Attribute4);
                        o.Total_Amount__c+=o.GST__c;
                    }
                    if(String.isNotBlank(reqObj.Attribute5)){
                        o.HST__c = Decimal.valueOf(reqObj.Attribute5);
                        o.Total_Amount__c+=o.HST__c;
                    }
                    if(String.isNotBlank(reqObj.Attribute6)){
                        o.QST__c = Decimal.valueOf(reqObj.Attribute6);
                        o.Total_Amount__c+=o.QST__c;
                    }

                    //Surcharge Amount
                    if(String.isNotBlank(reqObj.Attribute8)){
                        o.Surcharge_Amount__c = Decimal.valueOf(reqObj.Attribute8);
                        o.Total_Amount__c+=o.Surcharge_Amount__c;
                    }

                    update o;

                    List<Order_Item__c> oilist = [select id,OrderLine_OracleID__c from Order_Item__c
                                                where OrderLine_OracleID__c in :orderlineIDlist
                                                And Order__c = :o.Id];
                    List<Order_Item__c> oiInsertList = new List<Order_Item__c>();
                    List<Order_Item__c> oiUpdateList = new List<Order_Item__c>();
                    Map<String,Order_Item__c> orderItemMap = new Map<String,Order_Item__c>();
                    for(Order_Item__c oi : oilist){
                        orderItemMap.put(oi.OrderLine_OracleID__c, oi);
                    }

                    Decimal reverseReturnProductQty = 0;
                    Set<String> reverseOrderLineStatus = new Set<String>();
                    if(reqObj.OrderLine != null && reqObj.OrderLine.size() > 0){
                        for(OrderLine ol : reqObj.OrderLine){
                            if(orderItemMap.get(ol.OrderLine_OracleID)!=null){
                                Order_Item__c oi = orderItemMap.get(ol.OrderLine_OracleID);
                                oi.Order__c = o.Id;
                                oi.CurrencyIsoCode = o.CurrencyIsoCode;
                                oi.Sample_Order_Item__c = sampleOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) != null ? sampleOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) : null;
                                if(purchaseOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) != null){
                                    oi.Purchase_Order_Item__c = purchaseOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number);
                                }
                                if(String.isBlank(ol.Product) && isPlaceOrder && (ol.Line_Attribute5 == 'FG' || ol.Line_Attribute5 == 'MKT')){
                                    String eMsg = Label.CCM_ErrorMsg_LackOfProductInfo;
                                    throw new OrderException(eMsg.replace('{0}', 'Order').replace('{1}', ol.OrderLine_OracleID));
                                }
                                if(proMap.get(ol.Product) != null){
                                    oi.Product__c = proMap.get(ol.Product);
                                }else if(proEBSMap.get(ol.Product) != null){
                                    oi.Product__c = proEBSMap.get(ol.Product);
                                }else if(isPlaceOrder && (ol.Line_Attribute5 == 'FG' || ol.Line_Attribute5 == 'MKT')){
                                    String eMsg = Label.CCM_ErrorMsg_NotFindProduct;
                                    throw new OrderException(eMsg.replace('{0}', 'Order').replace('{1}', ol.OrderLine_OracleID).replace('{2}', ol.Product));
                                }
                                oi.Product_Text__c = ol.Product;
                                oi.Order_Quantity__c = String.isNotBlank(ol.Order_Quantity) ? Decimal.valueOf(ol.Order_Quantity) : null;
                                oi.Reverse_Quantity__c = String.isNotBlank(ol.Reverse_Quantity) ? Integer.valueOf(ol.Reverse_Quantity) : null;
                                oi.Cannel_Quantity__c = String.isNotBlank(ol.Cannel_Quantity) ? Integer.valueOf(ol.Cannel_Quantity) : null;
                                oi.Price__c = String.isNotBlank(ol.Price) ? Decimal.valueOf(ol.Price) : null;
                                oi.Item_Type__c = ol.Line_Attribute5;
                                oi.List_Price__c = String.isNotBlank(ol.Line_Attribute6) ? Decimal.valueOf(ol.Line_Attribute6) : null;
                                oi.Request_Date__c = String.isNotBlank(ol.Request_Date) ? Datetime.valueOf(ol.Request_Date) : null;
                                oi.Ship_Date__c = String.isNotBlank(ol.Ship_Date) ? Date.valueOf(ol.Ship_Date) : null;
                                oi.Line_Type__c = ol.Line_Type;
                                if(pricebookMap.get(ol.Line_Price_List) != null){
                                    oi.Price_Book__c = pricebookMap.get(ol.Line_Price_List);
                                }
                                oi.Price_Book_Text__c = ol.Line_Price_List;

                                Set<String> codeList = new Set<String>();
                                if(String.isNotBlank(ol.Promotion_Code)){
                                    codeList.add(ol.Promotion_Code);
                                }

                                if(String.isNotBlank(o.Whole_Order_Promotion_Code__c)){
                                    codeList.add(o.Whole_Order_Promotion_Code__c);
                                }

                                if(String.isNotBlank(o.Payment_Term_Promotion_Code__c)){
                                    codeList.add(o.Payment_Term_Promotion_Code__c);
                                }

                                if(codeList.size() >0){
                                    oi.Promation_Code__c = String.join(new List<String>(codeList), ',');
                                }else{
                                    oi.Promation_Code__c = ol.Promotion_Code;
                                }
                                //populate promotion on order item - update on 2021-05-05
                                if(String.isNotBlank(ol.Promotion_Code)){
                                    populatePromotionByCode(oi, o, ol.Promotion_Code, promotionWindowList, false, false);
                                }
                                oi.Line_Status__c = ol.Line_Status;
                                oi.isHold__c = ol.Line_IsHold;
                                oi.Hold_Reason__c = ol.Line_Hold_Reason;

                                // add 2021-07-13
                                oi.Selling_Warehouse__c = ol.Line_Attribute4;

                                //populate org code for order item
                                oi.Org_Code__c = o.Org_Code__c;

                                //line surcharge amount
                                if(String.isNotBlank(ol.Line_Attribute7)){
                                    oi.Surcharge_Amount__c = Decimal.valueOf(ol.Line_Attribute7);
                                }
                                if(String.isNotBlank(ol.Line_Attribute8)){
                                    oi.Business_Purpose__c = String.valueOf(ol.Line_Attribute8);
                                }

                                oiUpdateList.add(oi);
                            } else {
                                Order_Item__c oi = new Order_Item__c();
                                oi.Order__c = o.Id;
                                oi.CurrencyIsoCode = o.CurrencyIsoCode;
                                oi.OrderLine_OracleID__c = ol.OrderLine_OracleID;
                                if(purchaseOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) != null){
                                    oi.Purchase_Order_Item__c = purchaseOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number);
                                }
                                oi.Sample_Order_Item__c = sampleOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) != null ? sampleOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) : null;
                                if(String.isBlank(ol.Product) && isPlaceOrder && (ol.Line_Attribute5 == 'FG' || ol.Line_Attribute5 == 'MKT')){
                                    String eMsg = Label.CCM_ErrorMsg_LackOfProductInfo;
                                    throw new OrderException(eMsg.replace('{0}', 'Order').replace('{1}', ol.OrderLine_OracleID));
                                }
                                if(proMap.get(ol.Product) != null){
                                    oi.Product__c = proMap.get(ol.Product);
                                }else if(proEBSMap.get(ol.Product) != null){
                                    oi.Product__c = proEBSMap.get(ol.Product);
                                }else if(isPlaceOrder && (ol.Line_Attribute5 == 'FG' || ol.Line_Attribute5 == 'MKT')){
                                    String eMsg = Label.CCM_ErrorMsg_NotFindProduct;
                                    throw new OrderException(eMsg.replace('{0}', 'Order').replace('{1}', ol.OrderLine_OracleID).replace('{2}', ol.Product));
                                }
                                oi.Product_Text__c = ol.Product;
                                oi.Order_Quantity__c = String.isNotBlank(ol.Order_Quantity) ? Decimal.valueOf(ol.Order_Quantity) : null;
                                oi.Reverse_Quantity__c = String.isNotBlank(ol.Reverse_Quantity) ? Integer.valueOf(ol.Reverse_Quantity) : null;
                                oi.Cannel_Quantity__c = String.isNotBlank(ol.Cannel_Quantity) ? Integer.valueOf(ol.Cannel_Quantity) : null;
                                oi.Price__c = String.isNotBlank(ol.Price) ? Decimal.valueOf(ol.Price) : null;
                                oi.Item_Type__c = ol.Line_Attribute5;
                                oi.List_Price__c = String.isNotBlank(ol.Line_Attribute6) ? Decimal.valueOf(ol.Line_Attribute6) : null;
                                oi.Request_Date__c = String.isNotBlank(ol.Request_Date) ? Datetime.valueOf(ol.Request_Date) : null;
                                oi.Ship_Date__c = String.isNotBlank(ol.Ship_Date) ? Date.valueOf(ol.Ship_Date) : null;
                                oi.Line_Type__c = ol.Line_Type;
                                if(pricebookMap.get(ol.Line_Price_List) != null){
                                    oi.Price_Book__c = pricebookMap.get(ol.Line_Price_List);
                                }
                                oi.Price_Book_Text__c = ol.Line_Price_List;

                                Set<String> codeList = new Set<String>();
                                if(String.isNotBlank(ol.Promotion_Code)){
                                    codeList.add(ol.Promotion_Code);
                                }

                                if(String.isNotBlank(o.Whole_Order_Promotion_Code__c)){
                                    codeList.add(o.Whole_Order_Promotion_Code__c);
                                }

                                if(String.isNotBlank(o.Payment_Term_Promotion_Code__c)){
                                    codeList.add(o.Payment_Term_Promotion_Code__c);
                                }

                                if(codeList.size() >0){
                                    oi.Promation_Code__c = String.join(new List<String>(codeList), ',');
                                }else{
                                    oi.Promation_Code__c = ol.Promotion_Code;
                                }

                                // populate promotion on order item - update on 2021-05-05
                                if(String.isNotBlank(ol.Promotion_Code)){
                                    populatePromotionByCode(oi, o, ol.Promotion_Code, promotionWindowList, false, false);
                                }
                                oi.Whole_Order_Promotion_Window__c = o.Whole_Order_Promotion_Window__c;
                                oi.Payment_Term_Promotion_Window__c = o.Payment_Term_Promotion_Window__c;
                                oi.Line_Status__c = ol.Line_Status;
                                oi.isHold__c = ol.Line_IsHold;
                                oi.Hold_Reason__c = ol.Line_Hold_Reason;

                                // add 2021-07-13
                                oi.Selling_Warehouse__c = ol.Line_Attribute4;

                                //populate org code for order item
                                oi.Org_Code__c = o.Org_Code__c;

                                //line surcharge amount
                                if(String.isNotBlank(ol.Line_Attribute7)){
                                    oi.Surcharge_Amount__c = Decimal.valueOf(ol.Line_Attribute7);
                                }

                                if(String.isNotBlank(ol.Line_Attribute8)){
                                    oi.Business_Purpose__c = String.valueOf(ol.Line_Attribute8);
                                }

                                oiInsertList.add(oi);
                            }
                        }
                    }


                    if(oiInsertList.size() > 0){
                        insert oiInsertList;
                    }
                    if(oiUpdateList.size() > 0){
                        update oiUpdateList;
                    }

                    // reverse order 场景
                    if (String.isNotEmpty(reqObj.Attribute7) && reqObj.Attribute7.startsWith('RO')) {
                        reverseReturnProductQty = 0;
                        Boolean onlyReturn = isReturnOnly(reverseItems);
                        for (Reverse_Order_Item__c item : reverseItems) {
                            item.Warehouse_Received_Qty__c = 0;
                            for (Order_Item__c oi: [SELECT Id, Order_Quantity__c, Line_Status__c
                                    , Product_Text__c
                                    FROM Order_Item__c WHERE Order__r.Reverse_Order_Request__c = :o.Reverse_Order_Request__c]) {
                                if (item.Product2__r.ProductCode == oi.Product_Text__c) {
                                    reverseOrderLineStatus.add(oi.Line_Status__c);
                                    if (oi.Line_Status__c == 'RETURNED' || oi.Line_Status__c == 'CLOSED') {
                                        item.Warehouse_Received_Qty__c += oi.Order_Quantity__c == null ? 0 : oi.Order_Quantity__c.abs();
                                        reverseReturnProductQty += item.Warehouse_Received_Qty__c;
                                    }
                                }
                            }
                        }

                        Decimal reverseProductQty = 0;
                        for (Reverse_Order_Item__c item : reverseItems) {
                            reverseProductQty += item.Qty__c;
                        }

                        Set<String> receivedStatus = new Set<String>{'CANCELLED', 'RETURNED', 'CLOSED'};
                        if (reverseRequest.Reverse_Order_Type__c != 'Shortage') {
                            if(reverseRequest.Reverse_Order_Type__c == 'Wrong Product' && reverseRequest.Return_Goods_Status__c == 'Received') {
                                // no need to update status
                                reverseRequest.Completed_In_API__c = true;
                            }
                            else {
                                if (reverseReturnProductQty < reverseProductQty) {
                                    reverseRequest.Return_Goods_Status__c = 'Partial Received';
                                    if(onlyReturn && reverseReturnProductQty == 0) {
                                        reverseRequest.Return_Goods_Status__c = 'N/A';
                                    }
                                    if (receivedStatus.containsAll(reverseOrderLineStatus)) {
                                        reverseRequest.Return_Goods_Status__c = 'Received';
                                        reverseRequest.Completed_In_API__c = true;
                                    }
                                }
                                if (reverseReturnProductQty == reverseProductQty) {
                                    reverseRequest.Return_Goods_Status__c = 'Received';
                                    reverseRequest.Completed_In_API__c = true;
                                }
                                if(isHaveInvoice){
                                    if(invoiceNumber < reverseReturnProductQty){
                                        reverseRequest.Credit_Memo_Status__c = 'Partial Issued';
                                        if (reverseRequest.Return_Goods_Status__c == 'Received') {
                                            reverseRequest.Credit_Memo_Status__c = 'Issued';
                                            if(!isACE) {
                                                reverseRequest.Reverse_Order_Request_Status__c = 'Completed';
                                            }
                                        }

                                    }
                                    if(invoiceNumber == reverseReturnProductQty){
                                        reverseRequest.Credit_Memo_Status__c = 'Issued';
                                        if(!isACE) {
                                            reverseRequest.Reverse_Order_Request_Status__c = 'Completed';
                                        }
                                    }
                                }
                                Boolean isAllCancelled = true;
                                for(String lineStatus : reverseOrderLineStatus) {
                                    if(lineStatus != 'CANCELLED') {
                                        isAllCancelled = false;
                                    }
                                }
                                if(isAllCancelled) {
                                    reverseRequest.Return_Goods_Status__c = 'N/A';
                                    reverseRequest.Reverse_Order_Request_Status__c = 'Cancelled';
                                }
                            }
                        } else {
                            reverseRequest.Return_Goods_Status__c = 'N/A';
                            if (reverseReturnProductQty == reverseProductQty) {
                                reverseRequest.Completed_In_API__c = true;
                            }
                        }
                        if (reverseOrderLineStatus.size() == 1
                            && reverseOrderLineStatus.contains('AWAITING_RETURN')
                            && reverseReturnProductQty == 0) {
                            reverseRequest.Return_Goods_Status__c = 'N/A';
                            reverseRequest.Completed_In_API__c = false;
                        }
                        update reverseRequest;
                        update reverseItems;
                    }

                    if (polist.size() > 0){
                        Purchase_Order__c po1 = polist.get(0);
                        po1.Sync_Status__c = 'Success';
                        update po1;
                    }
                }else{
                    List<Order_Item__c> oilist = new List<Order_Item__c>();
                    Order o = new Order();
                    Boolean isPlaceOrder = false;
                    if(PRecordTypeName == 'Place_Parts_Order'){
                        o.RecordTypeId = CCM_Contants.PLACE_PARTS_ORDER_RECORDTYPEID;
                    }else{
                        o.RecordTypeId = CCM_Contants.PLACE_ORDER_RECORDTYPEID;
                        o.Dropship_Customer__c = polist.size() > 0 ? polist.get(0).Dropship_Customer__c : null;
                        isPlaceOrder = true;
                    }

                    // if reqObj.Attribute7 start with 'RO' set reverse order request to order
                    List<Reverse_Order_Item__c> reverseItems = new List<Reverse_Order_Item__c>();
                    Reverse_Order_Request__c reverseRequest;
                    List<Invoice_Item__c> invoiceItemList;
                    Boolean isHaveInvoice = false;
                    Decimal invoiceNumber = 0;
                    // reverse order 的场景
                    if (String.isNotEmpty(reqObj.Attribute7)
                        && reqObj.Attribute7.startsWith('RO')
                        && (reqObj.Order_Type == 'CNA Return - Sales & Inv.Adj.'
                            || reqObj.Order_Type == 'CA Return - Sales & Inv.Adj.')) {
                        List<Reverse_Order_Request__c> reverseOrderRequests = [SELECT Id,
                                                                               			Reverse_Order_Type__c,
                                                                                        CreatedBy.Profile.Name,
                                                                                        CreatedBy.Email,
                                                                                        Customer__r.Owner.Email,
                                                                                        Return_Goods_Status__c,
                                                                                        (SELECT Id,
                                                                                            Qty__c,
                                                                                            Next_Step_Action__c,
                                                                                            Product2__c,
                                                                                            Product2__r.ProductCode,
                                                                                            Warehouse_Received_Product_Amount__c,
                                                                                            Warehouse_Received_Qty__c,
                                                                                            Warehouse_Received_Subtotal__c,
                                                                                            Warehouse_Received_Total_Quantity__c,
                                                                                            Warehouse_Return_Number__c
                                                                                            FROM Reverse_Order_Items__r)
                                                                                    FROM Reverse_Order_Request__c
                                                                                    WHERE Reverse_Order_Request_Number__c = :reqObj.Attribute7];
                        o.Reverse_Order_Request__c = reverseOrderRequests[0].Id;
                        reverseRequest = reverseOrderRequests[0];
                        reverseRequest.Return_Order_Number__c = reqObj.Order_OracleID;
                        reverseItems = reverseOrderRequests[0].Reverse_Order_Items__r;
                        invoiceItemList = [SELECT id,Qty_Extended_Number__c FROM Invoice_Item__c WHERE Invoice__r.Reverse_Order_Request__r.Name =:reqObj.Attribute7];
                        if(invoiceItemList.size() > 0){
                            isHaveInvoice = true;
                            for(Invoice_Item__c il : invoiceItemList){
                                invoiceNumber += il.Qty_Extended_Number__c.abs();
                            }
                        }
                    }

                    o.Order_OracleID__c = OrderOracleID;
                    o.Order_Number__c = reqObj.Order_Number;
                    if(polist.size() > 0){
                        o.Purchase_Order__c = polist.get(0).Id;
                    }
                    if(sampleList != null && sampleList.size() > 0){
                        o.Sample_Order__c = sampleList[0].id;
                    }
                    o.Purchase_Order_Text__c = Salesforce_PurchaseOrder_Number;
                    String customerAccountNumber = reqObj.Customer;
                    Boolean isACE = false;
                    Set<String> aceCustomersSet = new Set<String>();
                    aceCustomersSet.addAll(Label.CCM_WarrantyReturn_ACECustomers.split(';'));
                    if(aceCustomersSet.contains(customerAccountNumber)) {
                        isACE = true;
                    }
                    List<Account> acclist = [select id from Account where AccountNumber = :customerAccountNumber];
                    if(acclist.size() > 0){
                        o.AccountId = acclist.get(0).Id;
                        if (reqObj.Order_Type != 'CNA Sample Order Only') {
                            initialPromotionInfos(promotionWindowList, o.AccountId, allPromotionCodeSet);
                        }
                    }
                    if(isPlaceOrder && acclist.size() == 0){
                        String eMsg = Label.CCM_ErrorMsg_NotFindCustomer;
                        throw new OrderException(eMsg.replace('{0}', 'Order').replace('{1}', reqObj.Order_OracleID).replace('{2}', reqObj.Customer));
                    }
                    o.Account_Text__c = customerAccountNumber;
                    o.EffectiveDate = Date.today();
                    o.PO_Number__c = reqObj.PO_Number;
                    String shipTo = reqObj.ShipTo;
                    List<Address_With_Program__c> shipAPlist = [select id, Account_Address__c, Account_Address__r.Store_Location__c, Account_Address__r.EGO_Agency__c, Account_Address__r.SKIL_SKILSAW_Agency__c, Account_Address__r.FLEX_Agency__c from Address_With_Program__c where Customer_Line_Oracle_ID__c = :shipTo];
                    if(shipAPlist.size() > 0){
                        o.ShipTo__c = shipAPlist.get(0).Id;
                        o.Store_Location__c = shipAPlist.get(0).Account_Address__c != null ? shipAPlist.get(0).Account_Address__r.Store_Location__c : null;
                    }
                    o.ShipTo_Text__c = shipTo;
                    String billTo = reqObj.BillTo;
                    List<Address_With_Program__c> billAPlist = [select id,Program__r.OwnerId,Program__c,Program__r.Sales_Group__c from Address_With_Program__c where Customer_Line_Oracle_ID__c = :billTo];
                    if(billAPlist.size() > 0){
                        o.BillTo__c = billAPlist.get(0).Id;
                        if(billAPlist.get(0).Program__c != null){
                            o.OwnerId = billAPlist.get(0).Program__r.OwnerId;
                        }

                        o.Sales_Group__c = billAPlist.get(0).Program__r.Sales_Group__c;
                    }
                    o.BillTo_Text__c = billTo;
                    o.Order_Type__c = reqObj.Order_Type;
                    String PriceList = reqObj.Price_List;
                    List<PriceBook2> pricebooklist2 = [select id,Price_Book_OracleID__c,Name from PriceBook2 where Name = :PriceList];
                    if(pricebooklist2.size() > 0){
                        o.Price_Book__c = pricebooklist2.get(0).Id;
                    }
                    o.Price_Book_Text__c = PriceList;
                    o.Sales_Rep__c = reqObj.Sales_Rep;
                    // get Sales Agency by sales_rep Code
                    if (String.isNotBlank(reqObj.Sales_Rep)){
                        Id agencyId = Util.getSalesAgencyIdByOracleId(reqObj.Sales_Rep);
                        o.Sales_Agency__c = agencyId;
                    }
                    if (String.isEmpty(reqObj.Org_Code) || reqObj.Org_Code == CCM_Constants.ORG_CODE_CNA && shipAPlist.size() > 0) {
                        if (brandSet.size() == 1 && brandSet.contains('EGO') && String.isNotEmpty(shipAPlist[0].Account_Address__r.EGO_Agency__c)) {
                            o.Sales_Agency__c = shipAPlist[0].Account_Address__r.EGO_Agency__c;
                        } else if (brandSet.size() >=1 && !brandSet.contains('EGO') && (String.isNotEmpty(shipAPlist[0].Account_Address__r.SKIL_SKILSAW_Agency__c) || String.isNotEmpty(shipAPlist[0].Account_Address__r.FLEX_Agency__c))) {
                            o.Sales_Agency__c = String.isNotEmpty(shipAPlist[0].Account_Address__r.SKIL_SKILSAW_Agency__c) ? shipAPlist[0].Account_Address__r.SKIL_SKILSAW_Agency__c : shipAPlist[0].Account_Address__r.FLEX_Agency__c;
                        }
                    }
                    o.Date_Order__c = String.isNotBlank(reqObj.Date_Order) ? Datetime.valueOf(reqObj.Date_Order) : null;
                    o.CurrencyIsoCode = reqObj.CurrencyCode;
                    o.Payment_Term__c = reqObj.Payment_Term;
                    o.Freight_Term__c = reqObj.Freight_Term;
                    o.Shipping_Method__c = reqObj.Shipping_Method;
                    o.Sales_Channel__c = reqObj.Sales_Channel;
                    o.Shipping_Priority__c = reqObj.Shipping_Priority;
                    o.Notes__c = reqObj.Notes;
                    o.Expected_Delivery_Date__c = String.isNotBlank(reqObj.Expected_Delivery_Date) ? Date.valueOf(reqObj.Expected_Delivery_Date) : null;
                    o.Carrier_Code__c = reqObj.Carrier_Code;
                    o.Customer_Freight_Account__c = reqObj.Customer_Freight_Account;
                    o.Handling_Fee__c = String.isNotBlank(reqObj.Handling_Fee) ? Decimal.valueOf(reqObj.Handling_Fee) : null;
                    o.Feright_Fee__c = String.isNotBlank(reqObj.Freight_Fee) ? Decimal.valueOf(reqObj.Freight_Fee) : null;
                    o.Dropship_Name__c = reqObj.Dropship_Name;
                    o.Dropship_Address1__c = reqObj.Dropship_Address1;
                    o.Dropship_Address2__c = reqObj.Dropship_Address2;
                    o.Telephone_Number__c = reqObj.Telephone_Number;
                    o.Dropship_Country__c = reqObj.Dropship_Country;
                    o.Dropship_ZIP__c = reqObj.Dropship_ZIP;
                    o.Dropship_State__c = reqObj.Dropship_State;
                    o.Dropship_City__c = reqObj.Dropship_City;
                    o.Org_Code__c = reqObj.Org_Code;
                    o.Order_Status__c = OrderStatus;
                    o.IsHold__c = reqObj.IsHold;
                    o.Hold_Reason__c = reqObj.Hold_Reason;
                    String source = reqObj.Order_Source;
                    source = OrderSourceMap.get(source) != null ? OrderSourceMap.get(source) : source;
                    o.Order_Source__c = source;
                    List<Warranty_Return_Claim__c> warrantyReturnList = [SELECT Id FROM Warranty_Return_Claim__c WHERE Name = :reqObj.Salesforce_PurchaseOrder_Number];
                    o.Warranty_Return_Request__c = warrantyReturnList.size() > 0 ? warrantyReturnList[0].Id : null;

                    if(String.isNotBlank(reqObj.Handling_Fee)){
                        totalAmount = totalAmount + Decimal.valueOf(reqObj.Handling_Fee);
                    }
                    if(String.isNotBlank(reqObj.Freight_Fee)){
                        totalAmount = totalAmount + Decimal.valueOf(reqObj.Freight_Fee);
                    }
                    if(PRecordTypeName == 'Place_Parts_Order'){
                        totalAmount = totalAmount - discount;
                    }
                    o.Total_Amount__c = totalAmount;
                    o.Status = 'Draft';

                    //whole order&payment term promotion
                    if(String.isNotBlank(reqObj.Attribute2)){
                        o.Whole_Order_Promotion_Code__c = reqObj.Attribute2;
                        populatePromotionByCode(null, o, reqObj.Attribute2, promotionWindowList, true, false);
                    }
                    if(String.isNotBlank(reqObj.Attribute3)){
                        o.Payment_Term_Promotion_Code__c = reqObj.Attribute3;
                        populatePromotionByCode(null, o, reqObj.Attribute3, promotionWindowList, false, true);
                    }

                    //CA tax
                    if(String.isNotBlank(reqObj.Attribute4)){
                        o.GST__c = Decimal.valueOf(reqObj.Attribute4);
                        o.Total_Amount__c+=o.GST__c;
                    }
                    if(String.isNotBlank(reqObj.Attribute5)){
                        o.HST__c = Decimal.valueOf(reqObj.Attribute5);
                        o.Total_Amount__c+=o.HST__c;
                    }
                    if(String.isNotBlank(reqObj.Attribute6)){
                        o.QST__c = Decimal.valueOf(reqObj.Attribute6);
                        o.Total_Amount__c+=o.QST__c;
                    }

                    //Surcharge Amount
                    if(String.isNotBlank(reqObj.Attribute8)){
                        o.Surcharge_Amount__c = Decimal.valueOf(reqObj.Attribute8);
                        o.Total_Amount__c+=o.Surcharge_Amount__c;
                    }
                    // 23.12.27 增加9997客户仅针对SF中已存在的Sample Order才创建Order的逻辑
                    if(customerAccountNumber == '9997'){
                        if(o.Sample_Order__c != null){
                            insert o;
                        }
                    // 23.12.27 end
                    }else{
                        insert o;
                    }

                    Decimal reverseReturnProductQty = 0;
                    Set<String> reverseOrderLineStatus = new Set<String>();
                    if(reqObj.OrderLine != null && reqObj.OrderLine.size() > 0){
                        for(OrderLine ol : reqObj.OrderLine){
                            Order_Item__c oi = new Order_Item__c();
                            oi.Order__c = o.Id;
                            oi.CurrencyIsoCode = o.CurrencyIsoCode;
                            oi.OrderLine_OracleID__c = ol.OrderLine_OracleID;
                            if(purchaseOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) != null){
                                oi.Purchase_Order_Item__c = purchaseOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number);
                            }
                            oi.Sample_Order_Item__c = sampleOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) != null ? sampleOrderLineMap.get(ol.Salesforce_PurchaseOrderLine_Number) : null;
                            if(String.isBlank(ol.Product) && isPlaceOrder && (ol.Line_Attribute5 == 'FG' || ol.Line_Attribute5 == 'MKT')){
                                String eMsg = Label.CCM_ErrorMsg_LackOfProductInfo;
                                throw new OrderException(eMsg.replace('{0}', 'Order').replace('{1}', ol.OrderLine_OracleID));
                            }
                            if(proMap.get(ol.Product) != null){
                                oi.Product__c = proMap.get(ol.Product);
                            }else if(proEBSMap.get(ol.Product) != null){
                                oi.Product__c = proEBSMap.get(ol.Product);
                            }else if(isPlaceOrder && (ol.Line_Attribute5 == 'FG' || ol.Line_Attribute5 == 'MKT')){
                                String eMsg = Label.CCM_ErrorMsg_NotFindProduct;
                                throw new OrderException(eMsg.replace('{0}', 'Order').replace('{1}', ol.OrderLine_OracleID).replace('{2}', ol.Product));
                            }
                            oi.Product_Text__c = ol.Product;
                            oi.Order_Quantity__c = String.isNotBlank(ol.Order_Quantity) ? Decimal.valueOf(ol.Order_Quantity) : null;
                            oi.Reverse_Quantity__c = String.isNotBlank(ol.Reverse_Quantity) ? Integer.valueOf(ol.Reverse_Quantity) : null;
                            oi.Cannel_Quantity__c = String.isNotBlank(ol.Cannel_Quantity) ? Integer.valueOf(ol.Cannel_Quantity) : null;
                            oi.Price__c = String.isNotBlank(ol.Price) ? Decimal.valueOf(ol.Price) : null;
                            oi.Item_Type__c = ol.Line_Attribute5;
                            oi.List_Price__c = String.isNotBlank(ol.Line_Attribute6) ? Decimal.valueOf(ol.Line_Attribute6) : null;
                            oi.Request_Date__c = String.isNotBlank(ol.Request_Date) ? Datetime.valueOf(ol.Request_Date) : null;
                            oi.Ship_Date__c = String.isNotBlank(ol.Ship_Date) ? Date.valueOf(ol.Ship_Date) : null;
                            oi.Line_Type__c = ol.Line_Type;
                            if(pricebookMap.get(ol.Line_Price_List) != null){
                                oi.Price_Book__c = pricebookMap.get(ol.Line_Price_List);
                            }
                            oi.Price_Book_Text__c = ol.Line_Price_List;

                            Set<String> codeList = new Set<String>();
                            if(String.isNotBlank(ol.Promotion_Code)){
                                codeList.add(ol.Promotion_Code);
                            }
                            if(String.isNotBlank(o.Whole_Order_Promotion_Code__c)){
                                codeList.add(o.Whole_Order_Promotion_Code__c);
                            }

                            if(String.isNotBlank(o.Payment_Term_Promotion_Code__c)){
                                codeList.add(o.Payment_Term_Promotion_Code__c);
                            }

                            if(codeList.size() >0){
                                oi.Promation_Code__c = String.join(new List<String>(codeList), ',');
                            }else{
                                oi.Promation_Code__c = ol.Promotion_Code;
                            }


                            // populate promotion on order item - update on 2021-05-05
                            if(String.isNotBlank(ol.Promotion_Code)){
                                populatePromotionByCode(oi, o, ol.Promotion_Code, promotionWindowList, false, false);
                            }
                            oi.Whole_Order_Promotion_Window__c = o.Whole_Order_Promotion_Window__c;
                            oi.Payment_Term_Promotion_Window__c = o.Payment_Term_Promotion_Window__c;
                            oi.Line_Status__c = ol.Line_Status;
                            oi.isHold__c = ol.Line_IsHold;
                            oi.Hold_Reason__c = ol.Line_Hold_Reason;

                            // add 2021-07-13
                            oi.Selling_Warehouse__c = ol.Line_Attribute4;
                            // get reverse order item info, if Attribute7 start witho "RO"
                            if (String.isNotEmpty(reqObj.Attribute7) && reqObj.Attribute7.startsWith('RO')) {
                                for (Reverse_Order_Item__c item : reverseItems) {
                                    if (item.Product2__r.ProductCode == oi.Product_Text__c) {
                                        reverseOrderLineStatus.add(oi.Line_Status__c);
                                        if (oi.Line_Status__c == 'RETURNED' || oi.Line_Status__c == 'CLOSED') {
                                            if (item.Warehouse_Received_Qty__c == null) {
                                                item.Warehouse_Received_Qty__c = 0;
                                            }
                                            item.Warehouse_Received_Qty__c += oi.Order_Quantity__c == null ? 0 : oi.Order_Quantity__c.abs();
                                            reverseReturnProductQty += item.Warehouse_Received_Qty__c;
                                        }
                                    }
                                }
                            }

                            //populate org code for order item
                            oi.Org_Code__c = o.Org_Code__c;

                            //line surcharge amount
                            if(String.isNotBlank(ol.Line_Attribute7)){
                                oi.Surcharge_Amount__c = Decimal.valueOf(ol.Line_Attribute7);
                            }

                            if(String.isNotBlank(ol.Line_Attribute8)){
                                oi.Business_Purpose__c = String.valueOf(ol.Line_Attribute8);
                            }

                            oilist.add(oi);
                        }
                    }
                    if (String.isNotEmpty(reqObj.Attribute7) && reqObj.Attribute7.startsWith('RO')) {
                        Boolean returnOnly = isReturnOnly(reverseItems);
                        Decimal reverseProductQty = 0;
                        for (Reverse_Order_Item__c item : reverseItems) {
                            reverseProductQty += item.Qty__c;
                        }

                        Set<String> receivedStatus = new Set<String>{'CANCELLED', 'RETURNED', 'CLOSED'};
                        if (reverseRequest.Reverse_Order_Type__c != 'Shortage') {
                            if(reverseRequest.Reverse_Order_Type__c == 'Wrong Product' && reverseRequest.Return_Goods_Status__c == 'Received') {
                                // no need to update status
                                reverseRequest.Completed_In_API__c = true;
                            }
                            else {
                                if (reverseReturnProductQty < reverseProductQty) {
                                    reverseRequest.Return_Goods_Status__c = 'Partial Received';
                                    if(returnOnly && reverseReturnProductQty == 0) {
                                        reverseRequest.Return_Goods_Status__c = 'N/A';
                                    }
                                    if (receivedStatus.containsAll(reverseOrderLineStatus)) {
                                        reverseRequest.Return_Goods_Status__c = 'Received';
                                        reverseRequest.Completed_In_API__c = true;
                                    }

                                }
                                if (reverseReturnProductQty == reverseProductQty) {
                                    reverseRequest.Return_Goods_Status__c = 'Received';
                                    reverseRequest.Completed_In_API__c = true;
                                }
                                if(isHaveInvoice){
                                    if(invoiceNumber < reverseReturnProductQty){
                                        reverseRequest.Credit_Memo_Status__c = 'Partial Issued';
                                        if (reverseRequest.Return_Goods_Status__c == 'Received') {
                                            reverseRequest.Credit_Memo_Status__c = 'Issued';
                                            if(!isACE) {
                                                reverseRequest.Reverse_Order_Request_Status__c = 'Completed';
                                            }
                                        }

                                    }
                                    if(invoiceNumber == reverseReturnProductQty){
                                        reverseRequest.Credit_Memo_Status__c = 'Issued';
                                        if(!isACE) {
                                            reverseRequest.Reverse_Order_Request_Status__c = 'Completed';
                                        }
                                    }
                                }
                                Boolean isAllCancelled = true;
                                for(String lineStatus : reverseOrderLineStatus) {
                                    if(lineStatus != 'CANCELLED') {
                                        isAllCancelled = false;
                                    }
                                }
                                if(isAllCancelled) {
                                    reverseRequest.Return_Goods_Status__c = 'N/A';
                                    reverseRequest.Reverse_Order_Request_Status__c = 'Cancelled';
                                }
                            }
                        } else {
                            reverseRequest.Return_Goods_Status__c = 'N/A';
                            if (reverseReturnProductQty == reverseProductQty) {
                                reverseRequest.Completed_In_API__c = true;
                            }
                        }
                        if (reverseOrderLineStatus.size() == 1 && reverseOrderLineStatus.contains('AWAITING_RETURN') && reverseReturnProductQty == 0) {
                            reverseRequest.Return_Goods_Status__c = 'N/A';
                            reverseRequest.Completed_In_API__c = false;
                        }
                        update reverseRequest;
                        update reverseItems;
                    }
                    insert oilist;
                    if(polist.size() > 0){
                        Purchase_Order__c po1 = polist.get(0);
                        po1.Sync_Status__c = 'Success';
                        update po1;
                    	insertAMWareMerchandisingOrderItems(OrderStatus, polist.get(0), o.Id);
                    }
                }


                if (String.isNotEmpty(reqObj.Salesforce_PurchaseOrder_Number)
                    && reqObj.Salesforce_PurchaseOrder_Number.startsWith('RA')
                    && (reqObj.Order_Type == 'CNA Return Order - Warranty'
                        || reqObj.Order_Type == 'CA Return Order - Warranty')) {
                    handleWarrantyReturnOrder(reqObj.Salesforce_PurchaseOrder_Number);
                }
                if(reqObj.Order_Type == 'CNA Sample Order Only') {
                    dealSampleOrderInventory(reqObj.Salesforce_PurchaseOrder_Number, reqObj.OrderLine, OrderStatus);
                }
            }
            oiData.Return_Code__c = 'S';
            update oiData;
        }catch (OrderException e){
            oiData.Return_Code__c = 'E';
            oiData.Error_Msg__c = e.getMessage() + '\n' + e.getStackTraceString();
            update oiData;
        }catch (Exception e) {
            if(e.getMessage().toUpperCase().contains('ROW LOCK') || e.getMessage().toUpperCase().contains('UNABLE_TO_LOCK_ROW') || e.getMessage().contains('Please try again')) {
                return;
            }
            oiData.Return_Code__c = 'F';
            oiData.Error_Msg__c = e.getMessage() + '\n' + e.getStackTraceString();
            update oiData;
            String logId = Util.logIntegration(
                'Order Exception','CCM_RestService_DealOMSOrderInfo',
                'POST', oiData.Error_Msg__c ,JSON.serialize(reqObj),
                JSON.serialize(oiData)
            );
            Util.pushExceptionEmail('Accept Order Info', logId, oiData.Error_Msg__c);
        }

        if(oiData.Return_Code__c != 'NA') {
            CCM_OrderInterfaceUtil.sendResponse(oiData);
        }
    }

    global class OrderLine {
		global String Salesforce_PurchaseOrderLine_Number;
		global String Product;
		global String Order_Quantity;
        global String Reverse_Quantity;
        global String Cannel_Quantity;
		global String Line_Type;
		global String Request_Date;
		global String Line_Price_List;
		global String Price;
		global String OrderLine_OracleID;
		global String Ship_Date;
        global String Promotion_Code;
        global String Line_Status;
        global String Line_Attribute1;
        global String Line_Attribute2;
        global String Line_Attribute3;
        global String Line_Attribute4;
        global String Line_Attribute5;
        global String Line_Attribute6;
        global String Line_Attribute7;
        global String Line_Attribute8;
        global String Line_Attribute9;
        global String Line_Attribute10;
        global String Line_IsHold;
        global String Line_Hold_Reason;

        global String OrderInnerExternalId;
	}

	global class ReqestObj {
		global String PO_Number;
		global String Carrier_Code;
		global String ShipTo;
		global String Salesforce_PurchaseOrder_Number;
		global String BillTo;
		global List<OrderLine> OrderLine;
		global String CurrencyCode;
		global String Customer_Freight_Account;
		global String Shipping_Priority;
		global String Sales_Channel;
		global String Order_Type;
		global String Order_OracleID;
		global String Shipping_Method;
		global String Date_Order;
		global String Payment_Term;
		global String Handling_Fee;
		global String Customer;
		global String Sales_Rep;
		global String Price_List;
		global String Freight_Term;
		global String Expected_Delivery_Date;
		global String Notes;
		global String Order_Status;

        global String Org_Code;
        global String Freight_Fee;
        global String Error_Message;
        global String Dropship_Name;
        global String Dropship_Address1;
        global String Dropship_Address2;
        global String Telephone_Number;
        global String Dropship_Country;
        global String Dropship_ZIP;
        global String Dropship_State;
        global String Dropship_City;
        global String Attribute1;
        global String Attribute2;
        global String Attribute3;
        global String Attribute4;
        global String Attribute5;
        global String Attribute6;
        global String Attribute7;
        global String Attribute8;
        global String Attribute9;
        global String Attribute10;

        global String Order_Number;
        global String IsHold;
        global String Hold_Reason;
        global String Order_Source;

        global String OrderInnerExternalId;
	}

	global static List<ReqestObj> parse(String jsonStr) {
		return (List<ReqestObj>) JSON.deserialize(jsonStr, List<ReqestObj>.class);
	}

    global class ResultObj {
        global String oracleId;
        global String returnCode;
        global String returnMsg;
    }

    public static Map<String, String> OrderSourceMap = new Map<String, String> {
        // 'sf-edi' => 'EDI',
        // '复制' => 'EBS',
        // '联机' => 'EBS',
        // 'Salesforce' => 'Salesforce'
        'EDI' => 'EDI',
        'Manual'=> 'Manual',
        'SF' => 'Salesforce'
    };

    /**
     * @description Create order items for amware merchandising products.
     */
    public static void insertAMWareMerchandisingOrderItems(String OrderStatus, Purchase_Order__c purchaseOrder, String orderId){
        Order orderObj = [SELECT Id, OrderNumber FROM Order WHERE Id=:orderId];
        Set<String> merchProductCodes = new Set<String>();
        List<Purchase_Order_Item__c> purchaseOrderItemList = [
            SELECT Id,
                    Name,
                    Product__c,
                    Product__r.Name,
                    Product__r.ProductCode,
                    Price_Book__c,
                    Price_Book__r.Name,
                    Promotion__c,
                    Promotion__r.Promotion_Code_For_External__c,
                    Whole_Order_Promotion__c,
                    Whole_Order_Promotion__r.Promotion_Code_For_External__c,
                    Ship_Date__c,
                    Unit_Price__c,
                    List_Price__c,
                    Quantity__c,
                    Sub_Total__c,
                    CurrencyIsoCode,
                    CreatedDate
            FROM Purchase_Order_Item__c
            WHERE Purchase_Order__c =:purchaseOrder.Id
            AND Product__r.RecordType.DeveloperName = 'Amware_Merchandising'
        ];
        if(purchaseOrderItemList.size() > 0){
            List<Order_Item__c> orderItemList = new List<Order_Item__c>();
            for(Purchase_Order_Item__c pItem : purchaseOrderItemList){
                Order_Item__c oItem = new Order_Item__c();
                oItem.Order__c = orderId;
                oItem.CurrencyIsoCode = pItem.CurrencyIsoCode;
                oItem.OrderLine_OracleID__c = null;
                oItem.Purchase_Order_Item__c = pItem.Id;
                oItem.Product__c = pItem.Product__c;
                oItem.Product_Text__c = pItem.Product__r.ProductCode;
                oItem.Order_Quantity__c = pItem.Quantity__c;
                oItem.Reverse_Quantity__c = null;
                oItem.Cannel_Quantity__c = null;
                oItem.Price__c = pItem.Unit_Price__c;
                oItem.Request_Date__c = pItem.CreatedDate;
                oItem.Ship_Date__c = (Datetime)PItem.Ship_Date__c;
                oItem.Line_Type__c = 'CNA Amware Merchandising';
                oItem.Price_Book__c = pItem.Price_Book__c;
                oItem.Price_Book_Text__c = pItem.Price_Book__r.Name;
                oItem.Whole_Order_Promotion__c = pItem.Whole_Order_Promotion__c;
                oItem.Promotion__c = pItem.Promotion__c;
                if (String.isNotBlank(pItem.Whole_Order_Promotion__r.Promotion_Code_For_External__c) || String.isNotBlank(pItem.Promotion__r.Promotion_Code_For_External__c)) {
                    Set<String> promotionCodeSet = new Set<String>();
                    if (String.isNotBlank(pItem.Whole_Order_Promotion__r.Promotion_Code_For_External__c)) {
                        promotionCodeSet.add(pItem.Whole_Order_Promotion__r.Promotion_Code_For_External__c);
                    }
                    if (String.isNotBlank(pItem.Promotion__r.Promotion_Code_For_External__c)) {
                        promotionCodeSet.add(pItem.Promotion__r.Promotion_Code_For_External__c);
                    }
                    List<String> promotionCodeList = new List<String>();
                    promotionCodeList.addAll(promotionCodeSet);
                    oItem.Promation_Code__c = String.join(promotionCodeList, ',');
                }
                oItem.Line_Status__c = 'AWAITING_SHIPPING';
                oItem.isHold__c = null;
                oItem.Hold_Reason__c = null;
                orderItemList.add(oItem);

                merchProductCodes.add(pItem.Product__r.ProductCode);
            }
            insert orderItemList;

            List<User> caSalesManagers = [SELECT Id, Email FROM User WHERE UserRole.Name = 'CA TT Sales Manage' AND IsActive = true];

            List<Messaging.SingleEmailMessage> mailsToSend = new List<Messaging.SingleEmailMessage>();
            List<EmailTemplate> templateList = [SELECT Id FROM EmailTemplate WHERE DeveloperName = 'CCM_Amware_Merchandising_Notification' LIMIT 1];
            if (purchaseOrder.Customer__c != null){
                List<String> toEmailList = new List<String>{purchaseOrder.Customer__r.Owner.Email};
                if(purchaseOrder.ORG_ID__c == 'CCA'){
                    if(caSalesManagers.size() > 0){
                        toEmailList.add(caSalesManagers[0].Email);
                    }
                }
                Messaging.SingleEmailMessage mail = Messaging.renderStoredEmailTemplate(templateList[0].Id, null, orderId);
                mail.setToAddresses(toEmailList);
                mail.saveAsActivity = false;
                mailsToSend.add(mail);
            }
            //Send Email
            if(mailsToSend != null && mailsToSend.size() > 0)
            {
                Messaging.sendEmail(mailsToSend);
            }

            //the string of merchandising product codes
            String codesStr = '';
            for(String pCode : merchProductCodes){
                codesStr+= pCode + ',';
            }
            if(String.isNotEmpty(codesStr)){
                codesStr = codesStr.removeEnd(',');
            }

            //Create Tasks
            List<Task> taskList = new List<Task>();
            String taskUserName = Label.CCM_Merchandising_Task_User;
            List<User> userList = [SELECT Id FROM User WHERE Username =:taskUserName];
            System.debug('***** UserList:' + userList);
            String taskUserId = '';
            if(userList.size() > 0){
                User taskUser = userList[0];
                taskUserId = taskUser.Id;
            }
            System.debug('***** taskUserId:' + taskUserId);
            // the task of booking orders
            Task orderTask = new Task();
            orderTask.OwnerId = purchaseOrder.Customer__r.OwnerId;
            orderTask.WhatId = orderId;
            orderTask.Subject = 'Amware merchandising order pending for placement.';
            orderTask.Description = String.format('Order {0} contains Amware merchandising which is required to place in Amware.\r\n The merchandising is/are {1}. ',
                                                   new List<String>{orderObj.OrderNumber, codesStr});
            orderTask.Priority = 'Normal';
            orderTask.Status = 'In Progress';
            orderTask.IsReminderSet = true;
            orderTask.ReminderDateTime = Datetime.now();
            orderTask.ActivityDate = Date.today().addDays(5);
            if(String.isNotEmpty(taskUserId)){
                orderTask.CreatedById = taskUserId;
            }
            taskList.add(orderTask);

            if(purchaseOrder.ORG_ID__c == 'CCA'){
                if(caSalesManagers.size() > 0){
                    orderTask.OwnerId = caSalesManagers[0].Id;
                    taskList.add(orderTask);
                }
            }

            // the task of adding shipments
            Task shipmentTask = new Task();
            shipmentTask.OwnerId = purchaseOrder.Customer__r.OwnerId;
            shipmentTask.WhatId = orderId;
            shipmentTask.Subject = 'Amware merchandising is needed to add shipment(s).';
            shipmentTask.Description = String.format('Order {0} contains Amware merchandising which is required to add shipment(s) in Salesforce. Please click [Add Shipment] button in order detail page and input all the tracking #(s).',
                                                    new List<String>{orderObj.OrderNumber});
            shipmentTask.Priority = 'Normal';
            shipmentTask.Status = 'In Progress';
            shipmentTask.IsReminderSet = true;
            shipmentTask.ReminderDateTime = Datetime.now();
            shipmentTask.ActivityDate = Date.today().addDays(7);
            if(String.isNotEmpty(taskUserId)){
                shipmentTask.CreatedById = taskUserId;
            }
            taskList.add(shipmentTask);

            if(purchaseOrder.ORG_ID__c == 'CCA'){
                if(caSalesManagers.size() > 0){
                    shipmentTask.OwnerId = caSalesManagers[0].Id;
                    taskList.add(shipmentTask);
                }
            }

            insert taskList;

        }
    }

    /**
     * @description If the order doesn't have a purchase order,
     * use promotion code and customer id to populate promotion info in order item records.
     */
    public static void populatePromotionByCode(Order_Item__c oi, Order o, String promotionCode, List<Promotion_Window__c> promotionWindowList, Boolean isWholeOrderPromotion, Boolean isPaymentTermPromotion){
        if(promotionWindowList.size() >0){
            for(Promotion_Window__c pw : promotionWindowList){
                if(pw.Promotion__r.Promotion_Code_For_External__c == promotionCode
                    && o.Date_Order__c >= pw.Start_Date__c
                    && o.Date_Order__c <= pw.End_Date__c){
                    if(isWholeOrderPromotion){
                        o.Whole_Order_Promotion__c = pw.Promotion__c;
                        o.Whole_Order_Promotion_Window__c = pw.Id;
                    }else if(isPaymentTermPromotion){
                        o.Payment_Term_Promotion__c	= pw.Promotion__c;
                        o.Payment_Term_Promotion_Window__c = pw.Id;
                    }else if(!isWholeOrderPromotion && !isPaymentTermPromotion){
                        oi.Promotion__c = pw.Promotion__c;
                        oi.Regular_Promotion_Window__c = pw.Id;
                    }
                }
            }
        }
    }

     /**
     * @description Search all promotion windows for current customer
     */
    public static void initialPromotionInfos(List<Promotion_Window__c> promotionWindowList, String customerId, Set<String> allCodeSet){
        //get all promotions of current customer
        List<Promotion_Target_Customer__c> promo2CustomerList = [
            SELECT
                Id,
                Name,
                Promotion__c
            FROM Promotion_Target_Customer__c
            WHERE Customer__c =:customerId
            AND Promotion__r.RecordType.DeveloperName = 'Sell_In_Promotion'
            AND Promotion__r.Promotion_Code_For_External__c IN:allCodeSet
        ];

        if(promo2CustomerList.size() >0){
            Set<String> promoIds = new Set<String>();
            for(Promotion_Target_Customer__c p2cItem : promo2CustomerList){
                promoIds.add(p2cItem.Promotion__c);
            }

            promotionWindowList.addAll([
                SELECT
                    Id,
                    Promotion__c,
                    Promotion__r.Promotion_Code_For_External__c,
                    Promotion__r.Promotion_Type__c,
                	Promotion_Window_Status2__c,
                    Start_Date__c,
                    End_Date__c
                FROM Promotion_Window__c
                WHERE Promotion__c IN:promoIds
            ]);
        }

    }

    //Order Exception
    private class OrderException extends Exception {
    }

    /**
     * @description handleWarrantyReturnOrder description
     * 1. Request 全是DIF
     *      - Return Goods Status 显示 ’N/A‘
     *
     * 2. Request 中有RTV
     *      - Warehouse received total quantity 根据Oracle返回的收到货的数量更新
     *      - Return Goods Status 显示：
     *          -- 'Not Received', if warehouse received total quantity =0
     *          -- ’Partially Received', if 0< warehouse received total quantity<应收的RTV数量
     *          -- 'Received', if warehouse received total quantity = 应收的RTV数量
     * @param  objOrder   objOrder description
     */
    public static void handleWarrantyReturnOrder(String warrantyRequest) {

        /*

        */
        List<Warranty_Return_Claim__c> warrantyReturn = [SELECT Id,
                                                                (SELECT Id, Model__r.ProductCode, Quantity__c, Warehouse_Received_Total_Quantity__c, DIF_RTV__c FROM Warranty_Return_Claim_Item__r)
                                                            FROM Warranty_Return_Claim__c
                                                            WHERE Name = :warrantyRequest];
        if (warrantyReturn.size() == 0) return;
        List<AggregateResult> itemList = [SELECT Product_Text__c productCode, SUM(Order_Quantity__c) qty
                                            FROM Order_Item__c
                                            WHERE Order__r.Purchase_Order_Text__c = :warrantyRequest
                                            AND Line_Status__c IN ('RETURNED', 'CLOSED')
                                            GROUP BY Product_Text__c];
        Map<String, Integer> productReceivedQtyMap = new Map<String, Integer>();
        for (AggregateResult item : itemList) {
            productReceivedQtyMap.put(String.valueOf(item.get('productCode')), Integer.valueOf(item.get('qty')));
        }
        System.debug('*** productReceivedQtyMap: ' + productReceivedQtyMap);

        String returnGoodsStatus = 'N/A';
        Integer totalQuanity = 0;
        Integer totalReceivedQuantity = 0;
        Boolean allDIF = true;
        List<Warranty_Return_Claim_Item__c> needUpdateList = new List<Warranty_Return_Claim_Item__c>();
        System.debug('*** item: ' + warrantyReturn[0].Warranty_Return_Claim_Item__r);
        for (Warranty_Return_Claim_Item__c item : warrantyReturn[0].Warranty_Return_Claim_Item__r) {
            if (item.Warehouse_Received_Total_Quantity__c != productReceivedQtyMap.get(item.Model__r.ProductCode)) {
                item.Warehouse_Received_Total_Quantity__c = productReceivedQtyMap.get(item.Model__r.ProductCode);
                needUpdateList.add(item);
            }
            totalQuanity += Integer.valueOf(item.Quantity__c);
            totalReceivedQuantity += Math.abs(Integer.valueOf(item.Warehouse_Received_Total_Quantity__c == null ? 0 : item.Warehouse_Received_Total_Quantity__c));
            if (item.DIF_RTV__C == 'RTV') {
                allDIF = false;
            }
        }
        if (!allDIF) {
            if (totalReceivedQuantity == 0) {
                returnGoodsStatus = 'Not Received';
            } else if (totalQuanity > totalReceivedQuantity) {
                returnGoodsStatus = 'Partial Received';
            } else if (totalQuanity <= totalReceivedQuantity) {
                returnGoodsStatus = 'Received';
            }
        }
        if (needUpdateList.size() > 0) {
            update needUpdateList;
        }

        List<String> orderOracleIds = new List<String>();
        for (AggregateResult objOrder : [SELECT Order_OracleID__c FROM Order WHERE Purchase_Order_Text__c = :warrantyRequest GROUP BY Order_OracleID__c]) {
            orderOracleIds.add(String.valueOf(objOrder.get('Order_OracleID__c')));
        }

        Warranty_Return_Claim__c request = new Warranty_Return_Claim__c();
        request.Id = warrantyReturn[0].Id;
        request.Warranty_Return_Order_Number_in_EBS__c = String.join(orderOracleIds, ', ');
        request.Return_Goods_Status__c = returnGoodsStatus;
        update request;
    }

    public static void dealSampleOrderInventory(String sampleOrderName, List<OrderLine> itemList, String orderStatus) {

        Map<String, Integer> sampleShippedMap = new Map<String, Integer>();

        Map<String, Integer> sampleItemMap = new Map<String, Integer>();
        for (OrderLine item : itemList) {
            String lineStatus = item.Line_Status;
            if(lineStatus == 'INVOICED' || lineStatus == 'SHIPPED'|| lineStatus == 'CLOSED' ) {
                String key = item.Product;
                if (!sampleItemMap.containsKey(key)) {
                    sampleItemMap.put(key, 0);
                }
                sampleItemMap.put(key, sampleItemMap.get(key) + Integer.valueOf(item.Order_Quantity));
            }
        }


        List<Sample_Order__c> sampleList = [SELECT Id, Order_Status__c, Task_Owner__c FROM Sample_Order__c WHERE Name = :sampleOrderName];
        String taskOwner = '';
        if (sampleList.size() > 0) {
            taskOwner = sampleList[0].Task_Owner__c;
            sampleList[0].Order_Status__c = orderStatus;
            update sampleList[0];
        }

        List<Product_Inventory_Item__c> inventoryItemList = [SELECT Id, Quantity__c, Sample_Order_Item__c, Status__c, Product_Inventory__r.Product__r.ProductCode
                                                        FROM Product_Inventory_Item__c
                                                        WHERE Sample_Order_Item__r.Sample_Order__r.Name = :sampleOrderName
                                                        AND Product_Inventory__r.Task_Owner__c = :taskOwner];
        Map<String, Integer> bookedQtyMap = new Map<String, Integer>();
        Map<String, Integer> onhandsQtyMap = new Map<String, Integer>();
        Map<String, Integer> conusmedQtyMap = new Map<String, Integer>();
        List<Product_Inventory_Item__c> bookedList = new List<Product_Inventory_Item__c>();
        for (Product_Inventory_Item__c item : inventoryItemList) {
            String key = item.Product_Inventory__r.Product__r.ProductCode;
            if (!bookedQtyMap.containsKey(key)) {
                bookedQtyMap.put(key, 0);
            }
            if (!onhandsQtyMap.containsKey(key)) {
                onhandsQtyMap.put(key, 0);
            }
            if (!conusmedQtyMap.containsKey(key)) {
                conusmedQtyMap.put(key, 0);
            }
            if (item.Status__c == 'Booked') {
                bookedQtyMap.put(key, bookedQtyMap.get(key) + Integer.valueOf(item.Quantity__c));
                bookedList.add(item);
            }
            if (item.Status__c == 'On-Hands') {
                onhandsQtyMap.put(key, onhandsQtyMap.get(key) + Integer.valueOf(item.Quantity__c));
            }
            if (item.Status__c == 'Consumed') {
                conusmedQtyMap.put(key, conusmedQtyMap.get(key) + Integer.valueOf(item.Quantity__c));
            }
        }

        List<Product_Inventory_Item__c> toUpdateList = new List<Product_Inventory_Item__c>();
        Map<String, Integer> waitUpdateQtyMap = new Map<String, Integer>();
        for (String key : sampleItemMap.keySet()) {
            Integer waitUpdateQuantity = sampleItemMap.get(key) - (onhandsQtyMap.containsKey(key) ? onhandsQtyMap.get(key) : 0) - (conusmedQtyMap.containsKey(key) ? conusmedQtyMap.get(key) : 0);
            Integer ind = 0;
            for (Product_Inventory_Item__c item : bookedList) {
                if (item.Product_Inventory__r.Product__r.ProductCode == key && ind < waitUpdateQuantity) {
                    item.Status__c = 'On-Hands';
                    toUpdateList.add(item);
                    ind++;
                }
            }
        }
        if (toUpdateList.size() > 0) {
            update toUpdateList;
        }
    }

    private static Boolean isReturnOnly(List<Reverse_Order_Item__c> items) {
        Boolean returnOnly = true;
        Set<String> returnActions = new Set<String>{
            'I want to reject and return the overage product',
            'I want to return damaged goods and get credit back',
            'I want to return damaged goods and get replacement',
            'I want to return goods and get credit back'
        };

        for(Reverse_Order_Item__c item : items) {
            if(!returnActions.contains(item.Next_Step_Action__c)) {
                returnOnly = false;
                break;
            }
        }
        return returnOnly;
    }

    public static void forCoverage() {
        Integer i = 0;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
    }
}