/**
About
-----
Description: This Class is used for create new order in lightning .

Created for: Chervon classic to lightning
Created: 05 31 2019

Update History
--------------
Created: 05 31 2019 – <EMAIL>
-------------
**/
public WITHOUT Sharing class CCM_NewOrderController {
    @AuraEnabled public static Order thisOrder {get; set;}
    @AuraEnabled public static Account thisAccount {get; set;}
    @AuraEnabled public static Warranty__c thisWarranty {get; set;}
    @AuraEnabled public static Pricebook2 price {get; set;}
    @AuraEnabled public static Case thisCase {get; set;}//当前Case
    @AuraEnabled public static User currentUser {get; set;}//当前用户
    @AuraEnabled public static Set<String> set_proids {get; set;}
    @AuraEnabled public static Map<Id, Storage__c> storageMap {get;set;}
    @AuraEnabled public static Map<Id, PricebookEntry> pricebookEntryMap {get; set;}
    @AuraEnabled public static Map<Id, Product2> productMap {get; set;}
    @AuraEnabled public static List<SelectOption> optionList {get; set;}
    @AuraEnabled public static String storageName {get; set;}
    @AuraEnabled public static Map<Id, Storage__c> stoMap {get; set;}

    @AuraEnabled
    public static String getAddressByCode(String postalCode,String country) {
        return CalloutService.getAddressByPostalCode(postalCode,country);
    }

    @AuraEnabled
    public static String getOrder(String caseId, String warId, Id idOrderRecordType) {
        System.debug(warId);
        System.debug(caseId);
        String uId = UserInfo.getUserId();
        User us = [SELECT Id, UserRole.DeveloperName FROM User WHERE Id =: uId];
        Boolean csManagerFlag = false;
        if(us.UserRole.DeveloperName == 'NA_CS_Manager'){
            csManagerFlag = true;
        }
        Map<String, Object> orderMap = new Map<String, Object>();
        orderMap.clear();
        if(caseId == null && warId == null){
            orderMap.put('Message', 'You can NOT create order WITHOUT warranty!');
        }else{
            Integer checkResult = WebServiceUtil.checkWarrantyCanNewOrder(warId, caseId);
            if (checkResult == 201 && !Test.isRunningTest()) {
                orderMap.put('Message', 'You can NOT create order WITHOUT warranty!');
            } else if (checkResult == 202 && !Test.isRunningTest()) {
                orderMap.put('Message', 'You can NOT create order because customer LOST the receipt!');
            } else if (checkResult == 203 && !Test.isRunningTest()) {
                orderMap.put('Message', 'You can NOT create order WITHOUT receipt received and verified!');
            } else if (checkResult == 204 && !Test.isRunningTest()) {
                orderMap.put('Message', 'Lost receipt customers up to only ONE order!');
            } else if (checkResult == 206 && !Test.isRunningTest()) {
                orderMap.put('Message', 'You can NOT create more order!');
            } else if (checkResult == 208 && !Test.isRunningTest()) {
                orderMap.put('Message', 'You can NOT create order because it is the wrong case type! ');
            } else if (checkResult == 209 && !Test.isRunningTest()) {
                orderMap.put('Message', 'You can NOT create order because it is NOT Valid Warranty! ');
            }else if (checkResult == 210 && !Test.isRunningTest()) {
                orderMap.put('Message', 'You can NOT create order because it is NOT approved claim! ');
            }

            if(String.isBlank(warId)) {
                for(Case c : [SELECT Warranty__c FROM Case WHERE Id = :caseId]) {
                    warId = c.Warranty__c;
                }
            }

            Boolean isEligibleForLackOfStorage = isInLackOfStorageScope(warId, idOrderRecordType);
            if(idOrderRecordType == CCM_Constants.ORDER_RECORD_TYPE_ORDER_FOR_LACK_OF_STORAGE_ORDER_ID) {
                if(!isEligibleForLackOfStorage) {
                    orderMap.put('Message', 'You can NOT create order because it is NOT in the SN scope! ');
                }
            }

            thisAccount = new Account();
            set_proids = new Set<String>();
            storageMap = new Map<Id, Storage__c>();
            stoMap = new Map<Id, Storage__c>();
            currentUser = QueryUtils.getCurrentUser();
            thisCase = new Case();
            thisWarranty = new Warranty__c();
            thisOrder = new Order(Type = 'CNA Sample and Warranty Order');
            if (orderMap.size() < 1) {
                if(idOrderRecordType != null) {
                    if(idOrderRecordType == CCM_Constants.ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID) {
                        orderMap.put('placeOrderScope', 'parts');
                    }
                    else if(idOrderRecordType == CCM_Constants.ORDER_RECORD_TYPE_PLACE_ORDER_ID) {
                        orderMap.put('placeOrderScope', 'fg');
                    }
                    else {
                        orderMap.put('placeOrderScope', 'all');
                    }
                }

                if (String.isNotEmpty(caseId)) {
                    thisCase = CaseService.getCaseById(caseId);
                    thisOrder.Case__c = thisCase.Id;
                    orderMap.put('CaseId', thisCase.Id);
                    orderMap.put('CaseName', thisCase.CaseNumber);
                    orderMap.put('BrandName', thisCase.Brand_Name__c);
                    orderMap.put('CaseType', thisCase.Case_Type__c);
                    orderMap.put('ActualSolution', thisCase.Actual_Solution__c);
                    if( thisCase.Case_Type__c == 'Non-warranty Order'){
                        thisOrder.Type = idOrderRecordType == CCM_Constants.ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID
                            ? CCM_Constants.PURCHASE_ORDER_ORDER_TYPE_CNA_PARTS_SALES_ORDER_USD
                            : 'CNA Sample and Warranty Order';
                    }
                    if(String.isNotBlank(thisCase.Warranty__c)){
                        thisWarranty = WarrantyService.getWarrantySimpleByID(thisCase.Warranty__c);
                        thisOrder.Warranty__c = thisCase.Warranty__c;
                        orderMap.put('WarId', thisCase.Warranty__c);
                        orderMap.put('WarName', thisWarranty.Name);
                    }



                    thisAccount = AccountService.getAccountByID(thisCase.AccountId);
                    thisOrder.AccountId = thisAccount.Id;
                    orderMap.put('AccId', thisAccount.Id);
                    orderMap.put('AccName', thisAccount.Name);
                    orderMap.put('FirstName', thisAccount.FirstName);
                    orderMap.put('LastName', thisAccount.LastName);
                }

                if (String.isNotEmpty(warId)) {
                    thisWarranty = WarrantyService.getWarrantySimpleByID(warId);
                    thisOrder.Warranty__c = thisWarranty.Id;
                    orderMap.put('WarId', thisWarranty.Id);
                    orderMap.put('WarName', thisWarranty.Name);
                    orderMap.put('BrandName', thisWarranty.Brand_Name__c);
                    thisAccount = AccountService.getAccountByID(thisWarranty.AccountCustomer__c);
                    thisOrder.AccountId = thisAccount.Id;
                    orderMap.put('AccId', thisAccount.Id);
                    orderMap.put('AccName', thisAccount.Name);
                    List<Case> caseList = CaseService.getCaseByWarrantyId(warId);
                    if(caseList.size() > 0 && String.isEmpty(caseId)) {
                        thisCase = caseList.get(0);
                        thisOrder.Case__c = thisCase.Id;
                        orderMap.put('CaseId', thisCase.Id);
                        orderMap.put('CaseName', thisCase.CaseNumber);
                        orderMap.put('ActualSolution', thisCase.Actual_Solution__c);
                    }
                }
                orderMap.put('Type', thisorder.Type);
                thisOrder.Status = 'Draft';
                thisOrder.Customer__c = thisCase.ContactId;
                thisOrder.EffectiveDate = (thisOrder.EffectiveDate == null) ? System.today() : thisOrder.EffectiveDate;
                thisOrder.Custome_Phone__c = thisAccount.Phone;
                thisOrder.CustomerEmail__c = thisAccount.PersonEmail;
                orderMap.put('Status', thisOrder.Status);
                orderMap.put('Customer', thisOrder.Customer__c);
                orderMap.put('EffectiveDate', thisOrder.EffectiveDate);

                if(thisCase.RecordType.Name == 'Recall'){
                    thisWarranty = WarrantyService.getWarrantySimpleByIDforCase(thisCase.Warranty__c);
                }

                Map<String, String> stateNameMap = new Map<String, String>{
                            'Alabama' => 'AL',
                            'Alaska' => 'AK',
                            'Arizona' => 'AZ',
                            'Arkansas' => 'AR',
                            'California' => 'CA',
                            'Colorado' => 'CO',
                            'Connecticut' => 'CT',
                            'District of Columbia' => 'DC',
                            'Delaware' => 'DE',
                            'Florida' => 'FL',
                            'Georgia' => 'GA',
                            'Hawaii' => 'HI',
                            'Idaho' => 'ID',
                            'Illinois' => 'Illinois',
                            'Indiana' => 'IN',
                            'Iowa' => 'IA',
                            'Kansas' => 'KS',
                            'Kentucky' => 'KY',
                            'Louisiana' => 'LA',
                            'Maine' => 'ME',
                            'Maryland' => 'MD',
                            'Massachusetts' => 'MA',
                            'Michigan' => 'MI',
                            'Minnesota' => 'MN',
                            'Mississippi' => 'MS',
                            'Missouri' => 'MO',
                            'Montana' => 'MT',
                            'Nebraska' => 'NE',
                            'Nevada' => 'NV',
                            'New Hampshire' => 'NH',
                            'New Jersey' => 'NJ',
                            'New Mexico' => 'NM',
                            'New York' => 'NY',
                            'North Carolina' => 'NC',
                            'North Dakota' => 'ND',
                            'Ohio' => 'OH',
                            'Oklahoma' => 'OK',
                            'Oregon' => 'OR',
                            'Pennsylvania' => 'PA',
                            'Rhode Island' => 'RI',
                            'South Carolina' => 'SC',
                            'South Dakota' => 'SD',
                            'Tennessee' => 'TN',
                            'Texas' => 'TX',
                            'Utah' => 'UT',
                            'Vermont' => 'VT',
                            'Virginia' => 'VA',
                            'Washington' => 'WA',
                            'West Virginia' => 'WV',
                            'Wisconsin' => 'WI',
                            'Wyoming' => 'WY'};

                if(thisCase.Service_Option__c == 'Replacement' && thisCase.Replacement_Last_Name__c != null && thisCase.Case_Type__c == 'Service Claim'){
                    orderMap.put('ZipCode', thisCase.ShippingPostalCode__c);
                    orderMap.put('Street', thisCase.ShippingStreet__c);
                    orderMap.put('City', thisCase.ShippingCity__c);
                    orderMap.put('State', thisCase.ShippingState__c);
                    orderMap.put('BillingState', thisCase.ShippingState__c);
                    orderMap.put('Country', thisCase.ShippingCountry__c);
                    orderMap.put('AccName', thisCase.Replacement_Last_Name__c+' '+thisCase.Replacement_First_Name__c);
                    orderMap.put('Phone', thisCase.ReplacementPhone__c);
                }else if(thisAccount.ShippingAddress != null) {
                    Address accountAddress         = thisAccount.ShippingAddress;
                    thisOrder.Shipping_Zip_Code__c = accountAddress.getPostalCode();
                    thisOrder.Shipping_Address__c  = accountAddress.getStreet();
                    thisOrder.Shipping_City__c     = accountAddress.getCity();
                    thisOrder.Shipping_State__c    = (stateNameMap.containsKey(accountAddress.getState())) ?
                                    stateNameMap.get(accountAddress.getState()) :
                                    accountAddress.getState();
                    thisOrder.Shipping_Country__c  = accountAddress.getCountry();
                    thisOrder.Billing_State__c     = accountAddress.getState();
                    orderMap.put('ZipCode', thisOrder.Shipping_Zip_Code__c);
                    orderMap.put('Street', thisOrder.Shipping_Address__c);
                    orderMap.put('City', thisOrder.Shipping_City__c);
                    orderMap.put('State', thisOrder.Shipping_State__c);
                    orderMap.put('Country', thisOrder.Shipping_Country__c);
                    orderMap.put('BillingState', thisOrder.Billing_State__c);
                    orderMap.put('Phone', thisOrder.Custome_Phone__c);

                }
                orderMap.put('Email', thisOrder.CustomerEmail__c);
                orderMap.put('csManagerFlag', csManagerFlag);
            }
        }
        return JSON.serialize(orderMap);

    }

    @AuraEnabled
    public static String getProductBook(String brand, String priceBookId, String caseId) {
        String concatMsg = '';
        try {
            if(String.isBlank(priceBookId)){
                StringException se = new StringException();
                Default_PriceBook__c defaultPB;
                Default_PriceBook__c warrantyPB;
                thisCase = CaseService.getCaseById(caseId);
                if (thisCase.Case_Type__c == 'Warranty Order' || thisCase.Case_Type__c == 'Service Claim' || thisCase.RecordType.Name == 'Recall') {
                    /**
                    2021/1/12
                    Product warranty order:
                    先取Warranty and Sample price list, 找不到取MSRP
                    **/
                    set_proids.clear();
                    pricebookEntryMap = new Map<Id, PricebookEntry>();
                    List<Pricebook2> pricelist = new List<Pricebook2>();
                    if(brand != 'Hammerhead') {
                        if(brand == 'EGO'){
                            defaultPB = Default_PriceBook__c.getAll().get('CNA-EGO-MSRP');
                        }else if(brand == 'FLEX'){
                            defaultPB = Default_PriceBook__c.getAll().get('CNA-FLEX-MSRP');
                        }else if (brand == 'Skil' || brand == 'SkilSaw'){
                            defaultPB = Default_PriceBook__c.getAll().get('CNA-SKIL-MSRP');
                        }else{
                            defaultPB = Default_PriceBook__c.getAll().get('defaultBook');
                        }
                        if(defaultPB != null && defaultPB.Price_Book_Id__c != null && defaultPB.DevelopName__c != null) {
                            pricelist = [SELECT Id FROM Pricebook2 WHERE Id =: defaultPB.Price_Book_Id__c Limit 1];
                            if (pricelist.isEmpty()) {
                                pricelist = [SELECT Id FROM Pricebook2 WHERE Name =: defaultPB.DevelopName__c Limit 1];
                            }
                            if(pricelist.size() > 0){
                                price = pricelist.get(0);
                            }
                        }
                        thisOrder.Pricebook2Id = price.Id;
                        for(PricebookEntry pbe:[SELECT Id,Product2Id
                                                FROM PricebookEntry WHERE Id != NULL
                                                AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                                AND Pricebook2Id =: thisOrder.Pricebook2Id]) {
                            set_proids.add(pbe.Product2Id);
                        }
                        for(PricebookEntry pbe : [SELECT Id, Product2Id, Pricebook2Id, UnitPrice
                                                    FROM PricebookEntry WHERE Id != NULL
                                                    AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                                    AND Product2.Brand_Name__c =: brand
                                                    AND Pricebook2Id=:thisOrder.Pricebook2Id
                                                    AND IsActive = true]) {
                            pricebookEntryMap.put(pbe.Product2Id, pbe);
                        }
                    }
                    warrantyPB = Default_PriceBook__c.getAll().get('defaultBook');
                    if(warrantyPB != null && warrantyPB.Price_Book_Id__c != null && warrantyPB.DevelopName__c != null) {
                        pricelist = [SELECT Id FROM Pricebook2 WHERE Id =: warrantyPB.Price_Book_Id__c Limit 1];
                        if (pricelist.isEmpty()) {
                            pricelist = [SELECT Id FROM Pricebook2 WHERE Name =: warrantyPB.DevelopName__c Limit 1];
                        }
                        if(pricelist.size() > 0){
                            price = pricelist.get(0);
                        }
                    }
                    thisOrder.Pricebook2Id = price.Id;
                    for(PricebookEntry pbe:[SELECT Id,Product2Id
                                            FROM PricebookEntry
                                            WHERE Id != NULL
                                            AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                            AND Pricebook2Id =: thisOrder.Pricebook2Id]) {
                        set_proids.add(pbe.Product2Id);
                    }
                    for(PricebookEntry pbe : [SELECT Id, Product2Id, Pricebook2Id, UnitPrice
                                                FROM PricebookEntry WHERE Id != NULL
                                                AND Product2.Brand_Name__c =: brand
                                                AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                                AND Pricebook2Id=:thisOrder.Pricebook2Id
                                                AND IsActive = true]) {
                        pricebookEntryMap.put(pbe.Product2Id, pbe);
                    }
                } else {
                    if((brand == 'EGO' || brand == 'Hammerhead' || brand == 'FLEX') && thisCase.RecordType.Name != 'Recall') {
                        defaultPB = Default_PriceBook__c.getAll().get('defaultBook');
                    // } else if (thisCase.RecordType.Name == 'Recall') {
                    //     if(brand == 'EGO'){
                    //         defaultPB = Default_PriceBook__c.getAll().get('CNA-EGO-MSRP');
                    //     }else if(brand == 'FLEX'){
                    //         defaultPB = Default_PriceBook__c.getAll().get('CNA-FLEX-MSRP');
                    //     }else if (brand == 'Skil' || brand == 'SkilSaw'){
                    //         defaultPB = Default_PriceBook__c.getAll().get('CNA-SKIL-MSRP');
                    //     }else{
                    //         defaultPB = Default_PriceBook__c.getAll().get('defaultBook');
                    //     }
                    } else {
                        defaultPB = Default_PriceBook__c.getAll().get('SkilDefaultBook');
                    }
                    System.debug('no warranty order:default pricebook:' + defaultPB);
                    List<Pricebook2> pricelist = new List<Pricebook2>();
                    Boolean haveNoPrice = TRUE;
                    if(defaultPB != null && defaultPB.Price_Book_Id__c != null) {
                        pricelist = [SELECT Id FROM Pricebook2 WHERE Id =: defaultPB.Price_Book_Id__c Limit 1];
                        if(pricelist.size() > 0){
                            price = pricelist.get(0);
                            haveNoPrice = FALSE;
                        }
                    }

                    if(defaultPB != null && haveNoPrice && defaultPB.DevelopName__c != null){
                        pricelist = [SELECT Id FROM Pricebook2 WHERE Name =: defaultPB.DevelopName__c Limit 1];
                        if(pricelist.size() > 0){
                            price = pricelist.get(0);
                            haveNoPrice = FALSE;
                            defaultPB.Price_Book_Id__c = price.Id;
                        }
                    }
                    if(haveNoPrice){
                        String defaultPBName;
                        if(defaultPB == null || String.isBlank(defaultPB.DevelopName__c)) {
                            defaultPBName = 'Default Price Book';
                        } else {
                            defaultPBName = defaultPB.DevelopName__c;
                        }
                        se.setMessage('Initialize Failed: Can not get the pricebook \'' +
                                        defaultPBName +
                                        '\'. Please contact the system administrator.');
                        throw se;
                    }
                    set_proids.clear();
                    System.debug(LoggingLevel.INFO, '*** price.id: ' + price.id);
                    thisOrder.Pricebook2Id = price.Id;
                    for(PricebookEntry pbe:[SELECT Id,Product2Id
                                            FROM PricebookEntry WHERE Id != NULL
                                            AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                            AND Pricebook2Id =: thisOrder.Pricebook2Id]) {
                        set_proids.add(pbe.Product2Id);
                    }
                    System.debug(LoggingLevel.INFO, '*** set_proids: ' + set_proids);
                    pricebookEntryMap = new Map<Id, PricebookEntry>();
                    for(PricebookEntry pbe : [SELECT Id, Product2Id, Pricebook2Id, UnitPrice
                                                FROM PricebookEntry WHERE Id != NULL
                                                AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                                AND Product2.Brand_Name__c =: brand
                                                AND Pricebook2Id=:thisOrder.Pricebook2Id
                                                AND IsActive = true]) {
                        pricebookEntryMap.put(pbe.Product2Id, pbe);
                    }
                }
            }else{
                thisOrder.Pricebook2Id = priceBookId;
                for(PricebookEntry pbe:[SELECT Id,Product2Id
                                        FROM PricebookEntry WHERE Id != NULL
                                        AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                        AND Pricebook2Id =: thisOrder.Pricebook2Id]) {
                    set_proids.add(pbe.Product2Id);
                }
                System.debug(LoggingLevel.INFO, '*** set_proids: ' + set_proids);

                pricebookEntryMap = new Map<Id, PricebookEntry>();

                for(PricebookEntry pbe : [SELECT Id, Product2Id, Pricebook2Id, UnitPrice
                                            FROM PricebookEntry WHERE Id != NULL
                                            AND Product2.Brand_Name__c =: brand
                                            AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                            AND Pricebook2Id=:thisOrder.Pricebook2Id
                                            AND IsActive = true]) {
                    pricebookEntryMap.put(pbe.Product2Id, pbe);
                }

            }

        }catch(Exception e) {
            concatMsg = e.getMessage();
        }
        return concatMsg;
    }

    @AuraEnabled
    public static String checkHaveError(String type) {
        String concatMsg = '';
        try {
            StringException se = new StringException();
            if (thisCase.RecordType.DeveloperName == 'Recall_Case') {
                List<Order> existOrderList = [SELECT Id, Type, Case__c FROM Order WHERE Case__c =: thisCase.Id AND Type =: type];
                if (existOrderList != null && existOrderList.size() > 0) {
                    se.setMessage(type + ' orders CANNOT be created repeatedly');
                    throw se;
                }
            } else {
                if (type == 'Recall Order' || type == 'Recall Package') {
                    se.setMessage('This case CANNOT choose \'Recall Package\' or \'Recall Order\' type');
                    throw se;
                }
            }
        } catch(Exception e) {
            concatMsg = e.getMessage();
        }
        return concatMsg;
    }

    @AuraEnabled
    public static String Next(String caseId, String warId, String brand, String type, Id idOrderRecordType) {
        System.debug(LoggingLevel.INFO, '*** next----: ' + caseId +' + ' + warId + '+' + brand + '+' +type);
        String nextError = '';
        getOrder(caseId, warId, idOrderRecordType);

        if (String.isNotBlank(checkHaveError(type))) {
            nextError = checkHaveError(type);
        } else {
            getProductBook(brand,'',caseId);
            getProducts(brand, type, idOrderRecordType);
            nextError = getProductBook(brand,'',caseId);
        }
        //System.debug(LoggingLevel.INFO, '*** nextError: ' + nextError);
        return nextError;
    }

    @AuraEnabled
    public static void getProducts(String brand, String type, Id idOrderRecordType) {
        Boolean boolIsPartsOrder = [SELECT Name FROM RecordType WHERE SobjectType = 'Order' AND Id = :idOrderRecordType]?.Name?.containsIgnoreCase('Parts') == true;
        Integer intMinimumInventory = boolIsPartsOrder == true ? -1 : 0;
        optionList = new List<SelectOption>();
        Set<Id> productIdSet = new Set<Id>();
        Set<String> productCodeSet = new Set<String>();
        for (Warranty_Item__c warrantyItem : thisWarranty.Warranty_Items__r) {
            if (warrantyItem.Product__r.Brand_Name__c != brand) {
                continue;
            }
            if (type == null ||
                    (type == 'CNA Sample and Warranty Order') ||
                    (type == 'Recall Order' && thisCase.Warranty_Item__c == warrantyItem.Id)) {
                productIdSet.add(warrantyItem.Product__c);

                //add by yujie for MODEL T enhancement, which already in PRODUCTION
                productCodeSet.add(warrantyItem.Product__r.ProductCode + 'T');

                productCodeSet.add(warrantyItem.Product__r.ProductCode + '-FC');
                productCodeSet.add(warrantyItem.Product__r.ProductCode + '-RT');
                productCodeSet.add('CS-' + warrantyItem.Product__r.ProductCode);
                productCodeSet.add('CS1-' + warrantyItem.Product__r.ProductCode + '-FC');
                productCodeSet.add('CS2-' + warrantyItem.Product__r.ProductCode + '-FC');
            }
        }

        if(boolIsPartsOrder){
            for(Storage__c st : [SELECT Id, Product__c, Product__r.Name,
                                        Product__r.ProductCode, Product__r.Brand_Name__c,
                                        Product__r.Item_Number__c, Sub_storage__c
                                    FROM Storage__c WHERE Product__r.IsActive = true
                                    AND Available_Inventory__c > :intMinimumInventory
                                    AND Product__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                    AND Product__r.RecordType.DeveloperName = 'Product'
                                    AND Product__r.Source__c = 'EBS'
                                    ORDER BY Product__r.ProductModel__c ASC]) {
                if(st.Product__r.Brand_Name__c != brand){
                    continue;
                }
                                        system.debug(st);
                                        system.debug(st.Product__r.ProductCode);
                storageMap.put(st.Id, st);
                optionList.add(new SelectOption(st.Id, st.Product__r.Name + '(' +
                                st.Product__r.ProductCode + ',' + st.Product__r.Item_Number__c + ') ' +
                                st.Sub_storage__c));
            }
        }else{
            for(Storage__c st : [SELECT Id, Product__c, Product__r.Name,
                                        Product__r.ProductCode, Product__r.Brand_Name__c,
                                        Product__r.Item_Number__c, Sub_storage__c
                                    FROM Storage__c WHERE Product__r.IsActive = true
                                    AND Available_Inventory__c > :intMinimumInventory
                                    AND Product__c IN:set_proids
                                    AND Product__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                    AND Product__r.RecordType.DeveloperName = 'Product'
                                    AND Product__r.Source__c = 'EBS'
                                    ORDER BY Product__r.ProductModel__c ASC]) {
                if(st.Product__r.Brand_Name__c != brand){
                    continue;
                }
                                        system.debug(st);
                                        system.debug(st.Product__r.ProductCode);
                storageMap.put(st.Id, st);
                optionList.add(new SelectOption(st.Id, st.Product__r.Name + '(' +
                                st.Product__r.ProductCode + ',' + st.Product__r.Item_Number__c + ') ' +
                                st.Sub_storage__c));
            }
        }
    }

    public class PartsWapper{
        public String Id {get;set;}//产品id
        public String Name {get;set;}//产品名称
        public PartsWapper(String idValue,String nameValue){
            this.id   = idValue;
            this.name = nameValue;
        }
    }

    @AuraEnabled
    public static String getParts(String caseId, String warId, String brand, String type, String productId, String filterStr, Id orderRecordTypeId) {
        Next(caseId, warId, brand, type, orderRecordTypeId);
        List<PartsWapper> resultMap = new List<PartsWapper>();
        Map<String, String> errorMap = new Map<String, String>();
        List<SelectOption> selectList = new List<SelectOption>();
        String result = '';
        Boolean isEligibleForLackOfStorage = isInLackOfStorageScope(warId, orderRecordTypeId);
        List<Case> caseList = [SELECT Id,Case_Type__c FROM Case WHERE Id =: caseId];

        if(String.isNotBlank(productId) && caseList.size()>0){
            //获取所选产品的Parts
            System.debug(LoggingLevel.INFO, '*** storageMap: ' + storageMap);
            if(String.isNotBlank(productId)) {
                Storage__c stor = storageMap.get(productId);//所选产品的库存
                System.debug(LoggingLevel.INFO, '*** stor: ' + stor);
                System.debug(LoggingLevel.INFO, '*** productId: ' + productId);
                List<Product2> productIdList = [SELECT ProductCode FROM Product2 WHERE Id =: stor.Product__c LIMIT 1];
                if((productIdList.size() > 0 && productIdList[0].ProductCode == null) || stor == null) {
                    errorMap.put('message', 'The selected product have NOT Product Code. ');
                    result = JSON.serialize(errorMap);
                } else {
                    String productCode = productIdList[0].ProductCode;
                    if(productCode.startsWithIgnoreCase('CS-')) {
                        productCode = productCode.replaceFirst('CS-', '').replaceFirst('cs-', '');
                    }
                    stor.Product__r.ProductCode = stor.Product__r.ProductCode.replaceAll('-FC','');
                    stor.Product__r.ProductCode = stor.Product__r.ProductCode.replaceAll('-RT','');
                    stor.Product__r.ProductCode = stor.Product__r.ProductCode.replaceAll('T-FC','');
                    stor.Product__r.ProductCode = stor.Product__r.ProductCode.replaceAll('CS-','');
                    stor.Product__r.ProductCode = stor.Product__r.ProductCode.replaceAll('CS1-','');
                    stor.Product__r.ProductCode = stor.Product__r.ProductCode.replaceAll('CS2-','');
                    Map<String, Decimal> parsProductCodeMap = new Map<String, Decimal>();
                    //根据所选产品的产品Code，获取对应的Parts Code与数量
                    if(String.isBlank(filterStr)){
                        for(Kit_Item__c bom : [SELECT Id, Parts__r.ProductCode, Quantity__c
                                FROM Kit_Item__c
                                WHERE Id != NULL
                                AND Product__r.ProductCode =: productCode
                                AND Product__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                AND Parts__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                AND Parts__r.Source__c = 'PIM' LIMIT 100]) {
                            parsProductCodeMap.put(bom.Parts__r.ProductCode, bom.Quantity__c);
                        }
                    }else{
                        filterStr = '%'+filterStr+'%';
                        for(Kit_Item__c bom : [SELECT Id, Parts__r.ProductCode, Quantity__c
                                FROM Kit_Item__c
                                WHERE Id != NULL
                                AND Product__r.ProductCode =: productCode
                                AND Parts__r.Source__c = 'PIM'
                                AND Product__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                AND Parts__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                AND ( Parts__r.Name LIKE: filterStr
                                    OR Parts__r.productCode LIKE: filterStr
                                    ) LIMIT 100]) {
                            parsProductCodeMap.put(bom.Parts__r.ProductCode, bom.Quantity__c);
                        }
                    }

                    if(caseList[0].Case_Type__c != 'Non-warranty Order'){
                        // 根据Parts Code获取库存
                        Set<String> productCodeSet = parsProductCodeMap.keySet();
                        Boolean isHistoryProduct = CCM_Constants.blHistoryProduct;
                        String query = 'SELECT Id, Product__c, Product__r.Name, Product__r.ProductCode, Product__r.Item_Number__c, Sub_storage__c FROM Storage__c';
                        query += ' WHERE Product__r.ProductCode IN: productCodeSet';
                        query += ' AND Product__r.Is_History_Product__c = :isHistoryProduct';
                        query += ' AND Product__r.IsActive = true';
                        query += ' AND Product__r.RecordType.Name = \'Parts\'';
                        query += ' AND Sub_storage__c LIKE \'%CNA%\'';
                        if(!isEligibleForLackOfStorage) {
                            query += ' AND Available_Inventory__c > 0';
                        }
                        query += ' LIMIT 100';
                        for(Storage__c st : (List<Storage__c>)Database.query(query)) {
                            storageMap.put(st.Id, st);
                            system.debug(st);
                            Decimal partsNum = parsProductCodeMap.get(st.Product__r.ProductCode);
                            partsNum         = partsNum==null?1.00:partsNum;
                            selectList.add(new SelectOption(st.Id, st.Product__r.Name + '(' +
                                        st.Product__r.ProductCode + ')*' + partsNum));
                        }
                        for(SelectOption so : selectList) {
                            resultMap.add(new PartsWapper(so.getValue(),so.getLabel()));
                        }
                        result = JSON.serialize(resultMap);
                    }else{
                        //根据Parts Code获取库存
                        for(Storage__c st : [SELECT id, Product__c, Product__r.Name, Product__r.ProductCode,
                                                Product__r.Item_Number__c, Sub_storage__c
                                                FROM Storage__c
                                                WHERE Id != NULL
                                                AND Product__r.ProductCode IN: parsProductCodeMap.keySet()
                                                AND Product__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                                AND Product__r.IsActive = true
                                                AND Product__r.RecordType.Name = 'Parts'
                                                AND Sub_storage__c
                                                LIKE '%CNA%'
                                                LIMIT 100]) {
                            storageMap.put(st.Id, st);
                            system.debug(st);
                            Decimal partsNum = parsProductCodeMap.get(st.Product__r.ProductCode);
                            partsNum         = partsNum==null?1.00:partsNum;
                            selectList.add(new SelectOption(st.Id, st.Product__r.Name + '(' +
                                        st.Product__r.ProductCode + ')*' + partsNum));
                        }
                        for(SelectOption so : selectList) {
                            resultMap.add(new PartsWapper(so.getValue(),so.getLabel()));
                        }
                        result = JSON.serialize(resultMap);
                    }
                }
            }
        } else {
            Map<String, Decimal> parsProductCodeMap = new Map<String, Decimal>();
            //根据所选产品的产品Code，获取对应的Parts Code与数量
            if(String.isBlank(filterStr)){
                for(Kit_Item__c bom : [SELECT Id, Parts__r.ProductCode, Quantity__c
                        FROM Kit_Item__c WHERE Id != NULL
                        AND Parts__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                        AND Parts__r.Source__c = 'PIM' LIMIT 100]) {
                    parsProductCodeMap.put(bom.Parts__r.ProductCode, bom.Quantity__c);
                }
            }else{
                filterStr = '%'+filterStr+'%';
                for(Kit_Item__c bom : [SELECT Id, Parts__r.ProductCode, Quantity__c
                        FROM Kit_Item__c WHERE Id != NULL
                        AND Parts__r.Source__c = 'PIM'
                        AND Parts__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                        AND ( Parts__r.Name LIKE: filterStr
                            OR Parts__r.productCode LIKE: filterStr
                            ) LIMIT 100]) {
                    parsProductCodeMap.put(bom.Parts__r.ProductCode, bom.Quantity__c);
                }
            }

            //根据Parts Code获取库存
            Set<String> productCodeSet = parsProductCodeMap.keySet();
            Boolean isHistoryProduct = CCM_Constants.blHistoryProduct;
            String query = 'SELECT Id, Product__c, Product__r.Name, Product__r.ProductCode, Product__r.Item_Number__c, Sub_storage__c FROM Storage__c';
            query += ' WHERE Product__r.ProductCode IN: productCodeSet';
            query += ' AND Product__r.Is_History_Product__c = :isHistoryProduct';
            query += ' AND Product__r.IsActive = true';
            query += ' AND Product__r.RecordType.Name = \'Parts\'';
            query += ' AND Sub_storage__c LIKE \'%CNA%\'';
            if(!isEligibleForLackOfStorage) {
                query += ' AND Available_Inventory__c > 0';
            }
            query += ' LIMIT 100';
            for(Storage__c st : (List<Storage__c>)Database.query(query)) {
                storageMap.put(st.Id, st);
                system.debug(st);
                Decimal partsNum = parsProductCodeMap.get(st.Product__r.ProductCode);
                partsNum         = partsNum==null?1.00:partsNum;
                selectList.add(new SelectOption(st.Id, st.Product__r.Name + '(' +
                            st.Product__r.ProductCode + ')*' + partsNum));
            }
            //System.debug(LoggingLevel.INFO, '*** selectList.parts: ' + selectList);
            for(SelectOption so : selectList) {
                resultMap.add(new PartsWapper(so.getValue(),so.getLabel()));
            }
            result = JSON.serialize(resultMap);
        }
        return result;
    }

    private static Boolean isInLackOfStorageScope(String warrantyId, Id orderRecordTypeId) {
        Boolean isLackOfStorageOrder = orderRecordTypeId == CCM_Constants.ORDER_RECORD_TYPE_ORDER_FOR_LACK_OF_STORAGE_ORDER_ID ? true : false;
        Set<String> warrantySerialNumbers = new Set<String>();
        Set<String> productCodeSet = new Set<String>();
        for(Warranty_Item__c item : [SELECT Product__r.ProductCode, Serial_Number__c, Warranty__c, Warranty__r.Master_Product__r.ProductCode FROM Warranty_Item__c WHERE Warranty__c = :warrantyId]) {
            if(String.isNotBlank(item.Serial_Number__c)) {
                warrantySerialNumbers.add(item.Serial_Number__c.toUpperCase());
                productCodeSet.add(item.Product__r.ProductCode);
                productCodeSet.add(item.Warranty__r.Master_Product__r.ProductCode);
            }
        }

        Boolean isInScope = false;
        for(Lack_Of_Storage_Serial_Number__c item : [SELECT Lack_Of_Storage_Product__r.Product_Code__c, Serial_Number_Scope__c FROM Lack_Of_Storage_Serial_Number__c WHERE Lack_Of_Storage_Product__r.Product_Code__c IN :productCodeSet]) {
            List<String> serialNumberScopes = item.Serial_Number_Scope__c.split(',');
            for(String serialNumberScope : serialNumberScopes) {
                if(warrantySerialNumbers.contains(serialNumberScope)) {
                    isInScope = true;
                    break;
                }
            }
        }
        return isLackOfStorageOrder && isInScope;
    }

    @AuraEnabled
    public static String retrievePrice(String caseId, String warId, String brand, String type, String productStorageId, String partStorageId, Id orderRecordTypeId) {
        Next(caseId, warId, brand, type, orderRecordTypeId);
        String selectedProductId = '';
        String selectedProductCode = '';
        String selectedPartId = '';
        system.debug(partStorageId);
        Map<String, Object> resultMap = New Map<String, Object>();
        if(String.isNotBlank(productStorageId)) {
            system.debug(storageMap);
            system.debug(productStorageId);

            selectedProductId = storageMap.get(productStorageId).Product__c;
            selectedProductCode = storageMap.get(productStorageId).Product__r.ProductCode;
        }


        //判断选择的产品是否可以下订单
        String concatMsg = verifySelectProCanPlaceOrder(type, selectedProductId, selectedProductCode);
        if (String.isNotEmpty(concatMsg)) {
            resultMap.put('message', concatMsg);
        } else {
            if(String.isNotBlank(partStorageId)) {
                selectedPartId = [SELECT id, Product__c
                                        FROM Storage__c
                                        WHERE Id =: partStorageId].Product__c;
            }
            System.debug(LoggingLevel.INFO, '*** selectedProductId: ' + selectedProductId);
            System.debug(LoggingLevel.INFO, '*** selectedPartId: ' + selectedPartId);

            //产品单价：选了Parts取Parts的单价，否则为产品的单价
            Decimal priceValue = 0.00;
            thisCase = CaseService.getCaseById(caseId);
            String priceBookId = '';
            if(String.isNotBlank(selectedPartId)){
                //warranty&service claim

                if(thisCase.Case_Type__c == 'Service Claim'){
                    priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Name = 'CNA-Landed Cost for Parts' LIMIT 1].Price_Book__c;
                } else {
                    /**
                    2021/1/12
                    Parts warranty order & non-warranty order: 不分品牌和订单类型，全部都取下面这个价格册
                    CNA-MSRP for Parts
                    **/
                    priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Name = 'CNA-MSRP for Parts' LIMIT 1].Price_Book__c;
                }
                // else if(thisCase.Case_Type__c == 'Non-warranty Order'){
                //     if (brand == 'Skil' || brand == 'FLEX'){
                //         priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Name = 'CNA-MSRP for FLEX&SKIL Parts' LIMIT 1].Price_Book__c;
                //     } else{
                //         priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Name = 'CNA-MSRP for Parts' LIMIT 1].Price_Book__c;
                //     }
                // }

                //non-warranty
                //MSRP
                List<PricebookEntry> prodEntryList = Util.getPriceBookEntryByProdId(selectedPartId,priceBookId);
                if(prodEntryList.size() > 0){
                    priceValue = prodEntryList[0].UnitPrice;
                }
            }else if(String.isNotBlank(selectedProductId) && pricebookEntryMap.containsKey(selectedProductId)){
                // priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Name = 'CNA-Landed Cost for FG' LIMIT 1].Price_Book__c;
                // List<PricebookEntry> prodEntryList = Util.getPriceBookEntryByProdId(selectedPartId,priceBookId);
                // if(prodEntryList.size() > 0){
                //     priceValue = prodEntryList[0].UnitPrice;
                // }
                priceValue = pricebookEntryMap.get(selectedProductId).UnitPrice;
                price = new Pricebook2(Id=pricebookEntryMap.get(selectedProductId).Pricebook2Id);
            } else {
                priceValue = 0;
            }

            System.debug(LoggingLevel.INFO, '*** priceValue: ' + priceValue);
            resultMap.put('price', priceValue);
        }
        return JSON.serialize(resultMap);
    }

    public static String verifySelectProCanPlaceOrder(String type, String selectedProductId, String selectedProductCode) {
        String concatMsg = '';
        try {
            StringException se = new StringException();
            if(type != 'Recall Order' && type != 'Recall Package') {
                Boolean isCanPlaceOrder = true;
                String warrantyItemStatus = '';
                for(Warranty_Item__c warrantyItem : thisWarranty.Warranty_Items__r) {
                    String productCSCode = 'CS-' + warrantyItem.Product__r.ProductCode;
                    if(selectedProductId != '' && (warrantyItem.Product__c == selectedProductId || selectedProductCode == productCSCode)) {
                        if(warrantyItem.ActualIndicator__c == 'Vailid Warranty') {
                            continue;
                        }
                        if(warrantyItem.ActualIndicator__c == 'Lost Receipt' && thisWarranty.Order_Times__c == 0) {
                            continue;
                        }
                        isCanPlaceOrder = false;
                        if(warrantyItem.ActualIndicator__c != null) {
                            warrantyItemStatus = warrantyItem.ActualIndicator__c;
                        }
                    }
                }
                // if(!isCanPlaceOrder) {
                //     se.setMessage('Product: The \"' + warrantyItemStatus + '\" product cannot place an order!');
                //     throw se;
                // }
            }
        } catch(Exception e) {
            concatMsg = e.getMessage();
        }
        return concatMsg;
    }

    @AuraEnabled
    public static String Next2(String caseId, String warId, String brand, String type, String result, Id orderRecordTypeId) {
        String concatMsg = '';
        try {
            Next(caseId, warId, brand, type, orderRecordTypeId);
            StringException se = new StringException();
            List<ItemList> lineItems = (List<ItemList>) JSON.deserialize(result, List<ItemList>.class);
            Boolean isSameKind = lineItems.get(0).isParts;
            for(ItemList liw : lineItems) {
                thisOrder.Is_Parts_Order__c = liw.isParts;
                if (isSameKind != liw.isParts) {
                    se.setMessage('One order can only contain products or parts!');
                    throw se;
                }
            }
        } catch(Exception e) {
            concatMsg = e.getMessage();
        }
        return concatMsg;
    }

    public class ItemList {
        public ProductLine ProductLine {get;set;}
        public PartLine PartLine {get;set;}
        public Decimal price {get; set;}
        public Decimal quantity {get; set;}
        public String productType {get; set;}
        public Boolean warrantyItem {get; set;}
        public Date scheduleShippingDate {get; set;}
        public Boolean isParts {get; set;}
        public String warehouse {get; set;}
        public ItemList() {
            this.ProductLine = new ProductLine();
            this.PartLine = new PartLine();
        }
    }

    public class ProductLine {
        public String Id {get;set;}
        public String Name {get;set;}
        public String ProductCode {get;set;}
    }

    public class PartLine {
        public String Id {get;set;}
        public String Name {get;set;}

        public PartLine() {
            this.Id = '';
            this.Name = '';
        }
    }
    @AuraEnabled
    public static String Serify(String caseId, String warId, String brand, String type, String zipCode, String shippingmethod, String result, String shippingFree, Id orderRecordTypeId) {
        storageName = '';
        Next(caseId, warId, brand, type, orderRecordTypeId);
        String resultMsg = '';
        Map<String, Object> resultMap = new Map<String, Object>();
        String productIdNow = '';
        String partIdNow = '';
        String productNameNow = '';
        String partNameNow = '';
        String orgCode = 'CNA';

        List<ItemList> lineItems = (List<ItemList>) JSON.deserialize(result, List<ItemList>.class);

        Set<Id> itemIdSet = new Set<Id>();
        for (ItemList il : lineItems) {
            if (String.isNotBlank(il.ProductLine.Id)) {
                itemIdSet.add(il.ProductLine.Id);
            }
            if (String.isNotBlank(il.PartLine.Id)) {
                itemIdSet.add(il.PartLine.Id);
            }
        }
        //System.debug(LoggingLevel.INFO, '*** itemIdSet: ' + itemIdSet);
        for(Storage__c st : [SELECT id, Product__c, Product__r.Name, Product__r.ProductCode,
                                Product__r.Item_Number__c, Sub_storage__c
                            FROM Storage__c
                            WHERE Id IN: itemIdSet]) {
            stoMap.put(st.Id, st);
        }
        System.debug(LoggingLevel.INFO, '*** stomap.size(): ' + stomap.size());

        //获取订单产品Id
        Set<Id> proIdSet = new Set<Id>();
        for(ItemList liw : lineItems)
        {
            if(stoMap.size() > 0){
                //System.debug(LoggingLevel.INFO, '*** liw.PartLine.Id: ' + liw.PartLine.Id);
                System.debug(LoggingLevel.INFO, '*** liw.ProductLine.Id: ' + liw.ProductLine.Id);
                if (String.isNotEmpty(liw.PartLine.Id)) {
                    if(String.isNotBlank(stoMap.get(liw.PartLine.Id).Product__c)) {
                        proIdSet.add(stoMap.get(liw.PartLine.Id).Product__c);
                    }
                } else if (String.isNotEmpty(liw.ProductLine.Id)) {
                    if (String.isNotBlank(stoMap.get(liw.ProductLine.Id).Product__c)) {
                        proIdSet.add(stoMap.get(liw.ProductLine.Id).Product__c);

                    }
                }
            }
            if(String.isNotBlank(liw.warehouse) && liw.warehouse.contains('CA')){
                orgCode = 'CCA';
            }
        }
        //System.debug(LoggingLevel.INFO, '*** proIdSet: ' + proIdSet);
        if(proIdSet.isEmpty()) {
            resultMap.put('message', 'At least one product or part needs to be selected!');
        }  else {
            //计算运费
            system.debug(shippingFree);
            thisOrder.Total_Shipping_Charges__c = 0;
            List<System_Configuration__c> scList = [SELECT Max_Cost__c,Minimum_Cost__c,Freight_Charge__c
                                                        FROM System_Configuration__c
                                                        WHERE Freight_Charge__c >= 0
                                                        AND Minimum_Cost__c >= 0
                                                        AND Max_Cost__c >= 0
                                                        AND Org_Code__c = : orgCode];
            for(System_Configuration__c sc : scList){
                if(Decimal.valueOf(shippingFree) > sc.Minimum_Cost__c && Decimal.valueOf(shippingFree) < sc.Max_Cost__c){
                    thisOrder.Total_Shipping_Charges__c = sc.Freight_Charge__c;
                }
            }

            resultMap.put('price', thisOrder.Total_Shipping_Charges__c);
        }
        CCM_TaxService.TaxBundleWrapper objTBW;
        for (Case objC : [
            SELECT
                Account.LastName,
                Account.FirstName,
                Account.ShippingCity,
                Account.ShippingCountry,
                Account.ShippingState,
                Account.ShippingStreet,
                Account.ShippingPostalCode
            FROM Case
            WHERE Id = :caseId
        ]) {
            resultMap.put('BillTo', objC);
            resultMap.put('FirstName', objC.Account.FirstName);
            resultMap.put('LastName', objC.Account.LastName);
            objTBW = CCM_TaxService.getTaxBundle(objC.Account.ShippingState);
            resultMap.put('Tax', objTBW.decTax);
            resultMap.put('Include Freight', objTBW.boolIncludeFreight);
        }
        return JSON.serialize(resultMap);
    }

    @AuraEnabled
    public static String saveOrder(String orderId, String caseId, String warId, String brand, String type, String zipCode, String shippingmethod, String result, Id orderRecordTypeId) {
        String concatMsg = '';
        System.Savepoint point = Database.setSavepoint();
        Serify(caseId, warId, brand, type, zipCode, shippingmethod, result, '0', orderRecordTypeId);

        Boolean isPartsOrder = false;
        try{
            Order ord = [SELECT Id, canWorkFlow__c, Type,Is_Parts_Order__c,Status,RecordTypeId,Pricebook2Id FROM Order WHERE Id =: orderId LIMIT 1];
            Case ca = [SELECT Id, Recall_Status__c,Case_Type__c FROM Case WHERE Id = :caseId LIMIT 1];
            StringException se = new StringException();

            List<ItemList> lineItems = (List<ItemList>) JSON.deserialize(result, List<ItemList>.class);
            Set<String> casePRISet = new Set<String>{'Leaking phase change material','Short Running Time','No LED','Red & Green Alternative'};
            Set<String> productCodeSet = new Set<String>();
            String priceBookId = null;
            Boolean hasReceipt = false;
            Boolean isContainsPRI = false;
            Boolean isValid = false;
            Boolean isSamemodel = false;
            thisCase = CaseService.getCaseById(caseId);
            List<Product2> productList = [SELECT Id,Name,ProductCode, Source__c,IsActive,New_Product__c,New_Product__r.ProductCode FROM Product2 WHERE ProductCode =:thisCase.Product_Code__c And Is_History_Product__c = false AND Source__c ='EBS' AND IsActive = true];
            if(thisCase.Warranty__c != null){
                List<ContentDocumentLink> documentList = [SELECT ContentDocumentId, LinkedEntityId FROM ContentDocumentLink WHERE LinkedEntityId = :thisCase.Warranty__c];
                if(documentList.size() > 0 || thisCase.Warranty__r.Image_of_Receipt__c != null){
                    hasReceipt = true;
                }
                // if(!thisCase.Warranty__r.One_Time_Exception__c){
                //     isValid = true;
                // }else{
                //     for(Warranty_Item__c wi : [SELECT Id,ActualIndicator__c FROM Warranty_Item__c WHERE Warranty__c = :thisCase.Warranty__c]){
                //         if(wi.ActualIndicator__c == 'Vailid Warranty'){
                //             isValid = true;
                //         }
                //     }
                // }
            }
            for(String st : casePRISet){
                if(thisCase.Product_Related_Issue__c != null && thisCase.Product_Related_Issue__c.contains(st)){
                    isContainsPRI = true;
                }
            }
            for(Product2 pr : productList){
                productCodeSet.add(pr.ProductCode);
                productCodeSet.add(pr.ProductCode + 'T');
                productCodeSet.add(pr.ProductCode + '-FC');
                productCodeSet.add('CS-' + pr.ProductCode);
                productCodeSet.add('CS1-' + pr.ProductCode + '-FC');
                productCodeSet.add('CS2-' + pr.ProductCode + '-FC');
                productCodeSet.add('CS-' + pr.ProductCode + '-01');
                if(pr.New_Product__r.ProductCode != null){
                    productCodeSet.add(pr.New_Product__r.ProductCode);
                }
            }
            if(thisCase.Case_Type__c == 'Service Claim'){
                priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Name = 'CNA-Landed Cost for Parts' LIMIT 1].Price_Book__c;
                ord.Tax_Rate__c = 0;
                ord.Total_Tax__c = 0;
            } else {
                /**
                2021/1/12
                Parts warranty order & non-warranty order: 不分品牌和订单类型，全部都取下面这个价格册
                CNA-MSRP for Parts
                **/
                priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Name = 'CNA-MSRP for Parts' LIMIT 1].Price_Book__c;
            }

            //保存订单明细
            List<OrderItem> orderItemList = new List<OrderItem>();
            Map<String,String> storageIdMapToProductId = new Map<String,String>();
            Map<String,String> storageIdMapToRecordType = new Map<String,String>();
            List<String> storageIdList = new List<String>();
            for(ItemList liw : lineItems){
                storageIdList.add(liw.PartLine.Id);
            }

            for(Storage__c s : [SELECT Id,Product__c,Product__r.recordtype.Name FROM Storage__c WHERE Id IN: storageIdList]){
                storageIdMapToProductId.put(s.Id, s.Product__c);
                storageIdMapToRecordType.put(s.Id, s.Product__r.recordtype.Name);
            }

            if(storageIdMapToRecordType.size() > 0){
                // getProductBook(brand,priceBookId,'');
                ord.Pricebook2Id = priceBookId;
                ord.Price_Book__c = priceBookId;
            }else{
                getProductBook(brand,'',caseId);
                ord.Price_Book__c = price.Id;
                ord.Pricebook2Id = price.Id;
            }

            // system.debug(ord.Pricebook2Id);
            // update ord;
            Boolean isPart = false;
            Decimal totalPrice = 0.00;
            Integer itemNumber = 0;
            Decimal itemQuanity = 0;
            for(ItemList liw : lineItems) {
                itemNumber++;
                OrderItem oi = new OrderItem();
                if(String.isNotBlank(liw.PartLine.Id)) {
                    //获取Parts的价格手册
                    List<PricebookEntry> prodEntryList = Util.getPriceBookEntryByProdId(storageIdMapToProductId.get(liw.PartLine.Id),priceBookId);
                    oi.PricebookEntryId = prodEntryList[0].Id;
                }else{
                    //获取产品的价格手册
                    PricebookEntry pbe = pricebookEntryMap.get(stoMap.get(liw.ProductLine.Id).Product__c);
                    ord.Price_Book__c = pbe.Pricebook2Id;
                    ord.Pricebook2Id = pbe.Pricebook2Id;
                    oi.PricebookEntryId = pbe.Id;
                    oi.Product2Id = stoMap.get(liw.ProductLine.Id).Product__c;
                }
                if(storageIdMapToRecordType.get(liw.PartLine.Id) == 'Parts'){
                    isPart  = true;
                }
                oi.OrderId = ord.Id;
                oi.UnitPrice = liw.price;
                oi.Quantity = liw.quantity;
                itemQuanity += liw.quantity;
                totalPrice += liw.price * liw.quantity;
                oi.Warranty_Item__c = liw.warrantyItem;
                oi.Storage_Name__c = liw.warehouse;
                if(ca.Case_Type__c == 'Warranty Order' && !oi.Storage_Name__c.contains('CA')){
                    oi.Product_Type__c = 'CNA Free Warranty Line';
                }else if(ca.Case_Type__c == 'Non-warranty Order'){
                    oi.Product_Type__c = 'CNA General Line';
                }
                if(oi.Storage_Name__c.contains('CA')){
                    ord.Org_Code__c = 'CCA';
                    Ord.Type = 'CA Sample and Warranty Order';
                    oi.Product_Type__c = 'CA Free Warranty Line';
                }

                oi.Schedule_Shipping_Date__c = liw.scheduleShippingDate;
                oi.Price_Book__c = ord.Pricebook2Id;
                oi.Schedule_Shipping_Datetime__c = Datetime.newInstance(
                                oi.Schedule_Shipping_Date__c.year(),
                                oi.Schedule_Shipping_Date__c.month(),
                                oi.Schedule_Shipping_Date__c.day(),
                                0, 0, 0);
                orderItemList.add(oi);
            }

            update ord;
            upsert orderItemList;
            if(isPart){
                ord.Is_Parts_Order__c = isPart;
                update ord;
            }
            if(ItemNumber == 1){
                if(ca.Case_Type__c != 'Non-warranty Order'){
                    String productCode = stoMap.get(lineItems[0].ProductLine.Id).Product__r.ProductCode;
                    if(productCodeSet.contains(productCode)){
                        isSamemodel = true;
                    }
                }
            }
            Boolean canAutoApproveModel = false;
            Set<String> canAutoApproveModels = new Set<String>();
            for(WO_Auto_Approved_Model__mdt temp:[SELECT Id,product_code__c FROM WO_Auto_Approved_Model__mdt]){
                canAutoApproveModels.add(temp.product_code__c);
            }
            if(canAutoApproveModels != null){
                for(ItemList liw : lineItems){
                    if(canAutoApproveModels.contains(liw.ProductLine.ProductCode)){
                        canAutoApproveModel = true;
                        break;
                    }
                }
            }

            ord.canWorkFlow__c = true;
            if(ord.Is_Parts_Order__c){
                RecordType rt = [SELECT Id FROM Recordtype WHERE sobjecttype = 'Order' and name = 'Actived Order'];
                ord.RecordTypeId = rt.Id;
                ord.Status = 'Activated';
            }else{
                if(totalPrice <= 500 && itemQuanity <= 1 && itemNumber <=1  && isSamemodel  && isContainsPRI && canAutoApproveModel){
                    RecordType rt = [SELECT Id FROM Recordtype WHERE sobjecttype = 'Order' and name = 'Actived Order'];
                    ord.RecordTypeId = rt.Id;
                    ord.Status = 'Activated';
                    ord.Posting__c = true;
                    ord.Is_Auto_Approved__c = true;
                    CCM_Service.pushServiceReplacementOrderInfo(String.valueOf(ord.Id));
                }
            }
            update ord;

            if(ord.Type == 'Recall Order') {
                ca.Recall_Status__c = 'Replacement sent';
                update ca;
            } else if(ord.Type == 'Recall Package') {
                ca.Recall_Status__c = 'Packaging sent';
                update ca;
            }
            deleteGeocontact(caseId);

        } catch(Exception e) {
            Database.rollback(point);
            concatMsg = e.getMessage();
            System.debug(LoggingLevel.INFO, '*** e.getLineNumber(): ' + e.getLineNumber());
        }
        return concatMsg;
    }

    @AuraEnabled
    public static String authorizePayPalPayment(String cardNum, String expDate, String cvv2, Decimal amount, String billToFirstName, String billToLastName, String billToStreet, String billToCity, String billToState, String billToCountry, String billToZIP ) {
        CCM_PayPalUtil.BillingAddress ppba = new CCM_PayPalUtil.BillingAddress(billToFirstName,billToLastName,billToStreet,billToCity,billToState,billToCountry,billToZIP);
        expDate = expDate.replaceAll('/', '');
        CCM_PayPalUtil.AuthorizeResponse response = CCM_PayPalUtil.authorizePayPalPayment(cardNum,expDate,cvv2,amount,'',ppba);
        system.debug(response);
        Map<String,Object> result = new Map<String,Object>();
        if(response.resultCode == '0' ){
            result.put('Status', 'Success');
            result.put('Message', response.respMsg);
            result.put('PNREF', response.PNREF);
        }else{
            result.put('Status', 'Fail');
            result.put('Message', response.respMsg);
            result.put('PNREF', response.respMsg);
        }
        return JSON.serialize(result);
    }

    @AuraEnabled
    public static String getGeolocation(String caseId, String street, String city, String state, String postalCode, String country) {
        Map<String,Object> result = new Map<String,Object>();
        try{
            List<Contact> geoConList = [SELECT Id,Mailinglatitude,Mailinglongitude FROM Contact WHERE LastName=:caseId AND FirstName='GeolocationContactOrder' AND Mailinglatitude!=NULL AND Mailinglongitude!=NULL LIMIT 1];
            if(geoConList.size()>0){
                Contact geoCon = geoConList[0];
                geoCon.MailingCity = city;
                geoCon.MailingCountry = country;
                geoCon.MailingStreet = street;
                geoCon.MailingState = state;
                geoCon.MailingPostalCode = postalCode;

                update geoCon;
                result.put('contactId', geoCon.Id);
            }else{
                Contact con = new Contact();
                con.LastName = caseId;
                con.FirstName = 'GeolocationContactOrder';
                con.MailingCity = city;
                con.MailingCountry = country;
                con.MailingStreet = street;
                con.MailingPostalCode = postalCode;
                con.MailingState = state;

                insert con;
                result.put('contactId', con.Id);
            }


        }catch(Exception ex){
            result.put('Status', 'Success');
            result.put('Message', 'Could not get accurate location due to ' + ex.getMessage() + ex.getStackTraceString());
        }
        return JSON.serialize(result);
    }

    @AuraEnabled
    public static String getWarehouse(String caseId,String productId,String productName) {
        Map<String,Object> result = new Map<String,Object>();
        List<String> productNamelst = productName.split(' ');

        try{

            // check case is in alternative product scope, return true if needed
            Set<String> alternativeProducts = CCM_ServiceOrderUtil.getProductsNotMultipleSelect(caseId);
            if(!alternativeProducts.isEmpty()) {
                result.put('needCheckAlternativeProduct', true);
                result.put('alternativeProducts', alternativeProducts);
            }
            else {
                result.put('needCheckAlternativeProduct', false);
            }

            String storageName = productNamelst[productNamelst.size()-1];
            Storage__c originalStorage = [SELECT Id,Product__c,Product__r.RecordType.Name FROM Storage__c WHERE Id=: productId LIMIT 1];
            if(originalStorage.Product__r.RecordType.Name == 'Parts'){
                result.put('warehouse','CNA05');
            }else{
                result.put('warehouse',storageName);
            }
        }catch(Exception ex){
            result.put('Status', 'Success');
            result.put('Message', 'Could not add item due to ' + ex.getMessage() + ex.getStackTraceString());
        }
        return JSON.serialize(result);
    }

    public static String deleteGeocontact(String caseId) {
        Map<String,Object> result = new Map<String,Object>();
        try{
            List<Contact> geoConList = [SELECT Id,Mailinglatitude,Mailinglongitude FROM Contact WHERE LastName=:caseId AND FirstName='GeolocationContactOrder' AND Mailinglatitude!=NULL AND Mailinglongitude!=NULL LIMIT 1];
            if(geoConList.size()>0){
                delete geoConList;
            }
        }catch(Exception ex){
            result.put('Status', 'Success');
            result.put('Message', 'Could not save order due to ' + ex.getMessage() + ex.getStackTraceString());
        }
        return JSON.serialize(result);
    }

    @AuraEnabled
    public static string getCase(String warrantyId){
        String caseId = '';
        List<Case> caseList = [SELECT Id FROM Case WHERE Warranty__c = :warrantyId];
        if(!caseList.isEmpty()) {
            caseId = caseList[0].Id;
        }
        return caseId;
    }

    @AuraEnabled
    public static String queryRecordType(String caseId, String warrantyId) {
        List<Case> caseList = new List<Case>();
        if(String.isBlank(caseId) && String.isNotBlank(warrantyId)) {
            caseList = [SELECT Case_Type__c FROM Case WHERE Warranty__c = :warrantyId];
        }
        else if(String.isNotBlank(caseId)) {
            caseList = [SELECT Case_Type__c FROM Case WHERE Id = :caseId];
        }
        Boolean isNonWarrantyCase = false;
        for(Case c : caseList) {
            if(c.Case_Type__c == 'Non-warranty Order') {
                isNonWarrantyCase = true;
            }
        }

        List<RecordTypeWrapper> wrappers = new List<RecordTypeWrapper>();

        Map<String, String> recordTypeMap = new Map<String, String>();
        if(isNonWarrantyCase) {
            // recordTypeMap.put('Non-Warranty Parts Order', CCM_Constants.ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID);
            // recordTypeMap.put('Order for Lack of Storage', CCM_Constants.ORDER_RECORD_TYPE_ORDER_FOR_LACK_OF_STORAGE_ORDER_ID);
            RecordTypeWrapper wrapper = new RecordTypeWrapper();
            wrapper.name = 'Non-Warranty Parts Order';
            wrapper.value = CCM_Constants.ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID;
            wrappers.add(wrapper);
        }
        else {
            // recordTypeMap.put('Finished Good Warranty Order', CCM_Constants.ORDER_RECORD_TYPE_PLACE_ORDER_ID);
            // recordTypeMap.put('Warranty Parts Order', CCM_Constants.ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID);
            // recordTypeMap.put('Order for Lack of Storage', CCM_Constants.ORDER_RECORD_TYPE_ORDER_FOR_LACK_OF_STORAGE_ORDER_ID);
            RecordTypeWrapper wrapper = new RecordTypeWrapper();
            wrapper.name = 'Finished Good Warranty Order';
            wrapper.value = CCM_Constants.ORDER_RECORD_TYPE_PLACE_ORDER_ID;
            wrappers.add(wrapper);

            wrapper = new RecordTypeWrapper();
            wrapper.name = 'Warranty Parts Order';
            wrapper.value = CCM_Constants.ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID;
            wrappers.add(wrapper);

            wrapper = new RecordTypeWrapper();
            wrapper.name = 'Order for Lack of Storage';
            wrapper.value = CCM_Constants.ORDER_RECORD_TYPE_ORDER_FOR_LACK_OF_STORAGE_ORDER_ID;
            wrappers.add(wrapper);
        }

        return JSON.serialize(wrappers);
    }

    @AuraEnabled
    public static Boolean checkCanCreatePartsOrder(String caseId,String orderRecordTypeId, String warrantyId,String result) {
        // Non-warranty不检测
        if(CCM_Constants.ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID == orderRecordTypeId){
            return true;
        }
        List<ItemList> lineItems = (List<ItemList>) JSON.deserialize(result, List<ItemList>.class);
        Case ca = [SELECT Id,Case_Type__c FROM Case WHERE Id =:caseId];
        User currentUser = QueryUtils.getCurrentUser();
        Date currentDate = System.Today();
        Boolean isCanCreatePartsOrder = true;
        Set<String> canNotCreateOrderSet = new Set<String>();
        Set<Id> itemIdSet = new Set<Id>();
        Set<Id> StorageIdSet = new Set<Id>();
        for (ItemList il : lineItems) {
            if (String.isNotBlank(il.PartLine.Id)) {
                StorageIdSet.add(il.PartLine.Id);
            }
        }
        for(Storage__c st : [SELECT id, Product__c, Product__r.Name, Product__r.ProductCode,Product__r.Item_Number__c, Sub_storage__c FROM Storage__c WHERE Id IN: StorageIdSet]) {
            itemIdSet.add(st.Product__c);
        }
        if(itemIdSet.size() > 0){
            Map<String,String> warrantyPeriodMap = new Map<String,String>();
            Integer daysDifference = 0;
            Warranty__c warranty = [SELECT Id,Product_Use_Type2__c,Purchase_Date__c,(SELECT Id,Expiration_Date_New__c FROM Warranty_Items__r ORDER BY Expiration_Date_New__c DESC) FROM Warranty__c WHERE Id =:warrantyId];
            for(Product2 pro :[SELECT Id,Warranty_Period_for_wearing_parts__c, Warranty_Period_for_WP_Residential__c FROM Product2 WHERE Id IN:itemIdSet]){
                if(warranty.Product_Use_Type2__c == 'Residential'){
                    if(pro.Warranty_Period_for_WP_Residential__c != null){
                        daysDifference = -(currentDate.daysBetween(warranty.Purchase_Date__c));
                        if(daysDifference > Integer.valueOf(pro.Warranty_Period_for_WP_Residential__c)){
                            canNotCreateOrderSet.add(pro.Id);
                        }
                    }else{
                        daysDifference = currentDate.daysBetween(warranty.Warranty_Items__r[0].Expiration_Date_New__c);
                        if(daysDifference < 0){
                            canNotCreateOrderSet.add(pro.Id);
                        }
                    }
                }else if(warranty.Product_Use_Type2__c == 'Industrial/Professional/Commercial'){
                    if(pro.Warranty_Period_for_wearing_parts__c != null){
                        daysDifference = -(currentDate.daysBetween(warranty.Purchase_Date__c));
                        if(daysDifference > Integer.valueOf(pro.Warranty_Period_for_wearing_parts__c)){
                            canNotCreateOrderSet.add(pro.Id);
                        }
                    }else{
                        daysDifference = currentDate.daysBetween(warranty.Warranty_Items__r[0].Expiration_Date_New__c);
                        if(daysDifference < 0){
                            canNotCreateOrderSet.add(pro.Id);
                        }
                    }
                }
            }
            if(canNotCreateOrderSet.size() > 0){
                if((currentUser.Profile.Name != 'NA Customer Service Leader' && currentUser.Profile.Name != 'NA Customer Service Manager') && ca.Case_Type__c != 'Non-warranty Order' && orderRecordTypeId == CCM_Constants.ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID){
                    isCanCreatePartsOrder = false;
                }
            }
        }
        return isCanCreatePartsOrder;

    }

    private class RecordTypeWrapper {
        public String name;
        public String value;
    }

    public static void forCoverage() {
        Integer index = 0;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
    }
}