<!--
 - Created by gluo006 on 9/4/2019.
 -->

<aura:component description="CCM_Community_AllClaim" implements="force:hasRecordId,forceCommunity:availableForAllPageTypes" controller="CCM_ServiceHome" access="global">
        <aura:attribute name="claimColumns" type="List" default="[]"/>
        <aura:attribute name="currentClaims" type="List" default="[]"/>
        <aura:attribute name="claimStartDate" type="String" default=""/>
        <aura:attribute name="claimEndDate" type="String" default=""/>
        <aura:attribute name="isSearch" type="Boolean" default="false"/>
        <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
        <!-- 分页相关属性：页号、每页记录数、总记录数 -->
        <aura:attribute name="pageNumber" type="String" default="1"/>
        <aura:attribute name="pageCount" type="String" default="20" />
        <aura:attribute name="totalRecords" type="String" default="0" />

        <aura:handler name="pageChange" event="c:CCM_ListPageFooter_PageChangeEvt" action="{!c.pageChange}" />
        <aura:handler name="pageCountChange" event="c:CCM_ListPageFooter_PageCountChangeEvt" action="{!c.pageCountChange}" />

        <article class="slds-card slds-m-top--medium">
            <lightning:layout multipleRows="true">
                <lightning:layoutItem size="12" padding="around-small" class ="firstLine">
                    {!$Label.c.ClaimCreatedDate}
                </lightning:layoutItem>
                <lightning:layoutItem size="2" padding="around-small" class ="seconedLine">
                    <lightning:input type ='Date' value="{!v.claimStartDate}" label="" variant="label-hidden" placeholder="" />
                </lightning:layoutItem>
                 <lightning:layoutItem size="0.5" padding="around-small" class ="seconedLine">
                    —
                </lightning:layoutItem>
                <lightning:layoutItem size="2" padding="around-small" class ="seconedLine">
                    <lightning:input type ='Date' value="{!v.claimEndDate}" label="" variant="label-hidden" placeholder="" />
                </lightning:layoutItem>
                <lightning:layoutItem size="2" padding="around-small" class ="seconedLine">
                    <lightning:button variant="brand" label="{!$Label.c.CCM_Portal_Search}" onclick="{!c.handleSearch}"/>
                    <lightning:button variant="brand" label="{!$Label.c.CCM_Portal_Reset}" onclick="{!c.handleReset}"/>
                    <lightning:button variant="brand" label="{!$Label.c.Export}" onclick="{!c.handleExportWarrantyClaimList}"/>
                </lightning:layoutItem>
                  <lightning:layoutItem size="5.5" padding="around-small">
                </lightning:layoutItem>
            </lightning:layout>
                <div class="slds-grid">
                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                                <div class="slds-media__body">
                                        <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                    <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                          title="Latest Orders">
                                            <span><strong>{!$Label.c.CCM_Portal_ClaimHistory}</strong></span>
                                     </span>
                                        </h2>
                                </div>
                        </header>
                </div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                        <c:CCM_DataTable columns="{!v.claimColumns}" data="{!v.currentClaims}" />

            <aura:if isTrue="{!v.currentClaims.length > 1}">
                <div class="slds-text-align_right">
                    <c:CCM_ListPageFooter totalRecords="{! v.totalRecords}" pageCount="{!v.pageCount}"/>
                </div>
            </aura:if>
        </div>
    </article>
    <c:ccmExcelDownload aura:id="exportcmp" fileName="{!v.exportFileName}"></c:ccmExcelDownload>
</aura:component>