public with sharing class CCM_AccountSendApproval implements Triggers.Handler {
    public static Boolean isRun = true;

	private List<Account> newRecords = (List<Account>)Trigger.new;
	private Map<Id, Account> oldMap = (Map<Id, Account>)Trigger.oldMap;
    public void handle() {
        if(!isRun){
            return;
        }
        
		if (!CCM_PromotionUtil.runAccountTrigger ) {
			return;
		}
		
		if (!Boolean.valueOf(Label.Trigger_Run_Account_Send_Approvel)) {
			return;
		}
        
        if (Trigger.isAfter) {
        	if (Trigger.isUpdate) {
        		System.debug(LoggingLevel.INFO, '*** aaaaaaaa=======>: ');
        		User loginUser = [SELECT Id, Name, UserRole.Name, Profile.Name From User where Id =:UserInfo.getUserId()];
        		if (loginUser == NULL || loginUser.Profile==NULL || loginUser.Profile.Name == 'Finance' || loginUser.Profile.Name.contains('Partner')) {
        			return;
        		}
        		List<Account> itemList = (List<Account>) Trigger.new;
        		Schema.DescribeSObjectResult d = Schema.SObjectType.Account;
        		Map<Id, Schema.RecordTypeInfo> rtIdToInfoMap = d.getRecordTypeInfosById();
        		List<ProcessInstanceWorkitem> piwList = [
		    		SELECT Id, ProcessInstance.TargetObjectId FROM ProcessInstanceWorkitem
		            WHERE ProcessInstance.TargetObjectId IN :Trigger.newMap.keySet()
		    	];
        		for (Account item : itemList) {
        			Account oldacc = (Account)Trigger.oldMap.get(item.Id);

					Boolean isTriggerApproval = false;
					for(FieldSetMember tpfFM : SObjectType.Account.fieldSets.TriggerApprovalField.getFields()){
						if(String.valueOf(item.get(tpfFM.getFieldPath())) <> String.valueOf(oldacc.get(tpfFM.getFieldPath()))){
							isTriggerApproval = true;
							break;
						}
					}

					if(!isTriggerApproval){
						continue;
					}

        			if ((String.isNotBlank(item.AccountNumber) && rtIdToInfoMap.get(item.RecordTypeId).getName() == 'Channel' && String.isNotBlank(oldacc.AccountNumber))
						|| (rtIdToInfoMap.get(item.RecordTypeId).getDeveloperName() == CCM_Constants.CUSTOMER_RECORD_TYPE_POTENTIAL_STORE_LOCATION_DEVELOPER_NAME || rtIdToInfoMap.get(item.RecordTypeId).getDeveloperName() == CCM_Constants.CUSTOMER_RECORD_TYPE_EXISTING_STORE_LOCATION_DEVELOPER_NAME) && item.Update_Owner_Approval__c) {
                        if (oldacc.IsPartner != item.IsPartner) {
                            continue;
                        }
        				Boolean duplicateApproval = false;
		    			for (ProcessInstanceWorkitem piw : piwList) {
		    				System.debug(LoggingLevel.INFO, '*** Item.Id===>: ' + Item.Id);
		    				System.debug(LoggingLevel.INFO, '*** piw.ProcessInstance.TargetObjectId: ' + piw.ProcessInstance.TargetObjectId);
		    				if (piw.ProcessInstance.TargetObjectId == item.Id) {
		    					System.debug(LoggingLevel.INFO, '*** duuuuuuuuuu!: ');
		    					duplicateApproval = true;
		    					break;
		    				}
		    			}
		    			Account oldItem = (Account) Trigger.oldMap.get(item.Id);
		    			if (oldItem.Approval_Status__c != 'Pending for Approval' && item.Approval_Status__c == 'Pending for Approval') {
		    				continue;
		    			}
		    			if (!duplicateApproval) {
		    				System.debug(LoggingLevel.INFO, '*** --------------------------Approval----------------------: ');
		    				Approval.ProcessSubmitRequest request = new Approval.ProcessSubmitRequest();
		    				request.setComments(Label.CCM_SubmitApproval_Customer);
		        			request.setObjectId(item.Id);
		        			request.setSkipEntryCriteria(true);
		        			request.setSubmitterId(UserInfo.getUserId());

		        			Approval.ProcessResult result;
		        			result = Approval.process(request);
		    			}
        			}
        		}

        	}
        }
    }
}