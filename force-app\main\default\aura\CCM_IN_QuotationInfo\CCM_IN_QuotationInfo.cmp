<!--
  @description       : 
  <AUTHOR> <EMAIL>
  @group             : 
  @last modified on  : 03-22-2024
  @last modified by  : <EMAIL>
-->
<aura:component access="global" controller="CCM_Quotation_DetailCtl" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId">
    
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="customerId" type="String" default=""/>
    <aura:attribute name="stepNameList" type="List" default="[]"/>
    <aura:attribute name="currentStep" type="Integer" />
    <aura:attribute name="quotation" type="Object" />
    <aura:attribute name="quotationItems" type="List" default="[]"/>
    <aura:attribute name="customerName" type="String" default=""/>
    <aura:attribute name="brandScope" type="String" default=""/>
    <aura:attribute name="brandScopeOpt" type="List" default="" access="public"/>
    <aura:attribute name="paymentTerm" type="String" default=""/>
    <aura:attribute name="paymentTermLabel" type="String" default="" />
    <aura:attribute name="freightTermLabel" type="String" default="" />
    <aura:attribute name="freightTermRuleFee" type="String" default="" />
    <aura:attribute name="orderTypeVal" type="String" default=""/>
    <aura:attribute name="hasDropShipAddress" type="Boolean" default="false"/>
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <!--Add by Abby on 06022020 for research payment term-->
    <aura:attribute name="paymentTermAllOpts" type="List" default="[]"/>
    <aura:attribute name="paymentTermSelectOpt" type="List" default="[]"/>
    <aura:attribute name="paymentTermValue" type="String" default=""/>
    <aura:attribute name="customerType" type="String" default=""/>
    <aura:attribute name="needResearch" type="Boolean" default="false"/>
    <aura:attribute name="customerCluster" type="String" default=""/>

    <!--Whole Order Pormotion-->
    <aura:attribute name="wholeOrderPromo" type="Object" default=""/>
    <aura:attribute name="termsPromo" type="Object" default=""/>

    <!--Customer Org Code-->
    <aura:attribute name="customerOrgCode" type="String" default=""/>

    <!--Add by Eric on 03072022 for Surcharge Rate -->
    <aura:attribute name="surcharge" type="Decimal" default="0"/>

    <aura:handler name="change" value="{!v.customerId}" action="{!c.refreshBrands}"/>

    <aura:handler name="change" value="{!v.orderTypeVal}" action="{!c.clearOrderItem}"/>

    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <!-- <ltng:require  scripts="{!join(',', $Resource.Validator)}" afterScriptsLoaded="{!c.doInit}" /> -->

    <!-- Add by Roger -->
    <aura:attribute name="expirateDate" type="Boolean" default="true"/>

    <aura:attribute name="minSelectDate" type="String" />

    <!-- for first time purchase payment term promotion -->
    <aura:attribute name="needCheckOrder" type="Boolean" default="true" />

    <div class="slds-box slds-theme_default">
        <div class="halp-step-box-tabset-box slds-p-horizontal--medium slds-p-top--medium">
            <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
            <div class="slds-grid slds-grid--align-center slds-size--1-of-1">
                <div class="slds-col slds-size_1-of-1 slds-wrap">
                    <c:CCM_ProcessStep currentStep="{!v.currentStep}" stepName="{!v.stepNameList}" />

                    <div class="slds-path  slds-p-top_small slds-p-vertical_medium">
                        <aura:if isTrue="{!v.currentStep == 1}">
                            <c:CCM_IN_Quotation_SelectCustomer currentStep="{!v.currentStep}" customerId="{!v.customerId}" customerName="{!v.customerName}" orderTypeVal="{!v.orderTypeVal}" hasDropShipAddress="{!v.hasDropShipAddress}" recordId="{!v.recordId}" />
                        </aura:if>

                        <aura:if isTrue="{!v.currentStep == 2}">
                            <c:CCM_IN_Quotation_SelectProduct currentStep="{!v.currentStep}" customerId="{!v.customerId}" recordId="{!v.recordId}" brandScope="{!v.brandScope}" brandScopeOpt="{!v.brandScopeOpt}" quotation="{!v.quotation}" quotationItemList="{!v.quotationItems}" freightTermRuleFee="{!v.freightTermRuleFee}" paymentTermLabel="{!v.paymentTermLabel}" freightTermLabel="{!v.freightTermLabel}" orderTypeVal="{!v.orderTypeVal}" customerType="{!v.customerType}" paymentTermAllOpts="{!v.paymentTermAllOpts}" paymentTermSelectOpt="{!v.paymentTermSelectOpt}" needResearch="{!v.needResearch}" paymentTermValue="{!v.paymentTermValue}" customerCluster="{!v.customerCluster}" customerOrgCode="{!v.customerOrgCode}" defaultPaymentTerm="{!v.paymentTerm}" wholeOrderPromo="{!v.wholeOrderPromo}" termsPromo="{!v.termsPromo}" surcharge="{!v.surcharge}" ExpirateDate="{!v.expirateDate}" minSelectDate="{!v.minSelectDate}" needCheckOrder="{!v.needCheckOrder}"/>
                        </aura:if>

                        <aura:if isTrue="{!v.currentStep == 3}">
                            <c:CCM_Quotation_FillAddress_Cmp recordId="{!v.recordId}" quotation="{!v.quotation}" customerId="{!v.customerId}" currentStep="{!v.currentStep}" freightTermRuleFee="{!v.freightTermRuleFee}" brandScope="{!v.brandScope}" orderTypeVal="{!v.orderTypeVal}" needResearch="{!v.needResearch}" paymentTermSelectOpt="{!v.paymentTermSelectOpt}" paymentTermValue="{!v.paymentTermValue}" needCheckOrder="{!v.needCheckOrder}" bypassCheck="true"/>
                        </aura:if>

                        <aura:if isTrue="{!v.currentStep == 4}">
                            <c:CCM_Quotation_Detail recordId="{!v.recordId}" currentStep="{!v.currentStep}" />
                        </aura:if>
                    </div>
                </div>
            </div>
        </div>
    </div>
</aura:component>