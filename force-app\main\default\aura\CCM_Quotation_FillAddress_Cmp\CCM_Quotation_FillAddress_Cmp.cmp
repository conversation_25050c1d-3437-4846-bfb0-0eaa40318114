<!--
  @description       :
  <AUTHOR> <EMAIL>
  @group             :
  @last modified on  : 03-22-2024
  @last modified by  : <EMAIL>
-->
<aura:component description="CCM_Quotation_FillAddress_Cmp" implements="flexipage:availableForAllPageTypes,forceCommunity:availableForAllPageTypes" controller="CCM_Quotation_DetailCtl">
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="quotation" type="Object" default=""/>
    <aura:attribute name="customerId" type="String" default=""/>
    <aura:attribute name="currentStep" type="Integer" default="2"/>
    <aura:attribute name="currencySymbol" type="String" default="$"/>
    <aura:attribute name="buyerContactInput" type="String" default=""/>
    <aura:attribute name="showFreeShippingMsg" type="Boolean" default="false"/>
    <aura:attribute name="actualShippingFee" type="Currency" default="0.00"/>
    <aura:attribute name="freeShipping" type="Currency" default="0.00"/>
    <aura:attribute name="waiveShippingFee" type="Currency" default=""/>
    <aura:attribute name="handlingFee" type="Decimal" default="0.00"/>
    <aura:attribute name="billingAddress" type="String" default=""/>
    <aura:attribute name="shippingAddress" type="String" default=""/>
    <aura:attribute name="paymentTerm" type="String" default=""/>
    <aura:attribute name="freightTerm" type='String' default=""/>
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <aura:attribute name="freightTermRuleFee" type="Currency" default="0.00"/>
    <aura:attribute name="productAmount" type="Currency" default="0.00"/>
    <aura:attribute name="requireFlag" type="Boolean" default="true"/>
    <aura:attribute name="requireShipAddrFlag" type="Boolean" default="true"/>
    <aura:attribute name="isAddAddress" type="Boolean" default="false"/>
    <aura:attribute name="brandScope" type="String" default=""/>
    <!--是坦是新添加的临时Shipping Address Flag-->
    <aura:attribute name="isAlternativeAddress" type="Boolean" default="false"/>
    <aura:attribute name="isDropShip" type="Boolean" default="false"/>
    <aura:attribute name="isEdit" type="Boolean" default="false"/>
    <aura:attribute name="isChangeAddress" type="Boolean" default="false"/>
    <!--Order Type: Distribute / Dropship-->
    <aura:attribute name="orderTypeVal" type="String" default=""/>
    <aura:attribute name="isInnerUser" type="String" default="false"/>
    <aura:attribute name="isDisableShippingBy" type="Boolean" default="false"/>
    <aura:attribute name="salesAgencyAlias" type="String" default=""/>
    <aura:attribute name="salesAgencyId" type="String" default=""/>
    <aura:attribute name="needResearch" type="Boolean" default="false"/>
    <aura:attribute name="paymentTermSelectOpt" type="List" default="[]"/>
    <aura:attribute name="paymentTermValue" type="String" default=""/>
    <aura:attribute name="shippingBy" type="String" default=""/>
    <aura:attribute name="showCountryOptions" type="List" default="[ {'label': 'US', 'value': 'US'},
    {'label': 'CA', 'value': 'CA'},{'label': 'PR', 'value': 'PR'},]" access="public"/>
    <aura:attribute name="shippingByOptions" type="List" default="[]" access="public"/>
    <aura:attribute name="showStateMessage" type="Boolean" default="false"/>
    <aura:attribute name="isCCA" type="Boolean" default="false"/>
    <aura:attribute name="isShowShippingBy" type="Boolean" default="false"/>
    <aura:attribute name="isDisplaySection" type="Boolean" default="false"/>
    <aura:attribute name="selectedShippingAddressItem" type="Object"  access="public" />

    <aura:attribute name="isCollect" type="Boolean" default="false"/>
    <aura:attribute name="isThirdPartyBilling" type="Boolean" default="false"/>
    <aura:attribute name="bypassCheck" type="Boolean" default="false"/>
    <aura:attribute name="needCheckOrder" type="Boolean" />
    <!-- sqy add -->
    <aura:attribute name="isPortal" type="String" default="false"/>


    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <aura:handler name="change" value="{!v.quotation.Shipping_Address__c}" action="{!c.doChangeShippingAddress}"/>
    <aura:handler name="change" value="{!v.quotation.Billing_Address__c}" action="{!c.doChangeBillingAddress}"/>
    <aura:handler event="c:CCM_SelectListEvt" action="{!c.doChangeShippingBy}"/>
    <ltng:require scripts="{!join(',', $Resource.Validator)}"/>

    <lightning:card class="mainContent">
        <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
        <div class="c-container">
            <lightning:layout multipleRows="true">
                <lightning:layoutItem padding="around-small" size="6">
                    <div class="header-column">
                    <aura:if isTrue="{!v.orderTypeVal == 'Y'}">
                        <c:CCM_SearchAddressCmp
                            aura:id="required-Field"
                            selectedRecordId="{!v.quotation.Billing_Address__c}"
                            inputAddress="{!v.quotation.Billing_Address_Name__c}"
                            addressType="Dropship_Billing_Address"
                            customerId="{!v.customerId}"
                            brandNames="{!v.brandScope}"
                            quotation="{!v.quotation}"
                            isRequired="{!v.requireFlag}"
                            isPortal="{!v.isPortal}"
                            fieldLabel="{!$Label.c.CCM_Portal_BillingAddress}" />
                            <aura:set attribute="else">
                                <c:CCM_SearchAddressCmp
                                    aura:id="required-Field"
                                    selectedRecordId="{!v.quotation.Billing_Address__c}"
                                    inputAddress="{!v.quotation.Billing_Address_Name__c}"
                                    addressType="Billing_Address"
                                    customerId="{!v.customerId}"
                                    brandNames="{!v.brandScope}"
                                    quotation="{!v.quotation}"
                                    isRequired="{!v.requireFlag}"
                                    isPortal="{!v.isPortal}"
                                    fieldLabel="{!$Label.c.CCM_Portal_BillingAddress}" />
                            </aura:set>
                        </aura:if>
                    </div>
                </lightning:layoutItem>

                <aura:if isTrue="{!v.orderTypeVal == 'Y'}">
                        <lightning:layoutItem padding="around-small" size="5" class="ccm_widthStyle">
                            <div class="header-column">
                                <aura:if isTrue="{!!v.isAlternativeAddress}">
                                    <c:CCM_SearchAddressCmp
                                        aura:id="required-shipping"
                                        selectedRecordId="{!v.quotation.Shipping_Address__c}"
                                        inputAddress="{!v.quotation.Shipping_Address_Name__c}"
                                        addressType="Dropship_Shipping_Address"
                                        customerId="{!v.customerId}"
                                        brandNames="{!v.brandScope}"
                                        quotation="{!v.quotation}"
                                        isRequired="{!v.requireShipAddrFlag}"
                                        secondCustomerId="{!v.secondCustomerId}"
                                        fieldLabel="{!$Label.c.CCM_Portal_ShippingAddress}"/>
                                    <aura:set attribute="else">
                                        <p class="ccm_label">{!$Label.c.CCM_Portal_ShippingAddress}</p>
                                        <lightning:formattedAddress
                                            street="{!v.quotation.Additional_Shipping_Street__c}"
                                            city="{!v.quotation.Additional_Shipping_City__c}"
                                            country="{!v.quotation.Additional_Shipping_Country__c}"
                                            province="{!v.quotation.Additional_Shipping_Province__c}"
                                            postalCode="{!v.quotation.Additional_Shipping_Postal_Code__c}"
                                        />
                                        <ul>
                                            <li> {!v.quotation.Additional_Contact_Name__c}</li>
                                            <li>{!v.quotation.Additional_Contact_Phone__c}</li>
                                            <li>{!v.quotation.Additional_Contact_Email__c}</li>
                                        </ul>
                                    </aura:set>
                                </aura:if>
                            </div>
                        </lightning:layoutItem>
                    <aura:set attribute="else">
                        <lightning:layoutItem padding="around-small" size="5" class="ccm_widthStyle">
                            <div class="header-column">
                                <aura:if isTrue="{!!v.isAlternativeAddress}">
                                    <c:CCM_SearchAddressCmp
                                        aura:id="required-shipping"
                                        selectedRecordId="{!v.quotation.Shipping_Address__c}"
                                        inputAddress="{!v.quotation.Shipping_Address_Name__c}"
                                        addressType="Shipping_Address"
                                        customerId="{!v.customerId}"
                                        brandNames="{!v.brandScope}"
                                        quotation="{!v.quotation}"
                                        isRequired="{!v.requireShipAddrFlag}"
                                        fieldLabel="{!$Label.c.CCM_Portal_ShippingAddress}"/>
                                    <aura:set attribute="else">
                                        <p class="ccm_label">{!$Label.c.CCM_Portal_ShippingAddress}</p>
                                        <lightning:formattedAddress
                                            street="{!v.quotation.Additional_Shipping_Street__c}"
                                            city="{!v.quotation.Additional_Shipping_City__c}"
                                            country="{!v.quotation.Additional_Shipping_Country__c}"
                                            province="{!v.quotation.Additional_Shipping_Province__c}"
                                            postalCode="{!v.quotation.Additional_Shipping_Postal_Code__c}"
                                        />
                                        <ul>
                                            <li> {!v.quotation.Additional_Contact_Name__c}</li>
                                            <li>{!v.quotation.Additional_Contact_Phone__c}</li>
                                            <li>{!v.quotation.Additional_Contact_Email__c}</li>
                                        </ul>
                                    </aura:set>
                                </aura:if>
                            </div>
                        </lightning:layoutItem>
                    </aura:set>
                </aura:if>

                <c:CCM_IfContainsCmp
                    items="{!$Label.c.CCM_Purchase_Order_Hide_Adding_Shipping_Address_Brand_List}"
                    element="{!v.brandScope}"
                    ignoreCase="true"
                    reverse="true"
                    bypassCheck="{!v.bypassCheck}"
                >
                    <aura:if isTrue="{!!v.isCCA}">
                    <lightning:layoutItem padding="around-small" size="1" class="ccm_iconStyle">
                        <aura:if isTrue="{!!v.isEdit}">
                            <lightning:buttonIcon iconName="utility:add" variant="bare" onclick="{! c.doAddAddress}" alternativeText="{!$Label.c.CCM_Portal_AddShippingAddress}" />
                            <aura:set attribute="else">
                                <lightning:buttonIcon iconName="utility:edit" variant="bare" onclick="{! c.doAddAddress}" alternativeText="{!$Label.c.CCM_Portal_EditShippingAddress}" />
                                <lightning:buttonIcon iconName="utility:change_record_type" variant="bare" onclick="{! c.doChangeAddress}" alternativeText="{!$Label.c.CCM_Portal_ChangeShippingAddress}" />
                            </aura:set>
                        </aura:if>
                    </lightning:layoutItem>
                </aura:if>
                </c:CCM_IfContainsCmp>
            </lightning:layout>

            <aura:if isTrue="{!v.isDisplaySection}">
                <c:CCM_Section title="Freight Forwarder Information" expandable="false" >
                    <lightning:layout multipleRows="true">
                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input FieldName="ShipTo_Name__c" value="{!v.quotation.ShipTo_Name__c}"
                                    objectName="Purchase_Order__c" label="ShipTo Name"/>
                            </div>
                            <aura:if isTrue="{!v.quotation.ShipTo_Name__c.length > 30}">
                                 <div style="color: red;">Maximum number of characters allowed is 30.</div>
                            </aura:if>
                        </lightning:layoutItem>

                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input FieldName="Address_Line1__c" value="{!v.quotation.Address_Line1__c}"
                                    objectName="Purchase_Order__c" label="Address Line 1"/>
                            </div>
                            <aura:if isTrue="{!v.quotation.Address_Line1__c.length > 30}">
                                 <div style="color: red;">Maximum number of characters allowed is 30.</div>
                            </aura:if>
                        </lightning:layoutItem>

                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input FieldName="Address_Line2__c" value="{!v.quotation.Address_Line2__c}"
                                    objectName="Purchase_Order__c" label="Address Line 2"/>
                            </div>
                            <aura:if isTrue="{!v.quotation.Address_Line2__c.length > 30}">
                                 <div style="color: red;">Maximum number of characters allowed is 30.</div>
                            </aura:if>
                        </lightning:layoutItem>

                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input FieldName="City__c" value="{!v.quotation.City__c}"
                                    objectName="Purchase_Order__c" label="City"/>
                            </div>
                            <aura:if isTrue="{!v.quotation.City__c.length > 30}">
                                 <div style="color: red;">Maximum number of characters allowed is 30.</div>
                            </aura:if>
                        </lightning:layoutItem>

                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input FieldName="State__c" value="{!v.quotation.State__c}"
                                    objectName="Purchase_Order__c" label="State" onchange = "{!c.checkState}"/>
                            </div>
                            <aura:if isTrue="{!v.showStateMessage}">
                                 <div style="color: red;">The province/State Code must be a short code. Please fill in two uppercase letter and follow format like 'IL'.</div>
                            </aura:if>
                        </lightning:layoutItem>



                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input  FieldName="Zip_Code__c" value="{!v.quotation.Zip_Code__c}"
                                    objectName="Purchase_Order__c" label="Zip"/>
                            </div>
                            <aura:if isTrue="{!v.quotation.Zip_Code__c.length > 30}">
                                 <div style="color: red;">Maximum number of characters allowed is 30.</div>
                            </aura:if>
                        </lightning:layoutItem>



                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:combobox FieldName="Country__c" placeholder="" objectName="Purchase_Order__c" label="Country" value="{!v.quotation.Country__c}" options="{!v.showCountryOptions}"></lightning:combobox>
                            </div>
                        </lightning:layoutItem>



                          <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input FieldName="Phone_Number__c" value="{!v.quotation.Phone_Number__c}"
                                    objectName="Purchase_Order__c" label="Phone Number"/>
                            </div>
                            <aura:if isTrue="{!v.quotation.Phone_Number__c.length > 30}">
                                 <div style="color: red;">Maximum number of characters allowed is 30.</div>
                            </aura:if>
                        </lightning:layoutItem>


                    </lightning:layout>
                </c:CCM_Section>
            </aura:if>

            <c:CCM_Section title="{!(v.isCCA ? $Label.c.CCM_Portal_ShippingFees : $Label.c.CCM_Portal_CarrierFee)}" expandable="false" >
                <lightning:layout multipleRows="true">
                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="header-column">
                            <aura:if isTrue ="{!v.isShowShippingBy}">
                                <lightning:combobox aura:id="required-Field" name="Shipping_By__c" label="{!$Label.c.CCM_Portal_ShippingBy}" value = "{!v.quotation.Shipping_By__c}"  placeholder="" options="{!v.shippingByOptions}"  required="{!v.requireFlag}"></lightning:combobox>
                                    <aura:set attribute="else">
                                        <c:CCM_SelectList aura:id="required-Field" FieldName="Shipping_By__c" value="{!v.quotation.Shipping_By__c}" required="{!v.requireFlag}" objectName="Purchase_Order__c" label="{!$Label.c.CCM_Portal_ShippingBy}" showBlankVal="false" disabled="{!v.isDisableShippingBy}"/>
                                </aura:set>
                                </aura:if>
                        </div>
                    </lightning:layoutItem>

                    <aura:if isTrue="{!v.quotation.Shipping_By__c != 'Chervon'}">
                        <aura:if isTrue="{!v.quotation.Shipping_By__c == 'Customer'}">
                            <lightning:layoutItem padding="around-small" size="6">
                                    <div class="header-column">
                                        <c:CCM_SelectList aura:id="required-Field" FieldName="Delivery_Supplier__c" value="{!v.quotation.Delivery_Supplier__c}"
                                         objectName="Purchase_Order__c" label="{!$Label.c.CCM_Portal_PreferredCarrier}" disabled="true" />
                                    </div>
                            </lightning:layoutItem>
                            <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <lightning:input aura:id="required-Field" name="" label="{!$Label.c.CCM_Portal_YourCustomerFreightAccount}" value="{!v.quotation.Customer_Freight_Account__c}" disabled="true"/>
                                </div>
                            </lightning:layoutItem>
                            <aura:set attribute="else">
                                <lightning:layoutItem padding="around-small" size="6">
                                    <div class="header-column">
                                        <c:CCM_SelectList aura:id="required-Field" FieldName="Delivery_Supplier__c" value="{!v.quotation.Delivery_Supplier__c}"
                                         objectName="Purchase_Order__c" label="{!$Label.c.CCM_Portal_PreferredCarrier}" required="{!v.requireFlag}" showBlankVal="true" />
                                    </div>
                                </lightning:layoutItem>
                                <lightning:layoutItem padding="around-small" size="6">
                                    <div class="header-column">
                                        <lightning:input aura:id="required-Field" name="" label="{!$Label.c.CCM_Portal_YourCustomerFreightAccount}" value="{!v.quotation.Customer_Freight_Account__c}" required="{!v.requireFlag}"/>
                                    </div>
                                </lightning:layoutItem>
                            </aura:set>
                        </aura:if>
                    </aura:if>

                    <aura:if isTrue="{!and(v.isAlternativeAddress == false, v.isInnerUser == true)}">
                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input name="" label="{!$Label.c.CCM_Portal_SalesAgency}" value="{!v.salesAgencyAlias}" disabled="true"/>
                            </div>
                        </lightning:layoutItem>
                    </aura:if>

                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="header-column">
                            <lightning:input name="" label="{!$Label.c.CCM_Portal_FreightFee}" value="{!(v.currencySymbol + ' ' + v.actualShippingFee)}" disabled="true"/>
                        </div>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="header-column">
                            <lightning:input name="" label="{!$Label.c.CCM_Portal_HandlingFee}" value="{!(v.currencySymbol + ' ' + v.handlingFee)}" disabled="true"/>
                        </div>
                    </lightning:layoutItem>

                    <!-- <aura:if isTrue="{!!v.isCCA}"> -->
                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input name="" label="{!$Label.c.CCM_Portal_FreightFeeToBeWaived}" value="{!(v.currencySymbol + ' ' +  v.freeShipping)}" disabled="true"/>
                            </div>
                        </lightning:layoutItem>
                    <!-- </aura:if> -->

                    <aura:if isTrue="{!v.isInnerUser}">
                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input name="" label="{!$Label.c.CCM_Portal_ShippingDiscount}" value="{!v.quotation.Extra_Freight_Fee_To_Be_Waived__c}"/>
                            </div>
                        </lightning:layoutItem>
                    </aura:if>
                </lightning:layout>
            </c:CCM_Section>

            <c:CCM_Section title="{!$Label.c.CCM_Portal_DeliveryInformation}" expandable="false" >
                <lightning:layout multipleRows="true">
                    <aura:if isTrue="{!v.quotation.Shipping_By__c == 'Chervon'}">
                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <c:CCM_SelectList FieldName="Shipping_Method__c" value="{!v.quotation.Shipping_Method__c}"
                                    objectName="Purchase_Order__c" label="{!$Label.c.CCM_Portal_ShippingMethod}" showBlankVal="false" disabled="true"/>
                            </div>
                        </lightning:layoutItem>
                    </aura:if>

                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="header-column">
                            <lightning:input aura:id="required-Field" label="{!$Label.c.CCM_Portal_YourPurchaseOrder}" value="{!v.quotation.Customer_PO_Num__c}" required="{!v.requireFlag}"
                                pattern=".{0,22}" messageWhenPatternMismatch="Warning: You could enter up to 22 characters."
                            />
                        </div>
                    </lightning:layoutItem>

                    <!-- <aura:if isTrue="{!v.isInnerUser == true}">
                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input label="Expected Delivery Date" value="{!v.quotation.Expected_Delivery_Date__c}" type="Date" />
                            </div>
                        </lightning:layoutItem>
                    </aura:if> -->

                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="header-column">
                            <ui:inputText aura:id="txtEmail" label="{!$Label.c.CCM_Portal_BuyerEmail}" class="slds-input ccm_uiInput" value="{!v.quotation.Email__c}"/>
                        </div>
                    </lightning:layoutItem>

                    <!-- 是NP时，credit必填     NP_Bill_To__c是address字段，借用下quotation显示 -->
                    <aura:if isTrue="{!v.quotation.NP_Bill_To__c}">
                        <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <lightning:input aura:id="required-Field" type="Text" label="{!$Label.c.CCM_Portal_CreditAuthorization}" value="{!v.quotation.Credit_Authorization__c }" required="true"/>
                                </div>
                        </lightning:layoutItem>
                        <aura:set attribute="else">
                            <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <lightning:input type="Text" label="{!$Label.c.CCM_Portal_CreditAuthorization}" value="{!v.quotation.Credit_Authorization__c }"/>
                                 </div>
                            </lightning:layoutItem>
                        </aura:set>
                    </aura:if>

                    <!-- <lightning:layoutItem padding="around-small" size="6">
                        <div class="header-column">
                            <lightning:input type="Text" label="{!$Label.c.CCM_Portal_CreditAuthorization}" value="{!v.quotation.Credit_Authorization__c }"/>
                        </div>
                    </lightning:layoutItem> -->

                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="header-column">
                            <!-- <c:CCM_SelectList FieldName="Shipping_Priority__c" value="{!v.quotation.Shipping_Priority__c}"
                                    objectName="Purchase_Order__c" label="Shipment Priority"/> -->
                        </div>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="around-small" size="12">
                        <div class="header-column">
                            <lightning:textarea name="" label="{!$Label.c.CCM_Portal_ShippingandRoutingInstruction}" value="{!v.quotation.Notes__c}" messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255"/>
                        </div>
                    </lightning:layoutItem>
                </lightning:layout>
            </c:CCM_Section>
        </div>

        <c:CCM_Modal size="medium" isShow="{!v.isAddAddress}" onClose="{!c.closeModal}" title="{!$Label.c.CCM_Portal_AlternativeShippingAddressInformation}">
            <lightning:layout verticalAlign="center">
                <lightning:layoutItem class="slds-p-left--medium slds-text-heading--small" size="6">
                    <lightning:inputAddress
                            aura:id="alternativeShippingAddress"
                            addressLabel="{!$Label.c.CCM_Portal_AlternativeShippingAddress}"
                            streetLabel="{!$Label.c.CCM_Portal_Street}"
                            cityLabel="{!$Label.c.CCM_Portal_City}"
                            countryLabel="{!$Label.c.CCM_Portal_Country}"
                            provinceLabel="{!$Label.c.CCM_Portal_ProvinceState}"
                            postalCodeLabel="{!$Label.c.CCM_Portal_PostalCode}"
                            street="{!v.quotation.Additional_Shipping_Street__c}"
                            city="{!v.quotation.Additional_Shipping_City__c}"
                            province="{!v.quotation.Additional_Shipping_Province__c}"
                            country="{!v.quotation.Additional_Shipping_Country__c}"
                            postalCode="{!v.quotation.Additional_Shipping_Postal_Code__c}"
                            required="{!v.requireFlag}"
                        />
                </lightning:layoutItem>
                <lightning:layoutItem class="slds-p-left--medium slds-text-heading--small" size="6">
                    <lightning:input aura:id="alternativeShippingAddress" name="contactName" label="{!$Label.c.CCM_Portal_ContactName}" value="{!v.quotation.Additional_Contact_Name__c}" required="{!v.requireFlag}"/>

                    <lightning:input type="Phone" aura:id="alternativeShippingAddress" name="contactPhone" label="{!$Label.c.CCM_Portal_ContactPhone}" value="{!v.quotation.Additional_Contact_Phone__c}" required="{!v.requireFlag}"/>

                    <lightning:input type="Email" aura:id="alternativeShippingAddress" name="contactEmail" label="{!$Label.c.CCM_Portal_ContactEmail}" value="{!v.quotation.Additional_Contact_Email__c}" required="{!v.requireFlag}"/>
                </lightning:layoutItem>
            </lightning:layout>
            <aura:set attribute="footer">
                <lightning:button class="slds-button slds-button_neutral" onclick="{!c.closeModal}">
                    {!$Label.c.CCM_Portal_Cancel}
                </lightning:button>
                <lightning:button class="slds-button slds-button_brand" onclick="{!c.doSaveAddress}">
                    {!$Label.c.CCM_Portal_Save}
                </lightning:button>
            </aura:set>
        </c:CCM_Modal>

        <c:CCM_Modal size="noraml" isShow="{!v.isChangeAddress}" onClose="{!c.closeModal}" title="{!$Label.c.CCM_Portal_ShippingAddressChangeConfirmation}">
            <lightning:layout verticalAlign="center">
                <div class="text-center">
                    {!$Label.c.CCM_Portal_Willyouconfirmthechangeshippingaddress}
                </div>
            </lightning:layout>
            <aura:set attribute="footer">
                <lightning:button class="slds-button slds-button_neutral" onclick="{!c.closeChangeModal}">
                    {!$Label.c.CCM_Portal_Cancel}
                </lightning:button>
                <lightning:button class="slds-button slds-button_brand" onclick="{!c.doConfirm}">
                    {!$Label.c.CCM_Portal_Confirm}
                </lightning:button>
            </aura:set>
        </c:CCM_Modal>

        <aura:set attribute="footer">
            <div>
                <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" label="{!$Label.c.CCM_Portal_Previous}" title="{!$Label.c.CCM_Portal_Previous}" onclick="{!c.previousStep}" />
                <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" label="{!$Label.c.CCM_Portal_Save}" title="{!$Label.c.CCM_Portal_Save}" onclick="{!c.doSave}" />
                <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_Portal_Next}" title="{!$Label.c.CCM_Portal_Next}" onclick="{!c.nextStep}" />
            </div>
        </aura:set>
    </lightning:card>
</aura:component>