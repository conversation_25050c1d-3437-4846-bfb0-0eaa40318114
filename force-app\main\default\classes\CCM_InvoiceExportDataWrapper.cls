/**
 * <AUTHOR>
 * @date 2024-07-10
 * @description data structure for invoice export
 */
public with sharing class CCM_InvoiceExportDataWrapper {
    
    public static List<InvoiceDataWrapper> convertToInvoiceExportDataFormat(CCM_Community_InvoiceFilterPageCtl.ReturnData data) {
        List<InvoiceDataWrapper> exportDataWrappers = new List<InvoiceDataWrapper>();
        List<CCM_Community_InvoiceFilterPageCtl.DataTableWrapper> invoiceList = data.currentData;
        for(CCM_Community_InvoiceFilterPageCtl.DataTableWrapper invoiceData : invoiceList) {
            InvoiceDataWrapper exportInvoice = new InvoiceDataWrapper(invoiceData.orderDate, invoiceData.poNumber, invoiceData.invoiceNumber, invoiceData.invoiceDate, invoiceData.originalAmt, invoiceData.dueRemainingAmt, invoiceData.invoiceStatus, invoiceData.paypalStatus, invoiceData.dueDate, invoiceData.isOverdue, invoiceData.overdueDays, invoiceData.trackNo);
            exportDataWrappers.add(exportInvoice);
            for(CCM_Community_InvoiceFilterPageCtl.AccountingInfo accountingBalanceData : invoiceData.accountingList) {
                InvoiceDataWrapper exportAccountingBalance = new InvoiceDataWrapper('', '', accountingBalanceData.invoiceNumber, '', accountingBalanceData.invoiceAmount, accountingBalanceData.dueRemainingAmount, '', accountingBalanceData.paypalStatus, accountingBalanceData.dueDate, accountingBalanceData.isOverdue, accountingBalanceData.overdueDays, '');
                exportDataWrappers.add(exportAccountingBalance);
            }
        }
        return exportDataWrappers;
    }
    
    public class InvoiceDataWrapper {
        public String Order_Date{get;set;}
        public String Customer_PO_Number{get;set;}
        public String Invoice_Number{get;set;}
        public String Invoice_Created_Date{get;set;}
        public String Invoice_Amount{get;set;}
        public String Outstanding_Amount{get;set;}
        public String Invoice_Status{get;set;}
        public String Payment_Status{get;set;}
        public String Due_Date{get;set;}
        public String Overdue {get;set;}
        public String Overdue_Days{get;set;}
        public String Track_Number{get;set;}

        public InvoiceDataWrapper(String orderDate, String poNumber, String invoiceNumber, 
                                  String invoiceDate, Decimal originalAmt, Decimal dueRemainingAmt, 
                                  String invoiceStatus, String paypalStatus, String dueDate, String isOverdue, String overdueDays,
                                  String trackNo) {
            this.Order_Date = orderDate;
            this.Customer_PO_Number = poNumber;
            this.Invoice_Number = invoiceNumber;
            this.Invoice_Created_Date = invoiceDate;
            this.Invoice_Amount = String.valueOf(originalAmt);
            this.Outstanding_Amount = String.valueOf(dueRemainingAmt);
            this.Invoice_Status = invoiceStatus;
            this.Payment_Status = paypalStatus;
            this.Due_Date = dueDate;
            this.Overdue = isOverdue;
            this.Overdue_Days = overdueDays;
            this.Track_Number = trackNo;
        }
    }
    
    public static List<DebitMemoDataWrapper> convertToDebitMemoExportDataFormat(List<CCM_Community_MyAccountCtl.AmountBalanceData> datas) {
        List<DebitMemoDataWrapper> exportDataWrappers = new List<DebitMemoDataWrapper>();
        for(CCM_Community_MyAccountCtl.AmountBalanceData data : datas) {
            DebitMemoDataWrapper exportDebitMemo = new DebitMemoDataWrapper(data.invoiceNumber, data.poNumber, data.orderNumber, data.invoiceDate, data.dueDate, data.originalAmountStr, data.paidAmountStr, data.dueRemainingAmountStr, data.isOverdue, data.overdueDays);
            exportDataWrappers.add(exportDebitMemo);
        }
        return exportDataWrappers;
    }

    public class DebitMemoDataWrapper {
        public String Memo_Number{get;set;}
        public String Customer_PO_Number{get;set;}
        public String Order_Number{get;set;}
        public String Invoice_Created_Date{get;set;}
        public String Due_Date{get;set;}
        public String Invoice_Amount{get;set;}
        public String Paid_Amount{get;set;}
        public String Outstanding_Amount{get;set;}
        public String Overdue{get;set;}
        public String Overdue_Days{get;set;}

        public DebitMemoDataWrapper(String invoiceNumber, String poNumber, String orderNumber, String invoiceDate, String dueDate, String originalAmountStr,
                                    String paidAmountStr, String dueRemainingAmountStr, String isOverdue, String overdueDays) {
            this.Memo_Number = invoiceNumber;
            this.Customer_PO_Number = poNumber;
            this.Order_Number = orderNumber;
            this.Invoice_Created_Date = invoiceDate;
            this.Due_Date = dueDate;
            this.Invoice_Amount = originalAmountStr;
            this.Paid_Amount = paidAmountStr;
            this.Outstanding_Amount = dueRemainingAmountStr;
            this.Overdue = isOverdue;
            this.Overdue_Days = overdueDays;
        }
    }

    public static List<CreditMemoDataWrapper> convertToCreditMemoExportDataFormat(List<CCM_Community_MyAccountCtl.AmountBalanceData> datas) {
        List<CreditMemoDataWrapper> exportDataWrappers = new List<CreditMemoDataWrapper>();
        for(CCM_Community_MyAccountCtl.AmountBalanceData data : datas) {
            CreditMemoDataWrapper exportCreditMemo = new CreditMemoDataWrapper(data.invoiceNumber, data.poNumber, data.invoiceDate, data.paidAmountStr, data.dueRemainingAmountStr);
            exportDataWrappers.add(exportCreditMemo);
        }
        return exportDataWrappers;
    }

    public class CreditMemoDataWrapper {
        public String Invoice_Number{get;set;}
        public String Customer_PO_Number{get;set;}
        public String Invoice_Date{get;set;}
        public String Due_Amount{get;set;}
        public String Remaining_Amount{get;set;}

        public CreditMemoDataWrapper(String invoiceNumber, String poNumber, String invoiceDate, String paidAmountStr, String dueRemainingAmountStr) {
            this.Invoice_Number = invoiceNumber;
            this.Customer_PO_Number = poNumber;
            this.Invoice_Date = invoiceDate;
            this.Due_Amount = paidAmountStr;
            this.Remaining_Amount = dueRemainingAmountStr;
        }
    }

    public static List<TransactionHistoryDataWrapper> convertToTransactionHistoryExportDataFormat(List<CCM_Community_MyAccountCtl.PaymentInfo> datas) {
        List<TransactionHistoryDataWrapper> exportDataWarppers = new List<TransactionHistoryDataWrapper>();
        for(CCM_Community_MyAccountCtl.PaymentInfo paymentInfo : datas) {
            // history
            TransactionHistoryDataWrapper dataWrapper = new TransactionHistoryDataWrapper();
            dataWrapper.Payment_ID = paymentInfo.id;
            dataWrapper.Paid_By = paymentInfo.createdById;
            dataWrapper.Payment_Status = paymentInfo.paypalStatus;
            dataWrapper.Transaction_Status = paymentInfo.transactionStatus;
            dataWrapper.Transaction_Date = paymentInfo.transactionDate;
            dataWrapper.Payment_Method = paymentInfo.paymentMethod;
            dataWrapper.Card_Last_Four_Digits = paymentInfo.cardNumber;
            dataWrapper.Payment_Amount = paymentInfo.paidAmout != null ? paymentInfo.paidAmout.toString() : '';
            dataWrapper.Remark = paymentInfo.remark;
            dataWrapper.Total_Amount_Paid = paymentInfo.paidAmout != null ? paymentInfo.paidAmout.toString() : '';
            exportDataWarppers.add(dataWrapper);
            // detail
            List<TransactionHistoryDataWrapper> exportDetailDataWarppers = new List<TransactionHistoryDataWrapper>();
            if(paymentInfo.invoiceAndCreditList != null) {
                if(!paymentInfo.invoiceAndCreditList.invoiceList.isEmpty()) {
                    for(CCM_Community_MyAccountCtl.InvoiceInfo invoice : paymentInfo.invoiceAndCreditList.invoiceList) {
                        TransactionHistoryDataWrapper detailData = new TransactionHistoryDataWrapper();
                        if(invoice.hasNoSequence) {
                            detailData.Payment_ID = '';
                            detailData.Paid_By = '';
                            detailData.Payment_Status = '';
                            detailData.Transaction_Status = '';
                            detailData.Transaction_Date = '';
                            detailData.Payment_Method = '';
                            detailData.Card_Last_Four_Digits = '';
                            detailData.Payment_Amount = '';
                            detailData.Remark = '';
                            detailData.Total_Amount_Paid = '';
                            detailData.Invoice_Credit_Memo = invoice.invoiceNumber;
                            detailData.Invoice_Credit_Memo_Amount = invoice.originalAmt != null ? invoice.originalAmt.toString() : '';
                            detailData.Cash_Discount_for_Early_Payment = invoice.discountFee != null ? invoice.discountFee.toString() : '';
                            detailData.Total_Net_Payment = invoice.totalPaymentFee != null ? invoice.totalPaymentFee.toString() : '';
                            exportDetailDataWarppers.add(detailData);
                        }
                        else {
                            for(CCM_Community_MyAccountCtl.AccountingInfo accountingInfo : invoice.accountingList) {
                                detailData = new TransactionHistoryDataWrapper();
                                detailData.Payment_ID = '';
                                detailData.Paid_By = '';
                                detailData.Payment_Status = '';
                                detailData.Transaction_Status = '';
                                detailData.Transaction_Date = '';
                                detailData.Payment_Method = '';
                                detailData.Card_Last_Four_Digits = '';
                                detailData.Payment_Amount = '';
                                detailData.Remark = '';
                                detailData.Total_Amount_Paid = '';
                                detailData.Invoice_Credit_Memo = invoice.invoiceNumber;
                                detailData.Invoice_Credit_Memo_Amount = accountingInfo.invoiceAmount != null ? accountingInfo.invoiceAmount.toString() : '';
                                detailData.Cash_Discount_for_Early_Payment = accountingInfo.discountFee != null ? accountingInfo.discountFee.toString() : '';
                                detailData.Total_Net_Payment = accountingInfo.totalPaymentFee != null ? accountingInfo.totalPaymentFee.toString() : '';
                                exportDetailDataWarppers.add(detailData);
                            }
                        }
                    }
                }

                if(!paymentInfo.invoiceAndCreditList.creditList.isEmpty()) {
                    for(CCM_Community_MyAccountCtl.InvoiceInfo credit : paymentInfo.invoiceAndCreditList.creditList) {
                        TransactionHistoryDataWrapper detailData = new TransactionHistoryDataWrapper();
                        detailData.Payment_ID = '';
                        detailData.Paid_By = '';
                        detailData.Payment_Status = '';
                        detailData.Transaction_Status = '';
                        detailData.Transaction_Date = '';
                        detailData.Payment_Method = '';
                        detailData.Card_Last_Four_Digits = '';
                        detailData.Payment_Amount = '';
                        detailData.Remark = '';
                        detailData.Total_Amount_Paid = '';
                        detailData.Invoice_Credit_Memo = credit.invoiceNumber;
                        detailData.Invoice_Credit_Memo_Amount = credit.originalAmt != null ? credit.originalAmt.toString() : '';
                        detailData.Cash_Discount_for_Early_Payment = '';
                        detailData.Total_Net_Payment = '';
                        exportDetailDataWarppers.add(detailData);
                    }
                }
            }
            
            if(!exportDetailDataWarppers.isEmpty()) {
                dataWrapper.Invoice_Credit_Memo = exportDetailDataWarppers[0].Invoice_Credit_Memo;
                dataWrapper.Invoice_Credit_Memo_Amount = exportDetailDataWarppers[0].Invoice_Credit_Memo_Amount;
                dataWrapper.Cash_Discount_for_Early_Payment = exportDetailDataWarppers[0].Cash_Discount_for_Early_Payment;
                dataWrapper.Total_Net_Payment = exportDetailDataWarppers[0].Total_Net_Payment;

                exportDetailDataWarppers.remove(0);
                exportDataWarppers.addAll(exportDetailDataWarppers);
            }
        }
        return exportDataWarppers;
    }


    public class TransactionHistoryDataWrapper {
        public String Payment_ID{get;set;}
        public String Paid_By{get;set;}
        public String Payment_Status{get;set;}
        public String Transaction_Status{get;set;}
        public String Transaction_Date{get;set;}
        public String Payment_Method{get;set;}
        public String Card_Last_Four_Digits{get;set;}
        public String Payment_Amount{get;set;}
        public String Remark{get;set;}
        public String Total_Amount_Paid {get;set;}
        public String Invoice_Credit_Memo {get;set;}
        public String Invoice_Credit_Memo_Amount {get;set;}
        public String Cash_Discount_for_Early_Payment {get;set;}
        public String Total_Net_Payment {get;set;}
    }
}