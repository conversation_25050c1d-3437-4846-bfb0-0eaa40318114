<!--
 - Created by gluo006 on 10/17/2019.
 -->

<aura:component description="CCM_PartsOrder_Detail"  implements="forceCommunity:availableForAllPageTypes,flexipage:availableForAllPageTypes,force:hasRecordId" access="global" controller="CCM_PartsOrder_DetailCtl">
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="isDelegate" type="Boolean" default="false"/>
    <aura:attribute name="quotation" type="Object" default=""/>
    <aura:attribute name="customerId" type="String" default=""/>
    <aura:attribute name="currentStep" type="Integer" default="3"/>
    <aura:attribute name="columns" type="List" default="[]"/>
    <aura:attribute name="orderItemList" type="List" default="[]"/>
    <aura:attribute name="currencySymbol" type="String" default="$"/>
    <aura:attribute name="totalQuantity" type="Decimal" default="0"/>
    <aura:attribute name="totalAmount" type="Decimal" default="0"/>
    <aura:attribute name="paymentTermLabel" type="String" default=""/>
    <aura:attribute name="freightTermLabel" type='String' default=""/>
    <aura:attribute name="isCCA" type="Boolean" default="true"/>
    <aura:attribute name="showTax" type="Boolean" default="true" />
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <lightning:card class="mainContent slds-p-horizontal_x-large">
        <div class="c-container">
            <p>
                <strong>
                    <span>{!$Label.c.CCM_Portal_YourPurchaseOrder}:&nbsp;{!v.quotation.Customer_PO_Num__c}</span>
                    <!--<span>Submit Date: <ui:outputDate value="{!v.quotation.Submit_Date__c}" /></span>-->
                    <!--<span>Quotation Status: {!v.quotation.Status__c}</span>-->
                </strong>
            </p>
            <div class="slds-grid slds-p-around_medium topCon">
                <div class="header-column slds-size--1-of-2">
                    <div class="">
                        <p><strong>{!$Label.c.CCM_Portal_CustomerName}</strong></p>
                        <p>{!v.quotation.Customer__r.Name}</p>
                    </div>
                </div>
                <div class="header-column slds-size--1-of-2">
                    <div class="">
                        <p><strong>Chervon North American</strong></p>
                        <lightning:formattedAddress
                                street="1203 East Warrenville Road"
                                city="Naperville"
                                country="US"
                                province="IL"
                                postalCode="60563"/>
                        <p>{!v.quotation.Sales_Manager__r.Name}</p>
                    </div>
                </div>
            </div>
            <div class="slds-grid slds-p-around_medium topCon">
                <div class="header-column slds-size--1-of-2">
                    <div class="">
                        <p><strong>{!$Label.c.CCM_Portal_BillingTo}</strong></p>
                        <p>
                            <aura:if isTrue="{!and(v.quotation.Billing_Address__c != null)}">
                                <lightning:formattedAddress
                                        street="{!(v.quotation.Billing_Address__r.Address1__c + v.quotation.Billing_Address__r.Address2__c)}"
                                        city="{!v.quotation.Billing_Address__r.City__c}"
                                        country="{!v.quotation.Billing_Address__r.Country__c}"
                                        province="{!v.quotation.Billing_Address__r.State__c}"
                                        postalCode="{!v.quotation.Billing_Address__r.Postal_Code__c}"/>
                                <ul>
                                    <li> {!v.quotation.Billing_Address__r.Contact__r.Name}</li>
                                </ul>
                            </aura:if>
                        </p>
                    </div>
                </div>
                <div class="header-column slds-size--1-of-2">
                    <div class="">
                        <div>
                            <p><strong>{!$Label.c.CCM_Portal_PaymentTerm}</strong></p>
                            <p>{!v.paymentTermLabel}</p>
                        </div>
                        <div>
                            <p><strong>{!$Label.c.CCM_Portal_FreightTerm}</strong></p>
                            <p>{!v.freightTermLabel}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="slds-grid slds-p-around_medium topCon">
                <div class="header-column slds-size--1-of-2">
                    <p><strong>{!$Label.c.CCM_Portal_ShippingTo}</strong></p>
                    <p>
                        <aura:if isTrue="{!v.quotation.Is_Alternative_Address__c == false}">
                            <lightning:formattedAddress
                                    street="{!(v.quotation.Shipping_Address__r.Address1__c + v.quotation.Shipping_Address__r.Address2__c)}"
                                    city="{!v.quotation.Shipping_Address__r.City__c}"
                                    country="{!v.quotation.Shipping_Address__r.Country__c}"
                                    province="{!v.quotation.Shipping_Address__r.State__c}"
                                    postalCode="{!v.quotation.Shipping_Address__r.Postal_Code__c}"/>
                            <ul>
                                <li> {!v.quotation.Shipping_Address__r.Contact__r.Name}</li>
                            </ul>
                            <aura:set attribute="else">
                                <lightning:formattedAddress
                                        street="{!v.quotation.Additional_Shipping_Street__c}"
                                        city="{!v.quotation.Additional_Shipping_City__c}"
                                        country="{!v.quotation.Additional_Shipping_Country__c}"
                                        province="{!v.quotation.Additional_Shipping_Province__c}"
                                        postalCode="{!v.quotation.Additional_Shipping_Postal_Code__c}"
                                />
                                <ul>
                                    <li> {!v.quotation.Additional_Contact_Name__c}</li>
                                    <li>{!v.quotation.Additional_Contact_Phone__c}</li>
                                    <li>{!v.quotation.Additional_Contact_Email__c}</li>
                                </ul>
                            </aura:set>
                        </aura:if>
                    </p>
                </div>
                <div class="header-column slds-size--1-of-2">
                    <div>
                        <p><strong>{!$Label.c.CCM_Portal_ShippingBy}</strong></p>
                        <p>{!v.quotation.shippingBy}</p>
                    </div>
                    <aura:if isTrue="{!v.quotation.Shipping_By__c != 'Chervon'}">
                        <div>
                            <p><strong>{!$Label.c.CCM_Portal_PreferCarrier}</strong></p>
                            <p>{!v.quotation.Delivery_Supplier__c}</p>
                        </div>
                        <div>
                            <p><strong>{!$Label.c.CCM_Portal_YourCustomerFreightAccount}</strong></p>
                            <p>{!v.quotation.Customer_Freight_Account__c}</p>
                        </div>
                        <aura:set attribute="else">
                            <div>
                                <p><strong>{!$Label.c.CCM_Portal_ShippingMethod}</strong></p>
                                <p>{!v.quotation.Shipping_Method__c}</p>
                            </div>
                        </aura:set>
                    </aura:if>
                </div>
            </div>

            <div class="slds-grid slds-p-around_medium topCon">
                <div class="header-column slds-size--1-of-2">
                    <p><strong>{!$Label.c.CCM_Portal_EstimatedShipDate}</strong></p>
                    <p><lightning:formattedDateTime value="{!v.quotation.Expected_Delivery_Date__c}" /></p>
                </div>
                <div class="header-column slds-size--1-of-2">
                    <p><strong>{!$Label.c.CCM_Portal_BuyerEmail}</strong></p>
                    <p>{!v.quotation.Email__c}</p>
                </div>
            </div>

            <div class="slds-p-around_medium">
                <p><strong>{!$Label.c.CCM_Portal_ShippingandRoutingInstruction}</strong></p>
                <p>{!v.quotation.Notes__c}</p>
            </div>
            <table class="slds-p-horizontal_medium slds-m-bottom_medium"></table>
            <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_fixed-layout slds-table_resizable-cols slds-table_striped productTable" role="grid">
                <thead>
                <tr class="slds-line-height_reset">
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="width: 5%">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Line}">{!$Label.c.CCM_Portal_Line}</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth" scope="col">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Brand}">{!$Label.c.CCM_Portal_Brand}</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_PartsNumber}">{!$Label.c.CCM_Portal_PartsNumber}</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_PartsName}">{!$Label.c.CCM_Portal_PartsName}</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Quantity}">{!$Label.c.CCM_Portal_Quantity}</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_UnitPrice}">{!$Label.c.CCM_Portal_UnitPrice}</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Subtotal}">{!$Label.c.CCM_Portal_Subtotal}</span>
                            </div>
                        </a>
                    </th>
                </tr>
                </thead>
                <tbody>
                <aura:iteration items="{!v.orderItemList}" var="orderItem" indexVar="index">
                    <tr aria-selected="false" class="slds-hint-parent" id="{!row_index}">
                        <th scope="row">
                            <div class="slds-truncate" title="">
                                {!index + 1}
                            </div>
                        </th>
                        <td role="gridcell" title="Brand">
                            <div class="slds-truncate clear-user-agent-styles" >{!orderItem.Product__r.Brand_Name__c}</div>
                        </td>

                        <td role="gridcell" title="Qty">
                            <div class="slds-truncate clear-user-agent-styles" >
                                <span>{!orderItem.Product__r.ProductCode}</span>
                            </div>
                        </td>

                        <td role="gridcell" title="Qty UM">
                            <div class="slds-truncate clear-user-agent-styles" >
                                <span>{!orderItem.Product__r.Name}</span>
                            </div>
                        </td>

                        <td role="gridcell" title="Unit Price">
                            <div class="slds-truncate">
                                {!orderItem.Quantity__c}
                            </div>
                        </td>
                        <td role="gridcell">
                            <div class="slds-truncate">
                                <lightning:formattedNumber value="{!orderItem.Unit_Price__c}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/>
                            </div>
                        </td>
                        <td role="gridcell">
                            <div class="slds-truncate">
                                <lightning:formattedNumber value="{!orderItem.Sub_Total__c}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/>
                            </div>
                        </td>
                    </tr>
                </aura:iteration>
                <tr aria-selected="false" class="slds-hint-parent" id="{!row_index}" >
                    <th class="slds-p-right_x-small totalCon" scope="row" colspan="7" align="right">
                        <div class="slds-grid slds-float--right">
                            <div class="slds-text-align--right">
                                <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_TotalQuantity}:</div>
                                <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_ProductAmount}:</div>
                                <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_FreightFee}:</div>
                                <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_FreightFeeToBeWaived}:</div>
                                <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_Discount}:</div>
                                <aura:if isTrue="{!v.isCCA}">
                                    <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_QST}:</div>
                                    <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_GST}:</div>
                                    <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_HST}:</div>
                                </aura:if>
                                <div class="slds-border_bottom ccm_paddingTop" />
                                <div class="slds-truncate ccm_padding ccm_paddingTop" title=""><strong>{!$Label.c.CCM_Portal_TotalAmount}:</strong></div>
                            </div>
                            <div>
                                <div class="slds-truncate" title=""><strong>{!v.quotation.Total_Quantity__c}</strong></div>
                                <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.Product_Price__c}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.Freight_Fee__c}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                <div class="slds-truncate ccm_fontColor" title=""><strong>-{!v.currencySymbol}&nbsp;{!v.quotation.Freight_Fee_Waived__c}</strong></div>
                                <div class="slds-truncate ccm_fontColor" title=""><strong>-{!v.currencySymbol}&nbsp;{!v.quotation.Discount__c}</strong></div>
                                <aura:if isTrue="{!and(v.isCCA, v.showTax)}">
                                    <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.QST__c}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                    <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.GST__c}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                    <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.HST__c}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                </aura:if>
                                <div class="slds-border_bottom ccm_paddingTop" />
                                <div class="slds-truncate ccm_paddingTop" title=""><strong><lightning:formattedNumber value="{!v.quotation.Total_Amount__c}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/> </strong></div>
                            </div>
                        </div>
                    </th>
                </tr>
                </tbody>
            </table>

        </div>

        <aura:set attribute="footer">
            <aura:if isTrue="{!v.quotation.Status__c == 'Draft'}">
                <div>
                    <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" label="{!$Label.c.CCM_Portal_Previous}" title="{!$Label.c.CCM_Portal_Previous}" onclick="{!c.previousStep}" />
                    <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" label="{!$Label.c.CCM_Portal_Cancel}" title="{!$Label.c.CCM_Portal_Cancel}" onclick="{!c.backHome}" />
                    <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" label="{!$Label.c.CCM_Portal_Submit}" title="{!$Label.c.CCM_Portal_Submit}" variant="brand" onclick="{!c.doSubmit}" />
                </div>
            </aura:if>

            <aura:if isTrue="{!v.quotation.Status__c != 'Draft'}">
                <div>
                    <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" label="{!$Label.c.CCM_Portal_Back}" title="{!$Label.c.CCM_Portal_Back}" onclick="{!c.backHome}" />
                </div>
            </aura:if>
        </aura:set>
    </lightning:card>
</aura:component>