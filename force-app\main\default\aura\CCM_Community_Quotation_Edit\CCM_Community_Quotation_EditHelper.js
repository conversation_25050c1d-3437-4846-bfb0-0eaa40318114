({
    getUrlParameter : function(sParam) {
        
        var sPageURL = decodeURIComponent(window.location.search.substring(1)),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;
            
        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');
            if (sParameterName[0] === sParam || sParameterName[0] === ('0.' + sParam)) {
                return sParameterName[1] === undefined ? true : sParameterName[1];
            }
        }
    },
    
    saveData: function(component, currentStep){
        var action = component.get("c.saveData");
        action.setParam('poString', JSON.stringify(component.get('v.po')));
        action.setParam('poItemString', JSON.stringify(component.get('v.orderItemList')));
        action.setParam('currentStep', currentStep);
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if(results){
                    component.set('v.po', results.po);
                    component.set('v.orderItemList', results.poItems);
                    if(results.currentStep){
                        component.set("v.currentStep", results.currentStep);
                    }

                    component.set('v.recordId', results.po.Id);
                }
            } else {
                var errors = response.getError();
            }
        });
        $A.enqueueAction(action);
    },
    getPaymentFreightRule: function(component, event, helper){
        var action = component.get("c.getPaymentFreightTerm");
        action.setParam('customerId', component.get('v.customerId'));
        action.setParam('brandName', component.get('v.brandScope'));
        action.setParam('recordId', component.get('v.recordId'));
        action.setCallback(this, function(response){
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                if(result){
                    component.set('v.paymentTerm', result.paymentTerm);
                    component.set('v.freightTerm', result.freightTerm);
                    component.set('v.freightTermLabel', result.freightTermLabel);
                    component.set('v.paymentTermLabel', result.paymentTermLabel);
                    component.set('v.freightTermRuleFee', result.freightTermRuleFee);

                    var purchaseOrder = component.get('v.po');
                    purchaseOrder.Payment_Term__c = component.get('v.paymentTermValue');
                    purchaseOrder.Freight_Term__c = component.get('v.freightTerm');
                    component.set('v.po', purchaseOrder);
                }
            }else {
                console.log(response.getError());
            }
        });
        $A.enqueueAction(action);
    },
    clearWholeOrderPromotion: function(component){
        component.set('v.wholeOrderPromo', '');
        component.set("v.termsPromo", '');
    }
})