({
    getUrlParameter : function(sParam) {
        var sPageURL = decodeURIComponent(window.location.search.substring(1)),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');
            if (sParameterName[0] === sParam || sParameterName[0] === ('0.' + sParam)) {
                return sParameterName[1] === undefined ? true : sParameterName[1];
            }
        }
    },
    showToast : function(title, message, type){
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams({
            "title": title,
            "message": message,
            "type" : type
        });
        toastEvent.fire();
    },
    currencyFormat: function(number){
        var locale = $A.get("$Locale.lang");
        if (typeof number !== 'number') {
            number = Number(number)
        }
        return number.toLocaleString(locale, {minimumFractionDigits: 2});
    },

    calculateFreightFeeHandler : function(component, event, helper){
        component.set('v.isBusy', true);
        if (component.get('v.isEdit') == true){
            var zeroNum = 0.00;
            var action = component.get("c.calculateFreightFee");
            action.setParam('orderInfo', JSON.stringify(component.get('v.quotation')));
            action.setParam('orderItemsInfo', JSON.stringify(component.get('v.orderItemList')));
            action.setCallback(this, function (response) {
                var state = response.getState();
                if (state === "SUCCESS") {
                    var results = response.getReturnValue();
                    if(results){
                        component.set('v.quotation.freightFee', Number(results).toFixed(2));
                        component.set('v.freightFeeVal', this.currencyFormat(component.get('v.quotation.freightFee')));
                        var quotation = component.get('v.quotation');
                        var targetFee = 0.00;
                        if (quotation.freightTargetFee != null && quotation.freightTargetFee != undefined){
                            targetFee = quotation.freightTargetFee;
                        }
                        var productPrice = 0.00;
                        if (quotation.productPrice != null && quotation.productPrice != undefined){
                            productPrice = quotation.productPrice 
                        }
                        if (quotation.shippingBy == 'Chervon'){
                            if (productPrice >= targetFee){
                                component.set('v.quotation.freightFeeWaived', Number(results).toFixed(2));
                            }else{
                                component.set('v.quotation.freightFeeWaived', Number(zeroNum).toFixed(2));
                            }
                            component.set('v.freightFeeToBeWaivedVal', this.currencyFormat(component.get('v.quotation.freightFeeWaived')));
                        }
                    }else{
                        component.set('v.quotation.freightFee', Number(zeroNum).toFixed(2));
                    }
                } else {
                    var errors = response.getError();
                }
                component.set('v.isBusy', false);
            });
            $A.enqueueAction(action);
        }
    },
    isPoolFreeGoods: function(ruleItem){
        var offeringList = ruleItem.offeringList;
        var hasPool = false;
        offeringList.forEach(function(oItem){
            if(oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice'){
                hasPool = true;
            }
        });
        return hasPool;
    },
    isMeetThreshold: function(quotationItemList, index, promotion){
        var isMeet = false;
        var times = this.getThresholdTimes(quotationItemList, index, promotion);
        if(times >= 1){
            isMeet = true;
        }
        return isMeet;
    },
    getThresholdTimes: function(quotationItemList, index, promotion){
        var times = -1;
        var currentQuotation = quotationItemList[index];
        var currentProductCode = currentQuotation.productCode;
        var currentRuleName = currentQuotation.ruleName;
        var currentQty = currentQuotation.quantity;
        var currentAmt = currentQuotation.listPrice * currentQuotation.quantity;

        if(promotion.promotion.Promotion_Type__c == 'BOGO'){
            //only have one rule
            var thresholdList = promotion.ruleList[0].thresholdList;
            thresholdList.forEach(function(tItem){
                if(tItem.threshold.RecordType.DeveloperName == 'By_Quantity_of_Specific_Product'
                  && tItem.products[0].Product__r.ProductCode == currentProductCode){
                    var minimumQty = tItem.products[0].Minimum_Quantity__c;
                    times = Math.floor(currentQty/minimumQty);
                }
            });
        }else if(promotion.promotion.Promotion_Type__c == 'Price Discount'){
            //only have one rule
            var thresholdList = promotion.ruleList[0].thresholdList;
            thresholdList.forEach(function(tItem){
                if(tItem.threshold.RecordType.DeveloperName == 'By_Quantity_of_Specific_Product'){
                    var minimumQty = tItem.products[0].Minimum_Quantity__c;
                    //times = Math.floor(currentQty/minimumQty);
                    var productCode = tItem.products[0].Product__r.ProductCode;
                    quotationItemList.forEach(function(qItem){
                        if(qItem.productCode == productCode 
                            && qItem.ruleName == currentRuleName 
                            && (qItem.isThreshold || qItem.HasPromo)){
                            var qty = Number(qItem.quantity);
                            var currentTimes = Math.floor(qty/minimumQty);
                            if(times == -1){
                                times = currentTimes;
                            }else if(times > currentTimes){
                                times = currentTimes;
                            }
                        }
                    });           
                }else if(tItem.threshold.RecordType.DeveloperName == 'By_Amount_of_Specific_Product'){
                    var minimumAmt = tItem.products[0].Minimum_Amount__c;
                    //times = Math.floor(currentAmt/minimumAmt);
                    var productCode = tItem.products[0].Product__r.ProductCode;
                    quotationItemList.forEach(function(qItem){
                        if(qItem.productCode == productCode 
                            && qItem.ruleName == currentRuleName 
                            && (qItem.isThreshold || qItem.HasPromo)){
                            var amt = Number(qItem.listPrice) * Number(qItem.quantity);
                            var currentTimes = Math.floor(amt/minimumAmt);
                            if(times == -1){
                                times = currentTimes;
                            }else if(times > currentTimes){
                                times = currentTimes;
                            }
                        }
                    }); 
                }
            });
        }else if(promotion.promotion.Promotion_Type__c == 'Mix & Match'){
            //only have one rule
            var thresholdList = promotion.ruleList[0].thresholdList;
            thresholdList.forEach(function(tItem){
                var products = tItem.products;
                if(tItem.threshold.RecordType.DeveloperName == 'By_Mix_Match'){
                    var miniTotalQty = tItem.threshold.Minimum_Total_Quantity__c;
                    var miniTotalAmount = tItem.threshold.Minimum_Total_Amount__c;
                    var miniDiffModels = tItem.threshold.Minimum_Different_Tool_Models__c;
                    var selectedDiffModels = 0;
                    var selectedTotalQty = 0;
                    var selectedTotalAmount = 0.00;
                    products.forEach(function(pItem){
                        var productCode = pItem.Product__r.ProductCode;
                        var miniQty = pItem.Number__c;
                        if(pItem.selected){
                            selectedDiffModels++;
                            quotationItemList.forEach(function(qItem){
                                if(qItem.productCode == productCode 
                                    && qItem.ruleName == currentRuleName 
                                    && (qItem.isThreshold || qItem.HasPromo)){
                                    selectedTotalAmount+= Number(qItem.listPrice) * Number(qItem.quantity);
                                    selectedTotalQty+= Number(qItem.quantity);
                                    var currentTimes = Number(qItem.quantity)/Number(miniQty);
                                    if(times == -1){
                                        times = currentTimes;
                                    }else if(times > currentTimes){
                                        times = currentTimes;
                                    }
                                }
                            });
                        }                        
                    });
                    if(selectedDiffModels < miniDiffModels){
                        times = 0;
                    }else if(miniTotalAmount > 0){
                        if(miniTotalAmount > selectedTotalAmount){
                            times = 0;
                        }else{
                            var totalAmountTimes = selectedTotalAmount/miniTotalAmount;
                            if(times > totalAmountTimes){
                                times = totalAmountTimes;
                            }
                        }
                    }else if(miniTotalQty > 0){
                        if(miniTotalQty > selectedTotalQty){
                            times = 0;
                        }else{
                            var totalQtyTimes = selectedTotalQty/miniTotalQty;
                            if(times > totalQtyTimes){
                                times = totalQtyTimes;
                            }
                        }
                    }
                }                
            });
            if(times != 0){
                times = Math.floor(times);
            }
        }else if(promotion.promotion.Promotion_Type__c == 'Price Break'){
            //have more than one rule
            var availableRule = this.getAvailableRuleInPriceBreak(quotationItemList, index, promotion);
            if(availableRule){
                times = 1;
            }
        }else if(promotion.promotion.Promotion_Type__c == 'Full Pallet Promo'){
            //only have one rule
            var thresholdList = promotion.ruleList[0].thresholdList;
            var hasFreeGoods = this.hasFreeGoods(promotion.ruleList[0]);
            if(hasFreeGoods){
                thresholdList.forEach(function(tItem){
                    if(tItem.threshold.RecordType.DeveloperName == 'By_Full_Pallet_Quantity_of_Specific_Product'
                      && tItem.products[0].Product__r.ProductCode == currentProductCode){
                        var minimumQty = tItem.products[0].Minimum_Quantity__c;
                        times = Math.floor(currentQty/minimumQty);
                    }
                });
            }else{
                thresholdList.forEach(function(tItem){
                    if(tItem.threshold.RecordType.DeveloperName == 'By_Full_Pallet_Quantity_of_Specific_Product'){
                        var minimumQty = tItem.products[0].Minimum_Quantity__c;
                        //times = Math.floor(currentQty/minimumQty);                    
                        var productCode = tItem.products[0].Product__r.ProductCode;
                        quotationItemList.forEach(function(qItem){
                            if(qItem.productCode == productCode 
                                && qItem.ruleName == currentRuleName 
                                && (qItem.isThreshold || qItem.HasPromo)){
                                var qty = Number(qItem.quantity);
                                var currentTimes = Math.floor(qty/minimumQty);
                                if(times == -1){
                                    times = currentTimes;
                                }else if(times > currentTimes){
                                    times = currentTimes;
                                }
                            }
                        });
                    }
                });
            }            
        }else if(promotion.promotion.Promotion_Type__c == 'Others'){
            //have more than one rule
            var thresholdList = promotion.ruleList[0].thresholdList;
            thresholdList.forEach(function(tItem){
                if(tItem.threshold.RecordType.DeveloperName == 'By_Quantity_of_Specific_Product'){
                    quotationItemList.forEach(function(qItem){
                        if(qItem.productCode == tItem.products[0].Product__r.ProductCode 
                            && qItem.HasPromo
                            && qItem.Is_Initial__c){
                            var minimumQty = tItem.products[0].Minimum_Quantity__c;
                            if(times == -1){
                                times = qItem.quantity/minimumQty;
                            }else if(times > qItem.quantity/minimumQty){
                                times = qItem.quantity/minimumQty;
                            }
                        }
                    });                                        
                }else if(tItem.threshold.RecordType.DeveloperName == 'By_Amount_of_Specific_Product'){  
                    quotationItemList.forEach(function(qItem){
                        if(qItem.productCode == tItem.products[0].Product__r.ProductCode 
                            && qItem.HasPromo
                            && qItem.Is_Initial__c){
                            var minimumAmt = tItem.products[0].Minimum_Amount__c;
                            var qAmt = qItem.listPrice * qItem.quantity;
                            if(times == -1){
                                times = qAmt/minimumAmt; 
                            }else if(times > qAmt/minimumAmt){
                                times = qAmt/minimumAmt;
                            }
                        }
                    }); 
                }else if(tItem.threshold.RecordType.DeveloperName == 'By_Full_Pallet_Quantity_of_Specific_Product'){
                    quotationItemList.forEach(function(qItem){
                        if(qItem.productCode == tItem.products[0].Product__r.ProductCode 
                            && qItem.HasPromo
                            && qItem.Is_Initial__c){
                            var minimumQty = tItem.products[0].Minimum_Quantity__c;
                            if(times == -1){
                                times = qItem.quantity/minimumQty;
                            }else if(times > qItem.quantity/minimumQty){
                                times = qItem.quantity/minimumQty;
                            }
                        }
                    });  
                }else if(tItem.threshold.RecordType.DeveloperName == 'By_Mix_Match'){
                    var products = tItem.products;
                    var miniTotalQty = tItem.threshold.Minimum_Total_Quantity__c;
                    var miniTotalAmount = tItem.threshold.Minimum_Total_Amount__c;
                    var miniDiffModels = tItem.threshold.Minimum_Different_Tool_Models__c;
                    var selectedDiffModels = 0;
                    var selectedTotalQty = 0;
                    var selectedTotalAmount = 0.00;
                    products.forEach(function(pItem){
                        var productCode = pItem.Product__r.ProductCode;
                        var miniQty = pItem.Number__c;
                        if(pItem.selected){
                            selectedDiffModels++;
                            quotationItemList.forEach(function(qItem){
                                if(qItem.productCode == productCode 
                                    && qItem.ruleName == currentRuleName
                                    && (qItem.isThreshold || qItem.HasPromo)){
                                    selectedTotalAmount+= Number(qItem.listPrice) * Number(qItem.quantity);
                                    selectedTotalQty+= Number(qItem.quantity);
                                    var currentTimes = Number(qItem.quantity)/Number(miniQty);
                                    if(times == -1){
                                        times = currentTimes;
                                    }else if(times > currentTimes){
                                        times = currentTimes;
                                    }
                                }
                            });
                        }                        
                    });
                    if(selectedDiffModels < miniDiffModels){
                        times = 0;
                    }else if(miniTotalAmount > 0){
                        if(miniTotalAmount > selectedTotalAmount){
                            times = 0;
                        }else{
                            var totalAmountTimes = selectedTotalAmount/miniTotalAmount;
                            if(times > totalAmountTimes){
                                times = totalAmountTimes;
                            }
                        }
                    }else if(miniTotalQty > 0){
                        if(miniTotalQty > selectedTotalQty){
                            times = 0;
                        }else{
                            var totalQtyTimes = selectedTotalQty/miniTotalQty;
                            if(times > totalQtyTimes){
                                times = totalQtyTimes;
                            }
                        }
                    }
                }
            });
            if(times != 0){
                times = Math.floor(times);
            }
        }
        return times;
    },
    getAvailableRuleInPriceBreak: function(quotationItemList, index,selectedPromotion){
        var availableRule = null;
        var mainThresholdIndex = 0;        
        if(quotationItemList[index].HasPromo){
            mainThresholdIndex = index;
        }else{
            var ruleName = quotationItemList[index].ruleName;
            var i;
            for(i = 0; i<quotationItemList.length; i++){
                if(quotationItemList[i].ruleName == ruleName && quotationItemList[i].HasPromo){
                    mainThresholdIndex = i;
                    break;
                }
            }
        } 
        var currentQuotation = quotationItemList[mainThresholdIndex];
        var currentProductCode = currentQuotation.productCode;
        var currentQty = currentQuotation.quantity;
        var currentAmt = currentQuotation.listPrice * currentQuotation.quantity;
        var ruleList = selectedPromotion.ruleList;
        ruleList.forEach(function(rItem){
            var thresholdList = rItem.thresholdList;
            thresholdList.forEach(function(tItem){
                if(tItem.threshold.RecordType.DeveloperName == 'By_Quantity_of_Specific_Product'
                  && tItem.products[0].Product__r.ProductCode == currentProductCode){
                    var minimumQty = tItem.products[0].Minimum_Quantity__c;
                    var maximumQty = tItem.products[0].Maximum_Quantity__c;
                    if(currentQty >= minimumQty && currentQty < maximumQty){
                        availableRule = rItem;
                    }
                }else if(tItem.threshold.RecordType.DeveloperName == 'By_Amount_of_Specific_Product'
                        && tItem.products[0].Product__r.ProductCode == currentProductCode){
                    var minimumAmt = tItem.products[0].Minimum_Amount__c;
                    var maximumQty = tItem.products[0].Maximum_Amount__c;
                    if(currentAmt >= minimumAmt && currentAmt < maximumQty){
                        availableRule = rItem;
                    }
                }
            });
        });
        return availableRule;
    },
    checkMeetOfferingPoolLimit: function(quotationItemList, index, promotion){
        var offeringList = promotion.ruleList[0].offeringList;
        var ruleName = quotationItemList[index].ruleName;
        var mainThresholdIndex = 0;
        var i;
        for(i = 0; i<quotationItemList.length; i++){
            if(quotationItemList[i].ruleName == ruleName && quotationItemList[i].HasPromo){
                mainThresholdIndex = i;
                break;
            }
        }
        var times = this.getThresholdTimes(quotationItemList, mainThresholdIndex, promotion);        
        if(promotion.promotion.Promotion_Type__c == 'Price Break'){
            var availableRule = this.getAvailableRuleInPriceBreak(quotationItemList, mainThresholdIndex, promotion);
            if(availableRule){
                offeringList = availableRule.offeringList;
            }
        }        
        
        var j;
        for(j = 0; j < offeringList.length; j++){
            var oItem = offeringList[j];
            if(oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice'){
                var maxGiftQty = oItem.offering.Gift_Total_Quantity__c * times;
                var totalGiftQty = 0;
                var products = oItem.products;
                var giftQuotationList = [];
                var isExceed = false;
                products.forEach(function(pItem){
                    if(pItem.selected){
                        quotationItemList.forEach(function(qItem){
                            if(pItem.Product__r.ProductCode == qItem.productCode 
                                && qItem.ruleName == ruleName 
                                && qItem.isOffering){
                                totalGiftQty+= Number(qItem.quantity);
                                giftQuotationList.push(qItem);
                                if(qItem.quantity > pItem.Number__c*times){
                                    isExceed = true;
                                    qItem.isExceed = true;
                                    qItem.isLess = false;
                                }else{
                                    qItem.isExceed = false;
                                    if(qItem.quantity < pItem.Number__c*times){
                                        qItem.isLess = true;
                                    }else{
                                        qItem.isLess = false;
                                    }
                                }
                            }
                        });
                    }                
                });
                if(totalGiftQty > maxGiftQty){
                    isExceed = true;
                    giftQuotationList.forEach(function(gItem){
                        gItem.isExceed = true;
                        gItem.isLess = false;
                    });
                }
                
                if(totalGiftQty < maxGiftQty){
                    giftQuotationList.forEach(function(gItem){
                        if(gItem.isExceed){
                            gItem.isLess = false;
                        }
                    });
                }

                if(totalGiftQty == maxGiftQty){
                    giftQuotationList.forEach(function(gItem){
                        gItem.isLess = false;
                    });
                }
            }
        }        
    },
    isAllMeetThreshold: function(component){
        var isAllMeetThreshold = true;
        var quotationItemList = component.get('v.orderItemList');
        quotationItemList.forEach(function(qItem){
            if(qItem.Promotion 
                && (qItem.HasPromo || qItem.isThreshold)
                && (qItem.isMix &&!qItem.isMeet)){
                isAllMeetThreshold = false;
            }else if(qItem.Promotion 
                && qItem.isOffering 
                && (qItem.isPool && qItem.isExceed)){
                isAllMeetThreshold = false;
            }
        });
        return isAllMeetThreshold;
    },
    checkMixMatchOrder: function(component){
        var quotationItemList = component.get('v.orderItemList');
        for(var i = 0; i < quotationItemList.length; i++){
            var currentQuotation = quotationItemList[i];
            if(currentQuotation.Is_Initial__c){
                var promotion = currentQuotation.Promotion;
                if(promotion && this.hasMixMatch(promotion)){
                    var ruleName = currentQuotation.ruleName;
                    var isMeetThreshold = this.isMeetThreshold(quotationItemList, i, promotion);
                    if(!isMeetThreshold){
                        quotationItemList.forEach(function(qItem){
                            if(qItem.ruleName == ruleName
                                && qItem.isMix){
                                qItem.isMeet = false;
                            }
                        });
                    }
                }

                if(promotion){
                    var ruleItem = promotion.ruleList[0];
                    if(promotion.promotion.Promotion_Type__c == 'Price Break'){
                        var availableRuleItem = this.getAvailableRuleInPriceBreak(quotationItemList, i, promotion);
                        if(availableRuleItem){
                            ruleItem = availableRuleItem;
                        }
                    }
                    
                    if(promotion && this.isPoolFreeGoods(ruleItem)){
                        this.checkMeetOfferingPoolLimit(quotationItemList, i, promotion);                   
                    }
                }                
            }
        }
        component.set('v.orderItemList', quotationItemList);
    },
    checkOrderQty: function(component){
        var quotationItemList = component.get('v.orderItemList');
        if(quotationItemList && quotationItemList.length > 0){
            var hasEmptyQty = false;
            var hasNoProduct = false;
            quotationItemList.forEach(function(qItem){
                if(!qItem.quantity || qItem.quantity == 0){
                    hasEmptyQty = true;
                }
                if(!qItem.productId){
                    hasNoProduct = true;
                }
            });
            if(hasEmptyQty){
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": "Warning!",
                    "message": "Qty must be larger than 0.",
                    "type": "Warning"
                });
                toastEvent.fire();
                return false;
            }
            if(hasNoProduct){
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": "Warning!",
                    "message": "Please select a product in each line.",
                    "type": "Warning"
                });
                toastEvent.fire();
                return false;
            }

            quotationItemList.forEach(function(qItem){
                if(!qItem.promotionId){
                    qItem.promotionCode = '';
                }
            });
            component.set('v.orderItemList',quotationItemList);

            //check Mix&Match Order
            this.checkMixMatchOrder(component);

            if(!this.isAllMeetThreshold(component)){
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": "Warning!",
                    "message": "There are order items that do not meet its promotion rules.",
                    "type": "Warning"
                });
                toastEvent.fire();
                return false;
            }
        }
        return true;
    },
    hasMixMatch: function(selectedPromotion){
        var thresholdList = selectedPromotion.ruleList[0].thresholdList;
        var hasMixMatch = false;
        thresholdList.forEach(function(tItem){
            if(tItem.threshold.RecordType.DeveloperName == 'By_Mix_Match'){
                hasMixMatch = true;
            }
        });
        return hasMixMatch;
    },
	hasFreeGoods: function(ruleItem){
        var offeringList = ruleItem.offeringList;
        var hasFreeGoods = false;
        offeringList.forEach(function(oItem){
            if(oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice'){
                hasFreeGoods = true;
            }
            if(oItem.offering.RecordType.DeveloperName == 'Specific_Free_Good'){
                hasFreeGoods = true;
            }
        }); 
        return hasFreeGoods;
    },

    getUserInfo: function (component, event) { 
        var userInfo = $A.get("$SObjectType.CurrentUser");
        var userId = '';
        if (userInfo.Id) {
            userId = userInfo.Id;
        }
        var action = component.get('c.userInfo');
        action.setParam('userId', userId);
        action.setCallback(this, function(response){ 
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                if (results === 'NA Inside Sales' || results === 'System Administrator' || results === 'Canada Sales Manager') {
                    component.set('v.insideSalesModify', true);
                }
                if(results === 'Canada Sales Manager') {
                    component.set('v.handlingFeeEdit', false);
                }
            }    
        })
        $A.enqueueAction(action);
    }
})