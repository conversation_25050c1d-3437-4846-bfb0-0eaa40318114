trigger Account_Trigger2 on Account(
    before insert,
    after insert,
    before update,
    after update,
    before delete,
    after delete
) {
    if (CCM_SharingUtil.isSharingOnly) {
        return;
    }

    new Triggers()
        .bind(
            Triggers.Evt.beforeinsert,
            new CCM_Account_Validation_Rules_Handler()
        )
        .bind(Triggers.Evt.beforeinsert, new CCM_SalesOrgValidationHandler())
        .bind(Triggers.Evt.afterinsert, new CCM_AccountTaxIDUniqueCheckHandler())
        .bind(Triggers.Evt.afterupdate, new CCM_AccountTaxIDUniqueCheckHandler())
        .bind(Triggers.Evt.beforeupdate, new CCM_CustomerAssignmentRuleHandler())
        .bind(Triggers.Evt.afterupdate, new CCM_CustomerAssignmentRuleHandler())
        .bind(Triggers.Evt.beforeupdate, new CCM_CustomerOwnerUpdateHandler())
        .bind(Triggers.Evt.beforeupdate, new CCM_AccountChangeOwnerHandler())
        .bind(Triggers.Evt.beforeupdate, new CCM_SalesOrgValidationHandler())
        .bind(Triggers.Evt.afterupdate, new CCM_AccountChangeOwnerHandler())
        .bind(Triggers.Evt.afterupdate, new CCM_AccountSendApproval())
        .bind(Triggers.Evt.afterupdate, new CCM_AccountUpdateHandler())
        .bind(Triggers.Evt.beforeupdate, new CCM_PersonAccountFieldHandler())
        .bind(Triggers.Evt.beforeinsert, new CCM_PersonAccountFieldHandler())
        .bind(Triggers.Evt.beforeupdate, new CCM_ChannelCustomerFieldUpdate())
        .bind(Triggers.Evt.afterinsert, new CCM_ShareAccountHandler())
        .bind(Triggers.Evt.afterupdate, new CCM_ShareAccountHandler())
        // .bind(Triggers.Evt.afterinsert, new CCM_DealerLocationHandler())
        // .bind(Triggers.Evt.afterupdate, new CCM_DealerLocationHandler())
        .bind(Triggers.Evt.beforeinsert, new CCM_DealerLocationHandler2())
        .bind(Triggers.Evt.beforeupdate, new CCM_DealerLocationHandler2())
        .bind(Triggers.Evt.afterinsert, new CCM_DealerLocationHandler2())
        .bind(Triggers.Evt.afterupdate, new CCM_DealerLocationHandler2())
        .bind(Triggers.Evt.afterupdate, new CCM_AccountAutoSync())
        .bind(Triggers.Evt.beforeupdate, new CCM_ChannelCustomerFieldUpdate())
        .manage();
}