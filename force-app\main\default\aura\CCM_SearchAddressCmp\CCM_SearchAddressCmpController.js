({
    doInit :  function(component, event, helper) {
        component.set('v.inputAddress', component.get('v.inputAddress'));

        var addressId = component.get('v.selectedRecordId');
        helper.getAddress(component,event,addressId);

        var defaultCondition = '';
        if (component.get('v.addressType') == 'Billing_Address'){
                var action = component.get("c.getAddressId");
                action.setParam("customerId", component.get('v.customerId'));
                action.setParam("brandNames", component.get('v.brandNames'));
                action.setCallback(this, function (response) {
                    var state = response.getState();
                    console.log('state--->'+state);
                    if (state === "SUCCESS") {
                        var ret = JSON.parse(response.getReturnValue());
                        if (ret && ret != undefined){
                            component.set('v.addressIds', ret);
                            console.log('addressIds--->'+JSON.stringify(ret));
                            defaultCondition = '[{"Value":"{1}","FieldName":"Customer__c","Condtion":"="},{"Value":"{2}","FieldName":"RecordType_Name__c","Condtion":"="},{"Value":"Approved","FieldName":"Approval_Status__c","Condtion":"="},{"Value":"true","FieldName":"Active__c","Condtion":"="}]';

                            defaultCondition = defaultCondition.replace('{1}', component.get('v.customerId'));
                            defaultCondition = defaultCondition.replace('{2}', component.get('v.addressType'));
                            defaultCondition = defaultCondition.replace('{3}', component.get('v.addressIds').join('\',\''));
                            console.log('defaultCondition--->'+defaultCondition);
                            component.set('v.addressCondition', defaultCondition);
                        }
                    }else{
                        var errors = response.getError();
                        if (errors) {
                            if (errors[0] && errors[0].message) {
                                alert("ERROR: " + errors[0].message);
                            }
                        } else {
                            alert("ERROR: Unknown error");
                        }
                    }
                });

                $A.enqueueAction(action);
        }else if(component.get('v.addressType') == 'Shipping_Address' ||
         ((component.get('v.orderType') == 'parts') && component.get('v.addressType') == 'Billing_Address')){
            var userInfo = $A.get("$SObjectType.CurrentUser");
            var userId;
                if(userInfo.Id){
                userId = userInfo.Id;
            }
            defaultCondition = '[{"Value":"{1}","FieldName":"Customer__c","Condtion":"="},{"Value":"{2}","FieldName":"RecordType_Name__c","Condtion":"="},{"Value":"Approved","FieldName":"Approval_Status__c","Condtion":"="},{"Value":"true","FieldName":"Active__c","Condtion":"="}]';

            defaultCondition = defaultCondition.replace('{1}', component.get('v.customerId'));
            defaultCondition = defaultCondition.replace('{2}', component.get('v.addressType'));
            console.log('defaultCondition--->'+defaultCondition);
            component.set('v.addressCondition', defaultCondition);
        } else if (component.get('v.orderType') == 'parts' && component.get('v.addressType') == 'Dropship_Billing_Address') {
            var action = component.get("c.getAddressId");
            action.setParam("customerId", component.get('v.customerId'));
            action.setParam("brandNames", component.get('v.brandNames'));
            action.setCallback(this, function (response) {
                var state = response.getState();
                if (state === "SUCCESS") {
                    var ret = JSON.parse(response.getReturnValue());
                    if (ret && ret != undefined){
                        component.set('v.addressIds', ret);
                        console.log('addressIds--->'+JSON.stringify(ret));
                        defaultCondition = '[{"Value":"{1}","FieldName":"Customer__c","Condtion":"="},{"Value":"{2}","FieldName":"RecordType_Name__c","Condtion":"="},{"Value":"Approved","FieldName":"Approval_Status__c","Condtion":"="},{"Value":"true","FieldName":"Active__c","Condtion":"="}]';

                        defaultCondition = defaultCondition.replace('{1}', component.get('v.customerId'));
                        defaultCondition = defaultCondition.replace('{2}', component.get('v.addressType'));
                        defaultCondition = defaultCondition.replace('{3}', component.get('v.addressIds').join('\',\''));
                        console.log('defaultCondition--->'+defaultCondition);
                        component.set('v.addressCondition', defaultCondition);
                    }
                }else{
                    var errors = response.getError();
                    if (errors) {
                        if (errors[0] && errors[0].message) {
                            alert("ERROR: " + errors[0].message);
                        }
                    } else {
                        alert("ERROR: Unknown error");
                    }
                }
            });

            $A.enqueueAction(action);
        } else if (component.get('v.addressType') == 'Dropship_Billing_Address'){
            defaultCondition = '[{"Value":"Approved","FieldName":"Approval_Status__c","Condtion":"="},{"Value":"{1}","FieldName":"Customer__c","Condtion":"="},{"Value":"{2}","FieldName":"RecordType_Name__c","Condtion":"="},{"Value":"true","FieldName":"Active__c","Condtion":"="}]';
            defaultCondition = defaultCondition.replace('{1}', component.get('v.customerId'));
            defaultCondition = defaultCondition.replace('{2}', component.get('v.addressType'));
            console.log('defaultCondition--->'+defaultCondition);
            component.set('v.addressCondition', defaultCondition);
        }else if (component.get('v.addressType') == 'Dropship_Shipping_Address'){
            defaultCondition = '[{"Value":"Approved","FieldName":"Approval_Status__c","Condtion":"="},{"Value":"{1}","FieldName":"Customer__c","Condtion":"="},{"Value":"{2}","FieldName":"RecordType_Name__c","Condtion":"="},{"Value":"true","FieldName":"Active__c","Condtion":"="}]';
            defaultCondition = defaultCondition.replace('{1}', component.get('v.customerId'));
            defaultCondition = defaultCondition.replace('{2}', component.get('v.addressType'));
            console.log('defaultCondition--->'+defaultCondition);
            component.set('v.addressCondition', defaultCondition);
        }
        //update by austin
        else if (component.get('v.addressType') == '2nd_Tier_Dropship_Shipping_Address'){
            defaultCondition = '[{"Value":"Approved","FieldName":"Approval_Status__c","Condtion":"="},{"Value":"{1}","FieldName":"Customer__c","Condtion":"="},{"Value":"(\'{2}\')","FieldName":"X2nd_Tier_Dealer__c","Condtion":"IN"},{"Value":"Dropship_Shipping_Address","FieldName":"RecordType_Name__c","Condtion":"="},{"Value":"true","FieldName":"Active__c","Condtion":"="}]';
            defaultCondition = defaultCondition.replace('{1}', component.get('v.customerId'));
            defaultCondition = defaultCondition.replace('{2}', component.get('v.secondCustomerId').join('\',\''));
            console.log('defaultCondition---qwe>'+defaultCondition);
            component.set('v.addressCondition', defaultCondition);
        }
        //update by austin
    },
    onSelectAddress : function(component, event, helper){
        component.set('v.isShow', false);
        var addressId = event.getSource().get('v.value');
        helper.getAddress(component,event,addressId);
    },
    checkValidity: function(comp, event, helper) {
        var isValidity = comp.find('addressId').checkValidity();
        if(isValidity){
            var isRequired = comp.get('v.required');
            var inputValue = comp.get('v.value');
            if(isRequired && 
                (inputValue == null || inputValue == undefined || inputValue == '')){
                isValidity = false;
            }
        }

        return isValidity;
    },
    reportValidity: function(comp, event, helper) {
        comp.find('addressId').reportValidity();    
    },
    hideValidity: function(comp, event, helper) {
        Validator.verify(comp.find('addressId'));    
    },
    clearRequired: function(comp, event, helper) {
        Validator.clean( comp.find('addressId') );    
    }
})