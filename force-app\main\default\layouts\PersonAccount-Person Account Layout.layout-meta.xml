<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>ChangeOwnerOne</excludeButtons>
    <excludeButtons>ChangeRecordType</excludeButtons>
    <excludeButtons>DataDotComAccountInsights</excludeButtons>
    <excludeButtons>DataDotComCompanyHierarchy</excludeButtons>
    <excludeButtons>IncludeOffline</excludeButtons>
    <excludeButtons>SendEmail</excludeButtons>
    <excludeButtons>XClean</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Account Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RecordTypeId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Product_Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Problem_Customer__pc</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>CurrencyIsoCode</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Eligible_For_Fleet__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Trade__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Trade_Other__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Reverse_Order_Count__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TaxID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PaymentMethod__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PersonEmail</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Phone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Fax</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Brand_Influencer__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Organization_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MarketingOptIn__pc</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Email__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Recall_Message__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Address Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ShippingAddress</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BillingAddress</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Website Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Site_Origin__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EGO_username__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Skil_username__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SkilSaw_username__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FLEX_username__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Shopify_ID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SentWelcomeEmail__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EGO_password__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Skil_password__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SkilSaw_password__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FLEX_password__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>send_marketing_emails__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Customer_SF_ID__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Record_Type_Name__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns>
            <layoutItems>
                <customLink>GoogleSearch</customLink>
            </layoutItems>
            <layoutItems>
                <customLink>HooversProfile</customLink>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <customLink>GoogleMaps</customLink>
            </layoutItems>
        </layoutColumns>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Share</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>AccountAddToCampaign</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>PrintableView</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <relatedLists>
        <fields>CASES.CASE_NUMBER</fields>
        <fields>CASES.CREATED_DATE</fields>
        <fields>Case_Type__c</fields>
        <fields>CASES.SUBJECT</fields>
        <fields>Warranty__c</fields>
        <fields>CORE.USERS.ALIAS</fields>
        <relatedList>RelatedCaseList</relatedList>
    </relatedLists>
    <relatedLists>
        <customButtons>New_Warranty</customButtons>
        <fields>NAME</fields>
        <fields>Master_Product__c</fields>
        <fields>Master_Product_Code__c</fields>
        <fields>Purchase_Date__c</fields>
        <fields>CREATED_DATE</fields>
        <relatedList>Warranty__c.AccountCustomer__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Product_in_APP__c.Customer__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>TASK.WHAT_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>TASK.LAST_UPDATE</fields>
        <relatedList>RelatedHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Customer_Product__c.Customer__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Approval_Status__c</fields>
        <fields>Channel_Customer__c</fields>
        <fields>Bill_To_Address__c</fields>
        <fields>Total_Sales_Amount__c</fields>
        <fields>Fleet_Discount__c</fields>
        <fields>Total_Retail_Price__c</fields>
        <fields>Estimated_Credit_Return__c</fields>
        <relatedList>Fleet_Claim__c.End_User_Customer__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>CreatedDate</fields>
        <fields>StepStatus</fields>
        <fields>OriginalActor</fields>
        <fields>Actor</fields>
        <fields>Comments</fields>
        <relatedList>RelatedProcessHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedEntityHistoryList</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h1A00000bjoup</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
