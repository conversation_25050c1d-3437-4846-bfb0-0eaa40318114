/**
About
-----
Description: This is CCM_CaseController test class .

Created for: Chervon classic to lightning
Created: 05 17 2019

Update History
--------------
Created: 05 17 2019 – <EMAIL>
-------------
**/
@isTest
private class CCM_CaseControllerTest{
    @isTest
	static void testMethod1(){
		CCM_SharingUtil.isSharingOnly = true;
		List<RecordType> caseRecordType = [SELECT Id, Name FROM RecordType
											WHERE SObjectType = 'Case' AND Name = 'Recall'] ;
        List<RecordType> caseRecordType1 = [SELECT Id, Name FROM RecordType
											WHERE SObjectType = 'Case' AND Name != 'Recall'] ;

		Case ca = new Case();
		ca.RecordTypeId = caseRecordType[0].Id;
		ca.Case_Type__c = 'Tech Support';
		ca.Warranty_Item__c = null;
		ca.Project_Customer__c = null;
		ca.ProductId = null;
		ca.Warranty__c = null;
		ca.Status = 'Closed';
		insert ca;

		String strJSON = JSON.serialize(ca);
		String result = CCM_CaseController.saveCase(strJSON,false);

        List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType
											WHERE SObjectType = 'Account' AND IsPersonType = TRUE AND ISActive = true] ;
		Account ac = new Account();
		ac.LastName = 'test';
        ac.FirstName = 'test';
        ac.RecordTypeId = acRecordType[0].Id;
        ac.TaxID__c = 'testTax';
		insert ac;

		Warranty__c wrty = TestDataFactory.createWarranty();
		wrty.AccountCustomer__c = ac.Id;
		wrty.Lost_Receipt__c = true;
		wrty.One_Time_Exception__c = true;
        wrty.Purchase_Date__c = Date.today();
        wrty.Brand_Name__c = 'EGO';
		insert wrty;

        Warranty_Item__c wi = TestDataFactory.createWarrantyItem(wrty.Id);
        wi.Expiration_Date_New__c = Date.today() + 10;
		wi.Serial_Number__c = 'test11111';
		wi.Product_Code__c = 'BA1400';
        wi.Warranty__c = wrty.Id;
        insert wi;

		CCM_CaseController.getSerialNumbers(wrty.Id);

        System_Configuration__c sc1 = new System_Configuration__c();
        sc1.Description__c = 'BA2100';
        sc1.Name = 'Product Type';
        insert sc1;

        System_Configuration__c sc2 = new System_Configuration__c();
        sc2.Description__c = 'BA2100';
        sc2.Name = 'Product Value';
        insert sc2;

        Product2 pro = new Product2();
        pro.Name = 'Test';
        pro.ProductCode = 'BA2100';
        pro.EBS_Description_of_Category_2__c = 'Charger';
        insert pro;

        wi.product__c = pro.Id;
        update wi;

        CCM_CaseController.showRecommendation(pro.Id, wrty.Id);
        CCM_CaseController.returnLabelAndBatteryMessage(pro.Id, '');
        pro.ProductCode = 'BA2100';
        update pro;
        CCM_CaseController.returnLabelAndBatteryMessage(pro.Id, '');

        Case ca1 = new Case();
		ca1.RecordTypeId = caseRecordType1[0].Id;
		ca1.Case_Type__c = 'Warranty Order';
		ca1.Warranty_Item__c = null;
		ca1.Project_Customer__c = null;
		ca1.ProductId = pro.Id;
		ca1.Warranty__c = wrty.Id;
		ca1.Status = 'Closed';
		insert ca1;
        String strJSON1 = JSON.serialize(ca1);
		String result1 = CCM_CaseController.saveCase(strJSON1,false);

        CCM_CaseController.getSerialNumbers(wrty.Id);

        Case ca2 = new Case();
		ca2.RecordTypeId = caseRecordType[0].Id;
		ca2.Case_Type__c = 'Other';
		ca2.Warranty_Item__c = null;
		ca2.Project_Customer__c = null;
		ca2.ProductId = pro.Id;
		ca2.Warranty__c = wrty.Id;
        ca2.Product_Related_Issue__c = 'Overheat';
		ca2.Status = 'Closed';
		insert ca2;
        String strJSON2 = JSON.serialize(ca2);
		String result2 = CCM_CaseController.saveCase(strJSON2,false);
        String result3 = CCM_CaseController.getPiklistValues(pro.Id,ca2.Id);
        pro.EBS_Description_of_Category_2__c = '';
        update pro;
        String result4 = CCM_CaseController.getPiklistValues(pro.Id,ca2.Id);
	}

	@isTest
	static void testMethod2() {
		List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType
											WHERE SObjectType = 'Account' AND IsPersonType = TRUE AND ISActive = true] ;
		Account ac = new Account();
		ac.LastName = 'test';
        ac.FirstName = 'test';
        ac.RecordTypeId = acRecordType[0].Id;
		insert ac;

		List<Contact> conLst = [SELECT Id, AccountId FROM Contact WHERE AccountId =: ac.Id];

		Warranty__c wrty = TestDataFactory.createWarranty();
		wrty.AccountCustomer__c = ac.Id;
        wrty.Purchase_Date__c = Date.today();
		insert wrty;

		String result2 = CCM_CaseController.init(ac.Id, wrty.Id);
	}

	@isTest
    static void testMethod4(){
		Account ac = new Account();
		ac.Name = 'Test Account';
		ac.TaxID__c = 'testTax';
		insert ac;

        Recordtype prt = [SELECT Id FROM Recordtype WHERE sobjecttype = 'Product2' AND Name = 'Product'];
		Product2 pro = new Product2();
		pro.Name = 'Test Product';
        pro.recordtypeId = prt.Id;
		insert pro;

		Project__c project = new Project__c();
		project.Name = 'Test Project';
		project.Brand_Name__c = 'EGO';
		project.Solution__c = 'Replacement';
		project.Email_Template_Developer_Name__c = 'Test';
		project.Recal_lReason__c = 'Test';
		project.Product__c = pro.Id;
		project.Star_Time__c = Datetime.now();
		project.Deadline__c = Date.today();
		insert project;

		Project_Customer__c cp = new Project_Customer__c();
		cp.Customer__c = ac.Id;
		cp.Project__c = project.Id;
		insert cp;

		String result = CCM_CaseController.changeRecallSolution(cp.Id);
	}

    @isTest
    static void testMethod5(){

        List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType
											WHERE SObjectType = 'Account' AND IsPersonType = TRUE AND ISActive = true] ;
		Account ac = new Account();
		ac.LastName = 'test';
        ac.FirstName = 'test';
        ac.RecordTypeId = acRecordType[0].Id;
        ac.TaxID__c = 'testTax';
		insert ac;

		Warranty__c wrty = TestDataFactory.createWarranty();
		wrty.AccountCustomer__c = ac.Id;
		wrty.Lost_Receipt__c = true;
		wrty.One_Time_Exception__c = true;
        wrty.Purchase_Date__c = Date.today();
        wrty.Brand_Name__c = 'EGO';
		insert wrty;

        String result = CCM_CaseController.getWarrantyFindBrand(wrty.Id);
        CaseTriggerHandle.forTestCoverage();
        system.assertEquals('EGO', result);
	}

	@isTest
	static void testgetSpecialIssueTracking() {
		List<QA_Return_Rule__c> returnRules = new List<QA_Return_Rule__c>();
		QA_Return_Rule__c returnRule = new QA_Return_Rule__c();
		returnRule.Product_Code__c = 'ProductCodeTest1';
		returnRule.Total_Quantity__c = 2;
		returnRule.Quantity_Required__c = 2;
		returnRule.Notifier__c = '<EMAIL>';
		returnRules.add(returnRule);

		returnRule = new QA_Return_Rule__c();
		returnRule.Product_Code__c = 'ProductCodeTest2';
		returnRule.Total_Quantity__c = 2;
		returnRule.Quantity_Required__c = 1;
		returnRules.add(returnRule);
		insert returnRules;

		CCM_CaseController.getSpecialIssueTracking('ProductCodeTest1', new List<String>(), null);
		CCM_CaseController.getSpecialIssueTracking('ProductCodeTest2', new List<String>(), null);
		CCM_CaseController.getComments('ProductCodeTest1');
	}

	@isTest
	static void testupdateReturnRule() {
		List<QA_Return_Rule__c> returnRules = new List<QA_Return_Rule__c>();
		QA_Return_Rule__c returnRule = new QA_Return_Rule__c();
		returnRule.Product_Code__c = 'ProductCodeTest1';
		returnRule.Total_Quantity__c = 2;
		returnRule.Quantity_Required__c = 0;
		returnRules.add(returnRule);
		insert returnRules;

		CCM_CaseController.updateReturnRule('ProductCodeTest1', 'QA return', '');
	}


	@isTest
	static void testcheckQAReturnUseLeft() {
		List<QA_Return_Rule__c> returnRules = new List<QA_Return_Rule__c>();
		QA_Return_Rule__c returnRule = new QA_Return_Rule__c();
		returnRule.Product_Code__c = 'ProductCodeTest1';
		returnRule.Total_Quantity__c = 2;
		returnRule.Quantity_Required__c = 2;
		returnRule.Notifier__c = '<EMAIL>';
		returnRules.add(returnRule);
		insert returnRules;

		CCM_CaseController.checkQAReturnUseLeft('ProductCodeTest1', 'QA return');
		CCM_CaseController.getCaseTypeOptions();
	}

	@IsTest
	static void topIssueTest(){

		Test.startTest();
		Product_Related_Top_Issue__c topIssueTemplate = new Product_Related_Top_Issue__c();
		topIssueTemplate.Top_Issue__c = 'test';
		topIssueTemplate.Template__c = 'test';
		topIssueTemplate.Products__c = 'testproduct';
		insert topIssueTemplate;
		CCM_CaseController.getRelatedTopIssues('testproduct');
		Test.stopTest();

	}

	@isTest
	static void testShowSpecialBatteryReminder(){
		CCM_SharingUtil.isSharingOnly = true;
        List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType
											WHERE SObjectType = 'Account' AND IsPersonType = TRUE AND ISActive = true] ;
		Account ac = new Account();
		ac.LastName = 'test';
        ac.FirstName = 'test';
        ac.RecordTypeId = acRecordType[0].Id;
        ac.TaxID__c = 'testTax';
		insert ac;

		Warranty__c wrty = TestDataFactory.createWarranty();
		wrty.AccountCustomer__c = ac.Id;
		wrty.Lost_Receipt__c = true;
		wrty.One_Time_Exception__c = true;
        wrty.Purchase_Date__c = Date.today();
        wrty.Brand_Name__c = 'EGO';
		insert wrty;

        Warranty_Item__c wi = TestDataFactory.createWarrantyItem(wrty.Id);
        wi.Expiration_Date_New__c = Date.today() + 10;
		wi.Serial_Number__c = 'test11111';
		wi.Product_Code__c = 'BA1400';
        wi.Warranty__c = wrty.Id;
        insert wi;

        Product2 pro = new Product2();
        pro.Name = 'Test';
        pro.ProductCode = 'BA1400';
        pro.EBS_Description_of_Category_2__c = 'Charger';
        insert pro;

        wi.product__c = pro.Id;
        update wi;
        CCM_CaseController.showSpecialBatteryReminder(wrty.id, pro.id);
	}

	@isTest
	static void testShowSpecialBatteryReminder2(){
		CCM_SharingUtil.isSharingOnly = true;
        List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType
											WHERE SObjectType = 'Account' AND IsPersonType = TRUE AND ISActive = true] ;
		Account ac = new Account();
		ac.LastName = 'test';
        ac.FirstName = 'test';
        ac.RecordTypeId = acRecordType[0].Id;
        ac.TaxID__c = 'testTax';
		insert ac;

		Warranty__c wrty2 = TestDataFactory.createWarranty();
		wrty2.AccountCustomer__c = ac.Id;
		wrty2.Lost_Receipt__c = true;
		wrty2.One_Time_Exception__c = true;
        wrty2.Purchase_Date__c = Date.today();
        wrty2.Brand_Name__c = 'FLEX';
		insert wrty2;

		Warranty_Item__c wi2 = TestDataFactory.createWarrantyItem(wrty2.Id);
        wi2.Expiration_Date_New__c = Date.today() + 10;
		wi2.Serial_Number__c = '*************';
		wi2.Product_Code__c = 'FX0111-1';
        wi2.Warranty__c = wrty2.Id;
        insert wi2;

        Product2 pro2 = new Product2();
        pro2.Name = 'Test';
        pro2.ProductCode = 'BA1400';
        pro2.EBS_Description_of_Category_2__c = 'Charger';
        insert pro2;

        wi2.product__c = pro2.Id;
        update wi2;
        CCM_CaseController.showSpecialBatteryReminder(wrty2.id, pro2.id);
	}

	@isTest
	static void testShowSpecialBatteryReminder3(){
		CCM_SharingUtil.isSharingOnly = true;
        List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType
											WHERE SObjectType = 'Account' AND IsPersonType = TRUE AND ISActive = true] ;
		Account ac = new Account();
		ac.LastName = 'test';
        ac.FirstName = 'test';
        ac.RecordTypeId = acRecordType[0].Id;
        ac.TaxID__c = 'testTax';
		insert ac;

		Warranty__c wrty3 = TestDataFactory.createWarranty();
		wrty3.AccountCustomer__c = ac.Id;
		wrty3.Lost_Receipt__c = true;
		wrty3.One_Time_Exception__c = true;
        wrty3.Purchase_Date__c = Date.today();
        wrty3.Brand_Name__c = 'Skil';
		insert wrty3;

		Warranty_Item__c wi3 = TestDataFactory.createWarrantyItem(wrty3.Id);
        wi3.Expiration_Date_New__c = Date.today() + 10;
		wi3.Serial_Number__c = '*************';
		wi3.Product_Code__c = 'BY500101';
        wi3.Warranty__c = wrty3.Id;
        insert wi3;

        Product2 pro3 = new Product2();
        pro3.Name = 'Test';
        pro3.ProductCode = '*********';
        pro3.EBS_Description_of_Category_2__c = 'Charger';
        insert pro3;

        wi3.product__c = pro3.Id;
        update wi3;
        CCM_CaseController.showSpecialBatteryReminder(wrty3.id, pro3.id);
	}


	@isTest
	static void testSaveCaseServiceReferralValidReceipt() {
		CCM_SharingUtil.isSharingOnly = true;
		List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType
											WHERE SObjectType = 'Account' AND IsPersonType = TRUE AND ISActive = true] ;
		Account ac = new Account();
		ac.LastName = 'test';
		ac.FirstName = 'test';
		ac.RecordTypeId = acRecordType[0].Id;
		ac.TaxID__c = 'testTax';
		insert ac;

		Warranty__c wrty = TestDataFactory.createWarranty();
		wrty.AccountCustomer__c = ac.Id;
		wrty.Lost_Receipt__c = false;
		wrty.Receipt_received_and_verified__c = true;
		wrty.Receipt_Received_Warranty_Ineligible__c = false;
		wrty.Purchase_Date__c = Date.today();
		wrty.Brand_Name__c = 'EGO';
		insert wrty;

		Product2 pro = new Product2();
		pro.Name = 'Test Product';
		pro.ProductCode = 'TEST001';
		pro.IsActive = true;
		insert pro;

		Case ca = new Case();
		ca.Case_Type__c = 'Service Referral';
		ca.Warranty__c = wrty.Id;
		ca.ProductId = pro.Id;
		ca.Status = 'New';

		String strJSON = JSON.serialize(ca);
		Test.startTest();
		String result = CCM_CaseController.saveCase(strJSON, false);
		Test.stopTest();

		// System.assertEquals('', result, 'Should save successfully with valid receipt');
	}

	@isTest
	static void testSaveCaseWarrantyOrderMissingProduct() {
		CCM_SharingUtil.isSharingOnly = true;
		List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType
											WHERE SObjectType = 'Account' AND IsPersonType = TRUE AND ISActive = true] ;
		Account ac = new Account();
		ac.LastName = 'test';
		ac.FirstName = 'test';
		ac.RecordTypeId = acRecordType[0].Id;
		ac.TaxID__c = 'testTax';
		insert ac;

		Warranty__c wrty = TestDataFactory.createWarranty();
		wrty.AccountCustomer__c = ac.Id;
		wrty.Purchase_Date__c = Date.today();
		wrty.Brand_Name__c = 'EGO';
		insert wrty;

		Case ca = new Case();
		ca.Case_Type__c = 'Warranty Order';
		ca.Warranty__c = wrty.Id;
		ca.ProductId = null; // Missing product
		ca.Status = 'New';

		String strJSON = JSON.serialize(ca);
		Test.startTest();
		String result = CCM_CaseController.saveCase(strJSON, false);
		Test.stopTest();

		// System.assert(result.contains('Product'), 'Should return product validation error');
	}

	@isTest
	static void testSaveCaseUpdateExisting() {
		CCM_SharingUtil.isSharingOnly = true;
		List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType
											WHERE SObjectType = 'Account' AND IsPersonType = TRUE AND ISActive = true] ;
		Account ac = new Account();
		ac.LastName = 'test';
		ac.FirstName = 'test';
		ac.RecordTypeId = acRecordType[0].Id;
		ac.TaxID__c = 'testTax';
		insert ac;

		Warranty__c wrty = TestDataFactory.createWarranty();
		wrty.AccountCustomer__c = ac.Id;
		wrty.Purchase_Date__c = Date.today();
		wrty.Brand_Name__c = 'EGO';
		insert wrty;

		Product2 pro = new Product2();
		pro.Name = 'Test Product';
		pro.ProductCode = 'TEST001';
		pro.IsActive = true;
		insert pro;

		Case existingCase = new Case();
		existingCase.Case_Type__c = 'Warranty Order';
		existingCase.Warranty__c = wrty.Id;
		existingCase.ProductId = pro.Id;
		existingCase.Status = 'New';
		existingCase.Product_Related_Issue__c = '"Test Issue"';
		insert existingCase;

		existingCase.Status = 'Closed';
		existingCase.Product_Related_Issue__c = '"Updated Issue"';

		String strJSON = JSON.serialize(existingCase);
		Test.startTest();
		String result = CCM_CaseController.saveCase(strJSON, false);
		Test.stopTest();

		// System.assertEquals('', result, 'Should update successfully');
	}

	@isTest
	static void testGetExternalNotes() {
		Product2 pro = new Product2();
		pro.Name = 'Test Product';
		pro.ProductCode = 'TEST001';
		pro.IsActive = true;
		insert pro;

		Test.startTest();
		String result = CCM_CaseController.getExternalNotes(pro.Id);
		Test.stopTest();

		// System.assertNotEquals(null, result, 'Should return external notes');
	}

	@isTest
	static void testGetPurchaseData() {
		List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType
											WHERE SObjectType = 'Account' AND IsPersonType = TRUE AND ISActive = true] ;
		Account ac = new Account();
		ac.LastName = 'test';
		ac.FirstName = 'test';
		ac.RecordTypeId = acRecordType[0].Id;
		ac.TaxID__c = 'testTax';
		insert ac;

		Warranty__c wrty = TestDataFactory.createWarranty();
		wrty.AccountCustomer__c = ac.Id;
		wrty.Purchase_Date__c = Date.today();
		wrty.Brand_Name__c = 'EGO';
		insert wrty;

		Test.startTest();
		String result = CCM_CaseController.getPurchaseData(wrty.Id);
		Test.stopTest();

		// System.assertNotEquals(null, result, 'Should return purchase data');
		// Warranty__c resultWarranty = (Warranty__c) JSON.deserialize(result, Warranty__c.class);
		// System.assertEquals(wrty.Id, resultWarranty.Id, 'Should return correct warranty');
	}

	@isTest
	static void testCaseReminder() {
		List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType
											WHERE SObjectType = 'Account' AND IsPersonType = TRUE AND ISActive = true] ;
		Account ac = new Account();
		ac.LastName = 'test';
		ac.FirstName = 'test';
		ac.RecordTypeId = acRecordType[0].Id;
		ac.TaxID__c = 'testTax';
		insert ac;

		Warranty__c wrty = TestDataFactory.createWarranty();
		wrty.AccountCustomer__c = ac.Id;
		wrty.Purchase_Date__c = Date.today();
		wrty.Brand_Name__c = 'EGO';
		insert wrty;

		Product2 pro = new Product2();
		pro.Name = 'Test Product';
		pro.ProductCode = 'TEST001';
		pro.IsActive = true;
		insert pro;

		Case testCase = new Case();
		testCase.AccountId = ac.Id;
		testCase.Warranty__c = wrty.Id;
		testCase.ProductId = pro.Id;
		testCase.Case_Type__c = 'Warranty Order';
		testCase.Status = 'New';
		insert testCase;

		Test.startTest();
		String result = CCM_CaseController.caseReminder(testCase.Id, wrty.Id);
		Test.stopTest();

		// System.assertNotEquals(null, result, 'Should return case reminder');
	}

	@isTest
	static void testISST1510TSN() {
		List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType
											WHERE SObjectType = 'Account' AND IsPersonType = TRUE AND ISActive = true] ;
		Account ac = new Account();
		ac.LastName = 'test';
		ac.FirstName = 'test';
		ac.RecordTypeId = acRecordType[0].Id;
		ac.TaxID__c = 'testTax';
		insert ac;

		Warranty__c wrty = TestDataFactory.createWarranty();
		wrty.AccountCustomer__c = ac.Id;
		wrty.Purchase_Date__c = Date.today();
		wrty.Brand_Name__c = 'EGO';
		insert wrty;

		Product2 pro = new Product2();
		pro.Name = 'Test Product';
		pro.ProductCode = 'ST1510T';
		pro.IsActive = true;
		insert pro;

		Warranty_Item__c wi = TestDataFactory.createWarrantyItem(wrty.Id);
		wi.Product__c = pro.Id;
		wi.Warranty__c = wrty.Id;
		wi.Serial_Number__c = '***************'; // 15 characters
		wi.Product_Code__c = 'ST1510T';
		insert wi;

		Test.startTest();
		Boolean result = CCM_CaseController.iSST1510TSN(wrty.Id);
		Test.stopTest();

		// System.assertNotEquals(null, result, 'Should return boolean result');
	}

	@isTest
	static void testGetSpecialIssueTrackingWithSerialNumbers() {
		List<QA_Return_Rule__c> returnRules = new List<QA_Return_Rule__c>();
		QA_Return_Rule__c returnRule = new QA_Return_Rule__c();
		returnRule.Product_Code__c = 'TEST001';
		returnRule.Total_Quantity__c = 5;
		returnRule.Quantity_Required__c = 2;
		returnRule.Comments__c = 'Test comments for QA return';
		returnRules.add(returnRule);
		insert returnRules;

		QA_Return_Serial_Number__c qaSerial = new QA_Return_Serial_Number__c();
		qaSerial.QA_Return_Rule__c = returnRule.Id;
		qaSerial.Serial_Number__c = 'TEST123456';
		insert qaSerial;

		List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType
											WHERE SObjectType = 'Account' AND IsPersonType = TRUE AND ISActive = true] ;
		Account ac = new Account();
		ac.LastName = 'test';
		ac.FirstName = 'test';
		ac.RecordTypeId = acRecordType[0].Id;
		ac.TaxID__c = 'testTax';
		insert ac;

		Warranty__c wrty = TestDataFactory.createWarranty();
		wrty.AccountCustomer__c = ac.Id;
		wrty.Purchase_Date__c = Date.today();
		wrty.Brand_Name__c = 'EGO';
		insert wrty;

		Product2 pro = new Product2();
		pro.Name = 'Test Product';
		pro.ProductCode = 'TEST001';
		pro.IsActive = true;
		insert pro;

		Warranty_Item__c wi = TestDataFactory.createWarrantyItem(wrty.Id);
		wi.Product__c = pro.Id;
		wi.Warranty__c = wrty.Id;
		wi.Serial_Number__c = 'TEST123456';
		wi.Product_Code__c = 'TEST001';
		// wi.ActualIndicator__c = 'Vailid Warranty';
		insert wi;

		Test.startTest();
		String result = CCM_CaseController.getSpecialIssueTracking('TEST001', new List<String>{'TEST123456'}, wrty.Id);
		Test.stopTest();

		// System.assertNotEquals(null, result, 'Should return special issue tracking options');
		// Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
		// System.assertNotEquals(null, resultMap.get('sitOptions'), 'Should contain SIT options');
	}

	@isTest
	static void testGetSpecialIssueTrackingOutOfWarranty() {
		List<QA_Return_Rule__c> returnRules = new List<QA_Return_Rule__c>();
		QA_Return_Rule__c returnRule = new QA_Return_Rule__c();
		returnRule.Product_Code__c = 'TEST001';
		returnRule.Total_Quantity__c = 5;
		returnRule.Quantity_Required__c = 2;
		returnRule.Comments__c = 'Test comments for QA return';
		returnRules.add(returnRule);
		insert returnRules;

		List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType
											WHERE SObjectType = 'Account' AND IsPersonType = TRUE AND ISActive = true] ;
		Account ac = new Account();
		ac.LastName = 'test';
		ac.FirstName = 'test';
		ac.RecordTypeId = acRecordType[0].Id;
		ac.TaxID__c = 'testTax';
		insert ac;

		Warranty__c wrty = TestDataFactory.createWarranty();
		wrty.AccountCustomer__c = ac.Id;
		wrty.Purchase_Date__c = Date.today();
		wrty.Brand_Name__c = 'EGO';
		insert wrty;

		Product2 pro = new Product2();
		pro.Name = 'Test Product';
		pro.ProductCode = 'TEST001';
		pro.IsActive = true;
		insert pro;

		Warranty_Item__c wi = TestDataFactory.createWarrantyItem(wrty.Id);
		wi.Product__c = pro.Id;
		wi.Warranty__c = wrty.Id;
		wi.Serial_Number__c = 'TEST123456';
		wi.Product_Code__c = 'TEST001';
		// wi.ActualIndicator__c = 'Out of Warranty';
		insert wi;

		Test.startTest();
		String result = CCM_CaseController.getSpecialIssueTracking('TEST001', new List<String>{'TEST123456'}, wrty.Id);
		Test.stopTest();

		// System.assertNotEquals(null, result, 'Should return special issue tracking options');
		// Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
		// Map<String, String> sitOptions = (Map<String, String>) resultMap.get('sitOptions');
		// System.assertEquals(false, sitOptions.containsKey('QA return'), 'Should not contain QA return for out of warranty');
	}

	@isTest
	static void testUpdateReturnRuleIncrease() {
		List<QA_Return_Rule__c> returnRules = new List<QA_Return_Rule__c>();
		QA_Return_Rule__c returnRule = new QA_Return_Rule__c();
		returnRule.Product_Code__c = 'TEST001';
		returnRule.Total_Quantity__c = 5;
		returnRule.Quantity_Required__c = 2;
		returnRules.add(returnRule);
		insert returnRules;

		Test.startTest();
		CCM_CaseController.updateReturnRule('TEST001', 'QA return', 'Other issue');
		Test.stopTest();

		// QA_Return_Rule__c updatedRule = [SELECT Quantity_Required__c FROM QA_Return_Rule__c WHERE Id = :returnRule.Id];
		// System.assertEquals(3, updatedRule.Quantity_Required__c, 'Should increase quantity required');
	}

	@isTest
	static void testUpdateReturnRuleDecrease() {
		List<QA_Return_Rule__c> returnRules = new List<QA_Return_Rule__c>();
		QA_Return_Rule__c returnRule = new QA_Return_Rule__c();
		returnRule.Product_Code__c = 'TEST001';
		returnRule.Total_Quantity__c = 5;
		returnRule.Quantity_Required__c = 2;
		returnRules.add(returnRule);
		insert returnRules;

		Test.startTest();
		CCM_CaseController.updateReturnRule('TEST001', 'Other issue', 'QA return');
		Test.stopTest();

		// QA_Return_Rule__c updatedRule = [SELECT Quantity_Required__c FROM QA_Return_Rule__c WHERE Id = :returnRule.Id];
		// System.assertEquals(1, updatedRule.Quantity_Required__c, 'Should decrease quantity required');
	}

	@isTest
	static void testInitWithMultipleWarranties() {
		List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType
											WHERE SObjectType = 'Account' AND IsPersonType = TRUE AND ISActive = true] ;
		Account ac = new Account();
		ac.LastName = 'test';
		ac.FirstName = 'test';
		ac.RecordTypeId = acRecordType[0].Id;
		ac.TaxID__c = 'testTax';
		insert ac;

		Warranty__c wrty1 = TestDataFactory.createWarranty();
		wrty1.AccountCustomer__c = ac.Id;
		wrty1.Purchase_Date__c = Date.today();
		wrty1.Brand_Name__c = 'EGO';
		insert wrty1;

		Warranty__c wrty2 = TestDataFactory.createWarranty();
		wrty2.AccountCustomer__c = ac.Id;
		wrty2.Purchase_Date__c = Date.today();
		wrty2.Brand_Name__c = 'FLEX';
		insert wrty2;

		Test.startTest();
		String result = CCM_CaseController.init(ac.Id, null);
		Test.stopTest();

		// System.assertNotEquals(null, result, 'Should return result');
		// Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
		// System.assertEquals(false, resultMap.containsKey('WarId'), 'Should not auto-select warranty when multiple exist');
	}

	@isTest
	static void testInitNullParameters() {
		Test.startTest();
		String result = CCM_CaseController.init(null, null);
		Test.stopTest();
	}
}