<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <actionCalls>
        <name>Sync_Data</name>
        <label>Sync Data</label>
        <locationX>182</locationX>
        <locationY>1166</locationY>
        <actionName>CCM_CreateVisitSurveyController</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Visit_Survey_Created</targetReference>
        </connector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>lstSurveyId</name>
            <value>
                <elementReference>Create_Visit_Survey</elementReference>
            </value>
        </inputParameters>
    </actionCalls>
    <apiVersion>60.0</apiVersion>
    <decisions>
        <name>check_file</name>
        <label>check file</label>
        <locationX>182</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>Create_Visit_Survey</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>check_file_upload_is_empty</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>fileIds</leftValueReference>
                <operator>EqualTo</operator>
            </conditions>
            <connector>
                <targetReference>Notice_Upload_File</targetReference>
            </connector>
            <label>check file upload is empty</label>
        </rules>
    </decisions>
    <dynamicChoiceSets>
        <name>Any_new_or_notable_merchandising_from_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Any_new_or_notable_merchandising_from__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Any_new_products_from_competitors_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Any_new_products_from_competitors__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Are_stocking_beyond_L2_3_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Are_stocking_beyond_L2_3__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Are_they_participating_in_ANY_active_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Are_they_participating_in_ANY_active__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Augers_Picklist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Augers__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>BATTERY_Picklist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>BATTERY__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Bikes_nested_Picklist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Bikes_nested__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>CHAINSAWS_Pickuplist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>CHAINSAWS__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Commercial_Opportunity_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Commercial_Opportunity__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Core_set_LM2156SP_LM2135SP_LM2125S_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Core_set_LM2156SP_LM2135SP_LM2125S__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>EGO_Set_Location</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>EGO_Set_Loctaion__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Elliptical_opportunity_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Elliptical_opportunity__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
        <sortOrder>Desc</sortOrder>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Fulfill_Commercial_Guidelines_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Fulfill_Commercial_Guidelines__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>HEDGE_TRIMMER_Picklist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>HEDGE_TRIMMER__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>How_are_they_participating_select_a_Picklist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>How_are_they_participating_select_a__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>i_Is_it_merchandised_correctly_E3_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>i_Is_it_merchandised_correctly_E3__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Is_it_merchandised_correctly_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Is_it_merchandised_correctly__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Is_the_decision_maker_on_site_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Is_the_decision_maker_on_site__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Is_the_store_in_the_E3_set_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Is_the_store_in_the_E3_set__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Is_the_store_in_the_L2_set_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Is_the_store_in_the_L2_set__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Is_there_an_opportunity_to_bring_in_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Is_there_an_opportunity_to_bring_in__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Is_there_an_opportunity_to_expand_t_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Is_there_an_opportunity_to_expand_t__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Is_there_an_opportunity_to_prompt_res_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Is_there_an_opportunity_to_prompt_res__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Is_this_store_participating_in_ALL_ac_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Is_this_store_participating_in_ALL_ac__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>LEAF_BLOWER_Picklist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>LEAF_BLOWER__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>LIFESTYLE_Pickuplist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>LIFESTYLE__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Lights_Picklist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Lights__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>LocationofSet_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Location_of_Set__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Merchandising_Assortment</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Merchandising_Assortment__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Misting_Fan_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Misting_Fan__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>MULTIHEAD_Picklist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>MULTIHEAD__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Opportunity_to_stock_in_store_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Opportunity_to_stock_in_store__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Portable_Power_nested_Picklist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Portable_Power_nested__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Pressure_Washer_nested_Picklist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Pressure_Washer_nested__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Riders_Stocking_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Riders_Stocking__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Snow_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Snow__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Snow_What_Picklist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Snow_What__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>STRING_TRIMMER_Picklist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>STRING_TRIMMER__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Talking_with_whom</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Talking_with_Whom__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Wet_Dry_Vac_Picklist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Wet_Dry_Vac__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>What_is_stocked_Select_all_that_app_Picklist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>What_is_stocked_Select_all_that_app__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>What_is_the_cause_for_restocking_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>What_is_the_cause_for_restocking__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>What_is_the_current_dealer_choice</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>What_s_the_current_existing_competitor__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>What_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>What__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Which_models_Select_all_that_apply_Picklist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Which_models_Select_all_that_apply__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Which_SKUs_select_all_that_apply_Picklist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Which_SKUs_select_all_that_apply__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Why_not_All_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Why_not_All__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Why_not_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Why_not__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Why_not_select_all_that_apply_Picklist</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Why_not_select_all_that_apply__c</picklistField>
        <picklistObject>Visit_Survey__c</picklistObject>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <interviewLabel>Visit Survey Flow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Visit Survey Flow</label>
    <loops>
        <name>Iterate_Uploaded_Files_Id</name>
        <label>Iterate Uploaded Files Id</label>
        <locationX>182</locationX>
        <locationY>758</locationY>
        <collectionReference>fileIds</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Link_Attachment_to_Survey</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Create_a_Task</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_a_Task</name>
        <label>Create a Task</label>
        <locationX>182</locationX>
        <locationY>1058</locationY>
        <connector>
            <targetReference>Sync_Data</targetReference>
        </connector>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>Store Visit</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Visit_Survey__c</field>
            <value>
                <elementReference>Create_Visit_Survey</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Visit_Survey</name>
        <label>Create Visit Survey</label>
        <locationX>182</locationX>
        <locationY>650</locationY>
        <connector>
            <targetReference>Iterate_Uploaded_Files_Id</targetReference>
        </connector>
        <inputAssignments>
            <field>Any_new_or_notable_merchandising_from__c</field>
            <value>
                <elementReference>X2_Any_new_or_notable_merchandising_from_competitors</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Any_new_or_notable_merchandising_from_co__c</field>
            <value>
                <elementReference>X2_Any_new_or_notable_merchandising_from_competitors_FREE_RESPONSE</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Any_new_products_from_competitors__c</field>
            <value>
                <elementReference>X1_Any_new_products_from_competitors</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Are_stocking_beyond_L2_3__c</field>
            <value>
                <elementReference>a_Are_stocking_beyond_L2_3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Are_they_participating_in_ANY_active__c</field>
            <value>
                <elementReference>i_Are_they_participating_in_ANY_active_promos</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Augers__c</field>
            <value>
                <elementReference>f_Augers</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>BATTERY_FREE_RESPONSE__c</field>
            <value>
                <elementReference>viii_BATTERY_FREE_RESPONSE</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>BATTERY__c</field>
            <value>
                <elementReference>viii_BATTERY</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Bikes_nested__c</field>
            <value>
                <elementReference>b_Bikes_nested</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>CHAINSAWS_FREE_RESPONSE__c</field>
            <value>
                <elementReference>vi_CHAINSAWS_FREE_RESPONSE</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>CHAINSAWS__c</field>
            <value>
                <elementReference>vi_CHAINSAWS</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Commercial_Opportunity__c</field>
            <value>
                <elementReference>X7_Commercial_Opportunity</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Core_set_LM2156SP_LM2135SP_LM2125S__c</field>
            <value>
                <elementReference>a_Core_set_LM2156SP_LM2135SP_LM2125SP_LM2114</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Elliptical_opportunity__c</field>
            <value>
                <elementReference>X1_Elliptical_opportunity</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Fulfill_Commercial_Guidelines__c</field>
            <value>
                <elementReference>i_Fulfill_Commercial_Guidelines</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>HEDGE_TRIMMER_FREE_RESPONSE__c</field>
            <value>
                <elementReference>v_HEDGE_TRIMMER_FREE_RESPONSE</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>HEDGE_TRIMMER__c</field>
            <value>
                <elementReference>v_HEDGE_TRIMMER</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>How_are_they_participating_FREE_RESPON__c</field>
            <value>
                <elementReference>X1_i_How_are_they_participating_FREE_RESPONSE</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>How_are_they_participating_select_a__c</field>
            <value>
                <elementReference>X1_i_How_are_they_participating_select_all_that_apply</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Is_it_merchandised_correctly__c</field>
            <value>
                <elementReference>X1_Is_it_merchandised_correctly</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Is_the_decision_maker_on_site__c</field>
            <value>
                <elementReference>X1_Is_the_decision_maker_on_site</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Is_the_store_in_the_E3_set__c</field>
            <value>
                <elementReference>X1_Is_the_store_in_the_E3_set</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Is_the_store_in_the_L2_set__c</field>
            <value>
                <elementReference>a_Is_the_store_in_the_L2_set</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Is_there_an_opportunity_to_bring_in__c</field>
            <value>
                <elementReference>X1_ii_Is_there_an_opportunity_to_bring_in_new_SKUs</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Is_there_an_opportunity_to_expand_t__c</field>
            <value>
                <elementReference>X1_iii_Is_there_an_opportunity_to_expand_the_EGO_Set</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Is_there_an_opportunity_to_prompt_res__c</field>
            <value>
                <elementReference>X1_i_Is_there_an_opportunity_to_prompt_restock_from_RSC</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Is_this_store_participating_in_ALL_ac__c</field>
            <value>
                <elementReference>X1_Is_this_store_participating_in_ALL_active_promos</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LEAF_BLOWER_FREE_RESPONSE__c</field>
            <value>
                <elementReference>iv_LEAF_BLOWER_FREE_RESPONSE</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LEAF_BLOWER__c</field>
            <value>
                <elementReference>iv_LEAF_BLOWER</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LIFESTYLE_FREE_RESPONSE__c</field>
            <value>
                <elementReference>vii_LIFESTYLE_FREE_RESPONSE</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LIFESTYLE__c</field>
            <value>
                <elementReference>vii_LIFESTYLE</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Lights__c</field>
            <value>
                <elementReference>e_Lights</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Location_of_Set__c</field>
            <value>
                <elementReference>X1_Location_of_Set_2</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MULTIHEAD_FREE_RESPONSE__c</field>
            <value>
                <elementReference>iii_MULTIHEAD_FREE_RESPONSE</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MULTIHEAD__c</field>
            <value>
                <elementReference>iii_MULTIHEAD</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Misting_Fan__c</field>
            <value>
                <elementReference>X5_a_1_g_Misting_Fan</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Opportunity_to_stock_in_store__c</field>
            <value>
                <elementReference>a_Opportunity_to_stock_in_store</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Portable_Power_nested__c</field>
            <value>
                <elementReference>a_Portable_Power_nested</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Pressure_Washer_nested__c</field>
            <value>
                <elementReference>c_Pressure_Washer_nested</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Retailer_requests__c</field>
            <value>
                <elementReference>X1_FREE_RESPONSE</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Riders_Stocking__c</field>
            <value>
                <elementReference>a_Stocking</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>STRING_TRIMMER_FREE_RESPONSE__c</field>
            <value>
                <elementReference>STRING_TRIMMER_FREE_RESPONSE</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>STRING_TRIMMER__c</field>
            <value>
                <elementReference>ii_STRING_TRIMMER</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Snow_What__c</field>
            <value>
                <elementReference>i_What_select_all_that_apply</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Snow__c</field>
            <value>
                <elementReference>X6_Snow</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Store_Location__c</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Wet_Dry_Vac__c</field>
            <value>
                <elementReference>d_Wet_Dry_Vac</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>What__c</field>
            <value>
                <elementReference>X1_iii_a_What</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>What_is_stocked_Select_all_that_app__c</field>
            <value>
                <elementReference>X1_What_is_stocked_Select_all_that_apply</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>What_is_the_cause_for_restocking__c</field>
            <value>
                <elementReference>X1_i_a_What_is_the_cause_for_restocking</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>What_s_the_current_existing_competitor__c</field>
            <value>
                <elementReference>X3_What_s_the_current_competitor</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Which_SKUs_select_all_that_apply__c</field>
            <value>
                <elementReference>X1_ii_a_Which_SKUs_select_all_that_apply</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Which_models_Select_all_that_apply__c</field>
            <value>
                <elementReference>X1_Which_models_Select_all_that_apply</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Why_not_All_Free_Response__c</field>
            <value>
                <elementReference>X1_i_a_Why_not_All_FREE_RESPONSE</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Why_not_All__c</field>
            <value>
                <elementReference>X1_i_a_Why_not_All</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Why_not_FREE_RESPONSE__c</field>
            <value>
                <elementReference>X1_i_a_Why_not_FREE_RESPONSE</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Why_not__c</field>
            <value>
                <elementReference>X1_i_a_Why_not</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Why_not_select_all_that_apply__c</field>
            <value>
                <elementReference>X1_ii_a_Why_not_select_all_that_apply</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>X1_Any_new_products_from_competitors_F__c</field>
            <value>
                <elementReference>X1_Any_new_products_from_competitors_FREE_RESPONSE</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>i_Is_it_merchandised_correctly_E3__c</field>
            <value>
                <elementReference>X2_a_1_i_Is_it_merchandised_correctly</elementReference>
            </value>
        </inputAssignments>
        <object>Visit_Survey__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Link_Attachment_to_Survey</name>
        <label>Link Attachment to Survey</label>
        <locationX>270</locationX>
        <locationY>866</locationY>
        <connector>
            <targetReference>Iterate_Uploaded_Files_Id</targetReference>
        </connector>
        <inputAssignments>
            <field>ContentDocumentId</field>
            <value>
                <elementReference>Iterate_Uploaded_Files_Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LinkedEntityId</field>
            <value>
                <elementReference>Create_Visit_Survey</elementReference>
            </value>
        </inputAssignments>
        <object>ContentDocumentLink</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Store_Location_Records</name>
        <label>Get Store Location Records</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Visit_Survey</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>Notice_Upload_File</name>
        <label>Notice Upload File</label>
        <locationX>50</locationX>
        <locationY>458</locationY>
        <allowBack>true</allowBack>
        <allowFinish>false</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Create_Visit_Survey</targetReference>
        </connector>
        <fields>
            <name>notice1</name>
            <fieldText>&lt;p&gt;Attachment is required, please upload files.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Visit_Survey</name>
        <label>ACE Survey ********</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>check_file</targetReference>
        </connector>
        <fields>
            <name>SET_Integrity_What_are_stores_stocking_selling</name>
            <fieldText>SET Integrity – What are stores stocking/selling.</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>SET_Integrity_What_are_stores_stocking_selling_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>X1_Location_of_Set_2</name>
                    <choiceReferences>LocationofSet_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>1.	Location of Set</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Core</name>
                    <fieldText>&lt;p&gt;2 . Core&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>a_Is_the_store_in_the_L2_set</name>
                    <choiceReferences>Is_the_store_in_the_L2_set_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>2.a.	Is the store in the L2 set?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>X1_Is_it_merchandised_correctly</name>
                    <choiceReferences>Is_it_merchandised_correctly_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>2.a.1.	Is it merchandised correctly?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>a_Is_the_store_in_the_L2_set</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X1_Is_the_store_in_the_E3_set</name>
                    <choiceReferences>Is_the_store_in_the_E3_set_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>2.a.1.	Is the store in the E3 set?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>a_Is_the_store_in_the_L2_set</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>No</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X2_a_1_i_Is_it_merchandised_correctly</name>
                    <choiceReferences>i_Is_it_merchandised_correctly_E3_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>2.a.1.i.	Is it merchandised correctly?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_Is_the_store_in_the_E3_set</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>E3_set_no_text</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;font-family: Arial, sans-serif; font-size: 12px;&quot;&gt;2.a.1.i. If not, what Is being stocked? (select all that apply)&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_Is_the_store_in_the_E3_set</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>No</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>ii_STRING_TRIMMER</name>
                    <choiceReferences>STRING_TRIMMER_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>2.a.1.ii.	STRING TRIMMER (select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_Is_the_store_in_the_E3_set</leftValueReference>
                            <operator>Contains</operator>
                            <rightValue>
                                <stringValue>No</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>STRING_TRIMMER_FREE_RESPONSE</name>
                    <dataType>String</dataType>
                    <fieldText>2.a.1.ii. STRING TRIMMER (FREE RESPONSE)</fieldText>
                    <fieldType>InputField</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>ii_STRING_TRIMMER</leftValueReference>
                            <operator>Contains</operator>
                            <rightValue>
                                <stringValue>(FREE RESPONSE)</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>iii_MULTIHEAD</name>
                    <choiceReferences>MULTIHEAD_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>2.a.1.iii.	MULTIHEAD (select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_Is_the_store_in_the_E3_set</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>No</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>iii_MULTIHEAD_FREE_RESPONSE</name>
                    <dataType>String</dataType>
                    <fieldText>2.a.1.iii.	MULTIHEAD (FREE RESPONSE)</fieldText>
                    <fieldType>InputField</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>iii_MULTIHEAD</leftValueReference>
                            <operator>Contains</operator>
                            <rightValue>
                                <stringValue>(FREE RESPONSE)</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>iv_LEAF_BLOWER</name>
                    <choiceReferences>LEAF_BLOWER_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>2.a.1.iv.	LEAF BLOWER (select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_Is_the_store_in_the_E3_set</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>No</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>iv_LEAF_BLOWER_FREE_RESPONSE</name>
                    <dataType>String</dataType>
                    <fieldText>2.a.1.iv.	LEAF BLOWER (FREE RESPONSE)</fieldText>
                    <fieldType>InputField</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>iv_LEAF_BLOWER</leftValueReference>
                            <operator>Contains</operator>
                            <rightValue>
                                <stringValue>(FREE RESPONSE)</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>v_HEDGE_TRIMMER</name>
                    <choiceReferences>HEDGE_TRIMMER_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>2.a.1.v.	HEDGE TRIMMER (select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_Is_the_store_in_the_E3_set</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>No</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>v_HEDGE_TRIMMER_FREE_RESPONSE</name>
                    <dataType>String</dataType>
                    <fieldText>2.a.1.v.	HEDGE TRIMMER (FREE RESPONSE)</fieldText>
                    <fieldType>InputField</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>v_HEDGE_TRIMMER</leftValueReference>
                            <operator>Contains</operator>
                            <rightValue>
                                <stringValue>(FREE RESPONSE)</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>vi_CHAINSAWS</name>
                    <choiceReferences>CHAINSAWS_Pickuplist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>2.a.1.vi.	CHAINSAWS (select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_Is_the_store_in_the_E3_set</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>No</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>vi_CHAINSAWS_FREE_RESPONSE</name>
                    <dataType>String</dataType>
                    <fieldText>2.a.1.vi.	CHAINSAWS (FREE RESPONSE)</fieldText>
                    <fieldType>InputField</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>vi_CHAINSAWS</leftValueReference>
                            <operator>Contains</operator>
                            <rightValue>
                                <stringValue>(FREE RESPONSE)</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>vii_LIFESTYLE</name>
                    <choiceReferences>LIFESTYLE_Pickuplist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>2.a.1.vii.	LIFESTYLE (select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_Is_the_store_in_the_E3_set</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>No</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>vii_LIFESTYLE_FREE_RESPONSE</name>
                    <dataType>String</dataType>
                    <fieldText>2.a.1.vii.	LIFESTYLE (FREE RESPONSE)</fieldText>
                    <fieldType>InputField</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>vii_LIFESTYLE</leftValueReference>
                            <operator>Contains</operator>
                            <rightValue>
                                <stringValue>(FREE RESPONSE)</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>viii_BATTERY</name>
                    <choiceReferences>BATTERY_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>2.a.1.viii.	BATTERY (select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_Is_the_store_in_the_E3_set</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>No</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>viii_BATTERY_FREE_RESPONSE</name>
                    <dataType>String</dataType>
                    <fieldText>2.a.1.viii.	BATTERY (FREE RESPONSE)</fieldText>
                    <fieldType>InputField</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>viii_BATTERY_FREE_RESPONSE</leftValueReference>
                            <operator>Contains</operator>
                            <rightValue>
                                <stringValue>(FREE RESPONSE)</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>Mowers</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;font-size: 12px; font-family: Arial, sans-serif;&quot;&gt;3 . Mowers&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>a_Core_set_LM2156SP_LM2135SP_LM2125SP_LM2114</name>
                    <choiceReferences>Core_set_LM2156SP_LM2135SP_LM2125S_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>3.a.	Core set (LM2156SP, LM2135SP, LM2125SP, LM2114)</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>X1_What_is_stocked_Select_all_that_apply</name>
                    <choiceReferences>What_is_stocked_Select_all_that_app_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>3.a.1.	What is stocked? (Select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>a_Core_set_LM2156SP_LM2135SP_LM2125SP_LM2114</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>No</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>Rider</name>
                    <fieldText>&lt;p&gt;4 . Rider&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>a_Stocking</name>
                    <choiceReferences>Riders_Stocking_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>4.a.	Stocking</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>X1_Which_models_Select_all_that_apply</name>
                    <choiceReferences>Which_models_Select_all_that_apply_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>4.a.1.	Which models (Select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>a_Stocking</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X1_Elliptical_opportunity</name>
                    <choiceReferences>Elliptical_opportunity_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>4.a.1.	Elliptical opportunity?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>a_Stocking</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>No</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>Lifestyle</name>
                    <fieldText>&lt;p&gt;5 . Lifestyle&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>a_Are_stocking_beyond_L2_3</name>
                    <choiceReferences>Are_stocking_beyond_L2_3_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>5.a.	Are stocking (beyond L2/3)?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>What_Items</name>
                    <fieldText>&lt;p&gt;5.a.1 . What Items? (select all that apply)&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>a_Are_stocking_beyond_L2_3</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>a_Portable_Power_nested</name>
                    <choiceReferences>Portable_Power_nested_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>5.a.1.a.	Portable Power (nested) (select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>a_Are_stocking_beyond_L2_3</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>b_Bikes_nested</name>
                    <choiceReferences>Bikes_nested_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>5.a.1.b.	Bikes (nested) (select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>a_Are_stocking_beyond_L2_3</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>c_Pressure_Washer_nested</name>
                    <choiceReferences>Pressure_Washer_nested_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>5.a.1.c.	Pressure Washer (nested) (select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>a_Are_stocking_beyond_L2_3</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>d_Wet_Dry_Vac</name>
                    <choiceReferences>Wet_Dry_Vac_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>5.a.1.d.	Wet Dry Vac (select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>a_Are_stocking_beyond_L2_3</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>e_Lights</name>
                    <choiceReferences>Lights_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>5.a.1.e.	Lights (select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>a_Are_stocking_beyond_L2_3</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>f_Augers</name>
                    <choiceReferences>Augers_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>5.a.1.f.	Augers (select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>a_Are_stocking_beyond_L2_3</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X5_a_1_g_Misting_Fan</name>
                    <choiceReferences>Misting_Fan_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>5.a.1.g.	Misting Fan</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>a_Are_stocking_beyond_L2_3</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X6_Snow</name>
                    <choiceReferences>Snow_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>6.	Snow</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>i_What_select_all_that_apply</name>
                    <choiceReferences>Snow_What_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>6.i.	What? (select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X6_Snow</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X7_Commercial_Opportunity</name>
                    <choiceReferences>Commercial_Opportunity_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>7.	Commercial Opportunity</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>i_Fulfill_Commercial_Guidelines</name>
                    <choiceReferences>Fulfill_Commercial_Guidelines_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>7.i.	Fulfill Commercial Guidelines?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X7_Commercial_Opportunity</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>a_Opportunity_to_stock_in_store</name>
                    <choiceReferences>Opportunity_to_stock_in_store_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>7.i.a.	Opportunity to stock in store?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>i_Fulfill_Commercial_Guidelines</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <fields>
            <name>PROMO_Execution_drive_sales</name>
            <fieldText>PROMO Execution – drive sales</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>PROMO_Execution_drive_sales_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>X1_Is_this_store_participating_in_ALL_active_promos</name>
                    <choiceReferences>Is_this_store_participating_in_ALL_ac_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>1.	Is this store participating in ALL active promos?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>true</isRequired>
                </fields>
                <fields>
                    <name>X1_i_How_are_they_participating_select_all_that_apply</name>
                    <choiceReferences>How_are_they_participating_select_a_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>1.i.	How are they participating? (select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_Is_this_store_participating_in_ALL_active_promos</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X1_i_How_are_they_participating_FREE_RESPONSE</name>
                    <dataType>String</dataType>
                    <fieldText>1.i.	How are they participating? (FREE RESPONSE)</fieldText>
                    <fieldType>InputField</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_i_How_are_they_participating_select_all_that_apply</leftValueReference>
                            <operator>Contains</operator>
                            <rightValue>
                                <stringValue>Other - Free response</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>i_Are_they_participating_in_ANY_active_promos</name>
                    <choiceReferences>Are_they_participating_in_ANY_active_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>1.i.	Are they participating in ANY active promos?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_Is_this_store_participating_in_ALL_active_promos</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>No</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X1_i_a_Why_not_All</name>
                    <choiceReferences>Why_not_All_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>1.i.a.	Why not All?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>i_Are_they_participating_in_ANY_active_promos</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X1_i_a_Why_not_All_FREE_RESPONSE</name>
                    <dataType>String</dataType>
                    <fieldText>1.i.a.	Why not All? (FREE RESPONSE)</fieldText>
                    <fieldType>InputField</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_i_a_Why_not_All</leftValueReference>
                            <operator>Contains</operator>
                            <rightValue>
                                <stringValue>Other - Free response</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X1_i_a_Why_not</name>
                    <choiceReferences>Why_not_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>1.i.a.	Why not?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>i_Are_they_participating_in_ANY_active_promos</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>No</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X1_i_a_Why_not_FREE_RESPONSE</name>
                    <dataType>String</dataType>
                    <fieldText>1.i.a.	Why not? (FREE RESPONSE)</fieldText>
                    <fieldType>InputField</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_i_a_Why_not</leftValueReference>
                            <operator>Contains</operator>
                            <rightValue>
                                <stringValue>Other - Free response</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <fields>
            <name>SELL_IN_opportunities</name>
            <fieldText>SELL IN opportunities.</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>SELL_IN_opportunities_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>X1_Is_the_decision_maker_on_site</name>
                    <choiceReferences>Is_the_decision_maker_on_site_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>1.	Is the decision maker on site?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>true</isRequired>
                </fields>
                <fields>
                    <name>X1_i_Is_there_an_opportunity_to_prompt_restock_from_RSC</name>
                    <choiceReferences>Is_there_an_opportunity_to_prompt_res_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>1.i.	Is there an opportunity to prompt restock from RSC?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_Is_the_decision_maker_on_site</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X1_i_a_What_is_the_cause_for_restocking</name>
                    <choiceReferences>What_is_the_cause_for_restocking_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>1.i.a.	What is the cause for restocking?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_i_Is_there_an_opportunity_to_prompt_restock_from_RSC</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X1_ii_Is_there_an_opportunity_to_bring_in_new_SKUs</name>
                    <choiceReferences>Is_there_an_opportunity_to_bring_in_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>1.ii.	Is there an opportunity to bring in new SKUs?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_Is_the_decision_maker_on_site</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X1_ii_a_Which_SKUs_select_all_that_apply</name>
                    <choiceReferences>Which_SKUs_select_all_that_apply_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>1.ii.a.	Which SKUs? (select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_ii_Is_there_an_opportunity_to_bring_in_new_SKUs</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X1_ii_a_Why_not_select_all_that_apply</name>
                    <choiceReferences>Why_not_select_all_that_apply_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>1.ii.a.	Why not (select all that apply)</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_ii_Is_there_an_opportunity_to_bring_in_new_SKUs</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>No</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X1_iii_Is_there_an_opportunity_to_expand_the_EGO_Set</name>
                    <choiceReferences>Is_there_an_opportunity_to_expand_t_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>1.iii.	Is there an opportunity to expand the EGO Set</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_Is_the_decision_maker_on_site</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X1_iii_a_What</name>
                    <choiceReferences>What_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>1.iii.a.	What?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_iii_Is_there_an_opportunity_to_expand_the_EGO_Set</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <fields>
            <name>COMPETETOR_insights</name>
            <fieldText>COMPETETOR insights</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>COMPETETOR_insights_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>X1_Any_new_products_from_competitors</name>
                    <choiceReferences>Any_new_products_from_competitors_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>1.	Any new products from competitors?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>true</isRequired>
                </fields>
                <fields>
                    <name>X1_Any_new_products_from_competitors_FREE_RESPONSE</name>
                    <dataType>String</dataType>
                    <fieldText>1. Any new products from competitors? (FREE RESPONSE)</fieldText>
                    <fieldType>InputField</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X1_Any_new_products_from_competitors</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes - Describe(free response)</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X2_Any_new_or_notable_merchandising_from_competitors</name>
                    <choiceReferences>Any_new_or_notable_merchandising_from_Picklist</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>2.	Any new or notable merchandising from competitors?</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>true</isRequired>
                </fields>
                <fields>
                    <name>X2_Any_new_or_notable_merchandising_from_competitors_FREE_RESPONSE</name>
                    <dataType>String</dataType>
                    <fieldText>2.	Any new or notable merchandising from competitors? (FREE RESPONSE)</fieldText>
                    <fieldType>InputField</fieldType>
                    <isRequired>false</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>X2_Any_new_or_notable_merchandising_from_competitors</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes - Describe(free response)</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>X3_What_s_the_current_competitor</name>
                    <choiceReferences>What_is_the_current_dealer_choice</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>3. What&apos;s the current competitor?</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <helpText>&lt;p&gt;chervon???&lt;/p&gt;</helpText>
                    <isRequired>true</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <fields>
            <name>Retailer_requests</name>
            <fieldText>Retailer requests</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Retailer_requests_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>X1_FREE_RESPONSE</name>
                    <fieldText>1. FREE RESPONSE</fieldText>
                    <fieldType>LargeTextArea</fieldType>
                    <isRequired>true</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <fields>
            <name>Upload_File</name>
            <fieldText>Upload File</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Upload_File_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>uploadForServey</name>
                    <extensionName>c:ccmUploadFileForSurvey</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <outputParameters>
                        <assignToReference>fileIds</assignToReference>
                        <name>fileIds</name>
                    </outputParameters>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Visit_Survey_Created</name>
        <label>Visit Survey Created</label>
        <locationX>182</locationX>
        <locationY>1274</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Success_Information</name>
            <fieldText>&lt;p&gt;Your Visit Survey is Successfully Created.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>close_Survey</name>
            <extensionName>c:ccmCloseSurvey</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Store_Location_Records</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>FileIdList</name>
        <dataType>Picklist</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>fileIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
