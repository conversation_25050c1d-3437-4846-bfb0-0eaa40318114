/* eslint-disable padding-line-between-statements */
({
    doInit: function(component, event, helper){
        console.log('log1');
        component.set('v.isBusy', true);
        component.set('v.currencySymbol', $A.get("$Locale.currencyCode"));
        //component.set('v.currencySymbol', '$');
        console.log('log2');
        var approvalOpt = [ 
            {'label': $A.get("$Label.c.CCM_Portal_Approve"), 'value': 'Approved'},
            {'label': $A.get("$Label.c.CCM_Portal_Reject"), 'value': 'Rejected'}];
        component.set('v.approvalOpt', approvalOpt);

        //Submitted-Order Processing-Partial Shipment-Ship Complete
        var pathData1 = [
            {label: $A.get("$Label.c.CCM_Portal_NewOrder"), icon: 'edit_form'},
            {label: $A.get("$Label.c.CCM_Portal_Submitted"), icon: 'locker_service_api_viewer'},
            {label: $A.get("$Label.c.CCM_Portal_OrderProcessing"), icon: 'privately_shared'},
            {label: $A.get("$Label.c.CCM_Portal_PartialShipment"), icon: 'travel_and_places'},
            {label: $A.get("$Label.c.CCM_Portal_ShipComplete"), icon:'success'}
        ];
        component.set('v.processData', pathData1);
        console.log('log3');
        var recordId = helper.getUrlParameter('recordId');
        console.log('record Id--->'+recordId);
        var action = component.get("c.getData");
        action.setParam('recordId', recordId);
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log('state--->'+state);
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if(results){
                    component.set('v.quotation', results.order);
                    if(!results.order.totalQuantity){
                        component.set('v.quotation.totalQuantity', 0)
                    }
                    // component.set('v.quotation.freightFeeWaived', Number(helper.currencyFormat(component.get('v.quotation.freightFeeWaived'))).toFixed(2))
                    // component.set('v.quotation.extraFreightFeeToBeWaived', Number(helper.currencyFormat(component.get('v.quotation.extraFreightFeeToBeWaived'))).toFixed(2))
                    component.set('v.quotation.freightFeeWaived', Number(component.get('v.quotation.freightFeeWaived')).toFixed(2))
                    component.set('v.quotation.extraFreightFeeToBeWaived', Number(component.get('v.quotation.extraFreightFeeToBeWaived')).toFixed(2))
                    results.orderItems.forEach(function(e){
                        if(results.order.orgCode != 'CCA'){
                            e.caseQty = e.caseQty ? e.caseQty : 1;
                        }else{
                            e.caseQty = 1;
                        }
                    });
                    component.set('v.orderItemList', results.orderItems);
                    component.set('v.currentStep', results.currentStep);
                    component.set('v.customerId', results.order.customerId);
                    component.set('v.brandScope', results.order.brandScopeName);
                    component.set('v.paymentTermVal', results.paymentTermVal);
                    component.set('v.freightTermVal', results.freightTermVal);
                    
                    if(results.order.orgCode === 'CCA'){
                        component.set('v.isCCA', true);
                    }else{
                        component.set('v.isCCA', false);
                    }
                    component.set('v.showTax', results.showTax);
                }
            } else {
                var errors = response.getError();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    doBack: function(component){
        var url = window.location.origin + '/s/orderinformation';;
        window.open(url,'_self'); 
    }
})