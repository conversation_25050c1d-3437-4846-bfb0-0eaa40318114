<!--
 - Created by gluo006 on 7/15/2019.
 -->

<aura:component description="CCM_ProductRegistration" implements="forceCommunity:availableForAllPageTypes"
                access="global" controller="CCM_ProductRegistration">
    <!--owner information-->

    <aura:attribute name="organizationName" type="String" default=""/>
    <aura:attribute name="firstName" type="String" default=""/>
    <aura:attribute name="originalFirstName" type="String" default=""/>
    <aura:attribute name="lastName" type="String" default=""/>
    <aura:attribute name="nameInputDisable" type="Boolean" default="false" />
    <aura:attribute name="originalLastName" type="String" default=""/>
    <aura:attribute name="addressLine1" type="String" default=""/>
    <aura:attribute name="emailAddress" type="String" default=""/>
    <aura:attribute name="phone" type="String" default=""/>
    <aura:attribute name="zipPostalCode" type="String" default=""/>
    <aura:attribute name="city" type="String" default=""/>
    <aura:attribute name="state" type="String" default=""/>
    <aura:attribute name="country" type="String" default=""/>

    <!--product information-->
    <aura:attribute name="modelNumber" type="String" default="product"/>
    <aura:attribute name="ProductList" type="List" default="['ddd', 'ddd', 'ddd']"/>
    <aura:attribute name="masterProductObj" type="Map" default="{'Name':'', Id:''}"/>
    <aura:attribute name="purchasePlaceObj" type="Map" default="{'Name':'', Id:''}"/>
    <aura:attribute name="brandList" type="List" default=""/>
    <aura:attribute name="brandName" type="String" default="EGO"/>
    <aura:attribute name="purchasePlaceList" type="List" default=""/>
    <aura:attribute name="purchaseUseTypeList" type="List" default=""/>
    <aura:attribute name="productListData" type="Object"/>
    <aura:attribute name="firstSave" type="Boolean" default="true" />
    <aura:attribute name="uploadFinished" type="Boolean" default="false" />
    <aura:attribute name="uploadFileName" type="String" default="" />
    <aura:attribute name="contentId" type="String" default="" />


    <aura:attribute name="isReceiveInfo" type="List" default="[
    {'label': 'Yes', 'value': 'true'},
    {'label': 'No', 'value': 'false'}
    ]"/>
    <aura:attribute name="isVisitWebsite" type="List" default="[
    {'label': 'Yes', 'value': 'true'},
    {'label': 'No', 'value': 'false'}
    ]"/>
    <aura:attribute name="options" type="List" default="[]"/>
    <aura:attribute name="visitWebsiteValue" type="String" default="true"/>
    <aura:attribute name="receiveInfoValue" type="String" default="true"/>
    <aura:attribute name="targetRecord" type="Object" access="private"/>
    <aura:attribute name="targetFields" type="Object" access="private"/>
    <aura:attribute name="targetError" type="String" access="private"/>
    <aura:attribute name="lostReceipt" type="Boolean" access="private" default="false"/>

    <!--file upload-->
    <aura:attribute name="multiple" type="Boolean" default="true"/>
    <aura:attribute name="accept" type="List" default="['.jpg', '.png']"/>
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="disabled" type="Boolean" default="false"/>

    <!-- Survey -->
    <aura:attribute name="isShowSurveyConfirmation" type="Boolean" default="false" />
    <aura:attribute name="isShowSurveyContent" type="Boolean" default="false" />
    <aura:attribute name="surveyId" type="String" default=""/>
    <aura:attribute name="surveyTitle" type="String" default=""/>
    <aura:attribute name="surveyComments" type="String" default=""/>
    <aura:attribute name="customerId" type="String" default=""/>
    <aura:attribute name="warrantyId" type="String" default=""/>
    <aura:attribute name="surveyData" type="List" default="" />
    <aura:attribute name="endUserId" type="String" default="" />

    <aura:attribute name="selectedCustomerType" type="String" default="" />
    <aura:attribute name="customerTypeList" type="List" default="[]" />
    <aura:attribute name="organizationNameCommercial" type="String" default="" />
    <aura:attribute name="customerTypeInputDisable" type="Boolean" default="false" />
    <aura:attribute name="organizationNameInputDisable" type="Boolean" default="false" />

    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <aura:handler event="c:CCM_ProductLookUpChange" name="getProductChange" action="{!c.getMasterProductId}"/>

    <!--<aura:handler event="c:CCM_RebindListData" action="{!c.init}"/>-->
    
    <section class="slds-p-around_x-small">
        <lightning:spinner aura:id="spinner" class="slds-hide" alternativeText="Loading" size="medium" variant="brand"/>
        <article class="slds-card">
            <div class="slds-grid">
            <header class="slds-media slds-media_center slds-has-flexi-truncate">
                <div class="slds-media__body">
                    <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                        <span class="section-header-title slds-p-horizontal--small slds-truncate"
                              title="{!$Label.c.CCM_Portal_OrganizationInformation}">
                                <span><strong>{!$Label.c.CCM_Portal_OrganizationInformation}</strong></span>
                         </span>
                    </h2>
                </div>
            </header>
            </div>
            <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">

            <div class="slds-grid slds-wrap slds-align_absolute-center width80">
                <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                    <div>
                        <strong>{!$Label.c.CCM_Portal_Sellingorganizationname}: </strong>
                        <span> {!v.organizationName}</span>
                    </div>
                </div>
                <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                    <div>
                        <strong>{!$Label.c.CCM_Portal_Registrationsource}: </strong>
                        <span> {!$Label.c.CCM_Portal_BusinessPortal}</span>
                    </div>
                </div>
            </div>
        </div>
        </article>
        <article class="slds-card">
            <div class="slds-grid">
            <header class="slds-media slds-media_center slds-has-flexi-truncate">
                <div class="slds-media__body">
                    <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                        <span class="section-header-title slds-p-horizontal--small slds-truncate"
                              title="Owner information">
                                <span><strong>{!$Label.c.CCM_Portal_OwnerInformationTips}</strong></span>
                         </span>
                    </h2>
                </div>
            </header>
            </div>
            <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">

            <div class="slds-grid slds-wrap slds-align_absolute-center width80">
                <div class="slds-text-align--right">
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        <lightning:input aura:id="emailAddress" label="{!$Label.c.CCM_Portal_EmailAddress + ' : '}" value="{!v.emailAddress}"  onblur="{!c.emailOrPhoneGetCustomerInfo}" messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255"/>
                        <div aura:id="emailAddress-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_RegistrationmustincludeEmailandorPhone}</div>
                    </div>
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        <lightning:input aura:id="phone" name="input1" label="{!$Label.c.CCM_Portal_Phone + ' : '}" value="{!v.phone}" onblur="{!c.emailOrPhoneGetCustomerInfo}" messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255" pattern="[0-9]*" messageWhenPatternMismatch="{!$Label.c.CCM_Portal_Enternumbersonlynosymbols}" />
                        <div aura:id="phone-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_RegistrationmustincludeEmailandorPhone}</div>
                    </div>
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        <lightning:input aura:id="firstName" label="{!$Label.c.CCM_Portal_Firstname + ' : '}" value="{!v.firstName}" class="field-required slds-text-align_right" messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255" disabled="{!v.nameInputDisable}"/>
                        <div aura:id="firstName-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        <div aura:id="firstName-error" class="error-text slds-hide">{!$Label.c.CCM_Portal_FirstNameAlreadyExistCannotChange}</div>
                    </div>
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        <lightning:input aura:id="lastName" name="input1" label="{!$Label.c.CCM_Portal_Lastname + ' : '}" value="{!v.lastName}" class="field-required slds-text-align_right" messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255" disabled="{!v.nameInputDisable}"/>
                        <div aura:id="lastName-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        <div aura:id="lastName-error" class="error-text slds-hide">{!$Label.c.CCM_Portal_LirstNameAlreadyExistCannotChange}</div>
                    </div>
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        <lightning:input aura:id="country" name="input1" label="{!$Label.c.CCM_Portal_Country + ' : '}" value="{!v.country}" messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255"/>
                    </div>
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-flex field-required">
                        <lightning:select label="{!$Label.c.Customer_Type + ' : '}" aura:id="customerType" value="{!v.selectedCustomerType}" onchange="{!c.handleCustomerTypeChange}" disabled="{!v.customerTypeInputDisable}">
                            <aura:iteration items="{!v.customerTypeList}" var="customerType">
                                <option text="{!customerType.name}" value="{!customerType.value}"></option>
                            </aura:iteration>
                        </lightning:select>
                        <div aura:id="customerType-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                    </div>
                </div>
                <div class="slds-text-align--right">
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-hidden">
                        <lightning:input name="input1" label="" value="" />
                    </div>
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        <lightning:input aura:id="zipPostalCode" name="zipPostalCode" label="{!$Label.c.CCM_Portal_ZipPostalcode + ' : '}" value="{!v.zipPostalCode}" onblur="{!c.getAddressInfo}" messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255"/>
                    </div>
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        <lightning:input aura:id="city" name="input1" label="{!$Label.c.CCM_Portal_City + ' : '}" value="{!v.city}"  messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255"/>
                    </div>
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        <lightning:input aura:id="state" name="input1" label="{!$Label.c.CCM_Portal_StateProvince + ' : '}" value="{!v.state}"  messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255"/>
                    </div>
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small addressPosition">
                        <lightning:input aura:id="street" name="input1" label="{!$Label.c.CCM_Portal_Address + ' : '}" value="{!v.addressLine1}"  messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255"/>
                    </div>
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small ">
                        <lightning:input aura:id="organizationName" name="input1" label="{!$Label.c.Organization_Name + ' : '}" value="{!v.organizationNameCommercial}" class="{!v.selectedCustomerType=='Commercial' ? 'field-required slds-text-align_right' : 'slds-text-align_right'}"  messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255" disabled="{!v.organizationNameInputDisable}"/>
                        <div aura:id="organizationName-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                    </div>
                </div>
            </div>
        </div>
        </article>
        <article class="slds-card">
            <div class="slds-grid">
                <header class="slds-media slds-media_center slds-has-flexi-truncate">
                    <div class="slds-media__body">
                        <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                        <span class="section-header-title slds-p-horizontal--small slds-truncate"
                              title="{!$Label.c.CCM_Portal_ProductInformation}">
                                <span><strong>{!$Label.c.CCM_Portal_ProductInformation}</strong></span>
                         </span>
                        </h2>
                    </div>
                </header>
            </div>
            <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small productSection slds-clearfix">
                <div class="slds-grid slds-wrap slds-align_absolute-center spaceAround">
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small datePicker field-required">
                        <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Purchasedate + ': '}</label>
                        <ui:inputDate aura:id="purchaseDate" displayDatePicker="true" format="MM-dd-yyyy" />
                        <div aura:id="purchaseDate-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        <div aura:id="purchaseDate-error-value" class="error-text slds-hide">{!$Label.c.CCM_Portal_PurchaseDateNeedToEarlierThanToday}</div>
                    </div>
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-flex field-required">
                        <lightning:select label="{!$Label.c.CCM_Portal_Brand + ': '}" aura:id="brand" value="{!v.brandName}" >
                            <aura:iteration items="{!v.brandList}" var="brand">
                                <option text="{!brand}" value="{!brand}"></option>
                            </aura:iteration>
                        </lightning:select>
                        <div aura:id="brand-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                    </div>
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small field-required">
                        <c:CCM_Community_LookUp aura:id="masterProduct"
                                                fieldName="{!$Label.c.CCM_Portal_ModelNumber + ':'}"
                                                brandName="{!v.brandName}"
                                                selectedValue="{!v.masterProductObj}"
                        />
                        <div aura:id="masterProduct-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                    </div>
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-flex purchasePlace field-required">
                        <c:CCM_Community_LookUp  fieldName="{!$Label.c.CCM_Portal_Purchaseplace + ':'}"
                                                 brandName="{!v.brandName}"
                                                 selectedValue="{!v.purchasePlaceObj}"
                                                 aura:id="purchasePlace"

                        />
                        <div aura:id="purchasePlace-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                    </div>
                </div>
                <div class="slds-wrap slds-grid slds-grid--vertical-align-center slds-m-vertical_medium field-required">

                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium">
                        <lightning:select label="{!$Label.c.CCM_Portal_Primaryuse}" aura:id="purchaseUseType">
                            <aura:iteration items="{!v.purchaseUseTypeList}" var="purchaseUseType">
                                <option text="{!purchaseUseType.label}" value="{!purchaseUseType.value}"></option>
                            </aura:iteration>
                        </lightning:select>
                        <div aura:id="purchaseUseType-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                    </div>
                </div>
                <div class="table_container slds-size_1-of-1 slds-medium-size_1-of-1 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small slds-scrollable_x">
                    <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_fixed-layout slds-table_resizable-cols">
                        <lightning:spinner aura:id="proListSpinner" class="slds-hide" alternativeText="{!$Label.c.CCM_Portal_Loading}" size="small" variant="brand" />
                        <thead>
                        <tr class="slds-line-height_reset" aria-selected="false">
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 5%">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="slds-truncate" title=""></span>
                                    </div>
                                </a>
                            </th>
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 15%">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="slds-truncate" title="{!$Label.c.CCM_Portal_ProductName}">{!$Label.c.CCM_Portal_ProductName}</span>
                                    </div>
                                </a>
                            </th>
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 14%">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="slds-truncate" title="{!$Label.c.CCM_Portal_ProductCode}">{!$Label.c.CCM_Portal_ProductCode}</span>
                                    </div>
                                </a>
                            </th>
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 22%">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="slds-truncate" title="{!$Label.c.CCM_Portal_SerialNumber}">{!$Label.c.CCM_Portal_SerialNumber}</span>
                                    </div>
                                </a>
                            </th>
                        </tr>
                        </thead>
                        <tbody>

                        <aura:iteration items="{!v.productListData.proList}" var="projectList" indexVar="index">
                            <tr aria-selected="false" class="slds-hint-parent">
                                <td class="slds-text-align_right" role="gridcell">
                                    <lightning:input type="checkbox" label="" checked="{!projectList.isSelect}"/>
                                </td>
                                <td data-label="Product Name" scope="row">
                                    <div class="slds-truncate" title="{!projectList.warrantyItem.Product_Name__c}">
                                        {!projectList.warrantyItem.Product_Name__c}
                                    </div>
                                </td>
                                <td data-label="Product Code">
                                    <aura:if isTrue="{!!projectList.hasReplace}">
                                        <div class="slds-truncate" title="{!projectList.warrantyItem.Product_Code__c}">
                                            {!projectList.warrantyItem.Product_Code__c}
                                        </div>
                                    </aura:if>
                                    <aura:if isTrue="{!projectList.hasReplace}">
                                        <div class="slds-truncate">
                                            <lightning:select aura:id="proItemSelect" name="{!index}" onchange="{!c.handleChangeProItem}">
                                                <aura:iteration items="{!projectList.replaceCodeList}" var="codeInfo">
                                                    <option value="{!codeInfo.value}" selected="{!projectList.warrantyItem.Product_Code__c == codeInfo.value}">{!codeInfo.label}</option>
                                                </aura:iteration>
                                            </lightning:select>
                                        </div>
                                    </aura:if>
                                </td>
                                <td data-label="Serial Number">
                                    <div class="slds-truncate" title="{!projectList.warrantyItem.Serial_Number__c}" data-index="{!index}">
                                        <lightning:input label="" value="{!projectList.warrantyItem.Serial_Number__c}" onblur="{!c.checkSN}" data-index="{!index}" />
                                        <span class="required">{!projectList.snFormatErrorMessage}</span>
                                    </div>
                                </td>
                            </tr>
                        </aura:iteration>
                        </tbody>
                    </table>
                </div>
                <div class="slds-wrap receiptCon slds-float--right">
                    <lightning:input class="fileUpload" name="" type="file" label="{!$Label.c.CCM_Portal_Uploadyourreceipt + ' : '}" multiple="true" accept="image/png, .pdf" onchange="{! c.handleFilesChange }"/>
                    <aura:if isTrue="{!v.uploadFinished}">
                        <p class="uploadFinished" title="{!v.uploadFileName}"><span class="fileName">{!v.uploadFileName}</span><a class="delete" onclick="{!c.deleteReceipt}">{!$Label.c.CCM_Portal_Delete}</a></p>
                    </aura:if>
                    <div class="slds-m-top_xx-small slds-text-align_center">
                        <lightning:input class="" type="checkbox" label="{!$Label.c.CCM_Portal_LostReceipt}" name="" checked="{!v.lostReceipt}" aura:id="lostReceipt"/>
                    </div>
                </div>
            </div>
        </article>
        <div class="slds-align_absolute-center slds-m-top_small slds-p-horizontal_x-large">
            <lightning:button class="{!v.firstSave ? 'slds-p-horizontal_xx-large' : 'disabled slds-p-horizontal_xx-large'}" variant="brand"  label="{!$Label.c.CCM_Portal_Submit}" title="{!$Label.c.CCM_Portal_Submit}" onclick="{!c.onClickSubmit}" disabled="{!!v.firstSave}"/>
        </div>

    </section>

    <c:CCM_Modal size="medium" isShow="{!v.isShowSurveyConfirmation}" onClose="{!c.closeModal}" title="{!$Label.c.CCM_Portal_QuickSurvey}">
            <div class="text-center">
                <br/>
                <h2 class="surveyHeader">{!v.surveyTitle}</h2>
                <p class="slds-text-size_medium surveyContent"><lightning:formattedRichText value="{!v.surveyComments}"/></p>
            </div>

        <aura:set attribute="footer">
            <lightning:button class="slds-button slds-button_brand" onclick="{!c.doSurvey}">
                {!$Label.c.CCM_Portal_OptIntoExtendedWarranty}
            </lightning:button> 
            <lightning:button class="slds-button slds-button_neutral" onclick="{!c.doCancelSurvey}">
                {!$Label.c.CCM_Portal_Nothanks}
            </lightning:button>
        </aura:set>
    </c:CCM_Modal>

    <c:CCM_Modal size="medium" isShow="{!v.isShowSurveyContent}" onClose="{!c.closeModal}" title="{!$Label.c.CCM_Portal_QuickSurvey}">
        <lightning:layout class="slds-size_1-of-1">
            <c:ccmSurvey
                aura:id="survey"
                surveyId="{!v.surveyId}" 
                customerId="{!v.customerId}" 
                firstName="{!v.firstName}" 
                lastName="{!v.lastName}" 
                addressLine1="{!v.addressLine1}" 
                emailAddress="{!v.emailAddress}" 
                phone="{!v.phone}" 
                zipPostalCode="{!v.zipPostalCode}" 
                city="{!v.city}" 
                state="{!v.state}" 
                country="{!v.country}" 
                onpasssurvey="{!c.handleSurveyData}"
            />
        </lightning:layout>
        <aura:set attribute="footer">
            <lightning:button class="slds-button slds-button_brand" onclick="{!c.onClickSurveySave}">
                ={!$Label.c.CCM_Portal_Save}
            </lightning:button> 
            <lightning:button class="slds-button slds-button_neutral" onclick="{!c.doCancelSurvey}">
                {!$Label.c.CCM_Portal_Cancel}
            </lightning:button>
        </aura:set>
    </c:CCM_Modal>

</aura:component>
