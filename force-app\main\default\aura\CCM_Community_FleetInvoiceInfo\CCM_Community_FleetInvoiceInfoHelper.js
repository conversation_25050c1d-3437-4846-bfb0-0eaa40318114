({
    uploadReceiptHelper: function(component, file){
        var self = this;
        // create a FileReader object
        var objFileReader = new FileReader();
        // set onload function of FileReader object
        objFileReader.onload = $A.getCallback(function() {
            var fileContents = objFileReader.result;
            var base64 = 'base64,';
            var dataStart = fileContents.indexOf(base64) + base64.length;
            fileContents = fileContents.substring(dataStart);
            // console.log('fileContents', fileContents);
            if($A.util.isEmpty(fileContents)){
                self.showToast(null, $A.get("$Label.c.CCM_Portal_Pleaseuploadavalidfile"), "warning");
                return;
            }
            var fileName = file.name;
            var fleetClaimId = component.get("v.fleetClaim.id");
            var action = component.get('c.uploadFile');
            action.setParams({
                'fleetClaimId': fleetClaimId,
                'fileName': fileName,
                'content': fileContents
            });
            self.showEle(component, 'spinner');
            action.setCallback(this, function (response) {
                var state = response.getState();
                if (state === 'SUCCESS' ) {
                    var auraResponse = response.getReturnValue();
                    if(auraResponse.code === 200){
                        var fileInfo = auraResponse.data;
                        var fileInfoList = component.get("v.fleetClaim.fileInfoList");
                        fileInfoList.push(auraResponse.data);
                        component.set("v.fleetClaim.fileInfoList", fileInfoList);
                        self.showToast(null, $A.get("$Label.c.CCM_Portal_Uploadsuccessful"), "success");
                    }else{
                        self.showToast(null, auraResponse.message, "error");
                    }
                }else{
                    self.showToast(null, JSON.stringify(response.getError()), "error");
                }
                self.hideEle(component, 'spinner');
            });
            $A.enqueueAction(action);
        });
        objFileReader.readAsDataURL(file);
    },
    deleteReceiptHelper: function(component, rowIndex){
        var self = this;
        var fileInfoList = component.get("v.fleetClaim.fileInfoList");
        var fileInfo = fileInfoList[rowIndex];
        var action = component.get('c.deleteFile');
        action.setParams({
            'fileId': fileInfo.contentId
        });
        self.showEle(component, 'spinner');
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === 'SUCCESS' ) {
                var auraResponse = response.getReturnValue();
                if(auraResponse.code === 200){
                    fileInfoList.splice(rowIndex, 1);
                    component.set('v.fleetClaim.fileInfoList', fileInfoList);
                    self.showToast(null, $A.get("$Label.c.CCM_Portal_Deletesuccessful"), "success");
                }else{
                    self.showToast(null, auraResponse.message, "error");
                }
            }else{
                self.showToast(null, JSON.stringify(response.getError()), "error");
            }
            self.hideEle(component, 'spinner');
        });
        $A.enqueueAction(action);
    },
    showToast: function(title, message,type) {
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams({
            "title": title,
            "message": message,
            "type": type
        });
        toastEvent.fire();
    },
    showEle: function(component, ele){
        $A.util.removeClass(
            component.find(ele),
            "slds-hide"
        );
    },
    hideEle: function(component, ele){
        $A.util.addClass(
            component.find(ele),
            "slds-hide"
        );
    }
})