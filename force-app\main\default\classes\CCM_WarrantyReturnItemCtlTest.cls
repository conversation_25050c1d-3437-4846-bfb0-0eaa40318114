@IsTest
public class CCM_WarrantyReturnItemCtlTest {
    @TestSetup
    static void makeData(){
        Test.startTest();
        // customer info
        String recordId = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' AND DeveloperName = 'Channel' LIMIT 1].Id;
        Account account = new Account();
        account.Name = 'TestCustomer';
        account.Distributor_or_Dealer__c = '2nd Tier Dealer';
        account.RecordTypeId = recordId;
        account.AccountNumber = '12345';
        account.ORG_Code__c = 'CNA';
        insert account;
        
        Contact contact = new Contact();
        contact.AccountId = account.Id;
        contact.LastName = 'xxx';
        contact.FirstName = 'YYY';
        contact.OwnerId = UserInfo.getUserId();
        insert contact;
        
        Account_Address__c billingAddress = new Account_Address__c();
        billingAddress.Customer__c = account.Id;
        billingAddress.X2nd_Tier_Dealer__c = account.Id;
        billingAddress.Active__c = true;
        billingAddress.Approval_Status__c = 'Approved';
        billingAddress.Contact__c = contact.Id;
        billingAddress.RecordTypeId = CCM_Constants.ADDRESS_BILLING_ADDRESS_RECORD_TYPE_ID;

        Account_Address__c shippingAddress = new Account_Address__c();
        shippingAddress.Customer__c = account.Id;
        shippingAddress.X2nd_Tier_Dealer__c = account.Id;
        shippingAddress.Active__c = true;
        shippingAddress.Approval_Status__c = 'Approved';
        shippingAddress.Contact__c = contact.Id;
        shippingAddress.RecordTypeId = CCM_Constants.ADDRESS_DROPSHIP_SHIPPING_ADDRESS_RECORD_TYPE_ID;

        insert new List<Account_Address__c> {shippingAddress, billingAddress};

        // pricebook info
        Pricebook2 pb = new Pricebook2();
        pb.Name = 'Standard Price Book';
        pb.IsActive = true;
        pb.Contract_Price_Book_OracleID__c = '11';
        pb.Price_Book_OracleID__c = '11';
        pb.Org_Code__c = 'CNA';
        insert pb;
        
        Product2 pro = new Product2();
        pro.Name = 'TestProduct111';
        pro.Brand_Name__c = 'EGO';
        pro.ProductCode = '********';
        pro.IsActive = true;
        pro.Source__c = 'PIM';
        pro.Description = pro.SF_Description__c;
        insert pro;

        Product2 pro2 = new Product2();
        pro2.Name = 'TestProduct222';
        pro2.Brand_Name__c = 'EGO';
        pro2.ProductCode = '1234567';
        pro2.IsActive = true;
        pro2.Source__c = 'EBS';
        insert pro2;
        pro2.Description = pro2.SF_Description__c;
        update pro2;
        
        Order objOrder = new Order();
        objOrder.AccountId = account.Id;
        objOrder.Order_Type__c = 'CNA Sales Order - USD';
        objOrder.Order_Status__c = 'CLOSED';
        objOrder.EffectiveDate = Date.today().addDays(-7); 
        objOrder.Status = 'Draft';
        insert objOrder;

        Order_Item__c item1 = new Order_Item__c();
        item1.Order__c = objOrder.Id;
        item1.Product__c = pro.Id;
        Order_Item__c item2 = new Order_Item__c();
        item2.Order__c = objOrder.Id;
        item2.Product__c = pro2.Id;
        insert new List<Order_Item__c> {item1, item2};

        // pricebookEntry info
        PricebookEntry pbe = new PricebookEntry();
        pbe.IsActive = true;
        pbe.Product2Id = pro.Id;
        pbe.UnitPrice = 1000;
        pbe.Pricebook2Id = pb.Id;
        pbe.UseStandardPrice = false;
        insert pbe;

        // authorized brand info
        Sales_Program__c salesProgram = new Sales_Program__c();
        salesProgram.Customer__c = account.Id;
        salesProgram.Approval_Status__c = 'Approved';
        salesProgram.Price_Book__c = pb.Id;
        salesProgram.Authorized_Brand_Name_To_Oracle__c = 'EGO_2020_Test';
        salesProgram.RecordTypeId = CCM_Contants.SALES_STANDARD_PROGRAM_RECORDTYPEID;
        salesProgram.Brands__c = 'EGO';
        insert salesProgram;
        
        Address_With_Program__c billto = new Address_With_Program__c();
        billto.Account_Address__c = billingAddress.Id;
        billto.Program__c = salesProgram.Id;
        
        Address_With_Program__c shipto = new Address_With_Program__c();
        shipto.Account_Address__c = shippingAddress.Id;
        // shipto.Program__c = salesProgram.Id;
        insert new List<Address_With_Program__c> {billto, shipto};

        Invoice__c invoice = new Invoice__c();
        invoice.Customer__c = account.Id;
        invoice.Invoice_Number__c = '112233';
        invoice.Total_Due__c = 100;
        insert invoice;

        Test.stopTest();
    }

    @isTest
    static void testGetBillingShipping() {
        String accountNumber = [SELECT Id, AccountNumber FROM Account LIMIT 1].AccountNumber;
        String billto = [SELECT Id, Name FROM Address_With_Program__c WHERE Account_Address__r.RecordTypeId = :CCM_Constants.ADDRESS_BILLING_ADDRESS_RECORD_TYPE_ID LIMIT 1].Name;
        String shipto = [SELECT Id, Name FROM Address_With_Program__c WHERE Account_Address__r.RecordTypeId = :CCM_Constants.ADDRESS_DROPSHIP_SHIPPING_ADDRESS_RECORD_TYPE_ID LIMIT 1].Name;
        CCM_WarrantyReturnItemCtl.getBillingShipping(accountNumber, billto, shipto);
    }

    @isTest
    static void testInsertWarrantyReturnItem() {
        String accountNumber = [SELECT Id, AccountNumber FROM Account LIMIT 1].AccountNumber;
        String billto = [SELECT Id, Name FROM Address_With_Program__c WHERE Account_Address__r.RecordTypeId = :CCM_Constants.ADDRESS_BILLING_ADDRESS_RECORD_TYPE_ID LIMIT 1].Name;
        String shipto = [SELECT Id, Name FROM Address_With_Program__c WHERE Account_Address__r.RecordTypeId = :CCM_Constants.ADDRESS_DROPSHIP_SHIPPING_ADDRESS_RECORD_TYPE_ID LIMIT 1].Name;
        String warrantyReturnItemStr = '[{"line":1,"accountNumber":"' + accountNumber + '","contactEmailAddress":"<EMAIL>;<EMAIL>","billTo":"' + billto + '","shipTo":"' + shipto +'","paymentMethod":"Deduction","debitMemoNumber":"112233","productCode":"1234567","invoicePrice":100,"quantity":1,"DIFRTV":"RTV","returnReason":"Defective"},{"productCode":"********","invoicePrice":100,"quantity":2,"DIFRTV":"DIF","returnReason":"Customer Satisfaction"}]';
        CCM_WarrantyReturnItemCtl.insertWarrantyReturnItem(warrantyReturnItemStr);
    }
}