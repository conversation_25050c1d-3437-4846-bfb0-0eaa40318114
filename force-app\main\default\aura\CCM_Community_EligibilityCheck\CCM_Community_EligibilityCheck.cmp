<!--
 - Created by gluo006 on 10/30/2019.
 -->

<aura:component description="CCM_Community_EligibilityCheck" implements="forceCommunity:availableForAllPageTypes,flexipage:availableForAllPageTypes" access="global" controller="CCM_EligibilityCheck">
    <aura:attribute name="currencySymbol" type="String" default="$"/>
    <aura:attribute name="brand" type="String" default=""/>
    <aura:attribute name="prodCondition" type="String" default=""/>
    <aura:attribute name="partsCondition" type="String" default=""/>
    <aura:attribute name="orderItemList" type="List" />
    <aura:attribute type="Integer" name="operationRow" />
    <aura:attribute type="String" name="productModel" />
    <aura:attribute name="disableBtn" type="Boolean" default="true"/>
    <aura:attribute name="checkResult" type="String" default=""/>
    <aura:attribute name="lampColor" type="String" default=""/>
    <aura:attribute name="prefixURL" type="String" default="/sfc/servlet.shepherd/version/renditionDownload?rendition=ORIGINAL_Png&amp;versionId="/>
    <aura:attribute name="vfHost" type="String" default="msdev--steven.lightning.force.com"/>
    <aura:attribute name="contents" type="List" default="[]"/>
    <aura:attribute name="explosiveDataList" type="List" default="[]"/>
    <aura:attribute name="productId" type="String" default=""/>
    <aura:attribute name="sequenceNo" type="String" default=""/>
    <aura:attribute name="versionList" type="List" default="[]"/>
    <aura:attribute name="version" type="String" default=""/>
    <aura:attribute name="clickNum" type="Integer" default="0"/>
    <aura:attribute name="isCanCheck" type="Boolean" default="true"/>
    <aura:attribute name="repairTime" type="String" default="0"/>

    <ltng:require styles="{!$Resource.swiperCSS}" />
    <ltng:require scripts="{!$Resource.swiperJS}"/>

    <aura:attribute name="brandList" type="List" default=""/>
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <aura:handler event="c:CCM_ProductLookUpChange" name="getProductChange" action="{!c.getProductId}"/>

    <section>
        <div class="slds-grid slds-m-bottom_large">
            <div class="width27 slds-m-right--medium">
                <lightning:select name="" label="{!$Label.c.CCM_Portal_Brand}" aura:id="brand" value="{!v.brand}" onchange="{!c.selectBrand}">
                    <option text="{!$Label.c.CCM_Portal_NonePlaceTips}" value=""></option>
                    <aura:iteration items="{!v.brandList}" var="brand">
                        <option text="{!brand}" value="{!brand}"></option>
                    </aura:iteration>
                </lightning:select>
            </div>
            <div class="width27">
                <!-- add haibo: french -->
                <c:CCM_AutoMatchPickList objectType="product2"
                                         label="{!$Label.c.CCM_Portal_ProductModel}"
                                         labelField="{! ($Label.c.CCM_Portal_Language == 'fr' ? 'Product_Name_French__c' : 'Name')}"
                                         inputValue="{!v.productModel}"
                                         labelField1="ProductCode"
                                         filterCondition='{!v.prodCondition}'
                                         fieldList="Name,Product_Name_French__c,Item_Number__c,serial_number__c,ProductCode"
                                         onSelect="{!c.onSelectProd}"/>
            </div>
            <div class="reminder">
                &nbsp;&nbsp;Please choose the product model shows on the nameplate without -FC/CS-
            </div>
        </div>

        <lightning:card class="mainContent">
            <p class="slds-p-horizontal_small">
            <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_fixed-layout slds-table_resizable-cols slds-table_striped" role="grid">
                <thead>
                <tr class="slds-line-height_reset">
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="width: 5%">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Line}">{!$Label.c.CCM_Portal_Line}</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth" scope="col">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Brand}">{!$Label.c.CCM_Portal_Brand}</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Model}">{!$Label.c.CCM_Portal_Model}</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_PartsNumber}">{!$Label.c.CCM_Portal_PartsNumber}</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_PartsName}">{!$Label.c.CCM_Portal_PartsName}</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Qty}">{!$Label.c.CCM_Portal_Qty}</span>
                            </div>
                        </a>
                    </th>
                    <th class="smallWidth" scope="col" style="width: 6%">
                        <div class="slds-truncate slds-assistive-text" title="{!$Label.c.CCM_Portal_Actions}"></div>
                    </th>
                </tr>
                </thead>
                <tbody>
                <aura:iteration items="{!v.orderItemList}" var="orderItem" indexVar="index">
                    <tr aria-selected="false" class="slds-hint-parent" id="{!index}" onmouseover="{!c.rowFocus}">
                        <th scope="row">
                            <div class="slds-truncate" title="">
                                {!index + 1}
                            </div>
                        </th>
                        <td role="gridcell" title="Brand">
                            <div class="slds-truncate">
                                {!orderItem.Brand__c}
                            </div>
                        </td>
                        <td role="gridcell" title="Brand">
                            <div class="slds-truncate">
                                {!orderItem.ProductCode}
                            </div>
                        </td>
                        <td role="gridcell" title="Name">
                            <c:CCM_Community_LookUp aura:id="partsProduct"
                                                    fieldName=""
                                                    index="{!index}"
                                                    productId="{!v.productId}"
                                                    inputVal="{!orderItem.itemNumber}"
                                                    selectedValue="{!v.majorIssueObj}"
                                                    pageName="eligibilityCheck"
                            />
                        </td>
                        <td role="gridcell" title="Brand">
                            <div class="slds-truncate">
                                {!orderItem.Name}
                            </div>
                        </td>
                        <td role="gridcell" title="{!$Label.c.CCM_Portal_Qty}">
                            <div class="slds-truncate clear-user-agent-styles" >
                                <lightning:input type="number" label=""  value="{!orderItem.quantity}" min="1" disabled="{!v.disableFlag}" onchange="{!c.calculateSubTotal}"/>
                            </div>
                        </td>
                        <td role="gridcell" data-index="{!v.index}"  onclick="{!c.handleDelete}">
                            <lightning:icon iconName="utility:delete" alternativeText="{!$Label.c.CCM_Portal_Delete}" size="x-small" />
                        </td>
                    </tr>
                </aura:iteration>
                </tbody>
            </table>
            </p>

            <div class="slds-m-vertical_small slds-m-horizontal_medium slds-border_bottom">
                <lightning:layout horizontalAlign="space" verticalAlign="center">
                    <lightning:layoutItem alignmentBump="right">
                        <div style="padding: 10px;">
                            <lightning:button label="{!$Label.c.CCM_Portal_AddItem}" iconName="utility:add" iconPosition="left" onclick="{!c.addItem}" disabled="{!v.disableBtn}"/>                   
                        </div>
                    </lightning:layoutItem>
                </lightning:layout>
            </div>

            <div class="slds-card__footer">
                <div class="slds-float--left"><a class="explosive" onclick="{!c.openModal}">{!$Label.c.CCM_Portal_ViewFromExplodedDiagram}</a></div>
                <div>
                    <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" label="{!$Label.c.CCM_Portal_Cancel}" title="{!$Label.c.CCM_Portal_Cancel}" onclick="{!c.cancel}" />
                    <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_Portal_Next}" title="{!$Label.c.CCM_Portal_Next}" onclick="{!c.checkEligibility}" />
                </div>
            </div>

            <div class="searchResult slds-p-around_medium slds-hide" aura:id="checkResult">
                <h2 class="slds-m-bottom_medium slds-text-heading_medium"><strong>{!$Label.c.CCM_Portal_Result}</strong></h2>
                <div class="slds-grid">
                    <p class="slds-truncate">
                        <aura:if isTrue="{!v.lampColor == 'ineligible'}">
                            {!v.warrantyStatus}
                        </aura:if>
                        <aura:if isTrue="{!v.lampColor == 'eligible'}">
                            {!v.warrantyStatus}
                        </aura:if>
                        {!v.checkResult}
                    </p>
                    <div class="reminder slds-truncate slds-hide" aura:id="repairTime">&nbsp;&nbsp; {!$Label.c.CCM_Portal_OurExpectationForThisRepairIs}&nbsp;{!v.repairTime}&nbsp;{!$Label.c.CCM_Portal_Minutes}.</div>
                </div>
            </div>
        </lightning:card>
        <div aura:id="boomModal" class="slds-hide">
            <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                <div class="slds-modal__container">
                    <lightning:spinner aura:id="proListSpinner" class="slds-hide modalSpinner" alternativeText="{!$Label.c.CCM_Portal_Loading}" size="small" variant="brand" />
                    <header class="slds-modal__header">
                        <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse"
                                title="{!$Label.c.CCM_Portal_Cancel}" onclick="{!c.onClickModalCancel}">
                            <lightning:icon alternativeText="{!$Label.c.CCM_Portal_Cancel}" iconName="utility:close" size="small" variant="bare"/>
                        </button>
                    </header>
                    <div class="slds-modal__content slds-p-around_medium">
                        <div class="slds-grid slds-m-bottom_x-large slds-grid_vertical-align-end" id="top">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium slds-size_1-of-4 slds-is-relative">
                                <lightning:select name="" label="{!$Label.c.CCM_Portal_Version}" aura:id="version" value="{!v.version}">
                                    <option text="{!$Label.c.CCM_Portal_NonePlaceTips}" value=""/>
                                    <aura:iteration items="{!v.versionList}" var="version">
                                        <option text="{!version}" value="{!version}"/>
                                    </aura:iteration>
                                </lightning:select>
                                <div class="slds-p-top_x-small slds-is-absolute" style="width: 120%">
                                    <a href="{!$Label.c.CCM_Exploded_Diagram_Note_4_Different_Version}" target="_blank">{!$Label.c.CCM_Portal_differentVersionsTips}</a>
                                </div>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium">
                                <lightning:button variant="brand" label="{!$Label.c.CCM_Portal_Search}" onclick="{! c.handleSearch }" />
                            </div>
                        </div>
                        <div aura:id="explosiveContent" class="slds-hide">
                            <!-- Slider main container -->
                            <div class="swiper-container"  id="top">
                            <!-- Additional required wrapper -->
                            <div class="swiper-wrapper">
                                <!-- Slides -->
                                <aura:iteration items="{!v.contents}" var="content">
                                    <div class="swiper-slide"><lightning:fileCard fileId="{!content}" description=""/></div>
                                    <!--                                <div class="swiper-slide"><lightning:fileCard fileId="069S000000105rqIAA" description="Sample Description"/></div>-->
                                </aura:iteration>
                            </div>
                            <!-- If we need navigation buttons -->
                            <div class="swiper-button-prev"></div>
                            <div class="swiper-button-next"></div>
                        </div>
                            <div class="slds-grid slds-m-vertical--medium slds-grid_vertical-align-end">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium slds-size_1-of-4">
                                <lightning:input label="{!$Label.c.CCM_Portal_SequenceNo}" aura:id="sequenceNo" value="{!v.sequenceNo}"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium">
                                <a class="slds-button slds-button_brand slds-float--right" onclick="{! c.highLightSequenceNo }" id="sequenceNo">{!$Label.c.CCM_Portal_Go}</a>
                            </div>
                        </div>
                            <div class="table_container slds-size_1-of-1 slds-medium-size_1-of-1 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small slds-scrollable_x  slds-m-top_large">
                            <lightning:spinner aura:id="proListSpinner" class="slds-hide" alternativeText="{!$Label.c.CCM_Portal_Loading}" size="small" variant="brand" />
                            <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_fixed-layout slds-table_resizable-cols">
                                <thead>
                                <tr class="slds-line-height_reset" aria-selected="false">
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 15%">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_SequenceNo}">{!$Label.c.CCM_Portal_SequenceNo}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 10%">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_ProductModel}">{!$Label.c.CCM_Portal_ProductModel}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 10%">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_PartsNumber}">{!$Label.c.CCM_Portal_PartsNumber}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 30%">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_PartsName}">{!$Label.c.CCM_Portal_PartsName}</span>
                                            </div>
                                        </a>
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                <aura:iteration items="{!v.explosiveDataList}" var="parts" indexVar="index">
                                    <tr aria-selected="false" class="{!parts.highLight ? 'slds-hint-parent highLight' : 'slds-hint-parent'}" aura:id="{!('line' + index)}" id="{!parts.ExplosionID__c}">
                                        <td data-label="Product Model#" scope="row">
                                            <div class="slds-truncate" title="">
                                                {!parts.ExplosionID__c}
                                            </div>
                                        </td>
                                        <td data-label="Product Model#" scope="row">
                                            <div class="slds-truncate" title="">
                                                {!parts.Product__r.ProductCode}
                                            </div>
                                        </td>
                                        <td data-label="Parts Number">
                                            <div class="slds-truncate" title="">
                                                {!parts.Parts__r.ProductCode}
                                            </div>
                                        </td>
                                        <td data-label="Description">
                                            <div class="slds-truncate" title="">
                                                <!-- add haibo: french -->
                                                <aura:if isTrue="{!($Label.c.CCM_Portal_Language == 'fr')}">
                                                    {!parts.Parts__r.Product_Name_French__c}
                                                    <aura:set attribute="else">
                                                        {!parts.Parts__r.Name}
                                                    </aura:set>
                                                </aura:if>
                                            </div>
                                        </td>
                                    </tr>
                                </aura:iteration>
                                </tbody>
                            </table>
                        </div>
                        </div>
                    </div>
                    <footer class="slds-modal__footer">
                        <a class="slds-button slds-button_brand slds-float--right slds-m-vertical--medium slds-m-right--medium" onclick="{!c.backToTop}">{!$Label.c.CCM_Portal_BackToTop}</a>
                    </footer>>
                </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
        </div>



    </section>




</aura:component>