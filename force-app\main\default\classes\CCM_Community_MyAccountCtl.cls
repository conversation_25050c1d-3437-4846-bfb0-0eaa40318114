public without sharing class CCM_Community_MyAccountCtl {
    public static String sqlString =
        'SELECT Name, Accounting_OracleID__c, Amt_Due_Remaining__c, ' +
        'Amt_Original__c, BillTo__c, BillTo_Text__c, Commentes__c, ' +
        'CurrencyIsoCode, Customer__c, Customer_Text__c, IsDeleted, ' +
        'Due_Date__c, Due_Days__c, Gl_Date__c, Invoice__c, ' +
        'Invoice_Text__c, Invoice_Date__c, CreatedDate, ' +
        'Invoice__r.Invoice_Number__c, LastModifiedDate,' +
        'Invoice__r.Order__c, Invoice__r.Order__r.PO_Number__c, Invoice__r.Order__r.OrderNumber,' +
        'Invoice__r.PO_Number__c, Invoice__r.Order_Commande__c, ' +
        'Invoice__r.Warranty_Return_Request__c, Invoice__r.Warranty_Return_Request__r.Payment_Method__c, ' +
        'Overdue__c, Payment_Term__c, Invoice__r.Tracking_NO__c ' +
        'FROM Accounting_Balance__c ' +
        'WHERE IsDeleted = false ';

    public static String sqlString1 =
        'SELECT CurrencyIsoCode, Customer__c, IsDeleted, Delivery_Number__c, ' +
        'Freight_Term__c, Freight_and_other_charges__c, Invoice_Date__c, ' +
        'Name, Invoice_Number__c, Invoice_Status__c, Invoice_Type__c, ' +
        'LastModifiedDate, Number_of_Shipping_Units__c, Order_Date__c, ' +
        'Origin__c, OwnerId, PO_Number__c, Id, Shipment__c, Total_Due__c, ' +
        'Total_Remaining__c, Tracking_NO__c ' +
        'FROM Invoice__c ' +
        'WHERE Invoice_Type__c = \'CNA_CreditMemo\' OR Invoice_Type__c = \'CA_CreditMemo\' ';
    @AuraEnabled
    public static String getInvoices(Integer pageNumber, Integer pageSize) {
        InitData initD = new InitData();

        User usr = Util.getUserInfo(UserInfo.getUserId());
        if (usr.Contact != null && String.isNotBlank(usr.Contact.AccountId)) {
            //update by nick ******** invoice source = CNA_Auto_Invoice
            String queryTotalAPStr =
                'SELECT Amt_Due_Remaining__c FROM Accounting_Balance__c WHERE Invoice_Text__c != null AND Customer__c = \'' +
                usr.Contact.AccountId +
                '\' ' +
                ' AND ((Invoice__r.Invoice_Source__c = \'CNA_Auto_Invoice\' AND Invoice__r.Invoice_Type__c IN (\'CNA_Invoice\', \'CNA_Export_Invoice\')) ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CNA_Manual_Invoice\' AND Invoice__r.Invoice_Type__c = \'CNA_Invoice\') ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CA_Auto_Invoice\' AND Invoice__r.Invoice_Type__c IN (\'CA_Invoice\', \'CA_Export_Invoice\')) ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CA_Manual_Invoice\' AND Invoice__r.Invoice_Type__c = \'CA_Invoice\'))';
            //AND Invoice__r.Invoice_Type__c = \'CNA_Invoice\'
            System.debug(
                LoggingLevel.INFO,
                '*** queryTotalAPStr: ' + queryTotalAPStr
            );
            List<Accounting_Balance__c> res1 = (List<Accounting_Balance__c>) Database.query(
                queryTotalAPStr
            );
            if (res1 != null && res1.size() > 0) {
                Decimal apAmt = 0.00;
                for (Accounting_Balance__c a : res1) {
                    if (a.Amt_Due_Remaining__c == null)
                        continue;
                    apAmt += a.Amt_Due_Remaining__c;
                }
                initD.dueRemainingTotalAmount = Math.abs(apAmt);
            }
            //update by nick ******** invoice type = CNA_External_CM
            String queryTotalARStr =
                'SELECT Name,Invoice__c,Amt_Due_Remaining__c FROM Accounting_Balance__c WHERE Invoice_Text__c != null AND Customer__c = \'' +
                usr.Contact.AccountId +
                '\' ' +
                ' AND ((Invoice__r.Invoice_Source__c = \'CNA_Manual_Invoice\' AND Invoice__r.Invoice_Type__c = \'CNA_External_CM\') ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CNA_Auto_Invoice\' AND Invoice__r.Invoice_Type__c = \'CNA_CreditMemo\') ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CA_Manual_Invoice\' AND Invoice__r.Invoice_Type__c = \'CA_External_CM\') ' +
                ' OR (Invoice__r.Invoice_Type__c = \'CA_CreditMemo\' AND Invoice__r.Total_Remaining__c != 0 AND Invoice__r.Invoice_Date__c < 2021-01-01) ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CA_Auto_Invoice\' AND Invoice__r.Invoice_Type__c = \'CA_CreditMemo\')) ';
            // AND Invoice__r.Invoice_Type__c = \'CNA_CreditMemo\'
            System.debug(
                LoggingLevel.INFO,
                '*** queryTotalARStr: ' + queryTotalARStr
            );
            List<Accounting_Balance__c> res2 = (List<Accounting_Balance__c>) Database.query(
                queryTotalARStr
            );
            if (res2 != null && res2.size() > 0) {
                Decimal arAmt = 0.00;
                Map<Id, Map<String, Decimal>> invoiceIdMapToamount = new Map<Id, Map<String, Decimal>>();
                for (Accounting_Balance__c a : res2) {
                    if (a.Amt_Due_Remaining__c == null)
                        continue;
                    if (!invoiceIdMapToamount.containsKey(a.Invoice__c)) {
                        Map<String, Decimal> nameMapToamount = new Map<String, Decimal>();
                        nameMapToamount.put(a.Name, a.Amt_Due_Remaining__c);
                        invoiceIdMapToamount.put(a.Invoice__c, nameMapToamount);
                    } else {
                        invoiceIdMapToamount.get(a.Invoice__c)
                            .put(a.Name, a.Amt_Due_Remaining__c);
                    }
                }

                for (Id invoiceId : invoiceIdMapToamount.keySet()) {
                    String newestab = null;
                    Decimal newestamount = 0.00;
                    for (
                        String abName : invoiceIdMapToamount.get(invoiceId)
                            .keySet()
                    ) {
                        if (newestab == null) {
                            newestab = abName;
                            newestamount = invoiceIdMapToamount.get(invoiceId)
                                .get(abName);
                        } else if (abName > newestab) {
                            newestab = abName;
                            newestamount = invoiceIdMapToamount.get(invoiceId)
                                .get(abName);
                        }
                    }
                    arAmt += newestamount;
                }
                initD.remainingTotalAmount = 0 - Math.abs(arAmt);
            }
            //update by nick ******** remainingTotalAmount + initD.dueRemainingTotalAmount
            initD.accountBalanceAmount =
                initD.remainingTotalAmount + initD.dueRemainingTotalAmount;

            String queryModifyDateStr = 'SELECT LastModifiedDate FROM Accounting_Balance__c ORDER BY LastModifiedDate DESC LIMIT 1';
            System.debug(
                LoggingLevel.INFO,
                '*** queryModifyDateStr: ' + queryModifyDateStr
            );
            List<Accounting_Balance__c> res3 = (List<Accounting_Balance__c>) Database.query(
                queryModifyDateStr
            );
            if (res3 != null && res3.size() > 0) {
                Datetime dt = (Datetime) res3[0].get('LastModifiedDate');
                initD.lastModifiedDate = res3[0].get('LastModifiedDate') == null
                    ? ''
                    : String.valueOf(
                          dt.format('MM/dd/yyyy hh:mm a zz', 'America/Chicago')
                      );
            }

            String invoiceDataStr = searchInvoice(
                pageNumber,
                pageSize,
                null,
                null,
                usr.Contact.AccountId
            );
            InitData invoiceData = (InitData) JSON.deserialize(
                invoiceDataStr,
                InitData.class
            );
            initD.totalPastDueAmount = invoiceData.totalPastDueAmount;
            initD.allBalanceDataList = invoiceData.allBalanceDataList;
            initD.currentBalanceDataList = invoiceData.currentBalanceDataList;
            initD.totalInvoiceRecords = invoiceData.totalInvoiceRecords;

            // String creditMemoDataStr = searchCreditMemo(pageNumber, pageSize, usr.Contact.AccountId);
            // System.debug(LoggingLevel.INFO, '*** creditMemoDataStr: ' + creditMemoDataStr);
            // InitData creditMemoData = (InitData) JSON.deserialize(creditMemoDataStr, InitData.class);
            // initD.allCreditMemoList = creditMemoData.allCreditMemoList;
            // initD.currentCreditMemoList = creditMemoData.currentCreditMemoList;
            // initD.totalCreditMemoRecords = creditMemoData.totalCreditMemoRecords;
        }
        return JSON.serialize(initD);
    }

    @AuraEnabled
    public static String searchInvoice(
        Integer pageNumber,
        Integer pageSize,
        String isOverdueStr,
        String invoiceRangeStr,
        String customerId
    ) {
        InitData initD = new InitData();
        if (String.isBlank(customerId) || customerId == 'null') {
            User usr = Util.getUserInfo(UserInfo.getUserId());
            if (
                usr.Contact != null && String.isNotBlank(usr.Contact.AccountId)
            ) {
                customerId = usr.Contact.AccountId;
            }
        }

        Date today = System.today();
        Date last30Date = today.addDays(-30);
        Date last90Date = today.addDays(-90);
        Date lastYearDate = today.addDays(-365);

        if (String.isNotBlank(customerId) && customerId != 'null') {
            //update by nick ******** search by Invoice_Source__c = \'CNA_Auto_Invoice\'
            /*
             *   Show In Debit Memo
             *   1.CNA_Auto_Invoice + CNA_Invoice
             *   2.CNA_Auto_Invoice + CNA_Export_Invoice
             *   3.CNA_Manual_Invoice + CNA_Invoice
             */
            String queryStr =
                sqlString +
                ' AND Invoice_Text__c != null AND Customer__c = \'' +
                customerId +
                '\'' +
                ' AND ((Invoice__r.Invoice_Source__c = \'CNA_Auto_Invoice\' AND Invoice__r.Invoice_Type__c IN (\'CNA_Invoice\', \'CNA_Export_Invoice\')) ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CNA_Manual_Invoice\' AND Invoice__r.Invoice_Type__c = \'CNA_Invoice\') ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CA_Auto_Invoice\' AND Invoice__r.Invoice_Type__c IN (\'CA_Invoice\', \'CA_Export_Invoice\')) ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CA_Manual_Invoice\' AND Invoice__r.Invoice_Type__c = \'CA_Invoice\'))';

            if (String.isNotBlank(isOverdueStr)) {
                if (isOverdueStr.equalsIgnoreCase('yes')) {
                    queryStr += ' AND Amt_Due_Remaining__c > 0 AND Due_Date__c < :today ';
                } else if (isOverdueStr.equalsIgnoreCase('no')) {
                    queryStr += ' AND (Amt_Due_Remaining__c <= 0 OR Due_Date__c >= :today ) ';
                }
            }

            if (String.isNotBlank(invoiceRangeStr)) {
                if (invoiceRangeStr.equalsIgnoreCase('Last 30 Days')) {
                    queryStr += ' AND Invoice_Date__c >= :last30Date';
                } else if (invoiceRangeStr.equalsIgnoreCase('Last 90 Days')) {
                    queryStr += ' AND Invoice_Date__c >= :last90Date';
                } else if (invoiceRangeStr.equalsIgnoreCase('Last 1 Year')) {
                    queryStr += ' AND Invoice_Date__c >= :lastYearDate';
                }
            }
            queryStr += ' ORDER BY Invoice_Date__c DESC';
            System.debug(LoggingLevel.INFO, '*** queryStr: ' + queryStr);
            List<Accounting_Balance__c> abs = (List<Accounting_Balance__c>) Database.query(
                queryStr
            );
            if (abs.size() > 0) {
                initD.totalInvoiceRecords = abs.size();
                Decimal pastDueAmount = 0.00;
                List<AmountBalanceData> allDatas = new List<AmountBalanceData>();
                for (Accounting_Balance__c balance : abs) {
                    AmountBalanceData data = new AmountBalanceData();
                    data.currencyCode = balance.CurrencyIsoCode;
                    if (balance.Invoice__r != null) {
                        data.invoiceNumber = balance.Invoice__r.Invoice_Number__c;
                    }
                    data.invoiceDate = balance.Invoice_Date__c != null ? DateTime.newInstance(balance.Invoice_Date__c.year(),balance.Invoice_Date__c.month(),balance.Invoice_Date__c.day()).format('MM-dd-yyyy') : '';
                    data.dueDate = balance.Due_Date__c != null ? DateTime.newInstance(balance.Due_Date__c.year(),balance.Due_Date__c.month(),balance.Due_Date__c.day()).format('MM-dd-yyyy') : '';
                    data.glDate = balance.Gl_Date__c != null ? DateTime.newInstance(balance.Gl_Date__c.year(),balance.Gl_Date__c.month(),balance.Gl_Date__c.day()).format('MM-dd-yyyy') : '';
                    data.originalAmount = balance.Amt_Original__c == null
                        ? 0.00
                        : balance.Amt_Original__c;
                    data.originalAmountStr =
                        data.currencyCode +
                        ' ' +
                        Util.formatDecimal(data.originalAmount, 2);
                    data.dueRemainingAmount = balance.Amt_Due_Remaining__c ==
                        null
                        ? 0.00
                        : balance.Amt_Due_Remaining__c;
                    data.dueRemainingAmountStr =
                        data.currencyCode +
                        ' ' +
                        Util.formatDecimal(data.dueRemainingAmount, 2);
                    data.paidAmount =
                        data.originalAmount - data.dueRemainingAmount;
                    data.paidAmountStr =
                        data.currencyCode +
                        ' ' +
                        Util.formatDecimal(data.paidAmount, 2);
                        // add haibo: french
                    data.isOverdue = (balance.Due_Date__c < Date.today() &&
                        data.dueRemainingAmount > 0)
                        ? Label.CCM_Portal_YES
                        : Label.CCM_Portal_NO;
                    Integer days = balance.Due_Date__c.daysBetween(
                        Date.today()
                    );
                    data.overdueDays = String.valueOf(days);

                    System.debug(LoggingLevel.INFO, '*** balance: ' + balance);
                    if (balance.Invoice__r != null) {
                        //计算Past Due Balance Amount的金额，OverDue的Debit Memo的Outstanding amount的Sum
                        // add haibo: french
                        if (data.isOverdue == Label.CCM_Portal_YES) {
                            pastDueAmount =
                                pastDueAmount + data.dueRemainingAmount;
                        }

                        System.debug(
                            LoggingLevel.INFO,
                            '*** balance.Invoice__r: ' + balance.Invoice__r
                        );
                        data.poNumber = balance.Invoice__r.PO_Number__c;
                        data.orderNumber = '';
                        if (balance.Invoice__r.Order__c != null) {
                            data.orderNumber = balance.Invoice__r.Order__r.OrderNumber;
                            data.orderId = balance.Invoice__r.Order__c;
                            data.orderURL =
                                '/s/order-detail-page?recordId=' +
                                balance.Invoice__r.Order__c;
                        }
                    }
                    if (balance.Amt_Due_Remaining__c <= 0) {
                        data.overdueDays = 'N/A';
                    }
                    // add haibo: french
                    if (data.isOverdue == Label.CCM_Portal_YES) {
                        data.displayStyle = 'red';
                    }
                    allDatas.add(data);
                }
                initD.totalPastDueAmount = pastDueAmount;
                initD.allBalanceDataList = allDatas;
                initD.currentBalanceDataList = getCurrentData(
                    allDatas,
                    pageNumber,
                    pageSize
                );
            }
        }
        return JSON.serialize(initD);
    }

    @AuraEnabled
    public static string getExportDebitMemo(String isOverdueStr, String invoiceRangeStr){
        User usr = Util.getUserInfo(UserInfo.getUserId());
        String customerId = usr.Contact.AccountId;
        String queryStr =
                sqlString +
                ' AND Invoice_Text__c != null AND Customer__c = \'' +
                customerId +
                '\'' +
                ' AND ((Invoice__r.Invoice_Source__c = \'CNA_Auto_Invoice\' AND Invoice__r.Invoice_Type__c IN (\'CNA_Invoice\', \'CNA_Export_Invoice\')) ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CNA_Manual_Invoice\' AND Invoice__r.Invoice_Type__c = \'CNA_Invoice\') ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CA_Auto_Invoice\' AND Invoice__r.Invoice_Type__c IN (\'CA_Invoice\', \'CA_Export_Invoice\')) ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CA_Manual_Invoice\' AND Invoice__r.Invoice_Type__c = \'CA_Invoice\'))';
        if (String.isNotBlank(isOverdueStr)) {
            if (isOverdueStr.equalsIgnoreCase('yes')) {
                queryStr += ' AND Amt_Due_Remaining__c > 0 AND Due_Date__c < :today ';
            } else if (isOverdueStr.equalsIgnoreCase('no')) {
                queryStr += ' AND (Amt_Due_Remaining__c <= 0 OR Due_Date__c >= :today ) ';
            }
        }
        Date today = System.today();
        Date last30Date = today.addDays(-30);
        Date last90Date = today.addDays(-90);
        Date lastYearDate = today.addDays(-365);
        if (String.isNotBlank(invoiceRangeStr)) {
            if (invoiceRangeStr.equalsIgnoreCase('Last 30 Days')) {
                queryStr += ' AND Invoice_Date__c >= :last30Date';
            } else if (invoiceRangeStr.equalsIgnoreCase('Last 90 Days')) {
                queryStr += ' AND Invoice_Date__c >= :last90Date';
            } else if (invoiceRangeStr.equalsIgnoreCase('Last 1 Year')) {
                queryStr += ' AND Invoice_Date__c >= :lastYearDate';
            }
        }
        queryStr += ' ORDER BY Invoice_Date__c DESC';
        List<Accounting_Balance__c> abs = (List<Accounting_Balance__c>) Database.query(
            queryStr
        );
        if (abs.size() > 0) {
            List<AmountBalanceData> allDatas = new List<AmountBalanceData>();
            for (Accounting_Balance__c balance : abs) {
                AmountBalanceData data = new AmountBalanceData();
                data.currencyCode = balance.CurrencyIsoCode;
                if (balance.Invoice__r != null) {
                    data.invoiceNumber = balance.Invoice__r.Invoice_Number__c;
                }
                data.invoiceDate = String.valueOf(balance.Invoice_Date__c);
                data.dueDate = String.valueOf(balance.Due_Date__c);
                data.glDate = String.valueOf(balance.Gl_Date__c);
                data.originalAmount = balance.Amt_Original__c == null
                    ? 0.00
                    : balance.Amt_Original__c;
                data.originalAmountStr =
                    data.currencyCode +
                    ' ' +
                    Util.formatDecimal(data.originalAmount, 2);
                data.dueRemainingAmount = balance.Amt_Due_Remaining__c ==
                    null
                    ? 0.00
                    : balance.Amt_Due_Remaining__c;
                data.dueRemainingAmountStr =
                    data.currencyCode +
                    ' ' +
                    Util.formatDecimal(data.dueRemainingAmount, 2);
                data.paidAmount =
                    data.originalAmount - data.dueRemainingAmount;
                data.paidAmountStr =
                    data.currencyCode +
                    ' ' +
                    Util.formatDecimal(data.paidAmount, 2);
                data.isOverdue = (balance.Due_Date__c < Date.today() &&
                    data.dueRemainingAmount > 0)
                    ? 'YES'
                    : 'NO';
                Integer days = balance.Due_Date__c.daysBetween(
                    Date.today()
                );
                data.overdueDays = String.valueOf(days);
                if (balance.Invoice__r != null) {
                    data.poNumber = balance.Invoice__r.PO_Number__c;
                    data.orderNumber = '';
                    if (balance.Invoice__r.Order__c != null) {
                        data.orderNumber = balance.Invoice__r.Order__r.OrderNumber;
                    }
                }
                if (balance.Amt_Due_Remaining__c <= 0) {
                    data.overdueDays = 'N/A';
                }
                allDatas.add(data);
            }

            List<CCM_InvoiceExportDataWrapper.DebitMemoDataWrapper> datas = CCM_InvoiceExportDataWrapper.convertToDebitMemoExportDataFormat(allDatas);
            return JSON.serialize(datas);
        }
        return null;
    }

    @AuraEnabled
    public static String searchCreditMemo(
        Integer pageNumber,
        Integer pageSize,
        String customerId
    ) {
        InitData initD = new InitData();
        if (String.isBlank(customerId) || customerId == 'null') {
            User usr = Util.getUserInfo(UserInfo.getUserId());
            if (
                usr.Contact != null && String.isNotBlank(usr.Contact.AccountId)
            ) {
                customerId = usr.Contact.AccountId;
            }
        }

        Date last30Date = System.today().addDays(-30);
        Date last90Date = System.today().addDays(-90);
        Date lastYearDate = System.today().addDays(-365);

        if (String.isNotBlank(customerId) && customerId != 'null') {
            // update by eric, ********
            // 只需要查询 CNA_External_CM 类型
            /*
             *   Show In Credit Memo
             *   1.CNA_Auto_Invoice + CNA_CreditMemo
             *   2.CNA_Manual_Invoice + CNA_External_CM
             */
            String queryStr =
                sqlString +
                ' AND Invoice_Text__c != null AND Customer__c = \'' +
                customerId +
                '\' ' +
                ' AND ((Invoice__r.Invoice_Source__c = \'CNA_Manual_Invoice\' AND Invoice__r.Invoice_Type__c = \'CNA_External_CM\') ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CNA_Auto_Invoice\' AND Invoice__r.Invoice_Type__c = \'CNA_CreditMemo\') ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CA_Manual_Invoice\' AND Invoice__r.Invoice_Type__c = \'CA_External_CM\') ' +
                ' OR (Invoice__r.Invoice_Type__c = \'CA_CreditMemo\' AND Invoice__r.Total_Remaining__c != 0 AND Invoice__r.Invoice_Date__c < 2021-01-01) ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CA_Auto_Invoice\' AND Invoice__r.Invoice_Type__c = \'CA_CreditMemo\')) ';
            //+' AND (Invoice__r.Invoice_Type__c = \'CNA_External_CM\' OR Invoice__r.Invoice_Type__c = \'CA_External_CM\')';
            queryStr += ' ORDER BY Invoice_Date__c DESC';
            System.debug(LoggingLevel.INFO, '*** queryStr: ' + queryStr);
            List<Accounting_Balance__c> abs = (List<Accounting_Balance__c>) Database.query(
                queryStr
            );
            if (abs.size() > 0) {
                initD.totalCreditMemoRecords = abs.size();

                List<AmountBalanceData> allDatas = new List<AmountBalanceData>();
                for (Accounting_Balance__c balance : abs) {
                    if (String.isNotBlank(balance.Invoice__r.Warranty_Return_Request__r.Payment_Method__c) && balance.Invoice__r.Warranty_Return_Request__r.Payment_Method__c == 'Deduction') {
                        continue;
                    }
                    AmountBalanceData data = new AmountBalanceData();
                    data.currencyCode = balance.CurrencyIsoCode;
                    if (balance.Invoice__r != null) {
                        data.invoiceNumber = balance.Invoice__r.Invoice_Number__c;
                    }
                    data.invoiceDate = balance.Invoice_Date__c != null ? DateTime.newInstance(balance.Invoice_Date__c.year(),balance.Invoice_Date__c.month(),balance.Invoice_Date__c.day()).format('MM-dd-yyyy') : '';
                    data.glDate = balance.Gl_Date__c != null ? DateTime.newInstance(balance.Gl_Date__c.year(),balance.Gl_Date__c.month(),balance.Gl_Date__c.day()).format('MM-dd-yyyy') : '';
                    data.originalAmount = balance.Amt_Original__c == null
                        ? 0.00
                        : balance.Amt_Original__c;
                    data.originalAmountStr =
                        data.currencyCode +
                        ' ' +
                        Util.formatDecimal(data.originalAmount, 2);
                    data.dueRemainingAmount = balance.Amt_Due_Remaining__c ==
                        null
                        ? 0.00
                        : balance.Amt_Due_Remaining__c;
                    data.dueRemainingAmountStr =
                        data.currencyCode +
                        ' ' +
                        Util.formatDecimal(data.dueRemainingAmount, 2);
                    data.paidAmount =
                        data.originalAmount - data.dueRemainingAmount;
                    data.paidAmountStr =
                        data.currencyCode +
                        ' ' +
                        Util.formatDecimal(data.paidAmount, 2);

                    if (
                        balance.Invoice__r != null &&
                        balance.Invoice__r.Order__c != null &&
                        String.isNotBlank(
                            balance.Invoice__r.Order__r.PO_Number__c
                        )
                    ) {
                        data.poNumber = balance.Invoice__r.Order__r.PO_Number__c;
                        data.orderURL =
                            '/s/order-detail-page?recordId=' +
                            balance.Invoice__r.Order__c;
                    }
                    allDatas.add(data);
                }
                initD.allCreditMemoList = allDatas;
                initD.currentCreditMemoList = getCurrentData(
                    allDatas,
                    pageNumber,
                    pageSize
                );
            }
        }
        return JSON.serialize(initD);
    }

    @AuraEnabled
    public static string getExportCreditMemo(){
        User usr = Util.getUserInfo(UserInfo.getUserId());
        String customerId = usr.Contact.AccountId;
        String queryStr =
                sqlString +
                ' AND Invoice_Text__c != null AND Customer__c = \'' + customerId + '\' ' +
                ' AND ((Invoice__r.Invoice_Source__c = \'CNA_Manual_Invoice\' AND Invoice__r.Invoice_Type__c = \'CNA_External_CM\') ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CNA_Auto_Invoice\' AND Invoice__r.Invoice_Type__c = \'CNA_CreditMemo\') ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CA_Manual_Invoice\' AND Invoice__r.Invoice_Type__c = \'CA_External_CM\') ' +
                ' OR (Invoice__r.Invoice_Type__c = \'CA_CreditMemo\' AND Invoice__r.Total_Remaining__c != 0 AND Invoice__r.Invoice_Date__c < 2021-01-01) ' +
                ' OR (Invoice__r.Invoice_Source__c = \'CA_Auto_Invoice\' AND Invoice__r.Invoice_Type__c = \'CA_CreditMemo\')) ';
         queryStr += ' ORDER BY Invoice_Date__c DESC';

        List<Accounting_Balance__c> abs = (List<Accounting_Balance__c>) Database.query(queryStr);

        if (abs.size() > 0) {
            List<AmountBalanceData> allDatas = new List<AmountBalanceData>();
            for (Accounting_Balance__c balance : abs) {
                if (String.isNotBlank(balance.Invoice__r.Warranty_Return_Request__r.Payment_Method__c) && balance.Invoice__r.Warranty_Return_Request__r.Payment_Method__c == 'Deduction') {
                    continue;
                }
                AmountBalanceData data = new AmountBalanceData();
                data.currencyCode = balance.CurrencyIsoCode;
                if (balance.Invoice__r != null) {
                    data.invoiceNumber = balance.Invoice__r.Invoice_Number__c;
                }
                data.invoiceDate = String.valueOf(balance.Invoice_Date__c);
                data.originalAmount = balance.Amt_Original__c == null
                    ? 0.00
                    : balance.Amt_Original__c;
                data.originalAmountStr =
                    data.currencyCode +
                    ' ' +
                    Util.formatDecimal(data.originalAmount, 2);
                data.dueRemainingAmount = balance.Amt_Due_Remaining__c ==
                    null
                    ? 0.00
                    : balance.Amt_Due_Remaining__c;
                data.dueRemainingAmountStr =
                    data.currencyCode +
                    ' ' +
                    Util.formatDecimal(data.dueRemainingAmount, 2);
                data.paidAmount =
                    data.originalAmount - data.dueRemainingAmount;
                data.paidAmountStr =
                    data.currencyCode +
                    ' ' +
                    Util.formatDecimal(data.paidAmount, 2);
                if (
                    balance.Invoice__r != null &&
                    balance.Invoice__r.Order__c != null &&
                    String.isNotBlank(
                        balance.Invoice__r.Order__r.PO_Number__c
                    )
                ) {
                    data.poNumber = balance.Invoice__r.Order__r.PO_Number__c;
                }
                allDatas.add(data);
            }
            List<CCM_InvoiceExportDataWrapper.CreditMemoDataWrapper> datas = CCM_InvoiceExportDataWrapper.convertToCreditMemoExportDataFormat(allDatas);
            return JSON.serialize(datas);
        }
        return null;
    }

    @AuraEnabled
    public static String searchPayment(Integer pageNumber, Integer pageSize) {
        InitData initD = new InitData();
        User usr = Util.getUserInfo(UserInfo.getUserId());
        if (usr.Contact != null && String.isNotBlank(usr.Contact.AccountId)) {
            Payment_Information__c[] queryPaymentList = [
                SELECT
                    Id,
                    Name,
                    CreatedBy.Name,
                    Paypal_Status__c,
                    Result__c,
                    // add haibo: french
                    tolabel(Result__c) resultLabel,
                    CreatedDate,
                    Pay_Method__c,
                    // add haibo: french
                    tolabel(Pay_Method__c) payMethodLabel,
                    Actual_Paid_Amount__c,
                    Remark__c,
                    First_Pay_By_Paypal__c,
                    Card_Last_Four__c,
                    (
                        SELECT
                            Id,
                            Has_No_Sequence__c,
                            CurrencyIsoCode,
                            Total_Amount__c,
                            Cash_Discount_Amount__c,
                            Actual_Paid_Amount__c,
                            Invoice__c,
                            Invoice_Type__c,
                            Invoice__r.Invoice_Number__c
                        FROM Payment_Information_Items__r
                    )
                FROM Payment_Information__c
                WHERE Customer__c = :usr.Contact.AccountId
                ORDER BY CreatedDate DESC
            ];
            //预处理，将invoice下的accounting单独存入map
            Set<Id> ids=new Set<Id>();
            for (Payment_Information__c queryPayment : queryPaymentList) {
                for (Payment_Information_Item__c paidInvoice : queryPayment.Payment_Information_Items__r){
                    ids.add(paidInvoice.id);
                }
            }
            List<Payment_Information_Item__c> queryPaymentItemAccountingList =[
                            SELECT
                                Accounting_Balance__c,
                                Total_Amount__c,
                                Cash_Discount_Amount__c,
                                Actual_Paid_Amount__c,
                                Payment_Information_Item_accounting__c
                            FROM Payment_Information_Item__c
                            WHERE
                                Payment_Information_Item_accounting__c In :ids];
            Map<String, List<Payment_Information_Item__c>> sequenseItem = new Map<String, List<Payment_Information_Item__c>>();
            for (Payment_Information_Item__c  each : queryPaymentItemAccountingList) {
                if (!sequenseItem.containsKey(each.Payment_Information_Item_accounting__c)) {
                    List<Payment_Information_Item__c> accountingList = new List<Payment_Information_Item__c>();
                    accountingList.add(each);
                    sequenseItem.put(each.Payment_Information_Item_accounting__c, accountingList);
                } else {
                    sequenseItem.get(each.Payment_Information_Item_accounting__c).add(each);
                }
            }
            //查payment
            List<PaymentInfo> paymentList = new List<PaymentInfo>();
            for (Payment_Information__c queryPayment : queryPaymentList) {
                PaymentInfo payment = new PaymentInfo();
                payment.id = queryPayment.Name;
                payment.createdById = queryPayment.CreatedBy.Name;
                payment.paypalStatus = queryPayment.Paypal_Status__c;
                // add haibo: french
                payment.transactionStatus = String.valueOf(queryPayment.get('resultLabel'));
                payment.transactionDate = queryPayment.CreatedDate == null
                    ? ''
                    : queryPayment.CreatedDate.format();
                // add haibo: french
                payment.paymentMethod = String.valueOf(queryPayment.get('payMethodLabel'));
                payment.paidAmout = queryPayment.Actual_Paid_Amount__c.setScale(
                    2
                );
                payment.remark = queryPayment.Remark__c;
                payment.isFirstPay = queryPayment.First_Pay_By_Paypal__c;
                payment.cardNumber = queryPayment.Card_Last_Four__c;
                //查payment下invoice
                List<InvoiceInfo> selectInvoicesList = new List<InvoiceInfo>();
                List<InvoiceInfo> selectCreditList = new List<InvoiceInfo>();
                Decimal subtotal = 0.00;
                for (
                    Payment_Information_Item__c queryPaymentItem : queryPayment.Payment_Information_Items__r
                ) {
                    //无分期invoice
                    if (queryPaymentItem.Has_No_Sequence__c) {
                        //credit类型invoice
                        if (
                            queryPaymentItem.Invoice_Type__c ==
                            'CNA_External_CM' ||
                            queryPaymentItem.Invoice_Type__c ==
                            'CA_External_CM' ||
                            queryPaymentItem.Invoice_Type__c ==
                            'CNA_CreditMemo' ||
                            queryPaymentItem.Invoice_Type__c == 'CA_CreditMemo'
                        ) {
                            InvoiceInfo credit = new InvoiceInfo();
                            credit.currencyType = queryPaymentItem.CurrencyIsoCode;
                            credit.id = queryPaymentItem.Invoice__c;
                            credit.invoiceType = queryPaymentItem.Invoice_Type__c;
                            credit.invoiceNumber = queryPaymentItem.Invoice__r.Invoice_Number__c;
                            credit.originalAmt = queryPaymentItem.Total_Amount__c;
                            selectCreditList.add(credit);
                        //普通invoice
                        } else {
                            InvoiceInfo invoice = new InvoiceInfo();
                            invoice.hasNoSequence = queryPaymentItem.Has_No_Sequence__c;
                            invoice.currencyType = queryPaymentItem.CurrencyIsoCode;
                            invoice.id = queryPaymentItem.Invoice__c;
                            invoice.invoiceType = queryPaymentItem.Invoice_Type__c;
                            invoice.totalPaymentFee = queryPaymentItem.Actual_Paid_Amount__c;
                            invoice.discountFee = queryPaymentItem.Cash_Discount_Amount__c;
                            invoice.originalAmt = queryPaymentItem.Total_Amount__c;
                            invoice.invoiceNumber = queryPaymentItem.Invoice__r.Invoice_Number__c;
                            subtotal += queryPaymentItem.Actual_Paid_Amount__c;
                            selectInvoicesList.add(invoice);
                        }
                        //有分期invoice
                    } else {
                        InvoiceInfo invoice2 = new InvoiceInfo();
                        invoice2.hasNoSequence = queryPaymentItem.Has_No_Sequence__c;
                        invoice2.currencyType = queryPaymentItem.CurrencyIsoCode;
                        invoice2.id = queryPaymentItem.Invoice__c;
                        invoice2.invoiceType = queryPaymentItem.Invoice_Type__c;
                        invoice2.invoiceNumber = queryPaymentItem.Invoice__r.Invoice_Number__c;
                        //单独装配invoice下的accounting
                        List<AccountingInfo> accoutingList = new List<AccountingInfo>();
                        if (sequenseItem.containsKey(queryPaymentItem.Id)) {
                            for (Payment_Information_Item__c sequense : sequenseItem.get(queryPaymentItem.Id)) {
                                AccountingInfo accouting = new AccountingInfo();
                                accouting.totalPaymentFee = sequense.Actual_Paid_Amount__c;
                                accouting.discountFee = sequense.Cash_Discount_Amount__c;
                                accouting.invoiceAmount = sequense.Total_Amount__c;
                                subtotal += sequense.Actual_Paid_Amount__c;
                                accoutingList.add(accouting);
                            }
                        }
                        invoice2.accountingList = accoutingList;
                        selectInvoicesList.add(invoice2);
                    }
                }
                //封装
                InvoiceAndCreditList invoiceAndCreditList = new InvoiceAndCreditList();
                invoiceAndCreditList.invoiceList = selectInvoicesList;
                invoiceAndCreditList.creditList = selectCreditList;
                invoiceAndCreditList.paidAmout = payment.paidAmout;
                invoiceAndCreditList.isFirstPay = payment.isFirstPay;
                invoiceAndCreditList.subtotal = subtotal.setScale(2);
                payment.invoiceAndCreditList = invoiceAndCreditList;
                paymentList.add(payment);
            }
            initD.allPaymentList = paymentList;
            initD.currentPaymentList = getPaymentCurrentData(
                paymentList,
                pageNumber,
                pageSize
            );
        }
        return JSON.serialize(initD);
    }

    @AuraEnabled
    public static string getExportTransactionHistory(){
        User usr = Util.getUserInfo(UserInfo.getUserId());
        Payment_Information__c[] queryPaymentList = [
            SELECT
                Id,
                Name,
                CreatedBy.Name,
                Paypal_Status__c,
                Result__c,
                CreatedDate,
                Pay_Method__c,
                Actual_Paid_Amount__c,
                Remark__c,
                First_Pay_By_Paypal__c,
                Card_Last_Four__c,
                (
                    SELECT
                        Id,
                        Has_No_Sequence__c,
                        CurrencyIsoCode,
                        Total_Amount__c,
                        Cash_Discount_Amount__c,
                        Actual_Paid_Amount__c,
                        Invoice__c,
                        Invoice_Type__c,
                        Invoice__r.Invoice_Number__c
                    FROM Payment_Information_Items__r
                )
            FROM Payment_Information__c
            WHERE Customer__c = :usr.Contact.AccountId
            ORDER BY CreatedDate DESC
        ];
        Set<Id> ids=new Set<Id>();
        for (Payment_Information__c queryPayment : queryPaymentList) {
            for (Payment_Information_Item__c paidInvoice : queryPayment.Payment_Information_Items__r){
                ids.add(paidInvoice.id);
            }
        }
        List<Payment_Information_Item__c> queryPaymentItemAccountingList =[
                        SELECT
                            Accounting_Balance__c,
                            Total_Amount__c,
                            Cash_Discount_Amount__c,
                            Actual_Paid_Amount__c,
                            Payment_Information_Item_accounting__c
                        FROM Payment_Information_Item__c
                        WHERE
                            Payment_Information_Item_accounting__c In :ids];
        Map<String, List<Payment_Information_Item__c>> sequenseItem = new Map<String, List<Payment_Information_Item__c>>();
        for (Payment_Information_Item__c  each : queryPaymentItemAccountingList) {
            if (!sequenseItem.containsKey(each.Payment_Information_Item_accounting__c)) {
                List<Payment_Information_Item__c> accountingList = new List<Payment_Information_Item__c>();
                accountingList.add(each);
                sequenseItem.put(each.Payment_Information_Item_accounting__c, accountingList);
            } else {
                sequenseItem.get(each.Payment_Information_Item_accounting__c).add(each);
            }
        }
        //查payment
        if(!queryPaymentList.isEmpty()) {
            List<PaymentInfo> paymentList = new List<PaymentInfo>();
            for (Payment_Information__c queryPayment : queryPaymentList) {
                PaymentInfo payment = new PaymentInfo();
                payment.id = queryPayment.Name;
                payment.createdById = queryPayment.CreatedBy.Name;
                payment.paypalStatus = queryPayment.Paypal_Status__c;
                payment.transactionStatus = queryPayment.Result__c;
                payment.transactionDate = queryPayment.CreatedDate == null
                    ? ''
                    : queryPayment.CreatedDate.format();
                payment.paymentMethod = queryPayment.Pay_Method__c;
                payment.paidAmout = queryPayment.Actual_Paid_Amount__c.setScale(
                    2
                );
                payment.remark = queryPayment.Remark__c;
                payment.isFirstPay = queryPayment.First_Pay_By_Paypal__c;
                payment.cardNumber = queryPayment.Card_Last_Four__c;
                //查payment下invoice
                List<InvoiceInfo> selectInvoicesList = new List<InvoiceInfo>();
                List<InvoiceInfo> selectCreditList = new List<InvoiceInfo>();
                Decimal subtotal = 0.00;
                for (
                    Payment_Information_Item__c queryPaymentItem : queryPayment.Payment_Information_Items__r
                ) {
                    //无分期invoice
                    if (queryPaymentItem.Has_No_Sequence__c) {
                        //credit类型invoice
                        if (
                            queryPaymentItem.Invoice_Type__c ==
                            'CNA_External_CM' ||
                            queryPaymentItem.Invoice_Type__c ==
                            'CA_External_CM' ||
                            queryPaymentItem.Invoice_Type__c ==
                            'CNA_CreditMemo' ||
                            queryPaymentItem.Invoice_Type__c == 'CA_CreditMemo'
                        ) {
                            InvoiceInfo credit = new InvoiceInfo();
                            credit.currencyType = queryPaymentItem.CurrencyIsoCode;
                            credit.id = queryPaymentItem.Invoice__c;
                            credit.invoiceType = queryPaymentItem.Invoice_Type__c;
                            credit.invoiceNumber = queryPaymentItem.Invoice__r.Invoice_Number__c;
                            credit.originalAmt = queryPaymentItem.Total_Amount__c;
                            selectCreditList.add(credit);
                        //普通invoice
                        } else {
                            InvoiceInfo invoice = new InvoiceInfo();
                            invoice.hasNoSequence = queryPaymentItem.Has_No_Sequence__c;
                            invoice.currencyType = queryPaymentItem.CurrencyIsoCode;
                            invoice.id = queryPaymentItem.Invoice__c;
                            invoice.invoiceType = queryPaymentItem.Invoice_Type__c;
                            invoice.totalPaymentFee = queryPaymentItem.Actual_Paid_Amount__c;
                            invoice.discountFee = queryPaymentItem.Cash_Discount_Amount__c;
                            invoice.originalAmt = queryPaymentItem.Total_Amount__c;
                            invoice.invoiceNumber = queryPaymentItem.Invoice__r.Invoice_Number__c;
                            subtotal += queryPaymentItem.Actual_Paid_Amount__c;
                            selectInvoicesList.add(invoice);
                        }
                        //有分期invoice
                    } else {
                        InvoiceInfo invoice2 = new InvoiceInfo();
                        invoice2.hasNoSequence = queryPaymentItem.Has_No_Sequence__c;
                        invoice2.currencyType = queryPaymentItem.CurrencyIsoCode;
                        invoice2.id = queryPaymentItem.Invoice__c;
                        invoice2.invoiceType = queryPaymentItem.Invoice_Type__c;
                        invoice2.invoiceNumber = queryPaymentItem.Invoice__r.Invoice_Number__c;
                        //单独装配invoice下的accounting
                        List<AccountingInfo> accoutingList = new List<AccountingInfo>();
                        if (sequenseItem.containsKey(queryPaymentItem.Id)) {
                            for (Payment_Information_Item__c sequense : sequenseItem.get(queryPaymentItem.Id)) {
                                AccountingInfo accouting = new AccountingInfo();
                                accouting.totalPaymentFee = sequense.Actual_Paid_Amount__c;
                                accouting.discountFee = sequense.Cash_Discount_Amount__c;
                                accouting.invoiceAmount = sequense.Total_Amount__c;
                                subtotal += sequense.Actual_Paid_Amount__c;
                                accoutingList.add(accouting);
                            }
                        }
                        invoice2.accountingList = accoutingList;
                        selectInvoicesList.add(invoice2);
                    }
                }
                //封装
                InvoiceAndCreditList invoiceAndCreditList = new InvoiceAndCreditList();
                invoiceAndCreditList.invoiceList = selectInvoicesList;
                invoiceAndCreditList.creditList = selectCreditList;
                invoiceAndCreditList.paidAmout = payment.paidAmout;
                invoiceAndCreditList.isFirstPay = payment.isFirstPay;
                invoiceAndCreditList.subtotal = subtotal.setScale(2);
                payment.invoiceAndCreditList = invoiceAndCreditList;
                paymentList.add(payment);
            }
            
            List<CCM_InvoiceExportDataWrapper.TransactionHistoryDataWrapper> datas = CCM_InvoiceExportDataWrapper.convertToTransactionHistoryExportDataFormat(paymentList);
            return JSON.serialize(datas);
        }
        return null;
    }

    //分页
    public static List<AmountBalanceData> getCurrentData(
        List<AmountBalanceData> allData,
        Decimal pageNumber,
        Integer pageSize
    ) {
        List<AmountBalanceData> currentData = new List<AmountBalanceData>();
        Integer min = ((Integer) pageNumber - 1) * pageSize;
        Integer max = (Integer) pageNumber * pageSize - 1;
        for (Integer i = min; i <= max; i++) {
            if (i < allData.size()) {
                currentData.add(allData.get(i));
            }
        }
        return currentData;
    }
    //分页
    public static List<PaymentInfo> getPaymentCurrentData(
        List<PaymentInfo> allData,
        Decimal pageNumber,
        Integer pageSize
    ) {
        List<PaymentInfo> currentData = new List<PaymentInfo>();
        Integer min = ((Integer) pageNumber - 1) * pageSize;
        Integer max = (Integer) pageNumber * pageSize - 1;
        for (Integer i = min; i <= max; i++) {
            if (i < allData.size()) {
                currentData.add(allData.get(i));
            }
        }
        return currentData;
    }

    public class PaymentInfo {
        @AuraEnabled
        public String cardNumber { get; set; }
        @AuraEnabled
        public String id { get; set; }
        @AuraEnabled
        public String createdById { get; set; }
        @AuraEnabled
        public String paypalStatus { get; set; }
        @AuraEnabled
        public String transactionStatus { get; set; }
        @AuraEnabled
        public String transactionDate { get; set; }
        @AuraEnabled
        public String paymentMethod { get; set; }
        @AuraEnabled
        public Decimal paidAmout { get; set; }
        @AuraEnabled
        public String remark { get; set; }
        @AuraEnabled
        public Boolean isFirstPay { get; set; }

        @AuraEnabled
        public invoiceAndCreditList invoiceAndCreditList { get; set; }
    }

    public class InvoiceAndCreditList {
        @AuraEnabled
        public List<InvoiceInfo> invoiceList { get; set; }
        @AuraEnabled
        public List<InvoiceInfo> creditList { get; set; }
        @AuraEnabled
        public Decimal paidAmout { get; set; }
        @AuraEnabled
        public Decimal subtotal { get; set; }
        @AuraEnabled
        public Boolean isFirstPay { get; set; }

        public InvoiceAndCreditList() {
            this.invoiceList = new List<InvoiceInfo>();
            this.creditList = new List<InvoiceInfo>();
        }
    }

    public class InvoiceInfo {
        @AuraEnabled
        public String id { get; set; }
        @AuraEnabled
        public String currencyType { get; set; }
        @AuraEnabled
        public String invoiceNumber { get; set; }
        @AuraEnabled
        public String invoiceStatus { get; set; }
        @AuraEnabled
        public String invoiceType { get; set; }
        @AuraEnabled
        public String orderDate { get; set; }
        @AuraEnabled
        public String invoiceDate { get; set; }
        @AuraEnabled
        public String poNumber { get; set; }
        @AuraEnabled
        public String overdue { get; set; }
        @AuraEnabled
        public Integer dueDays { get; set; }
        @AuraEnabled
        public Decimal originalAmt { get; set; }
        @AuraEnabled
        public Decimal othersFee { get; set; }
        @AuraEnabled
        public Decimal dueRemainingAmt { get; set; }
        @AuraEnabled
        public Decimal totalDue { get; set; }
        @AuraEnabled
        public String trackNo { get; set; }
        @AuraEnabled
        public String paymentTerm { get; set; }
        @AuraEnabled
        public String freightTerm { get; set; }
        @AuraEnabled
        public String orderId { get; set; }
        @AuraEnabled
        public String orderURL { get; set; }
        @AuraEnabled
        public String dueDate { get; set; }
        @AuraEnabled
        public String isOverdue { get; set; }
        @AuraEnabled
        public String overdueDays { get; set; }
        @AuraEnabled
        public String displayStyle { get; set; }
        @AuraEnabled
        public Boolean hasNoSequence { get; set; }
        @AuraEnabled
        public Decimal discountFee { get; set; }
        @AuraEnabled
        public Decimal totalPaymentFee { get; set; }

        @AuraEnabled
        public List<AccountingInfo> accountingList { get; set; }

        public InvoiceInfo() {
            this.accountingList = new List<AccountingInfo>();
        }
    }

    public class AccountingInfo {
        @AuraEnabled
        public String Id { get; set; }
        @AuraEnabled
        public String invoiceNumber { get; set; }
        @AuraEnabled
        public String dueDate { get; set; }
        @AuraEnabled
        public Decimal invoiceAmount { get; set; }
        @AuraEnabled
        public String paidAmount { get; set; }
        @AuraEnabled
        public Decimal dueRemainingAmount { get; set; }
        @AuraEnabled
        public String isOverdue { get; set; }
        @AuraEnabled
        public String overdueDays { get; set; }
        @AuraEnabled
        public String displayStyle { get; set; }
        @AuraEnabled
        public Decimal discountFee { get; set; }
        @AuraEnabled
        public Decimal totalPaymentFee { get; set; }

        public AccountingInfo() {
        }
    }

    public class AmountBalanceData {
        @AuraEnabled
        public String currencyCode { get; set; }
        @AuraEnabled
        public String invoiceNumber { get; set; } //Invoice tracking number
        @AuraEnabled
        public String invoiceDate { get; set; } //发票日期
        @AuraEnabled
        public String glDate { get; set; } //记账日期
        @AuraEnabled
        public String dueDate { get; set; } //截止日期
        @AuraEnabled
        public Decimal originalAmount { get; set; } //原始金额
        @AuraEnabled
        public Decimal dueRemainingAmount { get; set; } //剩余未付金额
        @AuraEnabled
        public Decimal paidAmount { get; set; } //已付金额
        @AuraEnabled
        public String originalAmountStr { get; set; } //原始金额
        @AuraEnabled
        public String dueRemainingAmountStr { get; set; } //剩余未付金额
        @AuraEnabled
        public String paidAmountStr { get; set; } //已付金额
        @AuraEnabled
        public String isOverdue { get; set; } //是否已经逾期
        @AuraEnabled
        public String overdueDays { get; set; } //逾期天数
        @AuraEnabled
        public String orderDate { get; set; } //Invoice 订单生成日期
        @AuraEnabled
        public Decimal totalDueAmount { get; set; }
        @AuraEnabled
        public Decimal totalRemainingAmount { get; set; }
        @AuraEnabled
        public Decimal totalPastDueAmount { get; set; }
        @AuraEnabled
        public String totalDueAmountStr { get; set; }
        @AuraEnabled
        public String totalRemainingAmountStr { get; set; }
        @AuraEnabled
        public String totalPastDueAmountStr { get; set; }

        @AuraEnabled
        public String orderId { get; set; }
        @AuraEnabled
        public String orderURL { get; set; }
        @AuraEnabled
        public String poNumber { get; set; }
        @AuraEnabled
        public String orderNumber { get; set; }
        @AuraEnabled
        public String displayStyle { get; set; }

        public AmountBalanceData() {
            this.originalAmount = 0.00;
            this.dueRemainingAmount = 0.00;
            this.paidAmount = 0.00;
            this.totalDueAmount = 0.00;
            this.totalRemainingAmount = 0.00;
            this.totalPastDueAmount = 0.00;
        }
    }

    public class InitData {
        public List<AmountBalanceData> allBalanceDataList { get; set; }
        public List<AmountBalanceData> currentBalanceDataList { get; set; }
        public List<AmountBalanceData> allCreditMemoList { get; set; }
        public List<AmountBalanceData> currentCreditMemoList { get; set; }
        public List<PaymentInfo> allPaymentList { get; set; }
        public List<PaymentInfo> currentPaymentList { get; set; }
        public Integer totalInvoiceRecords { get; set; }
        public Integer totalCreditMemoRecords { get; set; }
        public Decimal totalAmount { get; set; }
        public Decimal dueRemainingTotalAmount { get; set; }
        public Decimal paidTotalAmount { get; set; }
        public Decimal remainingTotalAmount { get; set; }
        public String lastModifiedDate { get; set; }
        public Decimal dueTotalAmount { get; set; }
        public Decimal accountBalanceAmount { get; set; }
        public String accountBalanceAmountStr { get; set; }
        public String userCurrency { get; set; }
        public Decimal totalPastDueAmount { get; set; }

        public InitData() {
            this.allBalanceDataList = new List<AmountBalanceData>();
            this.allCreditMemoList = new List<AmountBalanceData>();
            this.currentBalanceDataList = new List<AmountBalanceData>();
            this.currentCreditMemoList = new List<AmountBalanceData>();
            this.allPaymentList = new List<PaymentInfo>();
            this.currentPaymentList = new List<PaymentInfo>();
            this.totalAmount = 0.00;
            this.dueRemainingTotalAmount = 0.00;
            this.paidTotalAmount = 0.00;
            this.totalInvoiceRecords = 0;
            this.totalCreditMemoRecords = 0;
            this.remainingTotalAmount = 0.00;
            this.dueTotalAmount = 0.00;
            this.accountBalanceAmount = 0.00;
            this.userCurrency = 'USD';
            this.totalPastDueAmount = 0.00;
        }
    }

    public class FilterWrap {
        public String dueStatus;
        public String settled;
        public String glDate;
    }
}