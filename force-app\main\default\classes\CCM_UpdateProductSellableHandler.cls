/**
 * <AUTHOR>
 * @date 2021-07-14
 * @description This class is used to update Sellable__c status for PIM Products according to EBS ones.
 */
public with sharing class CCM_UpdateProductSellableHandler implements Triggers.Handler {
    public static Boolean boolToRun;
    private final List<String> UPPER_CASED_SELLABLE_EBS_STATUSES = Label.CCM_EBS_Statuses_4_Sellable_Products.toUpperCase().split('\\s*;\\s*');
    public void handle() {
        // prettier-ignore
        if (boolToRun == false) return;
        system.debug(boolToRun);
        Set<String> setCountry = new Set<String>();
        Set<String> setProductCode = new Set<String>();
        Set<Product2> lstPimProductToUpdate = new Set<Product2>();
        Map<String, List<String>> mapKey2ListEbsStatus = new Map<String, List<String>>();
        Map<String, List<Product2>> mapKey2ListPimProduct = new Map<String, List<Product2>>();
        filterEbsProductCode(setCountry, setProductCode);
        // prettier-ignore
        if (setCountry.isEmpty() || setProductCode.isEmpty()) return;
        findPimAndRelatedEbsProduct(setCountry, setProductCode, mapKey2ListEbsStatus, mapKey2ListPimProduct);
        system.debug(mapKey2ListPimProduct);
        system.debug(mapKey2ListEbsStatus);
        // prettier-ignore
        if (mapKey2ListPimProduct.isEmpty() || mapKey2ListEbsStatus.isEmpty()) return;
        
        process(mapKey2ListEbsStatus, mapKey2ListPimProduct, lstPimProductToUpdate);
        updatePimProduct(lstPimProductToUpdate);
    }
    private void filterEbsProductCode(Set<String> setCountry, Set<String> setProductCode) {
        for (Product2 objP : (List<Product2>) Trigger.new) {
            if (
                String.isBlank(objP.Country_of_Origin__c) ||
                String.isBlank(objP.ProductCode) ||
                CCM_Constants.PRODUCT_SOURCE_PIM.equalsIgnoreCase(objP.Source__c) ||
                !CCM_Constants.PRODUCT_PRODUCT_RECORD_TYPE_ID.equals(objP.RecordTypeId)
            ) {
                continue;
            }
            system.debug(objP.Country_of_Origin__c);
            setCountry.add(objP.Country_of_Origin__c);
            setProductCode.add(objP.ProductCode);
        }
    }
    private void findPimAndRelatedEbsProduct(
        Set<String> setEbsCountry,
        Set<String> setEbsProductCode,
        Map<String, List<String>> mapKey2ListEbsStatus,
        Map<String, List<Product2>> mapKey2ListPimProduct
    ) {
        String strKey;
        for (Product2 objP : [
            SELECT ProductCode, Country_of_Origin__c, Source__c, EBS_Status__c, Sellable__c, RecordTypeId, Sellable_CCA__c
            FROM Product2
            WHERE
                Source__c IN (:CCM_Constants.PRODUCT_SOURCE_PIM, :CCM_Constants.PRODUCT_SOURCE_EBS)
                AND ProductCode IN :setEbsProductCode

        ]) {
            system.debug(objP.Country_of_Origin__c);
            system.debug(objP.Source__c);
            system.debug(objP.EBS_Status__c);
            strKey = objP.Country_of_Origin__c + objP.ProductCode;
            if (CCM_Constants.PRODUCT_SOURCE_PIM.equalsIgnoreCase(objP.Source__c)) {
                if (!mapKey2ListPimProduct.containsKey(strKey)) {
                    mapKey2ListPimProduct.put(strKey, new List<Product2>());
                }
                mapKey2ListPimProduct.get(strKey).add(objP);
            } else if (CCM_Constants.PRODUCT_SOURCE_EBS.equalsIgnoreCase(objP.Source__c) && CCM_Constants.PRODUCT_PRODUCT_RECORD_TYPE_ID.equals(objP.RecordTypeId)) {
                if (!mapKey2ListEbsStatus.containsKey(strKey)) {
                    mapKey2ListEbsStatus.put(strKey, new List<String>());
                }
                mapKey2ListEbsStatus.get(strKey).add(objP.EBS_Status__c);
            }
        }
    }
    private void process(Map<String, List<String>> mapKey2ListEbsStatus, Map<String, List<Product2>> mapKey2ListPimProduct, Set<Product2> lstPimProductToUpdate) {
        Boolean boolSellable;
        Map<String, Product2> productPIdMap = new Map<String, Product2>(); // 2022-07-11: fix 'duplicate id in list' bug
        for(Product2 objP : (List<Product2>) Trigger.new){
            String strK = objP.Country_of_Origin__c + objP.ProductCode;
            String newstrK = '';
            boolSellable = false;
            if(mapKey2ListEbsStatus.containsKey(strK)){
                for (String strEbsStatus : mapKey2ListEbsStatus.get(strK)) {
                    if (String.isNotBlank(strEbsStatus) && UPPER_CASED_SELLABLE_EBS_STATUSES.contains(strEbsStatus.toUpperCase())) {
                            boolSellable = true;
                            break;
                    }
                }
            }
            
            if(strK.contains('Canada')){
                newstrK = strK.replaceAll('Canada', 'United States');
            }else{
                newstrK = strK;
            }
            if(mapKey2ListPimProduct.containsKey(newstrK)){
                for (Product2 objPimP : mapKey2ListPimProduct.get(newstrK)) {
                    // prettier-ignore
                    //if (objPimP.Sellable__c == boolSellable) continue;
                    system.debug(newstrK);
                    system.debug(objPimP);
                    system.debug(boolSellable);
                    // 2022-07-11: fix 'duplicate id in list' bug
                    Product2 updatePIM = productPIdMap.containsKey(objPimP.Id) ? productPIdMap.get(objPimP.Id) : new Product2(Id = objPimP.Id);
                    if(strK.contains('Canada')){
                        updatePIM.Sellable_CCA__c = boolSellable;
                    }else{
                        updatePIM.Sellable__c = boolSellable;
                    }
                    productPIdMap.put(objPimP.Id, updatePIM);
                }
            }
        }

        // 2022-07-11: fix 'duplicate id in list' bug
        lstPimProductToUpdate.addAll(productPIdMap.values());
    }
    private void updatePimProduct(Set<Product2> lstPimProductToUpdate) {
        Integer intIndex = 0;
        List<Log__c> lstLog = new List<Log__c>();
        boolToRun = false;
        CCM_StopUpdatingPimSellableHandler.boolToRun = false;
        List<Product2> listPimProductToUpdate = new List<Product2>();
        listPimProductToUpdate.addAll(lstPimProductToUpdate);
        for (Database.SaveResult objSR : Database.update(listPimProductToUpdate, false)) {
            if (!objSR.isSuccess() || Test.isRunningTest()) {
                lstLog.add(
                    new Log__c(
                        Name = 'Failed Updating PIM Product: ' + (objSR.isSuccess() ? 'test' : objSR.getId()),
                        ApexName__c = 'CCM_UpdateProductSellableHandler',
                        Method__c = 'handle',
                        Error_Message__c = objSR.isSuccess() ? 'test' : objSR.getErrors()[0].getMessage(),
                        ReqParam__c = JSON.serialize(listPimProductToUpdate[intIndex])
                    )
                );
            }
            intIndex++;
        }
        boolToRun = true;
        CCM_StopUpdatingPimSellableHandler.boolToRun = true;
        // prettier-ignore
        if (!lstLog.isEmpty()) insert lstLog;
    }
}