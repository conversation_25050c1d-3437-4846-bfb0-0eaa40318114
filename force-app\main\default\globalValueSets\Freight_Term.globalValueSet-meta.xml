<?xml version="1.0" encoding="UTF-8"?>
<GlobalValueSet xmlns="http://soap.sforce.com/2006/04/metadata">
    <customValue>
        <fullName>COLLECT</fullName>
        <default>false</default>
        <label>Collect</label>
    </customValue>
    <customValue>
        <fullName>PICK</fullName>
        <default>false</default>
        <label>Customer Pick Up</label>
    </customValue>
    <customValue>
        <fullName>FOB</fullName>
        <default>false</default>
        <label>FOB Shipping Point</label>
    </customValue>
    <customValue>
        <fullName>Paid</fullName>
        <default>false</default>
        <label>Prepaid</label>
    </customValue>
    <customValue>
        <fullName>PPF</fullName>
        <default>false</default>
        <label>Prepaid @ CAD25000</label>
    </customValue>
    <customValue>
        <fullName>PPE</fullName>
        <default>false</default>
        <label>Prepaid @12500</label>
    </customValue>
    <customValue>
        <fullName>PPB</fullName>
        <default>false</default>
        <label>Prepaid @150.00</label>
    </customValue>
    <customValue>
        <fullName>PPC</fullName>
        <default>false</default>
        <label>Prepaid @2000</label>
    </customValue>
    <customValue>
        <fullName>PPD</fullName>
        <default>false</default>
        <label>Prepaid @500</label>
    </customValue>
    <customValue>
        <fullName>Due</fullName>
        <default>false</default>
        <label>Prepaid @750</label>
    </customValue>
    <customValue>
        <fullName>PPA</fullName>
        <default>false</default>
        <label>Prepaid and add</label>
    </customValue>
    <customValue>
        <fullName>DUECOST</fullName>
        <default>false</default>
        <label>Prepay &amp; Add with cost conversion</label>
    </customValue>
    <customValue>
        <fullName>THIRD_PARTY</fullName>
        <default>false</default>
        <label>Third Party Billing</label>
    </customValue>
    <customValue>
        <fullName>TBD</fullName>
        <default>false</default>
        <label>To Be Determined</label>
    </customValue>
    <customValue>
        <fullName>PPG</fullName>
        <default>false</default>
        <label>Prepaid @1000</label>
    </customValue>
    <customValue>
        <fullName>PPH</fullName>
        <default>false</default>
        <label>Prepaid @ 25000</label>
    </customValue>
    <customValue>
        <fullName>PPJ</fullName>
        <default>false</default>
        <label>Prepaid @1500</label>
    </customValue>
    <customValue>
        <fullName>PPK</fullName>
        <default>false</default>
        <label>Prepaid @15000</label>
    </customValue>
    <customValue>
        <fullName>PPL</fullName>
        <default>false</default>
        <label>Prepaid @3000</label>
    </customValue>
    <customValue>
        <fullName>PPM</fullName>
        <default>false</default>
        <label>Prepaid @3500</label>
    </customValue>
    <customValue>
        <fullName>PPO</fullName>
        <default>false</default>
        <label>Prepaid @2500</label>
    </customValue>
    <customValue>
        <fullName>PPP</fullName>
        <default>false</default>
        <label>Prepaid @5000</label>
    </customValue>
    <customValue>
        <fullName>PPN</fullName>
        <default>false</default>
        <label>Prepaid @100</label>
    </customValue>
    <customValue>
        <fullName>Prepaid @100</fullName>
        <default>false</default>
        <isActive>false</isActive>
        <label>Prepaid @100</label>
    </customValue>
    <masterLabel>Freight Term</masterLabel>
    <sorted>false</sorted>
</GlobalValueSet>
