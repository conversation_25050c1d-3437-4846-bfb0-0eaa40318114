@isTest
private class Test_Lead_Trigger {
    static testMethod void testMethod1() {
        
        User salesManager1 = Test_SalesData.createUser('TestLoginUser', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesManager2 = Test_SalesData.createUser('TestSalesManager', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesDirector = Test_SalesData.createUser('TestSalesDirector', 'OPE_Sales_Director', 'NA Sales Manager');

        

        System.runAs(salesManager1) {
            Test_SalesData.createProspectAssignmentRules(salesManager1.Id, salesManager2.Id, salesDirector.Id);
            Lead theLead = Test_SalesData.createProspectData();
            theLead.Street = 'test street';
            theLead.City = 'New York';
            theLead.State = 'NY';
            theLead.Country = 'US';
            theLead.PostalCode = '11111';
            theLead.Intended_Brand__c = 'EGO';
            theLead.Distributor_or_Dealer__c = 'Direct Dealer';
            update theLead;

            Sales_Program__c sp = new Sales_Program__c(
                Name = 'TestSP001',
                Brands__c = 'EGO',
                Payment_Term__c = 'NA059',
                Payment_Term_Description__c = '2% discount is given if the invoice is paid within 10 days ,Or One whole invoice by 6 payments installment without discount.',
                Freight_Term__c = 'PPC',
                Price_List__c = 'Standard Price List',
                Starting_Date_of_Payment_Term__c = 'Invoice Date',
                Order_Lead_Time__c = 5,
                Prospect__c = theLead.Id,
                Sales_Group__c = 'SG01',
                Authorized_Brand_Name_To_Oracle__c = 'Test'
            );
            insert sp;
            sp.Co_Op_Discount__c = 5;
            sp.Approval_Status__c = 'Approved';
            update sp;

            Attachment_Management__c am1 = Test_SalesData.createAttachmentManagement(theLead.Id, null, 'Brand Program');
            Attachment_Management__c am2 = Test_SalesData.createAttachmentManagement(theLead.Id, null, 'General Agreement');

            Contact con =  Test_SalesData.createContact(theLead.Id, null);
            Event eve = Test_SalesData.createEvent(theLead.Id);

            Test.startTest();
            List<Account_Address__c> aaList = [Select Id, Contact__c, Email_for_Invoicing__c, RecordType.Name from Account_Address__c where Prospect__c =: theLead.Id];
            Account_Address__c billingAddress;
            for (Account_Address__c aa : aaList) {
                aa.Contact__c = con.Id;
                if (aa.RecordType.Name == 'Billing Address') {
                    billingAddress = aa;
                }
            }
            update aaList;

            Address_With_Program__c awp = new Address_With_Program__c(
                Account_Address__c = billingAddress.Id,
                Program__c = sp.Id
            );
            insert awp;

            Database.LeadConvert lc = new Database.LeadConvert();
            lc.setLeadId(theLead.id);
            lc.setDoNotCreateOpportunity(true);
            lc.setConvertedStatus('Converted');
            Database.LeadConvertResult lcr = Database.convertLead(lc, false);  
          
            system.debug( 'Errors are ' + lcr.getErrors() );
            
            // System.assert(lcr.isSuccess());
            Test.stopTest();
        } 
    }

    static testMethod void testMethod2() {
        
        User beamUser = Test_SalesData.createUser('TestBeamUser', 'BEAM', 'BEAM');
        User salesManager1 = Test_SalesData.createUser('TestLoginUser', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesManager2 = Test_SalesData.createUser('TestSalesManager', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesDirector = Test_SalesData.createUser('TestSalesDirector', 'OPE_Sales_Director', 'NA Sales Manager');

        

        Test.startTest();
        System.runAs(beamUser) {
            Test_SalesData.createProspectAssignmentRules(salesManager1.Id, salesManager2.Id, salesDirector.Id);
            Lead theLead = Test_SalesData.createProspectData();

            Sales_Program__c sp = new Sales_Program__c(
                Name = 'TestSP001',
                Brands__c = 'EGO',
                Payment_Term__c = 'NA059',
                Payment_Term_Description__c = '2% discount is given if the invoice is paid within 10 days ,Or One whole invoice by 6 payments installment without discount.',
                Freight_Term__c = 'PPC',
                Price_List__c = 'Standard Price List',
                Starting_Date_of_Payment_Term__c = 'Invoice Date',
                Order_Lead_Time__c = 5,
                Prospect__c = theLead.Id,
                Approval_Status__c = 'Draft',
                Authorized_Brand_Name_To_Oracle__c = 'Test'
            );
            insert sp;

            theLead.Street = 'test street';
            theLead.City = 'New York';
            theLead.State = 'NY';
            theLead.Country = 'US';
            theLead.PostalCode = '11111';
            theLead.Intended_Brand__c = 'EGO';
            theLead.Distributor_or_Dealer__c = 'Direct Dealer';
            //theLead.OwnerId = salesManager1.Id
            update theLead;


        }

        Test.stopTest();
    }

    static testMethod void testMethod3() {
        
        User beamUser = Test_SalesData.createUser('TestBeamUser', 'BEAM', 'BEAM');
        User salesManager1 = Test_SalesData.createUser('TestLoginUser', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesManager2 = Test_SalesData.createUser('TestSalesManager', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesDirector = Test_SalesData.createUser('TestSalesDirector', 'OPE_Sales_Director', 'NA Sales Manager');

        

        Test.startTest();
        System.runAs(beamUser) {
            Test_SalesData.createProspectAssignmentRules(salesManager1.Id, salesManager2.Id, salesDirector.Id);
            Lead theLead = Test_SalesData.createProspectData();

            Sales_Program__c sp = new Sales_Program__c(
                Name = 'TestSP001',
                Brands__c = 'EGO',
                Payment_Term__c = 'NA059',
                Payment_Term_Description__c = '2% discount is given if the invoice is paid within 10 days ,Or One whole invoice by 6 payments installment without discount.',
                Freight_Term__c = 'PPC',
                Price_List__c = 'Standard Price List',
                Starting_Date_of_Payment_Term__c = 'Invoice Date',
                Order_Lead_Time__c = 5,
                Prospect__c = theLead.Id,
                Approval_Status__c = 'Draft',
                Authorized_Brand_Name_To_Oracle__c = 'Test'
            );
            insert sp;

            theLead.Street = 'test street';
            theLead.City = 'New York';
            theLead.State = 'NY';
            theLead.Country = 'US';
            theLead.PostalCode = '11111';
            theLead.Intended_Brand__c = 'EGO';
            theLead.Distributor_or_Dealer__c = 'Direct Dealer';
            theLead.OwnerId = salesManager1.Id;
            update theLead;


        }

        Test.stopTest();
    }

    static testMethod void testMethod4() {
        User beamUser = Test_SalesData.createUser('TestBeamUser', 'BEAM', 'BEAM');
        User salesManager1 = Test_SalesData.createUser('TestLoginUser', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesManager2 = Test_SalesData.createUser('TestSalesManager', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesDirector = Test_SalesData.createUser('TestSalesDirector', 'OPE_Sales_Director', 'NA Sales Manager');

        Test.startTest();
        System.runAs(beamUser) {
            Test_SalesData.createProspectAssignmentRules(salesManager1.Id, salesManager2.Id, salesDirector.Id);
            Prospect_Assignment_Rule__c rule1 = new Prospect_Assignment_Rule__c(
                ORG_Code__C = CCM_Constants.ORG_CODE_CCA,
                Brand__c = 'EGO',
                Nation__c = 'CA',
                State__c = 'NY',
                Distributor_or_Dealer__c = 'Canada Distributor',
                Cluster__c = 'CA-CG09',
                Sub_Cluster__c = 'DT - OPE CA ER',
                Sales_Channel__c = 'SC01',
                Sales_Group__c = 'SG15',
                User__c = salesManager1.Id,
                Director__c = salesDirector.Id,
                Is_Flex_Auto_Only__c = false
            );
            insert rule1;
            Lead theLead1 = Test_SalesData.createProspectData_CA();
            theLead1.ownerId = salesManager1.Id;
            theLead1.Status = 'Assigned';
            Lead theLead2 = Test_SalesData.createProspectData_CA();
            theLead2.OwnerId = salesManager2.Id;
            salesManager1 = [SELECT Id, UserRole.DeveloperName, Profile.Name FROM User WHERE Id = :salesManager1.Id LIMIT 1];
            CCM_V_Lead_Validations.validateOwnerIsSalesManager(theLead1, new Map<Id, User>{salesManager1.Id => salesManager1}, theLead2, new Set<Id>(), new Set<Id>());
        }

        Test.stopTest();
    }

    static testMethod void testMethod5() {
        
        User salesManager1 = Test_SalesData.createUser('TestLoginUser', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesManager2 = Test_SalesData.createUser('TestSalesManager', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesDirector = Test_SalesData.createUser('TestSalesDirector', 'OPE_Sales_Director', 'NA Sales Manager');

        

        System.runAs(salesManager1) {
            Test_SalesData.createProspectAssignmentRules(salesManager1.Id, salesManager2.Id, salesDirector.Id);
            Lead theLead = Test_SalesData.createProspectData();
            theLead.Street = 'test street';
            theLead.City = 'New York';
            theLead.State = 'NY';
            theLead.Country = 'US';
            theLead.PostalCode = '11111';
            theLead.Intended_Brand__c = 'EGO';
            theLead.Distributor_or_Dealer__c = 'Direct Dealer';
            theLead.Customer_Cluster__c = 'CNA-CG01';
            theLead.Sales_Channel__c = 'SC01';
            theLead.Customer_Sub_Cluster__c= 'KA001';
            update theLead;

            CCM_UpaftCtrl.inFutureContext = true;
            Sales_Program__c sp = new Sales_Program__c(
                Name = 'TestSP001',
                Brands__c = 'EGO',
                Payment_Term__c = 'NA059',
                Payment_Term_Description__c = '2% discount is given if the invoice is paid within 10 days ,Or One whole invoice by 6 payments installment without discount.',
                Freight_Term__c = 'PPC',
                Price_List__c = 'Standard Price List',
                Starting_Date_of_Payment_Term__c = 'Invoice Date',
                Order_Lead_Time__c = 5,
                Prospect__c = theLead.Id,
                Sales_Group__c = 'SG01',
                Authorized_Brand_Name_To_Oracle__c = 'Test',
                RecordTypeId = CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_ID
            );
            insert sp;
            sp.Co_Op_Discount__c = 5;
            sp.Approval_Status__c = 'Approved';
            update sp;

            Attachment_Management__c am1 = Test_SalesData.createAttachmentManagement(theLead.Id, null, 'Brand Program');
            Attachment_Management__c am2 = Test_SalesData.createAttachmentManagement(theLead.Id, null, 'General Agreement');
            Attachment_Management__c am3 = Test_SalesData.createAttachmentManagement(theLead.Id, null, 'Questionnaire');

            Test.startTest();
            theLead.Status = 'Qualified';
            // CCM_V_Lead_Validations.forCoverage();
            // CCM_Lead_Validation_Rules_Handler.forCoverage();
            update theLead;

            Test.stopTest();
        } 
    }
}