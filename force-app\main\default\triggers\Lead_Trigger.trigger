trigger Lead_Trigger on Lead(before insert, after insert, before update, after update, before delete, after delete) {
    new Triggers()
    .bind(Triggers.Evt.beforeinsert, new CCM_Lead_Validation_Rules_Handler())
    .bind(Triggers.Evt.beforeinsert, new CCM_LeadAutoAssignmentHandler())
    .bind(Triggers.Evt.beforeinsert, new CCM_SalesOrgValidationHandler())
    //.bind(Triggers.Evt.afterinsert, new CCM_UpdateLeadFromCampaignHandler())
    .bind(Triggers.Evt.beforeupdate, new CCM_Lead_Validation_Rules_Handler())
    .bind(Triggers.Evt.beforeupdate, new CCM_ProspectOwnerUpdateHandler())
    .bind(Triggers.Evt.beforeupdate, new CCM_LeadAutoAssignmentHandler())
    .bind(Triggers.Evt.beforeupdate, new CCM_SalesOrgValidationHandler())
    //.bind(Triggers.Evt.beforeupdate, new CCM_LeadApprovalFieldsUpdateHandler())

    //.bind(Triggers.Evt.afterupdate, new CCM_LeadApprovalSendEmailHandler())
    .bind(Triggers.Evt.afterupdate, new CCM_ProspectSendApproval())
    .bind(Triggers.Evt.afterupdate, new CCM_LeadConvertHandler())
    .bind(Triggers.Evt.afterupdate, new CCM_SyncSalesProgramInfo())
    .bind(Triggers.Evt.afterupdate, new CCM_SyncDelearNumToCampaign())


    .bind(Triggers.Evt.afterinsert, new CCM_Lead_AutoGenerateAddressHandler())
    .bind(Triggers.Evt.afterupdate, new CCM_Lead_AutoGenerateAddressHandler())

    .bind(Triggers.Evt.afterupdate, new CCM_ServiceSetupNotificationHandler())
    
    .manage();
}