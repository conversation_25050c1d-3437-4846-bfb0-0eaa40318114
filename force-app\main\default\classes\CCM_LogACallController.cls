/**
 * <AUTHOR>
 * @date 2023-09-27
 * @description Log a Call button backend logic
 */
public with sharing class CCM_LogACallController{
	@AuraEnabled
	public static String getSource(String recordId, String keyword){
		String objType = Id.valueOf(recordId).getSObjectType().getDescribe().getName();
		if (objType == 'Account'){
			List<Account> accountList = [SELECT Id, Name
			                             FROM Account
			                             WHERE Id = :recordId];
			if (!accountList.isEmpty()){
				SourceWrapper wrapper = new SourceWrapper();
				wrapper.type = 'Account';
				wrapper.tToday = Date.today();
				wrapper.accountSources = new List<AccountSource>();
				for (Account acc : accountList){
					AccountSource accSource = new AccountSource();
					accSource.accountId = acc.Id;
					accSource.accountName = acc.Name;
					wrapper.accountSources.add(accSource);
				}

				List<Contact> contacts = [SELECT Id, Name
				                          FROM Contact
				                          WHERE AccountId IN:accountList];
				if (!contacts.isEmpty()){
					wrapper.contactSources = new List<ContactSource>();
					for (Contact con : contacts){
						ContactSource conSource = new ContactSource();
						conSource.contactId = con.Id;
						conSource.contactName = con.Name;
						wrapper.contactSources.add(conSource);
					}
				}

				List<String> storeLocationRecordTypeIds = new List<String>{ CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID, CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID };
				List<Account> storeLocations = [SELECT Id, Name
				                                FROM Account
				                                WHERE Related_Entity__c IN:accountList AND RecordTypeId IN:storeLocationRecordTypeIds];
				if (!storeLocations.isEmpty()){
					wrapper.storeVisitSources = new List<StoreVisitSource>();
					for (Account storeLocation : storeLocations){
						StoreVisitSource svSource = new StoreVisitSource();
						svSource.storeVisitId = storeLocation.Id;
						svSource.storeVisitName = storeLocation.Name;
						wrapper.storeVisitSources.add(svSource);
					}
				}
				return JSON.serialize(wrapper);
			} else{
				return null;
			}
		} else if (objType == 'Lead'){
			List<Lead> leadList = [SELECT Id, Name
			                       FROM Lead
			                       WHERE Id = :recordId];
			if (!leadList.isEmpty()){
				SourceWrapper wrapper = new SourceWrapper();
				wrapper.type = 'Lead';
				wrapper.tToday = Date.today();
				wrapper.accountSources = new List<AccountSource>();
				for (Lead acc : leadList){
					AccountSource accSource = new AccountSource();
					accSource.accountId = acc.Id;
					accSource.accountName = acc.Name;
					wrapper.accountSources.add(accSource);
				}

				List<Contact> contacts = [SELECT Id, Name
				                          FROM Contact
				                          WHERE Prospect__c IN:leadList];
				if (!contacts.isEmpty()){
					wrapper.contactSources = new List<ContactSource>();
					for (Contact con : contacts){
						ContactSource conSource = new ContactSource();
						conSource.contactId = con.Id;
						conSource.contactName = con.Name;
						wrapper.contactSources.add(conSource);
					}
				}

				// List<Account_Address__c> addressList = [SELECT Id, Name FROM Account_Address__c WHERE Prospect__c IN :leadList];
				List<Account> potentialStoreLocations = [SELECT Id, Name
				                                         FROM Account
				                                         WHERE Related_Entity_Prospect__c IN:leadList AND RecordTypeId = :CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID];
				if (!potentialStoreLocations.isEmpty()){
					wrapper.storeVisitSources = new List<StoreVisitSource>();
					for (Account address : potentialStoreLocations){
						StoreVisitSource svSource = new StoreVisitSource();
						svSource.storeVisitId = address.Id;
						svSource.storeVisitName = address.Name;
						wrapper.storeVisitSources.add(svSource);
					}
				}
				return JSON.serialize(wrapper);
			} else{
				return null;
			}
		} else if (objType == 'Campaign'){
			// List<CampaignMember> campaignMemberList = [SELECT AccountId, LeadId
			// 							   FROM CampaignMember
			// 							   WHERE CampaignId = :recordId];

			// Set<String> accIdSet = new Set<String>();
			// Set<String> leadIdSet = new Set<String>();
			// for(CampaignMember member : campaignMemberList) {
			// 	if(String.isNotBlank(member.AccountId)) {
			// 		accIdSet.add(member.AccountId);
			// 	}
			// 	if(String.isNotBlank(member.LeadId)) {
			// 		leadIdSet.add(member.LeadId);
			// 	}
			// }

			SourceWrapper wrapper = new SourceWrapper();
			wrapper.tToday = Date.today();
			wrapper.type = 'Campaign';
			wrapper.accountSources = new List<AccountSource>();

			String query = 'SELECT Id, Name FROM Account WHERE RecordType.Name = \'Channel\'';
			if(String.isNotBlank(keyword)) {
				keyword = '%' + keyword + '%';
				query += ' AND Name LIKE :keyword';
			}
			query += ' LIMIT 20';

			List<Account> accountList = (List<Account>)Database.query(query);
			if(!accountList.isEmpty()) {
				for (Account acc : accountList){
					AccountSource accSource = new AccountSource();
					accSource.accountId = acc.Id;
					accSource.accountName = acc.Name;
					wrapper.accountSources.add(accSource);
				}
			}

			query = 'SELECT Id, Name FROM Lead';
			if(String.isNotBlank(keyword)) {
				keyword = '%' + keyword + '%';
				query += ' WHERE Name LIKE :keyword';
			}
			query += ' LIMIT 20';
			List<Lead> leadList = (List<Lead>)Database.query(query);
			if(!leadList.isEmpty()) {
				for (Lead acc : leadList){
					AccountSource accSource = new AccountSource();
					accSource.accountId = acc.Id;
					accSource.accountName = acc.Name;
					wrapper.accountSources.add(accSource);
				}
			}

			if(accountList.isEmpty() && leadList.isEmpty()) {
				return null;
			}

			// List<Contact> contacts = [SELECT Id, Name
			// 	                          FROM Contact
			// 	                          WHERE Prospect__c IN :leadIdSet OR AccountId IN :accIdSet];
			// if (!contacts.isEmpty()){
			// 	wrapper.contactSources = new List<ContactSource>();
			// 	for (Contact con : contacts){
			// 		ContactSource conSource = new ContactSource();
			// 		conSource.contactId = con.Id;
			// 		conSource.contactName = con.Name;
			// 		wrapper.contactSources.add(conSource);
			// 	}
			// }

			// List<String> storeLocationRecordTypeIds = new List<String>{ CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID, CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID };
			// List<Account> storeLocations = [SELECT Id, Name
			// 								FROM Account
			// 								WHERE (Related_Entity__c IN :accIdSet AND RecordTypeId IN :storeLocationRecordTypeIds)
			// 								OR (Related_Entity_Prospect__c IN :leadIdSet AND RecordTypeId = :CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID)];
			// if (!storeLocations.isEmpty()){
			// 	wrapper.storeVisitSources = new List<StoreVisitSource>();
			// 	for (Account storeLocation : storeLocations){
			// 		StoreVisitSource svSource = new StoreVisitSource();
			// 		svSource.storeVisitId = storeLocation.Id;
			// 		svSource.storeVisitName = storeLocation.Name;
			// 		wrapper.storeVisitSources.add(svSource);
			// 	}
			// }
			return JSON.serialize(wrapper);
		}
		else if (objType == 'Opportunity') {
			List<Opportunity> opportunityList = [SELECT Id, Name,AccountId,Account.Name
			                       FROM Opportunity
			                       WHERE Id = :recordId];
			if (!opportunityList.isEmpty()){
				SourceWrapper wrapper = new SourceWrapper();
				wrapper.tToday = Date.today();
				wrapper.type = objType;
				wrapper.accountSources = new List<AccountSource>();
				wrapper.customerSource = new List<CustomerSource>();
				for (Opportunity acc : opportunityList){
					AccountSource accSource = new AccountSource();
					CustomerSource custSource = new CustomerSource();
					accSource.accountId = acc.Id;
					accSource.accountName = acc.Name;
					custSource.customerName = acc.Account.Name;
					custSource.customerId = acc.AccountId;
					wrapper.accountSources.add(accSource);
					wrapper.customerSource.add(custSource);
				}
				return JSON.serialize(wrapper);
			} else{
				return null;
			}
		} else{
			return null;
		}
	}

	@AuraEnabled
	public static String getStores(String accountId, String keyword){
		SourceWrapper wrapper = new SourceWrapper();
		List<String> storeLocationRecordTypeIds = new List<String>{ CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID, CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID };
		List<Account> storeLocations = new List<Account>();
		if(String.isBlank(keyword)) {
			storeLocations = [SELECT Id, Name
						  FROM Account
						  WHERE (Related_Entity__c = :accountId AND RecordTypeId IN :storeLocationRecordTypeIds)
						  OR (Related_Entity_Prospect__c = :accountId AND RecordTypeId = :CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID) LIMIT 20];
		}
		else {
			keyword = '%' + keyword + '%';
			storeLocations = [SELECT Id, Name
						  FROM Account
						  WHERE Name Like :keyword AND ((Related_Entity__c = :accountId AND RecordTypeId IN :storeLocationRecordTypeIds)
						  OR (Related_Entity_Prospect__c = :accountId AND RecordTypeId = :CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID))];
		}

		if (!storeLocations.isEmpty()){
			wrapper.storeVisitSources = new List<StoreVisitSource>();
			for (Account storeLocation : storeLocations){
				StoreVisitSource svSource = new StoreVisitSource();
				svSource.storeVisitId = storeLocation.Id;
				svSource.storeVisitName = storeLocation.Name;
				wrapper.storeVisitSources.add(svSource);
			}
			return JSON.serialize(wrapper);
		}
		else {
			return null;
		}
	}

	@AuraEnabled
	public static string getVersionDataUrl(List<String> documentIds, String recordId){
		// String currentYear = String.valueOf(Date.today().year());
		// String customerProfileQuery = 'SELECT Id FROM Customer_Profile__c WHERE Effective_Year__c = :currentYear AND ';
		// String objType = Id.valueOf(recordId).getSObjectType().getDescribe().getName();
		// if(objType == 'Account') {
		//     customerProfileQuery += 'Customer__c = :recordId';
		// }
		// else if(objType == 'Lead') {
		//     customerProfileQuery += 'Prospect__c = :recordId';
		// }
		// List<Customer_Profile__c> customerProfiles = (List<Customer_Profile__c>)Database.query(customerProfileQuery);
		// List<Customer_Profile_Photo__c> customerProfilePhotos = new List<Customer_Profile_Photo__c>();
		List<ContentVersion> versions = [SELECT ContentDocumentId, VersionDataUrl
		                                 FROM ContentVersion
		                                 WHERE ContentDocumentId IN:documentIds];
		Map<String, String> documentVersionMap = new Map<String, String>();
		for (ContentVersion version : versions){
			documentVersionMap.put(version.ContentDocumentId, version.VersionDataUrl);
			// for(Customer_Profile__c cp : customerProfiles) {
			//     Customer_Profile_Photo__c objPhoto = new Customer_Profile_Photo__c();
			//     objPhoto.Customer_Profile__c = cp.Id;
			//     objPhoto.Photo_Category__c = 'Sync From Call Log';
			//     objPhoto.ContentVersionId__c = version.Id;
			//     customerProfilePhotos.add(objPhoto);
			// }
		}
		// insert customerProfilePhotos;
		return JSON.serialize(documentVersionMap);
	}
	@AuraEnabled
	public static void deleteFile(String documentId){
		List<ContentDocument> documents = [SELECT Id
		                                   FROM ContentDocument
		                                   WHERE Id = :documentId];
		Database.delete (documents, false);
	}
	@AuraEnabled
	public static String saveCall(String param){
		SaveResult result = new SaveResult();
		try{
			Map<String, Object> paramMap = (Map<String, Object>)JSON.deserializeUntyped(param);
			if (!paramMap.containsKey('accountId')){
				result.isSuccess = false;
				result.errorMsg = 'Please Fill In Related To.';
				return JSON.serialize(result);
			}

			List<String> taskIds = createTask(paramMap);
			//Added by Zoe for visit Journey on 2024-9-27
			Map<String,Integer> taskId2NumMap = linkFileToTask(taskIds, paramMap);
			updateVisitJourneyItem(taskId2NumMap);
			//Added by Zoe for visit Journey on 2024-9-27
			String recordId = (String) paramMap.get('accountId');
			List<String> documentIds = new List<String>();
			for (Object item : (List<Object>)paramMap.get('documentIds')){
				String documentId = (String) item;
				documentIds.add(documentId);
			}
			syncToCustomerProfile(recordId, documentIds);
			result.isSuccess = true;
		} catch (Exception ex){
			System.debug(ex.getStackTraceString());
			result.isSuccess = false;
			result.errorMsg = ex.getMessage();
		}
		return JSON.serialize(result);
	}
	//Added by Zoe for visit Journey on 2024-9-27
	private static void updateVisitJourneyItem(Map<String,Integer> taskId2NumMap){
		List<Visit_Journey_Item__c> vjiList = [SELECT Id,Ref_Id__c from Visit_Journey_Item__c WHERE Ref_Id__c in:taskId2NumMap.keySet()];
		for(Visit_Journey_Item__c  vji: vjiList){
			if(taskId2NumMap.containsKey(vji.Ref_Id__c)){
				vji.Total_Files__c = taskId2NumMap.get(vji.Ref_Id__c);
			}
		}
		update vjiList;
	}
	private static List<String> createTask(Map<String, Object> paramMap){
		String recordId = (String) paramMap.get('accountId');
		String objType = Id.valueOf(recordId).getSObjectType().getDescribe().getName();
		List<Task> tasks = new List<Task>();
		Task t = new Task();
		if (objType == 'Account'){
			t.WhatId = (String) paramMap.get('accountId');
		} else if (objType == 'Lead'){
			t.WhoId = (String) paramMap.get('accountId');
		} else if  (objType == 'Opportunity'){
			t.WhatId = (String) paramMap.get('accountId');
		}

		if (objType != 'Lead' && objType != 'Opportunity'){
			if (paramMap.containsKey('contactId')){
				t.WhoId = (String) paramMap.get('contactId');
			}
		}

		// if(objType != 'Lead') {
		//     if(paramMap.containsKey('storeVisitId')) {
		//         t.Account_Address__c = (String)paramMap.get('storeVisitId');
		//     }
		// }

		t.Subject = (String) paramMap.get('subject');

		if (paramMap.containsKey('comments')){
			t.Description = (String) paramMap.get('comments');
		}

		if (paramMap.containsKey('visitDate')){
			t.Date_of_Visit_Call__c = Date.valueOf((String) paramMap.get('visitDate'));
		}

		if (paramMap.containsKey('actionItems')){
			t.Action_Items_Takeaways__c = (String) paramMap.get('actionItems');
		}

		if (paramMap.containsKey('callSubject')){
			t.Call_Subject__c = (String) paramMap.get('callSubject');
		}

		if (paramMap.containsKey('purpose')){
			t.Purpose__c = (String) paramMap.get('purpose');
		}
		if (paramMap.containsKey('followUpSubject')){
			t.Follow_up_Subject__c = (String) paramMap.get('followUpSubject');
		}
		if (paramMap.containsKey('followUpDate')){
			t.Follow_up_Date__c = Date.valueOf((String) paramMap.get('followUpDate'));
		}
		t.ActivityDate = Date.today();
		t.Status = 'Completed';
		tasks.add(t);
		if (paramMap.containsKey('storeVisitId')){
			Task tClone = t.clone(false, true);
			tClone.WhatId = (String) paramMap.get('storeVisitId');
			tClone.WhoId = null;
			tasks.add(tClone);
		}
		if(paramMap.containsKey('campaignId')) {
			Task tClone = t.clone(false, true);
			tClone.WhatId = (String) paramMap.get('campaignId');
			tClone.WhoId = null;
			tasks.add(tClone);

			createCampaignMember((String) paramMap.get('campaignId'), (String) paramMap.get('accountId'), objType);
		}
        if (paramMap.containsKey('followUpDate') || paramMap.containsKey('followUpSubject')){
			Task tClone = t.clone(false, true);
			tClone.WhatId = (String) paramMap.get('accountId');
			tClone.Subject = (String) paramMap.get('followUpSubject');
			tClone.Date_of_Visit_Call__c = Date.valueOf((String) paramMap.get('followUpDate'));
			tClone.Follow_up_Date__c = null;
			tClone.Follow_up_Subject__c = null;
			tClone.WhoId = null;
			tClone.ActivityDate = Date.valueOf((String) paramMap.get('followUpDate'));
			tClone.Status = 'Not Started';
			tasks.add(tClone);
		}
		// if (paramMap.containsKey('customerId')){
		// 		Task tClone = t.clone(false, true);
		// 		tClone.WhatId = (String) paramMap.get('customerId');
		// 		tClone.WhoId = null;
		// 		tasks.add(tClone);
		// 	}
		insert tasks;
		List<String> taskIds = new List<String>();
		for (Task tItem : tasks){
			taskIds.add(tItem.Id);
		}
		return taskIds;
	}

	//Changed by Zoe for visit Journey on 2024-9-27
	private static Map<String,Integer> linkFileToTask(List<String> taskIds, Map<String, Object> paramMap){
		if (!paramMap.containsKey('documentIds')){
			return null;
		}
		Map<String,Integer> taskId2NumMap = new Map<String,Integer>();
		List<ContentDocumentLink> links = new List<ContentDocumentLink>();
		Integer num = 0;
		for (Object item : (List<Object>)paramMap.get('documentIds')){
			num = num + 1;
			for (String taskId : taskIds){
				String documentId = (String) item;
				ContentDocumentLink link = new ContentDocumentLink();
				link.ContentDocumentId = documentId;
				link.LinkedEntityId = taskId;
				links.add(link);
			}
		}
		for (String taskId : taskIds){
			taskId2NumMap.put(taskId,num);
		}
		insert links;
		return taskId2NumMap;
	}

	private static void createCampaignMember(String campaignId, String accountId, String objType) {

		List<CampaignMember> members = [SELECT Id FROM CampaignMember WHERE CampaignId = :campaignId AND (AccountId = :accountId OR LeadId = :accountId)];
		if(!members.isEmpty()) {
			return;
		}

		CampaignMember member = new CampaignMember();
		member.CampaignId = campaignId;
		if(objType == 'Account') {
			member.AccountId = accountId;
		}
		else if(objType == 'Lead') {
			member.LeadId = accountId;
		}
		insert member;
	}

	private static void syncToCustomerProfile(String recordId, List<String> documentIds){
		String topLevelRecordId = '';
		String recordType = '';
		String recordName = '';
		String objType = Id.valueOf(recordId).getSObjectType().getDescribe().getName();
		Account acc = new Account();
		if (objType == 'Account'){
			acc = [SELECT Name, Related_Entity__c, Related_Entity_Prospect__c
			               FROM Account
			               WHERE Id = :recordId];
			List<Sales_Hierarchy__c> hierarchys = [SELECT X1st_tier_dealer__c, X2st_tier_dealer__c
			                                       FROM Sales_Hierarchy__c
			                                       WHERE X2st_tier_dealer__c = :recordId];
			recordName = acc.Name;
			if (String.isNotBlank(acc.Related_Entity__c)){
				topLevelRecordId = acc.Related_Entity__c;
				recordType = 'Account';
			} else if (String.isNotBlank(acc.Related_Entity_Prospect__c)){
				topLevelRecordId = acc.Related_Entity_Prospect__c;
				recordType = 'Lead';
			} else{
				topLevelRecordId = acc.Id;
				recordType = 'Account';
			}

			if (!hierarchys.isEmpty()){
				topLevelRecordId = hierarchys[0].X1st_tier_dealer__c;
				recordType = 'Account';
			}
		} else if (objType == 'Lead'){
			topLevelRecordId = recordId;
			recordType = 'Lead';
		}

		String currentYear = String.valueOf(Date.today().year());
		String customerProfileQuery = 'SELECT Id FROM Customer_Profile__c WHERE Effective_Year__c = :currentYear AND ';
		if (recordType == 'Account'){
			customerProfileQuery += 'Customer__c = :topLevelRecordId';
		} else if (recordType == 'Lead'){
			customerProfileQuery += 'Prospect__c = :topLevelRecordId';
		}

		// List<Customer_Profile__c> customerProfiles = (List<Customer_Profile__c>)Database.query(customerProfileQuery);//Changed by Zoe for customer photo on 2024-9-27, customer photo仅和客户有关
		List<Customer_Profile_Photo__c> customerProfilePhotos = new List<Customer_Profile_Photo__c>();
		List<ContentVersion> versions = [SELECT ContentDocumentId, VersionDataUrl, CreatedById, CreatedDate, FileExtension,Title
		                                 FROM ContentVersion
		                                 WHERE ContentDocumentId IN:documentIds];
		Map<String, String> documentVersionMap = new Map<String, String>();
		List<Visit_Journey_Item__c> itemList = new List<Visit_Journey_Item__c>();
		for (ContentVersion version : versions){
			documentVersionMap.put(version.ContentDocumentId, version.VersionDataUrl);
			// for (Customer_Profile__c cp : customerProfiles){//Changed by Zoe for customer photo on 2024-9-27, customer photo仅和客户有关
				Customer_Profile_Photo__c objPhoto = new Customer_Profile_Photo__c();
				// objPhoto.Customer_Profile__c = cp.Id;//Changed by Zoe for customer photo on 2024-9-27, customer photo仅和客户有关
				objPhoto.Photo_Category__c = 'Sync From Call Log';
				//Added by Zoe for customer photo on 2024-9-27
				objPhoto.Related_Customer__c = acc.Related_Entity__c;
				if(objPhoto.Related_Customer__c == null){
					objPhoto.Related_Customer__c = acc.Id;
				}
                objPhoto.Related_Store__c = acc.Id;
                //Added by Zoe for customer photo on 2024-9-27
				objPhoto.ContentVersionId__c = version.Id;
				objPhoto.Sync_From__c = recordName;
				customerProfilePhotos.add(objPhoto);
			// }
			//为每个log a call的file生成数据visit journery ********开始----
			//Changed by Zoe for visit Journey on 2024-9-27,file不需要单独生成visit journery
			// if (objType == 'Account'){
			// 	Visit_Journey_Item__c item = new Visit_Journey_Item__c();
			// 	item.Type__c = CCM_Constants.VJ_UploadFiles;
			// 	item.Ref_Id__c = version.ContentDocumentId;
			// 	item.Related_to_location__c = recordId;
			// 	item.Created_Time__c = version.CreatedDate;
			// 	item.Created_By__c = version.CreatedById;
			// 	item.Name__c = version.Title +'.'+ version.FileExtension;
			// 	itemList.add(item);
			// }

		}
		if (itemList.size() > 0){
			List<Database.UpsertResult> urlist =Database.upsert (itemList, Visit_Journey_Item__c.Ref_Id__c.getDescribe().getSObjectField(), false);
		}
		//为每个log a call的file生成数据visit journery ********结束----
		insert customerProfilePhotos;
	}
	@AuraEnabled
	public static string getContentVersions(String taskId){
		List<ContentVersion> versions = new List<ContentVersion>();
		List<ContentDocumentLink> links = [SELECT ContentDocumentId
		                                   FROM ContentDocumentLink
		                                   WHERE LinkedEntityId = :taskId];
		Set<String> documentIds = new Set<String>();
		for (ContentDocumentLink link : links){
			documentIds.add(link.ContentDocumentId);
		}
		if (!documentIds.isEmpty()){
			versions = [SELECT Id, VersionDataUrl
			            FROM ContentVersion
			            WHERE ContentDocumentId IN:documentIds];
		}
		return JSON.serialize(versions);
	}
	public class SaveResult{
		public Boolean isSuccess{ get; set; }
		public String errorMsg{ get; set; }
	}
	public class SourceWrapper{
		public List<AccountSource> accountSources{ get; set; }
		public List<ContactSource> contactSources{ get; set; }
		public List<StoreVisitSource> storeVisitSources{ get; set; }
		public List<CustomerSource> customerSource{ get; set; }
		public Date tToday{ get; set; }
		public String type{ get; set; }
	}
	public class AccountSource{
		public String accountName{ get; set; }
		public String accountId{ get; set; }
	}
	public class ContactSource{
		public String contactName{ get; set; }
		public String contactId{ get; set; }
	}
	public class StoreVisitSource{
		public String storeVisitName{ get; set; }
		public String storeVisitId{ get; set; }
	}
	public class CustomerSource{
		public String customerName{ get; set; }
		public String customerId{ get; set; }
	}
}