/**
About
-----
Description: This Class is used for create/edit case in lightning .

Created for: Chervon classic to lightning
Created: 05 15 2019

Update History
--------------
Created: 05 15 2019 – <EMAIL>
@Revision: 2023-11-01 <PERSON> Add specific modals detection logic.
-------------
**/
public class CCM_CaseController {
    @AuraEnabled
    public static String init(String accId, String warId) {
        Map<String, Object> resultMap = new Map<String, Object>();
        User currentUser = QueryUtils.getCurrentUser();
        Boolean isCSRUser;
        if (currentUser.Profile.Name.endsWith('Customer Service')) {
            isCSRUser = true;
        } else {
            isCSRUser = false;
        }
        resultMap.put('isCSRUser', isCSRUser);
        Contact thisContact = new Contact();
        Account thisAccount = new Account();
        Warranty__c thisWarranty = new Warranty__c();
        if (String.isNotBlank(accId) && !accId.startsWith('CF')) {
            thisAccount = AccountService.getAccountByID(accId);
            List<Contact> conLst = [SELECT Id, Name, AccountId FROM Contact WHERE AccountId =: thisAccount.Id];
            if (conLst.size() > 0) {
                thisContact = conLst[0];
                resultMap.put('ConId', thisContact.Id);
                resultMap.put('ConName', thisContact.Name);
            }
            List<Warranty__c> warrList = [SELECT Id,Name,Brand_Name__c FROM Warranty__c WHERE AccountCustomer__c =: accId];
            if(warrList.size() == 1 && String.isBlank(warId)){
                resultMap.put('WarId', warrList[0].Id);
                resultMap.put('WarName', warrList[0].Name);
                resultMap.put('BrandName', warrList[0].Brand_Name__c);
            }
            resultMap.put('AccId', thisAccount.Id);
            resultMap.put('AccName', thisAccount.Name);
            List<Account> aList = [SELECT RecordType.Name FROM Account WHERE Id =: accId];
            if(aList.size() > 0 && aList[0].RecordType.Name == 'Channel'){
                resultMap.put('isChannel', true);
            }

        }
        if (String.isNotBlank(warId)) {
            thisWarranty = WarrantyService.getWarrantySimpleByIDforCase(warId);
            resultMap.put('WarId', thisWarranty.Id);
            resultMap.put('WarName', thisWarranty.Name);
            resultMap.put('BrandName', thisWarranty.Brand_Name__c);
            thisAccount = AccountService.getAccountByID(thisWarranty.AccountCustomer__c);
            List<Contact> conLst = [SELECT Id, Name, AccountId FROM Contact WHERE AccountId =: thisWarranty.AccountCustomer__c];
            if (conLst.size() > 0) {
                thisContact = conLst[0];
                resultMap.put('ConId', thisContact.Id);
                resultMap.put('ConName', thisContact.Name);
            }
            resultMap.put('AccId', thisAccount.Id);
            resultMap.put('AccName', thisAccount.Name);
        }
        return JSON.serialize(resultMap);
    }
    @AuraEnabled
    public static String saveCase(String details,Boolean isFromCustomerToNewCase) {
        string concatMsg = '';
        try{
            details = details.replace('[', '').replaceAll(']', '').replace('[', '').replace('[', '').replace('[', '');
            system.debug(details);
            Case con = (Case)JSON.deserialize(details,Case.class);
            Case thisCase = new Case();
            if(con.Id != null){
                thisCase.Id = con.Id;

            }
            thisCase.RecordTypeId = con.RecordTypeId;
            thisCase.Warranty_Item__c = con.Warranty_Item__c;
            thisCase.Project_Customer__c = con.Project_Customer__c;
            thisCase.Case_Type__c = con.Case_Type__c;
            thisCase.Case_Type2__c = con.Case_Type__c;
            thisCase.Warranty__c = con.Warranty__c;
            thisCase.ProductId = con.ProductId;
            thisCase.Status = con.Status;
            thisCase.Status2__c = con.Status;
            thisCase.Fedex_Link__c = '<a href=\"https://www.fedex.com/apps/fedextrack/?action=track&trackingnumber='+con.FED_EX__c+'&cntry_code=us&locale=en_US\">'+con.FED_EX__c+'</a>';
            thisCase.AttachmentURL__c = String.valueOf(con.AttachmentURL__c);
            if(con.Case_Type__c == 'Service Referral' && con.Warranty__c != null){

                Warranty__c warr = [SELECT Lost_Receipt__c,Receipt_received_and_verified__c,Receipt_Received_Warranty_Ineligible__c FROM Warranty__c WHERE Id =: con.Warranty__c];
                if(!(warr.Lost_Receipt__c || warr.Receipt_received_and_verified__c || warr.Receipt_Received_Warranty_Ineligible__c)){
                    concatMsg = 'receipt';
                    return concatMsg;
                }
            }
            if(isFromCustomerToNewCase && con.Case_Type__c == 'Service Referral' && con.Warranty__c == null){
                concatMsg = 'receipt';
                return concatMsg;
            }
            String pri = null;
            if(con.Product_Related_Issue__c != null){
                pri = '\'';
                for(String priitem : con.Product_Related_Issue__c.split(',')){
                   pri = pri + priitem.replaceAll('"', '') + ';';
                }
                pri = pri + '\'';
            }
            system.debug(pri);
            //thisCase.Product_Related_Issue__c = pri;
            if(con.Id != null){
                if(thisCase.Product_Related_Issue__c != null){
                     thisCase.Product_Related_Issue__c = thisCase.Product_Related_Issue__c.replaceAll('[', '').replaceAll(']', '').replaceAll('\\', '');
                }

                update thisCase;
            }


             //system.debug('*** con: ' + con.Product_Related_Issue__c.replaceAll(',',';').replaceAll('"',''));
            // System.debug('*** thisCase.ProductId: ' + thisCase.ProductId);

            List<RecordType> caseRecordType = [Select Name FROM RecordType
                                                 WHERE Id =:thisCase.RecordTypeId LIMIT 1] ;

            System.debug(thisCase);
            System.debug(caseRecordType[0]);
            System.debug(thisCase.RecordType);
            if (thisCase != null && thisCase.RecordTypeId != null && caseRecordType[0].Name == 'Recall') {
                System.debug('enter if!!!');
                Warranty_Item__c warItem        = QueryUtils.getWarrantyItem(thisCase.Warranty_Item__c);
                Project_Customer__c proCUstomer = QueryUtils.getProjectCustomer(thisCase.Project_Customer__c);
                if (warItem != null) {
                    thisCase.ProductId   = warItem.Product__c;
                    thisCase.Warranty__c = warItem.Warranty__c;
                    warItem.HasRecalled__c = true;
                }
                if (proCustomer != null) {
                    thisCase.Project__c  = proCUstomer.Project__c;
                }
                //update thisCase;
                update warItem;
            } else {

                //System.debug('enter else!!!');
                if(thisCase.Case_Type__c == 'Warranty Order') {
                    if(thisCase.Warranty__c == null ){
                        StringException se = new StringException();
                        se.setMessage('Warranty Number: You must enter a value.');
                        throw se;
                    }
                    if(thisCase.ProductId == null) {
                        StringException se = new StringException();
                        se.setMessage('Product: You must enter a value.');
                        throw se;
                    }
                }

            }
            // if (thisCase.Warranty__c != null) {
            //     if (thisCase.Status == 'Closed' && (thisCase.Case_Type__c == 'Warranty Order'  || thisCase.Case_Type__c == 'Non-warranty Order')) {
            //         List<Warranty__c> waLst = [SELECT Id, One_Time_Exception__c, Lost_Receipt__c,ClosedCase__c
            //                                     FROM Warranty__c
            //                                     WHERE Id =: thisCase.Warranty__c];
            //         List<Warranty_Item__c> waiLst = [SELECT Id, Warranty__c, Is_Expired__c, Expiration_Date_New__c
            //                                             FROM Warranty_Item__c
            //                                             WHERE Warranty__c =: thisCase.Warranty__c];
            //         if (waLst[0].Lost_Receipt__c == true && waLst[0].One_Time_Exception__c == true && waiLst.size() > 0) {
            //             List<Warranty_Item__c> wi_update = new List<Warranty_Item__c>();
            //             for (Warranty_Item__c wi : waiLst) {
            //                 if (wi.Is_Expired__c == false) {
            //                     wi.Expiration_Date_New__c = date.today();
            //                     wi_update.add(wi);
            //                 }
            //             }
            //             waLst[0].ClosedCase__c = true;
            //             update waLst[0];
            //             update wi_update;
            //         }
            //     }
            // }
        }catch(Exception e) {
            //System.debug('*** e.getMessage(): ' + e.getMessage());
            concatMsg = e.getMessage();
            System.debug('*** concatMsg: ' + e.getLineNumber());
        }
        return concatMsg;
    }


    @AuraEnabled
    public static String changeRecallSolution(String projectCustomerId){
        String projectSolution = '';
        if (projectCustomerId != null) {
            Project_Customer__c proCustomer = QueryUtils.getProjectCustomer(projectCustomerId);
            projectSolution = proCustomer.Project__r.Solution__c;
        }
        return projectSolution;
    }

    @AuraEnabled
    public static String showRecommendation(String productId, String warrantyId){
        Map<String,Object> result = new Map<String,Object>();
        String message = '';
        String qaReturnMessage = '';
        Boolean isValidWarranty = true;
        Set<String> serialNumbers = new Set<String>();
        if(String.isNotBlank(productId) && String.isNotBlank(warrantyId)){
            List<Warranty_Item__c> wiList = [SELECT Store_Policy_Date__c,Expiration_Date_New__c,Warranty__r.Purchase_Date__c,ActualIndicator__c, Serial_Number__c,
                                                Warranty__r.One_Time_Exception__c,Product__r.Product_Type__c,Product__r.ProductCode,Warranty__r.Brand_Name__c
                                            FROM Warranty_Item__c
                                            WHERE Product__c =: productId
                                            AND Warranty__c =: warrantyId];
            for(Warranty_Item__c item : wiList) {
                if(String.isNotBlank(item.Serial_Number__c)) {
                    serialNumbers.add(item.Serial_Number__c);
                }
                if(item.ActualIndicator__c == 'Out of Warranty') {
                    isValidWarranty = false;
                }
            }
            if(wiList.size() > 0 && wiList[0].Warranty__r.Brand_Name__c == 'EGO'){
                Date td = Date.today();

                System_Configuration__c productType = [SELECT Description__c FROM System_Configuration__c WHERE Name = 'Product Type'];
                System_Configuration__c productValue = [SELECT Description__c FROM System_Configuration__c WHERE Name = 'Product Value'];

                if(td < wiList[0].Store_Policy_Date__c){
                    message += 'If customer has an issue with tool. Confirm if end user spare part will solve customers’ issue, if so, send part. If tool needs to be replaced, we prefer to send tool directly to customer. However, if customer does not want to wait for us to ship them a new tool and the customer is still within store return policy you may allow the customer to return to their place of purchase for an exchange.\n';
                }

                if((wiList[0].Product__r.Product_Type__c == 'Charger' || wiList[0].Product__r.Product_Type__c == 'Battery' || productType.Description__c.containsIgnoreCase(wiList[0].Product__r.ProductCode)) && td > wiList[0].Store_Policy_Date__c && wiList[0].ActualIndicator__c == 'Vailid Warranty'){
                    message += 'Prior to replacement, please make sure the product is indeed defective; if needed ask customer to send pictures to confirm. If you are not completely confident and the defect(s) cannot be confirmed through the phone, redirect customer to servicing partner location. There the servicing partner can physically confirm if item is defective and assist the customer with the warranty process. If defects can be confirmed through the phone, we can send the replacement to customer.\n';
                }

                if(wiList[0].Warranty__r.One_Time_Exception__c && wiList[0].ActualIndicator__c == 'Vailid Warranty' ){
                    message += 'For one time exceptions use CS2-xxxx-FC or FC products for replacement if they are available. If they are not only then you will select a standard model. If a standard model is used the first choice should be a CS-xxxx if available, if not available then use the standard tool model.\n';
                }

                if( td > wiList[0].Warranty__r.Purchase_Date__c.addYears(2) && wiList[0].ActualIndicator__c == 'Vailid Warranty' && (wiList[0].Product__r.Product_Type__c == 'Charger' || wiList[0].Product__r.Product_Type__c == 'Battery')){
                    message += 'For a battery or charger with a full registration that has been used for more than 2 years use the FC item for replacement if available if not available use the standard model. \n';
                }

                if(td > wiList[0].Store_Policy_Date__c && wiList[0].ActualIndicator__c == 'Vailid Warranty' && td < wiList[0].Warranty__r.Purchase_Date__c.addYears(3) && !wiList[0].Warranty__r.One_Time_Exception__c){
                    message += 'Direct customers to service partner for repair; if a replacement is necessary, recommend to use the standard model for replacement.\n';
                }

                if(td > wiList[0].Store_Policy_Date__c && wiList[0].ActualIndicator__c == 'Vailid Warranty'&& td > wiList[0].Warranty__r.Purchase_Date__c.addYears(3)){
                    message += 'Direct customers to a service partner for repair; if a replacement is necessary, recommend to use the  FC products for replacement.\n';
                }

                if(wiList[0].Product__r.Product_Type__c == 'Product' && wiList[0].ActualIndicator__c == 'Out of Warranty'){
                    message += 'Assist customer in locating a service partner where they can take their tool to be repaired at their own cost. If they prefer to not pay to fix their tool recommend them to buy a new tool.\n';
                }

                if((wiList[0].Product__r.Product_Type__c == 'Charger' || wiList[0].Product__r.Product_Type__c == 'Battery') &&  wiList[0].ActualIndicator__c == 'Out of Warranty'){
                    message += 'Recommend customer to buy a new item. \n';
                }

                if( productValue.Description__c.containsIgnoreCase(wiList[0].Product__r.ProductCode)){
                    message += 'Need to collect malfunctioned machine from customers prior to send out the replacement. Seek leadership assistance/approval.\n';
                }
            }
        }

        if(String.isNotBlank(productId)) {
            String productCode = '';
            for(Product2 prod : [SELECT ProductCode FROM Product2 WHERE Id = :productId]) {
                productCode = prod.ProductCode;
            }
            if(String.isNotBlank(productCode)) {
                Boolean serialNumberInScope = false;
                String comments = '';
                List<QA_Return_Rule__c> returnRules = [SELECT Comments__c, Quantity_Left__c, (SELECT Serial_Number__c FROM QA_Return_Serial_Numbers__r) FROM QA_Return_Rule__c WHERE Product_Code__c = :productCode];
                for(QA_Return_Rule__c returnRule : returnRules) {
                    if(returnRule.Quantity_Left__c > 0) {
                        comments = returnRule.Comments__c;
                    }
                    if(!returnRule.QA_Return_Serial_Numbers__r.isEmpty()) {
                        for(QA_Return_Serial_Number__c serialNumber : returnRule.QA_Return_Serial_Numbers__r) {
                            if(serialNumbers.contains(serialNumber.Serial_Number__c)) {
                                serialNumberInScope = true;
                                break;
                            }
                        }
                    }
                    else {
                        serialNumberInScope = true;
                    }
                }
                if(String.isNotBlank(comments) && serialNumberInScope) {
                    qaReturnMessage = comments;
                }
            }
        }
        result.put('Message', message);
        if(!isValidWarranty) {
            qaReturnMessage = null;
        }
        result.put('qaReturnMessage', qaReturnMessage);
        return JSON.serialize(result);
    }

    @AuraEnabled
    public static String getWarrantyFindBrand(String warrantyId){

        if(warrantyId != null){
            Warranty__c wa = [SELECT Brand_Name__c FROM Warranty__c WHERE id =: warrantyId LIMIT 1];
            return wa.Brand_Name__c;
        }
        return null;
    }

    @AuraEnabled
    public static String getPiklistValues(String productId, String caseId) {
        Map<String, Object> resultMap = new Map<String, Object>();
        Product2 prod = [SELECT ProductCode, EBS_Description_of_Category_2__c FROM Product2 WHERE Id =: productId AND Is_History_Product__c = :CCM_Constants.blHistoryProduct];
        List<String> plValues = new List<String>();
        List<String> selectedValues = new List<String>();
        if(prod.EBS_Description_of_Category_2__c != null){
            List<PRI_Mapping__mdt> priList = [SELECT PRI_List__c FROM PRI_Mapping__mdt
                                              WHERE Product_Category__c = :prod.EBS_Description_of_Category_2__c
                                              OR Product_Category__c = :prod.ProductCode];


            if(priList.size() > 0){
                for(PRI_Mapping__mdt priMapping : priList) {
                    for(String pri : priMapping.PRI_List__c.split(';')){
                        plValues.add(pri);
                    }
                }
                if(caseId != null){
                    List<Case> pri = [SELECT Product_Related_Issue__c FROM Case WHERE Id =: caseId ];
                    if(pri.size() > 0 && pri[0].Product_Related_Issue__c != null){
                        for(String selectpri : pri[0].Product_Related_Issue__c.split(',')){
                            selectedValues.add(selectpri.substringBetween('"','"'));
                        }
                    }
                }
                resultMap.put('plValues', plValues);
                resultMap.put('selectedValues', selectedValues);
                return JSON.serialize(resultMap);
            }else{

                plValues.add('None');
                resultMap.put('plValues', plValues);
                resultMap.put('selectedValues', selectedValues);
                return JSON.serialize(resultMap);
            }
        }else{

            plValues.add('None');
            resultMap.put('plValues', plValues);
            resultMap.put('selectedValues', selectedValues);
            return JSON.serialize(resultMap);
        }
    }

    @AuraEnabled
    public static String returnLabelAndBatteryMessage(String productId, String returnLabel){
        Map<String,Object> result = new Map<String,Object>();
        if (returnLabel != null  && productId != null) {
            List<Product2> productList = [SELECT Id, Product_Type__c FROM Product2 WHERE Product_Type__c = 'Battery' AND Id =: productId AND Is_History_Product__c = :CCM_Constants.blHistoryProduct];
            if (productList.size() > 0) {
                result.put('Message', 'Battery ship back is not allowed.');
            } else {
                result.put('Message', '');
            }
        }

        return JSON.serialize(result);
    }


    @AuraEnabled
    public static string getSpecialIssueTracking(String productCode, List<String> serialNumbersNeedToCheck, String warrantyId){
        Map<String, Object> result = new Map<String, Object>();
        Map<String, String> picklistMap = new Map<String, String>();
        Schema.DescribeSObjectResult objSchema = Case.sObjectType.getDescribe();
        Map<String, Schema.SObjectField> fieldMap = objSchema.fields.getMap();
        List<Schema.Picklistentry> flds = fieldmap.get('Special_Issue_Tracking__c').getDescribe().getPicklistValues();
        for(Schema.Picklistentry fld : flds) {
            picklistMap.put(fld.getLabel(), fld.getValue());
        }
        if(String.isNotBlank(productCode)) {
            List<QA_Return_Rule__c> returnRules = [SELECT Product_Code__c, Quantity_Left__c, Comments__c, (SELECT Serial_Number__c FROM QA_Return_Serial_Numbers__r) FROM QA_Return_Rule__c WHERE Product_Code__c = :productCode];
            Integer leftCount = 0;
            Boolean isSerialNumberRange = false;
            for(QA_Return_Rule__c returnRule : returnRules) {
                leftCount = (Integer)returnRule.Quantity_Left__c;
                if(!returnRule.QA_Return_Serial_Numbers__r.isEmpty()) {
                    for(QA_Return_Serial_Number__c qaSerialNumber : returnRule.QA_Return_Serial_Numbers__r) {
                        if(serialNumbersNeedToCheck.contains(qaSerialNumber.Serial_Number__c)) {
                            isSerialNumberRange = true;
                            break;
                        }
                    }
                }
                else {
                    isSerialNumberRange = true;
                }
            }
            Boolean isValidWarranty = true;
            if(String.isNotBlank(warrantyId)) {
                for(Warranty_Item__c item : [SELECT Warranty__c, ActualIndicator__c, Product__r.ProductCode FROM Warranty_Item__c 
                                             WHERE Warranty__c = :warrantyId AND Product__r.ProductCode = :productCode]) {
                    if(item.ActualIndicator__c == 'Out of Warranty') {
                        isValidWarranty = false;
                    }
                }
            }
            if(!isValidWarranty) {
                picklistMap.remove('QA return');
            }

            if(!isSerialNumberRange) {
                picklistMap.remove('QA return');
            }
            if(leftCount <= 0) {
                picklistMap.remove('QA return');
            }
        }
        else {
            picklistMap.remove('QA return');
        }

        result.put('sitOptions', picklistMap);
        return JSON.serialize(result);
    }

    @AuraEnabled
    public static string getCaseTypeOptions(){
        Map<String, Object> result = new Map<String, Object>();
        Map<String, String> picklistMap = new Map<String, String>();
        Schema.DescribeSObjectResult objSchema = Case.sObjectType.getDescribe();
        Map<String, Schema.SObjectField> fieldMap = objSchema.fields.getMap();
        List<Schema.Picklistentry> flds = fieldmap.get('Case_Type__c').getDescribe().getPicklistValues();
        for(Schema.Picklistentry fld : flds) {
            if(fld.getValue() != 'Service Referral Tracking') {
                picklistMap.put(fld.getLabel(), fld.getValue());
            }
        }
        result.put('caseTypeOptions', picklistMap);
        return JSON.serialize(result);
    }

    @AuraEnabled
    public static void updateReturnRule(String productCode, String selectedIssues, String originalIssues){
        if(String.isNotBlank(productCode)) {
            String priNeedVirtualCustomer = System.Label.PRI_Of_Virtual_Customer;
            List<String> pris = new List<String>{'QA return'};
            Boolean hasSpecificIssue = false;
            Boolean originalHasSpecificIssue = false;
            Integer increaseVolume = 0;
            for(String pri : pris) {
                if(selectedIssues.contains(pri)) {
                    hasSpecificIssue = true;
                }
                if(originalIssues.contains(pri)) {
                    originalHasSpecificIssue = true;
                }
            }
            Boolean needUpdate = false;
            if(!originalHasSpecificIssue && hasSpecificIssue) {
                increaseVolume = 1;
                needUpdate = true;
            }
            else if(originalHasSpecificIssue && !hasSpecificIssue) {
                increaseVolume = -1;
                needUpdate = true;
            }
            if(needUpdate) {
                List<QA_Return_Rule__c> returnRules = [SELECT Product_Code__c, Quantity_Required__c FROM QA_Return_Rule__c WHERE Product_Code__c = :productCode];
                for(QA_Return_Rule__c returnRule : returnRules) {
                    if(returnRule.Quantity_Required__c == null) {
                        returnRule.Quantity_Required__c = 0;
                    }
                    returnRule.Quantity_Required__c = returnRule.Quantity_Required__c + increaseVolume;
                }
                update returnRules;
            }
        }
    }


    @AuraEnabled
    public static String checkQAReturnUseLeft(String productCode, String selectedIssues) {
        String errorMsg = '';
        if(String.isNotBlank(productCode)) {
            String priNeedVirtualCustomer = System.Label.PRI_Of_Virtual_Customer;
            List<String> pris = new List<String>{'QA return'};
            Boolean hasSpecificIssue = false;
            for(String pri : pris) {
                if(selectedIssues.contains(pri)) {
                    hasSpecificIssue = true;
                }
            }
            if(hasSpecificIssue) {
                List<QA_Return_Rule__c> returnRules = [SELECT Product_Code__c, Quantity_Left__c FROM QA_Return_Rule__c WHERE Product_Code__c = :productCode];
                for(QA_Return_Rule__c returnRule : returnRules) {
                    if(returnRule.Quantity_Left__c == 0) {
                        errorMsg = String.format('{0} has 0 usage left, please change QA return to another type.', new List<String>{productCode});
                    }
                }
            }
        }
        return errorMsg;
    }

    @AuraEnabled
    public static string getComments(String productCode){
        String comments = '';
        if(String.isNotBlank(productCode)) {
            List<QA_Return_Rule__c> returnRules = [SELECT Comments__c FROM QA_Return_Rule__c WHERE Product_Code__c = :productCode];
            for(QA_Return_Rule__c returnRule : returnRules) {
                comments = returnRule.Comments__c;
            }
        }
        return comments;
    }

    @AuraEnabled
    public static string getSerialNumbers(String warrantyId){
        List<SerialNumberProductCodeWrapper> wrappers = new List<SerialNumberProductCodeWrapper>();
        List<Warranty_Item__c> items = [SELECT Serial_Number__c, Product_Code__c FROM Warranty_Item__c WHERE Warranty__c = :warrantyId];
        for(Warranty_Item__c item : items) {
            if(String.isNotBlank(item.Serial_Number__c) && String.isNotBlank(item.Product_Code__c)) {
                SerialNumberProductCodeWrapper wrapper = new SerialNumberProductCodeWrapper();
                wrapper.SerialNumber = item.Serial_Number__c;
                wrapper.ProductCode = item.Product_Code__c;
                wrappers.add(wrapper);
            }
        }
        return JSON.serialize(wrappers);
    }

    @AuraEnabled
    public static Boolean iSST1510TSN(String warrantyId){
        Boolean flag = false;
        List<SerialNumberProductCodeWrapper> wrappers = new List<SerialNumberProductCodeWrapper>();
        List<Warranty_Item__c> items = [SELECT Serial_Number__c, Product_Code__c FROM Warranty_Item__c WHERE Warranty__c = :warrantyId];
        for(Warranty_Item__c item : items) {
            if(String.isNotBlank(item.Serial_Number__c) 
            && String.isNotBlank(item.Product_Code__c)
            && item.Product_Code__c == 'ST1510T'
            && item.Serial_Number__c.length() == 15) {
                Integer num = 2360;
                if(Integer.valueOf(item.Serial_Number__c.substring(5, 9)) < num){
                    flag = true;
                    return flag;
                }
            }
        }
        return flag;
    }

    @AuraEnabled
    public static string showSpecialBatteryReminder(String warrantyId, String productId){
        Map<String, Object> result = new Map<String, Object>();
        result.put('reminder', '');
        List<Warranty_Item__c> items = [SELECT Serial_Number__c, Product_Code__c, Warranty__r.Brand_Name__c FROM Warranty_Item__c WHERE Warranty__c = :warrantyId And Product__c =:productId];
        for(Warranty_Item__c item : items) {
            // 特殊model提示CSR
            if(item.Warranty__r.Brand_Name__c == 'EGO'){
                if(item.Product_Code__c == 'BA1400T' || item.Product_Code__c == 'BA1400' || item.Product_Code__c == 'BA2800' || item.Product_Code__c == 'BA2800T' || item.Product_Code__c == 'BA2242T'){
                    result.put('reminder', 'EGO');
                    continue;
                }
            }else if(item.Warranty__r.Brand_Name__c == 'FLEX'){
                if(item.Product_Code__c == 'FX0111-1' || item.Product_Code__c == 'FX0121-1' || item.Product_Code__c == 'FX0221-1' || item.Product_Code__c == 'FX0231-1'){
                    if(item.Serial_Number__c.length() == 13){
                        Integer dateCode = Integer.valueOf(item.Serial_Number__c.substring(2, 7));
                        if(dateCode >= 23300){
                            result.put('reminder', 'FLEX');
                            continue;
                        }
                    }
                }
            }else if(item.Warranty__r.Brand_Name__c == 'Skil'){
                if(item.Product_Code__c == 'BY500101' || item.Product_Code__c == 'BY519901' || item.Product_Code__c == 'BY519801' || item.Product_Code__c == 'BY519701' || item.Product_Code__c == 'BY519702'
                || item.Product_Code__c == 'BY519703' || item.Product_Code__c == 'BY519601' || item.Product_Code__c == 'BY519603' || item.Product_Code__c == 'BY5020A-00' || item.Product_Code__c == 'BY5040A-00'
                || item.Product_Code__c == 'BY0800C-00' || item.Product_Code__c == 'BY8705-00' || item.Product_Code__c == 'BY8708-00' || item.Product_Code__c == 'BY8708C-01' || item.Product_Code__c == 'BY8723C-00'){
                    if(item.Serial_Number__c.length() == 9){
                        Integer dateCode = Integer.valueOf(item.Serial_Number__c.substring(0, 3));
                        if(dateCode >= 331){
                            result.put('reminder', 'Skil');
                            continue;
                        }
                    }
                }
            } 
        }
        return JSON.serialize(result);
    }

    @AuraEnabled
    public static string getRelatedTopIssues(String productCode){
        List<Product_Related_Top_Issue__c> relatedTopIssues = new List<Product_Related_Top_Issue__c>();
        List<Product_Related_Top_Issue__c> topIssues = [SELECT Top_Issue__c, Template__c, Products__c FROM Product_Related_Top_Issue__c];
        for(Product_Related_Top_Issue__c topIssue : topIssues) {
            if(String.isNotBlank(topIssue.Products__c)) {
                List<String> productCodes = topIssue.Products__c.split(';');
                if(productCodes.contains(productCode)) {
                    relatedTopIssues.add(topIssue);
                }
            }
        }
        return JSON.serialize(relatedTopIssues);
    }

    @AuraEnabled
    public static string getExternalNotes(String productId){
        List<Product2> products = [SELECT ProductCode FROM Product2 WHERE Id = :productId];

        String externalNotes = '';
        if(!products.isEmpty()) {
            List<Product_External_Notes__mdt> notes = [SELECT Product_Code__c, External_Note__c FROM Product_External_Notes__mdt WHERE Product_Code__c = :products[0].ProductCode];
            List<String> noteList = new List<String>();
            for(Product_External_Notes__mdt note : notes) {
                noteList.add(note.External_Note__c);
            }

            externalNotes = String.join(noteList, '\n');
        }
        return externalNotes;
        
    }

    @AuraEnabled
    public static String caseReminder(String caseId, String warrantyId) {
        String reminderMsg = CCM_CaseReminderController.caseReminder(caseId, warrantyId);
        return reminderMsg;
    }

    @AuraEnabled
    public static string getPurchaseData(String warrantyId){
        List<Warranty__c> warranty = [SELECT Id, Purchase_Date__c from Warranty__c WHERE Id = :warrantyId];
        return JSON.serialize(warranty[0]);
    }

    public class SerialNumberProductCodeWrapper {
        @AuraEnabled public String SerialNumber {get;set;}
        @AuraEnabled public String ProductCode {get;set;}
    }
}