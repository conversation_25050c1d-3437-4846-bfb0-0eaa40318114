({
    doInit: function(component, event, helper){
        component.set('v.isBusy', true);
        component.set('v.currencySymbol', $A.get("$Locale.currencyCode"));
        var approvalOpt = [
            {'label': 'Approve', 'value': 'Approve'},
            {'label': 'Reject', 'value': 'Reject'}];
        component.set('v.approvalOpt', approvalOpt);

        //Inner User查看Partner订单的进度条场景
        var pathData1 = [
            {label: "New Order", icon: 'edit_form'},
            {label: "Pending Review", icon: 'approval'},
            {label: "Booked", icon: 'locker_service_api_viewer'},
            {label: "Order Processing", icon: 'privately_shared'},
            {label: "Partial Shipment", icon: 'travel_and_places'},
            {label: "Ship Complete", icon:'success'}
        ];
        component.set('v.processData', pathData1);

        var userInfo = $A.get("$SObjectType.CurrentUser");
        var userId = '';
        if (userInfo.Id) {
            userId = userInfo.Id;
        }
        component.set('v.userId', userId);
        
        console.log("A_LOCK_ORDER_FUNC_TMP:",$A.get("$Label.c.A_LOCK_ORDER_FUNC_TMP"));
        // 临时关闭订单功能
        component.set("v.blLockOrderFuncTmp", $A.get("$Label.c.A_LOCK_ORDER_FUNC_TMP") == 'True');

        var recordId = helper.getUrlParameter('recordId');
        component.set('v.recordId', recordId);
        console.log('record Id--->'+recordId);
        var action = component.get("c.getData");
        action.setParam('recordId', recordId);
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if(results){
                    let { attachmentSourceId } = results;
                    component.set("v.attachmentSourceId", attachmentSourceId || "");
                    component.set('v.quotation', results.order);
                    if(!component.get('v.quotation.handingFee')){
                        component.set('v.quotation.handingFee', 0.00);
                    }
                    component.set('v.freightFeeToBeWaivedVal', helper.currencyFormat(component.get('v.quotation.freightFeeWaived')));
                    component.set('v.freightFeeVal', helper.currencyFormat(component.get('v.quotation.freightFee')));
                    component.set('v.handlingFeeVal', helper.currencyFormat(component.get('v.quotation.handingFee')));
                    component.set('v.quotation.extraFreightFeeToBeWaived', helper.currencyFormat(component.get('v.quotation.extraFreightFeeToBeWaived')));
                    if (results.order){
                        if (results.order.isDelegated == true){
                            var pathData2 = [
                                    {label: "New Order", icon: 'edit_form'},
                                    {label: "Submitted", icon: 'approval'},
                                    {label: "Booked", icon: 'locker_service_api_viewer'},
                                    {label: "Order Processing", icon: 'privately_shared'},
                                    {label: "Partial Shipment", icon: 'travel_and_places'},
                                    {label: "Ship Complete", icon:'success'}
                                ];
                            component.set('v.processData', pathData2);
                        }

                        if(results.order.orgCode === 'CCA'){
                            component.set('v.isCCA', true);
                        }else{
                            component.set('v.isCCA', false);
                        }
                    }
                    component.set('v.showTax', results.showTax);
                    component.set("v.isAlternativeAddress", results.order.isAlternativeAddress);
                    if (results.order.isAlternativeAddress == true || results.order.isAlternativeAddress == 'true'){
                        component.set('v.isAddressEdit', true);
                    }
                    results.orderItems.forEach(function(e){
                        if(results.order.orgCode != 'CCA'){
                            e.caseQty = e.caseQty ? e.caseQty : 1;
                        }else{
                            e.caseQty = 1;
                        }
                    });
                    component.set('v.orderItemList', results.orderItems);
                    component.set('v.paymentTermAllOpts', results.paymentTermOptions);
                    console.log('v.paymentTermAllOpts',component.get('v.paymentTermAllOpts'));
                    component.set('v.customerType', results.customerType);
                    component.set('v.customerCluster', results.customerCluster);
                    component.set('v.paymentTermVal', results.paymentTermVal);
                    component.set('v.defaultPaymentTerm', results.defaultPaymentTerm);
                    component.set('v.freightTermVal', results.freightTermVal);
                    component.set('v.currentStep', results.currentStep);
                    component.set('v.customerId', results.order.customerId);
                    component.set('v.brandScope', results.order.brandScopeName);
                    component.set('v.isDelegate', results.order.isDelegated);
                    component.set('v.isDropShip', results.order.isDropShip);
                    component.set('v.isApprovalMode', results.isApprovalMode);
                    component.set('v.isShowEditBtn', results.isShowEditBtn);
                    component.set('v.isShowSyncBtn', results.isShowSyncBtn);
		            // Added By Anony 23.1.10 ---Start
                    component.set('v.isNotifiedInsideSales', results.isNotifiedInsideSales);
                    component.set('v.isSalesManagerModified', results.isSalesManagerModified);
                    // Added By Anony 23.1.10 ---End
                }
                helper.getUserInfo(component, event);
                component.set('v.dataInitComplete', true);
            } else {
                var errors = response.getError();
                component.set('v.dataInitComplete', true);
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
        
    },
    doEdit: function (component, event, helper) {
        component.set('v.isEdit', true);
        helper.getUserInfo(component, event);
    },
    changeShippingMethod: function(component, event, helper){
        var shippingMethod = component.get('v.quotation.shippingMethod');
        if(shippingMethod == 'Ground Freight'){
            //calculate the free shipping fee
            var actualShippingFee = component.get('v.actualShippingFee');
            var freightTermRuleFee = component.get('v.freightTermRuleFee');
            var totalProductAmount = component.get('v.productAmount');
            var waveShippingFee = Number(totalProductAmount) - Number(freightTermRuleFee);
            if(totalProductAmount < freightTermRuleFee){
                component.set('v.waveShippingFee', waveShippingFee);
                component.set('v.showFreeShippingMsg', true);
            }else{
                component.set('v.freeShipping', Number(actualShippingFee).toFixed(2));
                component.set('v.showFreeShippingMsg', false);
            }
        }else{
            component.set('v.showFreeShippingMsg', false);
            component.set('v.freeShipping', 0.00);
        }
    },
    doAddAddress: function(component, event, helper){
        component.set('v.isAddAddress', true);
    },
    doSaveAddress: function(component, event, helper){
        component.set('v.isAddAddress', false);
        component.set('v.isAlternativeAddress', true);
        component.set('v.quotation.isAlternativeAddress', component.get('v.isAlternativeAddress'));
        component.set('v.isAddressEdit', true);
        var purchaseOrder = component.get('v.quotation');
        var handlingFeeFactor = $A.get("$Label.c.CCM_Handling_Fee_Charge_Percent");
        var handlingFeeVal = purchaseOrder.productPrice * Number(handlingFeeFactor) / 100;
        component.set('v.quotation.handingFee', handlingFeeVal.toFixed(2));
        component.set('v.handlingFeeVal', helper.currencyFormat(component.get('v.quotation.handingFee')));

        helper.calculateFreightFeeHandler(component, event, helper);
    },
    doChangeAddress: function(component, event, helper){
        component.set('v.isAlternativeAddress', false);
        component.set('v.quotation.isAlternativeAddress', component.get('v.isAlternativeAddress'));
        component.set('v.isAddressEdit', false);
        component.set('v.isChangeAddress', true);
    },
    doConfirm: function(component, event, helper){
        component.set('v.isChangeAddress', false);
        component.set('v.isAlternativeAddress', false);
        component.set('v.quotation.isAlternativeAddress', component.get('v.isAlternativeAddress'));
        var zeroNum = 0.00;
        component.set('v.quotation.handingFee', Number(zeroNum).toFixed(2));
        component.set('v.handlingFeeVal', helper.currencyFormat(component.get('v.quotation.handingFee')));
    },
    closeModal: function(component, event, helper){
        component.set('v.isAddAddress', false);
    },
    closeChangeModal: function(component, event, helper){
        component.set('v.isChangeAddress', false);
    },
    
    update: function (component, event) { 
        component.set('v.files', '');
        let arr = JSON.stringify(event.getParam('files'));
        component.set('v.files', JSON.parse(arr));
    },


    doSave: function(component, event, helper){
        component.set('v.isBusy', true);
        var orderItemList = JSON.stringify(component.get('v.orderItemList'));
        if(orderItemList === "[]" || orderItemList === undefined){
            component.set("v.isBusy", false);
            helper.showToast('Warning!','Please add the product first.','Warning');
            return;
        }else{
            /*for(var i = 0; i < orderItemList.length; i++){

            }*/
        }
        //update by austin
        if (component.get('v.files').length == 0) {
            component.set('v.isBusy', false);
            helper.showToast('Warning!','Please add the file first.','Warning');
            return;
        }

        var result = Validator.pass(component.find("required-Field"));
        if(!result){
            component.set('v.isBusy', false);
            return;
        }
        var isAlternativeAddress = component.get('v.isAlternativeAddress');
        console.log('isAlternativeAddress--->'+isAlternativeAddress);
        if (isAlternativeAddress == false){
            component.set('v.requireShipAddrFlag', true);
            var result1 = Validator.pass(component.find("required-shipping"));
            if(!result1){
                component.set('v.isBusy', false);
                return;
            }
        }

        if(!helper.checkOrderQty(component)){
            component.set('v.isBusy', false);
            return;
        }

        var quotation = component.get('v.quotation');
        quotation.Authorization = component.get('v.quotation.Authorization');
        quotation.isAlternativeAddress = isAlternativeAddress;
        quotation.paymentTermValue = component.get('v.paymentTermValue');
        if (isAlternativeAddress == true){
            quotation.shippingAddressId = null;
        }else{
            quotation.additionalShipAddressStreet = null;
            quotation.additionalShipAddressCity = null;
            quotation.additionalShipAddressCountry = null;
            quotation.additionalShipAddressProvince = null;
            quotation.additionalShipAddressPostCode = null;
            quotation.additionalContactName = null;
            quotation.additionalContactPhone = null;
            quotation.additionalContactEmail = null;
        }

        var recordId = helper.getUrlParameter('recordId');
        var action = component.get("c.saveData");
        action.setParam('recordId', recordId);
        action.setParam('orderInfo', JSON.stringify(component.get('v.quotation')));
        action.setParam('orderItemsInfo', JSON.stringify(component.get('v.orderItemList')));
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if(results){
                    if (results.isSuccess){
                        var action = component.get("c.doInit");
                        $A.enqueueAction(action);
                        component.set('v.isEdit', false);
                    }
                }
            } else {
                var errors = response.getError();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    doBack: function(component){
        var url = window.location.origin + '/lightning/n/Order_Apply_List';
        window.open(url,'_self'); 
    },
    cancel: function(component){
        var action = component.get("c.doInit");
        $A.enqueueAction(action);
        component.set('v.isEdit', false);
    },
    /*doApprovalAct: function(component, event, helper){
        var recordId = helper.getUrlParameter('recordId');
        var approvalResult = component.get("v.approvalVal");
        console.log('approvalResult--->'+approvalResult);
        if (!approvalResult){
            helper.showToast('Approval Result','Please select the approval result first.','error');
            return;
        }

        var approvalComments = component.get('v.approvalComments');
        if (approvalResult == 'Reject' && !approvalComments){
            helper.showToast('Approval Result','Please fill in the reject reason.','warning');
            return;
        }

        var action = component.get("c.doApproval");
        action.setParams({ 
            'poId' : recordId,
            'approvalResult' : approvalResult,
            'approvalComments' : approvalComments
        });
        action.setCallback(this, function(response) {
            var state = response.getState();
            console.log('state--->'+state);
            if (state === "SUCCESS") {
                if (response.getReturnValue() == "SUCCESS") {
                    helper.showToast('Successful!','','success');
                    var url = window.location.origin + '/lightning/n/Order_Apply_List';
                    window.open(url,'_self'); 
                }
            }
        });
        $A.enqueueAction(action); 
    },*/
    doSync: function (component, event, helper) {
        var Authorization = component.get("v.quotation.Authorization");
        var isDropShipOrder = component.get("v.quotation.isDropShipOrder");
        var isDelegate = component.get("v.isDelegate");
        if (isDropShipOrder == true && isDelegate == false && (Authorization == '' || Authorization == null)) {
            helper.showToast('Credit Authorization# is required for Dropship Order', 'Please fill in Credit Authorization#', 'error');
        } else { 
            component.set('v.isBusy', true);
            var recordId = helper.getUrlParameter('recordId');
            var action = component.get("c.syncToEBS");
            action.setParam('recordId', recordId);
            action.setCallback(this, function (response) {
                var state = response.getState();
                if (state === "SUCCESS") {
                    var results = response.getReturnValue();
                    if(results){
                        if (results == 'S'){
                            helper.showToast('Sync Result','Successful!','success');
                            var url = window.location.origin + '/lightning/n/Order_Apply_List';
                            window.open(url,'_self'); 
                        }
                    }
                } else {
                    var errors = response.getError();
                    console.log('errors:',errors);
                }
                component.set('v.isBusy', false);
            });
            $A.enqueueAction(action);
        } 
    },
    calculateFreightFeeAmt: function(component, event, helper){
        helper.calculateFreightFeeHandler(component, event);
    },

    /**
     * Send Email for inside sales
     * add by Austin
     */
    doSendEmail: function (component, event) { 
        console.log('发邮件------', component.get('v.recordId'));
        var action = component.get("c.sendEmail");
        action.setParam('recordId', component.get('v.recordId'));
        action.setCallback(this, function (response) { 
            var state = response.getState();
            if (state === 'SUCCESS') {
                var results = response.getReturnValue(); 
                if (results === 'OK') {
                    //Modified By Anony 23.1.10 ---Start
                    // component.set('v.notify', true);
                    component.set('v.isNotifiedInsideSales', true);
                    //Modified By Anony 23.1.10 ---End
                }
                console.log('返回值------', results);
            }
        })
        $A.enqueueAction(action);
    },
    handlingFeeChange: function(component, event) {
        let handlingFeeCmp = component.find('handlingfee');
        let value = handlingFeeCmp.get('v.value');
        let currencySymbol = component.get('v.currencySymbol');
        value = value.replace(currencySymbol, '').trim();
        component.set('v.quotation.handingFee', parseFloat(value));
    }
})