<aura:component description="CCM_Community_OrderApplicationDetail"
                implements="forceCommunity:availableForAllPageTypes,force:hasRecordId,flexipage:availableForAllPageTypes,force:appHostable"
                controller="CCM_Community_OrderApplicationDetailCtl">
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="attachmentSourceId" type="String" default="" access="public"/>
    <aura:attribute name="currencySymbol" type="String" default="$"/>
    <aura:attribute name="brandScope" type="String" default=""/>
    <aura:attribute name="brandScopeOpt" type="List" default="" access="public"/>
    <aura:attribute name="customerId" type="String" default=""/>
    <aura:attribute name="quotation" type="Object" default=""/>
    <aura:attribute name="orderItemList" type="List" default="[]"/>
    <aura:attribute name="activeSections" type="List" default="" />
    <aura:attribute name="processData" type="List" default="[]"/>
    <aura:attribute name="requireFlag" type="Boolean" default="true"/>
    <aura:attribute name="requireShipAddrFlag" type="Boolean" default="true"/>
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <aura:attribute name="isEdit" type="Boolean" default="false"/>
    <aura:attribute name="currentStep" type="Integer" default="1"/>
    <aura:attribute name="isRequired" type="Boolean" default="false"/>
    <aura:attribute name="isDisabled" type="Boolean" default="false"/>
    <aura:attribute name="isDelegate" type="Boolean" default="false"/>
    <aura:attribute name="approvalComments" type="String" default=""/>
    <aura:attribute name="approvalOpt" type="List" default=""/>
    <aura:attribute name="approvalVal" type="String" default=""/>
    <aura:attribute name="isApprovalMode" type="Boolean" default="false"/>
    <aura:attribute name="isShowEditAndSyncBtn" type="Boolean" default="true"/>
    <aura:attribute name="isShowEditBtn" type="Boolean" default="false"/>
    <aura:attribute name="isShowSyncBtn" type="Boolean" default="false"/>
    <aura:attribute name="paymentTermVal" type="String" default=""/>
    <aura:attribute name="freightTermVal" type="String" default=""/>

    <aura:attribute name="freightFeeToBeWaivedVal" type="String" default="0.00"/>
    <aura:attribute name="freightFeeVal" type="String" default="0.00"/>
    <aura:attribute name="handlingFeeVal" type="String" default="0.00"/>
    <aura:attribute name="handlingFeeEdit" type="Boolean" default="true" />

    <!--是坦是新添加的Drop Ship Shipping Address Flag-->
    <aura:attribute name="isAddAddress" type="Boolean" default="false"/>
    <aura:attribute name="isAlternativeAddress" type="Boolean" default="false"/>
    <aura:attribute name="isDropShip" type="Boolean" default="false"/>
    <aura:attribute name="isAddressEdit" type="Boolean" default="false"/>
    <aura:attribute name="isChangeAddress" type="Boolean" default="false"/>
    <!--Payment Term Table modified by Abby on 06042020-->
    <aura:attribute name="paymentTermAllOpts" type="List" default="[]"/>
    <aura:attribute name="paymentTermSelectOpt" type="List" default="[]"/>
    <aura:attribute name="paymentTermValue" type="String" default=""/>
    <aura:attribute name="needResearch" type="Boolean" default="false"/>
    <aura:attribute name="customerType" type="String" default=""/>
    <aura:attribute name="customerCluster" type="String" default=""/>
    <aura:attribute name="defaultPaymentTerm" type="String" default=""/>
    <!-- 剝坰验话JS引用 -->
    <ltng:require scripts="{!join(',', $Resource.Validator)}"/>
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <aura:handler name="change" value="{!v.quotation.shippingMethod}" action="{!c.changeShippingMethod}"/>
    <aura:handler name="change" value="{!v.quotation.shippingAddressId}" action="{!c.calculateFreightFeeAmt}"/>
    <aura:handler name="change" value="{!v.quotation.additionalShipAddressPostCode}" action="{!c.calculateFreightFeeAmt}"/>
<!-- austin -->
    <aura:attribute name="files" type="List" default="[]"/>

    <!-- ORG Code: CCA -->
    <aura:attribute name="isCCA" type="Boolean" default="false"/>
    <aura:attribute name="showTax" type="Boolean" default="true" />

    <!-- Notify inside sales -->
    <aura:attribute name = "notify" type = "Boolean" default = "false"/>
    <!-- Added By Anony 23.1.10 Start -->
    <aura:attribute name="isNotifiedInsideSales" type="Boolean" default="false"/>
    <aura:attribute name="isSalesManagerModified" type="Boolean" default="false"/>
    <!-- Added By Anony 23.1.10 End -->

    <!-- login user id -->
    <aura:attribute name = "userId" type = "String" default=""/>
    <!-- Credit Authorization required -->
    <aura:attribute name="AuthorizationRequired" type="Boolean" default="false"/>
    <!-- isShow inside sales modify -->
    <aura:attribute name="insideSalesModify" type="Boolean" default="false"/>

    <!-- 临时关闭订单 button 按钮 begin -->
    <aura:attribute name="blLockOrderFuncTmp" type="Boolean" default="false"/>
    <!-- 临时关闭订单 button 按钮 end -->
    <aura:attribute name="dataInitComplete" type="Boolean" default="false" />

    <aura:if isTrue="{!v.dataInitComplete}">
        <div class="slds-box slds-theme_default">
        <div class="halp-step-box-tabset-box slds-p-horizontal--medium slds-p-top--medium">
            <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
            <c:CCM_PathProcess processData="{!v.processData}" currentStep="{!v.currentStep}"/>
            <c:CCM_Section title="Basic Information" expandable="true">
                <lightning:layout multipleRows="true">
                    <!--                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom&#45;&#45;small">-->
                    <!--                        <c:CCM_Field label="{!$Label.c.CCM_Customer_Purchase_Order_Num}">-->
                    <!--                            {!v.quotation.customerPONum}-->
                    <!--                        </c:CCM_Field>-->
                    <!--                    </lightning:layoutItem>-->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Purchase Order Type">
                            {!v.quotation.orderType}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Customer Type">
                            {!v.quotation.customerType}
                        </c:CCM_Field>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Purchase Order Number">
                            {!v.quotation.poNumber}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Customer Name">
                            <a href="{!('/' + v.quotation.customerId)}" target="_blank">{!v.quotation.customeName}</a>
                        </c:CCM_Field>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Purchase Order Status">
                            {!v.quotation.orderStatus}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Account Number">
                            {!v.quotation.accountNumber}
                        </c:CCM_Field>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Brand">
                            {!v.quotation.brandScopeName}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Customer PO Number">
                            {!v.quotation.customerPONum}
                        </c:CCM_Field>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Payment Term">
                            {!v.paymentTermVal}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Sales Group">
                            {!v.quotation.salesGroup}
                        </c:CCM_Field>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Freight Term">
                            {!v.freightTermVal}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Sales Manager">
                            {!v.quotation.salesManagerName}
                        </c:CCM_Field>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Sales Agency">
                            {!v.quotation.salesAgency}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Created By">
                            {!v.quotation.createdBy}
                        </c:CCM_Field>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Submit Date">
                            {!v.quotation.submitDate}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Create Date">
                            {!v.quotation.createdDate}
                        </c:CCM_Field>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Is DropShip Order?">
                            {!v.quotation.isDropShipOrder}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="SO Creation Status">
                            {!v.quotation.syncStatus}
                        </c:CCM_Field>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Is Delegated Order?">
                            {!v.quotation.isDelegatedOrder}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Sync Message">
                            {!v.quotation.syncMessage}
                        </c:CCM_Field>
                    </lightning:layoutItem>

                    <aura:if isTrue="{!v.quotation.wholeOrderPromoCode}">
                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                            <c:CCM_Field label="Whole Order Promotion">
                                {!v.quotation.wholeOrderPromoCode}
                            </c:CCM_Field>
                        </lightning:layoutItem>
                     </aura:if>

                    <aura:if isTrue="{!v.quotation.paymentTermPromoCode}">
                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                            <c:CCM_Field label="Payment Term Promotion">
                                {!v.quotation.paymentTermPromoCode}
                            </c:CCM_Field>
                        </lightning:layoutItem>
                    </aura:if>
                </lightning:layout>
            </c:CCM_Section>

            <aura:if isTrue="{!v.isEdit}">
                <c:CCM_Section title="Shipping Address Information" expandable="true" >
                    <lightning:layout multipleRows="true">
                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <aura:if isTrue="{!!v.isDropShip}">
                                    <c:CCM_SearchAddressCmp
                                            aura:id="required-Field"
                                            selectedRecordId="{!v.quotation.billingAddressId}"
                                            inputAddress="{!v.quotation.billingAddressName}"
                                            addressType="Billing_Address"
                                            customerId="{!v.customerId}"
                                            quotation="{!v.quotation}"
                                            brandNames="{!v.quotation.brandScopeName}"
                                            isRequired="{!v.requireFlag}"
                                            fieldLabel="Billing Address" />
                                        <aura:set attribute="else">
                                            <c:CCM_SearchAddressCmp
                                                aura:id="required-Field"
                                                selectedRecordId="{!v.quotation.billingAddressId}"
                                                inputAddress="{!v.quotation.billingAddressName}"
                                                addressType="Dropship_Billing_Address"
                                                customerId="{!v.customerId}"
                                                quotation="{!v.quotation}"
                                                brandNames="{!v.quotation.brandScopeName}"
                                                isRequired="{!v.requireFlag}"
                                                fieldLabel="Billing Address" />
                                        </aura:set>
                                </aura:if>
                            </div>
                        </lightning:layoutItem>

                        <aura:if isTrue="{!v.isDropShip}">
                            <lightning:layoutItem padding="around-small" size="5">
                                <div class="header-column">
                                    <aura:if isTrue="{!!v.isAlternativeAddress}">
                                        <c:CCM_SearchAddressCmp
                                            aura:id="required-shipping"
                                            selectedRecordId="{!v.quotation.shippingAddressId}"
                                            inputAddress="{!v.quotation.shippingAddressName}"
                                            addressType="Dropship_Shipping_Address"
                                            customerId="{!v.customerId}"
                                            quotation="{!v.quotation}"
                                            brandNames="{!v.quotation.brandScopeName}"
                                            isRequired="{!v.requireShipAddrFlag}"
                                            fieldLabel="Shipping Address"/>
                                        <aura:set attribute="else">
                                            <p class="ccm_label">Shipping Address</p>
                                            <lightning:formattedAddress
                                                street="{!v.quotation.additionalShipAddressStreet}"
                                                city="{!v.quotation.additionalShipAddressCity}"
                                                country="{!v.quotation.additionalShipAddressCountry}"
                                                province="{!v.quotation.additionalShipAddressProvince}"
                                                postalCode="{!v.quotation.additionalShipAddressPostCode}"
                                            />
                                            <ul>
                                                <li> {!v.quotation.additionalContactName}</li>
                                                <li>{!v.quotation.additionalContactPhone}</li>
                                                <li>{!v.quotation.additionalContactEmail}</li>
                                            </ul>
                                        </aura:set>
                                    </aura:if>
                                </div>
                            </lightning:layoutItem>
                            <aura:set attribute="else">
                                <lightning:layoutItem padding="around-small" size="5">
                                    <div class="header-column">
                                        <aura:if isTrue="{!!v.isAlternativeAddress}">
                                            <c:CCM_SearchAddressCmp
                                                aura:id="required-shipping"
                                                selectedRecordId="{!v.quotation.shippingAddressId}"
                                                inputAddress="{!v.quotation.shippingAddressName}"
                                                addressType="Shipping_Address"
                                                customerId="{!v.customerId}"
                                                quotation="{!v.quotation}"
                                                brandNames="{!v.quotation.brandScopeName}"
                                                isRequired="{!v.requireShipAddrFlag}"
                                                fieldLabel="Shipping Address"/>
                                            <aura:set attribute="else">
                                                <p class="ccm_label">Shipping Address</p>
                                                <lightning:formattedAddress
                                                    street="{!v.quotation.additionalShipAddressStreet}"
                                                    city="{!v.quotation.additionalShipAddressCity}"
                                                    country="{!v.quotation.additionalShipAddressCountry}"
                                                    province="{!v.quotation.additionalShipAddressProvince}"
                                                    postalCode="{!v.quotation.additionalShipAddressPostCode}"
                                                />
                                                <ul>
                                                    <li> {!v.quotation.additionalContactName}</li>
                                                    <li>{!v.quotation.additionalContactPhone}</li>
                                                    <li>{!v.quotation.additionalContactEmail}</li>
                                                </ul>
                                            </aura:set>
                                        </aura:if>
                                    </div>
                                </lightning:layoutItem>
                            </aura:set>
                        </aura:if>

                        <lightning:layoutItem padding="around-small" size="1" class="ccm_iconStyle">
                            <aura:if isTrue="{!!v.isAddressEdit}">
                                <aura:if isTrue="{! !v.isCCA}">
                                    <lightning:buttonIcon iconName="utility:add" variant="bare" onclick="{! c.doAddAddress}" alternativeText="Add Shipping Address" />
                                </aura:if>
                                <aura:set attribute="else">
                                    <lightning:buttonIcon iconName="utility:edit" variant="bare" onclick="{! c.doAddAddress}" alternativeText="Edit Shipping Address" />
                                    <lightning:buttonIcon iconName="utility:change_record_type" variant="bare" onclick="{! c.doChangeAddress}" alternativeText="Change Shipping Address" />
                                </aura:set>
                    </aura:if>
                </lightning:layoutItem>
                    </lightning:layout>
                </c:CCM_Section>

                <c:CCM_Section title="{!$Label.c.CCM_Carrier_Fee}" expandable="true" >
                    <lightning:layout multipleRows="true">
                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <c:CCM_SelectList aura:id="required-Field" FieldName="Shipping_By__c" value="{!v.quotation.shippingBy}" required="{!v.requireFlag}"
                                    objectName="Purchase_Order__c" label="Shipping By" showBlankVal="false"/>
                            </div>
                        </lightning:layoutItem>

                        <aura:if isTrue="{!v.quotation.shippingBy != 'Chervon'}">
                            <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <c:CCM_SelectList aura:id="required-Field" FieldName="Delivery_Supplier__c" value="{!v.quotation.deliverySupplier}"
                                        objectName="Purchase_Order__c" label="Prefer Carrier" showBlankVal="true" required="{!v.requireFlag}"/>
                                </div>
                            </lightning:layoutItem>
                            <aura:if isTrue="{!v.quotation.shippingBy == 'Customer'}">
                                <lightning:layoutItem padding="around-small" size="6">
                                    <div class="header-column">
                                        <lightning:input aura:id="required-Field" name="" label="Your Customer Freight Account #" value="{!v.quotation.freightAccount}"/>
                                    </div>
                                </lightning:layoutItem>
                                <aura:set attribute="else">
                                    <lightning:layoutItem padding="around-small" size="6">
                                        <div class="header-column">
                                            <lightning:input aura:id="required-Field" name="" label="Your Customer Freight Account #" value="{!v.quotation.freightAccount}" required="{!v.requireFlag}"/>
                                        </div>
                                    </lightning:layoutItem>
                                </aura:set>
                            </aura:if>
                        </aura:if>

                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input name="" label="Freight Fee" value="{!(v.currencySymbol + ' ' + v.freightFeeVal)}" disabled="true"/>
                            </div>
                        </lightning:layoutItem>

                        <!-- <aura:if isTrue="{!!v.isCCA}"> -->
                            <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <lightning:input name="" label="Freight Fee To Be Waived" value="{!(v.currencySymbol + ' ' + v.freightFeeToBeWaivedVal)}" disabled="true"/>
                                </div>
                            </lightning:layoutItem>
                        <!-- </aura:if>                         -->

                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input aura:id="handlingfee" name="" label="Handling Fee" value="{!(v.currencySymbol + ' ' + v.quotation.handingFee)}" onblur="{!c.handlingFeeChange}" disabled="{!v.handlingFeeEdit}"/>
                            </div>
                        </lightning:layoutItem>

                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input name="" label="Shipping Discount" value="{!v.quotation.extraFreightFeeToBeWaived}"/>
                            </div>
                        </lightning:layoutItem>
                    </lightning:layout>
                </c:CCM_Section>

                <c:CCM_Section title="Delivery Information" expandable="true" >
                    <lightning:layout multipleRows="true">
                        <aura:if isTrue="{!v.quotation.shippingBy == 'Chervon'}">
                            <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <c:CCM_SelectList FieldName="Shipping_Method__c" value="{!v.quotation.shippingMethod}"
                                        objectName="Purchase_Order__c" label="Shipping Method" showBlankVal="false" disabled="true"/>
                                </div>
                            </lightning:layoutItem>
                        </aura:if>

                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input aura:id="required-Field" label="Your Purchase Order#" value="{!v.quotation.customerPONum}" required="{!v.requireFlag}"
                                    pattern=".{0,50}" messageWhenPatternMismatch="Warning: You could enter up to 50 characters."
                                />
                            </div>
                        </lightning:layoutItem>
                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input label="Expected Delivery Date" value="{!v.quotation.expectedDeliveryDate}" type="Date" />
                            </div>
                        </lightning:layoutItem>

                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input aura:id = 'required-Field' label="Credit Authorization#" value="{!v.quotation.Authorization}" type="Text" required = "{!v.AuthorizationRequired}"/>
                            </div>
                        </lightning:layoutItem>


                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <lightning:input type="Email" label="Buyer Email" value="{!v.quotation.buyerEmail}"/>
                            </div>
                        </lightning:layoutItem>
                       <!--  <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <c:CCM_SelectList FieldName="Shipment_Priority__c" value="{!v.quotation.shippingPriority}"
                                        objectName="Purchase_Order__c" label="Shipping Priority" showBlankVal="false"/>
                            </div>
                        </lightning:layoutItem> -->

                        <lightning:layoutItem class="" padding="around-small" size="12">
                            <div class="header-column">
                                <lightning:textarea name="" label="Shipping and Routing Instruction" value="{!v.quotation.notes}" messageWhenTooLong="Please enter no more than 500 bytes." maxlength="500"/>
                            </div>
                        </lightning:layoutItem>
                    </lightning:layout>
                </c:CCM_Section>

                <c:CCM_Section title="Order Item Information" expandable="true" >
                    <aura:if isTrue="{!v.isEdit}">
                        <lightning:layout multipleRows="true">
                            <div class="slds-grid slds-grid_vertical-align-end slds-grid--align-spread">
                                <div class="slds-clearfix slds-text-align--left">
                                    <p><strong>Authorized Brands:</strong></p>
                                    <p class="{!!v.needResearch ? '': 'slds-m-vertical--medium'}"><strong>Payment Term:</strong></p>
                                    <p><strong>Freight Term:</strong></p>
                                </div>
                                <div>
                                    <p><ui:outputText class="slds-p-left_xx-small" value="{!v.brandScope}"/></p>
                                    <aura:if isTrue="{!!v.needResearch}">
                                        <p><ui:outputText class="slds-p-left_xx-small" value="{!v.paymentTermVal}"/></p>
                                        <aura:set attribute="else">
                                            <p><lightning:combobox class="paymentTermSelectList paymentTermCombobox" aura:id="required-Field" name="paymentTermInfo" placeholder="--Select Payment Term--" value="{!v.paymentTermValue}" options="{!v.paymentTermSelectOpt}" label=""/></p>
                                        </aura:set>
                                    </aura:if>
                                    <p><ui:outputText class="slds-p-left_xx-small" value="{!v.freightTermVal}"/></p>
                                </div>
                            </div>
                        </lightning:layout>
                    </aura:if>

                    <div class="slds-p-top--medium">
                        <c:CCM_IN_Quotation_ProductSelect recordId="{!v.recordId}" quotation="{!v.quotation}" orderItemList="{!v.orderItemList}" currentStep="1" brand="{!v.brandScope}" customerId="{!v.customerId}"  isDropShip="{!v.isDropShip}" paymentTermAllOpts="{!v.paymentTermAllOpts}" needResearch="{!v.needResearch}" paymentTermSelectOpt="{!v.paymentTermSelectOpt}" paymentTermValue="{!v.paymentTermValue}" paymentTermVal="{!v.paymentTermVal}" customerType="{!v.customerType}" customerCluster="{!v.customerCluster}" defaultPaymentTerm="{!v.defaultPaymentTerm}"/>
                    </div>
                </c:CCM_Section>

                <c:CCM_Section title="Attachment" expandable="true">
                    <aura:if isTrue="{!not(equals(v.attachmentSourceId, ''))}">
                        <c:ccmFileUploader
                            label="Attachment"
                            aura:id="fileUploader"
                            recordId="{!v.attachmentSourceId}"
                            disabled="false"
                            onupdate='{!c.update}'
                        />
                    </aura:if>
                </c:CCM_Section>

                <!-- else -->
                <aura:set attribute="else">
                    <c:CCM_Section title="Delivery Information" expandable="true" >
                        <lightning:layout multipleRows="true">
                            <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <p><strong>Billing To</strong></p>
                                    <p>
                                        <aura:if isTrue="{!and(v.quotation.billingAddressId != null)}">
                                            <lightning:formattedAddress
                                                street="{!(v.quotation.billingAddress1 + v.quotation.billingAddress2)}"
                                                city="{!v.quotation.billingAddressCity}"
                                                country="{!v.quotation.billingAddressCountry}"
                                                province="{!v.quotation.billingAddressState}"
                                                postalCode="{!v.quotation.billingAddressPostalCode}"/>
                                            <ul>
                                                <li> {!v.quotation.billingAddressContactName}</li>
                                            </ul>
                                        </aura:if>
                                    </p>
                                </div>
                            </lightning:layoutItem>

                            <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <aura:if isTrue="{!v.isCCA}">
                                        <p><strong>Chervon Canada, Inc.</strong></p>
                                        <lightning:formattedAddress
                                            street="1-3480 Laird Rd."
                                            city="Mississauga"
                                            country="Canada"
                                            province="ON"
                                            postalCode="L5L 5Y4"/>
                                        <p>{!v.quotation.salesManagerName}</p>
                                        <aura:set attribute="else">
                                            <p><strong>Chervon North American</strong></p>
                                            <lightning:formattedAddress
                                                street="1203 East Warrenville Road"
                                                city="Naperville"
                                                country="US"
                                                province="IL"
                                                postalCode="60563"/>
                                            <p>{!v.quotation.salesManagerName}</p>
                                        </aura:set>
                                    </aura:if>
                                </div>
                            </lightning:layoutItem>

                            <lightning:layoutItem padding="around-small" size="6">
                                <p><strong>Shipping To</strong></p>
                                <p>
                                    <aura:if isTrue="{!v.quotation.isAlternativeAddress == false}">
                                        <lightning:formattedAddress
                                            street="{!(v.quotation.shippingAddress1 + v.quotation.shippingAddress2)}"
                                            city="{!v.quotation.shippingAddressCity}"
                                            country="{!v.quotation.shippingAddressCountry}"
                                            province="{!v.quotation.shippingAddressState}"
                                            postalCode="{!v.quotation.shippingAddressPostalCode}"/>
                                        <ul>
                                            <li> {!v.quotation.shippingAddressContactName}</li>
                                        </ul>
                                        <aura:set attribute="else">
                                            <lightning:formattedAddress
                                                street="{!v.quotation.additionalShipAddressStreet}"
                                                city="{!v.quotation.additionalShipAddressCity}"
                                                country="{!v.quotation.additionalShipAddressCountry}"
                                                province="{!v.quotation.additionalShipAddressProvince}"
                                                postalCode="{!v.quotation.additionalShipAddressPostCode}"
                                            />
                                            <ul>
                                                <li> {!v.quotation.additionalContactName}</li>
                                                <li>{!v.quotation.additionalContactPhone}</li>
                                                <li>{!v.quotation.additionalContactEmail}</li>
                                            </ul>
                                        </aura:set>
                                    </aura:if>
                                </p>
                            </lightning:layoutItem>
                            <lightning:layoutItem padding="around-small" size="6">
                                <div>
                                    <p><strong>Shipping By</strong></p>
                                    <p>{!v.quotation.shippingByLabel}</p>
                                </div>
                                <aura:if isTrue="{!v.quotation.shippingBy != 'Chervon'}">
                                    <div>
                                        <p><strong>Prefer Carrier</strong></p>
                                        <p>{!v.quotation.deliverySupplier}</p>
                                    </div>
                                    <div>
                                        <p><strong>Your Customer Freight Account #</strong></p>
                                        <p>{!v.quotation.freightAccount}</p>
                                    </div>
                                <aura:set attribute="else">
                                    <div>
                                        <p><strong>Shipping Method</strong></p>
                                        <p>{!v.quotation.shippingMethod}</p>
                                    </div>
                                </aura:set>
                                </aura:if>
                            </lightning:layoutItem>

                            <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <p><strong>Expected Delivery Date</strong></p>
                                    <p>{!v.quotation.expectedDeliveryDate}</p>
                                </div>
                            </lightning:layoutItem>

                            <!-- <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <p><strong>Promotion Code</strong></p>
                                    <p>{!v.quotation.promotionCode}</p>
                                </div>
                            </lightning:layoutItem> -->

                            <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <p><strong>Buyer Email</strong></p>
                                    <p>{!v.quotation.buyerEmail}</p>
                                </div>
                            </lightning:layoutItem>

                            <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <p><strong>Shipping Priority</strong></p>
                                    <p>{!v.quotation.shippingPriority}</p>
                                </div>
                            </lightning:layoutItem>

                            <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <p><strong>Sync Message</strong></p>
                                    <p>{!v.quotation.syncMessage}</p>
                                </div>
                            </lightning:layoutItem>

                            <lightning:layoutItem padding="around-small" size="12">
                                <p><strong>Shipping and Routing Instruction</strong></p>
                                <p>{!v.quotation.notes}</p>
                            </lightning:layoutItem>
                        </lightning:layout>
                    </c:CCM_Section>

                    <c:CCM_Section title="Order Item Information" expandable="true" >
                        <div class="slds-p-left_medium slds-p-right--medium">
                        <table class="slds-p-horizontal_medium slds-m-bottom_medium"></table>
                        <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_fixed-layout slds-table_resizable-cols slds-table_striped productTable" role="grid">
                            <thead>
                                <tr class="slds-line-height_reset">
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="ID">Line</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Product Description">Product Description</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="Model #">Model #</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="Brand">Brand</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumLWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="Expected Ship Date">Expected Ship Date</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                    <!-- calvin start -->
                                                    <span class="slds-truncate" title="Quantity">Qty(EA)</span>
                                                    <!-- end -->
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="Quantity UM">Unit</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- calvin start -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="Quantity UM">Case Qty</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- end -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="List Price">List Price</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Invoice Price">Invoice Price</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Discount">Discount</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Promo Discount">Promo Discount</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Subtotal">Subtotal</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Promo Code">Promo Code</span>
                                            </div>
                                        </a>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <aura:iteration items="{!v.orderItemList}" var="orderItem" indexVar="index">
                                    <tr aria-selected="false" class="slds-hint-parent" id="{!row_index}">
                                        <th scope="row">
                                            <div class="slds-truncate" title="">
                                                {!index + 1}
                                            </div>
                                        </th>
                                        <td role="gridcell" title="{!orderItem.productName}">
                                            <div class="slds-truncate clear-user-agent-styles" >{!orderItem.productName}</div>
                                        </td>

                                        <td role="gridcell" title="{!orderItem.productCode}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.productCode}</span>
                                            </div>
                                        </td>

                                        <td role="gridcell" title="{!orderItem.brandName}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.brandName}</span>
                                            </div>
                                        </td>

                                        <td role="gridcell" title="{!orderItem.shipDate}">
                                            <div class="slds-truncate">
                                                <span>{!orderItem.shipDate}</span>
                                            </div>
                                        </td>

                                        <td role="gridcell" title="{!orderItem.quantity}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.quantity}</span>
                                            </div>
                                        </td>

                                        <td role="gridcell" title="EA">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>EA</span>
                                            </div>
                                        </td>

                                        <!-- calvin start -->
                                        <td role="gridcell" title="EA">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.caseQty}</span>
                                            </div>
                                        </td>
                                        <!-- end -->

                                        <td role="gridcell" title="{!orderItem.listPrice}">
                                            <div class="slds-truncate">
                                                <lightning:formattedNumber value="{!orderItem.listPrice}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/>
                                            </div>
                                        </td>

                                        <td role="gridcell" title="{!orderItem.unitPrice}">
                                            <div class="slds-truncate">
                                                <lightning:formattedNumber value="{!orderItem.unitPrice}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/>
                                            </div>
                                        </td>

                                        <td role="gridcell" title="{!orderItem.discountAmt}">
                                            <div class="slds-truncate required">
                                                <lightning:formattedNumber value="{!orderItem.discountAmt}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/>
                                            </div>
                                        </td>

                                        <td role="gridcell" title="{!(orderItem.promoDiscountAmt + orderItem.wholeOrderPromoDiscountAmt)}">
                                            <div class="slds-truncate required">
                                                <lightning:formattedNumber value="{!(orderItem.promoDiscountAmt + orderItem.wholeOrderPromoDiscountAmt)}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/>
                                            </div>
                                        </td>

                                        <td role="gridcell" title="{!orderItem.subTotal}">
                                            <div class="slds-truncate">
                                                <lightning:formattedNumber value="{!orderItem.subTotal}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/>
                                            </div>
                                        </td>

                                        <td role="gridcell" title="{!orderItem.promotionCode}">
                                            <div class="slds-truncate" >
                                                <span>{!orderItem.promotionCode}</span>
                                            </div>
                                        </td>
                                    </tr>
                                </aura:iteration>
                            </tbody>
                        </table>
                            <div class="slds-clearfix slds-m-top--medium">
                                <div class="slds-grid slds-float--right">
                                    <div class="slds-text-align--right">
                                        <div class="slds-truncate ccm_padding" title="">Total Quantity:&nbsp;</div>
                                        <div class="slds-truncate ccm_padding" title="">Product Amount:&nbsp;</div>
                                        <div class="slds-truncate ccm_padding" title="">Discount Amount:&nbsp;</div>
                                        <aura:if isTrue="{!v.quotation.surchargeAmt > 0}">
                                            <div class="slds-truncate ccm_padding" title="">Surcharge:&nbsp;</div>
                                        </aura:if>
                                        <div class="slds-truncate ccm_padding" title="">Freight Fee:&nbsp;</div>
                                        <div class="slds-truncate ccm_padding" title="">Freight Fee To Be Waived:&nbsp;</div>
                                        <div class="slds-truncate ccm_padding" title="">Shipping Discount:&nbsp;</div>
                                        <div class="slds-truncate ccm_padding" title="">Handling Fee:&nbsp;</div>
                                        <aura:if isTrue="{!and(v.isCCA, v.showTax)}">
                                            <div class="slds-truncate ccm_padding" title="">QST:&nbsp;</div>
                                            <div class="slds-truncate ccm_padding" title="">GST:&nbsp;</div>
                                            <div class="slds-truncate ccm_padding" title="">HST:&nbsp;</div>
                                        </aura:if>
                                        <div class="slds-border_bottom ccm_paddingTop" />
                                        <div class="slds-truncate ccm_padding ccm_paddingTop" title=""><strong>Total Amount::&nbsp;</strong></div>
                                    </div>
                                    <div>
                                        <div class="slds-truncate" title=""><strong>{!v.quotation.totalQuantity}</strong></div>
                                        <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.productPrice}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                        <div class="slds-truncate ccm_fontColor" title=""><strong><lightning:formattedNumber value="{!v.quotation.discountAmt}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>

                                        <aura:if isTrue="{!v.quotation.surchargeAmt > 0}">
                                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.surchargeAmt}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                        </aura:if>

                                        <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.freightFee}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                        <div class="slds-truncate ccm_fontColor" title=""><strong><lightning:formattedNumber value="{!v.quotation.freightFeeWaived}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                        <div class="slds-truncate ccm_fontColor" title=""><strong><lightning:formattedNumber value="{!v.quotation.extraFreightFeeToBeWaived}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                        <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.handingFee}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                        <aura:if isTrue="{!and(v.isCCA, v.showTax)}">
                                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.QST}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.GST}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.HST}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                        </aura:if>
                                        <div class="slds-border_bottom ccm_paddingTop" />
                                        <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.totalAmount}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </c:CCM_Section>
                    <c:CCM_Section title="Attachment" expandable="true">
                        <aura:if isTrue="{!not(equals(v.attachmentSourceId, ''))}">
                            <c:ccmFileUploader
                                label="Attachment"
                                aura:id="fileUploader"
                                recordId="{!v.attachmentSourceId}"
                                disabled="true"
                            />
                        </aura:if>
                    </c:CCM_Section>
                </aura:set>
            </aura:if>

            <c:CCM_Modal size="medium" isShow="{!v.isAddAddress}" onClose="{!c.closeModal}" title="Alternative Shipping Address Information">
                <lightning:layout verticalAlign="center">
                    <lightning:layoutItem class="slds-p-left--medium slds-text-heading--small" size="6">
                        <lightning:inputAddress
                                aura:id="alternativeShippingAddress"
                                addressLabel="Alternative Shipping Address"
                                streetLabel="Street"
                                cityLabel="City"
                                countryLabel="Country"
                                provinceLabel="Province/State"
                                postalCodeLabel="PostalCode"
                                street="{!v.quotation.additionalShipAddressStreet}"
                                city="{!v.quotation.additionalShipAddressCity}"
                                province="{!v.quotation.additionalShipAddressProvince}"
                                country="{!v.quotation.additionalShipAddressCountry}"
                                postalCode="{!v.quotation.additionalShipAddressPostCode}"
                                required="{!v.requireFlag}"
                            />
                    </lightning:layoutItem>
                    <lightning:layoutItem class="slds-p-left--medium slds-text-heading--small" size="6">
                        <lightning:input aura:id="alternativeShippingAddress" name="contactName" label="Contact Name" value="{!v.quotation.additionalContactName}" required="{!v.requireFlag}"/>

                        <lightning:input type="Phone" aura:id="alternativeShippingAddress" name="contactPhone" label="Contact Phone" value="{!v.quotation.additionalContactPhone}" required="{!v.requireFlag}"/>

                        <lightning:input type="Email" aura:id="alternativeShippingAddress" name="contactEmail" label="Contact Email" value="{!v.quotation.additionalContactEmail}" required="{!v.requireFlag}"/>
                    </lightning:layoutItem>
                </lightning:layout>
                <aura:set attribute="footer">
                    <lightning:button class="slds-button slds-button_neutral" onclick="{!c.closeModal}">
                        Cancel
                    </lightning:button>
                    <lightning:button class="slds-button slds-button_brand" onclick="{!c.doSaveAddress}">
                        Save
                    </lightning:button>
                </aura:set>
            </c:CCM_Modal>

            <c:CCM_Modal size="noraml" isShow="{!v.isChangeAddress}" onClose="{!c.closeModal}" title="Shipping Address Change Confirmation">
                <lightning:layout verticalAlign="center">
                    <div class="text-center">
                        Will you confirm the change shipping address?
                    </div>
                </lightning:layout>
                <aura:set attribute="footer">
                    <lightning:button class="slds-button slds-button_neutral" onclick="{!c.closeChangeModal}">
                        Cancel
                    </lightning:button>
                    <lightning:button class="slds-button slds-button_brand" onclick="{!c.doConfirm}">
                        Confirm
                    </lightning:button>
                </aura:set>
            </c:CCM_Modal>

            <div class="CCM_PaddingTop slds-m-bottom_medium">
                <aura:if isTrue="{!v.isEdit}">
                    <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" label="Cancel" title="Cancel" onclick="{!c.cancel}" />
                    <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" variant="brand" label="Save" title="Save" onclick="{!c.doSave}"/>
                    <aura:set attribute="else">
                        <lightning:button class="slds-p-horizontal_x-large" label="Back" title="Back" onclick="{!c.doBack}"/>

                        <!-- Modified By Anony 23.1.10 修改Modify按钮可点击逻辑 Start-->

                        <!-- 当Account Manager进入本页面时，Modify按钮显示逻辑 -->
                        <!-- <aura:if isTrue="{!and(v.quotation.syncStatus == null,v.isShowEditBtn == true)}"> -->
                        <aura:if isTrue="{!and(v.quotation.syncStatus == null,and(v.isShowEditBtn == true,v.userId == v.quotation.manageAccountId))}">
                            <lightning:button class="slds-p-horizontal_x-large" label="Modify" title="Modify" onclick="{!c.doEdit}" disabled="{!and(v.isNotifiedInsideSales == true,v.isDropShip == true)}"/>
                        </aura:if>

                        <!-- 当Inside Sales进入本页面时，Modify按钮显示逻辑 -->
                        <aura:if isTrue="{!and(v.quotation.syncStatus == null,and(v.isShowEditBtn == true, v.insideSalesModify == true))}">
                            <lightning:button class="slds-p-horizontal_x-large" label="Modify" title="Modify" onclick="{!c.doEdit}" disabled="{!and(v.isNotifiedInsideSales == false,v.isDropShip == true)}"/>
                        </aura:if>

                        <!-- Modified By Anony 23.1.10 修改Modify按钮可点击逻辑 End-->
                        <aura:if isTrue="{!and(v.isShowEditBtn == true, v.userId == v.quotation.manageAccountId)}">
                            <lightning:button class="slds-p-horizontal_x-large" label="Notify inside sales" title="Notify inside sales" onclick="{!c.doSendEmail}" disabled="{!v.isNotifiedInsideSales}" />
                        </aura:if>
                        <!-- true -> 临时关闭功能， false -> 正常使用 -->
                        <aura:if isTrue="{!v.blLockOrderFuncTmp}">
                            <aura:set attribute="else">
                                <!-- update end -->
                                <aura:if isTrue="{!and(v.quotation.syncStatus == null,v.isShowSyncBtn == true)}">
                                    <!-- Modified By Anony 23.1.11 修改Sync to EBS按钮可点击逻辑, 先点击notify后才可以点击 Start-->
                                    <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="Sync to OMS" title="Sync to OMS" onclick="{!c.doSync}" disabled="{!and(v.isDropShip == true,and(v.isNotifiedInsideSales == false,v.isDelegate == false))}"/>
                                    <!-- Modified By Anony 23.1.11 修改Sync to EBS按钮可点击逻辑 End-->
                                </aura:if>
                            </aura:set>
                        </aura:if>
                    </aura:set>
                </aura:if>
            </div>
        </div>
    </div>
    </aura:if>
</aura:component>