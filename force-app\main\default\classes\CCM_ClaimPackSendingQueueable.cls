/**
 * <AUTHOR>
 * @date 2021-11-25
 * @description This queueable is used to sync Claim Packs to ERP.
 */
@SuppressWarnings('PMD.VariableNamingConventions, PMD.ApexSuggestUsingNamedCred')
public with sharing class CCM_ClaimPackSendingQueueable extends CCM_Core implements Queueable, Database.AllowsCallouts {
    public static MeIntegration_Setting__mdt objIntegrationConfig = MeIntegration_Setting__mdt.getInstance(Label.CCM_Endpoint_Developer_Name_4_Claim_And_Pay_Pal);
    private Integer intNthRetry = 0;
    private final String EXCEPTION_CAUGHT_MESSAGE = 'Exception caught during sending a request: ';
    private Date datEndDate;
    private List<Claim_Pack__c> lstClaimPackToUpdate;
    private List<Claim_Pack__c> lstFailedClaimPack;
    private List<Claim_Pack__c> lstClaimPack;
    private List<Log__c> lstLog;
    @TestVisible
    private CCM_ClaimPackSendingQueueable(Date datEndDate, List<Claim_Pack__c> lstClaimPack, Integer intNthRetry) {
        this(datEndDate, lstClaimPack);
        this.intNthRetry = intNthRetry;
    }
    /**
     * @description This is the unique constructor receiving a list of Claim Packs.
     * @param datEndDate the processing date
     * @param lstClaimPack a list of Claim Packs
     */
    public CCM_ClaimPackSendingQueueable(Date datEndDate, List<Claim_Pack__c> lstClaimPack) {
        this.datEndDate = datEndDate;
        this.lstClaimPack = lstClaimPack;
        this.lstLog = new List<Log__c>();
        this.lstFailedClaimPack = new List<Claim_Pack__c>();
        this.lstClaimPackToUpdate = new List<Claim_Pack__c>();
    }
    /**
     * @description This method is used to execute the main logic to process Claim Pack sending.
     * @param objSC the queue context
     */
    public void execute(QueueableContext objSC) {
        Map<String, Object> mapParameter;
        if (objIntegrationConfig == null || this.lstClaimPack == null || this.lstClaimPack.isEmpty()) {
            return;
        }
        for (Claim_Pack__c objCP : lstClaimPack) {
            mapParameter = prepareRequestBody(objCP);
            storeSyncingResult(sendRequest(mapParameter), objCP, mapParameter);
        }
        CCM_DmlUtils.doUpdateIgnoreResults(lstClaimPackToUpdate, getClassName(), getMethodName());
        CCM_DmlUtils.doInsertIgnoreResults(lstLog, getClassName(), getMethodName());
        retry();
    }
    private void storeSyncingResult(HttpResponse objResponse, Claim_Pack__c objClaimPack, Map<String, Object> mapParameter) {
        Boolean boolHasInterfaceException = objResponse.getBody()?.contains(EXCEPTION_CAUGHT_MESSAGE) == true;
        Claim_Pack__c objClaimPackToUpdate = new Claim_Pack__c(Id = objClaimPack.Id, Send_Time__c = Datetime.now(), Sent_to_ERP__c = boolHasInterfaceException == false);
        if (boolHasInterfaceException == false) {
            setSyncingResult(objClaimPackToUpdate, parseResponse(objResponse, objClaimPack, mapParameter));
        }
        lstClaimPackToUpdate.add(objClaimPackToUpdate);
        if (boolHasInterfaceException == true || (Test.isRunningTest() && this.intNthRetry == 0)) {
            lstFailedClaimPack.add(objClaimPack);
        }
        lstLog.add(
            new Log__c(
                ApexName__c = getClassName(),
                Error_Message__c = boolHasInterfaceException == true ? objResponse.getBody() : null,
                Method__c = getMethodName(),
                Name = 'Sending a Claim Pack record: ' + objClaimPack.Id + ' at ' + Datetime.now().format(CCM_Constants.DATETIME_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS),
                ReqParam__c = JSON.serialize(mapParameter),
                ResParam__c = objResponse.getBody()
            )
        );
    }
    private void setSyncingResult(Claim_Pack__c objClaimPackToUpdate, ResponseWrapper objResponseWrapper) {
        String strErrorMessage = '';
        if (objResponseWrapper?.Process_Result != null && objResponseWrapper?.Process_Result.isEmpty() == false) {
            strErrorMessage += String.isNotBlank(objResponseWrapper.Process_Result[0].Error_Message) ? objResponseWrapper.Process_Result[0].Error_Message : '';
            strErrorMessage += String.isNotBlank(objResponseWrapper.Process_Result[0].Error_Detail) ? objResponseWrapper.Process_Result[0].Error_Detail : '';
        }
        objClaimPackToUpdate.Sync_Status__c = objResponseWrapper?.Process_Status;
        objClaimPackToUpdate.Error_Message__c = strErrorMessage;
    }
    private ResponseWrapper parseResponse(HttpResponse objResponse, Claim_Pack__c objClaimPack, Map<String, Object> mapParameter) {
        ResponseWrapper objResponseWrapper;
        try {
            objResponseWrapper = (ResponseWrapper) JSON.deserialize(objResponse.getBody(), ResponseWrapper.class);
        } catch (JSONException objE) {
            lstLog.add(
                new Log__c(
                    ApexName__c = getClassName(),
                    Error_Message__c = objE.getMessage(),
                    Method__c = getMethodName(),
                    Name = 'Failed in parsing JSON response for ' + objClaimPack.Id + ' at ' + System.now().format(CCM_Constants.DATETIME_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS),
                    ReqParam__c = JSON.serialize(mapParameter),
                    ResParam__c = objResponse.getBody()
                )
            );
        }
        return objResponseWrapper;
    }
    private Map<String, Object> prepareRequestBody(Claim_Pack__c objClaimPack) {
        ClaimItemWrapper objClaimItemWrapper;
        List<ClaimItemWrapper> lstClaimItemWrapper;
        Map<String, Object> mapParameter = new Map<String, Object>{
            'Salesforce_Number' => objClaimPack.Name,
            'Customer' => objClaimPack.Channel_Customer__r.AccountNumber,
            'BillTo' => objClaimPack.Bill_To_Address__r.Customer_Line_Oracle_ID__c,
            'Amount' => objClaimPack.Amount__c,
            'Date' => this.datEndDate,
            'CurrencyCode' => objClaimPack.CurrencyIsoCode,
            'Org_Code' => objClaimPack.Bill_To_Address__r.Account_Address__r.ORG_ID__c,
            'Cost_Center' => '',
            'DataType' => objClaimPack.Type__c == CCM_Constants.CLAIM_PACK_TYPE_FLEET_CLAIM ? 'F_CLAIM' : 'S_CLAIM',
            'Attribute1' => '',
            'Attribute2' => '',
            'Attribute3' => '',
            'Attribute4' => '',
            'Attribute5' => objClaimPack.Warranty_parts_credit_mark_up__c
        };
        if(objClaimPack.Ship_to_Address__r != null) {
            mapParameter.put('Attribute1', objClaimPack.Ship_to_Address__r.Customer_Line_Oracle_ID__c);
        }
        if(CCM_Constants.ORG_CODE_CCA == objClaimPack.Bill_To_Address__r.Account_Address__r.ORG_ID__c) {
            mapParameter.put('CurrencyCode', 'CAD');
        }
        lstClaimItemWrapper = new List<ClaimItemWrapper>();
        for (Warranty_Claim__c objWC : objClaimPack.Warranty_Claims__r) {
            List<Account_Address__c> tier2address = [SELECT Store_Number__c FROM Account_Address__c WHERE X2nd_Tier_Dealer__c =: objWC.Service_Partner__c AND Customer__r.AccountNumber = '0376'];
            objClaimItemWrapper = new ClaimItemWrapper();
            objClaimItemWrapper.Pack_No = objClaimPack.Name;
            objClaimItemWrapper.Claim_Item_No = objWC.Name;
            objClaimItemWrapper.Amount = String.valueOf(objWC.Total_without_Tax__c);
            objClaimItemWrapper.Quantity = '1';
            if(tier2address.size() > 0){
                objClaimItemWrapper.line_attribute1 = tier2address[0].Store_Number__c;
            }
            if(objWC.GST__c != null) {
                objClaimItemWrapper.line_attribute2 = String.valueOf(objWC.GST__c);    
            }
            if(objWC.HST__c != null) {
                objClaimItemWrapper.line_attribute3 = String.valueOf(objWC.HST__c);
            }
            if(objWC.QST__c != null) {
                objClaimItemWrapper.line_attribute4 = String.valueOf(objWC.QST__c);
            }
            if(objWC.PST__c != null) {
                objClaimItemWrapper.line_attribute5 = String.valueOf(objWC.PST__c);
            }
            lstClaimItemWrapper.add(objClaimItemWrapper);

        }
        for (Fleet_Claim__c objFC : objClaimPack.Fleet_Claims__r) {
            objClaimItemWrapper = new ClaimItemWrapper();
            objClaimItemWrapper.Pack_No = objClaimPack.Name;
            objClaimItemWrapper.Claim_Item_No = objFC.Name;
            objClaimItemWrapper.Amount = String.valueOf(objFC.Estimated_Credit_Return__c);
            objClaimItemWrapper.Quantity = '1';
            lstClaimItemWrapper.add(objClaimItemWrapper);
        }
        if (!lstClaimItemWrapper.isEmpty()) {
            mapParameter.put('Claim_Items', lstClaimItemWrapper);
        }
        return mapParameter;
    }
    private HttpResponse sendRequest(Map<String, Object> mapParameter) {
        HttpRequest objHttpRequest = new HttpRequest();
        HttpResponse objHttpResponse = new HttpResponse();
        objHttpRequest.setEndpoint(objIntegrationConfig.End_Point__c);
        objHttpRequest.setMethod('POST');
        objHttpRequest.setHeader('Authorization', 'Basic ' + objIntegrationConfig.Key__c);
        objHttpRequest.setHeader('Content-Type', 'application/json');
        objHttpRequest.setTimeout(120000);
        if (mapParameter != null) {
            objHttpRequest.setBody(JSON.serialize(mapParameter));
        }
        try {
            objHttpResponse = new Http().send(objHttpRequest);
        } catch (Exception objE) {
            objHttpResponse = new HttpResponse();
            objHttpResponse.setHeader('Content-Type', 'application/json');
            objHttpResponse.setBody('{"message":" ' + EXCEPTION_CAUGHT_MESSAGE + ' ' + objE.getMessage() + '"}');
        }
        return objHttpResponse;
    }
    private void retry() {
        if (lstFailedClaimPack.isEmpty() || this.intNthRetry == null || this.intNthRetry >= Integer.valueOf(Label.CCM_Claim_Pack_Sending_Maximum_Retry_Times)) {
            return;
        }
        // prettier-ignore
        if (!Test.isRunningTest()) { System.enqueueJob(new CCM_ClaimPackSendingQueueable(Date.today(), lstFailedClaimPack, intNthRetry + 1)); }
    }
    private class ClaimItemWrapper {
        public String Pack_No;
        public String Claim_Item_No;
        public String Amount;
        public String Quantity;
        public String line_attribute1;
        public String line_attribute2;
        public String line_attribute3;
        public String line_attribute4;
        public String line_attribute5;
    }
    private class ResponseWrapper {
        public String Process_Status;
        public List<ProcessResultWrapper> Process_Result;
    }
    private class ProcessResultWrapper {
        public String Error_Detail;
        public String Error_Message;
    }
}