<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CallHighlightAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CallHighlightAction</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CallHighlightAction</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <content>CCM_NewOrEditAccount</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>LightningComponent</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>EmailHighlightAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>EmailHighlightAction</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>EmailHighlightAction</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>EnableCustomerPortalUser</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>EnableCustomerPortalUser</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>EnableCustomerPortalUser</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ListClean</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ListClean</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ListClean</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <content>CCM_NewOrEditAccount</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>LightningComponent</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <content>NewOrEditAccountPage</content>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Visualforce</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <content>CCM_NewOrEditAccount</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>LightningComponent</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>RequestUpdate</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>RequestUpdate</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>RequestUpdate</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SmsHighlightAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SmsHighlightAction</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SmsHighlightAction</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Customer_Record_Page2</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ViewCustomerPortalUser</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ViewCustomerPortalUser</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ViewCustomerPortalUser</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>WebsiteHighlightAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>WebsiteHighlightAction</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>WebsiteHighlightAction</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>true</enableFeeds>
    <enableHistory>true</enableHistory>
    <externalSharingModel>Private</externalSharingModel>
    <profileSearchLayouts>
        <fields>ACCOUNT.NAME</fields>
        <fields>ACCOUNT.RECORDTYPE</fields>
        <fields>ACCOUNT.SITE</fields>
        <fields>ACCOUNT.PHONE1</fields>
        <fields>CORE.USERS.ALIAS</fields>
        <fields>Email__c</fields>
        <fields>PC_EMAIL</fields>
        <fields>Credit_Limit__c</fields>
        <fields>Risk_Code__c</fields>
        <profileName>NA Sales Manager</profileName>
    </profileSearchLayouts>
    <profileSearchLayouts>
        <fields>ACCOUNT.NAME</fields>
        <fields>ACCOUNT.RECORDTYPE</fields>
        <fields>ACCOUNT.SITE</fields>
        <fields>ACCOUNT.PHONE1</fields>
        <fields>CORE.USERS.ALIAS</fields>
        <fields>Email__c</fields>
        <fields>PC_EMAIL</fields>
        <fields>Risk_Code__c</fields>
        <fields>Credit_Limit__c</fields>
        <profileName>NA Inside Sales</profileName>
    </profileSearchLayouts>
    <profileSearchLayouts>
        <fields>ACCOUNT.NAME</fields>
        <fields>ACCOUNT.RECORDTYPE</fields>
        <fields>ACCOUNT.SITE</fields>
        <fields>ACCOUNT.ADDRESS1_STATE</fields>
        <fields>ACCOUNT.ADDRESS2_STATE</fields>
        <fields>ACCOUNT.PHONE1</fields>
        <fields>CORE.USERS.ALIAS</fields>
        <fields>Email__c</fields>
        <fields>PC_EMAIL</fields>
        <fields>ACCOUNT.ACCOUNT_NUMBER</fields>
        <profileName>System Administrator</profileName>
    </profileSearchLayouts>
    <recordTypeTrackFeedHistory>false</recordTypeTrackFeedHistory>
    <recordTypeTrackHistory>false</recordTypeTrackHistory>
    <searchLayouts>
        <customTabListAdditionalFields>ACCOUNT.NAME</customTabListAdditionalFields>
        <customTabListAdditionalFields>ACCOUNT.RECORDTYPE</customTabListAdditionalFields>
        <customTabListAdditionalFields>ACCOUNT.ADDRESS2_CITY</customTabListAdditionalFields>
        <customTabListAdditionalFields>ACCOUNT.PHONE1</customTabListAdditionalFields>
        <excludedStandardButtons>DiscoveryGetAccountsAction</excludedStandardButtons>
        <listViewButtons>Count_Categories</listViewButtons>
        <lookupDialogsAdditionalFields>ACCOUNT.NAME</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>ACCOUNT.SITE</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>ACCOUNT.RECORDTYPE</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>CORE.USERS.ALIAS</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>ACCOUNT.TYPE</lookupDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>ACCOUNT.NAME</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>ACCOUNT.RECORDTYPE</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>ACCOUNT.SITE</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>ACCOUNT.ADDRESS1_STATE</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>ACCOUNT.ADDRESS2_STATE</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>ACCOUNT.PHONE1</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>CORE.USERS.ALIAS</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Email__c</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>PC_EMAIL</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>ACCOUNT.ACCOUNT_NUMBER</lookupPhoneDialogsAdditionalFields>
        <searchResultsAdditionalFields>ACCOUNT.NAME</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>ACCOUNT.RECORDTYPE</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>ACCOUNT.SITE</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>ACCOUNT.PHONE1</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>CORE.USERS.ALIAS</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Email__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>PC_EMAIL</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>Private</sharingModel>
</CustomObject>
