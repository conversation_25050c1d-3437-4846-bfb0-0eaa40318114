<!-- <apex:page controller="CCM_RofPdfGenerateCtrl" id="CcmReverseOrderReturnFormVF"   applyHtmlTag="false" showHeader="false" sidebar="false" lightningStylesheets="false" standardStylesheets="true"> -->
<apex:page controller="CCM_RofPdfGenerateCtrl" id="CcmReverseOrderReturnFormVF" renderAs="pdf"  applyHtmlTag="false" showHeader="false" sidebar="false" lightningStylesheets="false" standardStylesheets="true">
    <!-- <meta charset="utf-8" /> --><!-- error if this mark-up be written here -->
    <head>
        <meta charset="UTF-8" />
        <style>
            /* style property background-color for debug */
            body { font-family: 'Arial Unicode MS'; } 
            .pdf-form-container-wrap {
                text-align: center;
            }
           .pdf-return-form {
                width: 100%;
                background-color: skyblue;
                margin: 0 auto;
            }
            .pdf-form-main {
                width: 60%;
                height: 400px; /* ui design */
                text-align: left;
                background-color: burlywood;
            }
            .pdf-instruction{
                width: 40%;
                text-align: left;
                background-color: gainsboro;
            }
            .pdf-table td {
                border-collapase: collapase;
                border:1px solid black;
            }
            .pdf-instruction ul li {
                list-style-type:decimal;
                font-size:10px;
            }
            .pdf-instruction .info-left {
            } 
            .pdf-instruction ul li {
                list-style-type:decimal;
                font-size:10px;
            }
        </style>
    </head>
    <body>
        <h1 style="">Return Form</h1>
        <p>"Include copy of Return Form in each carton"</p>
        <p>Request Number</p>
        <p class="" style="border:2px solid #000; text-align:center; padding:6px;">{!ROR.Reverse_Order_Request_Number__c}</p>
        <div class="pdf-form-container-wrap" style="background: #fff;">
            <div class="pdf-return-form">
                <div class="pdf-form-main" style="float:left;"> <!-- only in this way can INSTRUCTIONS be lied at the right -->
                    <!-- ship -->
                    <div class="slds-grid">
                        <div class="slds-col slds-size_6-of-12">
                            <div class="slds-grid">
                                <div style="width:2000px; font-weight:bold;"><b>Return Form Date: </b> 
                                    <apex:outputText value="{0,date,yyyy'-'MM'-'dd}"><apex:param value="{!ROR.Return_Form_Date__c}" /></apex:outputText>
                                </div>
                            </div>
                            <div class="slds-grid slds-m-top_xx-small">
                                <div style="width:240px; font-weight:bold;"><b>Customer Code: </b>{!ROR.Customer__r.AccountNumber}</div>
                            </div>
                            <div class="slds-grid slds-m-top_xx-small">
                                <div style="width:240px; font-weight:bold;"><b>Customer Name: </b></div>
                                <div style="font-size:14px;">{!ROR.Customer__r.Name}</div>
                            </div>
                            <div class="slds-grid slds-m-top_xx-small">
                                <div style="width:120px; font-weight:bold;"><b>Bill To: </b></div>
                                <div style="font-size:14px;">
                                    {!strBillToAddress}
                                </div>
                            </div>
                        </div>
                        <div class="slds-col slds-size_6-of-12">
                            <div>
                                <div style="font-weight:bold;"><b>Ship From: </b></div>
                                <div style="font-size:14px;">
                                    {!strShipFromAddress}
                                </div>
                            </div>
                        </div>
                        <div class="slds-col slds-size_6-of-12">
                            <div class="slds-grid slds-m-top_xx-small" style="{!IF(strAsnNumber != null, '' , 'display:none')}">
                                <div style="width:120px; font-weight:bold;"><b>ASN Number: </b></div>
                                <div style="font-size:14px;">RMA{!strAsnNumber}</div>
                            </div>
                            <div class="slds-grid slds-m-top_xx-small" style="{!IF(customerPO != null, '' , 'display:none')}">
                                <div style="width:120px; font-weight:bold;"><b>Customer PO: </b></div>
                                <div style="font-size:14px;">{!customerPO}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- instruction with caution image -->
                <div class="pdf-instruction">
                    <div class="info-above" style="width:100%;">
                        <h3 class="slds-text-heading_medium slds-var-p-left_x-small">SHIPPING INSTRUCTIONS:</h3>
                        <ul class="slds-list_ordered">
                            <li>
                                1. Please include a copy of this RGA in each package sent.
                            </li>
                            <li>
                                2. When the product is ready for shipment please email
                            </li>
                            <li style="{!IF(ROR.ORG_Code__c == 'CCA', '' , 'display:none')}"><EMAIL> for a shipping label or to</li>
                            <li style="{!IF(ROR.ORG_Code__c != 'CCA', '' , 'display:none')}"><EMAIL> </li>
                            <li style="{!IF(ROR.ORG_Code__c != 'CCA', '' , 'display:none')}">and <EMAIL> for a shipping label or to</li>
                            <li>schedule a pick up</li>
                            <li>
                                3. Please provide a copy of your RGA to
                            </li>
                            <li style="{!IF(ROR.ORG_Code__c == 'CCA', '' , 'display:none')}"><EMAIL> when you send your request.</li>
                            <li style="{!IF(ROR.ORG_Code__c != 'CCA', '' , 'display:none')}"><EMAIL> </li>
                            <li style="{!IF(ROR.ORG_Code__c != 'CCA', '' , 'display:none')}">and <EMAIL> when you send your request.</li>
                            <li>
                                4. If the product is on a pallet, please send the pallet
                            </li>
                            <li>dimensions(LxWxH) and weight when requesting a shipping</li>
                            <li>label or to schedule a pick up</li>
                        </ul>
                    </div>
                    <div class="img-below">
                        <apex:image id="cautionImg" value="{!$Resource.Reverse_Order_Caution}" width="200"/>
                    </div>
                </div>
            </div>
            <!-- TODO -->
            <apex:repeat value="{!warehouseItems}" var="warehouseItem">
                <div style="text-align:left;margin-top:20px;"><b>Ship To: </b>{!warehouseItem.warehouse}</div>
                <div class="table-container" style="width:100%;">
                    <table class="pdf-table " style=";border: 1px solid black">
                        <thead>
                            <tr class="slds-line-height_reset" style="margin:0 auto;">
                                <!-- <th style="width:10%;">
                                    Line
                                </th> -->
                                <th style="width: 20%;">
                                    Model
                                </th>
                                <th style="width: 40%;">
                                    Description
                                </th>
                                <th style="width: 20%;">
                                    Brand
                                </th>
                                <th style="width: 20%;">
                                    Unit
                                </th>
                                <th style="width: 10%;">
                                    Quantity
                                </th>
                            </tr>
                        </thead>
                        <tbody id="table-line-number">
                            <apex:repeat value="{!warehouseItem.items}" var="ROIs" >
                                <tr>
                                    <td scope="row">
                                        {!ROIs.Product2__r.ProductCode}
                                    </td>
                                    <td scope="row" style="text-align:left;">
                                        {!ROIs.Product2__r.SF_Description__c}
                                    </td>
                                    <td scope="row">
                                        {!ROIs.Brand__c}
                                    </td>
                                    <td scope="row">
                                        {!ROIs.Unit__c}
                                    </td>
                                    <td scope="row">
                                        {!ROIs.Qty__c}
                                    </td>
                                </tr>
                            </apex:repeat>
                        </tbody>
                    </table>
                </div>
            </apex:repeat>
        </div>
    </body>
</apex:page>