import { LightningElement, track, wire, api } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { NavigationMixin } from 'lightning/navigation';
import FORM_FACTOR from '@salesforce/client/formFactor'
import getSource from '@salesforce/apex/CCM_LogACallController.getSource';
import deleteFile from '@salesforce/apex/CCM_LogACallController.deleteFile';
import saveCall from '@salesforce/apex/CCM_LogACallController.saveCall';
import getVersionDataUrl from '@salesforce/apex/CCM_LogACallController.getVersionDataUrl';

export default class ccmLogACall_MAP extends NavigationMixin(LightningElement) {
	isDesktop=false;
	@track recordId;
	subject;
	comments;
	visitDate;
	actionItems;
	callSubject;
	purpose;
	purposeDisable = true;
	url;
	@track options;
	@track callSubjectOptions;
	@track purposeOptions;
	showLoading = false;

	imgURL;
	showLargeImg = false;

	purposemap = {
		'FLEX 24V': [{ label: 'Product Feedbacks', value: 'Product Feedbacks' }, { label: 'Market Information', value: 'Market Information' }, { label: 'Competitors’ Information', value: 'Competitors’ Information' }, { label: 'Other Information', value: 'Other Information' }],
		'Rear Handle Saw': [{ label: 'Product Feedbacks', value: 'Product Feedbacks' }, { label: 'Market Information', value: 'Market Information' }, { label: 'Competitors’ Information', value: 'Competitors’ Information' }, { label: 'Other Information', value: 'Other Information' }, { label: 'Customer Pitch Progress', value: 'Customer Pitch Progress' }, { label: 'Order', value: 'Order' }],
		'Product': [{ label: 'Product Demonstration', value: 'Product Demonstration' }, { label: 'New Product Review', value: 'New Product Review' }, { label: 'Product Information', value: 'Product Information' }],
		'Order': [{ label: 'Phone Order', value: 'Phone Order' }, { label: 'Order Status Inquiry', value: 'Order Status Inquiry' }],
		'Promotion': [{ label: 'Reviewed Promotions', value: 'Reviewed Promotions' }],
		'Business Review & Planning': [{ label: 'Quarterly Business Review', value: 'Quarterly Business Review' }, { label: 'Line Review', value: 'Line Review' }, { label: 'Strategic Planning', value: 'Strategic Planning' }],
		'Program & Rebate': [{ label: 'Spring Booking Program', value: 'Spring Booking Program' }, { label: 'Rebate Sign-up', value: 'Rebate Sign-up' }],
		'Shipping Issues': [{ label: 'Shortages', value: 'Shortages' }, { label: 'Shipping wrong items', value: 'Shipping wrong items' }],
		'Return & Warranty': [{ label: 'Damaged or Defective Product', value: 'Damaged or Defective Product' }, { label: 'Warranty Inquiry (Transferred Call)', value: 'Warranty Inquiry (Transferred Call)' }],
		'Event': [{ label: 'Customer Event', value: 'Customer Event' }, { label: 'Open House Event', value: 'Open House Event' }],
		'Other Inquiries': [{ label: 'Training', value: 'Training' }, { label: 'Dealer Portal Help', value: 'Dealer Portal Help' }, { label: 'Account Information Inquiry (Transferred Call)', value: 'Account Information Inquiry (Transferred Call)' }, { label: 'Potential Risk', value: 'Potential Risk' }],
		'Merchandising': [{ label: 'Merchandising Program Feedback', value: 'Merchandising Program Feedback' }, { label: 'Competitor’s Merchandising', value: 'Competitor’s Merchandising' }, { label: 'Store Merchandising Setup', value: 'Store, Merchandising Setup' }, { label: 'Other Information', value: 'Other Information' }],
		'Salesforce & Info Lab Outreach': [{ label: 'Rep Training', value: 'Rep Training' }, { label: 'Dealer Training', value: 'Dealer Training' }, { label: 'Promote Portal Features', value: 'Promote Portal Features' }, { label: 'Portal Access Help', value: 'Portal Access Help' }],
		'New Dealer Setup': [{ label: 'Submitting for Review', value: 'Submitting for Review' }],
		'New ASC Setup': [{ label: 'Submitting for Review', value: 'Submitting for Review' }],
		'Brand Extension': [{ label: 'Submitting Documentation', value: 'Submitting Documentation' }],
	};

	@track contact = {};
	contactName;
	@track contactSource = [];


	@track account = {};
	accountName;
	@track accountSource = [];


	@track storeVisit = {};
	storeVisitName;
	@track storeVisitSource = [];

	@track uploadedFiles = [];
	@track imgFiles = [];
	@track documentFiles = [];


	connectedCallback() {
		if (FORM_FACTOR == 'Large') { this.isDesktop =true}
		this.subject = "Call";
		this.options = [{ label: 'Store Visit', value: 'Store Visit' },
		{ label: 'Call Report', value: 'Call Report' },
		{ label: 'Call', value: 'Call' },
		{ label: 'Voicemail Received', value: 'Voicemail Received' },
		{ label: 'Email Received', value: 'Email Received' },
		{ label: 'Voicemail Left', value: 'Voicemail Left' },
		{ label: 'Community', value: 'Community' },
		{ label: 'Social Media', value: 'Social Media' },
		{ label: 'Review', value: 'Review' },
		{ label: 'Sales Target', value: 'Sales Target' },
		{ label: 'Reverse Order Request - Overage', value: 'Reverse Order Request - Overage' },
		{ label: 'Demo Log', value: 'Demo Log' },
		{ label: 'Other', value: 'Other' }];

		this.callSubjectOptions = [{ label: 'FLEX 24V', value: 'FLEX 24V' },
		{ label: 'Rear Handle Saw', value: 'Rear Handle Saw' },
		{ label: 'Product', value: 'Product' },
		{ label: 'Order', value: 'Order' },
		{ label: 'Promotion', value: 'Promotion' },
		{ label: 'Business Review & Planning', value: 'Business Review & Planning' },
		{ label: 'Program & Rebate', value: 'Program & Rebate' },
		{ label: 'Shipping Issues', value: 'Shipping Issues' },
		{ label: 'Return & Warranty', value: 'Return & Warranty' },
		{ label: 'Event', value: 'Event' },
		{ label: 'Other Inquiries', value: 'Other Inquiries' },
		{ label: 'Salesforce & Info Lab Outreach', value: 'Salesforce & Info Lab Outreach' },
		{ label: 'New Dealer Setup', value: 'New Dealer Setup' },
		{ label: 'New ASC Setup', value: 'New ASC Setup' },
		{ label: 'Brand Extension', value: 'Brand Extension' },
		{ label: 'Merchandising', value: 'Merchandising' }];

		//get recordId
		this.recordId = window.location.toString().slice(-18);
		// init contact source; account source; store visit source
		getSource({ 'recordId': this.recordId }).then(result => {
			if (result) {
				let resultJson = JSON.parse(result);
				this.accountSource = resultJson['accountSources'];
				this.contactSource = resultJson['contactSources'];
				this.storeVisitSource = resultJson['storeVisitSources'];

				if (this.accountSource.length > 0) {
					this.account = this.accountSource[0];
				}
				this.visitDate = resultJson['tToday'];
			}
		}, error => {
			console.log(error.body.message);
		});
	}

	handleSubjectChange(event) {
		console.log(event.target.value);
		this.subject = event.target.value;
	}

	handleCommentsChange(event) {
		console.log(event.target.value);
		this.comments = event.target.value;
	}

	handleVisitDateChange(event) {
		console.log(event.target.value);
		this.visitDate = event.target.value;
	}

	handleActionItemChange(event) {
		console.log(event.target.value);
		this.actionItems = event.target.value;
	}

	handleCallSubjectChange(event) {
		console.log(event.target.value);
		this.callSubject = event.target.value;
		if (event.target.value) {
			this.purposeDisable = false;
			this.purposeOptions = this.purposemap[event.target.value];
		}
		else {
			this.purposeDisable = true;
			this.purpose = undefined;
			this.purposeOptions = undefined;
		}
	}

	handlePurposeChange(event) {
		console.log(event.target.value);
		this.purpose = event.target.value;
	}

	handleContactSelected(event) {
		this.contact = JSON.parse(event.detail);
		console.log(this.contact["contactId"]);
	}

	handleContactInputChange(event) {
		console.log(event.detail);
		if (event.detail.trim() === '') {
			this.contact = {};
		}
	}

	handleAccountSelected(event) {
		this.account = JSON.parse(event.detail);
		console.log(this.account["accountId"]);
	}

	handleAccountInputChange(event) {
		console.log(event.detail);
		if (event.detail.trim() === '') {
			this.account = {};
		}
	}

	handleStoreVisitSelected(event) {
		this.storeVisit = JSON.parse(event.detail);
		console.log(this.storeVisit['storeVisitId']);
	}

	handleStoreVisitInputChange(event) {
		console.log(event.detail);
		if (event.detail.trim() === '') {
			this.storeVisit = {};
		}
	}

	handleUploadFinished(event) {
		console.log('file upload=======================');
		const uploadedFiles = event.detail.files;
		let imgExtensions = ['.JPEG', '.PNG', '.JPG'];
		let imgFiles = [];
		let documentFiles = [];
		uploadedFiles.forEach(item => {
			let fileName = item.name;
			let index = fileName.lastIndexOf('.');
			let fileExtension = fileName.substring(index).toUpperCase();
			if (imgExtensions.includes(fileExtension)) {
				imgFiles.push(item);
			}
			else {
				documentFiles.push(item);
			}
		});
		// this.uploadedFiles = this.uploadedFiles.concat(uploadedFiles);
		// console.log(this.uploadedFiles);
		let imgDocumentIds = [];
		imgFiles.forEach(item => {
			imgDocumentIds.push(item.documentId);
		});
		getVersionDataUrl({ 'documentIds': imgDocumentIds, recordId: this.recordId }).then(result => {
			if (result) {
				let resultJson = JSON.parse(result);
				let imgFilesResult = [];
				for (const [documentId, dataURL] of Object.entries(resultJson)) {
					imgFilesResult.push({
						'documentId': documentId,
						'dataURL': dataURL
					});
				}
				this.imgFiles = this.imgFiles.concat(imgFilesResult);
			}
		}, error => {
			console.log(error);
		})

		this.documentFiles = this.documentFiles.concat(documentFiles);
	}

	showImg(event) {
		const versionURL = event.currentTarget.dataset['fileurl'];
		this.imgURL = versionURL;
		this.showLargeImg = true;
	}

	hideImg() {
		this.showLargeImg = false;
	}

	deleteFile(event) {
		const documentId = event.currentTarget.dataset['id'];
		// remove id from uploadedFiles
		let tempImgFiles = JSON.parse(JSON.stringify(this.imgFiles));
		this.imgFiles = tempImgFiles.filter(item => {
			return item.documentId !== documentId;
		});

		let tempDocumentFiles = JSON.parse(JSON.stringify(this.documentFiles));
		this.documentFiles = tempDocumentFiles.filter(item => {
			return item.documentId !== documentId;
		});
		// delete file
		deleteFile({ 'documentId': documentId }).then(reulst => {

		}, error => {
			console.log(error);
		});
	}

	cancel() {
		if (this.isDesktop) {
			console.log('isDesktop:' + this.isDesktop + '--FORM_FACTOR:' + FORM_FACTOR);
			window.close();
		} else {
			let url = window.location.origin + '/lightning/n/maps__Maps';
			window.open(url, '_self');
		}
	}

	save() {
		let self = this;
		this.showLoading = true;
		let _param = {};
		if ('accountId' in this.account) {
			_param['accountId'] = this.account['accountId'];
		}
		if ('contactId' in this.contact) {
			_param['contactId'] = this.contact['contactId'];
		}
		if ('storeVisitId' in this.storeVisit) {
			_param['storeVisitId'] = this.storeVisit['storeVisitId'];
		}
		_param['subject'] = this.subject;
		if (this.comments) {
			_param['comments'] = this.comments;
		}
		if (this.visitDate) {
			_param['visitDate'] = this.visitDate;
		}
		if (this.actionItems) {
			_param['actionItems'] = this.actionItems;
		}
		if (this.callSubject) {
			_param['callSubject'] = this.callSubject;
		}
		if (this.purpose) {
			_param['purpost'] = this.purpose;
		}

		// let tempUploadedFiles = JSON.parse(JSON.stringify(this.uploadedFiles));
		let tempImgFiles = JSON.parse(JSON.stringify(this.imgFiles));
		let tempDocumentFiles = JSON.parse(JSON.stringify(this.documentFiles));
		_param['documentIds'] = [];
		tempImgFiles.forEach(item => {
			_param['documentIds'].push(item['documentId']);
		});
		tempDocumentFiles.forEach(item => {
			_param['documentIds'].push(item['documentId']);
		});

		saveCall({ 'param': JSON.stringify(_param) }).then(result => {
			let resultJson = JSON.parse(result);
			console.log(resultJson, 'resultJson=========');
			if (resultJson.isSuccess) {
				this.dispatchEvent(new ShowToastEvent({
					title: 'save',
					message: 'success',
					variant: 'success'
				}))
				console.log('isDesktop:' + this.isDesktop + '--FORM_FACTOR:' + FORM_FACTOR);
				if (this.isDesktop) {
					setTimeout(()=>{
						window.close();
					}, 1000)
				} else {
					var pageReference = {
						type: 'standard__recordPage',
						attributes: {
							recordId: `${this.recordId}`,
							objectApiName: 'Account',
							actionName: 'view'
						}
					};
					this[NavigationMixin.Navigate](pageReference);
				}
			}
			else {
				this.dispatchEvent(new ShowToastEvent({
					title: 'save',
					message: 'error:' + resultJson.errormsg,
					variant: 'error'
				}));
			}
			this.showLoading = false;
		}, error => {
			this.dispatchEvent(new ShowToastEvent({
				title: 'save',
				message: 'error:' + error,
				variant: 'error'
			}));
			this.showLoading = false;
		});
	}
}