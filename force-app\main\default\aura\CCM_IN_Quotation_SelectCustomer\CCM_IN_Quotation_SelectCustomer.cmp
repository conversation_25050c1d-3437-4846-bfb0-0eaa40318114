<aura:component access="global" controller="CCM_IN_Quotation_SelectCustomerCtl">
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <aura:attribute name="currentStep" type="Integer" />
    <aura:attribute name="customerId" type="String" default=""/>
    <aura:attribute name="customerFilterCondition" type="String" default=""/>
    <aura:attribute name="customerName" type="String" default=""/>
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="orderTypeVal" type="String" default=""/>
    <aura:attribute name="requireFlag" type="Boolean" default="true"/>
    <aura:attribute name="disableFlag" type="Boolean" default="false"/>
    <aura:attribute name="hasDropShipAddress" type="Boolean" default="false"/>
    <aura:attribute name="isDropShipOpt" type="List" default="[{'label': 'Yes', 'value': 'Y'},
                      {'label': 'No', 'value': 'N'}]"/>
    <aura:attribute name="validated" type="Boolean" default="false"/>
    <!-- 前台验证JS引用 -->
    <ltng:require scripts="{!join(',', $Resource.Validator)}"/>
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <aura:handler name="change" value="{!v.customerId}" action="{!c.handleSelectCustomer}" />
    <lightning:card class="mainContent">
        <div class="c-container" style="padding: 10px">
          <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
            <lightning:layout horizontalAlign="space" verticalAlign="center">
                <lightning:layoutItem alignmentBump="right" size="3">
                    <c:CCM_AutoMatchPickList
                        aura:id="required-Field"
                        objectType="Account" label="Select Customer"
                        labelField="Name"
                        labelField1="Distributor_or_Dealer__c"
                        filterCondition='{!v.customerFilterCondition}'
                        fieldList="Name,AccountNumber,Distributor_or_Dealer__c,Surcharge__c"
                        value="{!v.customerId}"
                        inputValue="{!v.customerName}"
                        required="{!v.requireFlag}"
                        disabled="{!v.disableFlag}"/>
                </lightning:layoutItem>
                <lightning:layoutItem alignmentBump="right" size="3">
                </lightning:layoutItem>
                <lightning:layoutItem alignmentBump="right" size="3">
                    <lightning:combobox class="ccm_display"
                        name="isDropShip" 
                        value="{!v.orderTypeVal}" 
                        options="{!v.isDropShipOpt}" 
                        label="Is DropShip Order?"
                        disabled="{!!v.hasDropShipAddress}"
                        required="{!v.requireFlag}"/>
                </lightning:layoutItem>
                <lightning:layoutItem alignmentBump="right" size="3">
                </lightning:layoutItem>
            </lightning:layout>

            <div>
                <c:CCM_Section title="Customer Detail" expandable="true">
                    <lightning:recordViewForm recordId="{!v.customerId}" objectApiName="Account">
                        <lightning:layout horizontalAlign="space" multipleRows="true">
                            <lightning:layoutItem size="6">
                              <lightning:outputField fieldName="Name" />
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                              <lightning:outputField fieldName="OwnerId" />
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                              <lightning:outputField fieldName="Customer_Class__c" />
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                              <lightning:outputField fieldName="Distributor_or_Dealer__c" />
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                              <lightning:outputField fieldName="Phone" />
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                              <lightning:outputField fieldName="Intended_Brand__c" />
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                              <lightning:outputField fieldName="Phone_Ext__c" />
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                              <lightning:outputField fieldName="Online_Selling__c" />
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                              <lightning:outputField fieldName="Fax" />
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                              <lightning:outputField fieldName="AccountNumber" />
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                              <lightning:outputField fieldName="Website" />
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                              <lightning:outputField fieldName="ParentId" />
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                              <p>   </p>
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                              <lightning:outputField fieldName="Surcharge__c" />
                            </lightning:layoutItem>
                        </lightning:layout>
                    </lightning:recordViewForm>
                  </c:CCM_Section>
            </div>
        </div>

        <aura:set attribute="footer">
            <div>
                <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" label="Cancel" title="Cancel" onclick="{!c.cancel}" />
                <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="Next" title="Next" disabled="{!v.validated}" onclick="{!c.nextStep}" />
            </div>
        </aura:set>
    </lightning:card>
</aura:component>