/**
 * Created by gluo006 on 9/25/2019.
 */
({
    getData: function(component){
        var recordId = this.getUrlParameter('recordId');
        var action = component.get("c.getData");
        if(recordId){
            action.setParam('recordId', recordId);
            component.set('v.isShowTerm', true);
        }
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log('state result--->'+state);
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if(results){
                    component.set('v.po', results.po);
                    component.set('v.currencySymbol', results.customerCurrencyIsoCode);
                    component.set('v.orderItemList', results.poItems);
                    component.set('v.customerId', results.customerId);
                    component.set('v.brandScopeOpt', results.brandScopeList);
                    if (recordId){
                        component.set('v.brandScope', results.po.Brand_Scope__c);
                        var e = $A.get("e.c:CCM_SelectProductListEvt");
                        e.fire();
                    }
                }
            } else {
                var errors = response.getError();
            }
        });
        $A.enqueueAction(action);
    },
    getUrlParameter : function(sParam) {
        var sPageURL = decodeURIComponent(window.location.search.substring(1)),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');
            if (sParameterName[0] === sParam || sParameterName[0] === ('0.' + sParam)) {
                return sParameterName[1] === undefined ? true : sParameterName[1];
            }
        }
    },
    getPaymentFreightRule: function(component, event, helper){
        var action = component.get("c.getPaymentFreightTerm");
        action.setParam('customerId', component.get('v.customerId'));
        action.setParam('brandName', component.get('v.brandScope'));
        action.setCallback(this, function(response){
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                if(result){
                    component.set('v.paymentTerm', result.paymentTerm);
                    component.set('v.freightTerm', result.freightTerm);
                    component.set('v.freightTermLabel', result.freightTermLabel);
                    component.set('v.paymentTermLabel', result.paymentTermLabel);
                    component.set('v.freightTermRuleFee', result.freightTermRuleFee);

                    var purchaseOrder = component.get('v.po');
                    purchaseOrder.Payment_Term__c = component.get('v.paymentTerm');
                    purchaseOrder.Freight_Term__c = component.get('v.freightTerm');
                    component.set('v.po', purchaseOrder);
                }
            }else {
                console.log(response.getError());
            }
        });
        $A.enqueueAction(action);
    }
})