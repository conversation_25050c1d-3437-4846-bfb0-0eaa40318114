public without sharing class CCM_NewOrEditCustomerForAddressCtl {

    @AuraEnabled
    public static String getRecordTypeId(String customerType,String recordId){
        CustomerInfoNew ci = new CustomerInfoNew();
        if (customerType=='ExistingStoreLocation') {
            RecordType rt=[Select r.DeveloperName, r.Id, r.Name, r.SobjectType from RecordType r  where r.SobjectType ='Account' and r.Name='Existing Store Location'][0];
            ci.recordTypeId=rt.Id;
            ci.recordTypeName=rt.DeveloperName;
        }else if (customerType=='PotentialStoreLocation') {
            RecordType rt=[Select r.DeveloperName, r.Id, r.Name, r.SobjectType from RecordType r  where r.SobjectType ='Account' and r.Name='Potential Store Location'][0];
            ci.recordTypeId=rt.Id;
            ci.recordTypeName=rt.DeveloperName;
        }
        Account_Address__c aa=[Select a.City__c, a.Country__c, a.Name, a.State__c, a.Address1__c from Account_Address__c a where id =:recordId]; 
        ci.city=aa.City__c;
        ci.country=aa.Country__c;
        ci.state=aa.State__c;
        ci.street=aa.Address1__c;
        ci.name=aa.Name;
        return JSON.serialize(ci);
    }

    public class CustomerInfoNew {
        @AuraEnabled
        public String recordTypeId {get; set;}
        @AuraEnabled
        public String recordTypeName {get; set;}
        @AuraEnabled
        public String city {get; set;}
        @AuraEnabled
        public String country {get; set;}
        @AuraEnabled
        public String name {get; set;}
        @AuraEnabled
        public String state {get; set;}
        @AuraEnabled
        public String street {get; set;}
    }

}