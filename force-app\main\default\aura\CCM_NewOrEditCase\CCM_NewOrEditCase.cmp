<!--
 - Created by ali375 on 2019/5/13.
 -->

<aura:component
        implements="lightning:isUrlAddressable,flexipage:availableForAllPageTypes,lightning:actionOverride,flexipage:availableForRecordHome,force:hasRecordId,lightning:hasPageReference"
        access="global" description="CCM_NewOrEditCase" controller="CCM_CaseController">

    <aura:attribute name="recordTypeId" type="String" default=""/>
    <aura:attribute name="recordType" type="String" default=""/>
    <aura:attribute name="recordId" type="String" default=""/>

    <!--<aura:attribute name="isMobile" type="Boolean" default="false"/>-->
    <aura:attribute name="isEdit" type="Boolean" default="false"/>
    <aura:attribute name="isChannel" type="Boolean" default="false"/>
    <aura:attribute name="isProductNeeded" type="Boolean" default="false"/>
    <aura:attribute name="isWarrantyNeeded" type="Boolean" default="false"/>
    <aura:attribute name="isSpecialTrackingNeeded" type="Boolean" default="false"/>
    <aura:attribute name="specialTrackingOptions" type="List" />
    <aura:attribute name="specialTrackingValue" type="String" />
    <aura:attribute name="productRelatedIssues" type="String" />
    <aura:attribute name="isExternalNoteNeeded" type="Boolean" default="false"/>
    <aura:attribute name="preExternalNote" type="String" />

    <aura:attribute name="caseTypeOptions" type="List" />

    <aura:attribute name="caseObjList" type="List" default="[]"/>
    <aura:attribute name="targetRecord" type="Object" access="private"/>
    <aura:attribute name="targetFields" type="Object" access="private"
                    default="{Case_Type__c:'',Brand_Name__c:'',Warranty__c:''}"/>
    <aura:attribute name="targetFields2" type="Object" access="private"/>
    <aura:attribute name="targetError" type="String" access="private"/>
    <aura:attribute name="targetError2" type="String" access="private"/>
    <aura:attribute name="initInterval" type="Object" access="private"/>
    <aura:attribute name="openTabs" type="List" default=""/>
    <aura:attribute name="focusedTabId" type="String" default=""/>
    <!--for CSR user-->
    <aura:attribute name="isCSR" type="Boolean" default="true"/>
    <aura:attribute name="isSaveClick" type="Boolean" default="false"/>
    <!-- for top issue -->
    <aura:attribute name="topIssueSelected" type="String" default="" />
    <aura:attribute name="topIssueTemplateMap" type="Object" default="{}" />
    <aura:attribute name="topIssueOptions" type="List" default="[]" />
    <aura:attribute name="topIssueTemplate" type="String" default="" />
    <aura:attribute name="showTopIssue" type="Boolean" default="false"/>
    <aura:attribute name="isFromCustomerToNewCase" type="Boolean" default="false" />

    <lightning:workspaceAPI aura:id="workspace"/>

    <aura:attribute name="openTime" type="String" default="" access="private"/>
    <aura:attribute name="brandName" type="String" default="EGO" access="private"/>
    <aura:attribute name="productObj" type="Map" default="{'Name':'', Id:'', Repairable__c:''}"/>
    <aura:attribute name="warrantyItemObj" type="Map" default="{'Name':'', Id:''}"/>
    <aura:attribute name="warrantyNumberObj" type="Map" default="{'Name':'', Id:''}"/>
    <aura:attribute name="customerProjectObj" type="Map" default="{'Name':'', Id:''}"/>
    <aura:attribute name="recallSolution" type="String" default="" access="private"/>
    <aura:attribute name="actualSolution" type="String" default="" access="private"/>
    <aura:attribute name="serialnumber" type="String" default="" access="private"/>
    <aura:attribute name="fedex2" type="String" default="" access="private"/>
    <aura:attribute name="casetype" type="String" default="" access="private"/>

    <aura:attribute name="handletime" type="String" default="" access="private"/>
    <aura:attribute name="representative" type="String" default="" access="private"/>
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <aura:handler name="change" value="{!v.productObj}" action="{!c.getRecommendation}"/>
    <aura:handler event="c:CCM_ProductLookUpChange" name="getProductChange" action="{!c.onProjectCustomerChange}"/>
    <aura:registerEvent name="KR_KnowledgeSearchEvent" type="c:KR_KnowledgeSearchEvent"/>
    <aura:attribute name="returnLabelAndProductValid" type="Boolean" default="true" />
    <aura:attribute name="GenreList" type="List" default="[]" description="Genre Picklist Values"/>
    <aura:attribute name="selectedGenreList" type="List" default="[]" description="Selected Genre Picklist Values"/>
    <aura:attribute name="qaReturnMessage" type="String" default=""/>
    <aura:attribute name="showQAReturnMessage" type="Boolean" default="false" />
    <aura:attribute name="serialNumbers" type="List" default="[]" />
    <aura:attribute name="originalSepcialTrackingValue" type="String" default="" />
    <aura:attribute name="isCanSaveWithSIT" type="Boolean" default="false" />
    <!-- <aura:handler name="change" value="{!v.warrantyNumberObj.Id}" action="{!c.checkSITWithWarranrty}"/> -->

    <force:recordData aura:id="caseRecord"
                      layoutType="FULL"
                      mode="EDIT"
                      targetRecord="{!v.targetRecord}"
                      targetFields="{!v.targetFields}"
                      targetError="{!v.targetError}"
                      recordId="{!v.recordId}"
                      recordUpdated="{!c.onRecordDataUpdate}"
    />
    <force:recordData aura:id="productLoader"
                      recordId="{!v.productObj.Id}"
                      targetFields="{!v.targetFields2}"
                      targetError="{!v.targetError2}"
                      fields="Repairable__c"
                      mode="VIEW"
    />
    <aura:html tag="style">
        .slds-modal__container.safeModal, .slds-modal__container.returnLabelModal{
        width: 50%!important;
        margin-top: 6rem;;
        }
        .slds-modal__container.safeModal .slds-modal__content, .slds-modal__container.returnLabelModal .slds-modal__content{
        height: 8rem;
        align-items: center;
        display: flex;
        }
        .forceBrandBand {
        	width: 100% !important;
        }
    </aura:html> 
    <div class="slds-brand-band slds-brand-band_cover slds-brand-band_narrow_view_two slds-template_default forceBrandBand">
        <lightning:layout aura:id="recordForms" class="firstLayout">
            <lightning:layoutItem padding="around-small" class="inlinePanel oneRecordActionWrapper">
                <lightning:spinner aura:id="spinner" class="slds-hide mainSpinner" alternativeText="Loading" size="medium" variant="brand" />
                <section>
                    <div>
                        <header class="slds-modal__header">
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">
                                {!v.isEdit ? 'Edit Case' : 'New Case'}
                            </h2>
                        </header>
                        <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-1">
                            <lightning:recordEditForm aura:id="recordForm"
                                                      objectApiName="Case" recordId="{!v.recordId}">
                                <article class="slds-card">
                                    <div class="slds-grid">
                                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                                            <div class="slds-media__body">
                                                <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                                      title="Case Information">
                                                            <span>Case Information</span>
                                                 </span>
                                                </h2>
                                            </div>
                                        </header>
                                    </div>
                                    <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">

                                        <!--This section will highlight four field for knowledge Search-->
                                        <aura:if isTrue="{!v.recordType == 'General'}">
                                            <div class = "slds-box">
                                                <lightning:layout multipleRows = "true" >
                                                    <lightning:layoutItem padding="around-small" size= "6">
                                                        <div >
                                                            <aura:if isTrue="{!!v.isChannel}">
                                                                <lightning:inputField class="field-required" aura:id="brand" fieldName="Brand_Name__c" value="{!v.targetFields.Brand_Name__c}" onchange="{!c.productRequiredFieldChange}"/>
                                                                <div aura:id="brand-error-required" class="error-text slds-hide">This field is
                                                                    required</div>
                                                            </aura:if>
                                                            <aura:if isTrue="{!v.isChannel}">
                                                                <lightning:inputField class="field-required" aura:id="brand2" fieldName="Brand_Name2__c" value="{!v.targetFields.Brand_Name__c}"/>
                                                                <div aura:id="brand2-error-required" class="error-text slds-hide">This field is
                                                                    required</div>
                                                            </aura:if>
                                                        </div>
                                                    </lightning:layoutItem>
                                                    <lightning:layoutItem padding="around-small" size= "6">
                                                        <div >
                                                            <aura:if isTrue="{!v.isProductNeeded}"><span class="required">*</span></aura:if>
                                                            <c:CCM_ProductLookup aura:id="product-lookup"
                                                                                 caseType="{!v.targetFields.Case_Type__c}"
                                                                                 brandName="{!v.targetFields.Brand_Name__c}"
                                                                                 warranty="{!v.warrantyNumberObj.Id}"
                                                                                 objName="Case"
                                                                                 fieldName="Product"
                                                                                 selectedValue="{!v.productObj}"/>

                                                        </div>
                                                    </lightning:layoutItem>
                                                    <!-- Top Issue Section -->
                                                    <aura:if isTrue="{!!v.isChannel}">
                                                        <aura:if isTrue="{!v.showTopIssue}">
                                                            <lightning:layoutItem padding="around-small" size="6">
                                                                <lightning:select name="topissue" label="Top Issue" aura:id="topissue" value="{!v.topIssueSelected}" onchange="{!c.handleTopIssueChange}">
                                                                    <aura:iteration items="{!v.topIssueOptions}" var="option">
                                                                        <option text="{!option.label}" value="{!option.value}" selected="{!option.selected}" />
                                                                    </aura:iteration>
                                                                </lightning:select>
                                                            </lightning:layoutItem>
                                                            <lightning:layoutItem padding="around-small" size="12">
                                                                <lightning:textarea name="temlate" label="Template" class="topissuecontent" value="{!v.topIssueTemplate}"/>
                                                                <lightning:button label="Add" title="Add" class="slds-m-top_xx-small" onclick="{!c.addTemplateToDescription}"/>
                                                            </lightning:layoutItem>
                                                        </aura:if>
                                                    </aura:if>
                                                    <aura:if isTrue="{!!v.isChannel}">
                                                        <lightning:layoutItem padding="around-small" size= "12">
                                                            <div >
                                                                <lightning:dualListbox aura:id="related-issue"
                                                                                       class="fieldset-required"
                                                                                       name="Genre"
                                                                                       label="Product Related Issue"
                                                                                       sourceLabel="Available"
                                                                                       selectedLabel="Chosen"
                                                                                       options="{!v.GenreList }"
                                                                                       value="{!v.selectedGenreList}"
                                                                                       onchange="{!c.handleGenreChange}"/>
                                                                <div aura:id="related-issue-error-required"
                                                                     class="error-text slds-hide">This
                                                                    field is required</div>

                                                            </div>
                                                        </lightning:layoutItem>
                                                    </aura:if>
                                                    <lightning:layoutItem padding="around-small" size= "12">
                                                        <div ><span class="required">*</span>
                                                            <lightning:textarea aura:id="Description" class="fieldset-required" label="Description" value="{!v.targetFields.Description}"/>
                                                        	<div aura:id="fieldset-error-required"
                                                                     class="error-text slds-hide">This
                                                                    field is required</div>
                                                        </div>
                                                    </lightning:layoutItem>
                                                    <lightning:layoutItem padding="around-small" size="6">
                                                        <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small checkbox-container">
                                                            <div class="slds-form-element">
                                                                <label class="slds-form-element__label" >
                                                                    Repairable
                                                                </label>
                                                                <div class="slds-form-element__control">
                                                                    <lightning:input type="text" label="Repairable" variant="label-hidden" disabled="true" value="{!v.productObj.Repairable__c}"></lightning:input>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </lightning:layoutItem>
                                                    <lightning:layoutItem padding="around-small" size="6">
                                                        <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small checkbox-container">
                                                            <div class="slds-form-element">
                                                                <label class="slds-form-element__label" >
                                                                    <span style="white-space: nowrap;">Customer was refused service</span>
                                                                    <br></br>
                                                                    <span style="padding-left: 40px;">from provider</span>

                                                                </label>
                                                                <div class="slds-form-element__control">
                                                                     <lightning:input style="padding-left: 65px;" label="Customer was refused service from provider" variant="label-hidden"
                                                                                     type="checkbox"
                                                                                     checked="{!v.targetFields.Customer_Was_Refused_Service_From_Provid__c}"
                                                                                     onclick="{!c.openRefusedServiceWindow}"/>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </lightning:layoutItem>
                                                    <aura:if isTrue="{!v.targetFields.Customer_Was_Refused_Service_From_Provid__c}">
                                                        <lightning:layoutItem padding="around-small" size= "12">
                                                            <div >
                                                                <lightning:textarea aura:id="LeaderComments" label="Leader Comments" value="{!v.targetFields.Leader_Comments__c}"/>
                                                            </div>
                                                        </lightning:layoutItem>
                                                    </aura:if>

                                                    <lightning:layoutItem padding="around-small" size= "6">
                                                        <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small checkbox-container">
                                                            <div class="slds-form-element">
                                                                <label class="slds-form-element__label" >
                                                                    Safely Concern Issue
                                                                </label>
                                                                <!--This section will highlight four field for knowledge Search-->

                                                                <div class="slds-form-element__control">
                                                                    <lightning:input class="safeConcernCheckBox" label="Safely Concern Issue" variant="label-hidden"
                                                                                     type="checkbox"
                                                                                     checked="{!v.targetFields.Recall__c}"
                                                                                     onclick="{!c.openSafeConcernWindow}"/>
                                                                </div>

                                                            </div>

                                                        </div>
                                                    </lightning:layoutItem>
                                                    <lightning:layoutItem size= "12"  class = "slds-text-align_center">
                                                        <lightning:button class="slds-m-top_small" variant="brand"  name="Search" label="Search" onclick = "{!c.searchKnowledge}"  />
                                                    </lightning:layoutItem>
                                                </lightning:layout>
                                            </div>
                                        </aura:if>
                                        <!--Section Ends here-->

                                        <div class="slds-grid slds-wrap">
                                             <aura:if isTrue="{!!v.isChannel}">
                                            <aura:if isTrue="{!v.recordType == 'General'}">

                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <aura:if isTrue="{!(v.isWarrantyNeeded)}"><span class="required">*</span></aura:if>
                                                    <c:CCM_ProductLookup aura:id="warrantyNumber-lookup"
                                                                         contactId="{!v.targetFields.ContactId}"
                                                                         objName="Case"
                                                                         fieldName="Warranty Number"
                                                                         selectedValue="{!v.warrantyNumberObj}"/>
                                                </div>
                                            </aura:if>
                                            </aura:if>
                                            <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                <lightning:inputField aura:id="subject" fieldName="Subject"/>
                                            </div>
                                            <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                <lightning:inputField class="field-required"  aura:id="account-id" fieldName="AccountId" value="{!v.targetFields.AccountId}"/>
                                                <div aura:id="account-id-error-required" class="error-text slds-hide">This
                                                    field is required</div>
                                            </div>
                                            <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label">
                                                        Case Record Type
                                                    </label>
                                                    <div class="slds-form-element__control">
                                                        <input type="text" readonly="true"
                                                               class="slds-input"
                                                               value="{!v.recordType}"/>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                <lightning:inputField aura:id="contact-id" fieldName="ContactId" value="{!v.targetFields.ContactId}"/>
                                            </div>
                                            <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label">Case Owner</label>
                                                    <div class="slds-form-element__control">
                                                        <input type="text" readonly="true"
                                                               class="slds-input"
                                                               value="{!v.targetRecord.fields.Owner.displayValue}"/>
                                                    </div>
                                                </div>
                                            </div>
                                            <aura:if isTrue="{!!v.isChannel}">
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <lightning:inputField class="field-required" aura:id="origin"
                                                                          fieldName="Origin"/>
                                                    <div aura:id="origin-error-required" class="error-text slds-hide">This field is
                                                        required</div>
                                                </div>
                                            </aura:if>

                                            <aura:if isTrue="{!v.recordType == 'General'}">
                                                <aura:if isTrue="{!!v.isChannel}">
                                                    <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                        <!-- <lightning:inputField class="field-required" aura:id="case-type"
                                                                              fieldName="Case_Type__c"
                                                                              onchange="{!c.productRequiredFieldChange}"/> -->
                                                        <lightning:select aura:id="case-type" name="Case Type" label="Case Type" onchange="{!c.productRequiredFieldChange}" required="true">
                                                            <aura:iteration items="{!v.caseTypeOptions}" var="caseTypeOption">
                                                                <option value="{!caseTypeOption.value}" text="{!caseTypeOption.label}" selected="{!caseTypeOption.selected}"></option>
                                                            </aura:iteration>
                                                        </lightning:select>
                                                        <div aura:id="case-type-error-required" class="error-text slds-hide">This
                                                            field is required</div>
                                                    </div>
                                                </aura:if>
                                                <aura:if isTrue="{!v.isChannel}">
                                                    <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                        <lightning:inputField class="field-required" aura:id="case-type2"
                                                                              fieldName="Case_Type2__c"
                                                                              onchange="{!c.productRequiredFieldChange}"/>
                                                        <div aura:id="case-type-error-required" class="error-text slds-hide">This
                                                            field is required</div>
                                                    </div>
                                                </aura:if>
                                            </aura:if>

                                            <!--This section moved to the top highlighted panel for knowledge search-->
                                            <!--aura:if isTrue="{!v.recordType == 'General'}">
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <lightning:inputField class="field-required" aura:id="brand" fieldName="Brand_Name__c" value="{!v.targetFields.Brand_Name__c}" onchange="{!c.productRequiredFieldChange}"/>
                                                    <div aura:id="brand-error-required" class="error-text slds-hide">This field is
                                                        required</div>
                                                </div>
                                            </aura:if-->
                                            <aura:if isTrue="{!!v.isChannel}">
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <lightning:inputField aura:id="priority" fieldName="Priority"/>
                                                </div>
                                            </aura:if>
                                            <!--This section moved to the top highlighted panel for knowledge search-->
                                            <!--aura:if isTrue="{!v.recordType == 'General'}">
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <aura:if isTrue="{!v.isProductNeeded}"><span class="required">*</span></aura:if>
                                                    <c:CCM_ProductLookup aura:id="product-lookup"
                                                                         caseType="{!v.targetFields.Case_Type__c}"
                                                                         brandName="{!v.targetFields.Brand_Name__c}"
                                                                         warranty="{!v.warrantyNumberObj.Id}"
                                                                         objName="Case"
                                                                         fieldName="Product"
                                                                         selectedValue="{!v.productObj}"/>

                                                </div>
                                            </aura:if-->
                                            <aura:if isTrue="{!!v.isChannel}">
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <lightning:inputField class="field-required" aura:id="status"
                                                                          fieldName="Status"/>
                                                    <div aura:id="status-error-required" class="error-text slds-hide">This
                                                        field is required</div>
                                                </div>
                                            </aura:if>
                                            <aura:if isTrue="{!v.isChannel}">
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <lightning:inputField class="field-required" aura:id="status2"
                                                                          fieldName="Status2__c"/>
                                                    <div aura:id="status-error-required" class="error-text slds-hide">This
                                                        field is required</div>
                                                </div>
                                            </aura:if>
                                            <!--This section moved to the top highlighted panel for knowledge search-->
                                            <aura:if isTrue="{!v.recordType == 'Recall'}">
                                               <!-- <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <lightning:textarea label="Description" value="{!v.targetFields.Description}"/>
                                                </div>
                                            </aura:if>
                                            <aura:if isTrue="{!v.recordType == 'Recall'}">-->


                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small"><span class="required">*</span>
                                                    <lightning:textarea aura:id="Description" class="fieldset-required" label="Description" value="{!v.targetFields.Description}"/>
                                                    <div aura:id="fieldset-error-required"
                                                         class="error-text slds-hide">This
                                                        field is required</div>
                                                </div>



                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <aura:if isTrue="{!(v.isWarrantyNeeded)}"><span class="required">*</span></aura:if>
                                                    <c:CCM_ProductLookup aura:id="warrantyNumber-lookup"
                                                                         contactId="{!v.targetFields.ContactId}"
                                                                         objName="Case"
                                                                         fieldName="Warranty Number"
                                                                         selectedValue="{!v.warrantyNumberObj}"/>
                                                </div>
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <!--<lightning:inputField aura:id="warranty-item"-->
                                                    <!--class="field-required"-->
                                                    <!--fieldName="Warranty_Item__c"/>-->
                                                    <span class="required">*</span>
                                                    <c:CCM_ProductLookup aura:id="warrantyItemObj"
                                                                         projectCustomerId="{!v.warrantyNumberObj.Id}"
                                                                         objName="Case"
                                                                         fieldName="Warranty Item"
                                                                         selectedValue="{!v.warrantyItemObj}"/>
                                                    <div aura:id="warrantyItemObj-error-required" class="error-text slds-hide">This
                                                        field is required</div>
                                                </div>

                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <!--<lightning:inputField aura:id="project-customer"-->
                                                    <!--fieldName="Project_Customer__c"-->
                                                    <!--class="field-required"-->
                                                    <!--onchange="{!c.onProjectCustomerChange}"/>-->
                                                    <span class="required">*</span>
                                                    <c:CCM_ProductLookup aura:id="customerProjectObj"
                                                                         contactIdForProject="{!v.warrantyItemObj.Id}"
                                                                         objName="Case"
                                                                         fieldName="Customer Project"
                                                                         selectedValue="{!v.customerProjectObj}"/>
                                                    <div aura:id="customerProjectObj-error-required" class="error-text slds-hide">This
                                                        field is required</div>
                                                </div>

                                            </aura:if>


                                        </div>
                                    </div>
                                </article>
                                <aura:if isTrue="{!v.recordType == 'Recall'}">
                                    <article class="slds-card">
                                        <div class="slds-grid">
                                            <header class="slds-media slds-media_center slds-has-flexi-truncate">
                                                <div class="slds-media__body">
                                                    <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                                      title="Recall Information">
                                                            <span>Recall Information</span>
                                                 </span>
                                                    </h2>
                                                </div>
                                            </header>
                                        </div>
                                        <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">

                                            <div class="slds-grid slds-wrap">

                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <div class="slds-form-element">
                                                        <label class="slds-form-element__label">
                                                            Solution
                                                        </label>
                                                        <div class="slds-form-element__control">
                                                            <input type="text" readonly="true"
                                                                   class="slds-input"
                                                                   value="{!v.recallSolution}"/>
                                                        </div>
                                                    </div>
                                                    <lightning:inputField class="slds-hide" aura:id="recallSolution" fieldName="Recall_Solution__c" value="{!v.targetFields.Recall_Solution__c}"/>
                                                </div>
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">

                                                    <lightning:inputField aura:id="actual-solution" class="field-required"
                                                                          fieldName="Actual_Solution__c" value="{!v.actualSolution}" onchange="{!c.actualSolutionChange}"/>
                                                    <div aura:id="actual-solution-error-required" class="error-text slds-hide">This
                                                        field is required</div>

                                                </div>
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <aura:if isTrue="{!v.isCSR}">
                                                        <!--<lightning:outputField aura:id="recall-status" fieldName="Recall_Status__c"/>-->
                                                        <lightning:input label="Recall Status" aura:id="recall-status" value="{!v.targetFields.Recall_Status__c}" readonly="true"/>
                                                        <aura:set attribute="else">
                                                            <lightning:inputField aura:id="recall-status" fieldName="Recall_Status__c" value="{!v.targetFields.Recall_Status__c}"/>
                                                        </aura:set>
                                                    </aura:if>
                                                    <!--<lightning:inputField aura:id="recall-status" fieldName="Recall_Status__c"/>-->
                                                </div>
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <lightning:inputField aura:id="return-label-number" fieldName="Return_Lable_No__c"/>
                                                </div>
                                            </div>

                                        </div>

                                    </article>
                                </aura:if>
                                <article class="slds-card">
                                    <div class="slds-grid">
                                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                                            <div class="slds-media__body">
                                                <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                                      title="Case Information">
                                                            <span>Additional Information</span>
                                                 </span>
                                                </h2>
                                            </div>
                                        </header>
                                    </div>
                                    <aura:if isTrue="{!!v.isChannel}">
                                        <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">

                                            <div class="slds-grid slds-wrap">
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-1 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <div class="slds-form-element">
                                                        <label class="slds-form-element__label">
                                                            Open Time
                                                        </label>
                                                        <div class="slds-form-element__control">
                                                            <input type="text" readonly="true"
                                                                   class="slds-input" aura:id="open-time"
                                                                   value="{!v.openTime}"/>
                                                        </div>
                                                    </div>
                                                    <div aura:id="open-time-error-required" class="error-text slds-hide">This
                                                        field is required</div>
                                                </div>
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <lightning:inputField aura:id="country" fieldName="Country__c"/>
                                                </div>
                                                <aura:if isTrue="{!v.recordType == 'General'}">
                                                    <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    	<aura:if isTrue="{!(v.isSpecialTrackingNeeded)}"><span class="required">*</span></aura:if>
                                                        <!-- <lightning:inputField aura:id="special-tracking"
                                                                              fieldName="Special_Issue_Tracking__c"/> -->
                                                        <lightning:select aura:id="special-tracking" name="Special Issue Tracking" label="Special Issue Tracking" onchange="{!c.checkSITWithWarranrty}">
                                                            <aura:iteration items="{!v.specialTrackingOptions}" var="trackingOption">
                                                                <option value="{!trackingOption.value}" text="{!trackingOption.label}" selected="{!trackingOption.selected}"></option>
                                                            </aura:iteration>
                                                        </lightning:select>
                                                        <div aura:id="special-tracking-error-required"
                                                                     class="error-text slds-hide">This
                                                                    field is required when Case Type is Warranty Order</div>
                                                    </div>

                                                    <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                        <lightning:inputField aura:id="return-label"
                                                                              fieldName="Return_Label__c" onchange="{!c.checkProductReturnLabel}"/>
                                                    </div>
                                                </aura:if>
                                                <!--<aura:if isTrue="{!v.isEdit}">-->
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <!--<aura:if isTrue="{!v.isCSR}">-->
                                                    <!--<lightning:outputField aura:id="repaired-tracking-number" fieldName="Repaired_Tracking_No__c"/>-->
                                                    <!--<aura:set attribute="else">-->
                                                    <lightning:outputField aura:id="repaired-tracking-number" fieldName="Repaired_Tracking_No__c"/>
                                                    <!--</aura:set>-->
                                                    <!--</aura:if>-->
                                                    <!--<lightning:inputField aura:id="repaired-tracking-number" fieldName="Repaired_Tracking_No__c"/>-->
                                                </div>
                                                <!--</aura:if>-->
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
    <!--                                                <lightning:inputField aura:id="FED_EX__c" fieldName="Fedex_Link__c"/>-->
                                                    <lightning:input name="" label="Return Tracking #" aura:id="FED_EX__c"/>
                                                </div>
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <aura:if isTrue="{!v.isCSR}">
                                                        <div class="slds-form-element">
                                                            <label class="slds-form-element__label">
                                                                Escalated
                                                            </label>
                                                            <div class="slds-form-element__control">
                                                                <lightning:input label="Escalated"
                                                                                 type="checkbox"
                                                                                 variant="label-hidden"
                                                                                 checked="{!v.targetFields.IsEscalated}"/>
                                                            </div>
                                                        </div>
                                                        <aura:set attribute="else">
                                                            <div class="slds-form-element">
                                                                <label class="slds-form-element__label">
                                                                    Escalation Resolved
                                                                </label>
                                                                <div class="slds-form-element__control">
                                                                    <lightning:input label="Escalation Resolved"
                                                                                     type="checkbox"
                                                                                     variant="label-hidden"
                                                                                     checked="{!v.targetFields.Escalation_Resolved__c}"/>
                                                                </div>
                                                            </div>
                                                        </aura:set>
                                                    </aura:if>
                                                </div>
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <lightning:outputField aura:id="Partner_Locator__c" fieldName="Partner_Locator__c" />
                                                </div>
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <lightning:inputField aura:id="AttachmentURL__c" fieldName="AttachmentURL__c" value="{!v.targetFields.AttachmentURL__c}"/>
                                                </div>

                                                <!--This section moved to the top highlighted panel for knowledge search-->
                                                <!--aura:if isTrue="{!v.recordType == 'General'}">
                                                    <div class="slds-size_1-of-1 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                        <lightning:inputField aura:id="related-issue" class="fieldset-required"
                                                                              fieldName="Product_Related_Issue__c"/>
                                                        <div aura:id="related-issue-error-required"
                                                             class="error-text slds-hide">This
                                                            field is required</div>

                                                    </div>
                                                </aura:if-->
                                            </div>

                                        </div>
                                    </aura:if>
                                    <aura:if isTrue="{!v.isChannel}">
                                        <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                                            <div class="slds-grid slds-wrap">
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <lightning:input name="" label="Return Tracking #" aura:id="FED_EX2__c" value="{!v.fedex2}"/>
                                                </div>
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <lightning:input name="" label="Serial Number " aura:id="Serial_Number__c" value="{!v.serialnumber}"/>
                                                </div>
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <lightning:input name="" label="Handle time" aura:id="Handle_Time__c"  value="{!v.handletime}"/>
                                                </div>
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <lightning:input name="" label="Representative" aura:id="Representative__c" value="{!v.representative}"/>
                                                </div>
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <aura:if isTrue="{!v.isCSR}">
                                                        <div class="slds-form-element">
                                                            <label class="slds-form-element__label">
                                                                Escalated
                                                            </label>
                                                            <div class="slds-form-element__control">
                                                                <lightning:input label="Escalated"
                                                                                 type="checkbox"
                                                                                 variant="label-hidden"
                                                                                 checked="{!v.targetFields.IsEscalated}"/>
                                                            </div>
                                                        </div>
                                                        <aura:set attribute="else">
                                                            <div class="slds-form-element">
                                                                <label class="slds-form-element__label">
                                                                    Escalation Resolved
                                                                </label>
                                                                <div class="slds-form-element__control">
                                                                    <lightning:input label="Escalation Resolved"
                                                                                     type="checkbox"
                                                                                     variant="label-hidden"
                                                                                     checked="{!v.targetFields.Escalation_Resolved__c}"/>
                                                                </div>
                                                            </div>
                                                        </aura:set>
                                                    </aura:if>
                                                </div>
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                    <lightning:outputField aura:id="Partner_Locator__c" fieldName="Partner_Locator__c" />
                                                </div>
                                            </div>

                                        </div>

                                    </aura:if>
                                </article>
                                <aura:if isTrue="{!or(v.targetFields.Case_Type__c == 'Service Referral',v.recordType == 'Recall')}">
                                    <div class="slds-card">
                                        <div class="slds-grid">
                                            <header class="slds-media slds-media_center slds-has-flexi-truncate">
                                                <div class="slds-media__body">
                                                    <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                                        <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                                              title="External Note">
                                                                    <span>External Note</span>
                                                         </span>
                                                    </h2>
                                                </div>
                                            </header>
                                        </div>
                                        <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">

                                            <div class="slds-grid slds-wrap">
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-1 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small recommendation">
                                                    <aura:if isTrue="{!(v.isExternalNoteNeeded)}"><span class="required">*</span></aura:if>
                                                    <lightning:inputField aura:id='external-note' fieldName="External_Note__c" />
                                                </div>
                                                <div aura:id="external-note-error-required"
                                                                     class="error-text slds-hide">This
                                                                    field is required when Case Type is Service Referral</div>
                                            </div>
                                        </div>
                                    </div>
                                </aura:if>

                                <aura:if isTrue="{!v.recordType == 'General'}">
                                    <article class="slds-card">
                                        <div class="slds-grid">
                                            <header class="slds-media slds-media_center slds-has-flexi-truncate">
                                                <div class="slds-media__body">
                                                    <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                                      title="Recommendation">
                                                            <span>Recommendation</span>
                                                 </span>
                                                    </h2>
                                                </div>
                                            </header>
                                        </div>
                                        <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">

                                            <div class="slds-grid slds-wrap">
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-1 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small recommendation">
                                                    <lightning:inputField fieldName="Recommendation__c" disabled="true" value="{!v.targetFields.Recommendation__c}"/>
                                                </div>
                                            </div>

                                        </div>
                                    </article>
                                </aura:if>
                                <aura:if isTrue="{!v.qaReturnMessage != ''}">
                                    <article class="slds-card">
                                        <div class="slds-grid">
                                            <header class="slds-media slds-media_center slds-has-flexi-truncate">
                                                <div class="slds-media__body">
                                                    <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                                    title="Recommendation">
                                                            <span>QA Return Recommendation</span>
                                                </span>
                                                    </h2>
                                                </div>
                                            </header>
                                        </div>
                                        <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">

                                            <div class="slds-grid slds-wrap">
                                                <div class="slds-size_1-of-1 slds-medium-size_1-of-1 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small recommendation">
                                                    <lightning:formattedRichText value="{!v.qaReturnMessage}" />
                                                </div>
                                            </div>

                                        </div>
                                    </article>
                                </aura:if>
                                <article class="slds-card">
                                    <div class="slds-grid">
                                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                                            <div class="slds-media__body">
                                                <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                                      title="Recommendation">
                                                            <span>Optional</span>
                                                 </span>
                                                </h2>
                                            </div>
                                        </header>
                                    </div>
                                    <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">

                                        <div class="slds-grid slds-wrap">
                                            <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small">
                                                <div class="slds-form-element checkbox-container">
                                                    <label class="slds-form-element__label">
                                                        Assign using active assignment rules
                                                    </label>
                                                    <div class="slds-form-element__control">
                                                        <lightning:input label="dsds"
                                                                         variant="label-hidden"
                                                                         type="checkbox"/>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            </lightning:recordEditForm>
                        </div>

                    </div>
                </section>
                <footer class="{!(v.isEdit ? 'slds-modal__footer caseContent': 'slds-modal__footer caseContent')}">
                    <button class="slds-button slds-button_neutral" onclick="{!c.onClickCancel}">Cancel</button>
                    <button class="slds-button slds-button_brand" disabled="{!v.isSaveClick}" onclick="{!c.onClickSave}">Save</button>
                </footer>
            </lightning:layoutItem>
            <aura:if isTrue="{!v.recordType == 'General'}">
                <lightning:layoutItem padding="around-small" size = "12" class="inlinePanel oneRecordActionWrapper slds-m-left_medium">
                    <c:KR_KnowledgePanel />
                </lightning:layoutItem>
            </aura:if>
        </lightning:layout>
        <!--<div class="slds-backdrop slds-backdrop_open"></div>-->
        <lightning:layout aura:id="checkModal" class="slds-hide">
            <lightning:layoutItem padding="around-small">
                <div style="height:640px">
                    <section role="dialog" tabindex="-1" aria-label="Meaningful description of the modal content" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                        <div class="slds-modal__container safeModal">
                            <header class="slds-modal__header">
                                <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse"
                                        title="Close" onclick="{!c.closeSafeConcernWindow}">
                                    <lightning:icon alternativeText="close" iconName="utility:close" size="small" variant="bare"/>
                                </button>
                                <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">Confirm</h2>
                            </header>
                            <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-1">
                                <p class="slds-align_absolute-center">Are you sure to change safely concern issue status of this case? </p>
                            </div>
                            <footer class="slds-modal__footer">
                                <button class="slds-button slds-button_neutral" onclick="{!c.closeSafeConcernWindow}">No</button>
                                <button class="slds-button slds-button_brand" onclick="{!c.confirmSafeConcernWindow}">Yes</button>
                            </footer>
                        </div>
                    </section>
                </div>
            </lightning:layoutItem>
        </lightning:layout>
        <div style="height:640px" class="slds-hide" aura:id="returnLabelModal">
            <section role="dialog" tabindex="-1" aria-label="Meaningful description of the modal content" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                <div class="slds-modal__container returnLabelModal">
                    <header class="slds-modal__header">
                        <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse"
                                title="Close" onclick="{!c.closeReturnLabelWindow}">
                            <lightning:icon alternativeText="close" iconName="utility:close" size="small" variant="bare"/>
                        </button>
                        <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">Confirm</h2>
                    </header>
                    <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-1">
                        <p class="slds-align_absolute-center">can not save the case because the product is a battery.</p>
                    </div>
                    <footer class="slds-modal__footer">
                        <button class="slds-button slds-button_neutral" onclick="{!c.closeReturnLabelWindow}">No</button>
                        <button class="slds-button slds-button_brand" onclick="{!c.confirmReturnLabelWindow}">Yes</button>
                    </footer>
                </div>
            </section>
        </div>
    </div>

    <!-- <c:CCM_Modal title="QA Return Reminder" isShow="{!v.showQAReturnMessage}">
        <lightning:formattedRichText value="{!v.qaReturnMessage}" />
    </c:CCM_Modal> -->
</aura:component>