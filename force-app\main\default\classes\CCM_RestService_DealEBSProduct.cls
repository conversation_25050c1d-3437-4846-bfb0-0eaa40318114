/**********************************************************************
 *
 *
 * @url: /services/apexrest/CCM_RestService_DealEBSProduct
 * @data:
 * [{
    "DESCRIPTION":"BA1400T 56V G3 2.5Ah Battery",
    "INVENTORY_ITEM_ID":"1201785.0",
    "MODEL_NUMBER":"BA1400T",
    "SEGMENT1":"400152001",
    "BRAND":"EGO",
    "PRIMARY_UOM_CODE":"EA",
    "INVENTORY_ITEM_STATUS_CODE":"Active",
    "CREATION_DATE":"2018-10-26 01:28:22",
    "LAST_UPDATE_DATE":"2020-07-07 06:55:51",
    "ITEM_TYPE":"配件",
    "DIMENSION_UOM_CODE":"ln",
    "UNIT_HEIGHT":"6.1",
    "UNIT_LENGTH":"7.52",
    "UNIT_WIDTH":"3.15",
    "UNIT_WEIGHT":"3.75",
    "WEIGHT_UOM_CODE":"lbs",
	"PRODUCT_CATEGROY":""
 }]
 *
 *
 * Test Class: CCM_RestService_DealEBSProductTest
*************************************************************************/
@RestResource(urlMapping='/CCM_RestService_DealEBSProduct')
global with sharing class CCM_RestService_DealEBSProduct {
    @HttpPost
    global static ResultObj doPost() {
        RestRequest req = RestContext.request;
        List<ReqestObj> reqObjList = new List<ReqestObj>();
        ResultObj resObj = new ResultObj();
        try{
            String resStr = req.requestBody.toString();
            if(!resStr.startsWith('[')){
                resStr = '[' + resStr + ']';
            }
            reqObjList = parse(resStr);
            resObj.Process_Result = new List<ReturnItem>();
            System.debug(LoggingLevel.INFO, '*** req.requestBody(): ' + reqObjList);

            Id partsType = CCM_Constants.PRODUCT_PARTS_RECORD_TYPE_ID;
            Id productType = CCM_Constants.PRODUCT_PRODUCT_RECORD_TYPE_ID;
            Id marketingType = CCM_Constants.PRODUCT_ORACLE_MERCHANDISING_RECORD_TYPE_ID;
            List<Product2> prodList = new List<Product2>();

            if (reqObjList != null && reqObjList.size() > 0) {
                for (ReqestObj reqObj : reqObjList) {
                    Boolean needCreateForEBS = false;
                    Product2 product = new Product2();
                    product.Name = reqObj.DESCRIPTION;
                    product.Description = reqObj.DESCRIPTION;
                    if(reqObj.Attribute1 == 'CNA'){
                        product.Country_of_Origin__c = 'United States';
                    }else if(reqObj.Attribute1 == 'CCA'){
                        product.Country_of_Origin__c = 'Canada';
                    }
                    product.EBS_ExternalID__c = String.isNotBlank(reqObj.INVENTORY_ITEM_ID) ? Decimal.valueOf(reqObj.INVENTORY_ITEM_ID)+product.Country_of_Origin__c : null;
                    product.ExternalID__c = String.isNotBlank(reqObj.INVENTORY_ITEM_ID) ? Decimal.valueOf(reqObj.INVENTORY_ITEM_ID) : null;
                    product.ProductCode = String.isNotBlank(reqObj.MODEL_NUMBER) ? reqObj.MODEL_NUMBER : reqObj.SEGMENT1;
                    product.Item_Number__c = reqObj.SEGMENT1;
                    product.Brand_Name__c = reqObj.BRAND;
                    product.UOM__c = reqObj.PRIMARY_UOM_CODE;
                    product.IsActive = reqObj.INVENTORY_ITEM_STATUS_CODE != 'Inactive' ? true : false;
                    product.EBS_Status__c = reqObj.INVENTORY_ITEM_STATUS_CODE;
                    product.Materail_ERP_created_time__c = String.isNotBlank(reqObj.CREATION_DATE) ? Datetime.valueOf(reqObj.CREATION_DATE) : null;
                    product.Material_ERP_last_modified_time__c = String.isNotBlank(reqObj.CREATION_DATE) ? Datetime.valueOf(reqObj.LAST_UPDATE_DATE) : null;
                    product.IsParts__c = reqObj.ITEM_TYPE == '配件' ? true : false;
                    product.Source__c = 'EBS';
                    product.Brand_Series__c = String.isNotBlank(reqObj.Attribute2) ? reqObj.Attribute2 : null;
                    product.Lanch_Date__c = String.isNotBlank(reqObj.Attribute3) ? Date.valueOf(reqObj.Attribute3) : null;
                    if (reqObj.ITEM_TYPE == '配件') {
                        if (reqObj.Attribute1 == 'CCA') {
                            product.ProductCode = reqObj.SEGMENT1;
                        }
                        product.RecordTypeId = partsType;
                    } else if (reqObj.ITEM_TYPE == 'Marketing') {
                        product.RecordTypeId = marketingType;
                        product.Source__c = 'PIM'; // to make sure merchandising products can be added into pricebook
                        needCreateForEBS = true;
                    } else {
                        product.RecordTypeId = productType;
                    }
                    product.Dimension_unit__c = reqObj.DIMENSION_UOM_CODE;
                    product.Height__c = String.isNotBlank(reqObj.UNIT_HEIGHT) ? Decimal.valueOf(reqObj.UNIT_HEIGHT) : null;
                    product.Length__c = String.isNotBlank(reqObj.UNIT_LENGTH) ? Decimal.valueOf(reqObj.UNIT_LENGTH) : null;
                    product.Width__c = String.isNotBlank(reqObj.UNIT_WIDTH) ? Decimal.valueOf(reqObj.UNIT_WIDTH) : null;
                    product.Weight__c = String.isNotBlank(reqObj.UNIT_WEIGHT) ? Decimal.valueOf(reqObj.UNIT_WEIGHT) : null;
                    product.Weight_unit__c = reqObj.WEIGHT_UOM_CODE;
                    product.Category_2__c = reqObj.PRODUCT_CATEGROY;

                    product.EBS_Category_1__c = reqObj.CATE_CODE1;
                    product.EBS_Category_2__c = reqObj.CATE_CODE2;
                    product.EBS_Category_3__c = reqObj.CATE_CODE3;
                    product.EBS_Description_of_Category_1__c = reqObj.CATE1;
                    product.EBS_Description_of_Category_2__c = reqObj.CATE2;
                    product.EBS_Description_of_Category_3__c = reqObj.CATE3;
                    product.Is_History_Product__c = CCM_Constants.blHistoryProduct;

                    if (reqObj.WEIGHT_UOM_CODE == 'Kg') {
                        product.Product_Weight__c = product.Weight__c * 2.205;
                    } else if (reqObj.WEIGHT_UOM_CODE == 'g') {
                        product.Product_Weight__c = product.Weight__c * 2.205/1000;
                    } else if (reqObj.WEIGHT_UOM_CODE == 'LBS') {
                        product.Product_Weight__c = product.Weight__c;
                    }
                    if (
                        product.ProductCode.startsWith('CS-BA') || product.ProductCode.startsWith('BA') || product.ProductCode.startsWith('BY') || product.ProductCode.startsWith('SPTH15') ||
                        (product.Brand_Name__c == 'Flex' && product.Description?.toLowerCase().contains('battery') == true && product.Description?.toLowerCase().contains('charger') == false)
                    ) {
                        product.Type__c = 'Battery';
                    } else if (
                        product.ProductCode.startsWith('CS-CH') || product.ProductCode.startsWith('CH') || product.ProductCode.startsWith('QC') || product.ProductCode.startsWith('SC') || product.ProductCode.startsWith('SPTH14') ||
                        (product.Brand_Name__c == 'Flex' && product.Description?.toLowerCase().contains('charger') == true)
                    ) {
                        product.Type__c = 'Charger';
                    } else {
                        product.Type__c = 'Product';
                    }

                    if(needCreateForEBS) {
                        Product2 productForEBS = product.clone(false, true);
                        productForEBS.Source__c = 'EBS';
                        if(String.isNotBlank(productForEBS.EBS_ExternalID__c)) {
                            productForEBS.EBS_ExternalID__c = productForEBS.EBS_ExternalID__c + '-For EBS';
                        }
                        prodList.add(productForEBS);
                    }
                    prodList.add(product);
                }
                // 上线后，修改这个label为True的值
                if (Label.IS_ORM_EBS_PRODUCT.equals('True')) {
                    Database.UpsertResult[] resList = Database.upsert(prodList,Product2.EBS_ExternalID__c.getDescribe().getSObjectField(),false);
                    for (Integer i = 0 ; i < resList.size() ; i++) {
                        if (!resList.get(i).isSuccess()) {
                            Database.Error[] err = resList.get(i).getErrors();
                            ReturnItem request = new ReturnItem();
                            request.External_Id = reqObjList.get(i).INVENTORY_ITEM_ID;
                            request.Error_Message = 'This Product was failed saving in Salesforce';
                            request.Error_Detail = '***The following error has occurred***' + err.get(0).getStatusCode() + ' : ' +err.get(0).getMessage();
                            resObj.Process_Result.add(request);
                        }
                    }
                }

                if (resObj.Process_Result.size() == 0) {
                    resObj.Process_Status = 'Success';
                }else {
                    resObj.Process_Status = 'Fail';
                    String logId = Util.logIntegration('Product Exception','CCM_RestService_DealEBSProduct','POST',JSON.serialize(resObj.Process_Result),JSON.serialize(reqObjList), JSON.serialize(resObj));
                    Util.pushExceptionEmail('Accept Product Info',logId,getMailErrorMessage(resObj));
                }

                System.debug(LoggingLevel.INFO, '*** : ' + JSON.serialize(resObj));
            }else{
                resObj.Process_Status = 'Fail';
                ReturnItem empty = new ReturnItem();
                empty.Error_Message = 'Empty JSON';
                empty.Error_Detail = 'Empty JSON';
                resObj.Process_Result.add(empty);

                String logId = Util.logIntegration('Product Exception','CCM_RestService_DealEBSProduct','POST',JSON.serialize(resObj.Process_Result),JSON.serialize(reqObjList), JSON.serialize(resObj));
                Util.pushExceptionEmail('Accept Product Info',logId,getMailErrorMessage(resObj));
            }
        }catch (Exception e) {
            resObj.Process_Status = 'Fail';
            for (ReqestObj reqObj : reqObjList) {
                ReturnItem request = new ReturnItem();
                request.External_Id = reqObj.INVENTORY_ITEM_ID;
                request.Error_Message = 'This Product was failed saving in Salesforce';
                request.Error_Detail = '*** e.getMessage(): '+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage();
                resObj.Process_Result.add(request);
            }
            System.debug(LoggingLevel.INFO, '*** e.getMessage(): '+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage());
            String logId = Util.logIntegration('Product Exception','CCM_RestService_DealEBSProduct','POST',
                                               JSON.serialize(resObj.Process_Result),JSON.serialize(reqObjList), JSON.serialize(resObj));
            Util.pushExceptionEmail('Accept Product Info',logId,getMailErrorMessage(resObj));
            return resObj;
        }
        if(Label.CCM_needlog == 'Y'){
           Util.logIntegration('ProductInfo log','CCM_RestService_DealEBSProduct', 'POST',
                               '',JSON.serialize(reqObjList), JSON.serialize(resObj));
        }
        System.debug(LoggingLevel.INFO, '*** resObj: ' + resObj);
        return resObj;
    }

    public static String getMailErrorMessage(ResultObj res){
        String errContent = '';
       	errContent += 'Process Status : Fail<br/><br/>';
        if(res.Process_Result.size() > 0){
            for(ReturnItem Item : res.Process_Result){
            	errContent += 'External ID : ' + Item.External_Id + '<br/>';
            	errContent += 'Error Message : ' + Item.Error_Message +'<br/>';
            	errContent += 'Error Detail : '+ Item.Error_Detail +'<br/><br/>';
            }
        }
        return errContent;
    }

    global class ReqestObj {
		global String DESCRIPTION;
		global String INVENTORY_ITEM_ID;
		global String MODEL_NUMBER;
		global String SEGMENT1;
		global String BRAND;
		global String PRIMARY_UOM_CODE;
		global String INVENTORY_ITEM_STATUS_CODE;
		global String CREATION_DATE;
		global String LAST_UPDATE_DATE;
		global String ITEM_TYPE;
		global String DIMENSION_UOM_CODE;
		global String UNIT_HEIGHT;
		global String UNIT_LENGTH;
		global String UNIT_WIDTH;
		global String UNIT_WEIGHT;
        global String WEIGHT_UOM_CODE;
        global String PRODUCT_CATEGROY;
        global String CATE_CODE1;
        global String CATE_CODE2;
        global String CATE_CODE3;
        global String CATE1;
        global String CATE2;
        global String CATE3;
        global String Attribute1;
        global String Attribute2;
        global String Attribute3;
        global String Attribute4;
        global String Attribute5;
        global String Attribute6;
        global String Attribute7;
        global String Attribute8;
        global String Attribute9;
        global String Attribute10;

    }

	global static List<ReqestObj> parse(String jsonStr) {
		return (List<ReqestObj>) JSON.deserialize(jsonStr, List<ReqestObj>.class);
    }

    global class ResultObj {
        global String Process_Status;
        global List<ReturnItem> Process_Result;
    }

    global class ReturnItem {
        global String External_Id;
        global String Error_Message;
        global String Error_Detail;
    }
}