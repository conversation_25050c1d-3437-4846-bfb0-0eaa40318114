<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>false</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <type>creator</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Administrator</submitter>
        <type>group</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Sales_Operation</submitter>
        <type>role</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Owner</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Warranty_Return_Claim__c.Notes__c</field>
                <operation>equals</operation>
                <value>This step is for future expansion 1</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Alternate step</label>
        <name>Alternate_step</name>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Inside_Sales__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <booleanFilter>1 OR 2 OR 3</booleanFilter>
            <criteriaItems>
                <field>Warranty_Return_Claim__c.IsPortal__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <criteriaItems>
                <field>User.Profile</field>
                <operation>equals</operation>
                <value>Sales Agency</value>
            </criteriaItems>
            <criteriaItems>
                <field>User.Profile</field>
                <operation>equals</operation>
                <value>CCA Sales Agency</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Inside Sales Approval</label>
        <name>Inside_Sales_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <label>NA OPS Approval</label>
        <name>NA_OPS_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Warranty_Return_Claim__c.Need_Sales_Approval__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>ApproveRecord</ifCriteriaNotMet>
        <label>Sales Approval</label>
        <name>Sales_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Warranty_Return_Claim__c.Notes__c</field>
                <operation>equals</operation>
                <value>This step is used for future expansion 2</value>
            </criteriaItems>
        </entryCriteria>
        <label>Alternate step 2</label>
        <name>Alternate_step_2</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>Warranty_Return_Claim__c.Approval_Status__c</field>
            <operation>equals</operation>
            <value>N/A,Rejected,Recalled</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Update_Approval_Status</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Update_Request_Status_When_Approved</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>true</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Update_Approval_Status_When_Rejected</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Update_Request_Status_When_Recalled_v1</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Update_Approval_Status_When_Submitted</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Update_Request_Status</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>Warranty Return Claim With portal</label>
    <processOrder>1</processOrder>
    <recallActions>
        <action>
            <name>Update_Approval_Status_When_Recalled</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Update_Request_Status_When_Recalled_v2</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
