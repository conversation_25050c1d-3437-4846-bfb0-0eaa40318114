<template>
  <template if:true={isSellInActive}>
    <div class="slds-grid slds-container_center width90">
      <div class="slds-col slds-size_3-of-12">
        <ul class="type">
          <li class="active slds-m-bottom_small">{label.CCM_Portal_SellinPromotion}</li>
          <li class="slds-m-bottom_small" onclick={handleActiveSellThrough} if:true={is2ndTierDealer}>
            {label.CCM_Portal_SellthroughPOSCredit}
          </li>
        </ul>
      </div>
      <div class="slds-col slds-size_9-of-12">
        <div class="slds-grid">
          <div class="slds-col slds-size_1-of-4 slds-p-right_xx-large">
            <lightning-input
              class="promoCode"
              type="text"
              value=""
              label={label.CCM_Portal_PromotionCode}
            ></lightning-input>
          </div>
          <div class="slds-col slds-size_1-of-4 slds-p-right_xx-large">
            <lightning-input
              class="product"
              type="text"
              value=""
              label={label.CCM_Portal_Product}
            ></lightning-input>
          </div>
          <div class="slds-col slds-size_1-of-4 slds-p-right_xx-large">
            <lightning-combobox
              class="typeforexternal"
              name="typeforexternal"
              label={label.CCM_Portal_PromotionType}
              value=""
              options={optionsSellIn}
            >
            </lightning-combobox>
          </div>
          <div class="slds-col slds-size_1-of-4">
            <div class="btn-search-wrap">
              <lightning-button
                class=""
                variant="brand"
                label={label.CCM_Portal_Search}
                title={label.CCM_Portal_Search}
                onclick={handleClickSearch}
              >
              </lightning-button>
            </div>
          </div>
        </div>
        <template if:false={isSearch}>
          <div>
            <h2
              class="slds-text-heading_medium slds-text-align_center slds-m-top_large slds-m-bottom_medium"
            >
              {label.CCM_Portal_FeaturedPromotions}
            </h2>
            <template if:true={bigSalesSellIn.length}>
              <div class="carousel-wrap">
                <lightning-carousel>
                  <template for:each={bigSalesSellIn} for:item="promotion">
                    <c-ccm-community-promotion-banner-image
                      key={promotion.promotionId}
                      promotion-id={promotion.promotionId}
                      promotion-code={promotion.promotionCode}
                      promotion-window-id={promotion.promotionWindowId}
                      href="#"
                      src={promotion.bannerImgUrl}
                      alternative-text={promotion.promotionName}
                    >
                    </c-ccm-community-promotion-banner-image>
                  </template>
                </lightning-carousel>
              </div>
            </template>
            <template if:false={bigSalesSellIn.length}>
              <div class="slds-text-align_center">{noDataStr}</div>
            </template>
          </div>

          <div class="brand-wrap">
            <!-- SKIL -->
            <temeplate if:true={isShowSkil}>
            <div class="brand-skil">
              <h2
                class="slds-text-heading_medium slds-text-align_center slds-m-top--x-large brandBg"
                style={skilBg}
              >
                SKIL
              </h2>
              <template if:true={skil.length}>
                <div class="brand-border">
                  <!-- Swiper -->
                  <div class="swiper-container swiper-container2">
                    <div class="swiper-wrapper">
                      <template for:each={skil} for:item="promotion">
                        <div key={promotion.promotionId} class="swiper-slide">
                          <div class="slds-size_full">
                            <h3
                              class="slds-text-title slds-text-align_left slds-text-color_default title"
                              title={promotion.promotionName}
                            >
                              {promotion.promotionName}
                            </h3>
                            <div class="promotion-wrap" style={promotion.bg}>
                              <template if:true={promotion.offeringName}>
                                <span>{promotion.offeringName}</span>
                              </template>
                              <!-- <template if:true={promotion.photoUrl}>
                                                                <img src={promotion.photoUrl} />
                                                            </template>
                                                            <template if:false={promotion.photoUrl}>
                                                                <img src={defalutImgUrl} />
                                                            </template> -->
                            </div>
                            <div
                              class="window slds-grid slds-grid_align-spread slds-wrap"
                            >
                              <span
                                >{label.CCM_Portal_Expiresin}&nbsp;
                                <span class="slds-text-color_destructive"
                                  >{promotion.expireDays}</span
                                >
                                &nbsp;{label.CCM_Portal_Days}</span
                              >
                              <span>{promotion.promotionCode}</span>
                            </div>
                            <div class="buttons">
                              <lightning-button
                                variant="neutral"
                                label={label.CCM_Portal_VIEWDETAIL}
                                title={label.CCM_Portal_VIEWDETAIL}
                                data-promotionwindowid={promotion.promotionWindowId}
                                onclick={handleClickViewDetail}
                              >
                              </lightning-button>
                              <!-- <lightning-button
                                                                variant="brand"
                                                                label="ORDER NOW"
                                                                title="ORDER NOW"
                                                                data-promotionwindowid={promotion.promotionWindowId}
                                                                data-promotioncode={promotion.promotionCode}
                                                                onclick={handleClickOrderNow}
                                                            >
                                                            </lightning-button> -->
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                    <!-- Add Pagination -->
                    <div lwc:dom="manual" class="swiper-pagination"></div>
                    <!-- Add Arrows -->
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                  </div>
                  <div class="slds-text-align_center">
                    <span
                      data-brand="SKIL"
                      data-recordtype={recordType}
                      class="viewall"
                      onclick={handleViewAll}
                      >{label.CCM_Portal_ViewAll}</span
                    >
                  </div>
                </div>
              </template>
              <template if:false={skil.length}>
                <div class="slds-text-align_center">{noDataStr}</div>
              </template>
            </div>
            </temeplate>

            <temeplate if:true={isShowFlex}>
            <!-- FLEX -->
            <div class="brand-flex">
              <h2
                class="slds-text-heading_medium slds-text-align_center slds-m-top--xx-large brandBg"
                style={flexBg}
              >
                FLEX
              </h2>
              <template if:true={flex.length}>
                <div class="brand-border">
                  <!-- Swiper -->
                  <div class="swiper-container swiper-container3">
                    <div class="swiper-wrapper">
                      <template for:each={flex} for:item="promotion">
                        <div key={promotion.promotionId} class="swiper-slide">
                          <div class="slds-size_full">
                            <h3
                              class="slds-text-title slds-text-align_left slds-text-color_default title"
                              title={promotion.promotionName}
                            >
                              {promotion.promotionName}
                            </h3>
                            <div class="promotion-wrap" style={promotion.bg}>
                              <template if:true={promotion.offeringName}>
                                <span>{promotion.offeringName}</span>
                              </template>
                            </div>
                            <div
                              class="window slds-grid slds-grid_align-spread slds-wrap"
                            >
                              <span
                                >{label.CCM_Portal_Expiresin}&nbsp;
                                <span class="slds-text-color_destructive"
                                  >{promotion.expireDays}</span
                                >
                                &nbsp;{label.CCM_Portal_Days}</span
                              >
                              <span>{promotion.promotionCode}</span>
                            </div>
                            <div class="buttons">
                              <lightning-button
                                variant="neutral"
                                label={label.CCM_Portal_VIEWDETAIL}
                                title={label.CCM_Portal_VIEWDETAIL}
                                data-promotionwindowid={promotion.promotionWindowId}
                                onclick={handleClickViewDetail}
                              >
                              </lightning-button>
                              <!-- <lightning-button
                                                                variant="brand"
                                                                label="ORDER NOW"
                                                                title="ORDER NOW"
                                                                data-promotionwindowid={promotion.promotionWindowId}
                                                                data-promotioncode={promotion.promotionCode}
                                                                onclick={handleClickOrderNow}
                                                            >
                                                            </lightning-button> -->
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                    <!-- Add Pagination -->
                    <div lwc:dom="manual" class="swiper-pagination"></div>
                    <!-- Add Arrows -->
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                  </div>
                  <div class="slds-text-align_center">
                    <span
                      data-brand="FLEX"
                      data-recordtype={recordType}
                      class="viewall"
                      onclick={handleViewAll}
                      >{label.CCM_Portal_ViewAll}</span
                    >
                  </div>
                </div>
              </template>
              <template if:false={flex.length}>
                <div class="slds-text-align_center">{noDataStr}</div>
              </template>
            </div>
            </temeplate>

            <temeplate if:true={isShowEgo}>
            <!-- EGO -->
            <div class="brand-ego">
              <h2
                class="slds-text-heading_medium slds-text-align_center slds-m-top--xx-large brandBg"
                style={egoBg}
              >
                EGO
              </h2>
              <template if:true={ego.length}>
                <div class="brand-border">
                  <!-- Swiper -->
                  <div class="swiper-container swiper-container1">
                    <div class="swiper-wrapper">
                      <template for:each={ego} for:item="promotion">
                        <div key={promotion.promotionId} class="swiper-slide">
                          <div class="slds-size_full">
                            <h3
                              class="slds-text-title slds-text-align_left slds-text-color_default title"
                              title={promotion.promotionName}
                            >
                              {promotion.promotionName}
                            </h3>
                            <div class="promotion-wrap" style={promotion.bg}>
                              <template if:true={promotion.offeringName}>
                                <span>{promotion.offeringName}</span>
                              </template>
                            </div>
                            <div
                              class="window slds-grid slds-grid_align-spread slds-wrap"
                            >
                              <span
                                >{label.CCM_Portal_Expiresin} &nbsp;
                                <span class="slds-text-color_destructive"
                                  >{promotion.expireDays}</span
                                >&nbsp; {label.CCM_Portal_Days}</span
                              >
                              <span>{promotion.promotionCode}</span>
                            </div>
                            <div class="buttons">
                              <lightning-button
                                variant="neutral"
                                label={label.CCM_Portal_VIEWDETAIL}
                                title={label.CCM_Portal_VIEWDETAIL}
                                data-promotionwindowid={promotion.promotionWindowId}
                                onclick={handleClickViewDetail}
                              >
                              </lightning-button>
                              <!-- <lightning-button
                                                                variant="brand"
                                                                label="ORDER NOW"
                                                                title="ORDER NOW"
                                                                data-promotionwindowid={promotion.promotionWindowId}
                                                                data-promotioncode={promotion.promotionCode}
                                                                onclick={handleClickOrderNow}
                                                            >
                                                            </lightning-button> -->
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                    <!-- Add Pagination -->
                    <div lwc:dom="manual" class="swiper-pagination"></div>
                    <!-- Add Arrows -->
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                  </div>
                  <div class="slds-text-align_center">
                    <span
                      data-brand="EGO"
                      data-recordtype={recordType}
                      class="viewall"
                      onclick={handleViewAll}
                      >{label.CCM_Portal_ViewAll}</span
                    >
                  </div>
                </div>
              </template>
              <template if:false={ego.length}>
                <div class="slds-text-align_center">{noDataStr}</div>
              </template>
            </div>
        </temeplate>

            <div>
              <h2
                class="slds-text-heading_medium slds-text-align_center slds-m-top_large slds-m-bottom_medium"
              >
                {label.CCM_Portal_ComingSoon}
              </h2>
              <template if:true={comingSoonSellIn.length}>
                <div class="carousel-wrap">
                  <lightning-carousel>
                    <template
                      for:each={comingSoonSellIn}
                      for:item="promotion"
                    >
                      <c-ccm-community-promotion-banner-image
                        key={promotion.promotionId}
                        hide-order-button={hide}
                        promotion-id={promotion.promotionId}
                        promotion-code={promotion.promotionCode}
                        promotion-window-id={promotion.promotionWindowId}
                        href="#"
                        src={promotion.bannerImgUrl}
                        alternative-text={promotion.promotionName}
                      >
                      </c-ccm-community-promotion-banner-image>
                    </template>
                  </lightning-carousel>
                </div>
              </template>
              <template if:false={comingSoonSellIn.length}>
                <div class="slds-text-align_center">{noDataStr}</div>
              </template>
            </div>
          </div>
        </template>

        <template if:true={isSearch}>
          <div class="brand-wrap">
            <!-- SKIL -->
            <template if:true={skil.length}>
              <div class="brand-skil">
                <h2
                  class="slds-text-heading_medium slds-text-align_center slds-m-top--x-large brandBg"
                  style={skilBg}
                >
                  SKIL
                </h2>
                <div>
                  <div class="slds-grid slds-gutters slds-wrap">
                    <template for:each={skil} for:item="promotion">
                      <div
                        key={promotion.promotionId}
                        class="slds-col slds-size_1-of-4"
                      >
                        <div class="promotion-list">
                          <h3
                            class="slds-text-title slds-text-align_left slds-text-color_default title"
                            title={promotion.promotionName}
                          >
                            {promotion.promotionName}
                          </h3>
                          <div class="promotion-wrap" style={promotion.bg}>
                            <template if:true={promotion.offeringName}>
                              <span>{promotion.offeringName}</span>
                            </template>
                          </div>
                          <div
                            class="window slds-grid slds-grid_align-spread slds-wrap"
                          >
                            <span
                              >{label.CCM_Portal_Expiresin} &nbsp;
                              <span class="slds-text-color_destructive"
                                >{promotion.expireDays}</span
                              >&nbsp; {label.CCM_Portal_Days}</span
                            >
                            <span>{promotion.promotionCode}</span>
                          </div>
                          <div class="buttons">
                            <lightning-button
                              variant="neutral"
                              label={label.CCM_Portal_VIEWDETAIL}
                              title={label.CCM_Portal_VIEWDETAIL}
                              data-promotionwindowid={promotion.promotionWindowId}
                              onclick={handleClickViewDetail}
                            >
                            </lightning-button>
                            <!-- <lightning-button
                                                            variant="brand"
                                                            label="ORDER NOW"
                                                            title="ORDER NOW"
                                                            data-promotionwindowid={promotion.promotionWindowId}
                                                            data-promotioncode={promotion.promotionCode}
                                                            onclick={handleClickOrderNow}
                                                        >
                                                        </lightning-button> -->
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </template>
            <!-- FLEX -->
            <template if:true={flex.length}>
              <div class="brand-flex">
                <h2
                  class="slds-text-heading_medium slds-text-align_center slds-m-top--xx-large brandBg"
                  style={flexBg}
                >
                  FLEX
                </h2>
                <div>
                  <div class="slds-grid slds-gutters slds-wrap">
                    <template for:each={flex} for:item="promotion">
                      <div
                        key={promotion.promotionId}
                        class="slds-col slds-size_1-of-4"
                      >
                        <div class="promotion-list">
                          <h3
                            class="slds-text-title slds-text-align_left slds-text-color_default title"
                            title={promotion.promotionName}
                          >
                            {promotion.promotionName}
                          </h3>
                          <div class="promotion-wrap" style={promotion.bg}>
                            <template if:true={promotion.offeringName}>
                              <span>{promotion.offeringName}</span>
                            </template>
                          </div>
                          <div
                            class="window slds-grid slds-grid_align-spread slds-wrap"
                          >
                            <span
                              >{label.CCM_Portal_Expiresin} &nbsp;
                              <span class="slds-text-color_destructive"
                                >{promotion.expireDays}</span
                              >&nbsp; {label.CCM_Portal_Days}</span
                            >
                            <span>{promotion.promotionCode}</span>
                          </div>
                          <div class="buttons">
                            <lightning-button
                              variant="neutral"
                              label={label.CCM_Portal_VIEWDETAIL}
                              title={label.CCM_Portal_VIEWDETAIL}
                              data-promotionwindowid={promotion.promotionWindowId}
                              onclick={handleClickViewDetail}
                            >
                            </lightning-button>
                            <!-- <lightning-button
                                                            variant="brand"
                                                            label="ORDER NOW"
                                                            title="ORDER NOW"
                                                            data-promotionwindowid={promotion.promotionWindowId}
                                                            data-promotioncode={promotion.promotionCode}
                                                            onclick={handleClickOrderNow}
                                                        >
                                                        </lightning-button> -->
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </template>
            <!-- EGO -->
            <template if:true={ego.length}>
              <div class="brand-ego">
                <h2
                  class="slds-text-heading_medium slds-text-align_center slds-m-top--xx-large brandBg"
                  style={egoBg}
                >
                  EGO
                </h2>
                <div>
                  <div class="slds-grid slds-gutters slds-wrap">
                    <template for:each={ego} for:item="promotion">
                      <div
                        key={promotion.promotionId}
                        class="slds-col slds-size_1-of-4"
                      >
                        <div class="promotion-list">
                          <h3
                            class="slds-text-title slds-text-align_left slds-text-color_default title"
                            title={promotion.promotionName}
                          >
                            {promotion.promotionName}
                          </h3>
                          <div class="promotion-wrap" style={promotion.bg}>
                            <template if:true={promotion.offeringName}>
                              <span>{promotion.offeringName}</span>
                            </template>
                          </div>
                          <div
                            class="window slds-grid slds-grid_align-spread slds-wrap"
                          >
                            <span
                              >{label.CCM_Portal_Expiresin} &nbsp;
                              <span class="slds-text-color_destructive"
                                >{promotion.expireDays}</span
                              >&nbsp; {label.CCM_Portal_Days}</span
                            >
                            <span>{promotion.promotionCode}</span>
                          </div>
                          <div class="buttons">
                            <lightning-button
                              variant="neutral"
                              label={label.CCM_Portal_VIEWDETAIL}
                              title={label.CCM_Portal_VIEWDETAIL}
                              data-promotionwindowid={promotion.promotionWindowId}
                              onclick={handleClickViewDetail}
                            >
                            </lightning-button>
                            <!-- <lightning-button
                                                            variant="brand"
                                                            label="ORDER NOW"
                                                            title="ORDER NOW"
                                                            data-promotionwindowid={promotion.promotionWindowId}
                                                            data-promotioncode={promotion.promotionCode}
                                                            onclick={handleClickOrderNow}
                                                        >
                                                        </lightning-button> -->
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </template>
            <template if:true={comingSoonSellIn.length}>
              <div class="coming-soon">
                <h2
                  class="slds-text-heading_medium slds-text-align_center slds-m-top_large slds-m-bottom_medium"
                >
                  {label.CCM_Portal_ComingSoon}
                </h2>
                <div>
                  <div class="slds-grid slds-gutters slds-wrap">
                    <template
                      for:each={comingSoonSellIn}
                      for:item="promotion"
                    >
                      <div
                        key={promotion.promotionId}
                        class="slds-col slds-size_1-of-4"
                      >
                        <div class="promotion-list">
                          <h3
                            class="slds-text-title slds-text-align_left slds-text-color_default title"
                            title={promotion.promotionName}
                          >
                            {promotion.promotionName}
                          </h3>
                          <div class="promotion-wrap" style={promotion.bg}>
                            <template if:true={promotion.offeringName}>
                              <span>{promotion.offeringName}</span>
                            </template>
                          </div>
                          <div
                            class="window slds-grid slds-grid_align-spread slds-wrap"
                          >
                            <span
                              >{label.CCM_Portal_Expiresin} &nbsp;
                              <span class="slds-text-color_destructive"
                                >{promotion.expireDays}</span
                              >&nbsp; {label.CCM_Portal_Days}</span
                            >
                            <span>{promotion.promotionCode}</span>
                          </div>
                          <div class="buttons">
                            <lightning-button
                              variant="neutral"
                              label={label.CCM_Portal_VIEWDETAIL}
                              title={label.CCM_Portal_VIEWDETAIL}
                              data-promotionwindowid={promotion.promotionWindowId}
                              onclick={handleClickViewDetail}
                            >
                            </lightning-button>
                            <!-- <lightning-button
                                                            variant="brand"
                                                            label="ORDER NOW"
                                                            title="ORDER NOW"
                                                            hide-order-button={hide}
                                                            data-promotionwindowid={promotion.promotionWindowId}
                                                            data-promotioncode={promotion.promotionCode}
                                                            onclick={handleClickOrderNow}
                                                        >
                                                        </lightning-button> -->
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </template>
      </div>
    </div>
  </template>
  <template if:true={isSellThroughActive}>
    <div class="slds-grid slds-container_center width90">
      <div class="slds-col slds-size_3-of-12">
        <ul class="type">
          <li class="slds-m-bottom_small" onclick={handleActiveSellIn}>
            {label.CCM_Portal_SellinPromotion}
          </li>

          <li class="active slds-m-bottom_small">{label.CCM_Portal_SellthroughPOSCredit}</li>
        </ul>
      </div>
      <div class="slds-col slds-size_9-of-12">
        <div class="slds-grid">
          <div class="slds-col slds-size_1-of-4 slds-p-right_xx-large">
            <lightning-input
              class="promoCode"
              type="text"
              value=""
              label={label.CCM_Portal_PromotionCode}
            ></lightning-input>
          </div>
          <div class="slds-col slds-size_1-of-4 slds-p-right_xx-large">
            <lightning-input
              class="product"
              type="text"
              value=""
              label={label.CCM_Portal_Product}
            ></lightning-input>
          </div>
          <div class="slds-col slds-size_1-of-4 slds-p-right_xx-large">
            <lightning-combobox
              class="typeforexternal"
              name="typeforexternal"
              label={label.CCM_Portal_PromotionType}
              value=""
              options={optionsSellThrough}
            >
            </lightning-combobox>
          </div>
          <div class="slds-col slds-size_1-of-4">
            <div class="btn-search-wrap">
              <lightning-button
                class=""
                variant="brand"
                label={label.CCM_Portal_Search}
                title={label.CCM_Portal_Search}
                onclick={handleClickSearch}
              >
              </lightning-button>
            </div>
          </div>
        </div>
        <template if:false={isSearch}>
          <div>
            <h2
              class="slds-text-heading_medium slds-text-align_center slds-m-top_large slds-m-bottom_medium"
            >
              {label.CCM_Portal_FeaturedPromotions}
            </h2>
            <template if:true={bigSalesSellThrough.length}>
              <div class="carousel-wrap">
                <lightning-carousel>
                  <template
                    for:each={bigSalesSellThrough}
                    for:item="promotion"
                  >
                    <c-ccm-community-promotion-banner-image
                      key={promotion.promotionId}
                      display-claim-button={promotion.isClaim}
                      hide-order-button={hide}
                      promotion-id={promotion.promotionId}
                      promotion-code={promotion.promotionCode}
                      promotion-window-id={promotion.promotionWindowId}
                      href="#"
                      src={promotion.bannerImgUrl}
                      alternative-text={promotion.promotionName}
                    >
                    </c-ccm-community-promotion-banner-image>
                  </template>
                </lightning-carousel>
              </div>
            </template>
            <template if:false={bigSalesSellThrough.length}>
              <div class="slds-text-align_center">{noDataStr}</div>
            </template>
          </div>

          <div class="brand-wrap">
            <!-- SKIL -->
            <div class="brand-skil">
              <h2
                class="slds-text-heading_medium slds-text-align_center slds-m-top--x-large brandBg"
                style={skilBg}
              >
                SKIL
              </h2>
              <template if:true={skil.length}>
                <div class="brand-border">
                  <!-- Swiper -->
                  <div class="swiper-container swiper-container2">
                    <div class="swiper-wrapper">
                      <template for:each={skil} for:item="promotion">
                        <div key={promotion.promotionId} class="swiper-slide">
                          <div class="slds-size_full">
                            <h3
                              class="slds-text-title slds-text-align_left slds-text-color_default title"
                              title={promotion.promotionName}
                            >
                              {promotion.promotionName}
                            </h3>
                            <div class="promotion-wrap" style={promotion.bg}>
                              <template if:true={promotion.offeringName}>
                                <span>{promotion.offeringName}</span>
                              </template>
                            </div>
                            <div
                              class="window slds-grid slds-grid_align-spread slds-wrap"
                            >
                              <template if:false={promotion.isClaim}>
                                <span>
                                  {label.CCM_Portal_Expiresin} &nbsp;
                                  <span class="slds-text-color_destructive">
                                    {promotion.expireDays}
                                  </span>
                                  &nbsp;{label.CCM_Portal_Days}
                                </span>
                              </template>
                              <template if:true={promotion.isClaim}>
                                <span>
                                  {label.CCM_Portal_Claimexpiresin} &nbsp;
                                  <span class="slds-text-color_destructive">
                                    {promotion.claimExpireDays}
                                  </span>
                                  &nbsp;{label.CCM_Portal_Days}
                                </span>
                              </template>
                              <span>{promotion.promotionCode}</span>
                            </div>
                            <div class="buttons">
                              <lightning-button
                                variant="neutral"
                                label={label.CCM_Portal_VIEWDETAIL}
                                title={label.CCM_Portal_VIEWDETAIL}
                                data-promotionwindowid={promotion.promotionWindowId}
                                onclick={handleClickViewDetail}
                              >
                              </lightning-button>
                              <!--<template if:true={promotion.isClaim}>-->
                                <lightning-button
                                  variant="brand"
                                  label={label.CCM_Portal_CLAIMNOW}
                                  title={label.CCM_Portal_CLAIMNOW}
                                  data-promotionwindowid={promotion.promotionWindowId}
                                  data-promotioncode={promotion.promotionCode}
                                  onclick={handleClickClaimNow}
                                >
                                </lightning-button>
                             <!-- </template>-->
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                    <!-- Add Pagination -->
                    <div lwc:dom="manual" class="swiper-pagination"></div>
                    <!-- Add Arrows -->
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                  </div>
                  <div class="slds-text-align_center">
                    <span
                      data-brand="SKIL"
                      data-recordtype={recordType}
                      class="viewall"
                      onclick={handleViewAll}
                      >{label.CCM_Portal_ViewAll}</span
                    >
                  </div>
                </div>
              </template>
              <template if:false={skil.length}>
                <div class="slds-text-align_center">{noDataStr}</div>
              </template>
            </div>

            <!-- Flex -->
            <div class="brand-flex">
              <h2
                class="slds-text-heading_medium slds-text-align_center slds-m-top--xx-large brandBg"
                style={flexBg}
              >
                FLEX
              </h2>
              <template if:true={flex.length}>
                <div class="brand-border">
                  <!-- Swiper -->
                  <div class="swiper-container swiper-container3">
                    <div class="swiper-wrapper">
                      <template for:each={flex} for:item="promotion">
                        <div key={promotion.promotionId} class="swiper-slide">
                          <div class="slds-size_full">
                            <h3
                              class="slds-text-title slds-text-align_left slds-text-color_default title"
                              title={promotion.promotionName}
                            >
                              {promotion.promotionName}
                            </h3>
                            <div class="promotion-wrap" style={promotion.bg}>
                              <template if:true={promotion.offeringName}>
                                <span>{promotion.offeringName}</span>
                              </template>
                            </div>
                            <div
                              class="window slds-grid slds-grid_align-spread slds-wrap"
                            >
                              <template if:false={promotion.isClaim}>
                                <span>
                                  {label.CCM_Portal_Expiresin} &nbsp;
                                  <span class="slds-text-color_destructive">
                                    {promotion.expireDays}
                                  </span>
                                  &nbsp;{label.CCM_Portal_Days}
                                </span>
                              </template>
                              <template if:true={promotion.isClaim}>
                                <span>
                                  {label.CCM_Portal_Claimexpiresin} &nbsp;
                                  <span class="slds-text-color_destructive">
                                    {promotion.claimExpireDays}
                                  </span>
                                  &nbsp;{label.CCM_Portal_Days}
                                </span>
                              </template>
                              <span>{promotion.promotionCode}</span>
                            </div>
                            <div class="buttons">
                              <lightning-button
                                variant="neutral"
                                label={label.CCM_Portal_VIEWDETAIL}
                                title={label.CCM_Portal_VIEWDETAIL}
                                data-promotionwindowid={promotion.promotionWindowId}
                                onclick={handleClickViewDetail}
                              >
                              </lightning-button>
                             <!-- <template if:true={promotion.isClaim}>-->
                                <lightning-button
                                  variant="brand"
                                  label={label.CCM_Portal_CLAIMNOW}
                                  title={label.CCM_Portal_CLAIMNOW}
                                  data-promotionwindowid={promotion.promotionWindowId}
                                  data-promotioncode={promotion.promotionCode}
                                  onclick={handleClickClaimNow}
                                >
                                </lightning-button>
                              <!--</template>-->
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                    <!-- Add Pagination -->
                    <div lwc:dom="manual" class="swiper-pagination"></div>
                    <!-- Add Arrows -->
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                  </div>
                  <div class="slds-text-align_center">
                    <span
                      data-brand="FLEX"
                      data-recordtype={recordType}
                      class="viewall"
                      onclick={handleViewAll}
                      >{label.CCM_Portal_ViewAll}</span
                    >
                  </div>
                </div>
              </template>
              <template if:false={flex.length}>
                <div class="slds-text-align_center">{noDataStr}</div>
              </template>
            </div>
            <!-- EGO -->
            <div class="brand-ego">
              <h2
                class="slds-text-heading_medium slds-text-align_center slds-m-top--xx-large brandBg"
                style={egoBg}
              >
                EGO
              </h2>
              <template if:true={ego.length}>
                <div class="brand-border">
                  <!-- Swiper -->
                  <div class="swiper-container swiper-container1">
                    <div class="swiper-wrapper">
                      <template for:each={ego} for:item="promotion">
                        <div key={promotion.promotionId} class="swiper-slide">
                          <div class="slds-size_full">
                            <h3
                              class="slds-text-title slds-text-align_left slds-text-color_default title"
                              title={promotion.promotionName}
                            >
                              {promotion.promotionName}
                            </h3>
                            <div class="promotion-wrap" style={promotion.bg}>
                              <template if:true={promotion.offeringName}>
                                <span>{promotion.offeringName}</span>
                              </template>
                            </div>
                            <div
                              class="window slds-grid slds-grid_align-spread slds-wrap"
                            >
                              <template if:false={promotion.isClaim}>
                                <span>
                                  {label.CCM_Portal_Expiresin} &nbsp;
                                  <span class="slds-text-color_destructive">
                                    {promotion.expireDays}
                                  </span>
                                  &nbsp;{label.CCM_Portal_Days}
                                </span>
                              </template>
                              <template if:true={promotion.isClaim}>
                                <span>
                                  {label.CCM_Portal_Claimexpiresin} &nbsp;
                                  <span class="slds-text-color_destructive">
                                    {promotion.claimExpireDays}
                                  </span>
                                  &nbsp;{label.CCM_Portal_Days}
                                </span>
                              </template>
                              <span>{promotion.promotionCode}</span>
                            </div>
                            <div class="buttons">
                              <lightning-button
                                variant="neutral"
                                label={label.CCM_Portal_VIEWDETAIL}
                                title={label.CCM_Portal_VIEWDETAIL}
                                data-promotionwindowid={promotion.promotionWindowId}
                                onclick={handleClickViewDetail}
                              >
                              </lightning-button>
                              <!--<template if:true={promotion.isClaim}>-->
                                <lightning-button
                                  variant="brand"
                                  label={label.CCM_Portal_CLAIMNOW}
                                  title={label.CCM_Portal_CLAIMNOW}
                                  data-promotionwindowid={promotion.promotionWindowId}
                                  data-promotioncode={promotion.promotionCode}
                                  onclick={handleClickClaimNow}
                                >
                                </lightning-button>
                              <!--</template>-->
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                    <!-- Add Pagination -->
                    <div lwc:dom="manual" class="swiper-pagination"></div>
                    <!-- Add Arrows -->
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                  </div>
                  <div class="slds-text-align_center">
                    <span
                      data-brand="EGO"
                      data-recordtype={recordType}
                      class="viewall"
                      onclick={handleViewAll}
                      >{label.CCM_Portal_ViewAll}</span
                    >
                  </div>
                </div>
              </template>
              <template if:false={ego.length}>
                <div class="slds-text-align_center">{noDataStr}</div>
              </template>
            </div>
            <div>
              <h2
                class="slds-text-heading_medium slds-text-align_center slds-m-top_large slds-m-bottom_medium"
              >
                {label.CCM_Portal_ComingSoon}
              </h2>

              <template if:true={comingSoonSellThrough.length}>
                <div class="carousel-wrap">
                  <lightning-carousel>
                    <template
                      for:each={comingSoonSellThrough}
                      for:item="promotion"
                    >
                      <c-ccm-community-promotion-banner-image
                        display-claim-button={promotion.isClaim}
                        hide-order-button={hide}
                        key={promotion.promotionId}
                        promotion-id={promotion.promotionId}
                        promotion-code={promotion.promotionCode}
                        promotion-window-id={promotion.promotionWindowId}
                        href="#"
                        src={promotion.bannerImgUrl}
                        alternative-text={promotion.promotionName}
                      >
                      </c-ccm-community-promotion-banner-image>
                    </template>
                  </lightning-carousel>
                </div>
              </template>
              <template if:false={comingSoonSellThrough.length}>
                <div class="slds-text-align_center">{noDataStr}</div>
              </template>
            </div>
          </div>
        </template>

        <template if:true={isSearch}>
          <div class="brand-wrap">
            <!-- SKIL -->
            <template if:true={skil.length}>
              <div class="brand-skil">
                <h2
                  class="slds-text-heading_medium slds-text-align_center slds-m-top--x-large brandBg"
                  style={skilBg}
                >
                  SKIL
                </h2>
                <div>
                  <div class="slds-grid slds-gutters slds-wrap">
                    <template for:each={skil} for:item="promotion">
                      <div
                        key={promotion.promotionId}
                        class="slds-col slds-size_1-of-4"
                      >
                        <div class="promotion-list">
                          <h3
                            class="slds-text-title slds-text-align_left slds-text-color_default title"
                            title={promotion.promotionName}
                          >
                            {promotion.promotionName}
                          </h3>
                          <div class="promotion-wrap" style={promotion.bg}>
                            <template if:true={promotion.offeringName}>
                              <span>{promotion.offeringName}</span>
                            </template>
                          </div>
                          <div
                            class="window slds-grid slds-grid_align-spread slds-wrap"
                          >
                            <template if:false={promotion.isClaim}>
                              <span>
                                {label.CCM_Portal_Expiresin} &nbsp;
                                <span class="slds-text-color_destructive">
                                  {promotion.expireDays}
                                </span>
                                &nbsp;{label.CCM_Portal_Days}
                              </span>
                            </template>
                            <template if:true={promotion.isClaim}>
                              <span>
                                {label.CCM_Portal_Claimexpiresin} &nbsp;
                                <span class="slds-text-color_destructive">
                                  {promotion.claimExpireDays}
                                </span>
                                &nbsp;{label.CCM_Portal_Days}
                              </span>
                            </template>
                            <span>{promotion.promotionCode}</span>
                          </div>
                          <div class="buttons">
                            <lightning-button
                              variant="neutral"
                              label={label.CCM_Portal_VIEWDETAIL}
                              title={label.CCM_Portal_VIEWDETAIL}
                              data-promotionwindowid={promotion.promotionWindowId}
                              onclick={handleClickViewDetail}
                            >
                            </lightning-button>
                            <!--<template if:true={promotion.isClaim}>-->
                              <lightning-button
                                variant="brand"
                                label={label.CCM_Portal_CLAIMNOW}
                                title={label.CCM_Portal_CLAIMNOW}
                                data-promotionwindowid={promotion.promotionWindowId}
                                data-promotioncode={promotion.promotionCode}
                                onclick={handleClickClaimNow}
                              >
                              </lightning-button>
                            <!--</template>-->
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </template>
            <!-- Flex -->
            <template if:true={flex.length}>
              <div class="brand-flex">
                <h2
                  class="slds-text-heading_medium slds-text-align_center slds-m-top--xx-large brandBg"
                  style={flexBg}
                >
                  FLEX
                </h2>
                <div>
                  <div class="slds-grid slds-gutters slds-wrap">
                    <template for:each={flex} for:item="promotion">
                      <div
                        key={promotion.promotionId}
                        class="slds-col slds-size_1-of-4"
                      >
                        <div class="promotion-list">
                          <h3
                            class="slds-text-title slds-text-align_left slds-text-color_default title"
                            title={promotion.promotionName}
                          >
                            {promotion.promotionName}
                          </h3>
                          <div class="promotion-wrap" style={promotion.bg}>
                            <template if:true={promotion.offeringName}>
                              <span>{promotion.offeringName}</span>
                            </template>
                          </div>
                          <div
                            class="window slds-grid slds-grid_align-spread slds-wrap"
                          >
                            <template if:false={promotion.isClaim}>
                              <span>
                                {label.CCM_Portal_Expiresin} &nbsp;
                                <span class="slds-text-color_destructive">
                                  {promotion.expireDays}
                                </span>
                                &nbsp;{label.CCM_Portal_Days}
                              </span>
                            </template>
                            <template if:true={promotion.isClaim}>
                              <span>
                                {label.CCM_Portal_Claimexpiresin} &nbsp;
                                <span class="slds-text-color_destructive">
                                  {promotion.claimExpireDays}
                                </span>
                                &nbsp;{label.CCM_Portal_Days}
                              </span>
                            </template>
                            <span>{promotion.promotionCode}</span>
                          </div>
                          <div class="buttons">
                            <lightning-button
                              variant="neutral"
                              label={label.CCM_Portal_VIEWDETAIL}
                              title={label.CCM_Portal_VIEWDETAIL}
                              data-promotionwindowid={promotion.promotionWindowId}
                              onclick={handleClickViewDetail}
                            >
                            </lightning-button>
                            <!--<template if:true={promotion.isClaim}>-->
                              <lightning-button
                                variant="brand"
                                label={label.CCM_Portal_CLAIMNOW}
                                title={label.CCM_Portal_CLAIMNOW}
                                data-promotionwindowid={promotion.promotionWindowId}
                                data-promotioncode={promotion.promotionCode}
                                onclick={handleClickClaimNow}
                              >
                              </lightning-button>
                            <!--</template>-->
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </template>
            <!-- EGO -->
            <template if:true={ego.length}>
              <div class="brand-ego">
                <h2
                  class="slds-text-heading_medium slds-text-align_center slds-m-top--xx-large brandBg"
                  style={egoBg}
                >
                  EGO
                </h2>
                <div>
                  <div class="slds-grid slds-gutters slds-wrap">
                    <template for:each={ego} for:item="promotion">
                      <div
                        key={promotion.promotionId}
                        class="slds-col slds-size_1-of-4"
                      >
                        <div class="promotion-list">
                          <h3
                            class="slds-text-title slds-text-align_left slds-text-color_default title"
                            title={promotion.promotionName}
                          >
                            {promotion.promotionName}
                          </h3>
                          <div class="promotion-wrap" style={promotion.bg}>
                            <template if:true={promotion.offeringName}>
                              <span>{promotion.offeringName}</span>
                            </template>
                          </div>
                          <div
                            class="window slds-grid slds-grid_align-spread slds-wrap"
                          >
                            <template if:false={promotion.isClaim}>
                              <span>
                                {label.CCM_Portal_Expiresin} &nbsp;
                                <span class="slds-text-color_destructive">
                                  {promotion.expireDays}
                                </span>
                                &nbsp;{label.CCM_Portal_Days}
                              </span>
                            </template>
                            <template if:true={promotion.isClaim}>
                              <span>
                                {label.CCM_Portal_Claimexpiresin} &nbsp;
                                <span class="slds-text-color_destructive">
                                  {promotion.claimExpireDays}
                                </span>
                                &nbsp;{label.CCM_Portal_Days}
                              </span>
                            </template>
                            <span>{promotion.promotionCode}</span>
                          </div>
                          <div class="buttons">
                            <lightning-button
                              variant="neutral"
                              label={label.CCM_Portal_VIEWDETAIL}
                              title={label.CCM_Portal_VIEWDETAIL}
                              data-promotionwindowid={promotion.promotionWindowId}
                              onclick={handleClickViewDetail}
                            >
                            </lightning-button>
                            <!--<template if:true={promotion.isClaim}>-->
                              <lightning-button
                                variant="brand"
                                label={label.CCM_Portal_CLAIMNOW}
                                title={label.CCM_Portal_CLAIMNOW}
                                data-promotionwindowid={promotion.promotionWindowId}
                                data-promotioncode={promotion.promotionCode}
                                onclick={handleClickClaimNow}
                              >
                              </lightning-button>
                            <!--</template>-->
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </template>
            <template if:true={comingSoonSellThrough.length}>
              <div class="coming-soon">
                <h2
                  class="slds-text-heading_medium slds-text-align_center slds-m-top_large slds-m-bottom_medium"
                >
                  {label.CCM_Portal_ComingSoon}
                </h2>
                <div>
                  <div class="slds-grid slds-gutters slds-wrap">
                    <template
                      for:each={comingSoonSellThrough}
                      for:item="promotion"
                    >
                      <div
                        key={promotion.promotionId}
                        class="slds-col slds-size_1-of-4"
                      >
                        <div class="promotion-list">
                          <h3
                            class="slds-text-title slds-text-align_left slds-text-color_default title"
                            title={promotion.promotionName}
                          >
                            {promotion.promotionName}
                          </h3>
                          <div class="promotion-wrap" style={promotion.bg}>
                            <template if:true={promotion.offeringName}>
                              <span>{promotion.offeringName}</span>
                            </template>
                          </div>
                          <div
                            class="window slds-grid slds-grid_align-spread slds-wrap"
                          >
                            <template if:false={promotion.isClaim}>
                              <span>
                                {label.CCM_Portal_Expiresin} &nbsp;
                                <span class="slds-text-color_destructive">
                                  {promotion.expireDays}
                                </span>
                                &nbsp;{label.CCM_Portal_Days}
                              </span>
                            </template>
                            <template if:true={promotion.isClaim}>
                              <span>
                                {label.CCM_Portal_Claimexpiresin} &nbsp;
                                <span class="slds-text-color_destructive">
                                  {promotion.claimExpireDays}
                                </span>
                                &nbsp;{label.CCM_Portal_Days}
                              </span>
                            </template>
                            <span>{promotion.promotionCode}</span>
                          </div>
                          <div class="buttons">
                            <lightning-button
                              variant="neutral"
                              label={label.CCM_Portal_VIEWDETAIL}
                              title={label.CCM_Portal_VIEWDETAIL}
                              data-promotionwindowid={promotion.promotionWindowId}
                              onclick={handleClickViewDetail}
                            >
                            </lightning-button>
                            <!-- <template if:true={promotion.isClaim}>
                                                            <lightning-button
                                                                variant="brand"
                                                                label="CLAIM NOW"
                                                                title="CLAIM NOW"
                                                                data-promotionwindowid={promotion.promotionWindowId}
                                                                data-promotioncode={promotion.promotionCode}
                                                                onclick={handleClickClaimNow}
                                                            >
                                                            </lightning-button>
                                                        </template> -->
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </template>
      </div>
    </div>
  </template>
</template>