<?xml version="1.0" encoding="UTF-8"?>
<StandardValueSet xmlns="http://soap.sforce.com/2006/04/metadata">
    <sorted>false</sorted>
    <standardValue>
        <fullName>Store Visit</fullName>
        <default>false</default>
        <label>Store Visit</label>
    </standardValue>
    <standardValue>
        <fullName>Call Report</fullName>
        <default>false</default>
        <label>Call Report</label>
    </standardValue>
    <standardValue>
        <fullName>Call</fullName>
        <default>true</default>
        <label>Call</label>
    </standardValue>
    <standardValue>
        <fullName>Voicemail Received</fullName>
        <default>false</default>
        <label>Voicemail Received</label>
    </standardValue>
    <standardValue>
        <fullName>Email Received</fullName>
        <default>false</default>
        <label>Email Received</label>
    </standardValue>
    <standardValue>
        <fullName>Voicemail Left</fullName>
        <default>false</default>
        <label>Voicemail Left</label>
    </standardValue>
    <standardValue>
        <fullName>Community</fullName>
        <default>false</default>
        <label>Community</label>
    </standardValue>
    <standardValue>
        <fullName>Social Media</fullName>
        <default>false</default>
        <label>Social Media</label>
    </standardValue>
    <standardValue>
        <fullName>Review</fullName>
        <default>false</default>
        <label>Review</label>
    </standardValue>
    <standardValue>
        <fullName>Other</fullName>
        <default>false</default>
        <label>Other</label>
    </standardValue>
    <standardValue>
        <fullName>Sales Target</fullName>
        <default>false</default>
        <label>Sales Target</label>
    </standardValue>
    <standardValue>
        <fullName>Reverse Order Request - Overage</fullName>
        <default>false</default>
        <label>Reverse Order Request - Overage</label>
    </standardValue>
    <standardValue>
        <fullName>Demo Log</fullName>
        <default>false</default>
        <label>Demo Log</label>
    </standardValue>
</StandardValueSet>
