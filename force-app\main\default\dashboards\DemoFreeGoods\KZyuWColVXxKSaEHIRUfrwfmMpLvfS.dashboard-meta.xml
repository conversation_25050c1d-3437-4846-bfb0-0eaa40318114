<?xml version="1.0" encoding="UTF-8"?>
<Dashboard xmlns="http://soap.sforce.com/2006/04/metadata">
    <backgroundEndColor>#FFFFFF</backgroundEndColor>
    <backgroundFadeDirection>Diagonal</backgroundFadeDirection>
    <backgroundStartColor>#FFFFFF</backgroundStartColor>
    <chartTheme>light</chartTheme>
    <colorPalette>unity</colorPalette>
    <dashboardChartTheme>light</dashboardChartTheme>
    <dashboardColorPalette>unity</dashboardColorPalette>
    <dashboardGridLayout>
        <dashboardGridComponents>
            <colSpan>6</colSpan>
            <columnIndex>0</columnIndex>
            <dashboardComponent>
                <autoselectColumnsFromReport>false</autoselectColumnsFromReport>
                <chartAxisRange>Auto</chartAxisRange>
                <chartSummary>
                    <axisBinding>y</axisBinding>
                    <column>RowCount</column>
                </chartSummary>
                <componentType>Column</componentType>
                <decimalPrecision>-1</decimalPrecision>
                <displayUnits>Auto</displayUnits>
                <drillEnabled>false</drillEnabled>
                <drillToDetailEnabled>false</drillToDetailEnabled>
                <enableHover>false</enableHover>
                <expandOthers>false</expandOthers>
                <groupingColumn>Consumption__c.Date__c</groupingColumn>
                <groupingSortProperties>
                    <groupingSorts>
                        <groupingLevel>g1</groupingLevel>
                        <sortOrder>a</sortOrder>
                    </groupingSorts>
                </groupingSortProperties>
                <header>Consumption Tracking</header>
                <legendPosition>Bottom</legendPosition>
                <report>DemoFreeGoods/Consumption_ITf</report>
                <showPercentage>false</showPercentage>
                <showValues>true</showValues>
                <sortBy>RowLabelAscending</sortBy>
                <useReportChart>false</useReportChart>
            </dashboardComponent>
            <rowIndex>0</rowIndex>
            <rowSpan>8</rowSpan>
        </dashboardGridComponents>
        <dashboardGridComponents>
            <colSpan>6</colSpan>
            <columnIndex>6</columnIndex>
            <dashboardComponent>
                <autoselectColumnsFromReport>false</autoselectColumnsFromReport>
                <componentType>FlexTable</componentType>
                <flexComponentProperties>
                    <decimalPrecision>-1</decimalPrecision>
                    <flexTableColumn>
                        <reportColumn>Product_Inventory__c.Task_Owner__c</reportColumn>
                        <showSubTotal>false</showSubTotal>
                        <showTotal>false</showTotal>
                        <type>grouping</type>
                    </flexTableColumn>
                    <flexTableColumn>
                        <reportColumn>s!Product_Inventory__c.Booked_Inventory__c</reportColumn>
                        <showSubTotal>false</showSubTotal>
                        <showTotal>false</showTotal>
                        <type>aggregate</type>
                    </flexTableColumn>
                    <flexTableColumn>
                        <reportColumn>RowCount</reportColumn>
                        <showSubTotal>false</showSubTotal>
                        <showTotal>false</showTotal>
                        <type>aggregate</type>
                    </flexTableColumn>
                    <flexTableColumn>
                        <reportColumn>s!Product_Inventory__c.Consumed_Inventory__c</reportColumn>
                        <showSubTotal>false</showSubTotal>
                        <showTotal>false</showTotal>
                        <type>aggregate</type>
                    </flexTableColumn>
                    <flexTableColumn>
                        <reportColumn>s!Product_Inventory__c.On_Hands_Inventory__c</reportColumn>
                        <showSubTotal>false</showSubTotal>
                        <showTotal>false</showTotal>
                        <type>aggregate</type>
                    </flexTableColumn>
                    <flexTableSortInfo>
                        <sortOrder>1</sortOrder>
                    </flexTableSortInfo>
                    <hideChatterPhotos>true</hideChatterPhotos>
                </flexComponentProperties>
                <groupingSortProperties>
                    <groupingSorts>
                        <groupingLevel>g1</groupingLevel>
                        <sortOrder>a</sortOrder>
                    </groupingSorts>
                </groupingSortProperties>
                <header>On-hands inventory</header>
                <report>DemoFreeGoods/onhands_inventory_9o5</report>
            </dashboardComponent>
            <rowIndex>0</rowIndex>
            <rowSpan>8</rowSpan>
        </dashboardGridComponents>
        <dashboardGridComponents>
            <colSpan>6</colSpan>
            <columnIndex>0</columnIndex>
            <dashboardComponent>
                <autoselectColumnsFromReport>false</autoselectColumnsFromReport>
                <chartAxisRange>Auto</chartAxisRange>
                <chartSummary>
                    <axisBinding>y</axisBinding>
                    <column>RowCount</column>
                </chartSummary>
                <componentType>BarStacked</componentType>
                <decimalPrecision>-1</decimalPrecision>
                <displayUnits>Auto</displayUnits>
                <drillEnabled>false</drillEnabled>
                <drillToDetailEnabled>false</drillToDetailEnabled>
                <enableHover>false</enableHover>
                <expandOthers>false</expandOthers>
                <groupingColumn>FK_$Demo_Plan__c.Customer__c</groupingColumn>
                <groupingColumn>FK_$Demo_Plan__c.Store_Location__c</groupingColumn>
                <groupingSortProperties>
                    <groupingSorts>
                        <groupingLevel>g1</groupingLevel>
                        <sortColumn>RowCount</sortColumn>
                        <sortOrder>d</sortOrder>
                    </groupingSorts>
                    <groupingSorts>
                        <groupingLevel>g2</groupingLevel>
                        <sortOrder>a</sortOrder>
                    </groupingSorts>
                </groupingSortProperties>
                <header>Top Sales by Account/Store location</header>
                <legendPosition>Right</legendPosition>
                <report>DemoFreeGoods/Top_Sales_by_AccountStore_location_Ym6</report>
                <showPercentage>false</showPercentage>
                <showValues>false</showValues>
                <sortBy>RowLabelAscending</sortBy>
                <useReportChart>false</useReportChart>
            </dashboardComponent>
            <rowIndex>8</rowIndex>
            <rowSpan>8</rowSpan>
        </dashboardGridComponents>
        <dashboardGridComponents>
            <colSpan>6</colSpan>
            <columnIndex>6</columnIndex>
            <dashboardComponent>
                <autoselectColumnsFromReport>false</autoselectColumnsFromReport>
                <chartAxisRange>Auto</chartAxisRange>
                <chartSummary>
                    <axisBinding>x</axisBinding>
                    <column>RowCount</column>
                </chartSummary>
                <chartSummary>
                    <aggregate>Sum</aggregate>
                    <axisBinding>y</axisBinding>
                    <column>Consumption__c.Total_Amount__c</column>
                </chartSummary>
                <componentType>Scatter</componentType>
                <decimalPrecision>-1</decimalPrecision>
                <displayUnits>Auto</displayUnits>
                <drillEnabled>false</drillEnabled>
                <drillToDetailEnabled>false</drillToDetailEnabled>
                <enableHover>false</enableHover>
                <expandOthers>false</expandOthers>
                <groupingColumn>FK_CUSTENT_OWNER_NAME</groupingColumn>
                <groupingSortProperties>
                    <groupingSorts>
                        <groupingLevel>g1</groupingLevel>
                        <sortOrder>a</sortOrder>
                    </groupingSorts>
                </groupingSortProperties>
                <header>Top Sales by JSS/TR</header>
                <legendPosition>Bottom</legendPosition>
                <report>DemoFreeGoods/Top_Sales_by_JSSTR_g16</report>
                <showPercentage>false</showPercentage>
                <showValues>false</showValues>
                <sortBy>RowLabelAscending</sortBy>
                <useReportChart>false</useReportChart>
            </dashboardComponent>
            <rowIndex>8</rowIndex>
            <rowSpan>8</rowSpan>
        </dashboardGridComponents>
        <numberOfColumns>12</numberOfColumns>
        <rowHeight>36</rowHeight>
    </dashboardGridLayout>
    <dashboardType>SpecifiedUser</dashboardType>
    <description>Demo Tracking</description>
    <isGridLayout>true</isGridLayout>
    <runningUser><EMAIL></runningUser>
    <textColor>#000000</textColor>
    <title>Demo Free Goods</title>
    <titleColor>#000000</titleColor>
    <titleSize>12</titleSize>
</Dashboard>
