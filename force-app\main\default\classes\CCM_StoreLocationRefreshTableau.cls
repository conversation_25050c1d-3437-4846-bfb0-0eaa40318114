/**
 * <AUTHOR>
 * @date 2023-06-21
 * @description refresh store location data in tableau
 */
public without sharing class CCM_StoreLocationRefreshTableau{
    @future(callout = true)
    public static void syncStoreLocationRefreshTableau(){
        String token = getToken();
        System.debug('gettoken:' + token);
        //获取 对应数据源id
        List<String> datasourcesIdList = getDataSources(token);
        System.debug('datasourcesIdList:' + datasourcesIdList.size());
        //数据提取启动
        refreshDataSources(token, datasourcesIdList);
    }
    public static String gettoken(){
        String token = '';
        HttpRequest request = new HttpRequest();
        request.setEndpoint(Label.Tableau_Storelocation_URL+'/api/3.4/auth/signin');
        String signbody = '{"credentials":{"personalAccessTokenName":"' + Label.Tableau_personalAccessTokenName + '","personalAccessTokenSecret":"' + Label.tableau_personalAccessTokenSecret + '","site":{"contentUrl":"cnatableau"}}}';
        request.setBody(signbody);
        request.setMethod('POST');
        request.setHeader('Content-Type', 'application/json');
        String resultStr = '';
        Integer statusCode = 0;
        if (Test.isRunningTest()){
            resultStr = '<?xml version="1.0" encoding="UTF-8"?><tsResponse xmlns="http://tableau.com/api" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://tableau.com/api https://help.tableau.com/samples/en-us/rest_api/ts-api_3_4.xsd"><credentials token="zKwor0l-Q-msJ5Zsx2fxhA&#124;9tUCyBVEd3zWIbusXCiAcd0sPzmHdPVZ&#124;a8547499-fe8e-4874-b416-a10254b578d6" estimatedTimeToExpiration="351:06:56"><site id="a8547499-fe8e-4874-b416-a10254b578d6" contentUrl="cnatableau"/><user id="e0d11b55-124f-45d5-b056-249bc983c292"/></credentials></tsResponse>';
            statusCode = 200;
        } else{
            HttpResponse response = new Http().send(request);
            resultStr = response.getBodyAsBlob().toString();
            statusCode = response.getStatusCode();
        }
        System.debug('statusCode:' + statusCode + 'body:' + resultStr);
        Dom.Document document = new Dom.Document();
        document.load(resultStr);
        Dom.XmlNode rootElement = document.getRootElement();
        for (Dom.XmlNode node : rootElement.getChildElements()){
            if (node.getName().equalsIgnoreCase('credentials')){
                token = node.getAttribute('token', null);
                System.debug('token:' + token);
                break;
            }
        }
        return token;
    }
    public static List<String> getDataSources(String token){
        List<String> contentUatUrls = new List<String>();
        Organization org = [SELECT Id, IsSandbox
                            FROM Organization
                            WHERE Id = :UserInfo.getOrganizationId()];
        if (org.IsSandbox){
            contentUatUrls = Label.Tableau_Storelocation_Datasource_UAT_Name.split(';');
        } else{
            contentUatUrls = Label.Tableau_Storelocation_Datasource_Prod_Name.split(';');

        }
        List<String> datasourcesIdList = new List<String>();
        HttpRequest request = new HttpRequest();
        request.setEndpoint(Label.Tableau_Storelocation_URL+'/api/3.4/sites/'+Label.Tableau_Storelocation_SiteId+'/datasources');
        request.setMethod('GET');
        request.setHeader('X-Tableau-Auth', token);
        String resultStr = '';
        Integer statusCode = 0;
        if (Test.isRunningTest()){
            resultStr = '<?xml version="1.0" encoding="UTF-8"?><tsResponse xmlns="http://tableau.com/api" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://tableau.com/api https://help.tableau.com/samples/en-us/rest_api/ts-api_3_6.xsd"><pagination pageNumber="1" pageSize="100" totalAvailable="26"/><datasources><datasource contentUrl="ChervonNA-UAT-datasource-storelocation" createdAt="2024-06-21T13:16:27Z" description="" encryptExtracts="false" id="c42323f3-9387-48ac-8687-4d90720fdfc6" isCertified="false" name="ChervonNA - UAT-datasource-storelocation" type="salesforce" updatedAt="2024-07-02T07:20:30Z" useRemoteQueryAgent="false" webpageUrl="https://prod-useast-b.online.tableau.com/#/site/cnatableau/datasources/63998358"><project id="dedd81e8-be68-442e-8be2-1feb5320d254" name="Chervon Store Insight - UAT"/><owner id="e0d11b55-124f-45d5-b056-249bc983c292"/><tags/></datasource><datasource contentUrl="ChervonNA-UAT-datasource-2nd" createdAt="2024-06-21T13:29:53Z" description="" encryptExtracts="false" id="2be7c362-e4e1-4c65-8243-84861f68f755" isCertified="false" name="ChervonNA - UAT-datasource-2nd" type="salesforce" updatedAt="2024-07-02T07:19:53Z" useRemoteQueryAgent="false" webpageUrl="https://prod-useast-b.online.tableau.com/#/site/cnatableau/datasources/63241222"><project id="dedd81e8-be68-442e-8be2-1feb5320d254" name="Chervon Store Insight - UAT"/><owner id="e0d11b55-124f-45d5-b056-249bc983c292"/><tags/></datasource><datasource contentUrl="ChervonNA-UAT-datasource" createdAt="2024-06-21T13:47:13Z" description="" encryptExtracts="false" id="a1dadc49-2ef0-40d1-94f3-f9cc34ed9633" isCertified="false" name="ChervonNA - UAT-datasource-myregion" type="salesforce" updatedAt="2024-07-02T07:51:24Z" useRemoteQueryAgent="false" webpageUrl="https://prod-useast-b.online.tableau.com/#/site/cnatableau/datasources/64413896"><project id="dedd81e8-be68-442e-8be2-1feb5320d254" name="Chervon Store Insight - UAT"/><owner id="e0d11b55-124f-45d5-b056-249bc983c292"/><tags/></datasource></datasources></tsResponse>';
            statusCode = 200;
        } else{
            HttpResponse response = new Http().send(request);
            resultStr = response.getBodyAsBlob().toString();
            statusCode = response.getStatusCode();
        }
        System.debug('statusCode:' + statusCode + 'body:' + resultStr);
        Dom.Document document = new Dom.Document();
        document.load(resultStr);
        Dom.XmlNode rootElement = document.getRootElement();
        for (Dom.XmlNode node : rootElement.getChildElements()){
            if (node.getName().equalsIgnoreCase('datasources')){
                for (Dom.XmlNode datasourceNode : node.getChildElements()){
                    String contentUrl = datasourceNode.getAttribute('name', null);
                    if (contentUatUrls.contains(contentUrl)){
                        datasourcesIdList.add(datasourceNode.getAttribute('id', null));
                    }
                }
            }
        }
        return datasourcesIdList;
    }
    public static void refreshDataSources(String token, List<String> datasourcesIdList){
        HttpRequest request = new HttpRequest();
        request.setMethod('POST');
        request.setHeader('X-Tableau-Auth', token);
        request.setHeader('Content-Type', 'application/xml');
        request.setBody('<tsRequest></tsRequest>');
        for (String datasourceId : datasourcesIdList){
            request.setEndpoint(Label.Tableau_Storelocation_URL+'/api/3.4/sites/'+Label.Tableau_Storelocation_SiteId+'/datasources/' + datasourceId + '/refresh');
            Integer statusCode = 0;
            if (Test.isRunningTest()){
                statusCode = 200;
            } else{
                HttpResponse response = new Http().send(request);
                statusCode = response.getStatusCode();
            }
            System.debug('refresh--------statusCode:' + statusCode );
        }
    }
}