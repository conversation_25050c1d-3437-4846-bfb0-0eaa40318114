public with sharing class CCM_CreateCustomerCreditCardController {
    @AuraEnabled
    public static DateResult searchData(Id recordId){
        DateResult result = new DateResult();
        Decimal yearInBusiness = 0;
        Decimal oneTime;
        String areThereCase = '';
        String paymentBehaviorCode = '';
        String currentYear = String.valueOf(System.today().year());
        Schema.SObjectType sObjectType = recordId.getSObjectType();
        Schema.DescribeSObjectResult describeResult = sObjectType.getDescribe();
        String objectName = describeResult.getName();
        Map<String, Payment_Term__mdt> paymentRuleMap = Util.getPaymentTermMap();
        List<Credit_Card__mdt> creditCardList = [SELECT End_Range__c,Final_Score__c,Name__c,Range__c,Score__c,Start_Range__c,Type__c,Weight__c FROM Credit_Card__mdt ORDER BY Type__c];
        List<Customer_Credit_Card__c> cred = [SELECT Id,Actual_Credit_Limit_Assigned__c,Explanation__c FROM Customer_Credit_Card__c WHERE (Customer_Name__c = :recordId OR Prospect_Name__c = :recordId) AND Effective_Year__c =:currentYear LIMIT 1];
        if(cred.size() > 0){
            result.creditId = cred[0].Id;
            result.actualCreditLimitAssigned = cred[0].Actual_Credit_Limit_Assigned__c;
            if(cred[0].Explanation__c != null){
                result.explanation = cred[0].Explanation__c;
            }
        }
        if (objectName == 'Lead') {
            Lead lead = [SELECT Id, Name,Company, CreatedDate, Address,Org_Code__c,TaxID__c,toLabel(Customer_Cluster__c),toLabel(Customer_Sub_Cluster__c),Credit_Limit__c,Risk_Code__c FROM Lead WHERE Id = :recordId];
            List<Sales_Program__c> sp = [SELECT Id,Payment_Term__c,Brands__c FROM Sales_Program__c WHERE Prospect__c = :recordId AND Approval_Status__c IN ('Approved','Pending for Approval')];
            List<Sales_Program__c> authBrand = [SELECT Id,Payment_Term__c FROM Sales_Program__c WHERE Prospect__c = :recordId AND Approval_Status__c ='Approved' ORDER BY Approved_Date__c   LIMIT 1];
            List<Sales_Program__c> authBrand1 = [SELECT Id,Payment_Term__c FROM Sales_Program__c WHERE Prospect__c = :recordId AND Approval_Status__c ='Pending for Approval' ORDER BY Approved_Date__c   LIMIT 1];
            Address addre = new Address();
            Decimal opePt = 0;
            if(lead.Address != null){
            addre= lead.Address;
            }
            result.actualAnnualSalesVolume = null;
            result.creditLimitType = 'New Customer';
            result.CustomerName = lead.Company;
            result.taxId = lead.TaxID__c;
            if(addre != null){
            result.address = addre.street + ' ' + addre.city + ','  + addre.state + ' ' + addre.postalCode + ' ' + addre.country;
            }
            result.legalEntity = lead.Org_Code__c;
            result.cluster = lead.Customer_Cluster__c;
            result.subCluster = lead.Customer_Sub_Cluster__c;
            result.currentCreditLimit = lead.Credit_Limit__c.setScale(2);
            List<Customer_Profile__c> cp = [SELECT Id, Name,Sales_potential__c,Chervon_Days_Beyond_Terms__c,OPE_Tool__c,PT_Tool__c,Years_in_business__c,Are_there_any_current_legal_case__c,Payment_Behavior_Code__c,On_time_Payment_10_days__c FROM Customer_Profile__c WHERE Prospect__c = :recordId ORDER BY Effective_Year__c DESC LIMIT 1];

            if(cp.size() > 0){
                result.forecastedAnnualSalesVolume = cp[0].Sales_potential__c;
                result.chervonDaysBeyondTerms = cp[0].Chervon_Days_Beyond_Terms__c;
                if(cp[0].OPE_Tool__c != null){
                    opePt +=  cp[0].OPE_Tool__c;
                }
                if(cp[0].PT_Tool__c != null){
                    opePt += cp[0].PT_Tool__c;
                }
                oneTime = cp[0].On_time_Payment_10_days__c;
                yearInBusiness = cp[0].Years_in_business__c;
                areThereCase = cp[0].Are_there_any_current_legal_case__c;
                paymentBehaviorCode = cp[0].Payment_Behavior_Code__c;
            }

            for(Sales_Program__c spr : sp){
                result.currentPaymentTerm = spr.Brands__c + '-' + spr.Payment_Term__c + '/';
            }
            if(String.isNotBlank(result.currentPaymentTerm) && result.currentPaymentTerm.endsWith('/')){
                result.currentPaymentTerm = result.currentPaymentTerm.removeEnd('/');
            }


            if(authBrand.size() > 0 && String.isNotBlank(authBrand[0].Payment_Term__c)){
                result.calculationTerms = paymentRuleMap.get(authBrand[0].Payment_Term__c).Payment_Leadtime__c;
            }else  if(authBrand1.size() > 0 && String.isNotBlank(authBrand1[0].Payment_Term__c)){
                result.calculationTerms = paymentRuleMap.get(authBrand1[0].Payment_Term__c).Payment_Leadtime__c;
            }
            if(result.calculationTerms > 0){
                result.purchasingAmountBasedOnTerm = (result.forecastedAnnualSalesVolume / 360 *  result.calculationTerms).setScale(2);
            }

            for(Credit_Card__mdt ccm : creditCardList){
                if(ccm.Type__c == 'Sales Volume'){
                    if((result.forecastedAnnualSalesVolume > ccm.Start_Range__c && result.forecastedAnnualSalesVolume <= ccm.End_Range__c) || (ccm.Start_Range__c == null && (result.forecastedAnnualSalesVolume <= ccm.End_Range__c || result.forecastedAnnualSalesVolume == null) ) || (result.forecastedAnnualSalesVolume > ccm.Start_Range__c && ccm.End_Range__c == null)){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore1 = ccm.Final_Score__c;
                        result.salesVolume = ccm.Name__c;
                        result.sorce1 = ccm.Score__c;
                    }
                }else if(ccm.Type__c == 'Tools Purchase from Chervon'){
                    if((opePt > ccm.Start_Range__c && opePt <= ccm.End_Range__c) || (ccm.Start_Range__c == null && (opePt <= ccm.End_Range__c || opePt == null) ) || (opePt > ccm.Start_Range__c && ccm.End_Range__c == null)){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore2 = ccm.Final_Score__c;
                        result.toolsPurchaseFromChervon = ccm.Name__c;
                        result.sorce2 = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Days Beyond Terms'){
                    if((result.chervonDaysBeyondTerms >= ccm.Start_Range__c && result.chervonDaysBeyondTerms <= ccm.End_Range__c) || (ccm.Start_Range__c == null && (result.chervonDaysBeyondTerms <= ccm.End_Range__c || result.chervonDaysBeyondTerms == null) ) || (result.chervonDaysBeyondTerms >= ccm.Start_Range__c && ccm.End_Range__c == null)){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore3 = ccm.Final_Score__c;
                        result.daysBeyondTerms = ccm.Name__c;
                        result.sorce3 = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Years in Business'){
                    if((yearInBusiness >= ccm.Start_Range__c && yearInBusiness < ccm.End_Range__c) || (ccm.Start_Range__c == null && (yearInBusiness < ccm.End_Range__c || yearInBusiness == null) ) || (yearInBusiness >= ccm.Start_Range__c && ccm.End_Range__c == null)){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore4 = ccm.Final_Score__c;
                        result.yearsInBusiness = ccm.Name__c;
                        result.sorce4 = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Business with Chervon'){
                    if(ccm.Start_Range__c == null  && ccm.End_Range__c == null){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore5 = ccm.Final_Score__c;
                        result.businessWithChervon = ccm.Name__c;
                        result.sorce5 = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'On time Payment'){
                    if((oneTime >= ccm.Start_Range__c && oneTime < ccm.End_Range__c) || (ccm.Start_Range__c == null && (oneTime < ccm.End_Range__c ) ) || (oneTime >= ccm.Start_Range__c && ccm.End_Range__c == null) || (oneTime == null && ccm.Start_Range__c == null && ccm.End_Range__c == null)){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore6 = ccm.Final_Score__c;
                        result.onTimePayment = ccm.Name__c;
                        result.sorce6 = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Are there any current legal case'){
                    if(areThereCase == ccm.Range__c){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore7 = ccm.Final_Score__c;
                        result.areThereAnyCurentLegalCase = ccm.Name__c;
                        result.sorce7 = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Payment Behavior Code'){
                    if(paymentBehaviorCode == ccm.Range__c){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore8 = ccm.Final_Score__c;
                        result.paymentBehaviorCode = ccm.Name__c;
                        result.sorce8 = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Credit Report Risk Code'){
                    if(lead.Risk_Code__c == ccm.Range__c){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore9 = ccm.Final_Score__c;
                        result.creditReportRiskCode = ccm.Name__c;
                        result.sorce9 = ccm.Score__c;
                    }
                }else{
                    if(result.totalScore >= ccm.Start_Range__c && result.totalScore < ccm.End_Range__c){
                        result.maximumRecommendedLimit = result.purchasingAmountBasedOnTerm * ccm.Weight__c / 100;
                    }

                }
            }
        } else if (objectName == 'Account') {
            Account account = [SELECT Id, Name,AccountNumber, CreatedDate, BillingAddress,Org_Code__c,TaxID__c,toLabel(Customer_Cluster__c),toLabel(Customer_Sub_Cluster__c),Credit_Limit__c,Risk_Code__c FROM Account WHERE Id = :recordId];
            List<Order_Item__c> orderItemList = [Select Order__c,Order__r.Date_Order__c,Order__r.EffectiveDate,Price__c ,Order__r.AccountId,Order_Quantity__c FROM Order_Item__c WHERE Order__r.AccountId =:recordId AND Line_Status__c !='CANCELLED' AND Order_Quantity__c >0 ORDER BY Order__r.EffectiveDate LIMIT 1];
            List<Customer_Profile__c> cp = [SELECT Id, Name,Sales_potential__c,Chervon_Days_Beyond_Terms__c,OPE_Tool__c,PT_Tool__c,Years_in_business__c,Are_there_any_current_legal_case__c,Payment_Behavior_Code__c,Actual_Inputs__c,OPE_PT_Category_Revenue__c FROM Customer_Profile__c WHERE Customer__c = :recordId ORDER BY Effective_Year__c DESC LIMIT 1];
            List<Sales_Program__c> sp = [SELECT Id,Payment_Term__c,Brands__c FROM Sales_Program__c WHERE Customer__c = :recordId AND Approval_Status__c = 'Approved'];
            List<Customer_Credit_Report_Dashboard__c> ccrd = [SELECT Id,Name,DSO__c,Current_10days__c,Chervon_Days_Beyond_Terms__c FROM Customer_Credit_Report_Dashboard__c WHERE Account_Number__c = :account.AccountNumber];
            Integer diffYear = 0;
            if(System.today().year()- account.CreatedDate.year() != 1){
                if(System.today().year()- account.CreatedDate.year() != 0){
                    diffYear = System.today().year()- account.CreatedDate.year();
                }
            }else{
                if(System.today().month() == account.CreatedDate.month()){
                    if(System.today().day() == account.CreatedDate.day()){
                        diffYear = 1;
                    }else if(System.today().day() > account.CreatedDate.day()){
                        diffYear = 1;
                    }
                }else if(System.today().month() > account.CreatedDate.month()){
                    diffYear = 1;
                }
            }

            Integer diffMonth = 0;
            if(System.today().month() == account.CreatedDate.month()){
                diffMonth = 1;
            }else if(System.today().month() > account.CreatedDate.month()){
                diffMonth = System.today().month() - account.CreatedDate.month() + 1;
            }else{
                diffMonth = System.today().month() - account.CreatedDate.month() + 12;
            }
            Decimal opePt = 0;
            Decimal totalAmount = 0;
            Integer orderDiffYear = 0;
            Address addre = new Address();
            if(account.BillingAddress != null){
            addre= account.BillingAddress;
            }
            result.creditLimitType = 'Credit Review';
            result.CustomerName = account.Name;
            result.taxId = account.TaxID__c;
            if(addre != null){
            result.address = addre.street + ' ' + addre.city + ','  + addre.state + ' ' + addre.postalCode + ' ' + addre.country;
            }
            result.legalEntity = account.Org_Code__c;
            result.cluster = account.Customer_Cluster__c;
            result.subCluster = account.Customer_Sub_Cluster__c;

            Date currentDate = System.today();
            Date lastDate = Date.newInstance(System.today().year() - 1, System.today().month(), System.today().day());
            String invoiceSource = 'CNA_Auto_Invoice';
            if (account.Org_Code__c == CCM_Constants.ORG_CODE_CCA) {
                invoiceSource = 'CA_Auto_Invoice';
            }
            for (List<AggregateResult> objARs : [
                SELECT Invoice__r.Customer__c,  SUM(Amount__c) SUM_AMOUNT
                FROM Invoice_Item__c
                WHERE
                    Invoice__r.Customer__c = :recordId
                    AND Invoice__r.Invoice_Source__c = :invoiceSource
                    AND Is_Parts__c = FALSE
                    AND Is_parts_EBS__c = FALSE
                    AND Catalog_Item_Text__c != '**********'
                    AND Catalog_Item_Text__c != 'V20'
                    AND Invoice_Date__c >= :lastDate
                    AND Invoice_Date__c <= :currentDate
                WITH SECURITY_ENFORCED
                GROUP BY Invoice__r.Customer__c
            ]) {
                for(AggregateResult objAR : objARs){
                    totalAmount = (Decimal)objAR.get('SUM_AMOUNT');
                }
            }
            if(account.Credit_Limit__c != null){
                result.currentCreditLimit = account.Credit_Limit__c.setScale(2);
            }
            result.forecastedAnnualSalesVolume = null;
            if(ccrd.size() > 0){
                result.calculationTerms = ccrd[0].DSO__c;
                oneTime = ccrd[0].Current_10days__c;
                if(ccrd[0].Chervon_Days_Beyond_Terms__c != null && ccrd[0].DSO__c != null){
                    result.chervonDaysBeyondTerms = ccrd[0].Chervon_Days_Beyond_Terms__c - ccrd[0].DSO__c;
                }
            }
            if(orderItemList.size() > 0){
                orderDiffYear = System.today().year()- orderItemList[0].Order__r.EffectiveDate.year();
            }
            if(cp.size() > 0){
                if(diffYear > 0){
                    result.actualAnnualSalesVolume = totalAmount;
                }else{
                    result.actualAnnualSalesVolume = (totalAmount / diffMonth *12).setScale(2);
                }
                if(cp[0].OPE_PT_Category_Revenue__c > 0){
                    opePt = (totalAmount / cp[0].OPE_PT_Category_Revenue__c).setScale(2);
                }
                yearInBusiness = cp[0].Years_in_business__c;
                areThereCase = cp[0].Are_there_any_current_legal_case__c;
                paymentBehaviorCode = cp[0].Payment_Behavior_Code__c;
            }
            for(Sales_Program__c spr : sp){
                result.currentPaymentTerm += spr.Brands__c + '-' + spr.Payment_Term__c + '/';
            }
            if(String.isNotBlank(result.currentPaymentTerm) && result.currentPaymentTerm.endsWith('/')){
                result.currentPaymentTerm = result.currentPaymentTerm.removeEnd('/');
            }
            if(result.calculationTerms > 0){
                result.purchasingAmountBasedOnTerm = (result.actualAnnualSalesVolume / 360 *  result.calculationTerms).setScale(2);
            }
            for(Credit_Card__mdt ccm : creditCardList){
                if(ccm.Type__c == 'Sales Volume'){
                    if((result.actualAnnualSalesVolume > ccm.Start_Range__c && result.actualAnnualSalesVolume <= ccm.End_Range__c) || (ccm.Start_Range__c == null && (result.actualAnnualSalesVolume <= ccm.End_Range__c || result.actualAnnualSalesVolume == null) ) || (result.actualAnnualSalesVolume > ccm.Start_Range__c && ccm.End_Range__c == null)){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore1 = ccm.Final_Score__c;
                        result.salesVolume = ccm.Name__c;
                        result.sorce1 = ccm.Score__c;
                    }
                }else if(ccm.Type__c == 'Tools Purchase from Chervon'){
                    if((opePt > ccm.Start_Range__c && opePt <= ccm.End_Range__c) || (ccm.Start_Range__c == null && (opePt <= ccm.End_Range__c || opePt == null) ) || (opePt > ccm.Start_Range__c && ccm.End_Range__c == null)){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore2 = ccm.Final_Score__c;
                        result.toolsPurchaseFromChervon = ccm.Name__c;
                        result.sorce2 = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Days Beyond Terms'){
                    if((result.chervonDaysBeyondTerms >= ccm.Start_Range__c && result.chervonDaysBeyondTerms <= ccm.End_Range__c) || (ccm.Start_Range__c == null && (result.chervonDaysBeyondTerms <= ccm.End_Range__c || result.chervonDaysBeyondTerms == null) ) || (result.chervonDaysBeyondTerms >= ccm.Start_Range__c && ccm.End_Range__c == null)){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore3 = ccm.Final_Score__c;
                        result.daysBeyondTerms = ccm.Name__c;
                        result.sorce3 = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Years in Business'){
                    if((yearInBusiness >= ccm.Start_Range__c && yearInBusiness < ccm.End_Range__c) || (ccm.Start_Range__c == null && (yearInBusiness < ccm.End_Range__c || yearInBusiness == null) ) || (yearInBusiness >= ccm.Start_Range__c && ccm.End_Range__c == null)){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore4 = ccm.Final_Score__c;
                        result.yearsInBusiness = ccm.Name__c;
                        result.sorce4 = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Business with Chervon'){
                    if((orderDiffYear >= ccm.Start_Range__c && orderDiffYear < ccm.End_Range__c) || (ccm.Start_Range__c == null && (orderDiffYear < ccm.End_Range__c || orderDiffYear == null) ) || (orderDiffYear >= ccm.Start_Range__c && ccm.End_Range__c == null)){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore5 = ccm.Final_Score__c;
                        result.businessWithChervon = ccm.Name__c;
                        result.sorce5 = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'On time Payment'){
                    if((oneTime >= ccm.Start_Range__c && oneTime < ccm.End_Range__c) || (ccm.Start_Range__c == null && (oneTime < ccm.End_Range__c ) ) || (oneTime >= ccm.Start_Range__c && ccm.End_Range__c == null) || (oneTime == null && ccm.Start_Range__c == null && ccm.End_Range__c == null)){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore6 = ccm.Final_Score__c;
                        result.onTimePayment = ccm.Name__c;
                        result.sorce6 = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Are there any current legal case'){
                    if(areThereCase == ccm.Range__c){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore7 = ccm.Final_Score__c;
                        result.areThereAnyCurentLegalCase = ccm.Name__c;
                        result.sorce7 = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Payment Behavior Code'){
                    if(paymentBehaviorCode == ccm.Range__c){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore8 = ccm.Final_Score__c;
                        result.paymentBehaviorCode = ccm.Name__c;
                        result.sorce8 = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Credit Report Risk Code'){
                    if(account.Risk_Code__c == ccm.Range__c){
                        result.totalScore += ccm.Final_Score__c;
                        result.finalScore9 = ccm.Final_Score__c;
                        result.creditReportRiskCode = ccm.Name__c;
                        result.sorce9 = ccm.Score__c;
                    }
                }else{
                    if(result.totalScore >= ccm.Start_Range__c && result.totalScore < ccm.End_Range__c){
                        result.maximumRecommendedLimit = result.purchasingAmountBasedOnTerm * ccm.Weight__c / 100;
                    }

                }
            }
        }
        return result;
    }

    @AuraEnabled
    public static void saveCreditCard(Id recordId,String creditData){
        DateResult resultData = (DateResult) JSON.deserialize(creditData, DateResult.class);
        Customer_Credit_Card__c creditCard = new Customer_Credit_Card__c();
        creditCard.CurrencyIsoCode = 'USD';
        Account account;
        Lead lead;
        if(resultData.creditId != null){
            creditCard.Id = resultData.creditId;
        }
        if(resultData.creditLimitType == 'Credit Review'){
            creditCard.Customer_Name__c = recordId;
            Account acc = [SELECT Id,Org_Code__c FROM Account WHERE Id =:recordId];
            if (acc.Org_Code__c == CCM_Constants.ORG_CODE_CCA) {
                creditCard.CurrencyIsoCode = 'CAD';
            }
            account = new Account();
            account.Id = recordId;
            if(resultData.actualCreditLimitAssigned != null && resultData.actualCreditLimitAssigned != 0){
                account.Credit_Limit__c = resultData.actualCreditLimitAssigned;
            }
            // else{
            //     account.Credit_Limit__c = resultData.maximumRecommendedLimit;
            // }
        }else{
            creditCard.Prospect_Name__c = recordId;
            Lead led = [SELECT Id,Org_Code__c FROM Lead WHERE Id =:recordId];
            if (led.Org_Code__c == CCM_Constants.ORG_CODE_CCA) {
                creditCard.CurrencyIsoCode = 'CAD';
            }
            lead = new Lead();
            lead.Id = recordId;
            if(resultData.actualCreditLimitAssigned != null && resultData.actualCreditLimitAssigned != 0){
                lead.Credit_Limit__c = resultData.actualCreditLimitAssigned;
            }
            // else{
            //     lead.Credit_Limit__c = resultData.maximumRecommendedLimit;
            // }
        }
        creditCard.Credit_Limit_Type__c = resultData.creditLimitType;
        creditCard.Current_Payment_Term__c = resultData.currentPaymentTerm;
        creditCard.Tax_ID__c = resultData.taxId;
        creditCard.Additional_Payment_Terms__c = resultData.additionalPaymentTerms;
        creditCard.Order_Type__c = resultData.orderType;
        creditCard.Address__c = resultData.address;
        creditCard.Legal_Entity__c = resultData.legalEntity;
        creditCard.Cluster__c = resultData.cluster;
        creditCard.Sub_Cluster__c = resultData.subCluster;
        creditCard.Sales_volume__c = resultData.salesVolume;
        creditCard.Tools_Purchase_From_Chervon__c = resultData.toolsPurchaseFromChervon;
        creditCard.Days_Beyond_Terms__c = resultData.daysBeyondTerms;
        creditCard.Years_In_Business__c = resultData.yearsInBusiness;
        creditCard.Business_With_Chervon__c = resultData.businessWithChervon;
        creditCard.On_Time_Payment_10_days__c = resultData.onTimePayment;
        creditCard.Are_There_Any_Current_Legal_Case__c = resultData.areThereAnyCurentLegalCase;
        creditCard.Payment_Behavior_Code__c = resultData.paymentBehaviorCode;
        creditCard.Credit_Report_Risk_Code__c = resultData.creditReportRiskCode;
        creditCard.Explanation__c = resultData.explanation;
        creditCard.Current_Credit_Limit__c = resultData.currentCreditLimit;
        creditCard.Calculation_Terms__c = resultData.calculationTerms;
        creditCard.Actual_Annual_Sales_Volume__c = resultData.actualAnnualSalesVolume;
        creditCard.Forecasted_Annual_Sales_Volume__c = resultData.forecastedAnnualSalesVolume;
        creditCard.Purchasing_Amount_Based_On_Term__c = resultData.purchasingAmountBasedOnTerm;
        creditCard.Chervon_Days_Beyond_Terms__c = resultData.chervonDaysBeyondTerms;
        creditCard.Score1__c = resultData.sorce1;
        creditCard.Score2__c = resultData.sorce2;
        creditCard.Score3__c = resultData.sorce3;
        creditCard.Score4__c = resultData.sorce4;
        creditCard.Score5__c = resultData.sorce5;
        creditCard.Score6__c = resultData.sorce6;
        creditCard.Score7__c = resultData.sorce7;
        creditCard.Score8__c = resultData.sorce8;
        creditCard.Score9__c = resultData.sorce9;
        creditCard.FinalScore1__c = resultData.finalScore1;
        creditCard.FinalScore2__c = resultData.finalScore2;
        creditCard.FinalScore3__c = resultData.finalScore3;
        creditCard.FinalScore4__c = resultData.finalScore4;
        creditCard.FinalScore5__c = resultData.finalScore5;
        creditCard.FinalScore6__c = resultData.finalScore6;
        creditCard.FinalScore7__c = resultData.finalScore7;
        creditCard.FinalScore8__c = resultData.finalScore8;
        creditCard.FinalScore9__c = resultData.finalScore9;
        creditCard.Total_Score__c = resultData.totalScore;
        creditCard.Maximum_Recommended_Limit__c  = resultData.maximumRecommendedLimit;
        Integer currentYear = System.today().year();
        creditCard.Effective_Year__c = String.valueOf(currentYear);
        if(resultData.actualCreditLimitAssigned == 0){
            creditCard.Actual_Credit_Limit_Assigned__c = null;
        }else{
            creditCard.Actual_Credit_Limit_Assigned__c = resultData.actualCreditLimitAssigned;
        }
        if(lead != null){
            upsert lead;
        }
        if(account != null){
            upsert account;
        }
        upsert creditCard;

    }


    public Class DateResult {
        @AuraEnabled public String creditId;
        @AuraEnabled public String creditLimitType;
        @AuraEnabled public String currentPaymentTerm;
        @AuraEnabled public String customerName;
        @AuraEnabled public String taxId;
        @AuraEnabled public String additionalPaymentTerms;
        @AuraEnabled public String orderType;
        @AuraEnabled public String address;
        @AuraEnabled public String legalEntity;
        @AuraEnabled public String cluster;
        @AuraEnabled public String subCluster;
        @AuraEnabled public String salesVolume;
        @AuraEnabled public String toolsPurchaseFromChervon;
        @AuraEnabled public String daysBeyondTerms;
        @AuraEnabled public String yearsInBusiness;
        @AuraEnabled public String businessWithChervon;
        @AuraEnabled public String onTimePayment;
        @AuraEnabled public String areThereAnyCurentLegalCase;
        @AuraEnabled public String paymentBehaviorCode;
        @AuraEnabled public String creditReportRiskCode;
        @AuraEnabled public String explanation;
        @AuraEnabled public Decimal currentCreditLimit;
        @AuraEnabled public Decimal calculationTerms;
        @AuraEnabled public Decimal actualAnnualSalesVolume;
        @AuraEnabled public Decimal forecastedAnnualSalesVolume;
        @AuraEnabled public Decimal purchasingAmountBasedOnTerm;
        @AuraEnabled public Decimal chervonDaysBeyondTerms;
        @AuraEnabled public Decimal sorce1;
        @AuraEnabled public Decimal sorce2;
        @AuraEnabled public Decimal sorce3;
        @AuraEnabled public Decimal sorce4;
        @AuraEnabled public Decimal sorce5;
        @AuraEnabled public Decimal sorce6;
        @AuraEnabled public Decimal sorce7;
        @AuraEnabled public Decimal sorce8;
        @AuraEnabled public Decimal sorce9;
        @AuraEnabled public Decimal finalScore1;
        @AuraEnabled public Decimal finalScore2;
        @AuraEnabled public Decimal finalScore3;
        @AuraEnabled public Decimal finalScore4;
        @AuraEnabled public Decimal finalScore5;
        @AuraEnabled public Decimal finalScore6;
        @AuraEnabled public Decimal finalScore7;
        @AuraEnabled public Decimal finalScore8;
        @AuraEnabled public Decimal finalScore9;
        @AuraEnabled public Decimal totalScore;
        @AuraEnabled public Decimal maximumRecommendedLimit;
        @AuraEnabled public Decimal actualCreditLimitAssigned;


        public DateResult(){
            this.creditLimitType = '';
            this.currentPaymentTerm = '';
            this.CustomerName = '';
            this.CurrentCreditLimit = 0;
            this.taxId = '';
            this.additionalPaymentTerms = '';
            this.orderType = '';
            this.calculationTerms = 0;
            this.address = '';
            this.actualAnnualSalesVolume = 0;
            this.legalEntity = '';
            this.forecastedAnnualSalesVolume = 0;
            this.cluster = '';
            this.purchasingAmountBasedOnTerm = 0;
            this.subCluster = '';
            this.chervonDaysBeyondTerms = 0;
            this.salesVolume = '';
            this.toolsPurchaseFromChervon = '';
            this.daysBeyondTerms = '';
            this.yearsInBusiness = '';
            this.businessWithChervon = '';
            this.onTimePayment = '';
            this.areThereAnyCurentLegalCase = '';
            this.paymentBehaviorCode = '';
            this.creditReportRiskCode = '';
            this.explanation = '';
            this.sorce1 = 0;
            this.sorce2 = 0;
            this.sorce3 = 0;
            this.sorce4 = 0;
            this.sorce5 = 0;
            this.sorce6 = 0;
            this.sorce7 = 0;
            this.sorce8 = 0;
            this.sorce9 = 0;
            this.finalScore1 = 0;
            this.finalScore2 = 0;
            this.finalScore3 = 0;
            this.finalScore4 = 0;
            this.finalScore5 = 0;
            this.finalScore6 = 0;
            this.finalScore7 = 0;
            this.finalScore8 = 0;
            this.finalScore9 = 0;
            this.totalScore = 0;
            this.maximumRecommendedLimit = 0;
            this.actualCreditLimitAssigned = 0;

        }
    }

}