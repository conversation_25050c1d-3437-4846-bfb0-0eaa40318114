/**
 * @description: Test class for CCM_AceReportCtl, CCM_AceReportSchedule,
 * CCM_ACE2ndTierDealerBatch, CCM_ACENetworkAnd2ndTierStoreBatch,
 * CCM_StoreLocationSummaryBatch, CCM_StoreRankBatch,CCM_StoreRankNullValueBatch,CCM_StoreLocationRefreshTableau
 * @date: 2024-01-23
 */
@isTest
public with sharing class CCM_AceReportCtlTest{
    @TestSetup
    static void makeData(){
        // 关闭Account Trigger、POS Trigger
        CCM_SharingUtil.isSharingOnly = true;
        CCM_DealerLocationUpdateOfPosDataHandler.isRun = false;
        // ACE HARDWARE
        Account objAccount0376 = new Account();
        objAccount0376.Name = '0376 ACE HARDWARE';
        objAccount0376.RecordTypeId = CCM_Constants.CUSTOMER_RECORD_TYPE_CHANNEL_ID;
        objAccount0376.TaxID__c = '********';
        objAccount0376.AccountNumber = '0376';

        Account objAce2ndTierDealer01 = new Account();
        objAce2ndTierDealer01.Name = 'ACE 2nd Tier Dealer - 01';
        objAce2ndTierDealer01.RecordTypeId = CCM_Constants.CUSTOMER_RECORD_TYPE_CHANNEL_ID;
        objAce2ndTierDealer01.TaxID__c = '********';

        Account objAce2ndTierDealer02 = new Account();
        objAce2ndTierDealer02.Name = 'ACE 2nd Tier Dealer - 02';
        objAce2ndTierDealer02.RecordTypeId = CCM_Constants.CUSTOMER_RECORD_TYPE_CHANNEL_ID;
        objAce2ndTierDealer02.TaxID__c = '********';

        Account objAccountNotAce01 = new Account();
        objAccountNotAce01.Name = 'Not ACE 2nd Tier Dealer - 01';
        objAccountNotAce01.RecordTypeId = CCM_Constants.CUSTOMER_RECORD_TYPE_CHANNEL_ID;
        objAccountNotAce01.TaxID__c = '********';
        objAccountNotAce01.ACE_2nd_Tier_Dealer__c = true;
        Database.insert (new List<Account>{ objAccount0376, objAce2ndTierDealer01, objAce2ndTierDealer02, objAccountNotAce01 });

        Sales_Hierarchy__c objSalesHierarchy01 = new Sales_Hierarchy__c();
        objSalesHierarchy01.X1st_tier_dealer__c = objAccount0376.Id;
        objSalesHierarchy01.X2st_tier_dealer__c = objAce2ndTierDealer01.Id;
        objSalesHierarchy01.Approval_Status__c = 'Approved';
        objSalesHierarchy01.Active__c = true;

        Sales_Hierarchy__c objSalesHierarchy02 = new Sales_Hierarchy__c();
        objSalesHierarchy02.X1st_tier_dealer__c = objAccount0376.Id;
        objSalesHierarchy02.X2st_tier_dealer__c = objAce2ndTierDealer02.Id;
        objSalesHierarchy01.Approval_Status__c = 'Approved';
        objSalesHierarchy01.Active__c = true;
        Database.insert (new List<Sales_Hierarchy__c>{ objSalesHierarchy01, objSalesHierarchy02 });

        // Store
        Account objAceStore = new Account();
        objAceStore.Name = 'ACE HARDWARE Store';
        objAceStore.RecordTypeId = CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID;
        objAceStore.Related_Entity__c = objAccount0376.Id;
        objAceStore.ACE_Network__c = true;

        Account objNotAceStore = new Account();
        objNotAceStore.Name = 'Not ACE HARDWARE Store';
        objNotAceStore.RecordTypeId = CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID;
        objNotAceStore.Related_Entity__c = objAccountNotAce01.Id;
        objNotAceStore.ACE_Network__c = true;
        objNotAceStore.X2nd_Tier_Store__c = true;

        Account objAce2ndTierStore01 = new Account();
        objAce2ndTierStore01.Name = 'ACE HARDWARE 2nd Tier Store 01';
        objAce2ndTierStore01.RecordTypeId = CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID;
        objAce2ndTierStore01.Related_Entity__c = objAce2ndTierDealer01.Id;
        objAce2ndTierStore01.EGO_Rolling_1_Year_POS_Amount__c = 100;

        Account objAce2ndTierStore02 = new Account();
        objAce2ndTierStore02.Name = 'ACE HARDWARE 2nd Tier Store 02';
        objAce2ndTierStore02.RecordTypeId = CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID;
        objAce2ndTierStore02.Related_Entity__c = objAce2ndTierDealer01.Id;
        objNotAceStore.ACE_Network__c = true;
        Database.insert (new List<Account>{ objAceStore, objNotAceStore, objAce2ndTierStore01, objAce2ndTierStore02 });

        // ACE Store POS Data
        POS_Data__c objAceStoreThisYearPos01 = new POS_Data__c(
            Customer_Store_Location__c = objAceStore.Id, 
            Year__c = Date.today().year(), 
            Month__c = '1', 
            Brand__c = 'EGO', 
            Model_Number__c = 'EGO-0001'
        );
        objAceStoreThisYearPos01.Sales_Amount__c = 50;
        objAceStoreThisYearPos01.Sales_Unit__c = 50;
        POS_Data__c objAceStoreThisYearPos02 = new POS_Data__c(
            Customer_Store_Location__c = objAceStore.Id, 
            Year__c = Date.today().year(), 
            Month__c = '1', 
            Brand__c = 'SKIL', 
            Model_Number__c = 'SKIL-0001'
        );
        objAceStoreThisYearPos02.Sales_Amount__c = 100;
        objAceStoreThisYearPos02.Sales_Unit__c = 100;
        POS_Data__c objAceStoreLastYearPos01 = new POS_Data__c(
            Customer_Store_Location__c = objAceStore.Id, 
            Year__c = Date.today().year() - 1, 
            Month__c = '6', 
            Brand__c = 'EGO', 
            Model_Number__c = 'EGO-0002'
        );
        objAceStoreLastYearPos01.Sales_Amount__c = 50;
        objAceStoreLastYearPos01.Sales_Unit__c = 50;
        POS_Data__c objAceStoreLastYearPos02 = new POS_Data__c(
            Customer_Store_Location__c = objAceStore.Id, 
            Year__c = Date.today().year() - 1, 
            Month__c = '6', 
            Brand__c = 'SKIL', 
            Model_Number__c = 'SKIL-0002'
        );
        objAceStoreLastYearPos02.Sales_Amount__c = 100;
        objAceStoreLastYearPos02.Sales_Unit__c = 100;
        POS_Data__c objAceStoreLastYearPos03 = new POS_Data__c(
            Customer_Store_Location__c = objAceStore.Id, 
            Year__c = Date.today().year() - 1, 
            Month__c = '1', 
            Brand__c = 'EGO', 
            Model_Number__c = 'EGO-0001'
        );
        objAceStoreLastYearPos03.Sales_Amount__c = 50;
        objAceStoreLastYearPos03.Sales_Unit__c = 50;
        POS_Data__c objAceStoreLastYearPos04 = new POS_Data__c(
            Customer_Store_Location__c = objAceStore.Id, 
            Year__c = Date.today().year() - 1, 
            Month__c = '1', 
            Brand__c = 'SKIL', 
            Model_Number__c = 'SKIL-0001'
        );
        objAceStoreLastYearPos04.Sales_Amount__c = 100;
        objAceStoreLastYearPos04.Sales_Unit__c = 100;
        // ACE 2nd Store POS Data
        POS_Data__c objAce2ndStoreThisYearPos01 = new POS_Data__c(
            Customer_Store_Location__c = objAce2ndTierStore02.Id, 
            Year__c = Date.today().year(), 
            Month__c = '1', 
            Brand__c = 'EGO', 
            Model_Number__c = 'EGO-0001'
        );
        objAce2ndStoreThisYearPos01.Sales_Amount__c = 50;
        objAce2ndStoreThisYearPos01.Sales_Unit__c = 50;
        POS_Data__c objAce2ndStoreThisYearPos02 = new POS_Data__c(
            Customer_Store_Location__c = objAce2ndTierStore02.Id, 
            Year__c = Date.today().year(), 
            Month__c = '1', 
            Brand__c = 'SKIL', 
            Model_Number__c = 'SKIL-0001'
        );
        objAce2ndStoreThisYearPos02.Sales_Amount__c = 100;
        objAce2ndStoreThisYearPos02.Sales_Unit__c = 100;
        POS_Data__c objAce2ndStoreLastYearPos01 = new POS_Data__c(
            Customer_Store_Location__c = objAce2ndTierStore02.Id, 
            Year__c = Date.today().year() - 1, 
            Month__c = '6', 
            Brand__c = 'EGO', 
            Model_Number__c = 'EGO-0002'
        );
        objAce2ndStoreLastYearPos01.Sales_Amount__c = 50;
        objAce2ndStoreLastYearPos01.Sales_Unit__c = 50;
        POS_Data__c objAce2ndStoreLastYearPos02 = new POS_Data__c(
            Customer_Store_Location__c = objAce2ndTierStore02.Id, 
            Year__c = Date.today().year() - 1, 
            Month__c = '6', 
            Brand__c = 'SKIL', 
            Model_Number__c = 'SKIL-0002'
        );
        objAce2ndStoreLastYearPos02.Sales_Amount__c = 100;
        objAce2ndStoreLastYearPos02.Sales_Unit__c = 100;
        POS_Data__c objAce2ndStoreLastYearPos03 = new POS_Data__c(
            Customer_Store_Location__c = objAce2ndTierStore02.Id, 
            Year__c = Date.today().year() - 1, 
            Month__c = '1', 
            Brand__c = 'EGO', 
            Model_Number__c = 'EGO-0001'
        );
        objAce2ndStoreLastYearPos03.Sales_Amount__c = 50;
        objAce2ndStoreLastYearPos03.Sales_Unit__c = 50;
        POS_Data__c objAce2ndStoreLastYearPos04 = new POS_Data__c(
            Customer_Store_Location__c = objAce2ndTierStore02.Id, 
            Year__c = Date.today().year() - 1, 
            Month__c = '1', 
            Brand__c = 'SKIL', 
            Model_Number__c = 'SKIL-0001'
        );
        objAce2ndStoreLastYearPos04.Sales_Amount__c = 100;
        objAce2ndStoreLastYearPos04.Sales_Unit__c = 100;
        Database.insert (new List<POS_Data__c>{ objAceStoreThisYearPos01, objAceStoreThisYearPos02, objAceStoreLastYearPos01, objAceStoreLastYearPos02, objAceStoreLastYearPos03, objAceStoreLastYearPos04, objAce2ndStoreThisYearPos01, objAce2ndStoreThisYearPos02, objAce2ndStoreLastYearPos01, objAce2ndStoreLastYearPos02, objAce2ndStoreLastYearPos03, objAce2ndStoreLastYearPos04 });

        //retail purchase:
        RetailPurchase__c objAceStoreThisYearRP01 = new RetailPurchase__c(
            Customer_Store_Location__c = objAceStore.Id, 
            Year__c = Date.today().year(), 
            Month__c = '1', 
            Brand__c = 'EGO', 
            Model_Number__c = 'EGO-0001'
        );
        objAceStoreThisYearRP01.Cost__c = 50;
        objAceStoreThisYearRP01.Units__c = 50;
        RetailPurchase__c objAceStoreThisYearRP02 = new RetailPurchase__c(
            Customer_Store_Location__c = objAceStore.Id, 
            Year__c = Date.today().year(), 
            Month__c = '1', 
            Brand__c = 'SKIL', 
            Model_Number__c = 'SKIL-0001'
        );
        objAceStoreThisYearRP02.Cost__c = 100;
        objAceStoreThisYearRP02.Units__c = 100;
        RetailPurchase__c objAceStoreLastYearRP01 = new RetailPurchase__c(
            Customer_Store_Location__c = objAceStore.Id, 
            Year__c = Date.today().year() - 1, 
            Month__c = '6', 
            Brand__c = 'EGO', 
            Model_Number__c = 'EGO-0002'
        );
        objAceStoreLastYearRP01.Cost__c = 50;
        objAceStoreLastYearRP01.Units__c = 50;
        RetailPurchase__c objAceStoreLastYearRP02 = new RetailPurchase__c(
            Customer_Store_Location__c = objAceStore.Id, 
            Year__c = Date.today().year() - 1, 
            Month__c = '6', 
            Brand__c = 'SKIL', 
            Model_Number__c = 'SKIL-0002'
        );
        objAceStoreLastYearRP02.Cost__c = 100;
        objAceStoreLastYearRP02.Units__c = 100;
        RetailPurchase__c objAceStoreLastYearRP03 = new RetailPurchase__c(
            Customer_Store_Location__c = objAceStore.Id, 
            Year__c = Date.today().year() - 1, 
            Month__c = '1', 
            Brand__c = 'EGO', 
            Model_Number__c = 'EGO-0001'
        );
        objAceStoreLastYearRP03.Cost__c = 50;
        objAceStoreLastYearRP03.Units__c = 50;
        RetailPurchase__c objAceStoreLastYearRP04 = new RetailPurchase__c(
            Customer_Store_Location__c = objAceStore.Id, 
            Year__c = Date.today().year() - 1, 
            Month__c = '1', 
            Brand__c = 'SKIL', 
            Model_Number__c = 'SKIL-0001'
        );
        objAceStoreLastYearRP04.Cost__c = 100;
        objAceStoreLastYearRP04.Units__c = 100;
        // ACE 2nd Store RP Data
        RetailPurchase__c objAce2ndStoreThisYearRP01 = new RetailPurchase__c(
            Customer_Store_Location__c = objAce2ndTierStore02.Id, 
            Year__c = Date.today().year(), 
            Month__c = '1', 
            Brand__c = 'EGO', 
            Model_Number__c = 'EGO-0001'
        );
        objAce2ndStoreThisYearRP01.Cost__c = 50;
        objAce2ndStoreThisYearRP01.Units__c = 50;
        RetailPurchase__c objAce2ndStoreThisYearRP02 = new RetailPurchase__c(
            Customer_Store_Location__c = objAce2ndTierStore02.Id, 
            Year__c = Date.today().year(), 
            Month__c = '1', 
            Brand__c = 'SKIL', 
            Model_Number__c = 'SKIL-0001'
        );
        objAce2ndStoreThisYearRP02.Cost__c = 100;
        objAce2ndStoreThisYearRP02.Units__c = 100;
        RetailPurchase__c objAce2ndStoreLastYearRP01 = new RetailPurchase__c(
            Customer_Store_Location__c = objAce2ndTierStore02.Id, 
            Year__c = Date.today().year() - 1, 
            Month__c = '6', 
            Brand__c = 'EGO', 
            Model_Number__c = 'EGO-0002'
        );
        objAce2ndStoreLastYearRP01.Cost__c = 50;
        objAce2ndStoreLastYearRP01.Units__c = 50;
        RetailPurchase__c objAce2ndStoreLastYearRP02 = new RetailPurchase__c(
            Customer_Store_Location__c = objAce2ndTierStore02.Id, 
            Year__c = Date.today().year() - 1, 
            Month__c = '6', 
            Brand__c = 'SKIL', 
            Model_Number__c = 'SKIL-0002'
        );
        objAce2ndStoreLastYearRP02.Cost__c = 100;
        objAce2ndStoreLastYearRP02.Units__c = 100;
        RetailPurchase__c objAce2ndStoreLastYearRP03 = new RetailPurchase__c(
            Customer_Store_Location__c = objAce2ndTierStore02.Id, 
            Year__c = Date.today().year() - 1, 
            Month__c = '1', 
            Brand__c = 'EGO', 
            Model_Number__c = 'EGO-0001'
        );
        objAce2ndStoreLastYearRP03.Cost__c = 50;
        objAce2ndStoreLastYearRP03.Units__c = 50;
        RetailPurchase__c objAce2ndStoreLastYearRP04 = new RetailPurchase__c(
            Customer_Store_Location__c = objAce2ndTierStore02.Id, 
            Year__c = Date.today().year() - 1, 
            Month__c = '1', 
            Brand__c = 'SKIL', 
            Model_Number__c = 'SKIL-0001'
        );
        objAce2ndStoreLastYearRP04.Cost__c = 100;
        objAce2ndStoreLastYearRP04.Units__c = 100;
        RetailPurchase__c objAce2ndStoreLastYearRP05 = new RetailPurchase__c(
            Customer_Store_Location__c = objAce2ndTierStore02.Id, 
            Year__c = Date.today().year() - 1, 
            Month__c = '1', 
            Brand__c = 'SKIL', 
            Model_Number__c = 'SKIL-0002'
        );
        objAce2ndStoreLastYearRP04.Cost__c = 100;
        objAce2ndStoreLastYearRP04.Units__c = 100;
        Database.insert (new List<RetailPurchase__c>{ objAceStoreThisYearRP01, objAceStoreThisYearRP02, objAceStoreLastYearRP01, objAceStoreLastYearRP02, objAceStoreLastYearRP03, objAceStoreLastYearRP04, objAce2ndStoreThisYearRP01, objAce2ndStoreThisYearRP02, objAce2ndStoreLastYearRP01, objAce2ndStoreLastYearRP02, objAce2ndStoreLastYearRP03, objAce2ndStoreLastYearRP04,objAce2ndStoreLastYearRP05});
        //Address
        Account_Address__c addr1 = new Account_Address__c();
        addr1.Name = 'warehouse Address 1';
        addr1.City__c = 'Test1';
        addr1.State__c = 'NY';
        addr1.Country__c = 'US';
        addr1.Postal_Code__c = '123456';
        addr1.Customer__c = objAce2ndTierStore02.Id;
        addr1.RecordTypeId = CCM_Contants.DROPSHIP_BILLING_ADDRESS_RECORDTYPEID;
        addr1.Address_Type__c = 'Warehouse';
        insert addr1;
        Address_With_Program__c baab1 = new Address_With_Program__c();
        baab1.Account_Address__c = addr1.Id;
        insert baab1;
        //order
        Product2 pro1 = new Product2(Name = 'test Product', Brand_Name__c= 'EGO', ProductCode = 'EGO-0001', Source__c = 'PIM'); 
        Product2 pro2 = new Product2(Name = 'test Product', Brand_Name__c= 'EGO', ProductCode = 'EGO-00031', Source__c = 'PIM'); 
        insert new List<Product2>{pro1,pro2};
        Order o1 = new Order(Name = 'test Order_1',ShipTo__c=baab1.Id,AccountId = objAceStore.Id,Store_Location__c = objAceStore.Id, Status = 'Draft', Date_Order__c = Date.today().addDays(-1),EffectiveDate = System.today());
        Order o2 = new Order(Name = 'test Order_2',ShipTo__c=baab1.Id, AccountId = objAce2ndTierStore02.Id,Store_Location__c =objAce2ndTierStore02.Id, Status = 'Draft',Date_Order__c = Date.today().addDays(-190),EffectiveDate = System.today());
        Order o3 = new Order(Name = 'test Order_3',ShipTo__c=baab1.Id,AccountId = objAce2ndTierStore02.Id,Store_Location__c =objAce2ndTierStore02.Id, Status = 'Draft',Date_Order__c = Date.today().addDays(-400),EffectiveDate = System.today());
        insert new List<Order>{o1, o2, o3};

        List<Order_Item__c> orList = new List<Order_Item__c>();
        Order_Item__c or1 = new Order_Item__c(Order__c = o1.Id, Product__c = pro1.Id,Order_Quantity__c=20,Price__c=3);
        Order_Item__c or2 = new Order_Item__c(Order__c = o2.Id, Product__c = pro2.Id,Order_Quantity__c=10,Price__c=2);
        Order_Item__c or3 = new Order_Item__c(Order__c = o3.Id, Product__c = pro2.Id,Order_Quantity__c=11,Price__c=2);
        insert new List<Order_Item__c>{or1, or2, or3};

        Store_Location_Summary__c objStoreSummary = new Store_Location_Summary__c();
        objStoreSummary.Store_Location__c = objAceStore.Id;
        objStoreSummary.This_Year_POS_YTD_Amount__c = 1;
        objStoreSummary.Model__c = 'ZT8888';
        Database.insert (objStoreSummary);
    }
    @isTest
    static void testRefreshStoreSummaryData(){
        Test.startTest();
        CCM_AceReportCtl.refreshStoreSummaryData();
        Test.stopTest();
    }
    @isTest
    static void testStoreLocationSummaryReset(){
        CCM_StoreLocationSummaryBatch objStoreLocationSummaryBatch = new CCM_StoreLocationSummaryBatch(false);
        Test.startTest();
        Database.executeBatch(objStoreLocationSummaryBatch, 10);
        Test.stopTest();
    }
    @isTest
    static void testRefreshSchedule(){
        Account objAccount = [SELECT Id
                              FROM Account
                              WHERE Name = 'ACE HARDWARE 2nd Tier Store 02'];
        Test.startTest();
        POS_Data__c objPos = new POS_Data__c(
            Customer_Store_Location__c = objAccount.Id, 
            Year__c = Date.today().year(), 
            Month__c = '1', 
            Brand__c = 'EGO', 
            Model_Number__c = 'EGO-9999'
        );
        objPos.Sales_Amount__c = 50;
        objPos.Sales_Unit__c = 50;
        Database.insert (objPos);
        CCM_AceReportSchedule objAceReportSchedule = new CCM_AceReportSchedule();
        objAceReportSchedule.execute(null);
        CCM_AceReportCtl.refreshStoreSummaryData();
        CCM_AceReportCtl.refreshStoreSummaryData();
        CCM_AceReportCtl.refreshStoreRank();
        CCM_AceReportCtl.refreshStoreRank();
        CCM_AceReportCtl.increaseTestCoverage();
        Test.stopTest();
    }
    @isTest
    static void testCCM_StoreLocationRefreshTableauQueue(){
    CCM_StoreLocationRefreshTableau.syncStoreLocationRefreshTableau();
    }
}