@isTest
private class CCM_RestService_DealProductInfoTest {
    static testMethod void testMethod1() {
        Test.startTest();

    	String str = '[{"DESCRIPTION":"BA1400T 56V G3 2.5Ah Battery","INVENTORY_ITEM_ID":"1201785.0","<PERSON><PERSON>EL_NUMBER":"BA1400T","SEGMENT1":"400152001","BRAND":"EGO","PRIMARY_UOM_CODE":"EA","CREATION_DATE":"2018-10-26 01:28:22","LAST_UPDATE_DATE":"2020-07-07 06:55:51","ITEM_TYPE":"配件","DIMENSION_UOM_CODE":"In","UNIT_HEIGHT":"0","UNIT_LENGTH":"0","UNIT_WIDTH":"0","UNIT_WEIGHT":"0","WEIGHT_UOM_CODE":"LBS"},{"DESCRIPTION":"BA1400T 56V G3 2.5Ah Battery","INVENTORY_ITEM_ID":"1201786.0","MODEL_NUMBER":"QC1400T","SEGMENT1":"400152001","BRAND":"EGO","PRIMARY_UOM_CODE":"EA","CREATION_DATE":"2018-10-26 01:28:22","LAST_UPDATE_DATE":"2020-07-07 06:55:51","ITEM_TYPE":"配件","DIMENSION_UOM_CODE":"In","UNIT_HEIGHT":"6.1","UNIT_LENGTH":"7.52","UNIT_WIDTH":"3.15","UNIT_WEIGHT":"3.75","WEIGHT_UOM_CODE":"kg"},{"DESCRIPTION":"BA1400T 56V G3 2.5Ah Battery","INVENTORY_ITEM_ID":"1231244","MODEL_NUMBER":"TTC1400T","SEGMENT1":"400152001","BRAND":"EGO","PRIMARY_UOM_CODE":"EA","CREATION_DATE":"2018-10-26 01:28:22","LAST_UPDATE_DATE":"2020-07-07 06:55:51","ITEM_TYPE":"Inactive","DIMENSION_UOM_CODE":"In","UNIT_HEIGHT":"6.1","UNIT_LENGTH":"7.52","UNIT_WIDTH":"3.15","UNIT_WEIGHT":"3.75","WEIGHT_UOM_CODE":"g"}]';
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestURI = '/services/apexrest/CCM_RestService_DealProductInfo';  //Request URL
        req.httpMethod = 'Post';
        req.requestBody = Blob.valueof(str);
        RestContext.request = req;
        CCM_RestService_DealProductInfo.doPost();
        CCM_RestService_DealProductInfo.doPost();
        
        String str2 = '[{"DESCRIPTION":"BA1400T 56V G3 2.5Ah Battery","INVENTORY_ITEM_ID":"1201785.0","MODEL_NUMBER":"BA1400T","SEGMENT1":"400152001","BRAND":"EGO","PRIMARY_UOM_CODE":"EA","CREATION_DATE":"2018-10-26 01:28:22","LAST_UPDATE_DATE":"2020-07-07 06:55:51","ITEM_TYPE":"配件","DIMENSION_UOM_CODE":"In","UNIT_HEIGHT":"6.1","UNIT_LENGTH":"7.52","UNIT_WIDTH":"3.15","UNIT_WEIGHT":"3.75","WEIGHT_UOM_CODE":"LBS"},{"DESCRIPTION":"BA1400T 56V G3 2.5Ah Battery","INVENTORY_ITEM_ID":"1201785.0","MODEL_NUMBER":"QC1400T","SEGMENT1":"400152001","BRAND":"EGO","PRIMARY_UOM_CODE":"EA","CREATION_DATE":"2018-10-26 01:28:22","LAST_UPDATE_DATE":"2020-07-07 06:55:51","ITEM_TYPE":"配件","DIMENSION_UOM_CODE":"In","UNIT_HEIGHT":"6.1","UNIT_LENGTH":"7.52","UNIT_WIDTH":"3.15","UNIT_WEIGHT":"3.75","WEIGHT_UOM_CODE":"kg"},{"DESCRIPTION":"BA1400T 56V G3 2.5Ah Battery","INVENTORY_ITEM_ID":"1231244","MODEL_NUMBER":"TTC1400T","SEGMENT1":"400152001","BRAND":"EGO","PRIMARY_UOM_CODE":"EA","CREATION_DATE":"2018-10-26 01:28:22","LAST_UPDATE_DATE":"2020-07-07 06:55:51","ITEM_TYPE":"Inactive","DIMENSION_UOM_CODE":"In","UNIT_HEIGHT":"6.1","UNIT_LENGTH":"7.52","UNIT_WIDTH":"3.15","UNIT_WEIGHT":"3.75","WEIGHT_UOM_CODE":"g"}]';
        RestRequest req2 = new RestRequest();
        RestResponse res2 = new RestResponse();
        req2.requestURI = '/services/apexrest/CCM_RestService_DealProductInfo';  //Request URL
        req2.httpMethod = 'Post';
        req2.requestBody = Blob.valueof(str2);
        RestContext.request = req2;
        CCM_RestService_DealProductInfo.doPost();
        
        String str3 = '[{"DESCRIPTION":"BA1400T 56V G3 2.5Ah Battery","MODEL_NUMBER":"BA1400T","SEGMENT1":"400152001","BRAND":"EGO","PRIMARY_UOM_CODE":"EA","CREATION_DATE":"2018-10-26 01:28:22","LAST_UPDATE_DATE":"2020-07-07 06:55:51","ITEM_TYPE":"配件","DIMENSION_UOM_CODE":"In","UNIT_HEIGHT":"6.1","UNIT_LENGTH":"7.52","UNIT_WIDTH":"3.15","UNIT_WEIGHT":"3.75","WEIGHT_UOM_CODE":"LBS"}]';
        RestRequest req3 = new RestRequest();
        RestResponse res3 = new RestResponse();
        req3.requestURI = '/services/apexrest/CCM_RestService_DealProductInfo';  //Request URL
        req3.httpMethod = 'Post';
        req3.requestBody = Blob.valueof(str3);
        RestContext.request = req3;
        CCM_RestService_DealProductInfo.doPost();
        
        String str4 = '[]';
        RestRequest req4 = new RestRequest();
        RestResponse res4 = new RestResponse();
        req4.requestURI = '/services/apexrest/CCM_RestService_DealProductInfo';  //Request URL
        req4.httpMethod = 'Post';
        req4.requestBody = Blob.valueof(str4);
        RestContext.request = req4;
        CCM_RestService_DealProductInfo.doPost();

        Test.stopTest();
    }
}