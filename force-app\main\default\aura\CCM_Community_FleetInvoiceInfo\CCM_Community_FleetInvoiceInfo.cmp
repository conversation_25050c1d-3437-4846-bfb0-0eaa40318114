<aura:component controller="CCM_Community_FleetEditCtl">

    <aura:attribute name="yesOrNoList" type="List" default="[]"/>

    <aura:attribute name="addressCondition" type="String"/>
    <aura:attribute name="shipToAddressCondition" type="String"/>
    <aura:attribute name="readonly" type="Boolean" default="true"/>
    <aura:attribute name="addressIdListStr" type="String"/>
    <aura:attribute name="shipToAddressIdListStr" type="String"/>
    <aura:attribute name="fleetClaim" type="Object" description="fleetClaim information"/>

    <aura:handler name="change" value="{!v.addressIdListStr}" action="{!c.doGetCondition}"/>
    <aura:handler name="change" value="{!v.fleetClaim.billToAddressName}" action="{!c.changeBillToAddressId}"/>
    <aura:handler name="change" value="{!v.fleetClaim.shipToAddressName}" action="{!c.changeShipToAddressId}"/>

    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <lightning:spinner aura:id="spinner" class="slds-hide slds-is-fixed" alternativeText="{!$Label.c.CCM_Portal_Loading}" size="medium" variant="brand"/>

    <article class="slds-card">
        <div class="slds-grid">
            <header class="slds-media slds-media_center slds-has-flexi-truncate">
                <div class="slds-media__body">
                    <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                        <span class="section-header-title slds-p-horizontal--small slds-truncate"
                              title="{!$Label.c.CCM_Portal_InvoiceInformation}">
                            <span><strong>{!$Label.c.CCM_Portal_InvoiceInformation}</strong></span>
                         </span>
                    </h2>
                </div>
            </header>
        </div>
        <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
            <lightning:layout multipleRows="true" horizontalAlign="space">

                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                    <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Dealerinvoice}:</label>
                </lightning:layoutItem>
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                    <aura:renderIf isTrue="{!v.readonly}">
                        {!v.fleetClaim.invoiceNumber}
                        <aura:set attribute="else">
                            <div class="input-width">
                                <lightning:input aura:id="invoiceNumber"
                                                 value="{!v.fleetClaim.invoiceNumber}"
                                                 variant="label-hidden"
                                                 messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan100bytes}"
                                                 maxlength="100" />
                            </div>
                            <div aura:id="invoiceNumber-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </aura:set>
                    </aura:renderIf>
                </lightning:layoutItem>
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                    <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Totalsalesamount}:</label>
                </lightning:layoutItem>
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                    <lightning:formattedNumber value="{!v.fleetClaim.totalSalesAmount}" minimumFractionDigits="2" currencyCode="{!v.fleetClaim.currencyCode}" style="currency" currencyDisplayAs="code"/>
                </lightning:layoutItem>

                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                    <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Totalretailprice}:</label>
                </lightning:layoutItem>
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                    <lightning:formattedNumber value="{!v.fleetClaim.totalRetailPrice}" minimumFractionDigits="2" currencyCode="{!v.fleetClaim.currencyCode}" style="currency" currencyDisplayAs="code"/>
                </lightning:layoutItem>
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                    <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Estimatedcreditreturn}:</label>
                </lightning:layoutItem>
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                    <lightning:formattedNumber value="{!v.fleetClaim.estimatedCreditReturn}" minimumFractionDigits="2" currencyCode="{!v.fleetClaim.currencyCode}" style="currency" currencyDisplayAs="code"/>
                </lightning:layoutItem>

                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                    <label class="{! 'slds-form-element__label' + if(v.readonly, '', ' field-required')}">{!$Label.c.CCM_Portal_Billtoaddress}:</label>
                </lightning:layoutItem>
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                    <aura:renderIf isTrue="{!v.readonly}">
                        {!v.fleetClaim.billToAddressName}
                        <aura:set attribute="else">
                            <aura:if isTrue="{! !empty(v.addressCondition)}">
                                <div class="input-width">
                                    <c:CCM_AutoMatchPickList aura:id="billToAddressId"
                                                             objectType="Account_Address__c"
                                                             labelField="Name"
                                                             labelField1="Address1__c;City__c;State__c;Country__c;Postal_Code__c"
                                                             showLabel="false"
                                                             inputValue="{!v.fleetClaim.billToAddressName}"
                                                             value="{!v.fleetClaim.billToAddressId}"
                                                             filterCondition='{!v.addressCondition}'
                                                             fieldList="Name,Country__c,State__c,City__c,Address1__c,Postal_Code__c"/>
                                </div>
                            </aura:if>
                            <div aura:id="billToAddressId-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </aura:set>
                    </aura:renderIf>
                </lightning:layoutItem>

                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                    <lightning:helptext content="{!$Label.c.CCM_Portal_Store_Location_HelpText}"></lightning:helptext>
                    <label class="{! 'slds-form-element__label' + if(v.readonly, '', ' field-required')}">{!$Label.c.CCM_Portal_Store_Location}:</label>
                </lightning:layoutItem>
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                    <aura:renderIf isTrue="{!v.readonly}">
                        {!v.fleetClaim.shipToAddressName}
                        <aura:set attribute="else">
                            <aura:if isTrue="{! !empty(v.shipToAddressCondition)}">
                                <div class="input-width">
                                    <c:CCM_AutoMatchPickList aura:id="shipToAddressId"
                                                             objectType="Account_Address__c"
                                                             labelField="Name"
                                                             labelField1="Address1__c;City__c;State__c;Country__c;Postal_Code__c"
                                                             showLabel="false"
                                                             inputValue="{!v.fleetClaim.shipToAddressName}"
                                                             value="{!v.fleetClaim.shipToAddressId}"
                                                             filterCondition='{!v.shipToAddressCondition}'
                                                             fieldList="Name,Country__c,State__c,City__c,Address1__c,Postal_Code__c"/>
                                </div>
                            </aura:if>
                            <div aura:id="shipToAddressId-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </aura:set>
                    </aura:renderIf>
                </lightning:layoutItem>
                <lightning:layoutItem size="6" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                </lightning:layoutItem>

                <lightning:layoutItem size="6" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                </lightning:layoutItem>
                <lightning:layoutItem size="12" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                    <div style="width: auto; display: inline-block; padding: 20px 50px 10px 50px;" class="slds-float_right">
                        <!-- file upload -->
                        <aura:if isTrue="{!!v.readonly}">
                            <label class="slds-form-element__label field-required">{!$Label.c.CCM_Portal_Uploadyourinvoice}:</label>
                            <div aura:id="invoiceFile-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                            <lightning:input class="slds-text-align--left"
                                             aura:id="invoiceFile"
                                             variant="label-hidden"
                                             type="file"
                                             multiple="false"
                                             accept="image/png, .pdf"
                                             onchange="{! c.handleFilesChange }"/>
                            <aura:set attribute="else">
                                <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Invoice}:</label>
                            </aura:set>
                        </aura:if>
                        <aura:iteration items="{!v.fleetClaim.fileInfoList}" var="fileInfo" indexVar="index">
                            <div>
                                <span class="fileName">
                                    <a class="uploadFinished" href="{!fileInfo.contentUrl}" target="_blank">{!fileInfo.uploadFileName}</a>&nbsp;
                                </span>
                                <aura:renderIf isTrue="{!!v.readonly}">
                                    <a class="delete" onclick="{!c.deleteFiles}" data-row="{!index}">{!$Label.c.CCM_Portal_Delete}</a>
                                </aura:renderIf>
                            </div>
                        </aura:iteration>
                    </div>
                </lightning:layoutItem>
            </lightning:layout>
        </div>
    </article>
</aura:component>