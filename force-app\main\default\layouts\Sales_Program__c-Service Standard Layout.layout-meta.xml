<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>ChangeOwnerOne</excludeButtons>
    <excludeButtons>ChangeRecordType</excludeButtons>
    <excludeButtons>Clone</excludeButtons>
    <excludeButtons>Delete</excludeButtons>
    <excludeButtons>OpenSlackRecordChannel</excludeButtons>
    <excludeButtons>PrintableView</excludeButtons>
    <excludeButtons>RecordShareHierarchy</excludeButtons>
    <excludeButtons>Share</excludeButtons>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Fields</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Authorized_Brand_Name_To_Oracle__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Brands__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Start_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Warranty_parts_credit_mark_up__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Price_Book__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ORG_Code__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Prospect__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Customer__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RecordTypeId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Expire_Date__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Customer Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Customer_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>State__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Service_Categories__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Brand_Certification__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Training__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Performance__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Company__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Condition_Service__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Mileage_One_Trip__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Value_Added_Services__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Competitiveness__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Competitiveness_Performance__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Payment Term</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Payment_Term__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Payment_Term_Description__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Starting_Date_of_Payment_Term__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Payment_Lead_Time__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Payment_Discount__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Freight Term</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Freight_Term__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Approval Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Approval_Status__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Approval_Comments__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Approval_Reject_Reason__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CurrencyIsoCode</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Approval_Submit_Time__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Approved_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Status__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Labor_Rate__c</fields>
        <fields>Start_Date__c</fields>
        <fields>Expire_Date__c</fields>
        <relatedList>Labor_Rate__c.Authorized_Brand__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Account_Address__c</fields>
        <fields>Address_Type__c</fields>
        <fields>Status__c</fields>
        <relatedList>Address_With_Program__c.Program__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>CreatedDate</fields>
        <fields>StepStatus</fields>
        <fields>OriginalActor</fields>
        <fields>Actor</fields>
        <fields>Comments</fields>
        <relatedList>RelatedProcessHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedEntityHistoryList</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h8a00000d5DkL</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
