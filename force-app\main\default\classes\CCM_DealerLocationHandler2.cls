public with sharing class CCM_DealerLocationHandler2 implements Triggers.Handler {
    public static Boolean isRun = true;
    public void handle() {
        if(!isRun){
            return;
        }
        CCM_ShareAccountHandler.isRun = false;
        List<Account> modifyAccounts = new List<Account>();
        for (Account acc : (List<Account>)Trigger.new) {
            if (acc.RecordTypeId==CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID||
            acc.RecordTypeId==CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID) {
                modifyAccounts.add(acc);
            }
        }
        if (modifyAccounts.size()>0){
            if (Trigger.isBefore&&Trigger.isInsert) {
                BeforeInsertCustomer(modifyAccounts, Trigger.newMap, null);
            }
            if (Trigger.isBefore&&Trigger.isUpdate) {
                BeforeUpdateCustomer(modifyAccounts, Trigger.newMap, Trigger.oldMap);
            }
            if (Trigger.isAfter&&Trigger.isInsert) {
                AfterInsertCustomer(modifyAccounts, Trigger.newMap, Trigger.oldMap);
            }
            if (Trigger.isAfter&&Trigger.isUpdate) {
                AfterUpdateCustomer(modifyAccounts, Trigger.newMap, Trigger.oldMap);
            }
        }
        CCM_ShareAccountHandler.isRun = true;
    }
    static void BeforeInsertCustomer(List<Account> accList, Map<Id, sObject> newMap, Map<Id, sObject> oldMap){
        List<Account> accs=new List<Account>();
        Validation(accList);
        updateBuyingGroup(accList);
        for (Account accRow : accList) {
            if (String.isNotBlank(accRow.Conversion_Reason__c)) {
                accRow.addError('Please change the record type before filling the conversion reason.');
            }
        }
    }
    static void BeforeUpdateCustomer(List<Account> accList, Map<Id, sObject> newMap, Map<Id, sObject> oldMap){
        for (Account accRow : accList) {
            Account oldItem = (Account)oldMap.get(accRow.Id);
            if (accRow.Related_Entity__c!=oldItem.Related_Entity__c||accRow.Related_Entity_Prospect__c!=oldItem.Related_Entity_Prospect__c){
                String entity=String.valueOf(accRow.Related_Entity__c);
                String entityPro=String.valueOf(accRow.Related_Entity_Prospect__c);
                if (String.isEmpty(entity)&&String.isEmpty(entityPro)) {
                    accRow.addError('The field Related Entity and Related Entity (Prospect) need to fill in one.');
                }
                if (String.isNotEmpty(entity)&&String.isNotEmpty(entityPro)) {
                    accRow.addError('The field Related Entity and Related Entity (Prospect) cannot be chosen simultaneously.');
                }
            }
            if(accRow.RecordTypeId == CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID && accRow.Related_Entity__c == oldItem.Related_Entity__c && accRow.Buying_Group__c != oldItem.Buying_Group__c){
                accRow.addError('The field Buying Group/Marketing Group cannot be update');
            }
            if(accRow.RecordTypeId == CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID && accRow.Related_Entity_Prospect__c == oldItem.Related_Entity_Prospect__c && accRow.Buying_Group__c != oldItem.Buying_Group__c){
                accRow.addError('The field Buying Group/Marketing Group cannot be update');
            }
            if(accRow.Store_Address__City__s!=oldItem.Store_Address__City__s||accRow.Store_Address__CountryCode__s!=oldItem.Store_Address__CountryCode__s||accRow.Store_Address__PostalCode__s!=oldItem.Store_Address__PostalCode__s||accRow.Store_Address__StateCode__s!=oldItem.Store_Address__StateCode__s||accRow.ORG_Code__c!=oldItem.ORG_Code__c) {
                Validation(accList);
            }
            if (accRow.RecordTypeId!=oldItem.RecordTypeId&&String.isBlank(accRow.Related_Entity__c)) {
                accRow.addError('To manually convert this potential store location, the related entity can not be a prospect. Please convert the prospect to a customer first.');
            }
            if (accRow.RecordTypeId!=oldItem.RecordTypeId&&oldItem.RecordTypeId==CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID) {
                accRow.RecordTypeId=CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID;
            }
            if (accRow.RecordTypeId!=oldItem.RecordTypeId&&accRow.RecordTypeId==CCM_Constants.CUSTOMER_RECORD_TYPE_CHANNEL_ID) {
                accRow.addError('Can not change a store location to a channel customer.');
            }
            if (accRow.RecordTypeId!=oldItem.RecordTypeId&&String.isBlank(accRow.Conversion_Reason__c)) {
                accRow.addError('Please choose a conversion reason.');
            }
        }
        updateBuyingGroup(accList);
    }
    static void AfterInsertCustomer(List<Account> accList, Map<Id, sObject> newMap, Map<Id, sObject> oldMap){
        Map<Id, Account> queriedNewAccMap =new Map<Id, Account>([Select Customer_Name__c, Id, RecordTypeId, Related_Entity__c,Related_Entity__r.Customer_Cluster__c, Related_Entity__r.Name, Related_Entity_Prospect__c,Related_Entity_Prospect__r.Name, StoreNumber_In_SF__c, Auto_Store_Number__c, Store_Address__c, Store_Address__City__s, Store_Address__CountryCode__s, Store_Address__PostalCode__s, Store_Address__StateCode__s, Store_Address__Street__s, Store_Number2__c from Account WHERE Id IN :newMap.keySet()]);
        List<Account> accs=new List<Account>();
        Map<String,String> contactNames=new Map<String,String>();
        Set<String> taskIds=new Set<String>();
        for (Account accRow : accList) {
            Account theNewAccount = queriedNewAccMap.get(accRow.Id);
            //Related_Entity__r.Customer Cluster=='OPE Direct Dealer'
            if (theNewAccount.Related_Entity__r.Customer_Cluster__c=='CNA-CG11'&&(theNewAccount.Store_Address__StateCode__s=='CA'||theNewAccount.Store_Address__StateCode__s=='FL'||theNewAccount.Store_Address__StateCode__s=='GA'||theNewAccount.Store_Address__StateCode__s=='ID'||theNewAccount.Store_Address__StateCode__s=='MT'||theNewAccount.Store_Address__StateCode__s=='NC'||theNewAccount.Store_Address__StateCode__s=='NE'||theNewAccount.Store_Address__StateCode__s=='NV'||theNewAccount.Store_Address__StateCode__s=='PA'||theNewAccount.Store_Address__StateCode__s=='SC'||theNewAccount.Store_Address__StateCode__s=='VA')) {
                taskIds.add(theNewAccount.Id);
            }
            Account upAccount=new Account();
            upAccount.Id=accRow.Id;
            String autoNumber=theNewAccount.Auto_Store_Number__c;
            if (!String.isBlank(theNewAccount.Related_Entity__c)&&String.isBlank(theNewAccount.StoreNumber_In_SF__c)) {
                upAccount.StoreNumber_In_SF__c=theNewAccount.Related_Entity__r.name.substring(0, 3)+autoNumber;
            }
            if (!String.isBlank(theNewAccount.Related_Entity_Prospect__c)&&String.isBlank(theNewAccount.StoreNumber_In_SF__c)) {
                upAccount.StoreNumber_In_SF__c=theNewAccount.Related_Entity_Prospect__r.Name.substring(0, 3)+autoNumber;
            }
            accs.add(upAccount);
            if (String.isNotBlank(accRow.Contact_Name__c)) {
                contactNames.put(accRow.Contact_Name__c, accRow.Id);
            }
        }
        if (accs.size()>0) {
            CCM_AccountSendApproval.isRun = false;
            upsert accs;
            CCM_AccountSendApproval.isRun = true;
        }
        if (contactNames.size()>0) {
            InsertContact(contactNames);
        }
        if(!taskIds.isEmpty()) {
            InsertTask(taskIds);
        }
    }
    static void AfterUpdateCustomer(List<Account> accList, Map<Id, sObject> newMap, Map<Id, sObject> oldMap){
        // //convert前检查附件
        // List<Attachment_Management_Of_Customer__c> files=[select id,(select id from ContentDocumentLinks) from Attachment_Management_Of_Customer__c where Customer__c in :oldMap.keySet()];
        // Boolean hasFile=false;
        // for (Attachment_Management_Of_Customer__c file : files) {
        //     if (file.ContentDocumentLinks.size()>0) {
        //         hasFile=true;
        //     }
        // }
        Map<Id, Account> queriedNewAccMap =new Map<Id, Account>([Select Customer_Name__c, Id, RecordTypeId, Related_Entity__c,Related_Entity__r.Customer_Cluster__c, Related_Entity__r.Name, Related_Entity_Prospect__c,Related_Entity_Prospect__r.Name, StoreNumber_In_SF__c, Auto_Store_Number__c, Store_Address__c, Store_Address__City__s, Store_Address__CountryCode__s, Store_Address__PostalCode__s, Store_Address__StateCode__s, Store_Address__Street__s, Store_Number2__c from Account WHERE Id IN :newMap.keySet()]);
        Map<String, String> customerClusterMap = new Map<String, String>();
        for(Account acc : queriedNewAccMap.values()) {
            customerClusterMap.put(acc.Related_Entity__c, acc.Related_Entity__r.Customer_Cluster__c);
        }
        List<OrgWideEmailAddress> lstEmailAddress=[select Id from OrgWideEmailAddress WHERE DisplayName= 'SFDC Notification'];
        String url = URL.getOrgDomainUrl().toExternalForm()+'/';
        Map<String,String> contactNames=new Map<String,String>();
        Set<String> taskIds=new Set<String>();
        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
        for (Account accRow : accList) {
            // if (accRow.RecordTypeId!=oldItem.RecordTypeId&&!hasFile) {
            //     accRow.addError('To manually convert this potential store location, you need to upload proof first.');
            // }
            Account oldItem = (Account)oldMap.get(accRow.Id);
            Account theNewAccount = queriedNewAccMap.get(accRow.Id);
            if (accRow.Related_Entity__c!=oldItem.Related_Entity__c||accRow.Store_Number2__c!=oldItem.Store_Number2__c) {
                try {
                    Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                    email.setOrgWideEmailAddressId(lstEmailAddress[0].Id);
                    List<String> sendTo = new List<String>();
                    List<String> notifiers = getNotifiers(accRow.Related_Entity__c, customerClusterMap);
                    // List<String> emailForAR=Label.Email_DealerLocation.split(';');
                    if(!notifiers.isEmpty()) {
                        sendTo.addAll(notifiers);
                    }
                    email.setSubject('Customer Data Change');
                    String line1 = 'Hi there,<br>';
                    String line2 = '&nbsp&nbsp&nbsp&nbspThe potential store location related entity or store number of '+accRow.Name+' has been changed. More detail please see via below link.<br>';
                    String line3 = url+accRow.Id;
                    String body = line1 + line2 + line3;
                    email.setHtmlBody(body);
                    email.setToAddresses(sendTo);
                    email.setCcAddresses(new List<String>{'<EMAIL>', '<EMAIL>'});
                    emails.add(email);
                    // Messaging.SendEmailResult[] r = Messaging.sendEmail(
                    //     new List<Messaging.SingleEmailMessage>{ email }
                    // );
                } catch (Exception e) {
                    System.debug(
                        'CCM_DealerLocationHandler2>>Err:' +
                        e.getMessage() +
                        e.getLineNumber()
                    );
                }
            }
            if (oldItem.RecordTypeId==CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID&&accRow.RecordTypeId==CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID) {
                try {
                    Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                    email.setOrgWideEmailAddressId(lstEmailAddress[0].Id);
                    List<String> sendTo = new List<String>();
                    List<String> notifiers = getNotifiers(accRow.Related_Entity__c, customerClusterMap);
                    // List<String> emailForAR=Label.Email_DealerLocation.split(';');
                    if(!notifiers.isEmpty()) {
                        sendTo.addAll(notifiers);
                    }
                    email.setSubject('Customer Recordtype Change');
                    String line1 = 'Hi there,<br>';
                    String line2 = '&nbsp&nbsp&nbsp&nbspThe potential store location '+accRow.Name+' has been converted to an existing store location. More detail please see via below link.<br>';
                    String line3 = url+accRow.Id;
                    String body = line1 + line2 + line3;
                    email.setHtmlBody(body);
                    email.setToAddresses(sendTo);
                    email.setCcAddresses(new List<String>{'<EMAIL>', '<EMAIL>'});
                    emails.add(email);
                    // Messaging.SendEmailResult[] r = Messaging.sendEmail(
                    //     new List<Messaging.SingleEmailMessage>{ email }
                    // );
                } catch (Exception e) {
                    System.debug(
                        'CCM_DealerLocationHandler2>>Err:' +
                        e.getMessage() +
                        e.getLineNumber()
                    );
                }
            }
            //Related_Entity__r.Customer Cluster=='OPE Direct Dealer'
            if (accRow.Related_Entity__c!=oldItem.Related_Entity__c){
                if(theNewAccount.Related_Entity__r.Customer_Cluster__c=='CNA-CG11'&&(theNewAccount.Store_Address__StateCode__s=='CA'||theNewAccount.Store_Address__StateCode__s=='FL'||theNewAccount.Store_Address__StateCode__s=='GA'||theNewAccount.Store_Address__StateCode__s=='ID'||theNewAccount.Store_Address__StateCode__s=='MT'||theNewAccount.Store_Address__StateCode__s=='NC'||theNewAccount.Store_Address__StateCode__s=='NE'||theNewAccount.Store_Address__StateCode__s=='NV'||theNewAccount.Store_Address__StateCode__s=='PA'||theNewAccount.Store_Address__StateCode__s=='SC'||theNewAccount.Store_Address__StateCode__s=='VA')) {
                    taskIds.add(theNewAccount.Id);
                }
            }
            if (accRow.Contact_Name__c!=oldItem.Contact_Name__c&&String.isNotBlank(accRow.Contact_Name__c)){
                contactNames.put(accRow.Contact_Name__c, accRow.Id);
            }
        }

        if(!taskIds.isEmpty()) {
            InsertTask(taskIds);
        }

        if(!emails.isEmpty()) {
            Messaging.SendEmailResult[] r = Messaging.sendEmail(emails);
        }

        if (contactNames.size()>0) {
            InsertContact(contactNames);
        }
    }

    static List<String> getNotifiers(String accId, Map<String, String> customerClusterMap) {
        List<String> notifiers = new List<String>();
        Map<String, List<String>> clusterEmailsMap = new Map<String, List<String>>{
            'CNA-CG22' => new List<String>{'<EMAIL>', '<EMAIL>'},
            'CNA-CG10' => new List<String>{'<EMAIL>', '<EMAIL>', '<EMAIL>'},
            'CNA-CG11' => new List<String>{'<EMAIL>', '<EMAIL>', '<EMAIL>'},
            'CNA-CG20' => new List<String>{'<EMAIL>', '<EMAIL>', '<EMAIL>'},
            'CNA-CG13' => new List<String>{'<EMAIL>'},
            'CNA-CG14' => new List<String>{'<EMAIL>'},
            'CNA-CG15' => new List<String>{'<EMAIL>'},
            'CNA-CG16' => new List<String>{'<EMAIL>'},
            'CNA-CG17' => new List<String>{'<EMAIL>'},
            'CNA-CG21' => new List<String>{'<EMAIL>'}
        };
        if(customerClusterMap.containsKey(accId)) {
            String cluster = customerClusterMap.get(accId);
            if(clusterEmailsMap.containsKey(cluster)) {
                notifiers = clusterEmailsMap.get(cluster);
            }
        }
        return notifiers;
    }

    static void InsertTask(Set<String> whatIds){
        List<Task> tasks = new List<Task>();
        List<String> emails = System.Label.StoreLocationCG11_TaskOwner.split(';');
        List<User> users=[SELECT Id, Email FROM User Where Email IN :emails AND IsActive = true];
        for (String whatId : whatIds) {
            for(User u : users) {
                Task newTask = new Task();
                newTask.Subject = 'Review/Change Owner';
                newTask.OwnerId = u.Id;
                newTask.WhatId = whatId;
                tasks.add(newTask);
            }
        }
        insert tasks;
    }

    static void InsertContact(Map<String,String> contactNames){
        List<Contact> contacts=new List<Contact>();
        Map<String,String> existContacts=new Map<String,String>();
        for (Contact con:[Select Id, LastName, AccountId from Contact c where LastName in :contactNames.keySet()]){
            existContacts.put(con.LastName,con.AccountId);
        }
        for (String key : contactNames.keySet()) {
            if (!existContacts.containsKey(key)||existContacts.get(Key)!=contactNames.get(key)) {
                Contact newContact=new Contact();
                newContact.LastName=key;
                newContact.AccountId=contactNames.get(key);
                contacts.add(newContact);
            }
        }
        if (contacts.size()>0) {
            upsert contacts;
        }
    }

    static void updateBuyingGroup(List<Account> accList){
        Set<String> relatedEntityIdSet = new Set<String>();
        Set<String> relatedEntityProspectIdSet = new Set<String>();
        Map<String,String> buyingGroupMap = new Map<String,String>();
        for(Account acc : accList){
            if(acc.RecordTypeId==CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID){
                relatedEntityIdSet.add(acc.Related_Entity__c);
            }else if(acc.RecordTypeId==CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID){
                if(acc.Related_Entity_Prospect__c != null){
                    relatedEntityProspectIdSet.add(acc.Related_Entity_Prospect__c);
                }else if(acc.Related_Entity__c != null){
                    relatedEntityIdSet.add(acc.Related_Entity__c);
                }
            }
        }
        for(Account ac : [SELECT Id,Buying_Group__c FROM Account WHERE Id IN :relatedEntityIdSet]){
            buyingGroupMap.put(ac.Id, ac.Buying_Group__c);
        }
        for(Lead le : [SELECT Id,Buying_Group__c FROM Lead WHERE Id IN :relatedEntityProspectIdSet]){
            buyingGroupMap.put(le.Id, le.Buying_Group__c);
        }
        for(Account account : accList){
            if(account.RecordTypeId==CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID){
                if(buyingGroupMap != null && buyingGroupMap.get(account.Related_Entity__c) != null){
                    account.Buying_Group__c = buyingGroupMap.get(account.Related_Entity__c);
                }
            }else if(account.RecordTypeId==CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID){
                if(buyingGroupMap != null){
                    if(buyingGroupMap.get(account.Related_Entity_Prospect__c) != null){
                        account.Buying_Group__c = buyingGroupMap.get(account.Related_Entity_Prospect__c);
                    }else if(buyingGroupMap.get(account.Related_Entity__c) != null){
                        account.Buying_Group__c = buyingGroupMap.get(account.Related_Entity__c);
                    }
                }
            }
        }
    }

    static void Validation(List<Account> accList){
        Set<String> cityMap = new Set<String>();
        Set<String> contryMap = new Set<String>();
        Set<String> postalMap = new Set<String>();
        Set<String> stateMap = new Set<String>();
        for (Account newItem : accList) {
            cityMap.add(newItem.Store_Address__City__s);
            contryMap.add(newItem.Store_Address__CountryCode__s);
            postalMap.add(newItem.Store_Address__PostalCode__s);
            stateMap.add(newItem.Store_Address__StateCode__s);
        }
        User currentUser = [SELECT Id, ContactId, Contact.AccountId, UserRoleId, UserRole.DeveloperName FROM User WHERE Id = :UserInfo.getUserId()];
        List<Account> allAddress=[Select Id,Name,Customer_Name__c, Store_Address__c, Store_Address__City__s, Store_Address__CountryCode__s, Store_Address__PostalCode__s, Store_Address__StateCode__s, Store_Address__Street__s from Account where RecordTypeId in (:CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID,:CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID) and Store_Address__City__s in :cityMap and Store_Address__StateCode__s in :stateMap and Store_Address__CountryCode__s in :contryMap and Store_Address__PostalCode__s in :postalMap ];
        for (Account accRow : accList) {
            String entity=String.valueOf(accRow.Related_Entity__c);
            String entityPro=String.valueOf(accRow.Related_Entity_Prospect__c);
            if (String.isEmpty(entity)&&String.isEmpty(entityPro)) {
                accRow.addError('The field Related Entity and Related Entity (Prospect) need to fill in one.');
            }
            if (String.isNotEmpty(entity)&&String.isNotEmpty(entityPro)) {
                accRow.addError('The field Related Entity and Related Entity (Prospect) cannot be chosen simultaneously.');
            }
            if(
                String.isBlank(accRow.Store_Address__City__s)||
                String.isBlank(accRow.Store_Address__CountryCode__s)||
                String.isBlank(accRow.Store_Address__PostalCode__s)||
                String.isBlank(accRow.Store_Address__Street__s)
            ){
                accRow.addError('Store Address can not be empty!');
                break;
            }
            if ((accRow.Store_Address__CountryCode__s=='CA'||accRow.Store_Address__CountryCode__s=='US')&&String.isBlank(accRow.Store_Address__StateCode__s)) {
                accRow.addError('State can not be empty!');
            }
            if(currentUser.UserRoleId != null){
                String roleName = currentUser.UserRole.DeveloperName;
                if(roleName.contains('CA_')){
                    accRow.ORG_Code__c='CCA';
                }else {
                    accRow.ORG_Code__c='CNA';
                }
            }
            String newStreet='';
            newStreet=accRow.Store_Address__Street__s.toLowerCase();
            newStreet=newStreet.replace('street', 'st').replace('st.', 'st');
            //newStreet=newStreet.replace('st.', 'st');
            newStreet=newStreet.replace('avenue', 'ave').replace('ave.', 'ave');
            //newStreet=newStreet.replace('ave.', 'ave');
            newStreet=newStreet.replace('road', 'rd').replace('rd.', 'rd');
            //newStreet=newStreet.replace('rd.', 'rd');
            newStreet=newStreet.replace('north', 'n').replace('n.', 'n');
            //newStreet=newStreet.replace('n.', 'n');
            newStreet=newStreet.replace('south', 's').replace('s.', 's');
            //newStreet=newStreet.replace('s.', 's');
            newStreet=newStreet.replace('east', 'e').replace('e.', 'e');
            //newStreet=newStreet.replace('e.', 'e');
            newStreet=newStreet.replace('west', 'w').replace('w.', 'w');
            //newStreet=newStreet.replace('w.', 'w');
            newStreet=newStreet.replace(' ', '');
            for (Account addressOfAll : allAddress) {
                // 筛除缩写，空格
                if (String.isNotBlank(addressOfAll.Store_Address__Street__s)) {
                    String oldStreet='';
                    oldStreet=addressOfAll.Store_Address__Street__s.toLowerCase();
                    oldStreet=oldStreet.replace('street', 'st').replace('st.', 'st');
                    //oldStreet=oldStreet.replace('st.', 'st');
                    oldStreet=oldStreet.replace('avenue', 'ave').replace('ave.', 'ave');
                    //oldStreet=oldStreet.replace('ave.', 'ave');
                    oldStreet=oldStreet.replace('road', 'rd').replace('rd.', 'rd');
                    //oldStreet=oldStreet.replace('rd.', 'rd');
                    oldStreet=oldStreet.replace('north', 'n').replace('n.', 'n');
                    //oldStreet=oldStreet.replace('n.', 'n');
                    oldStreet=oldStreet.replace('south', 's').replace('s.', 's');
                    //oldStreet=oldStreet.replace('s.', 's');
                    oldStreet=oldStreet.replace('east', 'e').replace('e.', 'e');
                    //oldStreet=oldStreet.replace('e.', 'e');
                    oldStreet=oldStreet.replace('west', 'w').replace('w.', 'w');
                    //oldStreet=oldStreet.replace('w.', 'w');
                    oldStreet=oldStreet.replace(' ', '');
                    addressOfAll.Store_Address__Street__s=oldStreet;
                }
                if (accRow.Store_Address__City__s==addressOfAll.Store_Address__City__s&&
                    accRow.Store_Address__CountryCode__s==addressOfAll.Store_Address__CountryCode__s&&
                    accRow.Store_Address__PostalCode__s==addressOfAll.Store_Address__PostalCode__s&&
                    accRow.Store_Address__StateCode__s==addressOfAll.Store_Address__StateCode__s&&
                    newStreet==addressOfAll.Store_Address__Street__s&&accRow.Id!=addressOfAll.Id) {
                    accRow.adderror('There is a store location record ('+addressOfAll.Name+') with the same address in system. Please check');
                    // accRow.addError('There is a store location record (<a href=\''+url+addressOfAll.Id+'\'>Duplicate Account</a>) with the same address in system. Please check.');
                }
            }
        }
    }
}