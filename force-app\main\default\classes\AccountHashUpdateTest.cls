@isTest
public class AccountHashUpdateTest {
    static testMethod void testMethod1() 
    {
        
        Account acc = new Account();
        acc.LastName = 'test';
        acc.Product_Type__c = 'EGO';
        acc.RecordTypeId = CCM_Contants.PERSONACCOUNT_RECORDTYPEID;
        acc.Site_Origin__c = 'United States';
        acc.EGO_username__c= '<EMAIL>';
        acc.EGO_password__c= 'july';
        insert acc;
        
        acc.Customer_Hash__c = '';
        update acc;
        
        test.startTest();
        AccountHashUpdate ac = new AccountHashUpdate();
        database.executeBatch(ac);
        test.stopTest();
    }  
}