/**
 * @description       :
 * <AUTHOR> <EMAIL>
 * @group             :
 * @last modified on  : 07-12-2024
 * @last modified by  : <EMAIL>
**/
public without sharing class CCM_PartsOrder_DetailCtl {
    public static String queryStr = 'SELECT Id, '
                    + 'Name, '
                    + 'Billing_Address__c, '
                    + 'Billing_Address__r.Name, '
                    + 'Billing_Address__r.Address1__c, '
                    + 'Billing_Address__r.Address2__c, '
                    + 'Billing_Address__r.Country__c, '
                    + 'Billing_Address__r.State__c, '
                    + 'Billing_Address__r.City__c, '
                    + 'Billing_Address__r.Contact__c, '
                    + 'Billing_Address__r.Contact__r.Name, '
                    + 'Billing_Address_Name__c, '
                    + 'Buyer_Contact__r.name, '
                    + 'Shipping_Address__c, '
                    + 'Shipping_Address__r.Name, '
                    + 'Shipping_Address_Name__c, '
                    + 'Shipping_Address__r.Address1__c, '
                    + 'Shipping_Address__r.Address2__c, '
                    + 'Shipping_Address__r.Country__c, '
                    + 'Shipping_Address__r.State__c, '
                    + 'Shipping_Address__r.City__c, '
                    + 'Shipping_Address__r.Contact__c, '
                    + 'Shipping_Address__r.Contact__r.Name, '
                    + 'Shipping_Address__r.Dropship_Contact__c, '
                    + 'Shipping_Address__r.Dropship_Contact__r.Name, '
                    + 'Is_Alternative_Address__c, '
                    + 'Additional_Shipping_Street__c,'
                    + 'Additional_Shipping_City__c,'
                    + 'Additional_Shipping_Country__c,'
                    + 'Additional_Shipping_Province__c,'
                    + 'Additional_Shipping_Postal_Code__c,'
                    + 'Additional_Contact_Name__c,'
                    + 'Additional_Contact_Phone__c,'
                    + 'Additional_Contact_Email__c,'
                    + 'Shipping_Method__c, '
                    + 'Shipping_By__c, '
                    + 'tolabel(Shipping_By__c) shippingBy, '
                    + 'Shipping_Priority__c, '
                    + 'Delivery_Supplier__c, '
                    + 'Customer__c, '
                    + 'Customer__r.Name, '
                    + 'Customer__r.Distributor_or_Dealer__c, '
                    + 'Customer__r.CurrencyIsoCode, '
                    + 'Customer__r.Shipment_Priority__c, '
                    + 'Customer__r.ORG_Code__c, '
                    + 'GST__c,'
                    + 'HST__c,'
                    + 'PST__c,'
                    + 'QST__c,'
                    + 'Status__c, '
                    + 'Is_Delegate__c, '
                    + 'Submit_Date__c, '
                    + 'Total_quantity__c, '
                    + 'Total_Price__c, '
                    + 'Total_Amount__c, '
                    + 'Notes__c, '
                    + 'Director_Approver__c, '
                    + 'Approval_Status__c,'
                    + 'Approval_Date__c,'
                    + 'Approval_Comments__c,'
                    + 'Email__c, '
                    + 'Phone__c, '
                    + 'Fax_Number__c, '
                    + 'Customer_PO_Num__c,'
                    + 'Expected_Delivery_Date__c,'
                    + 'Customer_Freight_Account__c,'
                    + 'Step__c,'
                    + 'Payment_Term__c,'
                    + 'Freight_Term__c,'
                    + 'tolabel(Payment_Term__c) paymentTermLabel, '
                    + 'tolabel(Freight_Term__c) freightTermLabel, '
                    + 'Handling_Fee__c,'
                    + 'Freight_Fee__c,'
                    + 'Freight_Fee_Waived__c,'
                    + 'Freight_Fee_To_Be_Waived__c,'
                    + 'Freight_Target_Fee__c,'
                    + 'Sales_Rep__c,'
                    + 'Sales_Manager__c,'
                    + 'Sales_Manager__r.Name,'
                    + 'Product_Price__c,'
                    + 'Need_Sales_Approval__c,'
                    + 'CurrencyIsoCode,'
                    + 'Brand_Scope__c,'
                    + 'Promotion__c,'
                    + 'Promotion_Code__c,'
                    + 'Promotion__r.Promotion_Code__c,'
                    + 'Is_DropShip__c,'
                    + 'Discount__c,'
                    + 'Discount_Amount__c,'
                    + 'Org_Code__c,'
                    + 'Org_Id__c,'
                    + 'Order_Type__c,'
                    + '(SELECT Id, '
                    + '     Name, '
                    + '     Brand__c, '
                    + '     Product__c, '
                    + '     ProductCode__c, '
                    + '     Product__r.Name, '
                    + '     Product__r.Description, '
                    + '     Product__r.Item_Number__c, '
                    + '     Product__r.ProductCode, '
                    + '     Product__r.Brand_Name__c, '
                    + '     Quantity__c, '
                    + '     List_Price__c, '
                    + '     Unit_Price__c, '
                    + '     Price_Book__c, '
                    + '     Line_Type__c, '
                    + '     MSRP__c, '
                    + '     Sub_Total__c '
                    + '     FROM Purchase_Order_Items__r) '
                    + 'FROM Purchase_Order__c';

    @AuraEnabled
    public static String getData(String recordId, String customerId){
        Boolean isSuccess = true;
        QuotationData quotaData = new QuotationData();

        if (String.isNotBlank(recordId) && String.isBlank(customerId)){
            Purchase_Order__c poInfo = [SELECT Id, Customer__c FROM Purchase_Order__c WHERE Id=: recordId];
            customerId = poInfo.Customer__c;

        }

        if(String.isNotBlank(customerId)){
            quotaData.hasDropShipAddress = Util.hasDropShipAddress(customerId);
        }

        if(String.isBlank(customerId) || customerId.equals('undefined') || customerId.equals('null')){
            User currentUser = Util.getUserInfo(UserInfo.getUserId());
            if(currentUser.ContactId != null){
                customerId = currentUser.Contact.AccountId;
            }
        }

        Boolean inKobaltScope = isInKobaltScope(customerId);
        quotaData.inKobaltScope = inKobaltScope;

        quotaData.isAce2ndTier = false;
        List<Account> accList = [SELECT Id, AccountNumber, Distributor_or_Dealer__c, ParentId, Parent.AccountNumber,CurrencyIsoCode,Org_Code__c FROM Account WHERE Id = :customerId AND Distributor_or_Dealer__c != null];

        if (accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('2nd Tier') && String.isNotEmpty(accList[0].ParentId) && accList[0].Parent.AccountNumber == Label.AceHareWareAccountNumber) {
            quotaData.isAce2ndTier = true;
            quotaData.Ace2ndTier_Customer = accList[0].ParentId;
        }
        if (accList.size() > 0) {
            quotaData.customerCurrencyIsoCode = accList[0].CurrencyIsoCode;
            quotaData.customerOrgCode = accList[0].Org_Code__c;
            quotaData.accountNumber = accList[0].AccountNumber;
        }

        if (String.isNotBlank(customerId)){
            Account acc = [SELECT AccountNumber FROM Account WHERE Id = :customerId LIMIT 1];
            quotaData.showTax = CCM_PurchaseOrderUtil.needCalculateTax(acc.AccountNumber);
            quotaData.customerId = customerId;
            quotaData.po.Customer__c = customerId;
            List<Sales_Program__c> authBrandList = [
                        SELECT Id, Customer__c, Brands__c, Freight_Term__c,
                                Approval_Status__c, IsDeleted
                        FROM Sales_Program__c
                        WHERE Customer__c =: customerId
                        AND Approval_Status__c = 'Approved'
                        AND IsDeleted = false];
            List<String> brandList = new List<String>();
            if (authBrandList != null && authBrandList.size() > 0){
                //TODO---> 给已经授权的Brand按Paymenr Term + Freight Term + Warehouse分组
                Map<String, List<String>> freightBrandMap = new Map<String, List<String>>();
                for (Sales_Program__c program : authBrandList){
                    if (String.isNotBlank(program.Brands__c)){
                        String wmsStr = '';
                        if (program.Brands__c == 'EGO'){
                            wmsStr = 'PE';
                        }else{
                            wmsStr = 'PT';
                        }
                        String keyVal = program.Freight_Term__c + wmsStr;
                        List<String> authBrands = new List<String>();
                        if (!brandList.contains(program.Brands__c)){
                            brandList.add(program.Brands__c);
                            if (freightBrandMap.containsKey(keyVal)){
                                authBrands = freightBrandMap.get(keyVal);
                                authBrands.add(program.Brands__c);
                            }else{
                                authBrands.add(program.Brands__c);
                            }
                            freightBrandMap.put(keyVal, authBrands);
                        }
                    }
                }

                if (!freightBrandMap.isEmpty()){
                    Set<String> freightSet = new Set<String>();
                    freightSet = freightBrandMap.keySet();
                    for (String freight : freightSet){
                        List<String> brands = new List<String>();
                        brands = freightBrandMap.get(freight);
                        System.debug(LoggingLevel.INFO, '*** brands: ' + brands);

                        Util.SelectItem brandScopeItem = getBrandOption(brands);
                        quotaData.brandScopeList.add(brandScopeItem);
                    }
                }
            }else{
                isSuccess = false;
                quotaData.errorMsg = 'No found any authorized brands, please contact the system administrator.';
            }
        }

        if (String.isNotBlank(recordId)){
            List<Purchase_Order__c> quotationList = (List<Purchase_Order__c>)Database.query(queryStr + ' WHERE Id = \'' + recordId + '\'');
            if(quotationList != null && quotationList.size() > 0){
                Purchase_Order__c poData = quotationList.get(0);
                quotaData.po = poData;

                // add,napoleon,23-1-1,two org code to return data;
                // po.org_code is a formula field, unsupported writability
                quotaData.po.Org_Id__c = poData.Customer__r.Org_Code__c;
                //Canada Tax
                if(quotaData.po.Org_Id__c  == 'CCA'){
                    quotaData.po.QST__c = poData.QST__c == null ? 0.00 : poData.QST__c;
                    quotaData.po.PST__c = poData.PST__c == null ? 0.00 : poData.PST__c;
                    quotaData.po.GST__c = poData.GST__c == null ? 0.00 : poData.GST__c;
                    quotaData.po.HST__c = poData.HST__c == null ? 0.00 : poData.HST__c;
                }
                // add end

                String priceBookId = null;
                //Add by wells start at 2023-01-03====
                if(poData.Customer__r.ORG_Code__c == 'CCA'){
                    // Name = 'CCA-Distributor Price for Parts' AND // remove name filter condition
                    priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != null AND ORG_Code__c ='CCA' AND Type__c = 'Service' AND Is_Active__c = true AND Customer_Type__c =: poData.Customer__r.Distributor_or_Dealer__c LIMIT 1].Price_Book__c;
                } else {
                //Add by wells end=at 2023-01-03===
                    if(quotaData.po.Customer__r.Distributor_or_Dealer__c.contains('Dealer') || quotaData.po.Customer__r.Distributor_or_Dealer__c.contains('Service Center')){
                        priceBookId = [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Direct Dealer Price for Parts' LIMIT 1].Price_Book__c;
                    }else if(quotaData.po.Customer__r.Distributor_or_Dealer__c.contains('Distributor')){
                        priceBookId = [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Distributor Price for Parts' LIMIT 1].Price_Book__c;
                    }
                }
                // update end

                for(Purchase_Order_Item__c poi : poData.Purchase_Order_Items__r){
                    poi.Price_Book__c = priceBookId;
                    if(poData.Customer__r.ORG_Code__c == CCM_Constants.ORG_CODE_CCA) {
                        poi.Ship_Date__c = Date.today();
                    }
                    else {
                        poi.Ship_Date__c = getMinSelectDate(poData.Submit_Date__c);
                    }
                }
                quotaData.poItems = poData.Purchase_Order_Items__r;
                quotaData.po.Shipping_priority__c = quotaData.po.Customer__r.Shipment_Priority__c;

                quotaData.po.Freight_Fee_To_Be_Waived__c = 0;

                System_Configuration__c sc = new System_Configuration__c();
                if(poData.Customer__r.Org_Code__c != null && poData.Customer__r.Org_Code__c == 'CNA' && quotaData.po.Product_Price__c != null && quotaData.po.Product_Price__c >= 100 ) {
                    sc.Freight_Charge__c        = 20.00;
                    sc.Freight_Fee_Waived__c    = 20.00;
                } else {
                    sc = Util.getFreightAndWaviedFee(poData.Customer__r.Org_Code__c, quotaData.po.Product_Price__c);
                }
                if (sc != null && quotaData.po.Shipping_By__c != 'Customer') {
                    quotaData.po.Freight_Fee__c                 = sc.Freight_Charge__c          == null ? 0 : sc.Freight_Charge__c;
                    quotaData.po.Freight_Fee_Waived__c          = sc.Freight_Fee_Waived__c      == null ? 0 : sc.Freight_Fee_Waived__c;
                    quotaData.po.Freight_Fee_To_Be_Waived__c    = sc.Freight_Fee_Waived__c      == null ? 0 : sc.Freight_Fee_Waived__c;
                    quotaData.po.Handling_Fee__c                = 0.00;
                } else if (quotaData.po.Shipping_By__c == 'Customer'){
                    quotaData.po.Freight_Fee__c                  = 0.00;
                    quotaData.po.Freight_Fee_Waived__c           = 0.00;
                    quotaData.po.Freight_Fee_To_Be_Waived__c     = 0.00;
                }
            }
        }
        // update,napoleon 22-12-30,add order type to CCA parts order.
        if (quotaData.customerOrgCode == 'CNA') {
            quotaData.po.Order_Type__c = CCM_Constants.PURCHASE_ORDER_ORDER_TYPE_CNA_PARTS_SALES_ORDER_USD;

        } else if (quotaData.customerOrgCode == 'CCA'){
            quotaData.po.Order_Type__c = CCM_Constants.PURCHASE_ORDER_ORDER_TYPE_CCA_PARTS_SALES_ORDER_CAD;

        }

        // add, napoleon, 23-1-1, feature update, po's payment term logic change
        // add, get customer's payment information while doing parts order
        String strPaymentFreight = getPaymentFreightTermByCustomerId(customerId,quotaData.customerOrgCode);
        PaymentFreightTerm pfPo = (PaymentFreightTerm)JSON.deserialize(strPaymentFreight, PaymentFreightTerm.class);
        // add end

        // add,napolen,23-2-1, CCA and CNA show different default payment,freight data
        // pls do not merge below two snippets and dealing different logic with diferent code is the best case
        if (quotaData.customerOrgCode == 'CCA') {
            if (quotaData.po.Shipping_By__c != 'Customer') {
                // description is `Prepaid @150.00`, PPB
                quotaData.po.Freight_Term__c    = String.isNotBlank(pfPo.freightTerm)? pfPo.freightTerm: 'PPB';
                quotaData.freightTermVal        = String.isNotBlank(pfPo.freightTermLabel)? pfPo.freightTermLabel: 'Prepaid @150.00';
                quotaData.freightTermRuleFee    = pfPo.freightTermRuleFee != 0.00 ? pfPo.freightTermRuleFee: 150.00;
            }
            quotaData.po.Payment_Term__c        = String.isNotBlank(pfPo.paymentTerm)? pfPo.paymentTerm: 'CA001';
            // add haibo: french
            if (UserInfo.getLanguage() == Label.CCM_Portal_French) {
                quotaData.paymentTermVal        = String.isNotBlank(pfPo.paymentTermLabel)? pfPo.paymentTermLabel: 'CA001 - 30 jours nets';
            } else {
                quotaData.paymentTermVal        = String.isNotBlank(pfPo.paymentTermLabel)? pfPo.paymentTermLabel: 'CA001 - NET 30 days';
            }
        } else if (quotaData.customerOrgCode == 'CNA'){
            if (quotaData.po.Shipping_By__c != 'Customer') {
                quotaData.po.Freight_Term__c    = String.isNotBlank(pfPo.freightTerm)? pfPo.freightTerm: 'Paid';
                quotaData.freightTermVal        = String.isNotBlank(pfPo.freightTermLabel)? pfPo.freightTermLabel: 'Prepaid @100';
                quotaData.freightTermRuleFee    = pfPo.freightTermRuleFee != 0.00 ? pfPo.freightTermRuleFee: 100.00;
            }
            quotaData.po.Payment_Term__c        = String.isNotBlank(pfPo.paymentTerm)? pfPo.paymentTerm: 'NA001';
            // add haibo: french
            if (UserInfo.getLanguage() == Label.CCM_Portal_French) {
                quotaData.paymentTermVal        = String.isNotBlank(pfPo.paymentTermLabel)? pfPo.paymentTermLabel: 'NA001 - 30 jours nets';
            } else {
                quotaData.paymentTermVal        = String.isNotBlank(pfPo.paymentTermLabel)? pfPo.paymentTermLabel: 'NA001 - NET 30 days';
            }
        }
        // add end

        quotaData.po.Handling_Fee__c                    = 0;
        quotaData.po.Freight_Fee_To_Be_Waived__c        = 0;
        quotaData.isSuccess = isSuccess;
        System.debug(LoggingLevel.INFO, '*** JSON.serialize(quotaData): ' + JSON.serialize(quotaData));
        return JSON.serialize(quotaData);
    }

    public static Boolean isInKobaltScope(String customerId) {
        Boolean inKobaltScope = false;
        List<Account> accList = [SELECT AccountNumber, Name, Is_Kobalt_Tractor__c FROM Account WHERE Id = :customerId];
        if(!accList.isEmpty()) {
            Account acc = accList[0];
            if(acc.AccountNumber == 'B10127' || acc.Is_Kobalt_Tractor__c) {
                inKobaltScope = true;
            }
        }
        return inKobaltScope;
    }

    //保存Purchase Order/Purchase Order Item
    @AuraEnabled
    public static String saveData(String poString, String poItemString, Integer currentStep){
        System.debug(LoggingLevel.INFO, '*** poString: ' + poString);
        System.debug(LoggingLevel.INFO, '*** poItemString: ' + poItemString);
        System.debug(LoggingLevel.INFO, '*** currentStep: ' + currentStep);
        QuotationData quotaData = new QuotationData();
        Purchase_Order__c po = (Purchase_Order__c)JSON.deserialize(poString, Purchase_Order__c.class);

        // po.Customer__c exists in po.
        // beacause `po.Order_Type__c` will be overwriten in fornt js script. rewrite the `Order_Type__c` in back-end with the low risk
        // order_type__c limit the options of line_type__c
        // CNA -> CNA General Line -> pick list default in field configuration; CCA -> CA Interco Line;
        // update,napoleon,22-12-30,add order type to CCA parts order.
        Account acc = [SELECT Org_Code__c, AccountNumber FROM Account WHERE Id = :po.Customer__c limit 1];
        String strOrgCodeOfCustomer = acc.Org_Code__c;
        String accountNumber = acc.AccountNumber;
        if (strOrgCodeOfCustomer == 'CNA') {
            po.Order_Type__c    = CCM_Constants.PURCHASE_ORDER_ORDER_TYPE_CNA_PARTS_SALES_ORDER_USD;
            po.CurrencyIsoCode  = 'USD';
        } else if (strOrgCodeOfCustomer == 'CCA') {
            po.Order_Type__c    = CCM_Constants.PURCHASE_ORDER_ORDER_TYPE_CCA_PARTS_SALES_ORDER_CAD;
            po.CurrencyIsoCode  = 'CAD';
        }
        // update end.

        String strCcaPartsOrderType     = po.Order_Type__c == CCM_Constants.PURCHASE_ORDER_ORDER_TYPE_CCA_PARTS_SALES_ORDER_CAD ? CCM_Constants.PURCHASE_ORDER_ORDER_TYPE_CCA_PARTS_SALES_ORDER_CAD : '';
        String strCcaPartsOrderItemType = 'CA Interco Line';

        Map<String, String> rtMap = Util.getSobjectRecordTypeNameMap('Purchase_Order__c');
        Po.RecordTypeId = rtMap.get('Place_Parts_Order');
        Map<String, Integer> stepMap = new Map<String, Integer>();
        Schema.DescribeFieldResult fieldResult = Purchase_Order__c.Step__c.getDescribe();
        List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
        if(po.Shipping_priority__c == null){
            po.Shipping_priority__c = po.Customer__r.Shipment_Priority__c;
        }

        Integer i = 0;
        for (Schema.PicklistEntry pickListVal : ple){
            i++;
            stepMap.put(pickListVal.getValue(), i);
        }
        po.Step__c = ple.get(currentStep - 1).getValue();
        System.debug(LoggingLevel.INFO, '*** po.Customer__c: ' + po.Customer__c);
        Id idParent;
        if (String.isNotBlank(po.Customer__c)) {
            for (Account_Address__c objAA : [
                SELECT Customer__c
                FROM Account_Address__c
                WHERE X2nd_Tier_Dealer__c = :po.Customer__c
                AND (RecordType_Name__c = 'Shipping_Address' OR RecordType_Name__c = 'Dropship_Shipping_Address')
                AND Approval_Status__c = 'Approved'
                AND Active__c = TRUE
                LIMIT 1
            ]) {
                idParent = objAA.Customer__c;
            }
        }
        if (String.isNotBlank(po.Customer__c)){
            if (String.isNotBlank(po.Billing_Address__c)){
                Set<String> setBrands = new Set<String>();
                if(String.isNotBlank(poItemString)){
                    List<Object> deserialized = (List<Object>)JSON.deserializeUntyped(poItemString);
                    for (Object instance : deserialized){
                        Map<String, Object> obj = (Map<String, Object>)instance;
                        // in internal, brand_name__c
                        // in community site, brand__c
                        // use set collection instead of list collection
                        String brandName = String.valueOf(obj.get('Brand_Name__c'));
                        String brand = String.valueOf(obj.get('Brand__c'));
                        if (String.isNotBlank(brandName)) {
                            setBrands.add(brandName);
                        } else if (String.isNotBlank(brand)) {
                            setBrands.add(brand);
                        }
                    }
                }
                String brandsItems = String.join(new List<String>(setBrands),'&');

                // Util.AddressOracleInfo bInfo = Util.getAddressOracelId(po.Billing_Address__c, String.isBlank(idParent) ? po.Customer__c : idParent, 'EGO&SKIL&SKILSAW&FLEX', true, true);
                Util.AddressOracleInfo bInfo = Util.getPartsOrderAddressOracelId(po.Billing_Address__c,
                                                String.isBlank(idParent) ? po.Customer__c : idParent,
                                                brandsItems, true);

                po.BillTo_OracleID__c = bInfo.oracelId;
                if (String.isNotBlank(bInfo.sfId)){
                    po.BillTo__c = bInfo.sfId;


                }
            }

            // @annotation
            // if Is_Alternative_Address__c = true, that means user is filling a tmp shipping address.
            // else, that means user is filling an address from customer's shipping address
            if (po.Is_Alternative_Address__c == false){
                if (String.isNotBlank(po.Shipping_Address__c)){
                    // Util.AddressOracleInfo sInfo = Util.getAddressOracelId(po.Shipping_Address__c, po.Customer__c,'EGO&SKIL&SKILSAW&FLEX', false, true);
                    Util.AddressOracleInfo sInfo = Util.getPartsOrderAddressOracelId(po.Shipping_Address__c,
                                                po.Customer__c, null, false);

                    po.ShipTo_OracleID__c = sInfo.oracelId;
                    System.debug(LoggingLevel.INFO, '*** sInfo.sfId: ' + sInfo.sfId);
                    if (String.isNotBlank(sInfo.sfId)){
                        po.ShipTo__c = sInfo.sfId;
                    }
                    Schema.DescribeSObjectResult r = Account_Address__c.sObjectType.getDescribe();
                    List<String> AddressApiNames =  new List<String>();
                    for(string apiName : r.fields.getMap().keySet()){
                        AddressApiNames.add(apiName);
                    }
                    AddressApiNames.add('Contact__r.Name');
                    AddressApiNames.add('Contact__r.Phone');
                    String queryStr = 'SELECT ' + String.join(AddressApiNames, ', ') + ' FROM Account_Address__c WHERE Id = \'' + po.Shipping_Address__c + '\'' ;
                    Account_Address__c add = Database.query(queryStr);

                    po.Additional_Shipping_Street__c = add.Address1__c;
                    po.Additional_Shipping_Street2__c = add.Address2__c;
                    po.Additional_Contact_Phone__c = add.Contact__r.Phone;
                    po.Additional_Contact_Name__c = add.Contact__r.Name;
                    po.Additional_Shipping_Country__c = add.Country__c;
                    po.Additional_Shipping_Postal_Code__c = add.Postal_Code__c;
                    po.Additional_Shipping_Province__c = add.State__c;
                    po.Additional_Shipping_City__c = add.City__c;

                     // add by John Jiang 2023-05-31
                    List<Address_With_Program__c> adds =[ SELECT Id,Account_Address__c FROM Address_With_Program__c WHERE Id =: po.ShipTo__c LIMIT 1];
                    if (adds.size() > 0) {
                        po.Shipping_Address__c = adds[0].Account_Address__c;
                    }

                }
            } else {
                for (Account_Address__c objAA : [
                    SELECT (SELECT Customer_Line_Oracle_ID__c FROM Addresses_With_Program__r LIMIT 1)
                    FROM Account_Address__c
                    WHERE
                        ((X2nd_Tier_Dealer__c = :po.Customer__c
                        AND X2nd_Tier_Dealer__r.Distributor_or_Dealer__c = '2nd Tier Dealer')
                        OR (Customer__c = :po.Customer__c
                        AND Customer__r.Distributor_or_Dealer__c != '2nd Tier Dealer'))
                        AND (RecordType_Name__c = 'Shipping_Address'
                        OR RecordType_Name__c = 'Dropship_Shipping_Address')
                        AND Approval_Status__c = 'Approved'
                        AND Active__c = TRUE
                    LIMIT 1
                ]) {
                    for (Address_With_Program__c objAP : objAA.Addresses_With_Program__r) {
                        po.ShipTo_OracleID__c = objAP.Customer_Line_Oracle_ID__c;
                        po.ShipTo__c = objAP.Id;
                    }
                }


                  // add by John Jiang 2023-05-31
                List<Address_With_Program__c> adds =[ SELECT Id,Account_Address__c FROM Address_With_Program__c WHERE Id =: po.ShipTo__c LIMIT 1];
                if (adds.size() > 0) {
                    po.Shipping_Address__c = adds[0].Account_Address__c;
                }
            }
        }


        // add,napoleon,22-12-31,get customer's payment information while doing parts order
        String strPaymentFreight = getPaymentFreightTermByCustomerId(po.Customer__c,strOrgCodeOfCustomer);
        PaymentFreightTerm pfPo = (PaymentFreightTerm)JSON.deserialize(strPaymentFreight, PaymentFreightTerm.class);

        System_Configuration__c sc = new System_Configuration__c();
        if(strOrgCodeOfCustomer != null && strOrgCodeOfCustomer == 'CNA' && po.Product_Price__c != null && po.Product_Price__c >= 100 ) {
            sc.Freight_Charge__c        = 20.00;
            sc.Freight_Fee_Waived__c    = 20.00;
        } else {
            sc = Util.getFreightAndWaviedFee(strOrgCodeOfCustomer, po.Product_Price__c);
        }

        if (sc != null && po.Shipping_By__c != 'Customer') {
            po.Freight_Fee__c                 = sc.Freight_Charge__c          == null ? 0 : sc.Freight_Charge__c;
            po.Freight_Fee_Waived__c          = sc.Freight_Fee_Waived__c      == null ? 0 : sc.Freight_Fee_Waived__c;
            po.Freight_Fee_To_Be_Waived__c    = sc.Freight_Fee_Waived__c      == null ? 0 : sc.Freight_Fee_Waived__c;
        } else if (po.Shipping_By__c == 'Customer'){
            po.Freight_Fee__c               = 0.00;
            po.Freight_Fee_Waived__c        = 0.00;
            po.Freight_Fee_To_Be_Waived__c  = 0.00;
        }

        if(String.isNotBlank(po.ShipTo__c)){
            // add,napoleon,23-1-2,Canada parts order QST,HST,GST taxt
            // tax save must be laid after the freight,handling fee.
            // Canada Tax
            Boolean needCalculateTax = CCM_PurchaseOrderUtil.needCalculateTax(accountNumber);
            if(strOrgCodeOfCustomer == 'CCA' && needCalculateTax){
                Util.caculateCcaPartsOrderTotalTax(po);
                quotaData.po.QST__c = po.QST__c == null ? 0.00 : po.QST__C;
                quotaData.po.GST__c = po.GST__c == null ? 0.00 : po.GST__c;
                quotaData.po.HST__c = po.HST__c == null ? 0.00 : po.HST__c;
                quotaData.po.PST__c = po.PST__c == null ? 0.00 : po.PST__c;
            }
            // add end
        }
        // update,napoleon,22-12-31, `payment_term,Freight_Term` follow customer's authorized brand

        if (strOrgCodeOfCustomer == 'CCA') {
            if (po.Shipping_By__c != 'Customer') {
                po.Freight_Term__c = String.isNotBlank(pfPo.freightTerm)? pfPo.freightTerm : 'PPB';
            }
            po.Payment_Term__c = String.isNotBlank(pfPo.paymentTerm)? pfPo.paymentTerm : 'CA001';

        } else if (strOrgCodeOfCustomer == 'CNA') {
            if (po.Shipping_By__c != 'Customer') {
                po.Freight_Term__c = String.isNotBlank(pfPo.freightTerm)? pfPo.freightTerm : 'Paid';
            }
            po.Payment_Term__c = String.isNotBlank(pfPo.paymentTerm)? pfPo.paymentTerm : 'NA001';
        }

        upsert po;

        Boolean isPriceChanged = false;

        List<Purchase_Order_Item__c> poItemsExsit = [SELECT Id FROM Purchase_Order_Item__c WHERE Purchase_Order__c = :po.Id];
        if(String.isNotBlank(poItemString)){
            List<Object> deserialized = (List<Object>)JSON.deserializeUntyped(poItemString);
            System.debug(LoggingLevel.INFO, '*** deserialized: ' + deserialized);
            List<String > finalJsonString = new List<String>();
            List<Purchase_Order_Item__c> poItems = new List<Purchase_Order_Item__c>();

            List<account> customerType = [SELECT Distributor_or_Dealer__c,AccountNumber,Org_Code__c FROM Account WHERE Id =: po.Customer__c];
            // @anonotation
            // Distributor_or_Dealer__c -> account.customer type
            String priceBookId = null;
            if(customerType.size() > 0){
                //Add by wells start at 2023-01-03====
                if(customerType[0].ORG_Code__c=='CCA'){
                    priceBookId =  [
                        SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c
                        WHERE Id != null
                            AND ORG_Code__c = 'CCA'
                            AND Type__c = 'Service'
                            AND Customer_Type__c =: customerType[0].Distributor_or_Dealer__c
                            AND Is_Active__c = true
                        LIMIT 1
                    ].Price_Book__c;
                } else {
                //Add by wells end=at 2023-01-03===
                    if(customerType[0].Distributor_or_Dealer__c.contains('Dealer') || customerType[0].Distributor_or_Dealer__c.contains('Service Center')){
                        priceBookId = [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Direct Dealer Price for Parts' LIMIT 1].Price_Book__c;
                    }else if(customerType[0].Distributor_or_Dealer__c.contains('Distributor')){
                        priceBookId = [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Distributor Price for Parts' LIMIT 1].Price_Book__c;
                    }
                }
            }

            if(customerType.size() > 0 && customerType[0].AccountNumber == 'B10127'){
                priceBookId =  [SELECT Id FROM Pricebook2 WHERE Name = 'CNA-MSRP for Parts' LIMIT 1].Id;
            }

            for (Object instance : deserialized){
                Map<String, Object> obj = (Map<String, Object>)instance;


                Purchase_Order_Item__c item = new Purchase_Order_Item__c();
                item.Price_Book__c = priceBookId;
                if(strOrgCodeOfCustomer == CCM_Constants.ORG_CODE_CCA) {
                    item.Ship_Date__c = Date.today();
                }
                else {
                    item.Ship_Date__c = getMinSelectDate(po.Submit_Date__c);
                }
                if(String.valueOf(obj.get('Parts__c')) != null){
                    item.Product__c = String.valueOf(obj.get('Parts__c'));
                }else{
                    item.Product__c = String.valueOf(obj.get('Product__c'));
                }

                item.Quantity__c = Integer.valueOf(obj.get('Quantity__c'));
                item.Purchase_Order__c = po.Id;
                item.Sub_Total__c = String.valueOf(obj.get('Sub_Total__c')) == null? 0.00: Decimal.valueOf(String.valueOf(obj.get('Sub_Total__c'))).setScale(2);
                item.Brand__c = String.valueOf(obj.get('Brand_Name__c'));
                item.ProductCode__c = String.valueOf(obj.get('ProductCode__c'));
                item.Unit_Price__c = Decimal.valueOf(String.valueOf(obj.get('Unit_Price__c')));
                item.Unit_Price__c = item.Unit_Price__c.setScale(2);
                item.CurrencyIsoCode = po.CurrencyIsoCode;
                // update,napoleon,23-1-19,add order line type, if po.Order_Type__c = CCM_Constants.PURCHASE_ORDER_ORDER_TYPE_CCA_PARTS_SALES_ORDER_CAD,CCA Internal Dropship -> CA Interco Line
                if (strCcaPartsOrderType == CCM_Constants.PURCHASE_ORDER_ORDER_TYPE_CCA_PARTS_SALES_ORDER_CAD) {
                    item.Line_Type__c = strCcaPartsOrderItemType;
                }

                poItems.add(item);
            }

            insert poItems;
            if(poItemsExsit.size() > 0){
                delete poItemsExsit;
            }
        }

        po.Need_Sales_Approval__c = isPriceChanged;
        update po;

        List<Purchase_Order__c> quotationList = (List<Purchase_Order__c>)Database.query(queryStr + ' WHERE Id = \'' + po.Id + '\'');
        if(quotationList != null && quotationList.size() > 0){
            Purchase_Order__c poData = quotationList.get(0);
            quotaData.po = poData;
            quotaData.poItems = poData.Purchase_Order_Items__r;
            if (String.isNotBlank(poData.Step__c)){
                quotaData.currentStep = stepMap.get(poData.Step__c);
            }
        }



        System.debug(LoggingLevel.INFO, '*** quotaData: ' + quotaData);
        return JSON.serialize(quotaData);
    }

    @AuraEnabled
    public static Date getMinSelectDate(Datetime submitDate) {
        Set<String> weekends = new Set<String> {
            'Saturday',
            'Sunday'
        };
        Datetime orderDate = Datetime.now();
        if(submitDate != null) {
            orderDate = submitDate;
        }
        Integer selectDays = 3;
        while (selectDays > 0) {
            orderDate = orderDate.addDays(1);
            String dayOfWeek = orderDate.format('EEEE');
            if(!weekends.contains(dayOfWeek)) {
                selectDays = selectDays - 1;
            }
        }
        return Date.newInstance(orderDate.year(), orderDate.month(), orderDate.day());
    }

    //获取Payment Term/Freight Term
    @AuraEnabled
    public static String getPaymentFreightTerm(String customerId, String brandName){
        PaymentFreightTerm term = new PaymentFreightTerm();
        if (String.isNotBlank(customerId) && String.isNotBlank(brandName)){
            Map<String, Freight_Term__mdt> freightRuleMap = Util.getFreightTermMap();
            List<Sales_Program__c> authBrands = Util.getAuthBrandInfo(customerId, brandName);
            if (authBrands != null && authBrands.size() > 0){
                Sales_Program__c authBrand = authBrands[0];
                term.paymentTerm = authBrand.Payment_Term__c;
                term.freightTerm = authBrand.Freight_Term__c;
                term.freightTermLabel = (String)authBrand.get('freightTermLabel');
                term.paymentTermLabel = (String)authBrand.get('paymentTermLabel');
                if (String.isNotBlank(authBrand.Freight_Term__c)){
                    Freight_Term__mdt fterm = freightRuleMap.get(authBrand.Freight_Term__c);
                    term.freightTermRuleFee = fterm.Freight_Fee__c;
                }
            }
        }

        return JSON.serialize(term);
    }
    // add,napoleon,22-12-19, modify the logic of paymentterm and freightterm for service customer
    // There are some differences betwen CCA and CNA,so divide the logic of paymentterm and freightterm into two methods which is flexiable in the future.
    @AuraEnabled
    public static String getPaymentFreightTermByCustomerId(String customerId,String strCustomerOrgCode){
        PaymentFreightTerm term = new PaymentFreightTerm();
        if (String.isNotBlank(customerId)){
            Map<String, Freight_Term__mdt> freightRuleMap = Util.getFreightTermMap();
            List<Sales_Program__c> authBrands = new List<Sales_Program__c>();
            if (strCustomerOrgCode == 'CNA') {
                authBrands = Util.getCnaSerivceAuthBrandInfo(customerId);
            } else if (strCustomerOrgCode == 'CCA') {
                authBrands = Util.getCcaSerivceAuthBrandInfo(customerId);
            }
            if (authBrands != null && authBrands.size() > 0){
                Sales_Program__c authBrand = authBrands[0];
                term.paymentTerm = authBrand.Payment_Term__c;
                term.freightTerm = authBrand.Freight_Term__c;
                term.freightTermLabel = (String)authBrand.get('freightTermLabel');
                term.paymentTermLabel = (String)authBrand.get('paymentTermLabel');
                if (String.isNotBlank(authBrand.Freight_Term__c)){
                    Freight_Term__mdt fterm = freightRuleMap.get(authBrand.Freight_Term__c);
                    term.freightTermRuleFee = fterm.Freight_Fee__c;
                }
            }
        }
        return JSON.serialize(term);
    }
    // add end

    //获取Price Infor
    @AuraEnabled
    public static String getPriceBook(String prodId, String customerId){
        return CCM_Quotation_ProductSelectCtl.getInternalPriceBook(prodId,customerId,'Service');
    }

    //获取Brands Options
    public static Util.SelectItem getBrandOption(List<String> brandOpts){
        Util.SelectItem brandScopeItem = new Util.SelectItem();
        if (brandOpts != null && brandOpts.size() > 0){
            Map<String, Util.SelectItem> brandOptMap = Util.getSelectOptMap(new Sales_Program__c(),'Brands__c');
            String finalStrLabel = '';
            String finalStrValue = '';
            for (String str : brandOpts){
                Util.SelectItem item = new Util.SelectItem();
                item = brandOptMap.get(str);
                finalStrLabel = finalStrLabel + ' & ' + item.label;
                finalStrValue = finalStrValue + '&' + item.value;
            }

            finalStrLabel = finalStrLabel.removeStart(' & ');
            finalStrValue = finalStrValue.removeStart('&');
            brandScopeItem.label = finalStrLabel;
            brandScopeItem.value = finalStrValue;
        }

        return brandScopeItem;
    }

    public class QuotationData{
        @AuraEnabled public Integer currentStep {get; set;}
        @AuraEnabled public String customerId {get; set;}
        @AuraEnabled public String accountNumber {get;set;}
        @AuraEnabled public String customerCurrencyIsoCode {get; set;}
        @AuraEnabled public String customerOrgCode {get; set;}
        @AuraEnabled public Boolean isAce2ndTier {get; set;}
        @AuraEnabled public String Ace2ndTier_Customer {get; set;}
        @AuraEnabled public Purchase_Order__c po {get; set;}
        @AuraEnabled public List<Purchase_Order_Item__c> poItems {get; set;}
        @AuraEnabled public List<Util.SelectItem> brandScopeList {get; set;}
        @AuraEnabled public String paymentTermVal {get; set;}
        @AuraEnabled public String freightTermVal {get; set;}
        @AuraEnabled public Decimal freightTermRuleFee {get; set;}
        @AuraEnabled public Boolean hasDropShipAddress { get; set; }
        @AuraEnabled public Boolean isSuccess {get; set;}
        @AuraEnabled public String errorMsg {get; set;}
        @AuraEnabled public Boolean showTax {get;set;}
        @AuraEnabled public Boolean inKobaltScope {get;set;}


        public QuotationData(){
            this.customerId = '';
            this.customerCurrencyIsoCode = '';
            this.customerOrgCode = '';
            this.Ace2ndTier_Customer = '';
            this.po = new Purchase_Order__c();
            this.poItems = new List<Purchase_Order_Item__c>();
            this.brandScopeList = new List<Util.SelectItem>();
            this.paymentTermVal = '';
            this.freightTermVal = '';
            this.freightTermRuleFee = 0.00;
            this.hasDropShipAddress = false;
            this.isSuccess = false;
            this.errorMsg = '';
            this.currentStep = 1;
            this.inKobaltScope = false;
        }
    }

    @AuraEnabled
    public static String submitData(String recordId, Boolean isDelegate){
        Purchase_Order__c po = new Purchase_Order__c(Id = recordId);

        System.debug(LoggingLevel.INFO, '*** isDelegate: ' + isDelegate);
        po.Approval_Status__c = 'Approved';
        po.Status__c= 'Submitted';


        update po;

        // system.enqueueJob(new CCM_callPushOrderToEBSQueue(po.Id));
        return 'Success';
    }

    @AuraEnabled
    public static String savePurchaseOrder(String poInfo, String poItemInfos, String customerId){
        SaveResult resp = new SaveResult();
        system.debug('**'+poInfo);
        if (String.isNotBlank(poInfo)){
            try{
                Purchase_Order__c po = (Purchase_Order__c)JSON.deserialize(poInfo, Purchase_Order__c.class);
                System.debug(LoggingLevel.INFO, '*** poInfo: ' + poInfo);
                Map<String, Integer> stepMap = new Map<String, Integer>();
                Schema.DescribeFieldResult fieldResult = Purchase_Order__c.Step__c.getDescribe();
                List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
                po.Order_Type__c = CCM_Constants.PURCHASE_ORDER_ORDER_TYPE_CNA_PARTS_SALES_ORDER_USD;
                po.Step__c = 'A';
                po.Approval_Status__c = 'Approved';
                po.Customer__c = customerId;
                po.Discount_Amount__c = po.Discount__c;
                po.Need_Sales_Approval__c = false;
                po.Status__c = 'Submitted';
                upsert po;
                resp.isSuccess = true;
                resp.result = po;

                if (String.isNotBlank(poItemInfos)){
                    List<Object> deserialized = (List<Object>)JSON.deserializeUntyped(poItemInfos);
                    List<String > finalJsonString = new List<String>();
                    List<Purchase_Order_Item__c> poItems = new List<Purchase_Order_Item__c>();
                    for (Object instance : deserialized){
                        Map<String, Object> obj = (Map<String, Object>)instance;
                        Purchase_Order_Item__c item = new Purchase_Order_Item__c();
                        item.Product__c = String.valueOf(obj.get('Product__c'));
                        item.Quantity__c = Integer.valueOf(obj.get('Quantity__c'));
                        item.Purchase_Order__c = po.Id;
                        item.List_Price__c = Decimal.valueOf(String.valueOf(obj.get('List_Price__c')));
                        item.Unit_Price__c = Decimal.valueOf(String.valueOf(obj.get('Unit_Price__c')));
                        if (item.Product__c != '' && item.Quantity__c > 0) {
                            poItems.add(item);
                        }
                    }
                    upsert poItems;
                }

                // system.enqueueJob(new CCM_callPushOrderToEBSQueue(po.Id));
            } catch (Exception ex) {
                resp.errorMsg = ex.getMessage();
            }

        }

        return JSON.serialize(resp);
    }



    public Class SaveResult {
        public Boolean isSuccess;
        public String errorMsg;
        public Purchase_Order__c result;

        public SaveResult() {
            isSuccess = false;
            errorMsg = '';
            result = new Purchase_Order__c();
        }
    }

    public class PaymentFreightTerm{
        @AuraEnabled public String paymentTerm {get; set;}
        @AuraEnabled public String paymentTermLabel {get; set;}
        @AuraEnabled public String freightTerm {get; set;}
        @AuraEnabled public String freightTermLabel {get; set;}
        @AuraEnabled public Decimal freightTermRuleFee {get; set;}

        public PaymentFreightTerm(){
            this.paymentTerm = '';
            this.paymentTermLabel = '';
            this.freightTerm = '';
            this.freightTermLabel = '';
            this.freightTermRuleFee = 0;
        }
    }
}