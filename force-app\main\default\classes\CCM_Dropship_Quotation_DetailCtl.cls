/**
 * @description This class is used to delegate Orders.
 * @revisions Chenyang Li 2021-02-13 Enable Order delegation for Tier 2 Dealer.
 *            Chenyang Li 2021-04-27 Update Ship Date logic: display +7 days once shipping date is chosen as today but send today to ERP.
 */
public without sharing class CCM_Dropship_Quotation_DetailCtl {
    public static String queryStr =
        'SELECT Id, ' +
        'Name, ' +  
        'Billing_Address__c, ' +
        'Billing_Address__r.Name, ' +
        'Billing_Address__r.Address1__c, ' +
        'Billing_Address__r.Address2__c, ' +
        'Billing_Address__r.Country__c, ' +
        'Billing_Address__r.State__c, ' +
        'Billing_Address__r.City__c, ' +
        'Billing_Address__r.Contact__c, ' +
        'Billing_Address__r.Contact__r.Name, ' +
        'Billing_Address_Name__c, ' +
        'Shipping_Address__c, ' +
        'Shipping_Address__r.Name, ' +
        'Shipping_Address_Name__c, ' +
        'Shipping_Address__r.Address1__c, ' +
        'Shipping_Address__r.Address2__c, ' +
        'Shipping_Address__r.Country__c, ' +
        'Shipping_Address__r.State__c, ' +
        'Shipping_Address__r.City__c, ' +
        'Shipping_Address__r.Postal_Code__c,' +
        'Shipping_Address__r.Contact__c, ' +
        'Shipping_Address__r.Contact__r.Name, ' +
        'Shipping_Address__r.Dropship_Contact__c, ' +
        'Shipping_Address__r.Customer__c, ' +
        'Shipping_Address__r.X2nd_Tier_Dealer__r.OwnerId, ' +
        'Shipping_Address__r.Dropship_Contact__r.Name, ' +
        'Is_Alternative_Address__c, ' +
        'Additional_Shipping_Street__c,' +
        'Additional_Shipping_City__c,' +
        'Additional_Shipping_Country__c,' +
        'Additional_Shipping_Province__c,' +
        'Additional_Shipping_Postal_Code__c,' +
        'Additional_Contact_Name__c,' +
        'Additional_Contact_Phone__c,' +
        'Additional_Contact_Email__c,' +
        'Shipping_Method__c, ' +
        'Shipping_By__c, ' +
        'tolabel(Shipping_By__c) shippingBy, ' +
        'Shipping_Priority__c, ' +
        'Delivery_Supplier__c, ' +
        'Customer__c, ' +
        'Customer__r.Name, ' +
        'Customer__r.Distributor_or_Dealer__c, ' +
        'Customer__r.CurrencyIsoCode, ' +
        'Customer__r.Shipment_Priority__c, ' +
        'Customer__r.Customer_Cluster__c, ' +
        'Dropship_Customer__c , ' +
        'Status__c, ' +
        'Is_Delegate__c, ' +
        'Submit_Date__c, ' +
        'Total_quantity__c, ' +
        'convertCurrency(Total_Price__c), ' +
        'convertCurrency(Total_Amount__c), ' +
        'convertCurrency(Discount_Amount__c), ' +
        'convertCurrency(Actual_Total_Product_Amount__c), ' +
        'Notes__c, ' +
        'Director_Approver__c, ' +
        'Approval_Status__c,' +
        'Approval_Date__c,' +
        'Approval_Comments__c,' +
        'Email__c, ' +
        'Phone__c, ' +
        'Fax_Number__c, ' +
        'Customer_PO_Num__c,' +
        'Expected_Delivery_Date__c,' +
        'Customer_Freight_Account__c,' +
        'Step__c,' +
        'Payment_Term__c,' +
        'Payment_Term_Promotion_Code__c,' +
        'Whole_Order_Promotion_Code__c,' +
        'Freight_Term__c,' +
        'tolabel(Payment_Term__c) paymentTermLabel, ' +
        'tolabel(Freight_Term__c) freightTermLabel, ' +
        'convertCurrency(Handling_Fee__c),' +
        'convertCurrency(Freight_Fee__c),' +
        'convertCurrency(Freight_Fee_Waived__c),' +
        'convertCurrency(Freight_Fee_To_Be_Waived__c),' +
        'convertCurrency(Freight_Target_Fee__c),' +
        'convertCurrency(Extra_Freight_Fee_To_Be_Waived__c),' +
        'Sales_Rep__c,' +
        'Sales_Manager__c,' +
        'Sales_Manager__r.Name,' +
        'convertCurrency(Product_Price__c),' +
        'Need_Sales_Approval__c,' +
        'CurrencyIsoCode,' +
        'Brand_Scope__c,' +
        'Promotion__c,' +
        'Promotion_Code__c,' +
        'Promotion__r.Promotion_Code__c,' +
        'Is_DropShip__c,' +
        'BillTo_OracleID__c,' +
        'ShipTo_OracleID__c,' +
        'Price_Book_List__c,' +
        'Order_Type__c,' +
        'Sales_Agency__c,' +
        'Sales_Agency__r.Alias_Formula__c,' +
        'ORG_ID__c,' +
        'convertCurrency(GST__c),' +
        'convertCurrency(HST__c),' +
        'convertCurrency(PST__c),' +
        'convertCurrency(QST__c),' +
        'convertCurrency(Total_Tax__c),' +
        'convertCurrency(Surcharge_Amount__c),' +
        '(SELECT Id, ' +
        '     Name, ' +
        '     Brand__c, ' +
        '     Product__c, ' +
        '     Price_Book__c, ' +
        '     ProductCode__c, ' +
        '     Product__r.Name, ' +
        '     Product__r.Weight__c, ' +
        '     Product__r.OverSize__c, ' +
        '     Product__r.Description, ' +
        '     Product__r.Item_Number__c, ' +
        '     Product__r.ProductCode, ' +
        '     Product__r.Brand_Name__c, ' +
        '     Product__r.Short_description__c, ' +
        '     Product__r.SF_Description__c, ' +
        '     Product__r.Cs_Exchange_Rate__c, ' +
        '     Quantity__c, ' +
        '     Gross_Weight__c, ' +
        '     Ship_Date__c, ' +
        '     CreatedDate, ' +
        '     convertCurrency(List_Price__c), ' +
        '     convertCurrency(Unit_Price__c), ' +
        '     MSRP__c, ' +
        '     Promotion__c, ' +
        '     Whole_Order_Promotion__c, ' +
        '     PromotionName__c, ' +
        '     Promotion_Rule_Name__c, ' +
        '     Is_Initial__c, ' +
        '     Whole_Order_Promotion_Code__c, ' +
        '     convertCurrency(Promo_Discount_Amount__c), ' +
        '     convertCurrency(Whole_Order_Promo_Discount_Amount__c), ' +
        '     convertCurrency(Discount_Amount__c), ' +
        '     convertCurrency(Sub_Total__c) ' +        
        '     FROM Purchase_Order_Items__r) ' +
        'FROM Purchase_Order__c';

    @AuraEnabled
    public static String getData(String recordId, String customerId) {
        system.debug('customerID--------------'+ customerId);
        Boolean isSuccess = true;
        QuotationData quotaData = new QuotationData();
        quotaData.isInnerUser = Util.isInnerUser();

        if (String.isNotBlank(recordId) && String.isBlank(customerId)) {
            customerId = [SELECT Id, Customer__c, Dropship_Customer__c FROM Purchase_Order__c WHERE Id = :recordId].Dropship_Customer__c;
        }

        //新建dropship，使用登陆人作为customerid
        if (String.isBlank(customerId) || customerId.equals('undefined') || customerId.equals('null')) {
            User currentUser = Util.getUserInfo(UserInfo.getUserId());
            if (currentUser.ContactId != null) {
                customerId = currentUser.Contact.AccountId;
            }
        }

        if (String.isBlank(recordId) && String.isNotBlank(customerId)) {
            Account account = [SELECT Id, Distributor_or_Dealer__c, Customer_Cluster__c,ORG_Code__c,Surcharge__c FROM Account WHERE Id = :customerId];
            quotaData.customerType = account.Distributor_or_Dealer__c;
            quotaData.customerCluster = account.Customer_Cluster__c;
            quotaData.customerOrgCode = String.isBlank(account.ORG_Code__c) ? 'CNA':account.ORG_Code__c;
            quotaData.surcharge = account.Surcharge__c != null ? account.Surcharge__c/100 : 0;
            if (quotaData.isInnerUser) {
                quotaData.hasDropShipAddress = Util.hasDropShipAddress(customerId);
            }

            //update by austin
            Set<Id> firstCustomerId = new Set<Id>();
            List<Sales_Hierarchy__c> buyingGroup = [SELECT X1st_tier_dealer__c, X1st_tier_dealer__r.Name, X2st_tier_dealer__c FROM Sales_Hierarchy__c 
                                                    WHERE X1st_tier_dealer__c =: customerId
                                                    AND Approval_Status__c =: CCM_Constants.SALES_HIERARCHY_APPROVAL_STATUS_APPROVED
                                                    AND Active__c = true];
            //用于搜索shipping address
            quotaData.secondTierCustomer.add(customerId);
            quotaData.dropshipCustomer = customerId;
            List<String> childCustomer = new List<String>();
            //判断当前customer 是否为buyingGroup,取二级对应的distributor
            if (buyingGroup.size() > 0) {
                for (Sales_Hierarchy__c bg : buyingGroup) {
                    childCustomer.add(bg.X2st_tier_dealer__c);
                    quotaData.secondTierCustomer.add(bg.X2st_tier_dealer__c);
                }
                List<Sales_Hierarchy__c>  buyingGroupMapping = [SELECT X1st_tier_dealer__c, X1st_tier_dealer__r.Name, X2st_tier_dealer__c FROM Sales_Hierarchy__c 
                                                                WHERE  X2st_tier_dealer__c =: customerId
                                                                AND Approval_Status__c =: CCM_Constants.SALES_HIERARCHY_APPROVAL_STATUS_APPROVED
                                                                AND Active__c = true];
                if (buyingGroupMapping.size() > 0 ) {
                    for (Sales_Hierarchy__c distributor : buyingGroupMapping) {
                        //firstCustomerId.add(distributor.X1st_tier_dealer__c);
                        Util.SelectItem distributorItem = new Util.SelectItem();
                        distributorItem.label = distributor.X1st_tier_dealer__r.Name;
                        distributorItem.value = distributor.X1st_tier_dealer__c;
                        quotaData.distributorScopeList.add(distributorItem);
                    }
                }
            }
            else{
                List<Sales_Hierarchy__c> storeMapping = [SELECT X1st_tier_dealer__c, X1st_tier_dealer__r.Name, X2st_tier_dealer__c FROM Sales_Hierarchy__c 
                                                        WHERE  X2st_tier_dealer__c =: customerId
                                                        AND Approval_Status__c =: CCM_Constants.SALES_HIERARCHY_APPROVAL_STATUS_APPROVED
                                                        AND Active__c = true];
                List<String> parentCuatomer = new List<String>();
                if (storeMapping.size() > 0) {
                    for (Sales_Hierarchy__c sm : storeMapping) {
                        parentCuatomer.add(sm.X1st_tier_dealer__c);
                    }
                    //判断当前的store对应的parentCustomer是否为buyingGroup
                    List<Sales_Hierarchy__c> buyingHierachy = [SELECT X1st_tier_dealer__c, X1st_tier_dealer__r.Name, X2st_tier_dealer__c FROM Sales_Hierarchy__c 
                                                                WHERE  X2st_tier_dealer__c =: parentCuatomer
                                                                AND Approval_Status__c =: CCM_Constants.SALES_HIERARCHY_APPROVAL_STATUS_APPROVED
                                                                AND Active__c = true];
                    if (buyingHierachy.size() > 0) {
                        for (Sales_Hierarchy__c distributor : buyingHierachy) {
                            //firstCustomerId.add(distributor.X1st_tier_dealer__c);
                            Util.SelectItem distributorItem = new Util.SelectItem();
                            distributorItem.label = distributor.X1st_tier_dealer__r.Name;
                            distributorItem.value = distributor.X1st_tier_dealer__c;
                            quotaData.distributorScopeList.add(distributorItem);
                        }
                    }else {
                        for (Sales_Hierarchy__c distributor : storeMapping) {
                            //firstCustomerId.add(distributor.X1st_tier_dealer__c);
                            Util.SelectItem distributorItem = new Util.SelectItem();
                            distributorItem.label = distributor.X1st_tier_dealer__r.Name;
                            distributorItem.value = distributor.X1st_tier_dealer__c;
                            quotaData.distributorScopeList.add(distributorItem);
                        }
                    }
                }   
            }           
            //end update by austin

            quotaData.paymentTermOptions = Util.getPaymentTermOptions(quotaData.customerType, quotaData.customerOrgCode);
        }

        if (String.isNotBlank(recordId)) {
            List<Purchase_Order__c> purchaseOrder = (List<Purchase_Order__c>) Database.query(queryStr + ' WHERE Id = \'' + recordId + '\'');
            //二级customerid，用于搜索shipping address
            quotaData.secondTierCustomer.add(customerId);
            //buying group
            List<Sales_Hierarchy__c> buyingGroup = [SELECT X1st_tier_dealer__c, X1st_tier_dealer__r.Name, X2st_tier_dealer__c FROM Sales_Hierarchy__c 
                                                    WHERE  X1st_tier_dealer__c =: customerId
                                                    AND Approval_Status__c =: CCM_Constants.SALES_HIERARCHY_APPROVAL_STATUS_APPROVED
                                                    AND Active__c = true];
            if (buyingGroup.size() > 0) {
                for (Sales_Hierarchy__c bg : buyingGroup) {
                    quotaData.secondTierCustomer.add(bg.X2st_tier_dealer__c);
                }
            }
            for (Purchase_Order__c objPO : purchaseOrder ) {
                for (Purchase_Order_Item__c objPOI : objPO.Purchase_Order_Items__r) {
                    // prettier-ignore
                    if (objPOI.CreatedDate.date() == objPOI.Ship_Date__c) objPOI.Ship_Date__c = objPOI.CreatedDate.date().addDays(7);
                }
                quotaData.po = objPO;
                quotaData.dropshipCustomer = objPO.Dropship_Customer__c;
                quotaData.poItems = objPO.Purchase_Order_Items__r;
                String termJSON = getPaymentFreightTerm(objPO.Customer__c, objPO.Brand_Scope__c, objPO.Is_DropShip__c, recordId);
                PaymentFreightTerm term = (PaymentFreightTerm) JSON.deserialize(termJSON, PaymentFreightTerm.class);
                quotaData.freightTermVal = term.freightTermLabel;
                quotaData.paymentTermVal = term.paymentTermLabel;
                quotaData.freightTermFee = term.freightTermRuleFee;
                quotaData.salesAgencyAlias = objPO.Sales_Agency__c != null ? objPO.Sales_Agency__r.Alias_Formula__c : '';
                quotaData.salesAgencyId = objPO.Sales_Agency__c;
            }
            //secCustomer -> top Customer info
            Map<String, String> topCustomerInfo = getTopCustomerInfo(new List<String>{quotaData.po.Dropship_Customer__c});
            for (String sh : topCustomerInfo.keySet()) {
                Util.SelectItem distributorItem = new Util.SelectItem();
                distributorItem.label = topCustomerInfo.get(sh);
                distributorItem.value = sh;
                quotaData.distributorScopeList.add(distributorItem);
            }
        }
        quotaData.isSuccess = isSuccess;
        return JSON.serialize(quotaData);
    }

    //update by austin
    @AuraEnabled
    public static string getCustomerBrand(String customerId){
            Boolean isSuccess = true;
            QuotationData quotaData = new QuotationData();
            quotaData.isInnerUser = Util.isInnerUser();

            Account account = [SELECT Id, Distributor_or_Dealer__c, Customer_Cluster__c,ORG_Code__c,Surcharge__c FROM Account WHERE Id = :customerId];
            quotaData.customerType = account.Distributor_or_Dealer__c;
            quotaData.customerCluster = account.Customer_Cluster__c;
            quotaData.customerId = customerId;
            quotaData.customerOrgCode = String.isBlank(account.ORG_Code__c) ? 'CNA':account.ORG_Code__c;
            quotaData.surcharge = account.Surcharge__c != null ? account.Surcharge__c/100 : 0;
            if (quotaData.isInnerUser) {
                quotaData.hasDropShipAddress = Util.hasDropShipAddress(customerId);
            }
            quotaData.paymentTermOptions = Util.getPaymentTermOptions(quotaData.customerType, quotaData.customerOrgCode);

            List<Sales_Program__c> authBrandList = [SELECT Id, Customer__c, Brands__c, Freight_Term__c, Approval_Status__c, IsDeleted, Payment_Term__c, RecordType.DeveloperName
                                                     FROM Sales_Program__c
                                                     WHERE Customer__c = :customerId    
                                                     AND RecordType.DeveloperName IN ('Dropship_Sales_Standard', 'Dropship_Sales_Customized')
                                                     AND IsDeleted = FALSE];               
            List<String> brandList = new List<String>();
            if (authBrandList != null && authBrandList.size() > 0) {
                //给已经授权的Brand按Paymenr Term + Freight Term + Warehouse分组
                Map<String, List<String>> freightBrandMap = new Map<String, List<String>>();
                for (Sales_Program__c program : authBrandList) {
                    if (String.isNotBlank(program.Brands__c)) {
                        String wmsStr = '';
                        if (program.Brands__c == 'EGO') {
                            wmsStr = 'PE';
                        } else {
                            wmsStr = 'PT';
                        }
                        String keyVal = program.Payment_Term__c + program.Freight_Term__c + wmsStr;
                        List<String> authBrands = new List<String>();
                        if (!brandList.contains(program.Brands__c)) {
                            brandList.add(program.Brands__c);
                            if (freightBrandMap.containsKey(keyVal)) {
                                authBrands = freightBrandMap.get(keyVal);
                                authBrands.add(program.Brands__c);
                            } else {
                                authBrands.add(program.Brands__c);
                            }
                            freightBrandMap.put(keyVal, authBrands);
                        }
                    }
                }

                if (!freightBrandMap.isEmpty()) {
                    Set<String> freightSet = new Set<String>();
                    freightSet = freightBrandMap.keySet();
                    for (String freight : freightSet) {
                        List<String> brands = new List<String>();
                        brands = freightBrandMap.get(freight);
                        System.debug(LoggingLevel.INFO, '*** brands: ' + brands);

                        Util.SelectItem brandScopeItem = getBrandOption(brands);
                        quotaData.brandScopeList.add(brandScopeItem);
                    }
                }
            } else {
                isSuccess = false;
                quotaData.errorMsg = 'No found any authorized brands, please contact the system administrator.';
            }
            return JSON.serialize(quotaData);
    }
    //end update by austin


    @AuraEnabled
    public static String getDataByInnerUser(String recordId, String customerId, String isDropShip) {
        Boolean isSuccess = true;
        QuotationData quotaData = new QuotationData();
        quotaData.isInnerUser = Util.isInnerUser();

        if (String.isNotBlank(recordId) && String.isBlank(customerId)) {
            Purchase_Order__c poInfo = [SELECT Id, Customer__c FROM Purchase_Order__c WHERE Id = :recordId];
            customerId = poInfo.Customer__c;
        }

        if (String.isBlank(customerId) || customerId.equals('undefined') || customerId.equals('null')) {
            User currentUser = Util.getUserInfo(UserInfo.getUserId());
            if (currentUser.ContactId != null) {
                customerId = currentUser.Contact.AccountId;
            }
        }

        if (String.isNotBlank(customerId)) {
            quotaData.customerId = customerId;
            Account account = [SELECT Id, Distributor_or_Dealer__c, Customer_Cluster__c, ORG_Code__c,Surcharge__c FROM Account WHERE Id = :customerId];
            quotaData.customerType = account.Distributor_or_Dealer__c;
            quotaData.customerCluster = account.Customer_Cluster__c;
            quotaData.customerOrgCode = String.isBlank(account.ORG_Code__c) ? 'CNA':account.ORG_Code__c;
            quotaData.surcharge = account.Surcharge__c != null ? account.Surcharge__c/100 : 0;

            if (quotaData.isInnerUser) {
                quotaData.hasDropShipAddress = Util.hasDropShipAddress(customerId);
            }
            quotaData.po.Customer__c = customerId;
            Map<String, String> dropShipFreightTermMap = Util.getDropShipFreightTermMap(quotaData.customerOrgCode=='CCA');
            List<Sales_Program__c> authBrandList = [SELECT Id, Customer__c, Brands__c, Freight_Term__c, Approval_Status__c, IsDeleted, Payment_Term__c, RecordType.DeveloperName
                                                        FROM Sales_Program__c
                                                        WHERE Customer__c = :customerId 
                                                        AND RecordType.DeveloperName != 'Service'
                                                        AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME
                                                        AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_NAME 
                                                        AND IsDeleted = FALSE
            ];
            List<String> brandList = new List<String>();
            if (authBrandList != null && authBrandList.size() > 0) {
                //给已经授权的Brand按Paymenr Term + Freight Term + Warehouse分组
                Map<String, List<String>> freightBrandMap = new Map<String, List<String>>();
                for (Sales_Program__c program : authBrandList) {
                    if (String.isNotBlank(program.Brands__c)) {
                        String wmsStr = '';
                        if (program.Brands__c == 'EGO') {
                            wmsStr = 'PE';
                        } else {
                            wmsStr = 'PT';
                        }
                        String freightTermStr = '';
                        if (isDropShip == 'Y') {
                            freightTermStr = dropShipFreightTermMap.get('program.Brands__c');
                        } else {
                            freightTermStr = program.Freight_Term__c;
                        }
                        String keyVal = program.Payment_Term__c + freightTermStr + wmsStr;
                        List<String> authBrands = new List<String>();
                        if (!brandList.contains(program.Brands__c)) {
                            brandList.add(program.Brands__c);
                            if (freightBrandMap.containsKey(keyVal)) {
                                authBrands = freightBrandMap.get(keyVal);
                                authBrands.add(program.Brands__c);
                            } else {
                                authBrands.add(program.Brands__c);
                            }
                            freightBrandMap.put(keyVal, authBrands);
                        }
                    }
                }

                if (!freightBrandMap.isEmpty()) {
                    Set<String> freightSet = new Set<String>();
                    freightSet = freightBrandMap.keySet();
                    for (String freight : freightSet) {
                        List<String> brands = new List<String>();
                        brands = freightBrandMap.get(freight);
                        System.debug(LoggingLevel.INFO, '*** brands: ' + brands);

                        Util.SelectItem brandScopeItem = getBrandOption(brands);
                        quotaData.brandScopeList.add(brandScopeItem);
                    }
                }
                quotaData.paymentTermOptions = Util.getPaymentTermOptions(quotaData.customerType, quotaData.customerOrgCode);
            } else {
                isSuccess = false;
                quotaData.errorMsg = 'No found any authorized brands, please contact the system administrator.';
            }
        }

        if (String.isNotBlank(recordId)) {
            List<Purchase_Order__c> quotationList = (List<Purchase_Order__c>) Database.query(queryStr + ' WHERE Id = \'' + recordId + '\'');
            if (quotationList != null && quotationList.size() > 0) {
                Purchase_Order__c poData = quotationList.get(0);
                if(String.isNotBlank(customerId) && poData.Customer__c ==customerId){
                    quotaData.po = poData;
                    quotaData.poItems = poData.Purchase_Order_Items__r;
                    String termJSON = getPaymentFreightTerm(
                        poData.Customer__c,
                        poData.Brand_Scope__c,
                        poData.Is_DropShip__c,
                        recordId
                    );
                    PaymentFreightTerm term = (PaymentFreightTerm) JSON.deserialize(
                        termJSON,
                        PaymentFreightTerm.class
                    );
                    quotaData.freightTermVal = term.freightTermLabel;
                    quotaData.paymentTermVal = term.paymentTermLabel;
                    quotaData.defautPaymentTerm = term.paymentTerm;
                    quotaData.paymentTermValue = poData.Payment_Term__c;
                }else{
                    quotaData.po = new Purchase_Order__c();
                }
                
            }
        }
        quotaData.isSuccess = isSuccess;
        System.debug(LoggingLevel.INFO, '*** JSON.serialize(quotaData): ' + JSON.serialize(quotaData));
        return JSON.serialize(quotaData);
    }

    //保存Purchase Order/Purchase Order Item
    @AuraEnabled
    public static String saveData(String poString, String poItemString, Integer currentStep) {
        Purchase_Order__c po = (Purchase_Order__c) JSON.deserialize(poString, Purchase_Order__c.class);        
        Map<String, Integer> stepMap = new Map<String, Integer>();
        Schema.DescribeFieldResult fieldResult = Purchase_Order__c.Step__c.getDescribe();
        List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
        Integer i = 0;
        for (Schema.PicklistEntry pickListVal : ple) {
            i++;
            stepMap.put(pickListVal.getValue(), i);
        }
        po.Step__c = ple.get(currentStep - 1).getValue();
        //po customer    
        Account acc = null;
        if (String.isNotBlank(po.Customer__c)) {
            //获取审批人
            acc = [SELECT Distributor_or_Dealer__c, Director_Approver__c,ORG_Code__c,Surcharge__c FROM Account WHERE Id = :po.Customer__c];
            po.Director_Approver__c = acc.Director_Approver__c;

            //Org Code of purchase order
            po.ORG_ID__c = acc.ORG_Code__c;
            Id idParent;
            if ('2nd Tier Dealer'.equals(acc.Distributor_or_Dealer__c)) {
                for (Account_Address__c objShipTo : [
                    SELECT Customer__c
                    FROM Account_Address__c
                    WHERE
                        X2nd_Tier_Dealer__c = :acc.Id
                        AND (RecordType_Name__c = 'Shipping_Address'
                        OR RecordType_Name__c = 'Dropship_Shipping_Address')
                        AND Approval_Status__c =: CCM_Constants.SALES_HIERARCHY_APPROVAL_STATUS_APPROVED
                        AND Active__c = true
                        AND Active__c = TRUE
                    LIMIT 1
                ]) {
                    idParent = objShipTo.Customer__c;
                    break;
                }
            }
            //获取Billing Address中间表上的OracelId (EBS)
            if (String.isNotBlank(po.Billing_Address__c)) { 
                Util.AddressOracleInfo bInfo = Util.getAddressOracelId(
                    po.Billing_Address__c,
                    String.isNotBlank(idParent) ? idParent : po.Customer__c,
                    po.Brand_Scope__c,
                    true,
                    true,
                    'Purchase Order'
                );
                po.BillTo_OracleID__c = bInfo.oracelId;
                if (String.isNotBlank(bInfo.sfId)) {
                    po.BillTo__c = bInfo.sfId;
                } else {
                    throw new AuraHandledException('Selected bill to address is not related to the sales authorized brand, Please go to last step and select another bill to address.');
                }
            }

            if (po.Is_Alternative_Address__c == false) {
                //获取Shipping Address中间表上的OracelId (EBS)
                if (String.isNotBlank(po.Shipping_Address__c)) {
                    Util.AddressOracleInfo sInfo = Util.getAddressOracelId(po.Shipping_Address__c, po.Customer__c, po.Brand_Scope__c, false, true, 'Purchase Order');
                    po.ShipTo_OracleID__c = sInfo.oracelId;
                    System.debug(LoggingLevel.INFO, '*** sInfo.sfId: ' + sInfo.sfId);
                    if (String.isNotBlank(sInfo.sfId)) {
                        po.ShipTo__c = sInfo.sfId;
                    }
                    Schema.DescribeSObjectResult r = Account_Address__c.sObjectType.getDescribe();
                    List<String> AddressApiNames = new List<String>();
                    for (string apiName : r.fields.getMap().keySet()) {
                        AddressApiNames.add(apiName);
                    }
                    AddressApiNames.add('Contact__r.Name');
                    AddressApiNames.add('Contact__r.Phone');
                    String queryStr = 'SELECT ' + String.join(AddressApiNames, ', ') + ' FROM Account_Address__c WHERE Id = \'' + po.Shipping_Address__c + '\'';
                    Account_Address__c add = Database.query(queryStr);
                    po.Additional_Shipping_Street__c = add.Address1__c;
                    po.Additional_Shipping_Street2__c = add.Address2__c;
                    po.Additional_Contact_Phone__c = add.Contact__r.Phone;
                    po.Additional_Contact_Name__c = add.Contact__r.Name;
                    po.Additional_Shipping_Country__c = add.Country__c;
                    po.Additional_Shipping_Postal_Code__c = add.Postal_Code__c;
                    po.Additional_Shipping_Province__c = add.State__c;
                    po.Additional_Shipping_City__c = add.City__c;
                }
            } else {
                //Customer 临时地址的处理
                Util.AddressOracleInfo sAInfo = Util.getAlternativeAddressInfo(po.Customer__c, po.Brand_Scope__c);
                po.ShipTo_OracleID__c = sAInfo.oracelId;
                if (String.isNotBlank(sAInfo.sfId)) {
                    po.ShipTo__c = sAInfo.sfId;
                }
            }

            //Surcharge Amount
            // if(acc.Surcharge__c != null && acc.Surcharge__c > 0){
            //     po.Surcharge_Amount__c = po.Discount_Amount__c != null ? ((po.Product_Price__c - Math.abs(po.Discount_Amount__c)) * acc.Surcharge__c/100).setScale(2,System.RoundingMode.HALF_UP) : 0.00;
            // }
        }

        //handling fee for CCA
        if(po.Product_Price__c!=null && po.ORG_ID__c == 'CCA'){
            Decimal productPrice = po.Product_Price__c ;
            if(productPrice < 250){
                po.Handling_Fee__c = productPrice * 0.1;
            }else{
                po.Handling_Fee__c = 0;
            }
        }

        //tax for CCA
        if(((!po.Is_Alternative_Address__c && String.isNotBlank(po.ShipTo__c)) 
            || (po.Is_Alternative_Address__c && String.isNotBlank(po.Additional_Shipping_Province__c))) 
            && po.ORG_ID__c == 'CCA'){
            //caculate Canada total tax by shipping address
            caculateTotalTax(po);
        }

        // if (po.Is_DropShip__c == true || po.Is_Alternative_Address__c == true) {
            if(po.ORG_ID__c == 'CCA'){                
                po.Order_Type__c = 'CA Dropship Order';
            }else{
                po.Order_Type__c = 'CNA Dropship Order'; 
            }                       
        // } else {
        //     if(po.ORG_ID__c == 'CCA'){                
        //         po.Order_Type__c = 'CA Sales Order - CAD';
        //     }else{
        //         po.Order_Type__c = 'CNA Sales Order - USD';
        //     }            
        // }

        upsert po;

        //Product Item weight
        List<Decimal> prodWeightList = new List<Decimal>();
        //OverSize Product ---> oversize Prod Qty * 100
        Decimal overSizeAmount = 0.00;
        //Total Surcharge Amount
        Decimal totalSurchargeAmout = 0.00;

        List<Purchase_Order_Item__c> poItemsExsit = [SELECT Id FROM Purchase_Order_Item__c WHERE Purchase_Order__c = :po.Id];
        List<Purchase_Order_Item__c> poItems = new List<Purchase_Order_Item__c>();
        if (String.isNotBlank(poItemString)) {
            List<Object> deserialized = (List<Object>) JSON.deserializeUntyped(poItemString);
            System.debug(LoggingLevel.INFO, '*** deserialized: ' + deserialized);
            List<String> finalJsonString = new List<String>();
            for (Object instance : deserialized) {
                Map<String, Object> obj = (Map<String, Object>) instance;
                Purchase_Order_Item__c item = new Purchase_Order_Item__c();
                item.Product__c = String.valueOf(obj.get('Product__c'));
                item.Quantity__c = Integer.valueOf(obj.get('Quantity__c'));
                item.Brand__c = String.valueOf(obj.get('Brand__c'));
                if (obj.get('Ship_Date__c') != null && obj.get('Ship_Date__c') != '') {
                    item.Ship_Date__c = Date.valueOf(String.valueOf(obj.get('Ship_Date__c')));
                }
                item.ProductCode__c = String.valueOf(obj.get('ProductCode__c'));
                item.Purchase_Order__c = po.Id;
                item.Price_Book__c = String.valueOf(obj.get('Price_Book__c'));
                item.List_Price__c = Decimal.valueOf(String.valueOf(obj.get('List_Price__c'))).setScale(2);
                item.Unit_Price__c = Decimal.valueOf(String.valueOf(obj.get('Unit_Price__c'))).setScale(2);
                if (obj.get('Discount_Amount__c') != null) {
                    item.Discount_Amount__c = Decimal.valueOf(String.valueOf(obj.get('Discount_Amount__c'))).setScale(2);
                }

                item.Sub_Total__c = String.valueOf(obj.get('Sub_Total__c')) == null ? 0.00 : Decimal.valueOf(String.valueOf(obj.get('Sub_Total__c'))).setScale(2);

                item.Is_Over_Size_Product__c = String.valueOf(obj.get('OverSize__c')) == null ? false : Boolean.valueOf(String.valueOf(obj.get('OverSize__c')));

                item.Gross_Weight__c = obj.get('Gross_Weight__c') == null ? 0.00 : Decimal.valueOf(String.valueOf(obj.get('Gross_Weight__c')));

                //Promotion Info
                if(obj.get('Promotion__c') != null){
                    item.Promotion__c = String.valueOf(obj.get('Promotion__c'));
                }
                if(obj.get('PromotionName__c') != null){
                    item.PromotionName__c = String.valueOf(obj.get('PromotionName__c'));
                }
                if(obj.get('Promotion_Rule_Name__c') != null){
                    item.Promotion_Rule_Name__c = String.valueOf(obj.get('Promotion_Rule_Name__c'));
                }
                if(obj.get('Whole_Order_Promotion__c') != null){
                    item.Whole_Order_Promotion__c = String.valueOf(obj.get('Whole_Order_Promotion__c'));
                }
                if (obj.get('Promo_Discount_Amount__c') != null) {
                    item.Promo_Discount_Amount__c = Decimal.valueOf(
                            String.valueOf(obj.get('Promo_Discount_Amount__c'))
                        )
                        .setScale(2);
                }
                if (obj.get('Whole_Order_Promo_Discount_Amount__c') != null) {
                    item.Whole_Order_Promo_Discount_Amount__c = Decimal.valueOf(
                            String.valueOf(obj.get('Whole_Order_Promo_Discount_Amount__c'))
                        )
                        .setScale(2);
                }
                if(obj.get('Is_Initial__c') != null){
                    item.Is_Initial__c = Boolean.valueOf(obj.get('Is_Initial__c'));
                }
                if(obj.get('Regular_Promotion_Window__c') != null){
                    item.Regular_Promotion_Window__c = String.valueOf(obj.get('Regular_Promotion_Window__c'));
                }

                //Org Code of purchase order items
                item.ORG_Code__c = po.ORG_ID__c;

                if(item.ORG_Code__c == 'CCA'){
                    item.Line_Type__c = 'CA General Line';
                }

                //Surcharge Amount
                if(acc!= null && acc.Surcharge__c != null && acc.Surcharge__c > 0){
                    item.Surcharge_Amount__c = (item.Unit_Price__c * item.Quantity__c * acc.Surcharge__c/100).setScale(2,System.RoundingMode.HALF_UP);
                    totalSurchargeAmout+=item.Surcharge_Amount__c;
                }

                poItems.add(item);
            }

            upsert poItems;
            if (poItemsExsit.size() > 0) {
                delete poItemsExsit;
            }

            //Total Surcharge Amount
            if(acc!= null && acc.Surcharge__c != null && acc.Surcharge__c > 0){
                po.Surcharge_Amount__c = totalSurchargeAmout;
                update po;
            }
        }

        QuotationData quotaData = new QuotationData();
        List<Purchase_Order__c> quotationList = (List<Purchase_Order__c>) Database.query(queryStr + ' WHERE Id = \'' + po.Id + '\'');
        if (quotationList != null && quotationList.size() > 0) {
            Purchase_Order__c poData = quotationList.get(0);
            quotaData.po = poData;
            quotaData.poItems = poData.Purchase_Order_Items__r;
            if (String.isNotBlank(poData.Step__c)) {
                quotaData.currentStep = stepMap.get(poData.Step__c);
            }
        }

        System.debug(LoggingLevel.INFO, '*** quotaData: ' + quotaData);
        return JSON.serialize(quotaData);
    }

    @AuraEnabled
    public static String getPaymentTerm(
        String customerId,
        String brandName,
        String recordId
    ) {
        PaymentFreightTerm term = new PaymentFreightTerm();
        Map<String, Payment_Term__mdt> paymentRuleMap = Util.getPaymentTermMap();
        System.debug('*** customerId: ' + customerId);
        System.debug('*** brandName: ' + brandName);
        List<Sales_Program__c> authBrands = Util.getAuthBrandInfoSales(customerId, brandName);

        if (authBrands != null && authBrands.size() > 0){
            Sales_Program__c authBrand = authBrands[0];
            term.paymentTerm = authBrand.Payment_Term__c;

            Payment_Term__mdt pterm = new Payment_Term__mdt();
            if (String.isNotBlank(authBrand.Payment_Term__c)) {
                pterm = paymentRuleMap.get(authBrand.Payment_Term__c);
                term.paymentTermLabel = pterm.Description__c;
            }
        }
        return JSON.serialize(term);
    }

    ////获取Payment Term/Freight Term(如果有PO的RecordId,则获取PO的Payment Term信息，否则获取Authorized Brand上的默认Payment Term)
    @AuraEnabled
    public static String getPaymentFreightTerm(String customerId, String brandName, Boolean isDropShip, String recordId) {
        PaymentFreightTerm term = new PaymentFreightTerm();
        if (String.isNotBlank(customerId) && String.isNotBlank(brandName)) {
            Map<String, Freight_Term__mdt> freightRuleMap = Util.getFreightTermMap();
            Map<String, Payment_Term__mdt> paymentRuleMap = Util.getPaymentTermMap();

            List<Sales_Program__c> authBrands = Util.getDropShipAuthBrandInfoSales(customerId, brandName);
            system.debug('获取authBrands======='+authBrands);
            if (authBrands != null && authBrands.size() > 0) {
                Sales_Program__c authBrand = authBrands[0];
                term.paymentTerm = authBrand.Payment_Term__c;
                term.freightTerm = authBrand.Freight_Term__c;

                if (String.isNotBlank(term.freightTerm)) {
                    Freight_Term__mdt fterm = freightRuleMap.get(term.freightTerm);
                    term.freightTermRuleFee = fterm.Freight_Fee__c;
                    term.freightTermLabel = fterm.Description__c;
                }

                

                Payment_Term__mdt pterm = new Payment_Term__mdt();
                if (String.isNotBlank(authBrand.Payment_Term__c)) {
                    pterm = paymentRuleMap.get(authBrand.Payment_Term__c);
                    term.paymentTermLabel = pterm.Description__c;
                }
                if (String.isNotBlank(recordId)) {
                    Purchase_Order__c po = Util.getPOPaymentTermInfo(recordId);
                    if (String.isNotBlank(po.Payment_Term__c)) {
                        pterm = paymentRuleMap.get(po.Payment_Term__c);
                        term.paymentTermLabel = pterm.Description__c;
                        system.debug('term.paymentTermLabel======'+ term.paymentTermLabel);
                    } 
                } 
            }
        }

        return JSON.serialize(term);
    }

    @AuraEnabled
    public static String getDropShipPaymentFreightTerm(String customerId, String brandName, String isDropShip) {
        PaymentFreightTerm term = new PaymentFreightTerm();
        if (String.isNotBlank(customerId) && String.isNotBlank(brandName)) {
            Map<String, Freight_Term__mdt> freightRuleMap = Util.getFreightTermMap();
            Map<String, Payment_Term__mdt> paymentRuleMap = Util.getPaymentTermMap();

            List<Sales_Program__c> authBrands = Util.getDropShipAuthBrandInfoSales(customerId, brandName);
            if (authBrands != null && authBrands.size() > 0) {
                Sales_Program__c authBrand = authBrands[0];
                term.paymentTerm = authBrand.Payment_Term__c;
                term.freightTerm = authBrand.Freight_Term__c;

                if (String.isNotBlank(term.freightTerm)) {
                    Freight_Term__mdt fterm = freightRuleMap.get(term.freightTerm);
                    term.freightTermRuleFee = fterm.Freight_Fee__c;
                    term.freightTermLabel = fterm.Description__c;
                }
                if (String.isNotBlank(authBrand.Payment_Term__c)) {
                    Payment_Term__mdt pterm = paymentRuleMap.get(authBrand.Payment_Term__c);
                    term.paymentTermLabel = pterm.Description__c;
                }
            }
        }

        return JSON.serialize(term);
    }

    //获取Price Infor
    @AuraEnabled
    public static String getPriceBook(String prodId, String customerId, String orderType) {
        if (orderType == 'Y') {
            return CCM_Quotation_ProductSelectCtl.getDropShipInternalPriceBook(prodId, customerId);
        } else {
            return CCM_Quotation_ProductSelectCtl.getInternalPriceBook(prodId, customerId, 'Dropship Sales');
        }
    }

    //获取Brands Options
    public static Util.SelectItem getBrandOption(List<String> brandOpts) {
        Util.SelectItem brandScopeItem = new Util.SelectItem();
        if (brandOpts != null && brandOpts.size() > 0) {
            Map<String, Util.SelectItem> brandOptMap = Util.getSelectOptMap(new Sales_Program__c(), 'Brands__c');
            String finalStrLabel = '';
            String finalStrValue = '';
            for (String str : brandOpts) {
                Util.SelectItem item = new Util.SelectItem();
                item = brandOptMap.get(str);
                finalStrLabel = finalStrLabel + ' & ' + item.label;
                finalStrValue = finalStrValue + '&' + item.value;
            }

            finalStrLabel = finalStrLabel.removeStart(' & ');
            finalStrValue = finalStrValue.removeStart('&');
            brandScopeItem.label = finalStrLabel;
            brandScopeItem.value = finalStrValue;
        }

        return brandScopeItem;
    }

    public class QuotationData {
        @AuraEnabled
        public Integer currentStep { get; set; }
        @AuraEnabled
        public String customerId { get; set; }
        @AuraEnabled
        public Purchase_Order__c po { get; set; }
        @AuraEnabled
        public List<Purchase_Order_Item__c> poItems { get; set; }
        @AuraEnabled
        public List<Util.SelectItem> brandScopeList { get; set; }
        @AuraEnabled
        public String paymentTermVal { get; set; }
        @AuraEnabled
        public String freightTermVal { get; set; }
        @AuraEnabled
        public Boolean isSuccess { get; set; }
        @AuraEnabled
        public String errorMsg { get; set; }
        @AuraEnabled
        public Boolean isInnerUser { get; set; }
        @AuraEnabled
        public Boolean hasDropShipAddress { get; set; }
        @AuraEnabled
        public String salesAgencyAlias { get; set; }
        @AuraEnabled
        public String salesAgencyId { get; set; }
        @AuraEnabled
        public Decimal freightTermFee { get; set; }
        @AuraEnabled
        public List<Util.PTSelectItem> paymentTermOptions { get; set; }
        @AuraEnabled
        public String customerType { get; set; }
        @AuraEnabled
        public String customerCluster { get; set; }
        @AuraEnabled
        public String paymentTermValue { get; set; }
        @AuraEnabled
        public String defautPaymentTerm { get; set; }
        @AuraEnabled
        public String customerOrgCode { get; set; }
        @AuraEnabled
        public Decimal surcharge { get; set; }
        @AuraEnabled
        public List<Util.SelectItem> distributorScopeList { get; set; }
        @AuraEnabled
        public List<String> secondTierCustomer { get; set; }
        @AuraEnabled
        public String dropshipCustomer { get; set; }



        public QuotationData() {
            this.customerId = '';
            this.po = new Purchase_Order__c();
            this.poItems = new List<Purchase_Order_Item__c>();
            this.brandScopeList = new List<Util.SelectItem>();
            this.paymentTermOptions = new List<Util.PTSelectItem>();
            this.secondTierCustomer = new List<String>();
            this.paymentTermVal = '';
            this.freightTermVal = '';
            this.isSuccess = false;
            this.errorMsg = '';
            this.currentStep = 1;
            this.isInnerUser = false;
            this.hasDropShipAddress = false;
            this.salesAgencyAlias = '';
            this.salesAgencyId = '';
            this.freightTermFee = 0;
            this.customerType = '';
            this.customerCluster = '';
            this.customerOrgCode = '';
            this.surcharge = 0;
            this.distributorScopeList = new List<Util.SelectItem>();
            this.dropshipCustomer = '';
        }
    }


    @AuraEnabled
    public static String submitData(String recordId, Boolean isDelegate) {
        List<Purchase_Order__c> poList = [SELECT Customer__r.Name,Customer_PO_Num__c,Submit_Date__c FROM Purchase_Order__c WHERE id =: recordId];
        Boolean isDuplicate = false;
        List<Purchase_Order__c> dPoList = [SELECT Id,Submit_Date__c FROM Purchase_Order__c WHERE Customer__r.Name =: poList[0].Customer__r.Name AND Customer_PO_Num__c =: poList[0].Customer_PO_Num__c AND Id !=:recordId];
        if(dPoList.size() > 0){
            for(Purchase_Order__c po : dPoList){
                system.debug(Date.today()-90);
                system.debug(po.Submit_Date__c);
                system.debug( (Date.today()-90) <= po.Submit_Date__c);
                if((Date.today()-90) <= po.Submit_Date__c){
                    isDuplicate = true;
                }
               
            }
        }
        
        if(isDuplicate){
            return 'Error';
        }else{
            update new Purchase_Order__c(Id = recordId, Status__c = 'Submitted', Submit_Date__c = Datetime.now(), Sync_Date__c = Datetime.now(), Is_Notified_Inside_Sales__c = true);
            return 'Success';
        }
        return null;
    }

    @AuraEnabled
    public static void sendEmail(String manageAccountOwnerId, String recordId){
        try{
        if(String.isNotBlank(manageAccountOwnerId)){
            User user = [SELECT Id, Email FROM User WHERE Id =: manageAccountOwnerId];
            //modify by austin, update message content
            Purchase_Order__c po = [select id, name, customer__r.name, dropship_customer__r.name from Purchase_Order__c where id=: recordId];
            String EmailTemplate = 'Email_Temeplate_for_2nd_tier_Account_Manager';
            Map<String, String> mapMailParam = new Map<String, String>();
            String toAddress = user.Email;
            List<String> strToAddress = new List<String>();
            strToAddress.Add(toAddress);
            strToAddress.Add('<EMAIL>');
            // user.Contact.Account.Owner.Email;
            List<EmailTemplate> et = [SELECT id, Subject, Body FROM EmailTemplate WHERE DeveloperName =: EmailTemplate];

            String url = Url.getOrgDomainUrl().toExternalForm();
            mapMailParam.put('{!Url}', url + '/lightning/n/Order_Apply_Detail_Page?0.recordId=' + recordId);
            mapMailParam.put('{!poname}', po.name);
            mapMailParam.put('{!1st}', po.customer__r.name);
            mapMailParam.put('{!2st}', po.dropship_customer__r.name);
            if(et.size() > 0){
                Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
                List<OrgWideEmailAddress> listAddresses = [
                    SELECT 
                        Id, DisplayName, Address
                    FROM 
                        OrgWideEmailAddress
                    WHERE 
                        DisplayName = :'SFDC Notification'
                ];
                if (listAddresses.size() > 0) {
                    mail.setOrgWideEmailAddressId(listAddresses[0].Id);
                }

                //Setting EmailTemeplate
                mail.setTemplateId(et[0].id);

                //Setting Recipient Email Addresses
                mail.setToAddresses(strToAddress);

                String strSubject = '';
                String strTextBody = '';
                
                if (mapMailParam != null) {
                    strSubject = et[0].subject;
                    strTextBody = et[0].body;
                    for (String parmKey : mapMailParam.keySet()) {
                        strSubject = strSubject.replace(parmKey, mapMailParam.get(parmKey));
                        strTextBody = strTextBody.replace(parmKey, mapMailParam.get(parmKey));
                    }
                   
                    //Setting the subject
                    mail.setSubject(strSubject);

                    //Setting the Email Content
                    mail.setPlainTextBody(strTextBody);
                }
                
                mail.setSaveAsActivity(false);
                mail.setUseSignature(false);
                mail.setCharset('UTF-8');

                //Send Email
                List<Messaging.SingleEmailMessage> allmsg = new List<Messaging.SingleEmailMessage>();
                allmsg.add(mail);
                Messaging.SendEmailResult[] results = Messaging.sendEmail(allmsg, false);
                Purchase_Order__c objPruchaseOrder = [SELECT Id,Is_Notified_Inside_Sales__c FROM Purchase_Order__c WHERE Id = :recordId LIMIT 1];
                objPruchaseOrder.Is_Notified_Inside_Sales__c = true;
                update objPruchaseOrder;
            }

        }
    }catch(Exception ex){
        Log__c d = new Log__c(Name = 'CCM_Dropship'+ex.getLineNumber(), Error_Message__c = ex.getMessage());
        insert d;
    }

    }

    @AuraEnabled
    public static String savePurchaseOrder(String poInfo, String poItemInfos, String customerId) {
        SaveResult resp = new SaveResult();
        if (String.isNotBlank(poInfo)) {
            try {
                Purchase_Order__c po = (Purchase_Order__c) JSON.deserialize(poInfo, Purchase_Order__c.class);
                System.debug(LoggingLevel.INFO, '*** poInfo: ' + poInfo);
                Map<String, Integer> stepMap = new Map<String, Integer>();
                Schema.DescribeFieldResult fieldResult = Purchase_Order__c.Step__c.getDescribe();
                List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();

                po.Customer__c = customerId;
                Account acc = [SELECT Id, ORG_Code__c FROM Account WHERE Id=:customerId];
                po.ORG_ID__c = acc.ORG_Code__c;
                upsert po;
                resp.isSuccess = true;
                resp.result = po;

                if (String.isNotBlank(poItemInfos)) {
                    List<Object> deserialized = (List<Object>) JSON.deserializeUntyped(poItemInfos);
                    List<String> finalJsonString = new List<String>();
                    List<Purchase_Order_Item__c> poItems = new List<Purchase_Order_Item__c>();
                    for (Object instance : deserialized) {
                        Map<String, Object> obj = (Map<String, Object>) instance;
                        Purchase_Order_Item__c item = new Purchase_Order_Item__c();
                        item.Product__c = String.valueOf(obj.get('Product__c'));
                        item.Quantity__c = Integer.valueOf(obj.get('Quantity__c'));
                        item.Purchase_Order__c = po.Id;
                        item.Brand__c = String.valueOf(obj.get('Brand__c'));
                        item.Ship_Date__c = Date.valueOf(String.valueOf(obj.get('Ship_Date__c')));
                        item.List_Price__c = Decimal.valueOf(String.valueOf(obj.get('List_Price__c')));
                        item.Unit_Price__c = Decimal.valueOf(String.valueOf(obj.get('Unit_Price__c')));
                        item.Discount_Amount__c = Decimal.valueOf(String.valueOf(obj.get('Discount_Amount__c'))).setScale(2);
                        item.ORG_Code__c = acc.ORG_Code__c;
                        if (item.Product__c != '' && item.Quantity__c > 0) {
                            poItems.add(item);
                        }
                    }
                    upsert poItems;
                }
            } catch (Exception ex) {
                resp.errorMsg = ex.getMessage();
            }
        }

        return JSON.serialize(resp);
    }

    @AuraEnabled
    public static String deleteChangedBrandPO(String poInfo) {
        SaveResult resp = new SaveResult();
        if (String.isNotBlank(poInfo)) {
            try {
                Purchase_Order__c po = (Purchase_Order__c) JSON.deserialize(poInfo, Purchase_Order__c.class);
                system.debug('poinfo======='+ poInfo);
                system.debug('po======='+ po);
                if (String.isNotBlank(po.Id)) {
                    List<Purchase_Order__c> poList = [SELECT Id, (SELECT Id FROM Purchase_Order_Items__r) FROM Purchase_Order__c WHERE Id = :po.Id];

                    if (poList != null && poList.size() > 0) {
                        if (poList[0].Purchase_Order_Items__r != null && poList[0].Purchase_Order_Items__r.size() > 0) {
                            delete poList[0].Purchase_Order_Items__r;
                        }
                        po.Order_Type__c ='CNA Dropship Order';
                        po.Additional_Contact_Email__c = '';
                        po.Additional_Contact_Name__c = '';
                        po.Additional_Contact_Phone__c = '';
                        po.Additional_Shipping_City__c = '';
                        po.Additional_Shipping_Country__c = '';
                        po.Additional_Shipping_Postal_Code__c = '';
                        po.Additional_Shipping_Province__c = '';
                        po.Additional_Shipping_Street__c = '';
                        po.Additional_Shipping_Street2__c = '';
                        po.Billing_Address__c = null;
                        po.BillTo__c = null;
                        po.BillTo_OracleID__c = '';
                        po.Email__c = '';
                        po.Carrier_Code__c = null;
                        po.Customer_Freight_Account__c = '';
                        po.Customer_PO_Num__c = '';
                        po.Delivery_Supplier__c = null;
                        po.Discount__c = 0.00;
                        po.Expected_Delivery_Date__c = null;
                        po.Freight_Fee_To_Be_Waived__c = 0.00;
                        po.Extra_Freight_Fee_To_Be_Waived__c = 0.00;
                        po.Actual_Total_Product_Amount__c = 0.00;
                        po.Freight_Fee__c = 0.00;
                        po.Freight_Fee_Waived__c = 0.00;
                        po.Freight_Target_Fee__c = 0.00;
                        po.Handling_Fee__c = 0.00;
                        po.Is_Alternative_Address__c = false;
                        po.Discount_Amount__c = 0.00;
                        po.Notes__c = '';
                        // po.Order_Type__c = null;
                        po.Other_Fees__c = 0.00;
                        po.Price_Book_List__c = '';
                        po.Product_Price__c = 0.00;
                        po.Shipping_Address__c = null;
                        po.Shipping_By__c = null;
                        po.Shipping_Method__c = null;
                        po.ShipTo__c = null;
                        po.ShipTo_OracleID__c = '';
                        po.Status__c = 'Draft';
                        po.Total_Price__c = 0.00;
                        po.Total_Quantity__c = 0.00;
                        po.Payment_Term__c = '';
                        po.Payment_Term_Promotion__c = null;
                    }
                } else {
                    po.Order_Type__c ='CNA Dropship Order';
                    po.Freight_Fee__c = 0.00;
                    po.Freight_Fee_To_Be_Waived__c = 0.00;
                    po.Freight_Fee_Waived__c = 0.00;
                    po.Freight_Target_Fee__c = 0.00;
                    po.Actual_Total_Product_Amount__c = 0.00;
                    po.Discount_Amount__c = 0.00;
                    po.Handling_Fee__c = 0.00;
                    po.Total_Price__c = 0.00;
                    po.Total_Quantity__c = 0.00;
                    po.Product_Price__c = 0.00;
                    po.Payment_Term__c = '';
                    po.Payment_Term_Promotion__c = null;
                }
                system.debug('poDetail--------'+ po);
                upsert po;
                resp.isSuccess = true;
                resp.result = po;
            } catch (Exception ex) {
                resp.errorMsg = ex.getMessage();
            }
        }

        return JSON.serialize(resp);
    }

    @AuraEnabled
    public static String deleteQuotation(String recordId) {
        if (String.isNotBlank(recordId)) {
            Purchase_Order_Item__c poItem = [SELECT Id FROM Purchase_Order_Item__c WHERE Id = :recordId];
            delete poItem;

            return 'Success';
        }
        return 'Fail';
    }

    @AuraEnabled
    public static String deleteQuotation(List<String> recordIds){
        if (recordIds!=null && recordIds.size()>0){
            List<Purchase_Order_Item__c> poItems = [SELECT Id FROM Purchase_Order_Item__c WHERE Id IN: recordIds];
            delete poItems;

            return 'Success';
        }
        return 'Fail';
    }

    @AuraEnabled
    public static String getPriceBookEntryCondition(
        String brandName,
        String customerId,
        Boolean isDropShip
    ) {
        return Util.getPriceBookEntryCondition(
            brandName,
            customerId,
            isDropShip
        );
    }

    public static Decimal getFreihtChargeAmount(Decimal productAmount, String orgCode, String brand) {
        Decimal freightFee = 0.00;
        if(orgCode != 'CCA'){
            List<Freight_Charges__mdt> freightChargeList = [SELECT Order_Value_From__c, Order_Value_To__c, Ground_Shipping__c FROM Freight_Charges__mdt];
            if (freightChargeList != null && freightChargeList.size() > 0) {
                for (Freight_Charges__mdt fc : freightChargeList) {
                    if (productAmount >= fc.Order_Value_From__c && productAmount <= fc.Order_Value_To__c) {
                        freightFee = fc.Ground_Shipping__c;
                        break;
                    }
                }
            }
        }else if(orgCode == 'CCA'){
            List<CA_Freight_Charges__mdt> freightChargeList = [SELECT Order_Value_From__c, Order_Value_To__c, Ground_Shipping__c FROM CA_Freight_Charges__mdt];
            if(brand.contains('EGO') || productAmount < 750){
                if (freightChargeList != null && freightChargeList.size() > 0) {
                    for (CA_Freight_Charges__mdt fc : freightChargeList) {
                        if (productAmount >= fc.Order_Value_From__c && productAmount <= fc.Order_Value_To__c) {
                            freightFee = fc.Ground_Shipping__c;
                            break;
                        }
                    }
                }
            }
        }        

        return freightFee;
    }    

    @AuraEnabled
    public static String getFreihtFeeAmount(String recordId, String quotation, Boolean isAlternativeAddress, Decimal freightTermRuleFee, Decimal productAmount) {
        System.debug(LoggingLevel.INFO, '*** freightTermRuleFee: ' + freightTermRuleFee);
        System.debug(Limits.getQueries());
        System.debug(LoggingLevel.INFO, '*** productAmount: ' + productAmount);
        ShippingAddressInfo shippingInfo = new ShippingAddressInfo();
        Decimal freightFeeAmt = 0.00;
        List<Decimal> prodWeightList = new List<Decimal>();
        Decimal prodWeightTotal = 0.00;
        Decimal overSizeAmount = 0.00;

        String shipperCountryCode = 'US';
        String shipperPostalCode = '38115';
        String recipientCountryCode = 'US';
        String recipientPostalCode = '38017';
        //运费系数
        Decimal freightFactor = Util.getFreightFeeFactor();
        if (String.isNotBlank(recordId)) {
            if (String.isNotBlank(quotation)) {
                Purchase_Order__c poObj = (Purchase_Order__c) JSON.deserialize(quotation, Purchase_Order__c.class);
                System.debug(LoggingLevel.INFO, '*** JSON.serialize(poObj): ' + JSON.serialize(poObj));
                if (isAlternativeAddress == false) {
                    String addressId = poObj.Shipping_Address__c;
                    List<Account_Address__c> addressList = [
                        SELECT
                            Id,
                            Country__c,
                            Postal_Code__c,
                            Sales_Agency__c,
                            Sales_Agency__r.Alias_Formula__c,
                            EGO_Agency__c,
                            EGO_Agency__r.Alias_Formula__c,
                            FLEX_Agency__c,
                            FLEX_Agency__r.Alias_Formula__c,
                            SKIL_SKILSAW_Agency__c,
                            SKIL_SKILSAW_Agency__r.Alias_Formula__c
                        FROM Account_Address__c
                        WHERE Id = :addressId
                    ];

                    //Shangmin changed at 2020-12-15 for updating sales agency to 3 different fields.
                    //Start
                    System.debug(addressList);
                    Purchase_Order__c po = [SELECT Id, BillTo__r.Program__r.Brands__c FROM Purchase_Order__c WHERE Id = :recordId][0];
                    System.debug(po);
                    String brand = '';
                    if (po.BillTo__r.Program__r.Brands__c != null) {
                        brand = po.BillTo__r.Program__r.Brands__c;
                    }
                    //End
                    if (addressList != null && addressList.size() > 0) {
                        recipientCountryCode = addressList[0].Country__c;
                        recipientPostalCode = addressList[0].Postal_Code__c;

                        //获取Shipping Address上的Sales Agency信息
                        Account_Address__c accAddress = addressList[0];

                        //Shangmin changed at 2020-12-15 for updating sales agency to 3 different fields.
                        //Start
                        if (brand == 'EGO') {
                            if (accAddress.EGO_Agency__c != null) {
                                shippingInfo.salesAgencyAlias = accAddress.EGO_Agency__r.Alias_Formula__c;
                                shippingInfo.salesAgencyId = accAddress.EGO_Agency__c;
                            } else {
                                shippingInfo.salesAgencyAlias = '';
                            }
                        } else if (brand == 'Flex') {
                            if (accAddress.FLEX_Agency__c != null) {
                                shippingInfo.salesAgencyAlias = accAddress.FLEX_Agency__r.Alias_Formula__c;
                                shippingInfo.salesAgencyId = accAddress.FLEX_Agency__c;
                            } else {
                                shippingInfo.salesAgencyAlias = '';
                            }
                        } else if (brand == 'Skil' || brand == 'SkilSaw') {
                            if (accAddress.SKIL_SKILSAW_Agency__c != null) {
                                shippingInfo.salesAgencyAlias = accAddress.SKIL_SKILSAW_Agency__r.Alias_Formula__c;
                                shippingInfo.salesAgencyId = accAddress.SKIL_SKILSAW_Agency__c;
                            } else {
                                shippingInfo.salesAgencyAlias = '';
                            }
                        }
                        System.debug(brand);
                        System.debug('<==================== Shangmin Test New Sales Agency Logic 0 ===============>');
                        System.debug(shippingInfo.salesAgencyId);
                        System.debug(shippingInfo.salesAgencyAlias);
                        System.debug('<==================== Shangmin Test New Sales Agency Logic 1 ===============>');

                        //End

                        // shippingInfo.salesAgencyAlias = addressList[0]
                        //         .Sales_Agency__c != null
                        //     ? accAddress.Sales_Agency__r.Alias_Formula__c
                        //     : '';
                        // shippingInfo.salesAgencyId = addressList[0]
                        //     .Sales_Agency__c;
                    }
                } else {
                    recipientCountryCode = poObj.Additional_Shipping_Country__c;
                    recipientPostalCode = poObj.Additional_Shipping_Postal_Code__c;
                }
            }
            System.debug(LoggingLevel.INFO, '*** recipientCountryCode: ' + recipientCountryCode);
            System.debug(LoggingLevel.INFO, '*** recipientPostalCode: ' + recipientPostalCode);

            //如果订单产品金额 >= Freight Term Fee
            if (productAmount >= freightTermRuleFee) {
                List<Purchase_Order__c> poList = [
                    SELECT Id, Brand_Scope__c, ORG_ID__c, (SELECT Id, Product__c, Product__r.Weight__c, Product__r.OverSize__c, Quantity__c FROM Purchase_Order_Items__r)
                    FROM Purchase_Order__c
                    WHERE Id = :recordId
                ];
                if (poList != null && poList.size() > 0) {
                    if (poList != null && poList.size() > 0) {
                        Purchase_Order__c po = poList[0];
                        if(po.ORG_ID__c == 'CCA'){
                            shipperPostalCode = CCM_SystemConstants.CA_WAREHOUSE_ZIPCODE;
                        }else{
                            if (po.Brand_Scope__c != 'EGO') {
                                shipperPostalCode = CCM_SystemConstants.SKIL_SKILSAW_FLEX_WAREHOUSE_ZIPCODE;
                            } else {
                                shipperPostalCode = CCM_SystemConstants.EGO_WAREHOUSE_ZIPCODE;
                            }
                        }
                        
                        System.debug(LoggingLevel.INFO, '*** shipperPostalCode: ' + shipperPostalCode);
                        if (po.Purchase_Order_Items__r != null && po.Purchase_Order_Items__r.size() > 0) {
                            for (Purchase_Order_Item__c item : Po.Purchase_Order_Items__r) {
                                Decimal weight = item.Product__r.Weight__c == null ? 0.00 : item.Product__r.Weight__c;
                                Decimal subWeight = item.Quantity__c * weight;
                                prodWeightTotal = prodWeightTotal + subWeight;
                                prodWeightList.add(subWeight);
                                if (item.Product__r.OverSize__c == true) {
                                    overSizeAmount = overSizeAmount + (item.Quantity__c * CCM_SystemConstants.OVER_SIZE_PRODUCT_FREIGHT_FEE);
                                }
                            }

                            if (prodWeightTotal >= 150) {
                                prodWeightList = new List<Decimal>{ 150 };
                            }
                            Decimal freightFee = 0.00;
                            if (Test.isRunningTest()) {
                                freightFee = 10.89;
                            } else {
                                freightFee = Util.getFreightFeeAmt(shipperCountryCode, shipperPostalCode, recipientCountryCode, recipientPostalCode, prodWeightList);
                            }

                            //订单总重量高于等于150磅时，显示的运费等于 FedEx 接口返回的 【运费金额 * (订单总重量/150磅） *  运费系数】
                            //订单总重量低于150磅时，显示的运费等于 FedEx 接口返回的 【运费金额 * 运费系数】
                            if (prodWeightTotal >= 150) {
                                freightFee = freightFee * (prodWeightTotal / 150) * (freightFactor / 100);
                            } else {
                                freightFee = freightFee * (freightFactor / 100);
                            }
                            Decimal actualFreightFeeAmt = freightFee + overSizeAmount;
                            freightFeeAmt = actualFreightFeeAmt.setScale(2);
                        }
                    }
                }
            } else {
                Purchase_Order__c poObj = [SELECT ORG_ID__c, Brand_Scope__c FROM Purchase_Order__c WHERE Id=:recordId];
                if (productAmount > 2499 && poObj.ORG_ID__c != 'CCA') {
                    freightFeeAmt = productAmount * Decimal.valueOf(Label.CCM_Freight_Fee_By_Product_Amt_Over_2000) / 100;
                } else {                    
                    freightFeeAmt = getFreihtChargeAmount(productAmount, poObj.ORG_ID__c, poObj.Brand_Scope__c);
                }
            }

            //如果订单产品金额 < Freight Term Fee
        }
        shippingInfo.freightFeeAmt = freightFeeAmt;
        return JSON.serialize(shippingInfo);
    }

    /**
     * add by austin
     * sales hierarchy，sec cusstomer -> top customer and top customer name
     */
    public static Map<String, String> getTopCustomerInfo(List<String> customerId){
       //top customer map
       Map<String, String> topCustomerMap = new Map<String, String>();
       //判断buying group
       List<Sales_Hierarchy__c> buyingGroup = [SELECT X1st_tier_dealer__c, X1st_tier_dealer__r.Name, X2st_tier_dealer__c FROM Sales_Hierarchy__c 
                                                WHERE X1st_tier_dealer__c =: customerId
                                                AND Approval_Status__c =: CCM_Constants.SALES_HIERARCHY_APPROVAL_STATUS_APPROVED
                                                AND Active__c = true];
       if (buyingGroup.size() > 0) {
        for (Sales_Hierarchy__c bg : buyingGroup) {
            topCustomerMap.put(bg.X1st_tier_dealer__c, bg.X1st_tier_dealer__r.Name);
        }
       }else {
        //store
        Set<String> secId = new Set<String>();
        List<Sales_Hierarchy__c> store = [SELECT X1st_tier_dealer__c, X1st_tier_dealer__r.Name, X2st_tier_dealer__c FROM Sales_Hierarchy__c 
                                            WHERE X2st_tier_dealer__c =: customerId
                                            AND Approval_Status__c =: CCM_Constants.SALES_HIERARCHY_APPROVAL_STATUS_APPROVED
                                            AND Active__c = true];
        for (Sales_Hierarchy__c st : store) {
            secId.add(st.X1st_tier_dealer__c);
        }
        //判断store 的上级buying group
        List<Sales_Hierarchy__c> stBuyingGroup = [SELECT X1st_tier_dealer__c, X1st_tier_dealer__r.Name, X2st_tier_dealer__c FROM Sales_Hierarchy__c 
                                                    WHERE X2st_tier_dealer__c =: secId
                                                    AND Approval_Status__c =: CCM_Constants.SALES_HIERARCHY_APPROVAL_STATUS_APPROVED
                                                    AND Active__c = true];
        if (stBuyingGroup.size() > 0) {
            for (Sales_Hierarchy__c stbg : stBuyingGroup) {
                topCustomerMap.put(stbg.X1st_tier_dealer__c, stbg.X1st_tier_dealer__r.Name);
            }
        }else {
            for (Sales_Hierarchy__c st : store) {
                topCustomerMap.put(st.X1st_tier_dealer__c, st.X1st_tier_dealer__r.Name);
            }
        }
    }
    return topCustomerMap;
    }

    public class SaveResult {
        public Boolean isSuccess;
        public String errorMsg;
        public Purchase_Order__c result;

        public SaveResult() {
            isSuccess = false;
            errorMsg = '';
            result = new Purchase_Order__c();
        }
    }

    public class PaymentFreightTerm {
        @AuraEnabled
        public String paymentTerm { get; set; }
        @AuraEnabled
        public String paymentTermLabel { get; set; }
        @AuraEnabled
        public String freightTerm { get; set; }
        @AuraEnabled
        public String freightTermLabel { get; set; }
        @AuraEnabled
        public Decimal freightTermRuleFee { get; set; }

        public PaymentFreightTerm() {
            this.paymentTerm = '';
            this.paymentTermLabel = '';
            this.freightTerm = '';
            this.freightTermLabel = '';
            this.freightTermRuleFee = 0;
        }
    }

    public class ShippingAddressInfo {
        @AuraEnabled
        public Decimal freightFeeAmt { get; set; }
        @AuraEnabled
        public String salesAgencyId { get; set; }
        @AuraEnabled
        public String salesAgencyAlias { get; set; }

        public ShippingAddressInfo() {
            this.freightFeeAmt = 0.00;
            this.salesAgencyId = '';
            this.salesAgencyAlias = '';
        }
    }

    /**
     * @description Get promotion details by customerId and promotion code.
     */
    @AuraEnabled
    public static String getPromotion(String promotionCode, String customerId, Boolean isDropShip){
        return CCM_Quotation_ProductSelectCtl.getPromotion(promotionCode, customerId, isDropShip, false);
    }

    /**
     * @description Check applied promotion codes by customer id.
     */
    @AuraEnabled
    public static String checkPromotions(List<String> promotionCodes, String customerId){
        return CCM_Quotation_ProductSelectCtl.checkPromotions(promotionCodes, customerId);
    }

    /**
     * @description Caculate Canada taxes for purchase order.
     */
    public static void caculateTotalTax(Purchase_Order__c po){
        //CA Province Tax Rate
        Map<String, CA_Tax_Rate_By_Province__mdt> rateMap = Util.getCanadaTaxRateMap();
        
        CA_Tax_Rate_By_Province__mdt taxRate;
        if(po.Is_Alternative_Address__c){
            taxRate = rateMap.get(po.Additional_Shipping_Province__c);
        }else{
            //Shipping Address
            Address_With_Program__c shipAddress = [
                SELECT 
                Account_Address__r.State__c
                FROM Address_With_Program__c
                WHERE Id=:po.ShipTo__c
            ];
            taxRate = rateMap.get(shipAddress.Account_Address__r.State__c);
        }
        if(taxRate != null){
            Decimal baseAmount = 0;
            if(po.Product_Price__c != null){
                baseAmount+=po.Product_Price__c;
            }
            if(po.Freight_Fee__c !=null){
                baseAmount+=po.Freight_Fee__c;
            }
            if(po.Handling_Fee__c !=null){
                baseAmount+=po.Handling_Fee__c;
            }
            if(po.Freight_Fee_Waived__c !=null){
                baseAmount-=Math.abs(po.Freight_Fee_Waived__c);
            }
            if(po.Extra_Freight_Fee_To_Be_Waived__c !=null){
                baseAmount-=Math.abs(po.Extra_Freight_Fee_To_Be_Waived__c);
            }
            if(po.Discount_Amount__c !=null){
                baseAmount-=Math.abs(po.Discount_Amount__c);
            }
            if(po.Surcharge_Amount__c !=null){
                baseAmount+=po.Surcharge_Amount__c;
            }


            po.GST__c = (baseAmount*taxRate.GST__c/100).setScale(2,System.RoundingMode.HALF_UP);
            po.HST__c = (baseAmount*taxRate.HST__c/100).setScale(2,System.RoundingMode.HALF_UP);
            po.PST__c = (baseAmount*taxRate.PST__c/100).setScale(2,System.RoundingMode.HALF_UP);
            po.QST__c = (baseAmount*taxRate.QST__c/100).setScale(2,System.RoundingMode.HALF_UP);
            po.Total_Tax__c = (baseAmount*(taxRate.Total_Rate__c-taxRate.PST__c)/100).setScale(2,System.RoundingMode.HALF_UP);
        }else{
            po.GST__C = null;
            po.HST__c = null;
            po.PST__c = null;
            po.QST__c = null;
            po.Total_Tax__c = null;
        }
    }
}