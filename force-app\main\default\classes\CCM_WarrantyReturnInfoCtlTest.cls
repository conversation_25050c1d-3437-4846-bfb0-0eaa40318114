/**
 * @Author: <PERSON> Guo
 * @description: test CCM_WarrantyReturnInfoCtl
 * @Create Date: 2022-11-21
*/
@IsTest
public class CCM_WarrantyReturnInfoCtlTest {

    // 初始化好需要的数据
    @TestSetup
    static void testSetUpData() {
        Test.startTest();
        Account_SyncToShopify obj = new Account_SyncToShopify();
        Account_SyncToShopify.functionName.add('Account');
        CCM_SharingUtil.isSharingOnly = true;
        CCM_ContactUpdateHandler.isRun = false;
        // CCM_ActionStandardPrice.isRun = false;
        CCM_UpdateProductSellableHandler.boolToRun = false;
        CCM_StopUpdatingPimSellableHandler.boolToRun = false;
        CCM_UpaftCtrl.inFutureContext = true;
        CCM_BaabASGHandler.isRun = false;
        CCM_BaabAccessHandlerAfter.isRun = false;
        CCM_ValidationRelateListHandler.isRun = false;
        CCM_AddressWithProgramUpdateHandler.isRun = false;
        CCM_AddressWithProgramInsertHandler.isRun = false;
        CCM_VerifyAddressAgencyHandler.isRun= false;
        CCM_AddressMergeAgencies.isRun= false;
        CCM_AddressASGHandler.isRun = false;
        CCM_AddressAccessHandler.isRun = false;
        CCM_GetSubInventoryHandler.isRun = false;
        CCM_ShippingAddressSendApproval.isRun = false;
        CCM_AddressUpdateHandler.isRun = false;
        CCM_ValidateEDIContactHandler.isRun = false;
        CCM_AddressAccessPlusHandler.isRun = false;
        CCM_AddressBatchHandler.isRun = false;
        CCM_DataSharingForAddressOwner.isRun = false;
        CCM_AddressStateValidation.isRun  = false;
        CCM_InvoiceASGHandler.isRun = false;
        CCM_ShareInvoicessToAddressOwner.isRun = false;
        CCM_ReverseOrderCompleteHandler.isRun = false;
        CCM_PoASGHandler.isRun = false;
        CCM_SharePurchaseOrdersToAddressOwner.isRun = false;
        CCM_FillExpectedDeliveryDateHandler.boolToRun = false;
        OrderTriggerHandle.isRun = false;
        CCM_OrderASGHandler.isRun = false;
        CCM_ShareOrdersToAddressOwner.isRun = false;
        CCM_TaxUpdateOrderHandler.boolToRun = false;
        CCM_DealerLocationUpdateOfOrderHandler.isRun = false;
        CCM_OrderItemBeforeHandler.isBatch = false;
        CCM_OrderItemASGHandler.isRun = false;
        CCM_ShareOrderItemToAddressOwner.isRun = false;
        CCM_PromotionActualQuantity.isRun = false;
        CCM_CountCaseAttachmentHandler.boolToRun =false;
        
        // customer info
        String recordId = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' AND DeveloperName = 'Channel' LIMIT 1].Id;
        Account account = new Account();
        account.Name = 'TestCustomer';
        account.Distributor_or_Dealer__c = '2nd Tier Dealer';
        account.RecordTypeId = recordId;
        account.AccountNumber = '12345';
        account.TaxID__c = 'taxtest';
        account.ORG_Code__c = 'CNA';
        insert account;
        
        Contact contact = new Contact();
        contact.AccountId = account.Id;
        contact.LastName = 'xxx';
        contact.FirstName = 'YYY';
        contact.OwnerId = UserInfo.getUserId();
        insert contact;

        // get user profile info
        Profile userProfile = [SELECT Id, Name FROM Profile WHERE Id = :UserInfo.getProfileId()];
        
        Account_Address__c address = new Account_Address__c();
        address.Customer__c = account.Id;
        address.X2nd_Tier_Dealer__c = account.Id;
        address.Active__c = true;
        address.Approval_Status__c = 'Approved';
        address.Contact__c = contact.Id;
        insert address;

        // pricebook info
        Pricebook2 pb = new Pricebook2();
        pb.Name = 'Standard Price Book';
        pb.IsActive = true;
        pb.Contract_Price_Book_OracleID__c = '11';
        pb.Price_Book_OracleID__c = '11';
        pb.Org_Code__c = 'CNA';
        insert pb;
        
        Product2 pro = new Product2();
        pro.Name = 'TestProduct111';
        pro.Brand_Name__c = 'EGO';
        pro.ProductCode = '1234567';
        pro.IsActive = true;
        pro.Source__c = 'PIM';
        pro.Description = pro.SF_Description__c;
        insert pro;

        Product2 pro2 = new Product2();
        pro2.Name = 'TestProduct222';
        pro2.Brand_Name__c = 'EGO';
        pro2.ProductCode = '1234567';
        pro2.IsActive = true;
        pro2.Source__c = 'EBS';
        insert pro2;
        pro2.Description = pro2.SF_Description__c;
        update pro2;

        // pricebookEntry info
        PricebookEntry pbe = new PricebookEntry();
        pbe.IsActive = true;
        pbe.Product2Id = pro.Id;
        pbe.UnitPrice = 1000;
        pbe.Pricebook2Id = pb.Id;
        pbe.UseStandardPrice = false;
        insert pbe;

        // authorized brand info
        Sales_Program__c salesProgram = new Sales_Program__c();
        salesProgram.Customer__c = account.Id;
        salesProgram.Approval_Status__c = 'Approved';
        salesProgram.Price_Book__c = pb.Id;
        salesProgram.Authorized_Brand_Name_To_Oracle__c = 'EGO_2020_Test';
        salesProgram.RecordTypeId = CCM_Contants.SALES_STANDARD_PROGRAM_RECORDTYPEID;
        salesProgram.Brands__c = 'EGO';
        salesProgram.ORG_Code__c = 'CNA';
        salesProgram.Payment_Term__c = 'NA001';
        salesProgram.ORG_Code__c = 'CNA';
        salesProgram.Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.';
        insert salesProgram;

        // BAAB info
        Address_With_Program__c addPro = new Address_With_Program__c();
        addPro.Account_Address__c = address.Id;
        addPro.Program__c = salesProgram.Id;
        insert addPro;

        Invoice__c invoice = new Invoice__c();
        invoice.Customer__c = account.Id;
        invoice.Invoice_Number__c = '112233';
        invoice.Invoice_Type__c = 'CNA_DebitMemo';
        invoice.Total_Remaining__c = 10;

        invoice.Total_Due__c = 100;
        insert invoice;

        Purchase_Order__c objPO = new Purchase_Order__c(Customer__c = account.Id);
        insert objPO;

        Order o = TestDataFactory.createOrder();
        o.AccountId = account.Id;
        o.Order_Number__c = 'yyy';
        o.Order_Status__c = 'Partial Shipment';
        o.BillTo__c = addPro.Id;
        o.ShipTo__c = addPro.Id;
        o.Purchase_Order__c = objPO.Id;
        o.Price_Book__c = pb.Id;
        o.PO_Number__c = 'CCC-11111';
        o.Org_Code__c = 'CNA';
        o.Order_Type__c = 'CNA Sales Order - USD';
        insert o;

        Order_Item__c orderItem = new Order_Item__c();
        orderItem.Order__c = o.Id;
        orderItem.Product__c = pro.Id;
        orderItem.Line_Status__c = 'INVOICED';
        insert orderItem;

        Warranty_Return_Claim__c objWarrantyReturnClaim = new Warranty_Return_Claim__c();
        objWarrantyReturnClaim.Customer__c = account.Id;
        objWarrantyReturnClaim.Name = 'RA00001111';
        objWarrantyReturnClaim.Chervon_or_Warehouse_Purchase__c = 'Chervon';
        objWarrantyReturnClaim.Approved_Date__c = Date.today();
        objWarrantyReturnClaim.Contact_Name__c = 'test';
        objWarrantyReturnClaim.Contact_Phone_Number__c = '********';
        objWarrantyReturnClaim.Contact_Email_Address__c = '<EMAIL>';
        objWarrantyReturnClaim.Customer_Reference_Number__c = '************';
        objWarrantyReturnClaim.Notes__c = 'test notes';
        objWarrantyReturnClaim.Billing_Address__c = address.Id;
        objWarrantyReturnClaim.Shipping_Address__c = address.Id;
        objWarrantyReturnClaim.Warehouse_Location__c = address.Id;
        objWarrantyReturnClaim.Store_Location__c = address.Id;
        objWarrantyReturnClaim.Store_Address__c = address.Id;
        objWarrantyReturnClaim.Is_Alternative_Address__c = false;
        objWarrantyReturnClaim.Is_Alternative_Store_Address__c = false;
        objWarrantyReturnClaim.Payment_Method__c = 'Credit Memo';
        objWarrantyReturnClaim.Debit_Memo_Number__c = invoice.Id;
        objWarrantyReturnClaim.IsFinanceApproved__c = false;
        insert objWarrantyReturnClaim;

        Warranty_Return_Claim_Item__c objWarrantyItem = new Warranty_Return_Claim_Item__c();
        objWarrantyItem.Warranty_Return_Claim__c = objWarrantyReturnClaim.Id;
        objWarrantyItem.Model__c = pro.Id;
        objWarrantyItem.Quantity__c = 1;
        objWarrantyItem.DIF_RTV__c = 'DIF';
        objWarrantyItem.Serial_Number__c = '12121212121';
        objWarrantyItem.End_Consumer_Purchase_Date__c = Date.newInstance(2022, 11, 22);
        objWarrantyItem.End_Consumer_Return_Date__c = Date.newInstance(2022, 11, 25);
        objWarrantyItem.Index__c = '0';
        insert objWarrantyItem;

        
        Warranty_Return_Claim_Item_Attachment__c attachment = new Warranty_Return_Claim_Item_Attachment__c();
        attachment.Attachment_Type__c = 'End Customer Purchase Date';
        attachment.Warranty_Return_Claim__c = objWarrantyReturnClaim.Id;
        attachment.Warranty_Return_Claim_Item__c = objWarrantyItem.Id;
        insert attachment;

        Warranty_Return_Claim_Item_Attachment__c attachment1 = new Warranty_Return_Claim_Item_Attachment__c();
        attachment1.Attachment_Type__c = 'End Customer Return Date';
        attachment1.Warranty_Return_Claim__c = objWarrantyReturnClaim.Id;
        attachment1.Warranty_Return_Claim_Item__c = objWarrantyItem.Id;
        insert attachment1;

        Warranty_Return_Claim_Item_Attachment__c attachment2 = new Warranty_Return_Claim_Item_Attachment__c();
        attachment2.Attachment_Type__c = 'Return Reason';
        attachment2.Warranty_Return_Claim__c = objWarrantyReturnClaim.Id;
        attachment2.Warranty_Return_Claim_Item__c = objWarrantyItem.Id;
        insert attachment2;

        ContentVersion objContentVersion = new ContentVersion(Title = 'test', VersionData = Blob.valueOf('test'), PathOnClient = 'test', Origin = 'H');
        insert objContentVersion;
        objContentVersion = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :objContentVersion.Id];

        ContentDocumentLink cdl = New ContentDocumentLink();
        cdl.LinkedEntityId = attachment.id;
        cdl.ContentDocumentId = objContentVersion.ContentDocumentId;
        cdl.ShareType = 'V';
        insert cdl;

        ContentDocumentLink doc1 = New ContentDocumentLink();
        doc1.LinkedEntityId = attachment1.id;
        doc1.ContentDocumentId = objContentVersion.ContentDocumentId;
        doc1.ShareType = 'V';
        insert doc1;

        ContentDocumentLink doc2 = New ContentDocumentLink();
        doc2.LinkedEntityId = attachment2.id;
        doc2.ContentDocumentId = objContentVersion.ContentDocumentId;
        doc2.ShareType = 'V';
        insert doc2;

        Warranty_Return_Policy__c objPolicy = new Warranty_Return_Policy__c();
        objPolicy.Name = 'test policy';
        objPolicy.Active_Date__c = Date.today() - 1;
        objPolicy.Expiry_Date__c = Date.today();
        objPolicy.DIF_RTV_Policy__c = 'DIF for All';
        objPolicy.Approval_Status__c = 'Approved';
        insert objPolicy;

        Test.stopTest();
    }

    @IsTest
    static void testWarrantyReturnSalesApprovalHandler() {
        Test.startTest();
        Warranty_Return_Claim__c objWarranty = [SELECT Id FROM Warranty_Return_Claim__c LIMIT 1];
        objWarranty.IsFinanceApproved__c = true;
        update objWarranty;
        Test.stopTest();
    }

    @IsTest
    static void testGetDIFRTV() {
        Test.startTest();
        Warranty_Return_Policy__c objPolicy = [SELECT Id FROM Warranty_Return_Policy__c LIMIT 1];
        Product2 objProduct = [SELECT Id, ProductCode FROM Product2 LIMIT 1];
        CCM_WarrantyReturnInfoCtl.getDIFRTV(objProduct.Id, objPolicy.Id);
        Test.stopTest();
    }

    @IsTest
    static void testGetCustomerPONumber() {
        Test.startTest();
        Account objCustomer = [SELECT Id FROM Account LIMIT 1];
        Product2 objProduct = [SELECT Id, ProductCode FROM Product2 LIMIT 1];
        Account_Address__c add = [SELECT Id FROM Account_Address__c LIMIT 1];
        CCM_WarrantyReturnInfoCtl.getCustomerPONumber(objCustomer.Id, objProduct.Id, 'Chervon', add.Id);
        Test.stopTest();
    }

    @IsTest
    static void testSubmitForApproval() {
        Test.startTest();
        Warranty_Return_Claim__c objWarranty = [SELECT Id FROM Warranty_Return_Claim__c LIMIT 1];
        CCM_WarrantyReturnInfoCtl.submitForApproval(objWarranty.Id);
        Test.stopTest();
    }

    // test getProfile()
    @IsTest
    static void testGetProfile() {
        Test.startTest();
        CCM_WarrantyReturnInfoCtl.getProfile();
        Test.stopTest();
    }

    @IsTest
    static void testGetProfile1() {
        Test.startTest();
        Profile profile_1 = [SELECT Id FROM Profile WHERE Name = 'Partner Community Sales' LIMIT 1];
        Account acc = [SELECT Id FROM Account LIMIT 1];
        User admin = [SELECT Id, Username, UserRoleId FROM User WHERE Profile.Name = 'System Administrator'  AND UserRoleId != null AND IsActive = true LIMIT 1];
        User user_1;
        System.runAs(admin) {
            Contact contact_1 = new Contact();
            contact_1.AccountId = acc.Id;
            contact_1.LastName = '234242';
            insert contact_1;

            user_1 = new User(
                Email = '<EMAIL>',
                ProfileId = profile_1.Id,
                UserName = '<EMAIL>',
                Alias = 'Test',
                TimeZoneSidKey = 'America/New_York',
                EmailEncodingKey = 'ISO-8859-1',
                LocaleSidKey = 'en_US',
                LanguageLocaleKey = 'en_US',
                ContactId = contact_1.Id,
                //PortalRole = 'Manager',
                FirstName = 'Firstname',
                LastName = 'Lastname'
            );
            insert user_1;

            System.runAs(user_1){
                CCM_WarrantyReturnInfoCtl.ProfileWrapper response = CCM_WarrantyReturnInfoCtl.getProfile();
            }
        }
        Test.stopTest();
    }


    // test getCustomer()
    @IsTest
    static void testGetCustomer() {
        Test.startTest();
        String keyWord = 'Te';
        CCM_WarrantyReturnInfoCtl.getCustomer(keyWord);
        Test.stopTest();
    }

    @IsTest
    static void testGetCustomer1() {
        Test.startTest();
        Profile profile_1 = [SELECT Id FROM Profile WHERE Name = 'Sales Agency' LIMIT 1];
        User admin = [SELECT Id, Username, UserRoleId FROM User WHERE Profile.Name = 'System Administrator'  AND UserRoleId != null AND IsActive = true LIMIT 1];
        User user_1;
        System.runAs(admin) {
            user_1 = new User(
                Email = '<EMAIL>',
                ProfileId = profile_1.Id,
                UserName = '<EMAIL>',
                Alias = 'Test',
                TimeZoneSidKey = 'America/New_York',
                EmailEncodingKey = 'ISO-8859-1',
                LocaleSidKey = 'en_US',
                LanguageLocaleKey = 'en_US',
                FirstName = 'Firstname',
                LastName = 'Lastname'
            );
            insert user_1;

            System.runAs(user_1){
                String keyWord = 'Te';
                CCM_WarrantyReturnInfoCtl.getCustomer(keyWord);
            }
        }

        Test.stopTest();
    }

    // test getProducts()
    @IsTest
    static void testGetProducts() {
        //getProducts(String customerId, String keyWord)
        Test.startTest();
        Account account = [SELECT Id FROM Account LIMIT 1];
        Product2 product1 = [SELECT Id,SF_Description__c FROM Product2 WHERE Source__c = 'PIM' LIMIT 1];
        CCM_WarrantyReturnInfoCtl.getProducts(account.Id, product1.SF_Description__c);

        Product2 product2 = [SELECT Id,SF_Description__c FROM Product2 WHERE Source__c = 'EBS' LIMIT 1];
        CCM_WarrantyReturnInfoCtl.getProducts(account.Id, product2.SF_Description__c);

        Test.stopTest();

    }

    // test getAvailabelInvoice()
    @IsTest
    static void testGetAvailabelInvoice() {
        Test.startTest();
        Account account = [SELECT Id FROM Account LIMIT 1];
        CCM_WarrantyReturnInfoCtl.getAvailabelInvoice(account.Id);
        Test.stopTest();
    }

    // test getInvoicePrice()
    @IsTest
    static void testGetInvoicePrice() {
        Test.startTest();
        Account account = [SELECT Id FROM Account LIMIT 1];
        Product2 product = [SELECT Id FROM Product2 LIMIT 1];
        CCM_WarrantyReturnInfoCtl.getInvoicePrice(account.Id, product.Id);
        Test.stopTest();
    }

    // test getAddress
    @IsTest
    static void testGetAddress() {
        Test.startTest();
        Account account = [SELECT Id, Distributor_or_Dealer__c FROM Account LIMIT 1];
        Account_Address__c address = [SELECT Id, Approval_Status__c FROM Account_Address__c LIMIT 1];

        CCM_WarrantyReturnInfoCtl.getAddress(null);

        address.Approval_Status__c = 'Approved';
        update address;
        CCM_WarrantyReturnInfoCtl.getAddress(account.Id);

        account.Distributor_or_Dealer__c = '2nd Tier Distributor';
        update account;
        CCM_WarrantyReturnInfoCtl.getAddress(account.Id);

        Test.stopTest();
    }

    // test saveWarrantyReturn()
    @IsTest
    static void testSaveWarrantyReturn() {
        Test.startTest();
        Account account = [SELECT Id FROM Account LIMIT 1];
        Account_Address__c accountAddress = [SELECT Id FROM Account_Address__c LIMIT 1];
        Warranty_Return_Claim__c objWarranty = [SELECT Id, Name, Contact_Name__c, Contact_Email_Address__c, Contact_Phone_Number__c, Warehouse_Location__c, Chervon_or_Warehouse_Purchase__c, Customer_Reference_Number__c, Notes__c, Store_Address__c, Payment_Method__c, IsFinanceApproved__c
                                                FROM Warranty_Return_Claim__c LIMIT 1];

        // Warranty_Return_Claim_Item_Attachment__c attachment = [SELECT Id FROM Warranty_Return_Claim_Item_Attachment__c WHERE Attachment_Type__c = 'End Customer Purchase Date' LIMIT 1];
        Warranty_Return_Claim_Item__c objWarrantyItem = [SELECT Id FROM Warranty_Return_Claim_Item__c LIMIT 1];
        Product2 objProduct = [SELECT Id, Name, Brand_Name__c, ProductCode, IsActive, Description, Source__c FROM Product2 WHERE Name = 'TestProduct111'];

        // List<ContentDocumentLink> cDocList = [SELECT Id, LinkedEntityId, ContentDocumentId, ShareType FROM ContentDocumentLink LIMIT 3];
        // ContentDocumentLink cDoc = [SELECT Id, LinkedEntityId, ContentDocumentId, ShareType FROM ContentDocumentLink];

        ContentDocumentLink cDoc = New ContentDocumentLink();
        List<ContentDocument> documents2 = [
                SELECT Id, Title, LatestPublishedVersionId
                FROM ContentDocument
        ];
        // cDoc.LinkedEntityId = attachment2.id;
        cDoc.ContentDocumentId = documents2[0].Id;
        cDoc.ShareType = 'V';
        // insert cDoc;

        CCM_WarrantyReturnInfoCtl.WarrantyReturnWrapper wrapper = new CCM_WarrantyReturnInfoCtl.WarrantyReturnWrapper();

        List<CCM_WarrantyReturnInfoCtl.AttachmentWrapper> docAttachmentList = new List<CCM_WarrantyReturnInfoCtl.AttachmentWrapper>();
        CCM_WarrantyReturnInfoCtl.AttachmentWrapper docAttachment = new CCM_WarrantyReturnInfoCtl.AttachmentWrapper();
        docAttachment.documentId = cDoc.contentDocumentId;
        docAttachmentList.add(docAttachment);


        wrapper.recordId = objWarranty.Id;
        wrapper.customerId = account.Id;
        // wrapper.accountNumber = objWarranty.Customer__r.AccountNumber;
        // wrapper.customerName = objWarranty.Customer__r.Name;
        wrapper.contactName = objWarranty.Contact_Name__c;
        wrapper.contactEmailAddress = objWarranty.Contact_Email_Address__c;
        wrapper.contactPhoneNumber = objWarranty.Contact_Phone_Number__c;
        wrapper.warehouseLocationId = objWarranty.Warehouse_Location__c;
        wrapper.chervonOrWarehousePurchase = objWarranty.Chervon_or_Warehouse_Purchase__c;
        wrapper.customerReferenceNumber = objWarranty.Customer_Reference_Number__c;
        wrapper.notes = objWarranty.Notes__c;
        wrapper.storeAddressId = objWarranty.Store_Address__c;
        wrapper.paymentMethod = objWarranty.Payment_Method__c;

        List<CCM_WarrantyReturnInfoCtl.WarrantyReturnItemWrapper> warrantyReturnItemWrapperList = new List<CCM_WarrantyReturnInfoCtl.WarrantyReturnItemWrapper>();
        CCM_WarrantyReturnInfoCtl.WarrantyReturnItemWrapper warrantyReturnItemWrapper = new CCM_WarrantyReturnInfoCtl.WarrantyReturnItemWrapper();
        
        warrantyReturnItemWrapper.recordId = objWarrantyItem.Id;
        warrantyReturnItemWrapper.serialNumber = '11202009251234';
        warrantyReturnItemWrapper.brand = objProduct.Brand_Name__c;
        warrantyReturnItemWrapper.productCode = objProduct.ProductCode;
        warrantyReturnItemWrapper.purchaseDateFiles = docAttachmentList;
        warrantyReturnItemWrapper.returnDateFiles = docAttachmentList;
        warrantyReturnItemWrapper.returnReasonFiles = docAttachmentList;
        warrantyReturnItemWrapper.invoicePrice = 1;
        warrantyReturnItemWrapper.quantity = 1;
        


        warrantyReturnItemWrapperList.add(warrantyReturnItemWrapper);
        wrapper.items = warrantyReturnItemWrapperList;

        CCM_WarrantyReturnInfoCtl.AddressWrapper addressWrapper = new CCM_WarrantyReturnInfoCtl.AddressWrapper();
        addressWrapper.addressId = accountAddress.Id;
        wrapper.billingAddress = addressWrapper;
        wrapper.isAlternativeAddress = true;
        wrapper.isAlternativeStoreAddress = true;
        CCM_WarrantyReturnInfoCtl.saveWarrantyReturn(JSON.serialize(wrapper), true);

        wrapper.isAlternativeAddress = false;
        wrapper.isAlternativeStoreAddress = false;
        CCM_WarrantyReturnInfoCtl.saveWarrantyReturn(JSON.serialize(wrapper), true);

        Test.stopTest();
    }
    
    // test getWarrantyReturnClaimInfo()
    @IsTest
    static void testGetWarrantyReturnClaimInfo() {
        Test.startTest();
        Warranty_Return_Claim__c objWarrantyReturn = [SELECT Id FROM Warranty_Return_Claim__c LIMIT 1];
        objWarrantyReturn.Is_Alternative_Address__c = true;
        objWarrantyReturn.Is_Alternative_Store_Address__c = true;
        update objWarrantyReturn;
        CCM_WarrantyReturnInfoCtl.getWarrantyReturnClaimInfo(objWarrantyReturn.Id);
        Test.stopTest();
    }

    @IsTest
    static void testGetWarrantyReturnClaimInfo2() {
        Test.startTest();
        Warranty_Return_Claim__c objWarrantyReturn = [SELECT Id FROM Warranty_Return_Claim__c LIMIT 1];
        objWarrantyReturn.Is_Alternative_Address__c = false;
        objWarrantyReturn.Is_Alternative_Store_Address__c = false;
        update objWarrantyReturn;
        CCM_WarrantyReturnInfoCtl.getWarrantyReturnClaimInfo(objWarrantyReturn.Id);
        Test.stopTest();
    }

}