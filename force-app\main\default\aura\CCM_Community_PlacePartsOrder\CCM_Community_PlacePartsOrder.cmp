<!--
 - Created by gluo006 on 9/25/2019.
 -->

<aura:component controller="CCM_PartsOrder_DetailCtl" description="CCM_Community_PlacePartsOrder"
                implements="forceCommunity:availableForAllPageTypes,flexipage:availableForAllPageTypes,force:hasRecordId" access="global">
    <aura:attribute name="brandScope" type="String" default=""/>
    <aura:attribute name="brandScopeOpt" type="List" default="" access="public"/>
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="customerId" type="String" default=""/>
    <aura:attribute name="stepNameList" type="List" default="[]"/>
    <aura:attribute name="currentStep" type="Integer" default="1"/>
    <aura:attribute name="po" type="Object" default=""/>
    <aura:attribute name="orderItemList" type="List" default="[]"/>
    <aura:attribute name="columns" type="List" default="[]"/>
    <aura:attribute name="billingAddress" type="String" default=""/>
    <aura:attribute name="billingAddressInput" type="String" default=""/>
    <aura:attribute name="shippingAddress" type="String" default=""/>
    <aura:attribute name="shippingAddressInput" type="String" default=""/>
    <aura:attribute name="shippingMethod" type="String" default=""/>
    <aura:attribute name="logisticsSupplier" type="String" default=""/>
    <aura:attribute name="showEditModal" type="Boolean" default="false" />
    <aura:attribute name="modalContent" type="String" />
    <aura:attribute name="paymentTerm" type="String" default="" />
    <aura:attribute name="paymentTermLabel" type="String" default="" />
    <aura:attribute name="freightTerm" type="String" default="" />
    <aura:attribute name="freightTermLabel" type="String" default="" />
    <aura:attribute name="freightTermRuleFee" type="Currency" default="0" />
    <aura:attribute name="isShowTerm" type="Boolean" default="false"/>
    <aura:attribute name="oldBrandName" type="String" default=""/>
    <aura:registerEvent name="productSelected" type="c:CCM_SelectProductListEvt"/>
    <aura:handler name="change" value="{!v.brandScope}" action="{!c.brandChange}" />
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <aura:attribute name="currencySymbol" type="String" default=""/>

    <div class="slds-grid slds-grid_align-space">
        <div class="slds-grid slds-grid--align-center slds-size--1-of-1">
            <div class="slds-col slds-size_1-of-1 slds-wrap">
                <c:CCM_ProcessStep currentStep="{!v.currentStep}" stepName="{!v.stepNameList}" />
                <div class="slds-path  slds-p-top_small slds-p-vertical_medium">
                    <aura:if isTrue="{!v.currentStep == 1}">
                        <c:CCM_Community_PartsProductSelect recordId="{!v.recordId}" quotation="{!v.po}" orderItemList="{!v.orderItemList}" currentStep="{!v.currentStep}" brand="{!v.brandScope}" customerId="{!v.customerId}" currencySymbol="{!v.currencySymbol}"/>
                    </aura:if>

                    <aura:if isTrue="{!v.currentStep == 2}">
                        <c:CCM_PartsOrder_FillAddress_Cmp recordId="{!v.recordId}" quotation="{!v.po}" customerId="{!v.customerId}" currentStep="{!v.currentStep}" freightTermRuleFee="{!v.freightTermRuleFee}" orderItemList="{!v.orderItemList}"/>
                    </aura:if>

                    <aura:if isTrue="{!v.currentStep == 3}">
                        <c:CCM_PartsOrder_Detail recordId="{!v.recordId}" currentStep="{!v.currentStep}" />
                    </aura:if>
                </div>
            </div>
        </div>
    </div>

    <aura:if isTrue="{!v.showEditModal}">
        <div style="" >
            <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                <div class="slds-modal__container" style="height:auto !important; transform: translate(0%, 50%);">
                    <div class="modal-header slds-modal__header">
                        <button class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse" title="{!$Label.c.CCM_Portal_Close}" onclick="{!c.closeModal}">
                            <lightning:icon iconName="utility:close" alternativeText="{!$Label.c.CCM_Portal_Close}" variant="close" class = "modal_close"/>
                            <span class="slds-assistive-text">{!$Label.c.CCM_Portal_Close}</span>
                        </button>
                        <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">{!$Label.c.CCM_Portal_ConfirmAuthorizedBrandChange}</h2>
                    </div>
                    <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                        <p style="padding: 10px;">
                            {!v.modalContent}
                        </p>
                    </div>
                    <footer class="slds-modal__footer">
                        <button class="slds-button slds-button_neutral" onclick="{!c.closeModal}">{!$Label.c.CCM_Portal_Cancel}</button>
                        <button class="slds-button slds-button_brand" onclick="{!c.confirmChange}">{!$Label.c.CCM_Portal_Confirm}</button>
                    </footer>
                </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
        </div>
    </aura:if>
</aura:component>