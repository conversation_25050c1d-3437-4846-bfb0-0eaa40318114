({
    onInit: function (component, event, helper) {
        var action = component.get('c.checkCurrentUser');
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var resp = response.getReturnValue();
                var createRecordEvent = $A.get("e.force:createRecord");
                createRecordEvent.setParams({
                    "entityApiName": "Lead",
                    "defaultFieldValues": {
                        "Intended_Brand__c": resp.brand,
                        "Sales_Group__c": resp.salesGroup
                    }
                });
                createRecordEvent.fire();
            } else {
                var errors = response.getError();
                console.error(errors);
            }
        });
        $A.enqueueAction(action);
    },
})