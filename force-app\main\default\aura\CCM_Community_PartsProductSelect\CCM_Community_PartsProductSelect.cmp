<!--
 - Created by gluo006 on 9/25/2019.
 -->
<aura:component description="CCM_Community_PartsProductSelect" implements="forceCommunity:availableForAllPageTypes,flexipage:availableForAllPageTypes"
                access="global" controller="CCM_NewPartsOrder">
    <ltng:require scripts="{!join(',', $Resource.Validator)}"/>
    <aura:attribute name="brand" type="String" default=""/>
    <aura:attribute name="customerId" type="String" default=""/>
    <aura:attribute name="prodCondition" type="String" default="" />
    <aura:attribute name="priceBookEntry" type="Object" default=""/>
    <aura:attribute name="quotation" type="Object" default=""/>
    <aura:attribute name="product" type="Object" default=""/>
    <!--order table-->
    <aura:attribute name="orderItemList" type="List" />
    <aura:attribute name="totalQuantity" type="String" default="0"/>
    <aura:attribute name="subTotal" type="Decimal" default="0"/>
    <aura:attribute name="currencySymbol" type="String" default="$"/>
    <aura:attribute name="requireFlag" type="Boolean" default=""/>
    <aura:attribute name="disableFlag" type="Boolean" default="false"/>
    <aura:attribute name="disableBtn" type="Boolean" default="false"/>
    <aura:handler event="c:CCM_Community_InlineEditTableEvent" name="eventTrigger" action="{!c.handleDeleteAndCalculate}"/>
    <aura:attribute name="isProductSelectStatus" type="Boolean" default="false"/>
    <aura:attribute type="String" name="recordId"/>
    <aura:attribute type="Integer" name="currentStep" default="1"/>
    <aura:attribute type="Integer" name="operationRow" />
    <aura:attribute name="brandList" type="List" default="['EGO', 'Skil', 'SkilSaw', 'HammerHead', 'Hypertough' ]"/>
    <aura:attribute type="List" name="explosiveDataList" />
    <aura:attribute name="vfHost" type="String" default="msdev--steven.lightning.force.com"/>
    <aura:attribute type="Object" name="mySwiper" />
    <aura:attribute name="contents" type="List" default="[]"/>
    <aura:attribute name="prefixURL" type="String" default="/sfc/servlet.shepherd/version/renditionDownload?rendition=ORIGINAL_Png&amp;versionId="/>
    <aura:attribute name="versionList" type="List" default="[]"/>
    <aura:attribute name="version" type="String" default=""/>
    <aura:attribute name="searchExplosiveProductCondition" type="String" default=""/>
    <aura:attribute name="explosiveBrand" type="String" default=""/>
    <aura:attribute name="explosiveProductId" type="String" default=""/>
    <aura:attribute name="activeIndex" type="String" default=""/>
    <aura:attribute name="paymentTerm" type="String" default="NA001 - NET 30 days" />
    <aura:attribute name="freightTerm" type="String" default="Prepaid @100" />
    <aura:attribute name="freightTermRuleFee" type="Currency"/>
    <aura:attribute name="Freight_Fee_To_Be_Waived__c" type="String" default="" />
    <aura:attribute name="sequenceNo" type="String" default="" />
    <aura:attribute name="showUpload" type="Boolean" default="false" />
    <!-- add this attribute to avoid wrong product price due to network latency -->
    <aura:attribute name="productPriceRefreshComplete" type="Boolean" default="true" />
    <aura:attribute name="kobaltEnable" type="Boolean" default="false" />
    <ltng:require styles="{!$Resource.swiperCSS}" />
    <ltng:require scripts="{!$Resource.swiperJS}"
                  afterScriptsLoaded="{!c.afterScriptsLoaded}" />
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <aura:handler name="change" value="{!v.activeIndex}" action="{!c.changeSlide}"/>
    <aura:handler name="change" value="{!v.explosiveBrand}" action="{!c.refreshFlag}"/>
    <aura:handler event="c:CCM_SelectProductListEvt" action="{!c.doInit}"/>

    <lightning:card class="mainContent">
        <div class="slds-grid slds-grid--align-end slds-p-horizontal--small slds-p-bottom--small">
            <div class="slds-clearfix">
                <div class="slds-col slds-list_horizontal slds-wrap slds-float_right">
                    <span class="ccm_fontColor">{!$Label.c.CCM_Portal_PaymentTerm}: {!v.paymentTerm}<br/>{!$Label.c.CCM_Portal_FreightTerm}: {!v.freightTerm}</span>
                </div>
            </div>
        </div>
        <p class="slds-p-horizontal_small">
        <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_fixed-layout slds-table_resizable-cols slds-table_striped" role="grid">
            <thead>
            <tr class="slds-line-height_reset">
                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="width: 5%">
                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Line}">{!$Label.c.CCM_Portal_Line}</span>
                        </div>
                    </a>
                </th>
                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth" scope="col">
                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Brand}">{!$Label.c.CCM_Portal_Brand}</span>
                        </div>
                    </a>
                </th>
                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="">
                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Model}">{!$Label.c.CCM_Portal_Model}</span>
                        </div>
                    </a>
                </th>
                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="">
                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_PartsNumber}">{!$Label.c.CCM_Portal_PartsNumber}</span>
                        </div>
                    </a>
                </th>
                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_PartsName}">{!$Label.c.CCM_Portal_PartsName}</span>
                        </div>
                    </a>
                </th>
                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Qty}">{!$Label.c.CCM_Portal_Qty}</span>
                        </div>
                    </a>
                </th>
                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_UnitPrice}">{!$Label.c.CCM_Portal_UnitPrice}</span>
                        </div>
                    </a>
                </th>
                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Subtotal}">{!$Label.c.CCM_Portal_Subtotal}</span>
                        </div>
                    </a>
                </th>
                <th class="smallWidth" scope="col" style="width: 6%">
                    <div class="slds-truncate" title="{!$Label.c.CCM_Portal_Actions}">{!$Label.c.CCM_Portal_Actions}</div>
                </th>
            </tr>
            </thead>
            <tbody>
            <aura:iteration items="{!v.orderItemList}" var="orderItem" indexVar="index">
                <tr aria-selected="false" class="slds-hint-parent" id="{!index}" onmouseover="{!c.rowFocus}">
                    <th scope="row">
                        <div class="slds-truncate" title="">
                            {!index + 1}
                        </div>
                    </th>
                    <td role="gridcell">
                        <div class="slds-truncate">
                            {!(and(orderItem.Product__r,orderItem.Product__r.Brand_Name__c) ? orderItem.Product__r.Brand_Name__c : orderItem.Brand_Name__c )}
                            <!-- {!orderItem.Brand_Name__c} -->
                        </div>
                    </td>
                    <td role="gridcell">
                        <div class="slds-truncate">
                            {!(and(orderItem.Product__r,orderItem.Product__r.ProductCode) ? orderItem.Product__r.ProductCode : orderItem.ProductCode )}
                            <!-- {!orderItem.ProductCode} -->
                        </div>
                    </td>
                    <td role="gridcell">
                        <c:CCM_AutoMatchPickList objectType="product2"
                                                showLabel="false"
                                                labelField="{! ($Label.c.CCM_Portal_Language == 'fr' ? 'Product_Name_French__c' : 'Name')}"
                                                labelField1="ProductCode"
                                                filterCondition='{!v.prodCondition}'
                                                fieldList="Name,Product_Name_French__c,Item_Number__c,serial_number__c,ProductCode"
                                                inputValue="{!orderItem.Item_Number__c}"
                                                value="{!orderItem.Product__c}"
                                                required="{!v.requireFlag}"
                                                disabled="{!v.disableFlag}"
                                                indexnum="{!index}"
                                                onSelect="{!c.onSelectProd}"/>
                    </td>
                    <td role="gridcell">
                        <div class="slds-truncate">
                            {!orderItem.Name}
                        </div>
                    </td>
                    <td role="gridcell">
                        <div class="slds-truncate clear-user-agent-styles" >
                            <lightning:input type="number" label=""  value="{!orderItem.Quantity__c}" name="{!index}" min="1" disabled="{!v.disableFlag}" onchange="{!c.calculateSubTotal}"/>
                        </div>
                    </td>
                    <td role="gridcell">
                        <div class="slds-truncate">
                            <lightning:formattedNumber value="{!orderItem.Unit_Price__c}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" minimumFractionDigits="2"/>
                        </div>
                    </td>
                    <td role="gridcell">
                        <div class="slds-truncate">
                            <lightning:formattedNumber value="{!orderItem.Sub_Total__c}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" minimumFractionDigits="2"/>
                        </div>
                    </td>
                    <td role="gridcell" data-index="{!v.index}"  onclick="{!c.handleDelete}" class="deleteBtn">
                         <lightning:icon iconName="utility:delete" alternativeText="{!$Label.c.CCM_Portal_Delete}" size="x-small" class="deleteBtn"/>
                    </td>
                </tr>
            </aura:iteration>
            </tbody>
        </table>
        </p>

        <div class="slds-m-vertical_small slds-m-horizontal_medium slds-border_bottom">
            <lightning:layout horizontalAlign="space" verticalAlign="center">
                <lightning:layoutItem alignmentBump="right">
                    <div style="padding: 10px;">
                        <lightning:button label="{!$Label.c.CCM_Portal_AddItem}" iconName="utility:add" iconPosition="left" onclick="{!c.addItem}" disabled="{!v.disableBtn}"/>                    </div>
                </lightning:layoutItem>
                <lightning:layoutItem alignmentBump="left">
                        <div class="slds-hide" aura:id="noFreeShipping">{!$Label.c.CCM_Portal_Purchaseextra} <lightning:formattedNumber value="{!v.Freight_Fee_To_Be_Waived__c}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" minimumFractionDigits="2"/> {!$Label.c.CCM_Portal_togetafreefreight}</div>
                        <div class="slds-hide" aura:id="freeShipping">{!$Label.c.CCM_Portal_YougetfreeshippingbyChervon} </div>
                </lightning:layoutItem>
            </lightning:layout>
            <lightning:layout>
                <lightning:layoutItem alignmentBump="left">
                    <div><strong>{!$Label.c.CCM_Portal_TotalQuantity}: {!v.quotation.Total_Quantity__c}</strong></div>
                </lightning:layoutItem>
            </lightning:layout>
            <lightning:layout>
                <lightning:layoutItem alignmentBump="left">
                    <div class="slds-m-vertical_xx-small"><strong>{!$Label.c.CCM_Portal_TotalAmount}: <lightning:formattedNumber value="{!v.quotation.Product_Price__c}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" minimumFractionDigits="2"/></strong></div>
                </lightning:layoutItem>
            </lightning:layout>
        </div>

        <aura:set attribute="footer">
            <div class="slds-float--left"><a class="explosive" onclick="{!c.openModal}">{!$Label.c.CCM_Portal_AddpartsviaExplodedDiagram}</a></div>
            <div>
                <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" label="{!$Label.c.CCM_Portal_Cancel}" title="{!$Label.c.CCM_Portal_Cancel}" onclick="{!c.cancel}" />
                <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" label="{!$Label.c.CCM_Portal_Save}" title="{!$Label.c.CCM_Portal_Save}" onclick="{!c.doSave}" />
                <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_Portal_Next}" title="{!$Label.c.CCM_Portal_Next}" onclick="{!c.nextStep}" />
            </div>
        </aura:set>
    </lightning:card>
    <div aura:id="boomModal" class="slds-hide">
        <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">
                <lightning:spinner class="slds-hide" variant="brand" aura:id="modalSpinner" alternativeText="{!$Label.c.CCM_Portal_Loading}" size="medium" />
                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse"
                            title="{!$Label.c.CCM_Portal_Close}" onclick="{!c.onClickModalCancel}">
                        <lightning:icon alternativeText="{!$Label.c.CCM_Portal_Close}" iconName="utility:close" size="small" variant="bare"/>
                    </button>
                </header>
                <div class="slds-modal__content slds-p-around_medium">
                    <div class="slds-grid slds-m-bottom_x-large slds-grid_vertical-align-end" id="top">
                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium slds-size_1-of-4">
                            <lightning:select name="" label="{!$Label.c.CCM_Portal_Brand}" aura:id="brand" value="{!v.explosiveBrand}">
                                <option text="{!$Label.c.CCM_Portal_NonePlaceTips}" value=""></option>
                                <option text="EGO" value="EGO"></option>
                                <option text="Skil" value="Skil"></option>
                                <option text="SkilSaw" value="SkilSaw"></option>
                                <option value="FLEX">FLEX</option>
                                <aura:if isTrue="{!v.kobaltEnable}">
                                    <option value="Kobalt">Kobalt</option>
                                </aura:if>
                            </lightning:select>
                        </div>
                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium slds-size_1-of-4">
                            <c:CCM_AutoMatchPickList objectType="product2"
                                                     showLabel="true"
                                                     label="{!$Label.c.CCM_Portal_Product}"
                                                     filterCondition='{!v.searchExplosiveProductCondition}'
                                                     fieldList="Name,Product_Name_French__c,Item_Number__c,serial_number__c,ProductCode"
                                                     labelField="{! ($Label.c.CCM_Portal_Language == 'fr' ? 'Product_Name_French__c' : 'Name')}"
                                                     labelField1="ProductCode"
                                                     onSelect="{!c.getVersion}"

                            />
                        </div>
                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium slds-size_1-of-4 slds-is-relative">
                                <lightning:select name="" label="{!$Label.c.CCM_Portal_Version}" aura:id="version" value="{!v.version}">
                                    <option text="{!$Label.c.CCM_Portal_NonePlaceTips}" value=""/>
                                    <aura:iteration items="{!v.versionList}" var="version">
                                        <option text="{!version}" value="{!version}"/>
                                    </aura:iteration>
                                </lightning:select>
                                <div class="slds-p-top_x-small slds-is-absolute" style="width: 120%">
                                    <a href="{!$Label.c.CCM_Exploded_Diagram_Note_4_Different_Version}" target="_blank">{!$Label.c.CCM_Portal_distinguishamongTips}</a>
                                </div>
                        </div>
                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium">
                            <lightning:button variant="brand" label="{!$Label.c.CCM_Portal_Search}" onclick="{! c.handleSearch }" />
                        </div>
                    </div>
                    <!-- Slider main container -->
                    <div class="swiper-container">
                        <!-- Additional required wrapper -->
                        <div class="swiper-wrapper">
                            <!-- Slides -->
                            <aura:iteration items="{!v.contents}" var="content">
                                <div class="swiper-slide"><lightning:fileCard fileId="{!content}" description=""/></div>
<!--                                <div class="swiper-slide"><lightning:fileCard fileId="069S000000105rqIAA" description="Sample Description"/></div>-->
                            </aura:iteration>
                        </div>
                        <!-- If we need navigation buttons -->
                        <div class="swiper-button-prev"></div>
                        <div class="swiper-button-next"></div>
                    </div>
                    <div class="slds-grid slds-m-vertical--medium slds-grid_vertical-align-end">
                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium slds-size_1-of-4">
                            <lightning:input label="{!$Label.c.CCM_Portal_SequenceNo}" aura:id="sequenceNo" value="{!v.sequenceNo}"/>
                        </div>
                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium">
                            <a class="slds-button slds-button_brand slds-float--right" onclick="{! c.highLightSequenceNo }" id="sequenceNo">{!$Label.c.CCM_Portal_Go}</a>
                        </div>
                    </div>
                    <div class="table_container slds-size_1-of-1 slds-medium-size_1-of-1 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small slds-scrollable_x  slds-m-top_large">
                        <lightning:spinner aura:id="proListSpinner" class="slds-hide" alternativeText="{!$Label.c.CCM_Portal_Loading}" size="small" variant="brand" />
                        <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_fixed-layout slds-table_resizable-cols">
                            <thead>
                            <tr class="slds-line-height_reset" aria-selected="false">
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 5%">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title=""></span>
                                        </div>
                                    </a>
                                </th>
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 15%">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_SequenceNo}">{!$Label.c.CCM_Portal_SequenceNo}</span>
                                        </div>
                                    </a>
                                </th>
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 10%">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_ProductModel}">{!$Label.c.CCM_Portal_ProductModel}</span>
                                        </div>
                                    </a>
                                </th>
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 10%">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_PartsNumber}">{!$Label.c.CCM_Portal_PartsNumber}</span>
                                        </div>
                                    </a>
                                </th>
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 30%">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_PartsName}">{!$Label.c.CCM_Portal_PartsName}</span>
                                        </div>
                                    </a>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <aura:iteration items="{!v.explosiveDataList}" var="parts" indexVar="index">
                                <tr aria-selected="false" class="{!parts.highLight ? 'slds-hint-parent highLight' : 'slds-hint-parent'}" aura:id="{!('line' + index)}" id="{!parts.ExplosionID__c}">
                                    <td class="slds-text-align_right" role="gridcell">
                                        <lightning:input type="checkbox" label="" checked="{!parts.isSelect}"/>
                                    </td>
                                    <td data-label="Product Model#" scope="row">
                                        <div class="slds-truncate" title="">
                                            {!parts.ExplosionID__c}
                                        </div>
                                    </td>
                                    <td data-label="Product Model#" scope="row">
                                        <div class="slds-truncate" title="">
                                            {!parts.Product__r.ProductCode}
                                        </div>
                                    </td>
                                    <td data-label="Parts Number">
                                        <div class="slds-truncate" title="">
                                            {!parts.Parts__r.ProductCode}
                                        </div>
                                    </td>
                                    <td data-label="Description">
                                        <div class="slds-truncate" title="">
                                            <!-- add haibo: french -->
                                            <aura:if isTrue="{!($Label.c.CCM_Portal_Language == 'fr')}">
                                                {!parts.Parts__r.Product_Name_French__c}
                                                <aura:set attribute="else">
                                                    {!parts.Parts__r.Name}
                                                </aura:set>
                                            </aura:if>
                                        </div>
                                    </td>
                                </tr>
                            </aura:iteration>
                            </tbody>
                        </table>
                    </div>
                </div>
                <footer class="slds-modal__footer">
                    <lightning:button onclick="{!c.addSelectedToCart}" class="slds-float--right slds-m-vertical--medium" variant="brand" label="{!$Label.c.CCM_Portal_AddToCart}" iconName="utility:add" iconPosition="left" />
                    <a class="slds-m-vertical--medium slds-m-right--medium backToTop" onclick="{!c.backToTop}">{!$Label.c.CCM_Portal_BackToTop}</a>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </div>
    <aura:if isTrue="{!v.showUpload}">
        <p style="margin: 10px 0 10px 0; font-size: 16px; font-weight:bold">{!$Label.c.CCM_Portal_AddProductsFromFile}</p>
        <div>
            <c:ccmPurchaseOrderUpload aura:id="uploadCmp" orderType="parts" onuploadfinish="{!c.handleUploadFinish}"></c:ccmPurchaseOrderUpload>
        </div>
    </aura:if>
</aura:component>