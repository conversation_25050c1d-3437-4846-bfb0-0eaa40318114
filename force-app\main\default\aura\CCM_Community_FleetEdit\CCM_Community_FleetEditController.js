({
    doInit : function(component, event, helper) {
        var userId = $A.get("$SObjectType.CurrentUser.Id");
        var recordId = helper.getUrlParameter('recordId');
        var programId = helper.getUrlParameter('programid');
        component.set('v.recordId', recordId);
        if($A.util.isEmpty(recordId)){
            helper.getInitDataHelper(component, userId, programId);
        }else{
            helper.getFleetClaimDataHelper(component, recordId);
        }

    },
    doNext: function(component, event, helper) {
        var fleetItemList = component.get("v.fleetClaim.fleetItemList");
        var filterFleetItem = fleetItemList.filter(item => {
            return item.kitCode != '' && item.kitCode !== undefined;
        });
        component.set("v.fleetClaim.fleetItemList", filterFleetItem);
        let fleetClaim = component.get("v.fleetClaim");
        if (helper.verifyForm(component, fleetClaim, "Save") === false) {
            helper.showToast(null, $A.get("$Label.c.CCM_Portal_Cannotsubmitthedatapleasereviewerrorfields"), "warning");
            return;
        }
        let fleetProgramRule = component.get("v.fleetProgramRule");
        let totalSalesAmount = fleetClaim.totalSalesAmount || 0;
        if(!fleetClaim.hasOneMatchThreshold) {
            if(fleetProgramRule.purchaseOrderAmount) {
                if(totalSalesAmount < fleetProgramRule.purchaseOrderAmount) {
                    let currency = fleetClaim.currencyCode;
                    if(currency != 'CAD') {
                        currency = '$';
                    }
                    helper.showToast(null, $A.get("$Label.c.CCM_Portal_Thisclaimdoesnotmeettheminimumsaleof") + ' ' + currency + fleetProgramRule.purchaseOrderAmount.toString() + ' ' + $A.get("$Label.c.CCM_Portal_requiredforthe") + ' ' + fleetProgramRule.programCode, "warning");
                    return;
                }
            }
        }
        helper.initWarrantyDataHelper(component);
        component.set("v.step", 2);
    },
    doPrevious : function(component, event, helper) {
        component.set('v.step', 1);
    },
    doSave : function(component, event, helper){
        helper.doSaveHelper(component, 'Save');
    },
    doSubmit : function(component, event, helper){
        helper.doSaveHelper(component, 'Submit');
    }
})