public without sharing class CCM_NewProspectController {
    @AuraEnabled
    public static CheckResult checkCurrentUser() {
        CheckResult r = new CheckResult();
        String userName = UserInfo.getName();
        r.brand = '';
        r.salesGroup = '';
        List<User> userObj =[SELECT Id,Name,UserRole.DeveloperName,UserRole.Name FROM User WHERE UserRole.Name LIKE 'OPE%' AND Id =:UserInfo.getUserId()];
        if (userObj != null && userObj.size() >0) {
            r.brand ='EGO';
        }
        for(Prospect_Assignment_Rule_SG_Mapping__mdt ma : [SELECT Id,Sales_Group__c,Sales_Manager_Name__c FROM Prospect_Assignment_Rule_SG_Mapping__mdt WHERE Channel__c ='DD']){
            if(userName == ma.Sales_Manager_Name__c){
                r.salesGroup = ma.Sales_Group__c;
                break;
            }
        }
        System.debug(LoggingLevel.INFO, '*** r: ' + r);
        return r;
    }


    public Class CheckResult {
        @AuraEnabled public String salesGroup;
        @AuraEnabled public String brand;
    }
}