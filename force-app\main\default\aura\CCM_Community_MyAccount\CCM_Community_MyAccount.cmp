<!--
 - Created by gluo006 on 7/17/2019.
 -->

<aura:component description="CCM_MyAccount" implements="forceCommunity:availableForAllPageTypes" access="global"
    controller="CCM_Community_MyAccountCtl">
    <aura:attribute name="columns" type="List" default="[]" />
    <aura:attribute name="columns1" type="List" default="[]" />
    <aura:attribute name="columns3" type="List" default="[]" />
    <aura:attribute name="currentData" type="List" default="[]" />
    <aura:attribute name="currentCreditData" type="List" default="[]" />
    <aura:attribute name="currentPaymentData" type="List" default="[]" />
    <aura:attribute name="allData" type="List" default="[]" />
    <aura:attribute name="allCreditData" type="List" default="[]" />
    <aura:attribute name="totalAmount" type="Currency" default="0.00" />
    <aura:attribute name="dueRemainingAmount" type="Currency" default="0.00" />
    <aura:attribute name="paidAmount" type="Currency" default="0.00" />
    <aura:attribute name="accountBalanceAmount" type="String" default="0.00" />
    <aura:attribute name="dueTotalAmount" type="Currency" default="0.00" />
    <aura:attribute name="creditMemoTotalAmount" type="Currency" default="0.00" />
    <aura:attribute name="pastDueBalanceAmount" type="Currency" default="0.00" />
    <aura:attribute name="isBusy" type="Boolean" default="false" />
    <aura:attribute name="lastModifiedDate" type="String" default="" />
    <aura:attribute name="currencySymbol" type="String" default="USD" />

    <aura:attribute name="pageNumber" type="String" default="1" />
    <aura:attribute name="pageCount" type="String" default="10" />
    <aura:attribute name="totalRecords" type="String" default="0" />
    <aura:handler name="pageChange" event="c:CCM_ListPageFooter_PageChangeEvt" action="{!c.pageChange}" />
    <aura:handler name="pageCountChange" event="c:CCM_ListPageFooter_PageCountChangeEvt"
        action="{!c.pageCountChange}" />

    <aura:attribute name="tabId" type="String" default="Invoice" />
    <aura:attribute name="isFirstChange" type="Boolean" default="true" />
    <aura:handler name="change" value="{!v.tabId}" action="{!c.handleChangeTab}" />
    <aura:attribute name="pageNumber2" type="String" default="1" />
    <aura:attribute name="pageCount2" type="String" default="10" />
    <aura:attribute name="totalRecords2" type="String" default="0" />
    <aura:attribute name="pageNumber3" type="String" default="1" />
    <aura:attribute name="pageCount3" type="String" default="10" />
    <aura:attribute name="totalRecords3" type="String" default="0" />
    <aura:attribute name="isShowPayment" type="Boolean" default="false" />
    <aura:attribute type="Boolean" name="isCreditSelect" default="false" />
    <aura:attribute type="Boolean" name="isFirstPay" />
    <aura:attribute type="List" name="selectInvoicesList" />
    <aura:attribute type="List" name="selectCreditList" />
    <aura:attribute type="Integer" name="subtotal" default="0" />
    <aura:attribute type="Integer" name="totalPay" default="0" />

    <aura:attribute name="invoiceDateRange" type="String" default="" />
    <aura:attribute name="invoiceDateRangeOption" type="List"
        default="[]" />

    <aura:attribute name="overdueStr" type="String" default="" />
    <aura:attribute name="overdueOption" type="List"
        default="[]" />

    <!-- export -->
    <aura:attribute name="exportFileName" type="String" default="" />
    <aura:attribute name="showExport" type="Boolean" default="false" />
    <aura:attribute name="exportType" type="String" default="" />
    <aura:attribute name="exportTypeList" type="List" default="[]" />

    <aura:handler name="init" action="{!c.doinit}" value="this" />
    <article class="slds-card slds-m-top--medium">
        <lightning:spinner size="large" variant="brand"
            class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }" />
        <div class="slds-grid">
            <header class="slds-media slds-media_center slds-has-flexi-truncate">
                <div class="slds-media__body firstBanner">
                    <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade">
                        <span class="section-header-title slds-p-horizontal--small slds-truncate"
                            title="{!$Label.c.CCM_Portal_AccountBalance}">
                            <span><strong>{!$Label.c.CCM_Portal_AccountBalance}</strong></span>
                        </span>
                        <span class="slds-p-right--small">{!$Label.c.CCM_Portal_Thedatawasupdated}: {!v.lastModifiedDate}</span>
                    </h2>
                </div>
            </header>
        </div>
        <div class="slds-grid slds-m-top--medium slds-p-horizontal--small">
            <header class="slds-media slds-media_center slds-has-flexi-truncate">
                <div class="slds-media__body">
                    <span class="ccm_marginRight"><span class="rebate_label">{!$Label.c.CCM_Portal_AccountBalance}: </span>
                        <lightning:formattedNumber value="{!v.accountBalanceAmount}" style="currency"
                            currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" class="rebate_value" />
                    </span>

                    <span class="ccm_marginRight"><span class="rebate_label">{!$Label.c.CCM_Portal_OpenInvoices}: </span>
                        <lightning:formattedNumber value="{!v.dueTotalAmount}" style="currency"
                            currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" class="rebate_value" />
                    </span>

                    <span class="ccm_marginRight"><span class="rebate_label">{!$Label.c.CCM_Portal_CreditMemo}: </span>
                        <lightning:formattedNumber value="{!v.creditMemoTotalAmount}" style="currency"
                            currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" class="rebate_value" />
                    </span>

                    <span class="ccm_marginRight"><span class="rebate_label">{!$Label.c.CCM_Portal_PastDueBalance}: </span>
                        <lightning:formattedNumber value="{!v.pastDueBalanceAmount}" style="currency"
                            currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" class="rebate_value" />
                    </span>

                    <!-- <span class="ccm_marginRight"><span class="rebate_label">Account Balance: </span><span class="rebate_value">{!v.accountBalanceAmount}</span></span>
                    <span class="ccm_marginRight"><span class="rebate_label">Account Payable: </span><span class="rebate_value">{!v.dueTotalAmount}</span></span>
                    <span class="ccm_marginRight"><span class="rebate_label">Account Receivable: </span><span class="rebate_value">{!v.creditMemoTotalAmount}</span></span> -->
                </div>
            </header>
        </div>
        <div class="slds-theme_default slds-m-top--medium slds-p-horizontal--small">
            <lightning:tabset selectedTabId="{!v.tabId}">
                <lightning:tab label="{!$Label.c.CCM_Portal_DebitMemo}" id="Invoice">
                    <lightning:layout multipleRows="true" horizontalAlign="spread">
                        <lightning:layoutItem size="4" class="slds-p-top_small">
                            <lightning:combobox class="ccm_display" name="Invoice Date Range"
                                value="{!v.invoiceDateRange}" options="{!v.invoiceDateRangeOption}"
                                label="{!$Label.c.CCM_Portal_InvoiceDateRange}" />
                        </lightning:layoutItem>

                        <lightning:layoutItem size="4" class="slds-p-top_small slds-p-left--large">
                            <lightning:combobox class="ccm_display" name="overdue" value="{!v.overdueStr}"
                                options="{!v.overdueOption}" label="{!$Label.c.CCM_Portal_Overdue}" />
                        </lightning:layoutItem>

                        <lightning:layoutItem size="4" class="slds-p-top_small slds-p-left--large">
                        </lightning:layoutItem>
                    </lightning:layout>

                    <div class="slds-m-top--large slds-m-bottom--large">
                        <lightning:layout horizontalAlign="center">
                            <lightning:layoutItem>
                                <lightning:button variant="brand" label="{!$Label.c.CCM_Portal_Search}" onclick="{! c.handleSearch }" />
                                <lightning:button variant="brand" label="{!$Label.c.CCM_Portal_Reset}" onclick="{!c.doReset}" />
                                <lightning:button variant="brand" label="{!$Label.c.CCM_Portal_Export}" onclick="{!c.handleExportDebitMemo}" />
                            </lightning:layoutItem>
                        </lightning:layout>
                    </div>
                    <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                        <div class="slds-wrap">
                            <c:CCM_DataTable columns="{!v.columns}" data="{!v.currentData}" />
                            <div class="slds-m-top--large slds-m-bottom--large slds-text-align--right">
                                <aura:renderIf isTrue="{! v.totalRecords != 0}">
                                    <c:CCM_ListPageFooter totalRecords="{! v.totalRecords}"
                                        pageCount="{!v.pageCount}" />
                                </aura:renderIf>
                            </div>
                        </div>
                    </div>
                </lightning:tab>
                <lightning:tab label="{!$Label.c.CCM_Portal_CreditMemo}" id="CreditMemo">
                    <div class="slds-text-align_center">
                        <lightning:button variant="brand" label="{!$Label.c.CCM_Portal_Export}" onclick="{!c.handleExportCreditMemo}" />
                    </div>
                    <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                        <div class="slds-wrap">
                            <c:CCM_DataTable columns="{!v.columns1}" data="{!v.currentCreditData}" />
                            <div class="slds-m-top--large slds-m-bottom--large slds-text-align--right">
                                <aura:renderIf isTrue="{! v.totalRecords2 > 0}">
                                    <c:CCM_ListPageFooter totalRecords="{! v.totalRecords2}" pageCount="{!v.pageCount2}"
                                        pageNumber="{!v.pageNumber2}" />
                                </aura:renderIf>
                            </div>
                        </div>
                    </div>
                </lightning:tab>
                <lightning:tab label="{!$Label.c.CCM_Portal_TransactionHistory}" id="TransactionHistory">
                    <div class="slds-text-align_center">
                        <lightning:button variant="brand" label="{!$Label.c.CCM_Portal_Export}" onclick="{!c.handleExportTransactionHistory}" />
                    </div>
                    <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                        <div class="slds-wrap">
                            <c:CCM_DataTable columns="{!v.columns3}" data="{!v.currentPaymentData}" />
                            <div class="slds-m-top--large slds-m-bottom--large slds-text-align--right">
                                <aura:renderIf isTrue="{! v.totalRecords3 > 0}">
                                    <c:CCM_ListPageFooter totalRecords="{!v.totalRecords3}" pageCount="{!v.pageCount3}"
                                        pageNumber="{!v.pageNumber3}" />
                                </aura:renderIf>
                            </div>
                        </div>
                    </div>
                </lightning:tab>
            </lightning:tabset>
        </div>
    </article>
    <c:CCM_Modal size="medium" isShow="{!v.isShowPayment}" title="{!$Label.c.CCM_Portal_TransactionHistoryInvoiceDetail}">
        <div class="slds-grid slds-gutters">
            <div class="slds-col slds-size_3-of-3">
                <div style="overflow-x: auto">
                    <table class="slds-wrap outerTable">
                        <thead>
                            <tr>
                                <td style="text-align: left">
                                    <strong>{!$Label.c.CCM_Portal_Invoice}</strong>
                                </td>
                                <th>{!$Label.c.CCM_Portal_Sequence}</th>
                                <th>{!$Label.c.CCM_Portal_InvoiceAmount}</th>
                                <th>{!$Label.c.CCM_Portal_CashDiscountforEarlyPayment}</th>
                                <th>{!$Label.c.CCM_Portal_TotalNetPayment}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <aura:iteration var="selectInvoiceItem" items="{!v.selectInvoicesList}">
                                <aura:if isTrue="{!selectInvoiceItem.hasNoSequence}">
                                    <tr>
                                        <td style="text-align: left">
                                            {!selectInvoiceItem.invoiceNumber}
                                        </td>
                                        <td></td>
                                        <td>
                                            <lightning:formattedNumber value="{!selectInvoiceItem.originalAmt}"
                                                currencyCode="{!selectInvoiceItem.currencyType}"
                                                currencyDisplayAs="code" style="currency" />
                                        </td>
                                        <td>
                                            <lightning:formattedNumber value="{!selectInvoiceItem.discountFee}"
                                                currencyCode="{!selectInvoiceItem.currencyType}"
                                                currencyDisplayAs="code" style="currency" />
                                        </td>
                                        <td>
                                            <lightning:formattedNumber value="{!selectInvoiceItem.totalPaymentFee}"
                                                currencyCode="{!selectInvoiceItem.currencyType}"
                                                currencyDisplayAs="code" style="currency" />
                                        </td>
                                    </tr>
                                    <aura:set attribute="else">
                                        <tr>
                                            <td style="text-align: left">
                                                {!selectInvoiceItem.invoiceNumber}
                                            </td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        <aura:iteration var="selectAccountingItem"
                                            items="{!selectInvoiceItem.accountingList}" indexVar="sindex">
                                            <tr>
                                                <td></td>
                                                <td>{!$Label.c.CCM_Portal_Sequence} {!sindex+1}</td>
                                                <td>
                                                    <lightning:formattedNumber
                                                        value="{!selectAccountingItem.invoiceAmount}"
                                                        currencyCode="{!selectInvoiceItem.currencyType}"
                                                        currencyDisplayAs="code" style="currency" />
                                                </td>
                                                <td>
                                                    <lightning:formattedNumber
                                                        value="{!selectAccountingItem.discountFee}"
                                                        currencyCode="{!selectInvoiceItem.currencyType}"
                                                        currencyDisplayAs="code" style="currency" />
                                                </td>
                                                <td>
                                                    <lightning:formattedNumber
                                                        value="{!selectAccountingItem.totalPaymentFee}"
                                                        currencyCode="{!selectInvoiceItem.currencyType}"
                                                        currencyDisplayAs="code" style="currency" />
                                                </td>
                                            </tr>
                                        </aura:iteration>
                                    </aura:set>
                                </aura:if>
                            </aura:iteration>
                        </tbody>
                        <tfoot>
                            <tr class="line">
                                <th colspan="2" style="text-align: left">
                                    {!$Label.c.CCM_Portal_Subtotal}
                                </th>
                                <td></td>
                                <td></td>
                                <td>
                                    <lightning:formattedNumber value="{!v.subtotal}" currencyCode="{!v.currencyType}"
                                        currencyDisplayAs="code" style="currency" />
                                </td>
                            </tr>
                            <aura:if isTrue="{!v.isCreditSelect}">
                                <tr>
                                    <td colspan="2">
                                        <table>
                                            <tr>
                                                <th style="text-align: left">
                                                    {!$Label.c.CCM_Portal_CreditMemo}
                                                </th>
                                            </tr>
                                            <aura:iteration var="selectCreditItem" items="{!v.selectCreditList}">
                                                <tr>
                                                    <td style="text-align: left">
                                                        {!selectCreditItem.label}
                                                    </td>
                                                </tr>
                                            </aura:iteration>
                                        </table>
                                    </td>
                                    <td></td>
                                    <td></td>
                                    <td>
                                        <table>
                                            <tr>
                                                <th>{!$Label.c.CCM_Portal_CreditAmount}</th>
                                            </tr>
                                            <aura:iteration var="selectCreditItem" items="{!v.selectCreditList}">
                                                <tr>
                                                    <td>
                                                        <lightning:formattedNumber value="{!selectCreditItem.value}"
                                                            currencyCode="{!v.selectCreditItem.currencyType}"
                                                            currencyDisplayAs="code" style="currency" />
                                                    </td>
                                                </tr>
                                            </aura:iteration>
                                        </table>
                                    </td>
                                </tr>
                            </aura:if>
                            <tr></tr>
                            <aura:if isTrue="{!v.isFirstPay}">
                                <tr>
                                    <th colspan="2" style="text-align: left">
                                        {!$Label.c.CCM_Portal_OneTimeOnlinePaymentPromotion}
                                    </th>
                                    <th></th>
                                    <th></th>
                                    <th>
                                        <lightning:formattedNumber value="-20" currencyCode="{!v.currencyType}"
                                            currencyDisplayAs="code" style="currency" />
                                    </th>
                                </tr>
                            </aura:if>
                            <tr class="line">
                                <th colspan="2" style="text-align: left">
                                    {!$Label.c.CCM_Portal_TotalAmountPaid}
                                </th>
                                <th></th>
                                <th></th>
                                <th>
                                    <lightning:formattedNumber value="{!v.totalPay}" currencyCode="{!v.currencyType}"
                                        currencyDisplayAs="code" style="currency" />
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </c:CCM_Modal>

    <!-- export -->
    <!-- <c:CCM_Modal size="small" isShow="{!v.showExport}" onClose="{!c.doCancelExport}" title="Export Invoice">
        <div class="slds-grid slds-gutters">
            <div class="slds-col slds-size_1-of-3">
                <lightning:combobox name="exportTypeList" label="Export Type" value="{!v.exportType}" options="{!v.exportTypeList}" />
            </div>
            <div class="slds-col slds-size_2-of-3">
            </div>
        </div>
        <div class="slds-grid slds-gutters">
            <div class="slds-col slds-size_9-of-12">
            </div>
            <div class="slds-col slds-size_1-of-12">
                <lightning:button variant="brand" label="Confirm" onclick="{!c.handleExportConfirm}" />
            </div>
            <div class="slds-col slds-size_1-of-12" style="margin-left: 20px">
                <lightning:button label="Cancel" onclick="{!c.doCancelExport}" />
            </div>
        </div>
    </c:CCM_Modal> -->
    <c:ccmExcelDownload aura:id="exportcmp" fileName="{!v.exportFileName}"></c:ccmExcelDownload>
</aura:component>