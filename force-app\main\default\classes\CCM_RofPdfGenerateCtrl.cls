/**
 * @Author: <PERSON><PERSON>
 * @Description: This class to generate [return order form] pdf with the help of VF-page -> CcmReverseOrderReturnFormVF
 */
public without sharing class CCM_RofPdfGenerateCtrl {
    // refer to below apex's method
    // CCM_ReverseOrderInfoCtl.getReverseOrderInfo
    public static String queryStr = 'SELECT Id, ' + 
                                    ' Name,' +
                                    ' ORG_Code__c,' +
                                    ' Customer__r.Name, ' + 
                                    ' Customer__r.AccountNumber, ' + 
                                    ' Billing_Address__c, ' + 
                                    ' Billing_Address__r.Name, ' + 
                                    ' Billing_Address__r.Address1__c, ' + 
                                    ' Billing_Address__r.Address2__c, ' + 
                                    ' Billing_Address__r.Country__c, ' + 
                                    ' Billing_Address__r.State__c, ' + 
                                    ' Billing_Address__r.City__c, ' + 
                                    ' Billing_Address__r.Postal_Code__c, ' +
                                    ' Shipping_Address__c, ' + 
                                    ' Shipping_Address__r.Name, ' + 
                                    ' Shipping_Address__r.Address1__c, ' + 
                                    ' Shipping_Address__r.Address2__c, ' + 
                                    ' Shipping_Address__r.Country__c, ' + 
                                    ' Shipping_Address__r.State__c, ' + 
                                    ' Shipping_Address__r.City__c, ' + 
                                    ' Shipping_Address__r.Postal_Code__c, ' +
                                    ' Additional_Shipping_City__c, ' + 
                                    ' Additional_Shipping_Country__c, ' + 
                                    ' Additional_Shipping_Postal_Code__c, ' + 
                                    ' Additional_Shipping_Province__c, ' + 
                                    ' Additional_Shipping_Street__c, ' + 
                                    ' Additional_Shipping_Street2__c, ' + 
                                    ' Is_Alternative_Address__c, ' + 
                                    ' Order__c, ' + 
                                    ' Reverse_Order_Request_Number__c, ' + 
                                    ' External_Return_Reason__c, ' +
                                    ' Return_Form_Date__c, ' +
                                    ' Original_Customer_PO_Number__c, ' +
                                    ' Reverse_Order_Type__c, ' +
                                    ' (SELECT Order_Number__c FROM Orders__r)' +
                                    ' FROM Reverse_Order_Request__c ' + 
                                    ' WHERE Is_Deleted__c = false';

    public static String sqlItem = 'SELECT Id,'
                                    + 'Product2__r.ProductCode, '
                                    + 'Product2__r.SF_Description__c, '
                                    + 'Brand__c, '
                                    + 'Unit__c, '
                                    + 'Qty__c, '
                                    + 'Order_Item__r.Selling_Warehouse__c, '
                                    + 'Order_Product_Type__c '
                                + 'FROM Reverse_Order_Item__c '
                                + 'WHERE IsDeleted = false ';

    public Reverse_Order_Request__c ROR { get; set; }   // vf page data
    public List<Reverse_Order_Item__c> ROI {get; set;}

    public String strShipFromAddress {get; set;}        // shipping from address
    public String strShipToAddress {get; set;}          // TODO shipping to address
    public String strBillToAddress {get; set;}          // bill to address
    public String strAsnNumber {get; set;}              // astrShipFromAddresssn number
    public String customerPO {get;set;}
    public List<ROIWrapper> warehouseItems {get;set;} // group reverse order item by warehouse

    public String contactEmail {get;set;}

    public CCM_RofPdfGenerateCtrl() {

        String ReverseOrderId = ApexPages.currentPage().getParameters().get('id');
        System.debug('ReverseOrderId = ' + ReverseOrderId);
        this.strShipFromAddress = '';
        this.strBillToAddress = '';

        try {
            // refer to class-> CCM_ReverseOrderInfoCtl
            queryStr += ' AND Id =: ReverseOrderId ';
            sqlItem += ' AND Reverse_Order_Request__c = :ReverseOrderId';

            List<Reverse_Order_Request__c> requestList = (List<Reverse_Order_Request__c>)Database.query(queryStr);
            List<Reverse_Order_Item__c> querywrcItem = (List<Reverse_Order_Item__c>)Database.query(sqlItem);
            this.ROI = new List<Reverse_Order_Item__c>();
            if(querywrcItem.size()>0){
                for(Reverse_Order_Item__c roItem : querywrcItem){
                    if(roItem.Order_Product_Type__c == 'Overage') {
                        this.ROI.add(roItem);
                    }
                }
            }
            if(requestList.size()>0){ // not null to continue
                this.ROR = requestList[0];
                if(this.ROR.ORG_Code__c == CCM_Constants.ORG_CODE_CCA) {
                    this.contactEmail = '<EMAIL>';
                }
                else {
                    this.contactEmail = '<EMAIL>.<NAME_EMAIL>';
                }
                Boolean isDamaged = false;
                if(this.ROR.Reverse_Order_Type__c == 'Damaged in Shipment' && this.ROR.ORG_Code__c != 'CCA') {
                    isDamaged = true;
                }
                 // billingAddress bill to
                 if (String.isNotBlank(requestList[0].Billing_Address__c)) {
                    // street
                    if (String.isNotEmpty(requestList[0].Billing_Address__r.Address1__c)){
                        this.strBillToAddress += requestList[0].Billing_Address__r.Address1__c + ',  ';
                    } else if (String.isNotEmpty(requestList[0].Billing_Address__r.Address2__c)) {
                        this.strBillToAddress += requestList[0].Billing_Address__r.Address2__c + ',  ';
                    }
                    if (String.isNotEmpty(requestList[0].Billing_Address__r.City__c)){
                        this.strBillToAddress += requestList[0].Billing_Address__r.City__c + ',  ';
                    }
                    if (String.isNotEmpty(requestList[0].Billing_Address__r.State__c)){
                        this.strBillToAddress += requestList[0].Billing_Address__r.State__c + ',  ';
                    }
                    if (String.isNotEmpty(requestList[0].Billing_Address__r.Country__c)){
                        this.strBillToAddress += requestList[0].Billing_Address__r.Country__c + ',  ';
                    }
                    if (String.isNotEmpty(requestList[0].Billing_Address__r.Postal_Code__c)){
                        this.strBillToAddress += requestList[0].Billing_Address__r.Postal_Code__c;
                    }
                }
                // shippingAddress ship from
                if (requestList[0].Is_Alternative_Address__c) {
                    if (String.isNotEmpty(requestList[0].Additional_Shipping_Street__c)){
                        this.strShipFromAddress += requestList[0].Additional_Shipping_Street__c + ',  ';
                    } else if (String.isNotEmpty(requestList[0].Additional_Shipping_Street2__c)) {
                        this.strShipFromAddress += requestList[0].Additional_Shipping_Street2__c + ',  ';
                    }

                    if (String.isNotEmpty(requestList[0].Additional_Shipping_City__c)){
                        this.strShipFromAddress += requestList[0].Additional_Shipping_City__c + ',  ';
                    }
                    if (String.isNotEmpty(requestList[0].Additional_Shipping_Province__c)){
                        this.strShipFromAddress += requestList[0].Additional_Shipping_Province__c + ',  ';
                    }
                    if (String.isNotEmpty(requestList[0].Additional_Shipping_Country__c)){
                        this.strShipFromAddress += requestList[0].Additional_Shipping_Country__c + ',  ';
                    }
                    if (String.isNotEmpty(requestList[0].Additional_Shipping_Postal_Code__c)){
                        this.strShipFromAddress += requestList[0].Additional_Shipping_Postal_Code__c;
                    }

                } else if (String.isNotBlank(requestList[0].Shipping_Address__c)) {
                    // street
                    if (String.isNotEmpty(requestList[0].Shipping_Address__r.Address1__c)){
                        this.strShipFromAddress += requestList[0].Shipping_Address__r.Address1__c + ',  ';
                    } else if (String.isNotEmpty(requestList[0].Shipping_Address__r.Address2__c)) {
                        this.strShipFromAddress += requestList[0].Shipping_Address__r.Address2__c + ',  ';
                    }
                    if (String.isNotEmpty(requestList[0].Shipping_Address__r.City__c)){
                        this.strShipFromAddress += requestList[0].Shipping_Address__r.City__c;
                    }
                    if (String.isNotEmpty(requestList[0].Shipping_Address__r.State__c)){
                        this.strShipFromAddress += requestList[0].Shipping_Address__r.State__c + ',  ';
                    }
                    if (String.isNotEmpty(requestList[0].Shipping_Address__r.Country__c)){
                        this.strShipFromAddress += requestList[0].Shipping_Address__r.Country__c + ',  ';
                    }
                    if (String.isNotEmpty(requestList[0].Shipping_Address__r.Postal_Code__c)){
                        this.strShipFromAddress += requestList[0].Shipping_Address__r.Postal_Code__c;
                    }
                }

                String warehouseCodeInOrderItem = '';
                List<Order_Item__c> orderItemList = [SELECT Selling_Warehouse__c FROM Order_Item__c WHERE Order__c = :this.ROR.Order__c AND Selling_Warehouse__c != null];
                if (orderItemList.size() > 0) {
                    for(Order_Item__c item : orderItemList) {
                        warehouseCodeInOrderItem = item.Selling_Warehouse__c;
                    }
                }

                Map<String, String> warehouseCodeAddressMap = new Map<String, String>();
                for(Selling_Warehouse_Configuration__mdt config : [SELECT Id, Selling_Warehouse_Code__c, Address__c FROM Selling_Warehouse_Configuration__mdt]) {
                    warehouseCodeAddressMap.put(config.Selling_Warehouse_Code__c, config.Address__c);
                }
                Map<String, List<Reverse_Order_Item__c>> warehouseROIMap = new Map<String, List<Reverse_Order_Item__c>>();
                String warehouseForNoOrderItem = '';
                for(Reverse_Order_Item__c item : this.ROI) {
                    if(String.isNotBlank(item.Order_Item__c)) {
                        warehouseForNoOrderItem = item.Order_Item__r.Selling_Warehouse__c;
                    }
                }

                for(Reverse_Order_Item__c item : this.ROI) {
                    String warehouseCode = '';
                    if(String.isNotBlank(item.Order_Item__c)) {
                        warehouseCode = item.Order_Item__r.Selling_Warehouse__c;
                    }
                    else {
                        if(String.isNotBlank(warehouseForNoOrderItem)) {
                            warehouseCode = warehouseForNoOrderItem;
                        }
                        else {
                            warehouseCode = warehouseCodeInOrderItem;
                        }
                    }
                    if(isDamaged) {
                        // warehouseCode = 'CNA03';
                        warehouseCode = 'CNA27';
                    }
                    if(warehouseCode.equalsIgnoreCase('CNA01')) {
                        warehouseCode = 'CNA08';
                    }
                    if(warehouseCodeAddressMap.containsKey(warehouseCode)) {
                        String warehouseAddress = warehouseCodeAddressMap.get(warehouseCode);

                        if(!warehouseROIMap.containsKey(warehouseAddress)) {
                            warehouseROIMap.put(warehouseAddress, new List<Reverse_Order_Item__c>());
                        }
                        warehouseROIMap.get(warehouseAddress).add(item);
                    }
                }
                this.warehouseItems = new List<ROIWrapper>();
                for(String warehouse : warehouseROIMap.keySet()) {
                    ROIWrapper wrapper = new ROIWrapper();
                    wrapper.warehouse = warehouse;
                    wrapper.items = warehouseROIMap.get(warehouse);
                    this.warehouseItems.add(wrapper);
                }
                if((    requestList[0].External_Return_Reason__c == 'Damaged in Shipment'
                    ||  requestList[0].External_Return_Reason__c == 'Customer Refusal - No reason provided')

                    &&  (requestList[0].Orders__r.size() > 0)
                    ){
                    this.strAsnNumber = requestList[0].Orders__r[0].Order_Number__c;
                }
                this.customerPO = requestList[0].Original_Customer_PO_Number__c;
            }
            
        } catch (Exception ex) {
            System.debug('error>>' + ex.getLineNumber() + '>>' + ex.getMessage());
        }
    }

    public class ROIWrapper {
        public String warehouse {get;set;}
        public List<Reverse_Order_Item__c> items {get;set;}
    }

    @future(callout = true)
    public static void  generateROFPdf(String strSObjId){
        System.debug('contact.Contact_Email_Address__c' + strSObjId);
        try {
            Reverse_Order_Request__c contact = [SELECT CreatedBy.Name,CreatedBy.Email,Customer_Contact_Email__c FROM Reverse_Order_Request__c WHERE Id = :strSObjId];
            Reverse_Order_Request__c RequestNumber = [SELECT Reverse_Order_Request_Number__c, ORG_Code__c, Original_Customer_PO_Number__c FROM Reverse_Order_Request__c WHERE Id = :strSObjId];
            PageReference pdf = Page.CcmReverseOrderReturnFormVF;
            pdf.getParameters().put('id',strSObjId); 
            pdf.setRedirect(true);
            
            Blob b;
            if(Test.isRunningTest()) { 
              b = blob.valueOf('Unit.Test');
            } else {
              b = pdf.getContent();
            }
            Messaging.EmailFileAttachment efa = new Messaging.EmailFileAttachment();
            efa.setFileName('Return Form - ' + RequestNumber.Reverse_Order_Request_Number__c + '.pdf');
            efa.setBody(b);

            OrgWideEmailAddress[] owea = [select Id from OrgWideEmailAddress where Address = '<EMAIL>'];


            List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();

            // Messaging.SingleEmailMessage createByEmail = generateEmail(contact.CreatedBy.Email, efa, owea.get(0).Id, RequestNumber.Reverse_Order_Request_Number__c, RequestNumber.ORG_Code__c, RequestNumber.Original_Customer_PO_Number__c);
            // if(createByEmail != null) {
            //     emails.add(createByEmail);
            // }
            
            Messaging.SingleEmailMessage contactEmail = generateEmail(contact.Customer_Contact_Email__c, contact.CreatedBy.Email, efa, owea.get(0).Id, RequestNumber.Reverse_Order_Request_Number__c, RequestNumber.ORG_Code__c, RequestNumber.Original_Customer_PO_Number__c);
            if(contactEmail != null) {
                emails.add(contactEmail);
            }

            Messaging.SendEmailResult [] r = Messaging.sendEmail(emails);
        } catch (Exception e){
            System.debug('CCM_RofPdfGenerateCtrl.generateROFPdf>>Err:' + e.getMessage() + e.getLineNumber());
        }
    }

    private static Messaging.SingleEmailMessage generateEmail(String customerContactEmailAddress, String creatorEmailAddress, Messaging.EmailFileAttachment efa, String senderId, String requestNumber, String orgCode, String customerPONumber) {
        Messaging.SingleEmailMessage email = null;
        if(String.isNotBlank(customerContactEmailAddress) || String.isNotBlank(creatorEmailAddress)) {
            email = new Messaging.SingleEmailMessage();
            email.setOrgWideEmailAddressId(senderId);

            List<Messaging.EmailFileAttachment> attachments = new List<Messaging.EmailFileAttachment>();
            attachments.add(efa);
            if(CCM_Constants.ORG_CODE_CCA == orgCode) { 
                email.setSubject('Return Form Has Generated');
            }
            else {
                email.setSubject('RGA PO#' + customerPONumber);
                
                List<Document> versions = [select Id, Body from Document where Name = 'Return Instructions.jpg'];
                if(!versions.isEmpty()) {
                    Messaging.EmailFileAttachment rgaImage = new Messaging.EmailFileAttachment();
                    rgaImage.setFileName('Return Instructions.jpg');
                    rgaImage.setBody(versions[0].Body);
                    attachments.add(rgaImage);
                }
            }
            
            String body = generateEmailContent(orgCode, customerContactEmailAddress, requestNumber);
            email.setHtmlBody(body);

            email.setFileAttachments(attachments);

            Set<String> emails = new Set<String>();
            emails.addAll(customerContactEmailAddress.split(';'));
            emails.add(creatorEmailAddress);

            List<String> emailList = new List<String>();
            emailList.addAll(emails);
            email.setToAddresses(emailList);
        }
        return email;
    }


    private static String generateEmailContent(String orgCode, String emailAddress, String requestNumber) {
        String emailContent = '';
        if(CCM_Constants.ORG_CODE_CCA == orgCode) {
            String line1 = 'Dear ' + emailAddress + ',';
            String line2 = ' Your reverse order request ' + requestNumber + ' is approved,<br>';
            String line3 = ' Return Form is ready, please print from salesforce and include it in your return box,';
            String line4 = ' You can contact Chervon representative to arrange shipment for you.';
            emailContent = line1 + line2 + line3 + line4; 
        }
        else {
            EmailTemplate reverseOrderApprovalTemplateNA = [SELECT Id, HtmlValue FROM EmailTemplate WHERE DeveloperName = 'ReverseOrderApprovalTemplate' LIMIT 1];
            emailContent = reverseOrderApprovalTemplateNA.HtmlValue;
        }
        return emailContent;
    }
}