/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 07-12-2024
 * @last modified by  : <EMAIL>
**/
public without sharing class CCM_ClaimRequestCtl {
    /**
     * @description: get current customer info
     */
    @AuraEnabled
    public static CustomerWrapper getCustomerInfo() {
        CustomerWrapper customer = new CustomerWrapper();
        //if the current user is portal user
        User portalUser = null;
        for (User userObj : [
            SELECT Id, ContactId
            FROM User
            WHERE Id = :UserInfo.getUserId() AND ContactId != NULL
        ]) {
            portalUser = userObj;
        }

        if (portalUser != null) {
            Contact conObj = [
                SELECT id, AccountId, Account.Name
                FROM Contact
                WHERE id = :portalUser.ContactId
            ];
            customer.customerId = conObj.AccountId;
            customer.customerName = conObj.Account.Name;
            customer.promotionCodeList = getPromotionCodeByCustomer(
                customer.customerId
            );
            // update by winfried on 2022.12.21
            // 控制GST/HST、QST两个字段可见性
            Account acc = [
                SELECT Id, OwnerId, ORG_Code__c
                FROM Account
                WHERE Id = :conObj.AccountId
            ];
            customer.isCCA = acc.ORG_Code__c == 'CCA';
        }
        return customer;
    }

    /**
     * @Description: get promotion window by customer id
     */
    @AuraEnabled
    public static List<PromotionWindowWrapper> getPromotionCodeByCustomer(
        String customerId
    ) {
        List<PromotionWindowWrapper> result = new List<PromotionWindowWrapper>();
        if (String.isEmpty(customerId)) {
            return null;
        }
        try {
            Set<Id> promotionIdSet = getPromotionByCustomers(
                new Set<Id>{ customerId }
            );
            Set<String> promotionSet = new Set<String>();
            // get promotion windows
            // 2022-08-01: 在promo期间也能提交claim
            for (Promotion_Window__c pw : [
                SELECT
                    Id,
                    Promotion__r.Promotion_Code_For_External__c,
                    Promotion__r.Name,
                    Start_Date__c,
                    End_Date__c,
                    Claim_End_Date__c,
                    Start_Date_End_Date__c
                FROM Promotion_Window__c
                WHERE
                    Promotion__c IN :promotionIdSet
                    AND Promotion_Window_Status2__c IN (
                        'In Progress',
                        'Claim Period'
                    )
                    AND Promotion__r.RecordType.DeveloperName = 'Sell_Through_Promotion'
                    AND Promotion__r.Promotion_Status__c != 'Draft'
                ORDER BY Claim_End_Date__c DESC
            ]) {
                if (promotionSet.contains(pw.Promotion__c)) {
                    continue;
                }
                promotionSet.add(pw.Promotion__c);
                PromotionWindowWrapper wrapper = new PromotionWindowWrapper();
                wrapper.promotionName = pw.Promotion__r.Name;
                wrapper.promotionCodeForExternal = pw.Promotion__r.Promotion_Code_For_External__c;
                result.add(wrapper);
            }
            return result;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    /**
     * @description Get promotions targetted by current customer
     */
    private static Set<Id> getPromotionByCustomers(Set<Id> customerIds) {
        String recordType = CCM_PromotionUtil.PROMOTION_SELLTHROUGH_RECORDTYPE_APINAME;
        Set<Id> result = new Set<Id>();

        for (Promotion_Target_Customer__c promotionOne : [
            SELECT Promotion__c
            FROM Promotion_Target_Customer__c
            WHERE
                Customer__c IN :customerIds
                AND Promotion__r.RecordType.DeveloperName = :recordType
                AND Promotion__r.Promotion_Status__c != 'Closed'
        ]) {
            result.add(promotionOne.Promotion__c);
        }
        return result;
    }

    /**
     * @description: get promotion info by promotion code
     */
    @AuraEnabled
    public static PromotionInfoWrapper getPromotionInfoByCode(
        String promotionCode,
        String customerId
    ) {
        PromotionInfoWrapper promotionInfo = new PromotionInfoWrapper(
            promotionCode
        );
        Promotion2__c promotionObj = null;
        // 2022-08-01: 在promo期间也能提交claim
        for (Promotion2__c promotion : [
            SELECT
                Id,
                (
                    SELECT Id, Customer__c
                    FROM Promotion_Target_Customer__r
                    WHERE Customer__c = :customerId
                ),
                (
                    SELECT
                        Id,
                        Start_Date__c,
                        End_Date__c,
                        Claim_End_Date__c,
                        Start_Date_End_Date__c
                    FROM Promotion_Windows__r
                    WHERE
                        Promotion_Window_Status2__c IN (
                            'In Progress',
                            'Claim Period'
                        )
                    ORDER BY Claim_End_Date__c DESC
                ),
                Brands__c,
                Promotion_Type__c
            FROM Promotion2__c
            WHERE
                Promotion_Code_For_External__c = :promotionCode
                AND RecordType.DeveloperName = 'Sell_Through_Promotion'
                AND Promotion_Status__c != 'Draft'
        ]) {
            promotionObj = promotion;
        }
        // System.debug('1========='+promotionInfo);
        if (promotionObj == null) {
            return promotionInfo;
        }
        if (promotionObj.Promotion_Windows__r.size() == 0) {
            return promotionInfo;
        }

        // claim end date < today
        // if (
        //     promotionObj.Promotion_Windows__r[0].Claim_End_Date__c <
        //     Date.today()
        // ) {
        //     promotionInfo.claimExpiredMessage = 'Sorry, you cannot select expired Promotion!';
        //     return promotionInfo;
        // }
        //1.it is inner user
        if (promotionObj != null && customerId == null) {
            promotionInfo.promotionId = promotionObj.Id;
            promotionInfo.promotionType = promotionObj.Promotion_Type__c;
            List<Promotion_Target_Customer__c> customerList = [
                SELECT id, Customer__c, Customer__r.Name
                FROM Promotion_Target_Customer__c
                WHERE Promotion__c = :promotionObj.Id
            ];
            List<PromotionCustomer> customerListWrapper = new List<PromotionCustomer>();
            for (Promotion_Target_Customer__c customer : customerList) {
                PromotionCustomer customerWrapper = new PromotionCustomer();
                customerWrapper.customerId = customer.Id;
                customerWrapper.name = customer.Customer__r.Name;
                customerListWrapper.add(customerWrapper);
            }
            promotionInfo.customerList = customerListWrapper;
            return promotionInfo;
        }
        // System.debug('2========='+promotionInfo);
        if (promotionObj.Promotion_Target_Customer__r.size() > 0) {
            promotionInfo.promotionId = promotionObj.Id;
            promotionInfo.promotionType = promotionObj.Promotion_Type__c;
            List<PromotionWindowWrapper> windowList = new List<PromotionWindowWrapper>();
            for (
                Promotion_Window__c window : promotionObj.Promotion_Windows__r
            ) {
                PromotionWindowWrapper wrapper = new PromotionWindowWrapper();
                wrapper.windowId = window.Id;
                wrapper.winStartDate = window.Start_Date__c;
                wrapper.winEndDate = window.End_Date__c;
                wrapper.winStartDateEndDate = window.Start_Date_End_Date__c;
                windowList.add(wrapper);
            }
            promotionInfo.windowList = windowList;
        }
        // System.debug('3========='+promotionInfo);
        //get auth brand by promotion brand
        List<String> brandsList = promotionObj.Brands__c.split(';');
        Map<String,Id> brandMaptoPricebookId = new Map<String,Id>();
        Date dateForToday = Date.today();

        //get auth brands
        List<Sales_Program__c> authBrandsWithAddress = [
            SELECT
                Id,
                Brands__c,
                Price_Book__c,
                RecordType.Name,
                (
                    SELECT
                        Id,
                        Account_Address__c,
                        Account_Address__r.Name,
                        Account_Address__r.Address1__c,
                        Account_Address__r.Country__c,
                        Account_Address__r.State__c,
                        Account_Address__r.City__c
                    FROM Addresses_With_Program__r
                )
            FROM Sales_Program__c
            WHERE
                Brands__c IN :brandsList
                AND Customer__c = :customerId
                AND RecordType.developerName != 'Service'
                AND Approval_Status__c = 'Approved'
                AND RecordTypeId != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_ID
                AND RecordTypeId != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_ID
                AND (Expire_Date__c >=: dateForToday OR Expire_Date__c = null)
        ];
        List<BillToWrapper> billList = new List<BillToWrapper>();
        Set<String> addressSet = new Set<String>();
        for (Sales_Program__c progrom : authBrandsWithAddress) {
            for (
                Address_With_Program__c address : progrom.Addresses_With_Program__r
            ) {
                if (addressSet.contains(address.Account_Address__c)) {
                    continue;
                }
                addressSet.add(address.Account_Address__c);
                BillToWrapper billTo = new BillToWrapper();
                billTo.authBrandAndAddressId = address.Id;
                billTo.name = address.Account_Address__r.Name;
                billTo.address1 = address.Account_Address__r.Address1__c;

                billTo.brand = progrom.Brands__c;
                billTo.city = address.Account_Address__r.City__c;
                billTo.country = address.Account_Address__r.Country__c;
                billTo.state = address.Account_Address__r.State__c;
                billList.add(billTo);
            }
            system.debug(progrom.RecordType.Name);
            if(progrom.RecordType.Name.contains('Dropship')){
                brandMaptoPricebookId.put(progrom.Brands__c, progrom.Price_Book__c);
            }else if(!brandMaptoPricebookId.containsKey(progrom.Brands__c)){
                brandMaptoPricebookId.put(progrom.Brands__c, progrom.Price_Book__c);
            }

        }
        promotionInfo.billList = billList;

        addressSet = new Set<String>();
        List<BillToWrapper> shiptoList = new List<BillToWrapper>();
        // get ship to
        List<Address_With_Program__c> shipToAddressList = [SELECT Id, Account_Address__c, Account_Address__r.Name, Account_Address__r.Address1__c,
                                                                  Account_Address__r.City__c, Account_Address__r.Country__c, Account_Address__r.State__c
                                                           FROM Address_With_Program__c
                                                           WHERE Account_Address__r.RecordTypeId IN (:CCM_Constants.ADDRESS_SHIPPING_ADDRESS_RECORD_TYPE_ID, :CCM_Constants.ADDRESS_DROPSHIP_SHIPPING_ADDRESS_RECORD_TYPE_ID)
                                                           AND Status__c = 'A' AND Account_Address__r.Approval_Status__c = 'Approved' AND Account_Address__r.Customer__c = :customerId];

        for(Address_With_Program__c shipToAddress : shipToAddressList) {
            if(addressSet.contains(shipToAddress.Account_Address__c)) {
                continue;
            }
            addressSet.add(shipToAddress.Account_Address__c);
            BillToWrapper shipTo = new BillToWrapper();
            shipTo.authBrandAndAddressId = shipToAddress.Id;
            shipTo.name = shipToAddress.Account_Address__r.Name;
            
            shipTo.address1 = shipToAddress.Account_Address__r.Address1__c;
            shipTo.city = shipToAddress.Account_Address__r.City__c;
            shipTo.country = shipToAddress.Account_Address__r.Country__c;
            shipTo.state = shipToAddress.Account_Address__r.State__c;
            shiptoList.add(shipTo);
        }
        promotionInfo.shiptoList = shiptoList;
        //load threshold and offering products
        promotionInfo.thresholdProducts = new List<ClaimProduct>();
        promotionInfo.offeringProducts = new List<ClaimProduct>();

        // get all products
        List<Promotion_Product__c> allProducts = [
            SELECT
                Id,
                PMAPP__c,
                Chervon_Funding__c,
                Product__c,
                Product__r.Name,
                // add haibo: product french
                Product__r.Product_Name_French__c,
                Product__r.ProductCode,
                Product__r.Brand_Name__c,
                Promotion2_Threshold__c,
                Promotion2_Offering__c,
                Promotion2_Offering__r.Promotion_Rule__c,
                Promotion2_Offering__r.Promotion_Rule__r.Promotion__r.Promotion_Type__c
            FROM Promotion_Product__c
            WHERE
                Promotion2_Offering__r.Promotion_Rule__r.Promotion__c = :promotionObj.Id
                OR Promotion2_Threshold__r.Promotion_Rule__r.Promotion__c = :promotionObj.Id
        ];
        Set<Id> productSet = new Set<Id>();
        for (Promotion_Product__c productObj : allProducts) {
            productSet.add(productObj.Product__c);
        }

        Map<Id,Decimal> productMapToPrice = new Map<Id,Decimal>();
        if(allProducts.size() > 0){
            if(allProducts[0].Product__r != null){
                if(brandMaptoPricebookId.containsKey(allProducts[0].Product__r.Brand_Name__c)){
                    String pricebookId = brandMaptoPricebookId.get(allProducts[0].Product__r.Brand_Name__c);
                    for(PricebookEntry pbe : [SELECT id,UnitPrice,Product2Id FROM PricebookEntry WHERE Pricebook2Id =: pricebookId AND Product2Id IN: productSet]){
                        productMapToPrice.put(pbe.Product2Id, pbe.UnitPrice);
                    }
                }
            }
        }
        // Map<Id, List<PricebookEntry>> productMSRPMap = getMsrpPriceMap(productSet);
        Map<Id, List<Promotion_Performance__c>> productMSRPMap = getMsrpPriceMap(
            promotionObj.Id,
            productSet
        );
        Map<String, Integer> offeringProductMap = new Map<String, Integer>();
        Map<String, Integer> thresholdProductMap = new Map<String, Integer>();
        for (Promotion_Product__c productObj : allProducts) {
            offeringProductMap.put(productObj.Product__c, 0);
            thresholdProductMap.put(productObj.Product__c, 0);
        }
        for (Promotion_Product__c productObj : allProducts) {
            ClaimProduct claimProductObj = new ClaimProduct();
            claimProductObj.chervonFundedAmount = productObj.Chervon_Funding__c;
            claimProductObj.productId = productObj.Product__c;
            claimProductObj.productName = productObj.Product__r.Name;
            claimProductObj.brand = productObj.Product__r.Brand_Name__c;
            claimProductObj.productCode = productObj.Product__r.ProductCode;
            claimProductObj.sellInPrice = productMapToPrice.get(productObj.Product__c);
            if (productMSRPMap.containsKey(productObj.Product__c)) {
                // claimProductObj.MSRP = productMSRPMap.get(productObj.Product__c).get(0).UnitPrice;
                claimProductObj.MSRP = productMSRPMap.get(productObj.Product__c)
                    .get(0)
                    .MSRP__c;
                // update 2021-07-07
                claimProductObj.windowMSRP = new List<windowPrice>();
                for (
                    Promotion_Performance__c pp : productMSRPMap.get(
                        productObj.Product__c
                    )
                ) {
                    windowPrice wp = new windowPrice();
                    wp.windowId = pp.Promotion_Window__c;
                    wp.MSRP = pp.MSRP__c;
                    claimProductObj.windowMSRP.add(wp);
                }
            } else {
                claimProductObj.MSRP = 0;
                claimProductObj.windowMSRP = new List<windowPrice>();
            }
            //2.return product brand
            claimProductObj.promoPrice = productObj.PMAPP__c;
            if (productObj.Promotion2_Offering__c != null) {
                claimProductObj.type = 'Offering';
                //avoid duplicate product add to list
                claimProductObj.ruleId = productObj.Promotion2_Offering__r.Promotion_Rule__c;
                if (productObj.Promotion2_Offering__r.Promotion_Rule__r.Promotion__r.Promotion_Type__c == 'Whole Order Promo') {
                    promotionInfo.offeringProducts.add(claimProductObj);
                }else {
                    if (offeringProductMap.get(productObj.Product__c) == 0) {
                        promotionInfo.offeringProducts.add(claimProductObj);
                    }
                    offeringProductMap.put(productObj.Product__c, 1);
                }
            } else {
                claimProductObj.type = 'Threshold';
                Map<String, Integer> productMap = new Map<String, Integer>();
                if (thresholdProductMap.get(productObj.Product__c) == 0) {
                    promotionInfo.thresholdProducts.add(claimProductObj);
                }
                thresholdProductMap.put(productObj.Product__c, 1);
            }
        }

        promotionInfo.promotionRules = getPromotionRulesByPromotionId(
            (String)promotionObj.Id
        );

        return promotionInfo;
    }

    //ADD BY AUSTIN,whole order product product，获取值
    @AuraEnabled
    public static PromotionInfoWrapper getWholeOrderPromtionInfo(String PromotionId, String productName){
        List<Promotion2__c> promtionList = [SELECT Id, Promo_Code__c, Brands__c, Promotion_Type__c FROM Promotion2__c WHERE Id =: PromotionId];
        List<String> brandsList = promtionList[0].Brands__c.split(';');
        PromotionInfoWrapper promotionInfo = new PromotionInfoWrapper(promtionList[0].Promo_Code__c);
        //(1)product 取值
        String keyWords = '%'+productName+'%';
        System.debug('keyWords------'+keyWords);
        // add haibo: french
        List<Product2> productList = [SELECT Id, Name, Product_Name_French__c, ProductCode,  Brand_Name__c
                        FROM Product2 WHERE Brand_Name__c =: brandsList
                        AND Source__c = 'PIM'
                        AND RecordType.DeveloperName = 'product'
                        AND (Name LIKE: keyWords OR ProductCode LIKE: keyWords)
                        AND Is_History_Product__c = :CCM_Constants.blHistoryProduct limit 100];
        System.debug('productList----'+productList);
        promotionInfo.thresholdProducts = new List<ClaimProduct>();
        if (productList.size() > 0) {
            Set<Id> productSet = new Set<Id>();
            for (Product2 productObj : productList) {
                productSet.add(productObj.Id);
            }
            //product info
            List<User> user =  [SELECT id, Contact.Account.ORG_Code__c FROM User WHERE Id =: UserInfo.getUserId()];
            Boolean isCCA = false;
            if (user[0].Contact.Account.ORG_Code__c == 'CCA') {
                isCCA = true;
            }
            Map<Id, List<PricebookEntry>> productMSRPMap = getMSRPPriceBookMap(productSet, isCCA);
            for (Product2 productObj : productList) {
                ClaimProduct claimProductObj = new ClaimProduct();
                claimProductObj.productId = productObj.Id;
                // add haibo: product french
                if(UserInfo.getLanguage() == Label.CCM_Portal_French){
                    claimProductObj.productName = productObj.Product_Name_French__c;
                }else {
                    claimProductObj.productName = productObj.Name;
                }
                claimProductObj.brand = productObj.Brand_Name__c;
                claimProductObj.productCode = productObj.ProductCode;
                claimProductObj.promotionId = PromotionId;
                if (productMSRPMap.containsKey(productObj.Id)) {
                    // claimProductObj.MSRP = productMSRPMap.get(productObj.Product__c).get(0).UnitPrice;
                    claimProductObj.MSRP = productMSRPMap.get(productObj.Id).get(0).UnitPrice;
                    claimProductObj.windowMSRP = new List<windowPrice>();
                    for (PricebookEntry pp : productMSRPMap.get(productObj.Id)) {
                        windowPrice wp = new windowPrice();
                        wp.windowId = pp.Product2Id;
                        wp.MSRP = pp.UnitPrice;
                        claimProductObj.windowMSRP.add(wp);
                    }
                } else {
                    claimProductObj.MSRP = 0;
                    claimProductObj.windowMSRP = new List<windowPrice>();
                }
                promotionInfo.thresholdProducts.add(claimProductObj);
            }
        }
        System.debug('promotionInfo===='+promotionInfo);
        return promotionInfo;
    }



    /**
     * @description: get Reimbursement Type
     */
    @AuraEnabled
    public static List<CCM_PromotionUtil.PromotionPickWrapper> getReimbursementType() {
        Schema.DescribeFieldResult fieldDescribe = Claim_Request__c.Reimbursement_Type__c.getDescribe();
        List<CCM_PromotionUtil.PromotionPickWrapper> promoTypeList = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        promoTypeList = CCM_PromotionUtil.getPickListMap(
            fieldDescribe,
            promoTypeList
        );
        return promoTypeList;
    }

    /**
     * @description: get products by key word and promotion code
     */
    @AuraEnabled
    public static List<ClaimProduct> getClaimProducts(
        String brand,
        String keyWord,
        String promotionId,
        String flag
    ) {
        Map<String, String> brandMap = new Map<String, String>{
            'EGO' => 'EGO',
            'SKIL' => 'Skil',
            'SKILSAW' => 'SkilSaw',
            'FLEX' => 'Flex'
        };
        List<ClaimProduct> claimProductList = new List<ClaimProduct>();
        if (flag == 'Offering') {
            String queryStr = 'Select Id,PMAPP__c,Chervon_Funding__c,Product__c,Product__r.Name, Product__r.ProductCode From ';
            queryStr += ' Promotion_Product__c WHERE Promotion2_Offering__c!=null  ';
            queryStr += ' And Product__c!=null ';
            queryStr +=
                ' And Promotion2_Offering__r.Promotion_Rule__r.Promotion__c=\'' +
                promotionId +
                '\' ';
            //1.remove brand
            //queryStr += ' And Product__r.Brand_Name__c=\''+brandMap.get(brand)+'\'';
            if (keyWord != null) {
                queryStr +=
                    ' And Product__r.ProductCode like \'%' +
                    keyWord +
                    '%\' ';
            } else {
                queryStr += ' limit 10';
            }
            List<Promotion_Product__c> offeringPromotionProducts = (List<Promotion_Product__c>) Database.query(
                queryStr
            );
            Set<Id> productSet = new Set<Id>();
            for (Promotion_Product__c productObj : offeringPromotionProducts) {
                productSet.add(productObj.Product__c);
            }
            // Map<Id, List<PricebookEntry>> productMSRPMap = getMsrpPriceMap(productSet);
            Map<Id, List<Promotion_Performance__c>> productMSRPMap = getMsrpPriceMap(
                promotionId,
                productSet
            );
            for (Promotion_Product__c productObj : offeringPromotionProducts) {
                ClaimProduct claimProductObj = new ClaimProduct();
                claimProductObj.chervonFundedAmount = productObj.Chervon_Funding__c;
                claimProductObj.productId = productObj.Product__c;
                claimProductObj.productName = productObj.Product__r.Name;
                claimProductObj.productCode = productObj.Product__r.ProductCode;
                //2.return product brand
                claimProductObj.promoPrice = productObj.PMAPP__c;
                claimProductObj.type = 'Offering';
                if (productMSRPMap.containsKey(productObj.Product__c)) {
                    // claimProductObj.MSRP = productMSRPMap.get(productObj.Product__c).get(0).UnitPrice;
                    claimProductObj.MSRP = productMSRPMap.get(
                            productObj.Product__c
                        )
                        .get(0)
                        .MSRP__c;
                } else {
                    claimProductObj.MSRP = 0;
                }
                claimProductList.add(claimProductObj);
            }
        } else {
            String queryStr = 'Select Id,PMAPP__c,Chervon_Funding__c,Product__c,Product__r.Name, Product__r.ProductCode From ';
            queryStr += ' Promotion_Product__c WHERE Promotion2_Offering__c!=null  ';
            queryStr += ' And Product__c!=null ';
            queryStr +=
                ' And Promotion2_Offering__r.Promotion_Rule__r.Promotion__c=\'' +
                promotionId +
                '\' ';
            //1.remove brand
            //queryStr += ' And Product__r.Brand_Name__c=\''+brandMap.get(brand)+'\'';
            if (keyWord != null) {
                queryStr +=
                    ' And Product__r.ProductCode like \'%' +
                    keyWord +
                    '%\' ';
            } else {
                queryStr += ' limit 10';
            }
            List<Promotion_Product__c> offeringPromotionProducts = (List<Promotion_Product__c>) Database.query(
                queryStr
            );
            Set<Id> productSet = new Set<Id>();
            for (Promotion_Product__c productObj : offeringPromotionProducts) {
                productSet.add(productObj.Product__c);
            }
            // Map<Id, List<PricebookEntry>> productMSRPMap = getMsrpPriceMap(productSet);
            Map<Id, List<Promotion_Performance__c>> productMSRPMap = getMsrpPriceMap(
                promotionId,
                productSet
            );
            for (Promotion_Product__c productObj : offeringPromotionProducts) {
                ClaimProduct claimProductObj = new ClaimProduct();
                claimProductObj.chervonFundedAmount = productObj.Chervon_Funding__c;
                claimProductObj.productId = productObj.Product__c;
                claimProductObj.productName = productObj.Product__r.Name;
                claimProductObj.productCode = productObj.Product__r.ProductCode;
                //2.return product brand
                claimProductObj.promoPrice = productObj.PMAPP__c;
                claimProductObj.type = 'Offering';
                if (productMSRPMap.containsKey(productObj.Product__c)) {
                    // claimProductObj.MSRP = productMSRPMap.get(productObj.Product__c).get(0).UnitPrice;
                    claimProductObj.MSRP = productMSRPMap.get(
                            productObj.Product__c
                        )
                        .get(0)
                        .MSRP__c;
                } else {
                    claimProductObj.MSRP = 0;
                }
                claimProductList.add(claimProductObj);
            }
        }
        return claimProductList;
    }

    /**
     * @description: get msrp form MSRP price book by products
     */
    @TestVisible
    private static Map<Id, List<PricebookEntry>> getMsrpPriceMap(
        Set<Id> productIds
    ) {
        //CNA-EGO-MSRP, CNA-FLEX-MSRP, CNA-SKIL-MSRP
        List<PricebookEntry> msrpList = [
            SELECT Id, Name, Product2Id, UnitPrice, Pricebook2.Name
            FROM PricebookEntry
            WHERE
                Pricebook2Id IN (
                    SELECT Id
                    FROM Pricebook2
                    WHERE
                        Price_Book_OracleID__c IN (
                            :Label.CCM_Price_Book_Oracle_ID_CNA_EGO_MSRP,
                            :Label.CCM_Price_Book_Oracle_ID_CNA_SKIL_MSRP,
                            :Label.CCM_Price_Book_Oracle_ID_CNA_FLEX_MSRP
                        )
                )
                AND Product2Id IN :productIds
            ORDER BY Product2Id, Id
        ];
        Map<Id, List<PricebookEntry>> msrpMap = new Map<Id, List<PricebookEntry>>();
        for (PricebookEntry entry : msrpList) {
            if (!msrpMap.containsKey(entry.Product2Id)) {
                List<PricebookEntry> tmpList = new List<PricebookEntry>();
                msrpMap.put(entry.Product2Id, tmpList);
            }
            msrpMap.get(entry.Product2Id).add(entry);
        }
        return msrpMap;
    }

    /**
     * @description: get MSRP from promotion performance
     */
    private static Map<Id, List<Promotion_Performance__c>> getMSRPPriceMap(
        String promotionId,
        Set<Id> productIds
    ) {
        List<Promotion_Performance__c> performanceList = [
            SELECT Id, Product__c, MSRP__c, Promotion_Window__c
            FROM Promotion_Performance__c
            WHERE
                Promotion_Window__r.Promotion__c = :promotionId
                AND Product__c IN :productIds
        ];
        Map<Id, List<Promotion_Performance__c>> msrpMap = new Map<Id, List<Promotion_Performance__c>>();
        for (Promotion_Performance__c entry : performanceList) {
            if (!msrpMap.containsKey(entry.Product__c)) {
                List<Promotion_Performance__c> tmpList = new List<Promotion_Performance__c>();
                msrpMap.put(entry.Product__c, tmpList);
            }
            msrpMap.get(entry.Product__c).add(entry);
        }
        return msrpMap;
    }

    /**
     * @description: get MSRP from  price book, add by austin
     */
    private static Map<Id, List<PricebookEntry>> getMSRPPriceBookMap(Set<Id> productId, Boolean isCCA){
        Map<Id, List<PricebookEntry>> result  = new Map<Id, List<PricebookEntry>>();
        String[] CAPriceBook = new String[]{'CA MSRP Price List'};
        String[] CNAPriceBook = new String[]{'CNA-EGO-MSRP','CNA-FLEX-MSRP', 'CNA-MSRP for FLEX&SKIL Parts', 'CNA-MSRP for Parts', 'CNA-SKIL-MSRP'};
        String str = 'SELECT id, Product2Id, UnitPrice, Pricebook2.name, Pricebook2Id FROM PricebookEntry WHERE Product2Id =: productId';
        if (isCCA) {
            str += ' AND Pricebook2.name =: CAPriceBook';
        }else{
            str += ' AND Pricebook2.name =: CNAPriceBook';
        }
        System.debug('str-------'+str);
        List<PricebookEntry> priceBookEntryList = Database.query(str);
        if (priceBookEntryList.size() > 0) {
            for (PricebookEntry price : priceBookEntryList) {
                if (!result.containsKey(price.Product2Id)) {
                    result.put(price.Product2Id, new List<PricebookEntry>());
                }
                result.get(price.Product2Id).add(price);
            }
        }
        return result;
    }


    /**
     * @description: get claim request info
     */
    @AuraEnabled
    public static ClaimRequestWrapper getClaimRequestWrapper(String recordId) {
        ClaimRequestWrapper requestWrapper = new ClaimRequestWrapper();
        //query claim request
        Claim_Request__c claimRequest = [
            SELECT
                id,
                Billing_Address_With_Authorized_Brand__c,
                Billing_Address_With_Authorized_Brand__r.Program__r.Brands__c,
                Ship_To__c,
                Claim_Description__c,
                Claim_Status__c,
                // add haibo: french
                tolabel(Claim_Status__c) claimStatusLabel,
                Customer__c,
                Customer__r.Name,
                Customer__r.ORG_Code__c,
                (
                    SELECT
                        Id,
                        Chervon_Funded_Amount__c,
                        Claim_Amount__c,
                        Model__c,
                        MSRP__c,
                        Model__r.Name,
                        Model__r.Brand_Name__c,
                        Model__r.ProductCode,
                        Promo_Price__c,
                        Quantity__c,
                        Sale_Price__c,
                        Total_Sales__c,
                        Type__c
                    FROM Claim_Request_Items__r
                ) Promotion_Code__c,
                Promotion_Code__r.Name,
                // add haibo: french
                Promotion_Code__r.Promotion_Name_French__c,
                Promotion_Code__r.Promo_Code__c,
                Promotion_Code__r.Promotion_Code_For_External__c,
                Promotion_Window__c,
                Reimbursement_Type__c,
                Total_Claim_Amount__c,
                Reject_Reason__c,
                FLEX_Total__c,
                EGO_Total__c,
                SKIL_Total__c,
                SKILSAW_Total__c,
                GSTOrHST_Currency__c,
                QST_Currency__c,
                Total_Claim_Amount_Tax_Inclusive__c
            FROM Claim_Request__c
            WHERE Id = :recordId
            //AND Promotion_Window__r.Promotion_Window_Status2__c = ''
        ];
        requestWrapper.recordId = claimRequest.id;
        requestWrapper.claimDescription = claimRequest.Claim_Description__c;
        requestWrapper.customerId = claimRequest.Customer__c;
        requestWrapper.customerName = claimRequest.Customer__r.Name;
        requestWrapper.customerOrgCode = claimRequest.Customer__r.ORG_Code__c;
        // add haibo: product french
        if(UserInfo.getLanguage() == Label.CCM_Portal_French){
            requestWrapper.promotionName = claimRequest.Promotion_Code__r.Promotion_Name_French__c == null ? claimRequest.Promotion_Code__r.Name : claimRequest.Promotion_Code__r.Promotion_Name_French__c;
        }else {
            requestWrapper.promotionName = claimRequest.Promotion_Code__r.Name;
        }
        requestWrapper.promotionName = claimRequest.Promotion_Code__r.Name;
        requestWrapper.promotionCode = claimRequest.Promotion_Code__r.Promotion_Code_For_External__c;
        requestWrapper.promotionId = claimRequest.Promotion_Code__c;
        requestWrapper.promotionWindowId = claimRequest.Promotion_Window__c;
        requestWrapper.reimbursementType = claimRequest.Reimbursement_Type__c;
        requestWrapper.totalClaimAmount = claimRequest.Total_Claim_Amount__c;
        requestWrapper.address = claimRequest.Billing_Address_With_Authorized_Brand__c;
        requestWrapper.shiptoAddress = claimRequest.Ship_To__c;
        // add haibo: french
        requestWrapper.statusText = String.valueOf(claimRequest.get('claimStatusLabel'));
        requestWrapper.status = claimRequest.Claim_Status__c;
        requestWrapper.rejectReason = claimRequest.Reject_Reason__c;
        // update by winfried on 2022.10.21
        // 为CCA用户添加GST/HST、QST两个字段
        requestWrapper.gstOrHSTCurrency = claimRequest.GSTOrHST_Currency__c;
        requestWrapper.qstCurrency = claimRequest.QST_Currency__c;
        requestWrapper.totalClaimAmountInclusive = claimRequest.Total_Claim_Amount_Tax_Inclusive__c;
        //get promotion basic info wrapper
        PromotionInfoWrapper promotioninfoWrapperObj = getPromotionInfoByCode(
            claimRequest.Promotion_Code__r.Promotion_Code_For_External__c,
            claimRequest.Customer__c
        );
        requestWrapper.promotionBasicInfo = promotioninfoWrapperObj;
        requestWrapper.invoiceList = [SELECT Id,
                                             Name,
                                             CurrencyIsoCode,
                                             Invoice_Date__c,
                                             Total_Amount__c
                                        FROM Invoice__c
                                        WHERE Promotion_Claim_Request__c=:claimRequest.id];
        //get offering/threshold claim request item
        List<ClaimProduct> offeringClaimItems = new List<ClaimProduct>();
        List<ClaimProduct> thresholdClaimItems = new List<ClaimProduct>();

        // add by John Jiang for add sell in price
        Map<String,Id> brandMaptoPricebookId = new Map<String,Id>();
        Date dateForToday = Date.today();

        List<Address_With_Program__c> awpList = [SELECT Program__r.Brands__c,Program__r.Price_Book__c,Program__r.RecordType.Name FROM Address_With_Program__c
                                                    WHERE Program__r.Customer__c =: claimRequest.Customer__c
                                                    AND Program__r.Approval_Status__c = 'Approved'
                                                    AND Program__r.RecordTypeId IN: CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SALES_IDS
                                                    AND Program__r.Brands__c =: claimRequest.Billing_Address_With_Authorized_Brand__r.Program__r.Brands__c
                                                    AND (Program__r.Expire_Date__c >=: dateForToday OR Program__r.Expire_Date__c = null)];
        if(awpList.size() > 0){
            for(Address_With_Program__c awp : awpList){
                if(awp.Program__r.RecordType.Name.contains('Dropship')){
                    brandMaptoPricebookId.put(awp.Program__r.Brands__c, awp.Program__r.Price_Book__c);
                }else if(!brandMaptoPricebookId.containsKey(awp.Program__r.Brands__c)){
                    brandMaptoPricebookId.put(awp.Program__r.Brands__c, awp.Program__r.Price_Book__c);
                }
            }

        }
        Map<Id,Decimal> productMapToPrice = new Map<Id,Decimal>();
        Set<Id> productSet = new Set<Id>();
        List<Claim_Request_Item__c> allProducts = claimRequest.Claim_Request_Items__r;
        for (Claim_Request_Item__c item : allProducts) {
            productSet.add(item.Model__c);
        }
        if(allProducts.size() > 0){
            if(brandMaptoPricebookId.containsKey(allProducts[0].Model__r.Brand_Name__c)){
                String pricebookId = brandMaptoPricebookId.get(allProducts[0].Model__r.Brand_Name__c);
                for(PricebookEntry pbe : [SELECT id,UnitPrice,Product2Id FROM PricebookEntry WHERE Pricebook2Id =: pricebookId AND Product2Id IN: productSet]){
                    productMapToPrice.put(pbe.Product2Id, pbe.UnitPrice);

                }
            }
        }


        for (Claim_Request_Item__c item : claimRequest.Claim_Request_Items__r) {
            ClaimProduct claimItem = new ClaimProduct();
            claimItem.chervonFundedAmount = item.Chervon_Funded_Amount__c;
            claimItem.claimAmount = item.Claim_Amount__c;
            claimItem.MSRP = item.MSRP__c;
            claimItem.productId = item.Model__c;
            claimItem.productName = item.Model__r.Name;
            claimItem.brand = item.Model__r.Brand_Name__c;
            claimItem.promoPrice = item.Promo_Price__c;
            claimItem.quantity = item.Quantity__c;
            claimItem.productCode = item.Model__r.ProductCode;
            claimItem.recordId = item.Id;
            claimItem.salePrice = item.Sale_Price__c;
            claimItem.sellInPrice = productMapToPrice.get(claimItem.productId);
            claimItem.totalSales = item.Total_Sales__c;
            claimItem.type = item.Type__c;
            if (item.Type__c == 'Offering') {
                offeringClaimItems.add(claimItem);
            } else {
                thresholdClaimItems.add(claimItem);
            }
        }
        requestWrapper.offeringClaimItems = offeringClaimItems;
        requestWrapper.thresholdClaimItems = thresholdClaimItems;
        //populate summary amount
        requestWrapper.offeringSummaryList = new List<OfferingSummary>();
        if (
            claimRequest.EGO_Total__c != null &&
            claimRequest.EGO_Total__c != 0
        ) {
            OfferingSummary flex = new OfferingSummary(
                'EGO Total Claim Amount',
                claimRequest.EGO_Total__c
            );
            requestWrapper.offeringSummaryList.add(flex);
        }
        if (
            claimRequest.SKIL_Total__c != null &&
            claimRequest.SKIL_Total__c != 0
        ) {
            OfferingSummary flex = new OfferingSummary(
                'Skil Total Claim Amount',
                claimRequest.SKIL_Total__c
            );
            requestWrapper.offeringSummaryList.add(flex);
        }
        if (
            claimRequest.SKILSAW_Total__c != null &&
            claimRequest.SKILSAW_Total__c != 0
        ) {
            OfferingSummary flex = new OfferingSummary(
                'SkilSaw Total Claim Amount',
                claimRequest.SKILSAW_Total__c
            );
            requestWrapper.offeringSummaryList.add(flex);
        }
        if (
            claimRequest.FLEX_Total__c != null &&
            claimRequest.FLEX_Total__c != 0
        ) {
            OfferingSummary flex = new OfferingSummary(
                'Flex Total Claim Amount',
                claimRequest.FLEX_Total__c
            );
            requestWrapper.offeringSummaryList.add(flex);
        }

        requestWrapper.promotionRules = getPromotionRulesByPromotionId((String)claimRequest.Promotion_Code__c);

        return requestWrapper;
    }

    /**
     * @description: save claim request
     */
    @AuraEnabled
    public static ClaimRequestWrapper saveClaimRequest(String claimRequestStr) {
        ClaimRequestWrapper claimRequest = (ClaimRequestWrapper) JSON.deserialize(
            claimRequestStr,
            ClaimRequestWrapper.class
        );

        // 25.3.17 Check claim end date
        if(checkClaimEndDateExpired(claimRequest.promotionWindowId, null)){
            claimRequest.isClaimDateExpired = true;
            return claimRequest;
        }
        // 25.3.17 end

        Claim_Request__c claimRequestObj = new Claim_Request__c();
        Boolean promotionCodeChanged = false;

        // delete claim request item if promotion code changed when edit
        if (claimRequest.recordId != null) {
            claimRequestObj.Id = claimRequest.recordId;
            // promotionCodeChanged = [
            //         SELECT Promotion_Code__c
            //         FROM Claim_Request__c
            //         WHERE Id = :claimRequest.recordId
            //     ]
            //     .Promotion_Code__c != claimRequest.promotionId
            //     ? true
            //     : false;
            // if (promotionCodeChanged) {
            //     delete [
            //         SELECT Id
            //         FROM Claim_Request_Item__c
            //         WHERE Claim_Request__c = :claimRequest.recordId
            //     ];
            // }
            delete [
                SELECT Id
                FROM Claim_Request_Item__c
                WHERE Claim_Request__c = :claimRequest.recordId
            ];
        }
        claimRequestObj.Billing_Address_With_Authorized_Brand__c = claimRequest.address;
        claimRequestObj.Ship_To__c = claimRequest.shiptoAddress;
        claimRequestObj.Claim_Description__c = claimRequest.claimDescription;
        claimRequestObj.Customer__c = claimRequest.customerId;
        claimRequestObj.Promotion_Code__c = claimRequest.promotionId;
        claimRequestObj.Promotion_Window__c = claimRequest.promotionWindowId;
        claimRequestObj.Claim_Status__c = 'Draft';
        claimRequestObj.Reimbursement_Type__c = claimRequest.reimbursementType;
        // update by winfried on 2022.10.21
        // 为CCA用户添加GST/HST、QST两个字段
        claimRequestObj.GSTOrHST_Currency__c = claimRequest.gstOrHSTCurrency;
        claimRequestObj.QST_Currency__c = claimRequest.qstCurrency;
        claimRequestObj.Total_Claim_Amount_Tax_Inclusive__c = claimRequest.totalClaimAmountInclusive;
        claimRequestObj.CurrencyIsoCode =string.isEmpty(claimRequest.currencyCode)? 'USD':claimRequest.currencyCode;

        upsert claimRequestObj;
        claimRequest.recordId = claimRequestObj.Id;
        List<Claim_Request_Item__c> upsertItems = new List<Claim_Request_Item__c>();
        List<ClaimProduct> offerAndThresholdClaimRequestItems = new List<ClaimProduct>();
        if (
            claimRequest.offeringClaimItems != null &&
            claimRequest.offeringClaimItems.size() > 0
        ) {
            offerAndThresholdClaimRequestItems.addAll(
                claimRequest.offeringClaimItems
            );
        }
        if (
            claimRequest.thresholdClaimItems != null &&
            claimRequest.thresholdClaimItems.size() > 0
        ) {
            offerAndThresholdClaimRequestItems.addAll(
                claimRequest.thresholdClaimItems
            );
        }
        //计算精度
        // upsert claim request item
        Boolean hasFillClaimAmountForBMSM = false;
        for (ClaimProduct claimItem : offerAndThresholdClaimRequestItems) {
            Claim_Request_Item__c item = new Claim_Request_Item__c();
            // if (claimItem.recordId != null) {
            //     item.id = claimItem.recordId;
            // }
            item.Chervon_Funded_Amount__c = claimItem.chervonFundedAmount;
            item.Claim_Request__c = claimRequestObj.Id;
            item.Model__c = claimItem.productId;
            item.MSRP__c = claimItem.MSRP;
            item.Brand__c = claimItem.brand;
            item.Promo_Price__c = claimItem.promoPrice;
            item.Quantity__c = claimItem.quantity;
            item.Sale_Price__c = claimItem.salePrice;
            item.Type__c = claimItem.type;
            item.CurrencyIsoCode = string.isEmpty(claimRequest.currencyCode)? 'USD':claimRequest.currencyCode;
            if (claimItem.stWholeOrder && claimItem.type == 'Threshold') {
                item.Chervon_Funded_Amount__c = claimItem.wholeOrderFunding;
                item.Claim_Amount2__c = claimItem.scaleAmount;
            } else if (claimItem.isBMSM && hasFillClaimAmountForBMSM == false) {
                item.Claim_Amount2__c = claimRequest.totalClaimAmountInclusive - claimRequest.gstOrHSTCurrency - claimRequest.qstCurrency;
                hasFillClaimAmountForBMSM = true;
            } else {
                item.Claim_Amount2__c = claimItem.chervonFundedAmount * claimItem.quantity;
            }
            claimItem.itemObj = item;
            upsertItems.add(item);
        }
        upsert upsertItems;
        // if(
        //     claimRequest.offeringClaimItems.size() == 0
        // ) {
        //     List<Claim_Request_Item__c> deleteItems = [SELECT id FROM Claim_Request_Item__c WHERE Claim_Request__c =: claimRequestObj.Id AND Type__c = 'Offering'];
        //     delete deleteItems;
        // }

        // if (
        //     claimRequest.deleteClaimItemIds != null &&
        //     claimRequest.deleteClaimItemIds.size() > 0
        // ) {
        //     delete [
        //         SELECT Id
        //         FROM Claim_Request_Item__c
        //         WHERE id IN :claimRequest.deleteClaimItemIds
        //     ];
        // }

        for (ClaimProduct claimItem : offerAndThresholdClaimRequestItems) {
            claimItem.recordId = claimItem.itemObj.id;
        }
        return getClaimRequestWrapper(claimRequestObj.Id);
    }

    /**
     * @description: submit approve
     */
    @AuraEnabled
    public static ClaimRequestWrapper submitClaimReqeust(String claimId) {
        // 25.3.17 Check claim end date
        if(checkClaimEndDateExpired(null, claimId)){
            ClaimRequestWrapper claimRequest = new ClaimRequestWrapper();
            claimRequest.isClaimDateExpired = true;
            return claimRequest;
        }
        // 25.3.17 end
        //ClaimRequestWrapper claimRequest = saveClaimRequest(claimRequestStr);
        Approval.ProcessSubmitRequest approvalRequest = new Approval.ProcessSubmitRequest();
        approvalRequest.setComments('Claim Request Submitted for approval');
        approvalRequest.setObjectId(claimId);
        Approval.ProcessResult approvalResult = Approval.process(
            approvalRequest
        );
        return getClaimRequestWrapper(claimId);
    }

    /**
     * @description: delete content document
     */
    @AuraEnabled
    public static void deleteContentDocument(String cdId) {
        ContentDocument cd = new ContentDocument(Id = cdId);
        delete cd;
    }

    /**
     * @description: load customer
     */
    @AuraEnabled
    public static List<CCM_PromotionInfoCtl.CustomerOrGroupWrapper> getCustomer(
        String keyWord
    ) {
        List<CCM_PromotionInfoCtl.CustomerOrGroupWrapper> result = new List<CCM_PromotionInfoCtl.CustomerOrGroupWrapper>();
        String likeKeyWord = keyWord;
        if (String.isBlank(likeKeyWord)) {
            return result;
        }
        if (!likeKeyWord.startsWith('%')) {
            likeKeyWord = '%' + likeKeyWord;
        } else {
            likeKeyWord = likeKeyWord;
        }

        if (!likeKeyWord.endsWith('%')) {
            likeKeyWord = likeKeyWord + '%';
        } else {
            likeKeyWord = likeKeyWord;
        }

        // get customers and groups
        Map<Id, Account> accountMap = new Map<Id, Account>(
            [
                SELECT
                    Id,
                    Name,
                    AccountNumber,
                    ORG_Code__c,
                    (
                        SELECT
                            Id,
                            Segmentation__c,
                            TM_Segmentation__c,
                            Summary_of_Potential_Actual_Sales__c
                        FROM Customer_Profile__r
                    )
                FROM Account
                WHERE
                    (Name LIKE :likeKeyWord
                    OR AccountNumber LIKE :likeKeyWord)
                    AND RecordType.Name = 'Channel'
            ]
        );
        Map<Id, Promotion_Customer_Group__c> groupMap = new Map<Id, Promotion_Customer_Group__c>(
            [
                SELECT Id, Name
                FROM Promotion_Customer_Group__c
                WHERE Name LIKE :likeKeyWord
            ]
        );

        for (Id oneId : accountMap.keySet()) {
            CCM_PromotionInfoCtl.CustomerOrGroupWrapper one = new CCM_PromotionInfoCtl.CustomerOrGroupWrapper();
            one.Id = oneId;
            one.Name = accountMap.get(oneId).Name;
            one.Type = 'CUSTOMER';
            one.AccountNumber = accountMap.get(oneId).AccountNumber;
            for (
                Customer_Profile__c profile : accountMap.get(oneId)
                    .Customer_Profile__r
            ) {
                one.autualSegmentation = profile.Summary_of_Potential_Actual_Sales__c;
                one.tmSegmentation = profile.TM_Segmentation__c;
            }
            if (accountMap.get(oneId).Customer_Profile__r.size() > 0) {
                one.hasCustomerProfile = true;
            } else {
                one.hasCustomerProfile = false;
            }
            // update by winfried on 2022.12.27
            // 控制GST/HST、QST两个字段可见性
            if (accountMap.get(oneId).ORG_Code__c == 'CCA') {
                one.isCCA = true;
            } else {
                one.isCCA = false;
            }
            result.add(one);
        }
        return result;
    }

    /**
     * @description: get all sell-through claims files
     */
    @AuraEnabled
    public static List<ContentDocument> getAllFiles(String claimId) {
        List<ContentDocument> returnList = new List<ContentDocument>();

        for (ContentDocumentLink iterator : [
            SELECT ContentDocumentId, ContentDocument.Title
            FROM ContentDocumentLink
            WHERE LinkedEntityId = :claimId
        ]) {
            returnList.add(
                new ContentDocument(
                    Id = iterator.ContentDocumentId,
                    Title = iterator.ContentDocument.Title
                )
            );
        }

        return returnList;
    }

    /**
     * @description: get all claim request
     */
    @AuraEnabled
    public static ClaimListWrapper getAllClaimRequest(
        Integer pageIndex,
        Integer pageSize
    ) {
        Set<Id> customerSet = new Set<Id>();
        CustomerWrapper wrapper = getCustomerInfo();
        if (wrapper.customerId != null) {
            customerSet.add(wrapper.customerId);
        }
        // System.debug('=======>'+customerSet);
        List<Claim_Request__c> claimRequestList = [
            SELECT
                Id,
                Name,
                Promotion_Code__c,
                Promotion_Code__r.Name,
                // add haibo: french
                Promotion_Code__r.Promotion_Name_French__c,
                Promotion_Code__r.Promo_Code__c,
                Total_Claim_Amount__c,
                Claim_Status__c,
                // add haibo: french
                tolabel(Claim_Status__c) claimStatusLabel,
                CreatedDate,
                LastModifiedDate,
                CreatedBy.Name
            FROM Claim_Request__c
            WHERE Customer__c IN :customerSet
            ORDER BY createdDate DESC
            LIMIT 50000
        ];
        ClaimListWrapper returnWrapper = new ClaimListWrapper(
            pageSize,
            pageIndex
        );
        List<Claim_Request__c> finalList = new List<Claim_Request__c>();
        //this fist page the front end pass to back end zero
        Integer startIndex = pageIndex * pageSize;
        Integer endIndex = startIndex + pageSize - 1;
        for (Integer index = startIndex; index <= endIndex; index++) {
            if (index <= claimRequestList.size() - 1) {
                finalList.add(claimRequestList[index]);
            }
        }
        returnWrapper.claimRequestList = finalList;
        returnWrapper.totalCounts = claimRequestList.size();
        return returnWrapper;
    }

    /**
     * @description: delete promotion performance
     */
    @AuraEnabled
    public static void deletePerformances(
        List<Promotion_Performance__c> oldList
    ) {
        if (!oldList.isEmpty()) {
            delete oldList;
        }
    }

    /**
     * @description: create new performance
     */
    @AuraEnabled
    public static void insertPerformances(
        List<Promotion_Performance__c> insertList
    ) {
        Set<Id> promotionWindowIdSet = new Set<Id>();
        for (Promotion_Performance__c performance : insertList) {
            promotionWindowIdSet.add(performance.Promotion_Window__c);
        }
        // get promotion window
        Map<Id, Promotion_Window__c> promotionWindowMap = new Map<Id, Promotion_Window__c>(
            [
                SELECT
                    Start_Date__c,
                    End_Date__c,
                    Promotion_Window_Approval_Status__c
                FROM Promotion_Window__c
                WHERE Id IN :promotionWindowIdSet
            ]
        );
        // only update new, rejected or recalled promotion performance
        List<Promotion_Performance__c> toInsertList = new List<Promotion_Performance__c>();
        for (Promotion_Performance__c performance : insertList) {
            Promotion_Window__c promotionWindow = promotionWindowMap.get(
                performance.Promotion_Window__c
            );
            if (
                promotionWindow != null &&
                String.isNotBlank(
                    promotionWindow.Promotion_Window_Approval_Status__c
                ) &&
                promotionWindow.Promotion_Window_Approval_Status__c !=
                'Rejected' &&
                promotionWindow.Promotion_Window_Approval_Status__c !=
                'Recalled'
            ) {
                continue;
            }
            toInsertList.add(performance);
        }
        if (!toInsertList.isEmpty()) {
            upsert toInsertList Identifier__c;
        }
    }

    /**
     * @description: get all promotion rules with offering and threshold by promotion
     */
    @AuraEnabled
    public static List<CCM_PromotionSaveCtl.PromotionRule> getPromotionRulesByPromotionId(
        String idPromotion
    ) {
        CCM_PromotionSaveCtl.PromotionRule objRuleWrapper;
        CCM_PromotionSaveCtl.PromotionThreshold objThresholdWrapper;
        CCM_PromotionSaveCtl.PromotionOffering objOfferingWrapper;
        List<CCM_PromotionSaveCtl.PromotionRule> lstRuleWrapper = new List<CCM_PromotionSaveCtl.PromotionRule>();
        Map<Id, List<Promotion_Product__c>> mapThresholdOfferingId2ListProduct = new Map<Id, List<Promotion_Product__c>>();

        // get promotion rule with promotion offering and promotion threshold
        for (Promotion_Rule__c objPR : [
            SELECT
                Id,
                Promotion__r.Promotion_Type__c,
                Name,
                (
                    SELECT
                        Gift_Total_Quantity__c,
                        Amount_Off__c,
                        Discount_Off__c,
                        Payment_Term__c,
                        Payment_Term_Label__c,
                        Payment_Term_Start_Date__c,
                        RecordTypeId,
                        RecordType.Name,
                        RecordType.DeveloperName,
                        Chervon_Funding__c, // add by austin, whole order offering 新加计算使用
                        Percent_Chervonfunding__c // add by Yanko, whole order offering Chervon Funding %
                    FROM Promotion_Offerings__r
                ),
                (
                    SELECT
                        Bogo_Remark__c,
                        RecordTypeId,
                        RecordType.Name,
                        RecordType.DeveloperName,
                        Minimum_Total_Amount__c,
                        Minimum_Total_Quantity__c,
                        Minimum_Different_Tool_Models__c,
                        Minimum_Whole_Order_Amount__c,
                        Maximum_Whole_Order_Amount__c,
                        Minimum_Whole_Order_Quantity__c,
                        Min_Total_Order_Amount__c, // add by austin,whole order区间最小值
                        Max_Total_Order_Amount__c // add by austin,whole order区间最大值
                    FROM Thresholds__r
                )
            FROM Promotion_Rule__c
            WHERE Promotion__c = :idPromotion
        ]) {
            objRuleWrapper = new CCM_PromotionSaveCtl.PromotionRule();
            objRuleWrapper.ruleId = objPR.Id;
            objRuleWrapper.name = objPR.Name;

            // generate threshold wrapper
            for (Promotion_Threshold__c objPT : objPR.Thresholds__r) {
                objThresholdWrapper = new CCM_PromotionSaveCtl.PromotionThreshold();
                objThresholdWrapper.thresholdId = objPT.Id;
                if (!mapThresholdOfferingId2ListProduct.containsKey(objPT.Id)) {
                    mapThresholdOfferingId2ListProduct.put(
                        objPT.Id,
                        new List<Promotion_Product__c>()
                    );
                }
                objThresholdWrapper.recordTypeId.value = objPT.RecordTypeId;
                objThresholdWrapper.recordTypeApiName.value = objPT.RecordType.DeveloperName;
                objThresholdWrapper.recordTypeName.value = objPT.RecordType.Name;
                objThresholdWrapper.bogoRemark.value = objPT.Bogo_Remark__c;
                objThresholdWrapper.mintotalorderamount = objPT.Min_Total_Order_Amount__c == null ? null : String.valueOf(objPT.Min_Total_Order_Amount__c);
                objThresholdWrapper.maxtotalorderamount = objPT.Max_Total_Order_Amount__c == null ? null : String.valueOf(objPT.Max_Total_Order_Amount__c);
                objThresholdWrapper.minimumTotalAmount.value = objPT.Minimum_Total_Amount__c ==
                    null
                    ? null
                    : String.valueOf(objPT.Minimum_Total_Amount__c);
                objThresholdWrapper.minimumTotalQuantity.value = objPT.Minimum_Total_Quantity__c ==
                    null
                    ? null
                    : String.valueOf(objPT.Minimum_Total_Quantity__c);
                objThresholdWrapper.minimumDifferentToolModels.value = objPT.Minimum_Different_Tool_Models__c ==
                    null
                    ? null
                    : String.valueOf(objPT.Minimum_Different_Tool_Models__c);
                objThresholdWrapper.minimumWholeOrderAmount.value = objPT.Minimum_Whole_Order_Amount__c ==
                    null
                    ? null
                    : String.valueOf(objPT.Minimum_Whole_Order_Amount__c);
                objThresholdWrapper.maximumWholeOrderAmount.value = objPT.Maximum_Whole_Order_Amount__c ==
                    null
                    ? null
                    : String.valueOf(objPT.Maximum_Whole_Order_Amount__c);
                objThresholdWrapper.minimumWholeOrderQuantity.value = objPT.Minimum_Whole_Order_Quantity__c ==
                    null
                    ? null
                    : String.valueOf(objPT.Minimum_Whole_Order_Quantity__c);
                objRuleWrapper.thresholds.add(objThresholdWrapper);
            }

            // generate offering wrapper
            for (Promotion_Offering__c objPO : objPR.Promotion_Offerings__r) {
                objOfferingWrapper = new CCM_PromotionSaveCtl.PromotionOffering();
                objOfferingWrapper.offeringId = objPO.Id;
                if (!mapThresholdOfferingId2ListProduct.containsKey(objPO.Id)) {
                    mapThresholdOfferingId2ListProduct.put(
                        objPO.Id,
                        new List<Promotion_Product__c>()
                    );
                }
                objOfferingWrapper.recordTypeId.value = objPO.RecordTypeId;
                objOfferingWrapper.recordTypeApiName.value = objPO.RecordType.DeveloperName;
                objOfferingWrapper.recordTypeName.value = objPO.RecordType.Name;
                objOfferingWrapper.giftTotalQuantity.value = objPO.Gift_Total_Quantity__c ==
                    null
                    ? null
                    : String.valueOf(objPO.Gift_Total_Quantity__c);
                objOfferingWrapper.amountOff.value = objPO.Amount_Off__c == null
                    ? null
                    : String.valueOf(objPO.Amount_Off__c);
                objOfferingWrapper.discountOff.value = objPO.Discount_Off__c ==
                    null
                    ? null
                    : String.valueOf(objPO.Discount_Off__c);
                objOfferingWrapper.paymentTerm.value = objPO.Payment_Term__c;
                objOfferingWrapper.paymentTermLabel.value = objPO.Payment_Term_Label__c;
                objOfferingWrapper.termStartDate.value = CCM_PromotionRuleCtl.getFormattedDateString(
                    objPO.Payment_Term_Start_Date__c
                );
                objOfferingWrapper.ChervonFunding.value = String.valueOf(objPO.Chervon_Funding__c);// add by austin
                objOfferingWrapper.percentChervonFunding.value = String.valueOf(objPO.Percent_Chervonfunding__c);// add by Yanko
                objRuleWrapper.offerings.add(objOfferingWrapper);
            }
            lstRuleWrapper.add(objRuleWrapper);
        }

        // get promotion product by offering and threshold
        for (Promotion_Product__c objPP : [
            SELECT
                Minimum_Amount__c,
                Maximum_Amount__c,
                Minimum_Quantity__c,
                Maximum_Quantity__c,
                Gift_Quantity__c,
                Number__c,
                Chervon_Funding__c,
                PMAPP__c,
                Promotion2_Threshold__c,
                Promotion2_Offering__c,
                Product__c,
                Product__r.Name,
                Product__r.ProductCode,
                Product__r.Full_Pallet_Quantity__c
            FROM Promotion_Product__c
            WHERE
                Promotion2_Threshold__c IN :mapThresholdOfferingId2ListProduct.keySet()
                OR Promotion2_Offering__c IN :mapThresholdOfferingId2ListProduct.keySet()
        ]) {
            mapThresholdOfferingId2ListProduct.get(
                    objPP.Promotion2_Threshold__c == null
                        ? objPP.Promotion2_Offering__c
                        : objPP.Promotion2_Threshold__c
                )
                .add(objPP);
        }
        for (CCM_PromotionSaveCtl.PromotionRule objPR : lstRuleWrapper) {
            for (
                CCM_PromotionSaveCtl.PromotionThreshold objTW : objPR.thresholds
            ) {
                fillInPromotionProduct(
                    objTW,
                    mapThresholdOfferingId2ListProduct.get(objTW.thresholdId)
                );
            }
            for (
                CCM_PromotionSaveCtl.PromotionOffering objOW : objPR.offerings
            ) {
                fillInPromotionProduct(
                    objOW,
                    mapThresholdOfferingId2ListProduct.get(objOW.offeringId)
                );
            }
        }
        return lstRuleWrapper;
    }

    @AuraEnabled
    public static void insertDocumentLink(String recordId, List<String> documentIds) {
        CCM_WithoutSharingUtil.insertDocumentLink(recordId, documentIds);
    }

    /**
     * @description This method is used to fill in Products for Thresholds or Offerings.
     */
    private static void fillInPromotionProduct(
        CCM_PromotionSaveCtl.ThresholdOfferingParentWrapper objThresholdOfferingParentWrapper,
        List<Promotion_Product__c> lstPromotionProduct
    ) {
        CCM_PromotionSaveCtl.PromotionProduct objProductWrapper;
        if (
            lstPromotionProduct == null ||
            lstPromotionProduct.isEmpty() ||
            objThresholdOfferingParentWrapper.products == null
        )
            return;
        for (Promotion_Product__c objPP : lstPromotionProduct) {
            objProductWrapper = new CCM_PromotionSaveCtl.PromotionProduct();
            objProductWrapper.minimumAmount.value = objPP.Minimum_Amount__c ==
                null
                ? null
                : String.valueOf(objPP.Minimum_Amount__c);
            objProductWrapper.maximumAmount.value = objPP.Maximum_Amount__c ==
                null
                ? null
                : String.valueOf(objPP.Maximum_Amount__c);
            objProductWrapper.minimumQuantity.value = objPP.Minimum_Quantity__c ==
                null
                ? null
                : String.valueOf(objPP.Minimum_Quantity__c);
            objProductWrapper.maximumQuantity.value = objPP.Maximum_Quantity__c ==
                null
                ? null
                : String.valueOf(objPP.Maximum_Quantity__c);
            objProductWrapper.giftQuantity.value = objPP.Gift_Quantity__c ==
                null
                ? null
                : String.valueOf(objPP.Gift_Quantity__c);
            objProductWrapper.giftPMAPP.value = objPP.PMAPP__c == null
                ? null
                : String.valueOf(objPP.PMAPP__c);
            objProductWrapper.giftChervonFunding.value = objPP.Chervon_Funding__c ==
                null
                ? null
                : String.valueOf(objPP.Chervon_Funding__c);
            objProductWrapper.numberStr = objPP.Number__c == null
                ? null
                : String.valueOf((Integer) objPP.Number__c);
            objProductWrapper.value = objPP.Product__c;
            objProductWrapper.label = objPP.Product__r.Name;
            objProductWrapper.ProductCode = objPP.Product__r.ProductCode;
            objProductWrapper.fullPalletQuantity = objPP.Product__r.Full_Pallet_Quantity__c;
            objThresholdOfferingParentWrapper.products.add(objProductWrapper);
        }
    }

    // 25.3.17: 添加申报日期过期检查
    public static Boolean checkClaimEndDateExpired(String strWindowId, String strClaimId) {
        Date dtClaimEndDate;
        if (strClaimId != null) {            
            Claim_Request__c objSellThroughClaim = [SELECT Id, Promotion_Window__r.Claim_End_Date__c FROM Claim_Request__c WHERE Id = :strClaimId];
            dtClaimEndDate = objSellThroughClaim.Promotion_Window__r.Claim_End_Date__c;
        }
        else if (strWindowId != null){
            Promotion_Window__c objPromotionWindow = [SELECT Id, Claim_End_Date__c FROM Promotion_Window__c WHERE Id = :strWindowId];
            dtClaimEndDate = objPromotionWindow.Claim_End_Date__c;
        }
        return dtClaimEndDate != null ? Date.today() > dtClaimEndDate : false;
    }

    /**
     * @description: wrapper class for pagination
     */
    public class ClaimListWrapper {
        @AuraEnabled
        public Integer totalCounts { get; set; }
        @AuraEnabled
        public Integer pageSize { get; set; }
        @AuraEnabled
        public Integer pageIndex { get; set; }
        @AuraEnabled
        public List<Claim_Request__c> claimRequestList { get; set; }
        public ClaimListWrapper(Integer pageSize, Integer pageIndex) {
            this.pageIndex = pageIndex;
            this.pageSize = pageSize;
            this.claimRequestList = new List<Claim_Request__c>();
        }
    }

    /**
     * @description: wrapper class for Claim_Request__c
     */
    public class ClaimRequestWrapper {
        @AuraEnabled
        public String recordId { get; set; }
        @AuraEnabled
        public String promotionId { get; set; }
        @AuraEnabled
        public String promotionCode { get; set; }
        @AuraEnabled
        public String promotionName { get; set; }
        @AuraEnabled
        public String customerId { get; set; }
        @AuraEnabled
        public String customerName { get; set; }
        @AuraEnabled
        public String customerOrgCode { get; set; }
        @AuraEnabled
        public String promotionWindowId { get; set; }
        @AuraEnabled
        public String reimbursementType { get; set; }
        @AuraEnabled
        public String claimDescription { get; set; }
        @AuraEnabled
        public Decimal totalClaimAmount { get; set; }

        @AuraEnabled
        public String address { get; set; }
        @AuraEnabled
        public String shiptoAddress {get;set;}
        @AuraEnabled
        public String status { get; set; }
        @AuraEnabled public String statusText;
        @AuraEnabled
        public String rejectReason { get; set; }
        @AuraEnabled
        public PromotionInfoWrapper promotionBasicInfo { get; set; }
        @AuraEnabled
        public List<ClaimProduct> offeringClaimItems { get; set; }
        @AuraEnabled
        public List<ClaimProduct> thresholdClaimItems { get; set; }
        @AuraEnabled
        public List<String> deleteClaimItemIds { get; set; }
        @AuraEnabled
        public List<ContentDocument> showFiles { get; set; }
        @AuraEnabled
        public List<OfferingSummary> offeringSummaryList { get; set; }
        @AuraEnabled
        public List<CCM_PromotionSaveCtl.PromotionRule> promotionRules {
            get;
            set;
        }

        //TODO 加GST/HST QST
        @AuraEnabled
        public Decimal gstOrHSTCurrency { get; set; }
        @AuraEnabled
        public Decimal qstCurrency { get; set; }
        @AuraEnabled
        public Decimal totalClaimAmountInclusive { get; set; }
        @AuraEnabled
        public String currencyCode { get; set; }
        @AuraEnabled
        public List<Invoice__c> invoiceList { get; set; }

        // 25.3.17 添加过期状态
        @AuraEnabled public Boolean isClaimDateExpired;
    }

    /**
     * @description: wrapper class for customer with promotion window
     */
    public class CustomerWrapper {
        @AuraEnabled
        public String customerId { get; set; }
        @AuraEnabled
        public String customerName { get; set; }
        @AuraEnabled
        public List<PromotionWindowWrapper> promotionCodeList { get; set; }
        @AuraEnabled
        public Boolean isCCA { get; set; }
    }

    /**
     * @description: wrapper class for promotions
     */
    public class PromotionListWrapper {
        @AuraEnabled
        public Integer count { get; set; }
        @AuraEnabled
        public Map<String, String> promotionMap { get; set; }
    }

    /**
     * @description: wrapper class for promotion
     */
    public class PromotionInfoWrapper {
        @AuraEnabled
        public String promotionCode { get; set; }
        @AuraEnabled
        public String promotionId { get; set; }
        @AuraEnabled
        public String promotionType { get; set; }
        @AuraEnabled
        public String claimExpiredMessage { get; set; }
        @AuraEnabled
        public List<PromotionWindowWrapper> windowList { get; set; }
        @AuraEnabled
        public List<BillToWrapper> billList { get; set; }
        @AuraEnabled
        public List<BillToWrapper> shiptoList {get;set;}
        @AuraEnabled
        public List<PromotionCustomer> customerList { get; set; }
        @AuraEnabled
        public List<ClaimProduct> thresholdProducts { get; set; }
        @AuraEnabled
        public List<ClaimProduct> offeringProducts { get; set; }
        @AuraEnabled
        public List<CCM_PromotionSaveCtl.PromotionRule> promotionRules {get;set;}
        @AuraEnabled
        public List<Product2> products {get;set;} // add by austin

        public PromotionInfoWrapper(String promotionCode) {
            this.promotionCode = promotionCode;
        }
    }

    /**
     * @description: wrapper class for promotion_window__c
     */
    public class PromotionWindowWrapper {
        @AuraEnabled
        public String windowId { get; set; }
        @AuraEnabled
        public Date winStartDate { get; set; }
        @AuraEnabled
        public Date winEndDate { get; set; }
        @AuraEnabled
        public Date winClaimEndDate { get; set; }
        @AuraEnabled
        public String winStartDateEndDate { get; set; }
        @AuraEnabled
        public String promotionName { get; set; }
        @AuraEnabled
        public String promotionCodeForExternal { get; set; }
    }

    /**
     * @description: wrapper class for Billing address
     */
    public class BillToWrapper {
        @AuraEnabled
        public String name { get; set; }
        @AuraEnabled
        public String brand { get; set; }
        @AuraEnabled
        public String address1 { get; set; }
        @AuraEnabled
        public String city { get; set; }
        @AuraEnabled
        public String country { get; set; }
        @AuraEnabled
        public String state { get; set; }
        @AuraEnabled
        public String authBrandAndAddressId { get; set; }
    }

    /**
     * @description: wrapper class for promotion_target_customer__c
     */
    public class PromotionCustomer {
        @AuraEnabled
        public String name { get; set; }
        @AuraEnabled
        public String customerId { get; set; }
    }

    /**
     * @description: wrapper class for claim request item
     */
    public class ClaimProduct {
        @AuraEnabled
        public String recordId { get; set; }
        @AuraEnabled
        public String productId { get; set; }
        @AuraEnabled
        public String productName { get; set; }
        @AuraEnabled
        public String productCode { get; set; }
        @AuraEnabled
        public Decimal MSRP { get; set; }
        @AuraEnabled
        public List<windowPrice> windowMSRP { get; set; }
        @AuraEnabled
        public Decimal promoPrice { get; set; }
        @AuraEnabled
        public Decimal sellInPrice { get; set; }
        @AuraEnabled
        public Decimal chervonFundedAmount { get; set; }
        @AuraEnabled
        public Decimal salePrice { get; set; }
        @AuraEnabled
        public Decimal quantity { get; set; }
        @AuraEnabled
        public Decimal totalSales { get; set; }
        @AuraEnabled
        public Decimal claimAmount { get; set; }
        @AuraEnabled
        public String type { get; set; }
        @AuraEnabled
        public Claim_Request_Item__c itemObj { get; set; }
        @AuraEnabled
        public String brand { get; set; }
        //add by austin
        @AuraEnabled
        public String promotionId { get; set; }
        @AuraEnabled
        public Boolean stWholeOrder{get;set;}
        @AuraEnabled
        public Decimal wholeOrderFunding{get;set;}
        @AuraEnabled
        public Decimal scaleAmount{get;set;}
        @AuraEnabled
        public String ruleId{get;set;}
        //end
        @AuraEnabled
        public Boolean isBMSM;
    }

    public class windowPrice {
        @AuraEnabled
        public String windowId { get; set; }
        @AuraEnabled
        public Decimal MSRP { get; set; }
    }

    /**
     * @description: wrapper class
     */
    public class OfferingSummary {
        @AuraEnabled
        public String label { get; set; }
        @AuraEnabled
        public Decimal value { get; set; }
        public OfferingSummary(String label, Decimal value) {
            this.label = label;
            this.value = value;
        }
    }
}