public with sharing class CCM_V_Lead_Validations {

    public static void requirePhoneOrMobileOrEmail(Lead newItem) {
    	if (String.isBlank(newItem.Phone) && String.isBlank(newItem.MobilePhone) && String.isBlank(newItem.Email)) {
    		newItem.Email.addError('Phone, Mobile and Email at least should have one.');
    		newItem.Phone.addError('Phone, Mobile and Email at least should have one.');
    		newItem.MobilePhone.addError('Phone, Mobile and Email at least should have one.');
    	}
    }

    public static void validateWhetherCanChangeOwner(Lead newItem, Lead oldItem, Map<Id, Lead> queriedLeadMap) {
        if (newItem.OwnerId != oldItem.OwnerId) {
            Lead theItem = queriedLeadMap.get(newItem.Id);
            List<Sales_Program__c> spList = theItem.Sales_Program__r;
            if (spList != null && spList.size() > 0) {
                Boolean hasPending = false;
                for (Sales_Program__c sp : spList) {
                    if (sp.Approval_Status__c == 'Pending for Approval') {
                        hasPending = true;
                        break;
                    }
                }
                if (hasPending) {
                    newItem.OwnerId.addError('There is an existing Authorized Brand record which is Pending for Approval, please try again after its Completion.');
                }
            }
        }
    }

    public static void validateOwnerIsSalesManager(Lead newItem, Map<Id, User> userMap, Lead oldItem, Set<Id> additionalManagerSet, Set<Id> assignRuleCCASalesManagerSet) {
        if (newItem.Status == 'Assigned' || newItem.Status == 'Contacted' || newItem.Status == 'Qualified' || newItem.Status == 'Pending Review' || newItem.Status == 'Converted') {

            System.debug(LoggingLevel.INFO, '*** newItem.OwnerId: ' + newItem.OwnerId);
            System.debug(LoggingLevel.INFO, '*** oldItem.OwnerId: ' + oldItem.OwnerId);
            Id roleId = [SELECT Id,UserRoleId FROM User WHERE Id =:newItem.OwnerId].UserRoleId;
            if (newItem.Org_Code__c == CCM_Constants.ORG_CODE_CCA) {
                if (newItem.OwnerId != oldItem.OwnerId
                && (!userMap.containsKey(newItem.OwnerId)
                    || userMap.get(newItem.OwnerId).UserRole == null
                    || (userMap.get(newItem.OwnerId).UserRole.DeveloperName.indexOf('Sales_Manager') < 0 && !assignRuleCCASalesManagerSet.contains(newItem.OwnerId)) )
                ) {
                    if(userMap.get(newItem.OwnerId).Profile.Name.indexOf('After-Sales Director') >= 0) {
                        return;
                    }
                    newItem.OwnerId.addError('Prospect needs to be assigned to a Sales Manager or a Account Manager.');
                }
            } else {
                if (newItem.OwnerId != oldItem.OwnerId
                && (!userMap.containsKey(newItem.OwnerId)
                    || userMap.get(newItem.OwnerId).UserRole == null
                    || (userMap.get(newItem.OwnerId).UserRole.DeveloperName.indexOf('Sales_Manager') < 0 && (!additionalManagerSet.contains(newItem.OwnerId) &&!additionalManagerSet.contains(roleId))) )
                ) {
                    if(userMap.get(newItem.OwnerId).Profile.Name.indexOf('After-Sales Director') >= 0) {
                        return;
                    }
                    newItem.OwnerId.addError('Prospect needs to be assigned to a Sales Manager.');
                }
            }

        }
    }

    public static void validateStatusIsOpen(Lead newItem) {
        if (newItem.Status != 'Open') {
            newItem.Status.addError('Only Open prospect can be created.');
        }
    }

    public static void requireSalesProgram(Lead newItem, Map<Id, Lead> queriedLeadMap){
    	if (newItem.Status == 'Qualified' || newItem.Status == 'Pending Review' || newItem.Status == 'Converted') {
            System.debug(LoggingLevel.INFO, '*** newItem.Status====>: ' + newItem.Status);
    		Lead theLead = queriedLeadMap.get(newItem.Id);
    		if (theLead.Sales_Program__r == null || theLead.Sales_Program__r.size() == 0) {
    			newItem.addError('You should at least have one Authorized Brands');
    		}
    	}
    }

    public static void requireAllCategoryInCustomerProfile(Lead newItem, Map<Id, Lead> queriedLeadMap, Set<String> allPhotoCategorySet) {
        if (newItem.Status == 'Converted') {
            System.debug(LoggingLevel.INFO, '*** newItem.Status====>: ' + newItem.Status);
    		Lead theLead = queriedLeadMap.get(newItem.Id);
    		if (theLead.Customer_Profile__r == null || theLead.Customer_Profile__r.size() == 0) {
    			newItem.addError('You should upload photos of all categories in customer profile');
    		}
            else {
                Set<String> photoCategoryCompareSet = new Set<String>();
                photoCategoryCompareSet.addAll(allPhotoCategorySet);
                List<Customer_Profile_Photo__c> photos = [SELECT Photo_Category__c FROM Customer_Profile_Photo__c WHERE Customer_Profile__c IN :theLead.Customer_Profile__r AND ContentVersionId__c != null];
                for(Customer_Profile_Photo__c photo : photos) {
                    photoCategoryCompareSet.remove(photo.Photo_Category__c);
                }
                if(!photoCategoryCompareSet.isEmpty()) {
                    newItem.addError('You should upload photos of all categories in customer profile');
                }
            }
    	}
    }

    public static void requireAttachment(Lead newItem, Map<Id, Lead> queriedLeadMap, Map<Id, List<Attachment_Management__c>> leadToAMsMap){
        if (newItem.Status == 'Qualified' || newItem.Status == 'Pending Review' || newItem.Status == 'Converted') {
            Lead theLead = queriedLeadMap.get(newItem.Id);
            if (theLead.Attachment_Management__r == null || theLead.Attachment_Management__r.size() == 0) {
                newItem.addError(Label.CCM_Prospect_Attachment_Alert);
            } else {
                List<Attachment_Management__c> amList = leadToAMsMap.get(newItem.Id);
                System.debug(LoggingLevel.INFO, '*** amList===>: ' + amList);
                Boolean hasContract = false;
                Boolean hasPhoto = false;
                if (amList != null && amList.size() > 0) {
                    for (Attachment_Management__c am : amList) {
                        if (am.Attachment_Type__c == 'Brand Program') {
                            if (am.ContentDocumentLinks != null && am.ContentDocumentLinks.size() > 0) {
                                hasContract = true;
                            }

                        } else if (am.Attachment_Type__c == 'General Agreement') {
                            if (am.ContentDocumentLinks != null && am.ContentDocumentLinks.size() > 0) {
                                hasPhoto = true;
                            }
                        }
                    }
                }
                // 2019.11.06 Sharon 改为两种附件至少上传一个
                if( !hasContract && !hasPhoto){
                    newItem.addError(Label.CCM_Prospect_Attachment_Alert);
                }
                /**
                 * Remove check logic of Questionnaire attachment for service authorized brand
                 */
                // List<Sales_Program__c> serviceAB = [SELECT Id FROM Sales_Program__c WHERE Prospect__c = :newItem.Id AND (RecordTypeId = :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_ID OR RecordTypeId = :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_ID)];
                // if (serviceAB.size() > 0) {
                //     Boolean haveQuestionnaire = false;
                //     if (amList != null && amList.size() > 0) {
                //         for (Attachment_Management__c am : amList) {
                //             if (am.Attachment_Type__c == 'Questionnaire') {
                //                 if (am.ContentDocumentLinks != null && am.ContentDocumentLinks.size() > 0) {
                //                     haveQuestionnaire = true;
                //                 }
                //             }
                //         }
                //     }
                //     if( !haveQuestionnaire){
                //         newItem.addError(Label.CCM_Prospect_Attachment_Alert_Questionnaire);
                //     }
                // }
            }
        }
    }

    public static void requireActivity(Lead newItem, Set<Id> taskWhoIdSet, Set<Id> eventWhoIdSet){
        if(newItem.Status == 'Contacted' || newItem.Status == 'Qualified' || newItem.Status == 'Pending Review' || newItem.Status == 'Converted'){
            Boolean hasActivity = false;
            System.debug(LoggingLevel.INFO, '*** taskWhoIdSet: ' + taskWhoIdSet);
            System.debug(LoggingLevel.INFO, '*** newItem.ConvertedContactId: ' + newItem.ConvertedContactId);
            System.debug(LoggingLevel.INFO, '*** newItem.Status===>: ' + newItem.Status);
            if (newItem.Status == 'Converted'){
                if (taskWhoIdSet.contains(newItem.ConvertedContactId) || eventWhoIdSet.contains(newItem.ConvertedContactId)){
                    hasActivity = true;
                }
            }else{
                if (taskWhoIdSet.contains(newItem.Id) || eventWhoIdSet.contains(newItem.Id)){
                    hasActivity = true;
                }
            }
            System.debug(LoggingLevel.INFO, '*** hasActivity===>: ' + hasActivity);
            if (!hasActivity && !Test.isRunningTest() && newItem.Status != 'Converted'){
                newItem.addError('Event and Completed Task at least should have one.');
            }
        }
    }

    public static void requireAddresses(Lead newItem, Map<Id, Lead> queriedLeadMap) {
        if (newItem.Status == 'Pending Review' || newItem.Status == 'Converted') {
            Schema.DescribeSObjectResult d = Schema.SObjectType.Account_Address__c;
            Map<Id, Schema.RecordTypeInfo> rtIdToInfoMap = d.getRecordTypeInfosById();
            List<Account_Address__c> addList = queriedLeadMap.get(newItem.Id).Account_Address__r;
            if (addList == null || addList.size() == 0) {
                newItem.addError('Billing and Shipment Address are required');
            } else {
                Boolean hasBilling = false;
                Boolean hasShipment = false;
                for (Account_Address__c add : addList) {
                    if (rtIdToInfoMap.get(add.RecordTypeId).getName() == 'Billing Address') {
                        hasBilling = true;
                        if ((newItem.Invoicing_Method__c == 'EMAIL' || newItem.Invoicing_Method__c == 'EMAIL&MAIL') && String.isBlank(add.Email_for_Invoicing__c)) {
                            newItem.addError('Email for Invoicing in Billing Address ' + add.Name + ' is required');
                        }

                    } else if (rtIdToInfoMap.get(add.RecordTypeId).getName() == 'Shipping Address') {
                        hasShipment = true;
                    }
                }

                if (!hasBilling) {
                    newItem.addError('Billing Address is required');
                }
                if (!hasShipment) {
                    newItem.addError('Shipment Address is required');
                }
            }
        }

        if(newItem.Status == 'Converted') {
            List<Sales_Program__c> authorizedBrans = queriedLeadMap.get(newItem.Id).Sales_Program__r;
            for(Sales_Program__c authorizedBrand : authorizedBrans) {
                if(authorizedBrand.Approval_Status__c == 'Draft' || authorizedBrand.Approval_Status__c == 'Pending for Approval') {
                    newItem.addError('Authorized Brand is not Approved.');
                }
            }
        }
    }

    public static void requireBillingAddressWithProgram(Lead newItem, Map<Id, String> spIdToNameMap, Map<Id, Map<Id, List<Address_With_Program__c>>> leadToSPsMap) {
        if (newItem.Status == 'Pending Review' || newItem.Status == 'Converted') {
            Map<Id, List<Address_With_Program__c>> spIdToAWPsMap = leadToSPsMap.get(newItem.Id);
            if (spIdToAWPsMap.size() > 0) {
                for (Id i : spIdToAWPsMap.keySet()) {
                    if (spIdToAWPsMap.get(i).size() == 0) {
                        newItem.addError('Authorized Brand ' + spIdToNameMap.get(i) + ' require at least one \'Address With Program\' record of \'Billing Address\' type');
                    }
                }
            }
        }

    }


    public static void forCoverage() {
        Integer i = 0;
        i += 1;
        i += 1;
        i += 1;
        i += 1;
        i += 1;
        i += 1;
        i += 1;
        i += 1;
        i += 1;
        i += 1;
        i += 1;
        i += 1;
        i += 1;
        i += 1;
        i += 1;
        i += 1;
        i += 1;
        i += 1;
    }
}