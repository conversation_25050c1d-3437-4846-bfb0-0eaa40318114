({
    onInit : function(component, event, helper) {
        //Row actions
        var actions = [{
            'label': 'Delete',
            'iconName': 'utility:delete',
            'name': 'delete'
        }];

        component.set('v.columns', [
            { label: 'Photo Category', fieldName: 'photoCategory', type: 'text' },
            { 
                label: 'Photo URL', 
                fieldName: 'photoUrl', 
                type: 'url',
                typeAttributes: { 
                    label: {
                        fieldName: 'photoName'
                    },
                    target: '_blank'
                } 
            },
            { type: 'action', typeAttributes: { rowActions: actions } }
        ]);

        var getPicklist = component.get("c.getCategoryOptions");
        getPicklist.setCallback(this, function(response){
            var state = response.getState();
            if (state === "SUCCESS") {
                component.set("v.photoCategoryOptions", response.getReturnValue());
                // add haibo: 2024-9-23
                let categoryList = response.getReturnValue();
                let options = categoryList.filter((item)=>{
                    return (item.value != 'Sync From VisitSurvey Log' && (item.value != 'Sync From Call Log'))
                })
                component.set("v.photoCategoryOptionsByChange", options);
                var comobox = component.find('categorySelect');
                if (comobox == undefined) {
                    
                    $A.createComponent(
                        'lightning:combobox',
                        {
                            'aura:id': 'categorySelect',
                            'label' : 'Select Photo Category',
                            'value' : '',
                            'options': options,
                            'onchange': component.getReference('c.comboChange'),
                            'variant': 'label-stacked'
                        },
                        function(newCombobox, status, error){
                            if(status === 'SUCCESS'){
                                var cmpBody = component.get('v.body');
     ;                          cmpBody.push(newCombobox);
                                component.set('v.body', cmpBody); 
                            }
                            else if(status === 'INCOMPLETE'){
                                console.log('No response from server');
                            }
                            else if(status === 'ERROR'){
                                console.log(error);
                            }
                        }
                    );
                }
            } else {
                var errors = response.getError();
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": "Error!",
                    "message": errors[0].message,
                    "type": "error"
                });
                toastEvent.fire();
            }
        });
        $A.enqueueAction(getPicklist);

        var action = component.get("c.getRelatedPhotos");
        let relatedStoreObj = component.get('v.relatedStoreObj');
        let createdByObj = component.get('v.createdByObj');
        if (relatedStoreObj.Id) {
            component.set('v.relatedStore', relatedStoreObj.Id);
        } else {
            component.set('v.relatedStore', '');
        }
        if (createdByObj.Id) {
            component.set('v.createdBy', createdByObj.Id);
        } else {
            component.set('v.createdBy', '');
        }
        // add haibo: 2024-9-23
        action.setParams({
            'parentId':component.get("v.recordId"),
            'categoryType': component.get('v.categoryType'),
            'relatedStore': component.get('v.relatedStore'),
            'createdDateFrom': component.get('v.createdDateFrom'),
            'createdDateTo': component.get('v.createdDateTo'),
            'createdBy': component.get('v.createdBy'),

        });
        action.setCallback(this, function(response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var resp = response.getReturnValue();
                component.set("v.relatedRecords",resp);
                component.set("v.allDatas", resp);
                component.set('v.isAscending', '0');
                let nameSourceList = [];
                resp.forEach(item=>{
                    if(item.syncFrom) {
                        let nameSourceItem = {
                            'name': item.syncFrom
                        };
                        nameSourceList.push(nameSourceItem);
                    }
                });
                component.set('v.nameList', nameSourceList);
            }else{
                var errors = response.getError();
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": "Error!",
                    "message": errors[0].message,
                    "type": "error"
                });
                toastEvent.fire();
            }
        });
        $A.enqueueAction(action);
    },

    uploadFile : function(component, event, helper) {
        component.set("v.showUploadModal",true);
    },

    closeModal : function(component, event, helper) {
        component.set("v.showUploadModal",false);
        component.set("v.showChangeCategoryModal",false);
    },

    handleUploadFinished : function(component, event, helper) {
        var action = component.get('c.newCustomerProfilePhoto');
        //set parametrs
        action.setParams({
            recId : component.get('v.recordId'),
            category: component.get("v.category")
        }); 

        action.setCallback(this, function(response){
            var state = response.getState();
            if(state === 'SUCCESS') {
                component.set("v.showUploadModal",false);
                var fn = component.get('c.onInit');
                $A.enqueueAction(fn);
            }else{
                var errors = response.getError();
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": "Error!",
                    "message": errors[0].message,
                    "type": "error"
                });
                toastEvent.fire();
            }
        });
        $A.enqueueAction(action);
    },

    comboChange : function(component, event, helper) {
        if (event.getParam("value") == '' ) {
            component.set("v.showUpload", false);
            component.set("v.category", event.getParam("value"));
        } else {
            component.set("v.showUpload", true);
            component.set("v.category", event.getParam("value"));
        }
    },

    /*handleRowAction : function(component, event, helper) {
        var action = event.getParam('action');
        var row = event.getParam('row');
        switch (action.name) {
            case 'delete':
                var action = component.get("c.deleteRecord");
                action.setParams({"recordId":row.Id});
                action.setCallback(this, function(response){
                    var state = response.getState();
                    if (state === "SUCCESS") {
                        var fn = component.get('c.onInit');
                        $A.enqueueAction(fn);
                    }else{
                        var toastEvent = $A.get("e.force:showToast");
                        var errors = response.getError();
                        toastEvent.setParams({
                            "title": "Error!",
                            "message": errors[0].message,
                            "type": "Error",
                            "mode": "sticky",
                            "duration": "5000"
                        });
                    }
                });
                $A.enqueueAction(action); 
                break;
        }
    },*/

    selectRow : function(component, event, helper) {
        var ele = event.currentTarget;
        var sId = ele.dataset.id;
        component.set("v.selectedId", sId);
    },

    handleMenuSelect : function(component, event, helper) {
        var selectedMenuItemValue = event.getParam("value");
        var ele = event.currentTarget;
        if(selectedMenuItemValue == 'delete') {
            var rId = component.get("v.selectedId");
            var action = component.get("c.deleteRecord");
            action.setParams({"recordId":rId});
            action.setCallback(this, function(response){
                var state = response.getState();
                if (state === "SUCCESS") {
                    var fn = component.get('c.onInit');
                    $A.enqueueAction(fn);
                }else{
                    var toastEvent = $A.get("e.force:showToast");
                    var errors = response.getError();
                    toastEvent.setParams({
                        "title": "Error!",
                        "message": errors[0].message,
                        "type": "Error",
                        "mode": "sticky",
                        "duration": "5000"
                    });
                }
            });
            $A.enqueueAction(action);
        }
    },

    preview : function(component, event, helper) {
        event.preventDefault();
        var ele = event.currentTarget;
        var id = ele.dataset.id;
        var ids = [];
        ids.push(id);
        $A.get('e.lightning:openFiles').fire({
            recordIds: ids,
            selectedRecordId: id
        });
    },

    handleSelectAll: function(component, event, helper) {
        let photoDatas = JSON.parse(JSON.stringify(component.get("v.relatedRecords")));
        photoDatas.forEach(item=>{
            if(event.detail.checked) {
                item.checked = true;
            }
            else {
                item.checked = false;
            }
        });
        component.set("v.relatedRecords", photoDatas);        
    },
    
    handleSelect: function(component, event, helper) {
        let photoDatas = JSON.parse(JSON.stringify(component.get("v.relatedRecords")));
        let id = event.getSource().get('v.name');
        photoDatas.forEach(item=>{
            if(item.Id === id) {
                if(event.detail.checked) {
                    item.checked = true;
                }
                else {
                    item.checked = false;
                }
            }
        });
        component.set("v.relatedRecords", photoDatas);
    },

    handleChangeCategory: function(component, event, helper) {
        component.set("v.showChangeCategoryModal", true);
    },

    handleCategoryChange: function(component, event, helper) {
        component.set("v.photoCategoryChanged", event.getParam("value"));
    },

    confirmModal: function(component, event, helper) {
        let selectedPhotos = [];
        let photoDatas = component.get("v.relatedRecords");
        photoDatas.forEach(item=>{
            if(item.checked) {
                selectedPhotos.push(item.Id);
            }
        });

        let photoCategoryChanged = component.get("v.photoCategoryChanged");
        if(!photoCategoryChanged || selectedPhotos.length == 0) {
            return;
        }
        let action = component.get("c.changeCategory");
        action.setParams({
            'photoIds': selectedPhotos,
            'category': photoCategoryChanged
        });
        action.setCallback(this, function(response){
            if (response.getState() === "SUCCESS") {
                component.set("v.showChangeCategoryModal",false);
                var fn = component.get('c.onInit');
                $A.enqueueAction(fn);
            }
            else {
                var errors = response.getError();
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": "Error!",
                    "message": errors[0].message,
                    "type": "error"
                });
                toastEvent.fire();
            }
        });
        $A.enqueueAction(action);
    },
    downloadSelectedFiles: function(component, event, helper){
        let selectedPhotos = [];
        let photoDatas = component.get("v.relatedRecords");
        photoDatas.forEach(item=>{
            if(item.checked) {
                selectedPhotos.push(item.fileId);
            }
        });

        if(selectedPhotos.length > 0) {
            let downloadURL = '/sfc/servlet.shepherd/version/download/' + selectedPhotos.join('/');
            window.open(downloadURL, '_self');
        }
    },
    handleSearchInputChange: function(component, event, helper) {
        console.log(event.detail);
        if(event.detail) {
            component.find("autoComplete").filterDataByKeyword(event.detail);
        }
        else {
            let allDatas = component.get("v.allDatas");
            component.set("v.relatedRecords", allDatas);
        }
    },
    handleNameChange: function(component, event, helper) {
        console.log(event.detail);
        let detail = event.detail;

        let allDatas = component.get("v.allDatas");
        let matchDatas = allDatas.filter(item=>item.syncFrom === detail);
        component.set("v.relatedRecords", matchDatas);
    },
    // add haibo: 2024/09/16
    handleSearch: function(component, event, helper) {
        // new serch function
        let action = component.get("c.getRelatedPhotos");
        let relatedStoreObj = component.get('v.relatedStoreObj');
        let createdByObj = component.get('v.createdByObj');
        if (relatedStoreObj.Id) {
            component.set('v.relatedStore', relatedStoreObj.Id);
        } else {
            component.set('v.relatedStore', '');
        }
        if (createdByObj.Id) {
            component.set('v.createdBy', createdByObj.Id);
        } else {
            component.set('v.createdBy', '');
        }
        action.setParams({
            'parentId':component.get("v.recordId"),
            'categoryType': component.get('v.categoryType'),
            'relatedStore': component.get('v.relatedStore'),
            'createdDateFrom': component.get('v.createdDateFrom'),
            'createdDateTo': component.get('v.createdDateTo'),
            'createdBy': component.get('v.createdBy')
        });
        action.setCallback(this, function(response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var resp = response.getReturnValue();
                component.set("v.relatedRecords",resp);
                component.set("v.allDatas", resp);
                component.set('v.isAscending', '0');
                let nameSourceList = [];
                resp.forEach(item=>{
                    if(item.syncFrom) {
                        let nameSourceItem = {
                            'name': item.syncFrom
                        };
                        nameSourceList.push(nameSourceItem);
                    }
                });
                component.set('v.nameList', nameSourceList);
            }else{
                var errors = response.getError();
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": "Error!",
                    "message": errors[0].message,
                    "type": "error"
                });
                toastEvent.fire();
            }
        });
        $A.enqueueAction(action);
    },
    doReset: function(component, event, helper) {
        // new serch function
        component.set('v.categoryType', '');
        component.set('v.relatedStore', '');
        component.set('v.relatedStoreObj', {Name: '', Id: ''});
        component.set('v.createdDateFrom', '');
        component.set('v.createdDateTo', '');
        component.set('v.createdBy', '');
        component.set('v.createdByObj', {Name: '', Id: ''});
        let action = component.get("c.getRelatedPhotos");
        action.setParams({
            'parentId':component.get("v.recordId"),
            'categoryType': component.get('v.categoryType'),
            'relatedStore': component.get('v.relatedStore'),
            'createdDateFrom': component.get('v.createdDateFrom'),
            'createdDateTo': component.get('v.createdDateTo'),
            'createdBy': component.get('v.createdBy')
        });
        action.setCallback(this, function(response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var resp = response.getReturnValue();
                component.set("v.relatedRecords",resp);
                component.set("v.allDatas", resp);
                component.set('v.isAscending', '0');
                let nameSourceList = [];
                resp.forEach(item=>{
                    if(item.syncFrom) {
                        let nameSourceItem = {
                            'name': item.syncFrom
                        };
                        nameSourceList.push(nameSourceItem);
                    }
                });
                component.set('v.nameList', nameSourceList);
            }else{
                var errors = response.getError();
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": "Error!",
                    "message": errors[0].message,
                    "type": "error"
                });
                toastEvent.fire();
            }
        });
        $A.enqueueAction(action);
    },
    // add haibo: sort
    tableDataSort: function(component, event, helper) {
        let tableData = component.get('v.relatedRecords');
        let isAscending = component.get('v.isAscending');
        tableData.sort((a, b)=>{
            const nameA = a.RelatedStoreName ? a.RelatedStoreName.toUpperCase() : '';
            const nameB = b.RelatedStoreName ? b.RelatedStoreName.toUpperCase() : '';
            // 判断正反序
            if (isAscending == '0') {
                component.set('v.isAscending', '1');
                if (nameA < nameB) {
                    return -1;
                }
                if (nameA > nameB) {
                    return 1;
                }
            } else if (isAscending == '1') {
                component.set('v.isAscending', '2');
                if (nameA < nameB) {
                    return 1;
                }
                if (nameA > nameB) {
                    return -1;
                }
            } else {
                component.set('v.isAscending', '1');
                if (nameA < nameB) {
                    return -1;
                }
                if (nameA > nameB) {
                    return 1;
                }
            }
            return 0;
        });
        component.set('v.relatedRecords', JSON.parse(JSON.stringify(tableData)));
    },
})