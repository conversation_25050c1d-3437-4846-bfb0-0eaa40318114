/**
 * @description       :
 * <AUTHOR> <EMAIL>
 * @group             :
 * @last modified on  : 03-05-2024
 * @last modified by  : <EMAIL>
**/
public without sharing class CCM_CustomerAssignmentRuleHandler implements Triggers.Handler {

    public Set<String> salesBusinessType = new Set<String> {CCM_Constants.CUSTOMER_BUSINESS_TYPE_SALES, CCM_Constants.CUSTOMER_BUSINESS_TYPE_SALES_AND_SERVICE};
    private Set<String> secondTierCustomerType = new Set<String>{
        '2nd Tier Dealer',
        '2nd Tier Distributor',
        'Dealer Location'
    };
    public static Map<Id, Prospect_Assignment_Rule__c> customerAssignmentMap;

    public void handle() {
        List<Account> CNAAccounts = new List<Account>();
        Set<String> buyingGroupSet = new Set<String>();
        for (Account acc : (List<Account>)Trigger.new) {
            if (acc.RecordTypeId==CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_LOCATION_ID
            ||acc.RecordTypeId==CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID||
            acc.RecordTypeId==CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID) {
                return;
            }
            if (acc.ORG_Code__c == 'CNA' || String.isEmpty(acc.ORG_Code__c)) {
                CNAAccounts.add(acc);
                if(String.isNotBlank(acc.Buying_Group__c)) {
                    buyingGroupSet.add(acc.Buying_Group__c);
                }

            }
        }
        Map<String, String> buyingGroupMap = getBuyingGroupName(buyingGroupSet);
        if (Trigger.isBefore) {
            if (Trigger.isUpdate) {
                if (CNAAccounts.size() > 0) {
                    updateLeadFieldsForCNA(CNAAccounts, Trigger.newMap, (Map<Id, Account>)Trigger.oldMap, true, buyingGroupMap);
                }
            }
        }
        if (Trigger.isAfter && Trigger.isUpdate) {
            if (customerAssignmentMap != null && customerAssignmentMap.size() > 0) {
                System.debug('customerAssignmentMap -> ' + JSON.serialize(customerAssignmentMap));
                CCM_AccountChangeOwnerHandler.updateAccountRelatedRecordsOwnerAndDirector(customerAssignmentMap);
            }
        }
    }

    private Prospect_Assignment_Rule__c findAssignmentRule(String userId, String brand, String state, String nation, String distributorOrDealer, String buyingGroup, List<Prospect_Assignment_Rule__c> rules, Boolean isFlexAuto, String customerBusinessType,Boolean isDD,String salesGroup){
        Prospect_Assignment_Rule__c findRule = null;
        if (isDD) {
            if (String.isNotBlank(salesGroup) && rules != null && rules.size() > 0) {
                for (Prospect_Assignment_Rule__c r : rules) {
                    if(salesGroup.equalsIgnoreCase(r.sales_group__c)){
                        if(String.isNotBlank(buyingGroup) && buyingGroup != 'None') {
                            if(buyingGroup.equalsIgnoreCase(r.Buying_Group__c)) {
                                System.debug('*** 0000000:' + r);
                                findRule = r;
                            }
                        }
                        else {
                            if(String.isBlank(r.Buying_Group__c)) {
                                System.debug('*** 11111111:' + r);
                                findRule = r;
                            }
                        }
                    }
                }
            }
        }else{
            if (String.isNotBlank(brand) &&  String.isNotBlank(distributorOrDealer) && rules != null && rules.size() > 0) {
                for (Prospect_Assignment_Rule__c r : rules) {
                    Set<String> customerTypeSet = new Set<String>();
                    if(String.isNotBlank(r.Distributor_or_Dealer__c)) {
                        customerTypeSet.addAll(r.Distributor_or_Dealer__c.split(';'));
                    }
                    if(isFlexAuto){
                        if ((String.isNotBlank(r.Brand__c) && r.Brand__c.indexOf(brand) >= 0)
                            && (customerTypeSet.size() >0 &&customerTypeSet.contains(distributorOrDealer))
                            && r.Is_Flex_Auto_Only__c == isFlexAuto
                            && (r.Customer_Business_Type__c == customerBusinessType || (salesBusinessType.contains(r.Customer_Business_Type__c) && salesBusinessType.contains(customerBusinessType)))) {
                            return r;
                        }
                    } else {
                        if ((String.isNotBlank(r.Brand__c) && r.Brand__c.indexOf(brand) >= 0)
                            && (r.Nation__c == 'United States' && new Set<String>{'United States', 'US', 'USA'}.contains(nation) || r.Nation__c != 'United States' && r.Nation__c == nation)
                            && (customerTypeSet.size() >0 && customerTypeSet.contains(distributorOrDealer))
                            && (String.isBlank(r.State__c) || r.State__c.indexOf(state) >= 0)
                            && r.Is_Flex_Auto_Only__c == isFlexAuto
                            && r.Sales_Group__c == salesGroup
                           ) {
                                if(String.isNotBlank(buyingGroup) && buyingGroup != 'None') {
                                    if(buyingGroup.equalsIgnoreCase(r.Buying_Group__c)) {
                                        findRule = r;
                                    }
                                }
                                else {
                                    if(String.isBlank(r.Buying_Group__c)) {
                                        findRule = r;
                                    }
                                }
                        }
                    }
                }
            }
        }
        return findRule;
    }

    private void updateLeadFieldsForCNA(List<sObject> objList, Map<Id, sObject> newMap, Map<Id, Account> oldMap, Boolean toUpdate, Map<String, String> buyingGroupMap) {
    	List<Account> accList = (List<Account>)objList;
        Map<String,String> sgToSaleManagerMap = new Map<String,String>();
        List<Prospect_Assignment_Rule_SG_Mapping__mdt> sgList = [SELECT Id,Sales_Group__c,Sales_Manager_Name__c FROM Prospect_Assignment_Rule_SG_Mapping__mdt WHERE Channel__c ='DD'];
        List<String> opeSalesGroupSet = Label.OPE_SalesGroups.split(',');
        Set<String> ptSalesGroupSet = new Set<String>{'SG04','SG05','SG06','SG07','SG08','SG27','SG28','SG42'};
        List<Prospect_Assignment_Rule__c> rules = [SELECT Id, Customer_Business_Type__c, Brand__c, Distributor_or_Dealer__c, Nation__c, State__c, Cluster__c, Sales_Group__c, Sub_Cluster__c, Sales_Channel__c, User__c, Director__c, Is_Flex_Auto_Only__c,
                                                            Buying_Group__c
                                                        FROM Prospect_Assignment_Rule__c
                                                        WHERE (ORG_Code__c = '' OR ORG_Code__c = 'CNA')];
        for(Prospect_Assignment_Rule_SG_Mapping__mdt s : sgList){
            sgToSaleManagerMap.put(s.Sales_Manager_Name__c,s.Sales_Group__c);
        }
        customerAssignmentMap = new Map<Id, Prospect_Assignment_Rule__c>();

    	for (Account acc : accList) {
            Boolean isFlexAutoOnly = false;
            Boolean isDD = false;
            if(sgToSaleManagerMap.get(acc.Owner__c)!= null){
                acc.Sales_Group__c = sgToSaleManagerMap.get(acc.Owner__c);
            }
            if(opeSalesGroupSet.contains(acc.Sales_Group__c)){
                isDD = true;
            }
            if(acc.Sales_Group__c == 'SG28'){
                isFlexAutoOnly = true;
            }

            // If Closed, do nothing
            if ((acc.Customer_Business_Type__c == CCM_Constants.CUSTOMER_BUSINESS_TYPE_SALES_AND_SERVICE && oldMap.get(acc.Id).Customer_Business_Type__c == CCM_Constants.CUSTOMER_BUSINESS_TYPE_SALES) ||
                (acc.Customer_Business_Type__c == CCM_Constants.CUSTOMER_BUSINESS_TYPE_SALES && oldMap.get(acc.Id).Customer_Business_Type__c == CCM_Constants.CUSTOMER_BUSINESS_TYPE_SALES_AND_SERVICE) ||
                oldMap.get(acc.Id).Customer_Business_Type__c == null) {
                continue;
            }

            if (toUpdate) {
                Account oldItem = oldMap.get(acc.Id);
                if(isDD){
                    if(acc.Sales_Group__c != oldItem.Sales_Group__c){
                        String buyingGroupName = null;
                        buyingGroupName = buyingGroupMap.get(acc.Buying_Group__c);
                        // Prospect_Assignment_Rule__c theRule = findRuleBySales(l.OwnerId, l.Intended_Brand__c, l.BillingState, l.BillingCountry, l.Distributor_or_Dealer__c, rules, isFlexAutoOnly, l.Customer_Business_Type__c);
                        Prospect_Assignment_Rule__c theRule = findAssignmentRule(acc.OwnerId, acc.Intended_Brand__c, acc.BillingState, acc.BillingCountry, acc.Distributor_or_Dealer__c, buyingGroupName, rules, isFlexAutoOnly, acc.Customer_Business_Type__c,isDD,acc.Sales_Group__c);
                        if (theRule != null) {
                            acc.Director_Approver__c = theRule.Director__c;
                            acc.Customer_Cluster__c = theRule.Cluster__c;
                            acc.Customer_Sub_Cluster__c = theRule.Sub_Cluster__c;
                            acc.Sales_Channel__c = theRule.Sales_Channel__c;
                            acc.Sales_Group__c = theRule.Sales_Group__c;
                            acc.Assign_Msg__c = '';
                        } else {
                            acc.Assign_Msg__c = 'Cannot found assignment rule according to information your input, Please contact the system adminstrator.';
                        }
                        continue;
                    }
                }else{
                    // validate if its owner is manually changed, if Yes, not to do auto assginment
                    if (acc.OwnerId != oldItem.OwnerId ||
                        acc.BillingCountry != oldItem.BillingCountry || acc.BillingState != oldItem.BillingState || acc.Distributor_or_Dealer__c!= oldItem.Distributor_or_Dealer__c || acc.Intended_Brand__c != oldItem.Intended_Brand__c) {
                        /*if (String.isBlank(acc.Intended_Brand__c) || String.isBlank(acc.Distributor_or_Dealer__c) || String.isBlank(acc.BillingState) || String.isBlank(acc.BillingCountry) ) {
                            acc.Assign_Msg__c = 'Lack the' + (String.isBlank(acc.Intended_Brand__c) ? 'Intended Brand' : (String.isBlank(acc.Distributor_or_Dealer__c) ? 'Customer Type' : 'Billing Address')) + ' information for auto-assignment.';
                        }*/
                        if (String.isBlank(acc.Intended_Brand__c) || String.isBlank(acc.Distributor_or_Dealer__c) ) {
                            acc.addError('Lack of the intended brand or customer type information for auto-assignment.');
                        }
                        if(!secondTierCustomerType.contains(acc.Distributor_or_Dealer__c)) {
                            if (String.isBlank(acc.BillingState) || String.isBlank(acc.BillingCountry) ) {
                                acc.addError('Customer is lack of billing address on the customer detail page.');
                            }
                        }
                        String buyingGroupName = null;
                        buyingGroupName = buyingGroupMap.get(acc.Buying_Group__c);
                        // Prospect_Assignment_Rule__c theRule = findRuleBySales(l.OwnerId, l.Intended_Brand__c, l.BillingState, l.BillingCountry, l.Distributor_or_Dealer__c, rules, isFlexAutoOnly, l.Customer_Business_Type__c);
                        Prospect_Assignment_Rule__c theRule = findAssignmentRule(acc.OwnerId, acc.Intended_Brand__c, acc.BillingState, acc.BillingCountry, acc.Distributor_or_Dealer__c, buyingGroupName, rules, isFlexAutoOnly, acc.Customer_Business_Type__c,isDD,acc.Sales_Group__c);
                        if (theRule != null) {
                            acc.Director_Approver__c = theRule.Director__c;
                            acc.Customer_Cluster__c = theRule.Cluster__c;
                            acc.Customer_Sub_Cluster__c = theRule.Sub_Cluster__c;
                            acc.Sales_Channel__c = theRule.Sales_Channel__c;
                            acc.Sales_Group__c = theRule.Sales_Group__c;
                            acc.OwnerId =  theRule.User__c;
                            acc.Assign_Msg__c = '';
                            if (!isFlexAutoOnly && acc.OwnerId != oldItem.OwnerId) {
                                customerAssignmentMap.put(acc.Id, theRule);
                            }
                        } else {
                            acc.Assign_Msg__c = 'Cannot found assignment rule according to information your input, Please contact the system adminstrator.';
                        }
                        continue;
                    }
                }
            }
    	}
    }

    private Map<String, String> getBuyingGroupName(Set<String> buyingGroupSet) {
        Map<String, String> buyingGroupMap = new Map<String, String>();
        for(Account acc : [SELECT Id, Name FROM Account WHERE Id = :buyingGroupSet]) {
            buyingGroupMap.put(acc.Id, acc.Name);
        }
        return buyingGroupMap;
    }

}