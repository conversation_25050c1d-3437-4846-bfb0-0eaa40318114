<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>false</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Chervon_NA</submitter>
        <type>role</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Sales_Operation</submitter>
        <type>role</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Finance_NA</submitter>
        <type>roleSubordinates</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Sales_VP</submitter>
        <type>roleSubordinates</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Nanjing_BEAM_Manager</submitter>
        <type>roleSubordinatesInternal</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Owner</field>
        <field>Brands__c</field>
        <field>CurrencyIsoCode</field>
        <field>RecordType</field>
        <field>Is_Default__c</field>
        <field>Prospect_Customer__c</field>
        <field>Company__c</field>
        <field>State__c</field>
        <field>Price_Book_Mapping__c</field>
        <field>Contract_Price_List_Name__c</field>
        <field>Payment_Term__c</field>
        <field>Payment_Discount__c</field>
        <field>Starting_Date_of_Payment_Term__c</field>
        <field>Payment_Lead_Time__c</field>
        <field>Freight_Term__c</field>
        <field>Deliver_From__c</field>
        <field>Order_Lead_Time__c</field>
        <field>Show_Room__c</field>
        <field>Business_Volume_Potential__c</field>
        <field>Service__c</field>
        <field>Delivery__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Director_Approver__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Sales_Program__c.Prospect_Name__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Sales Director Approval</label>
        <name>Sales_Director_Approval</name>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Finance_Team</name>
                <type>queue</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Sales_Program__c.Customer_Type__c</field>
                <operation>notEqual</operation>
                <value>2nd Tier Dealer,2nd Tier Distributor</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>ApproveRecord</ifCriteriaNotMet>
        <label>Finance Approval</label>
        <name>Finance_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Sales_Program__c.Cluster__c</field>
                <operation>contains</operation>
                <value>CNA-CG20</value>
            </criteriaItems>
        </entryCriteria>
        <label>BEAM Manager</label>
        <name>BEAM_Manager</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <emailTemplate>Sales_Cloud/Program_Send_Approval</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <booleanFilter>1 AND 2</booleanFilter>
        <criteriaItems>
            <field>Sales_Program__c.RecordType</field>
            <operation>equals</operation>
            <value>Standard</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Program__c.ORG_Code__c</field>
            <operation>notEqual</operation>
            <value>CCA</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>FU_Clearing_Approval_Comments</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Program_Approved_Email</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Update_Service_Approval_Status_Approved</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Update_Service_Program_Approval_Time</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>false</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Program_Rejected_Email</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Update_Service_Approval_Status_Rejected</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Update_Program_Approval_Status_Pending</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Update_Program_Submit_Time</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>Program Sales Default Approval ProcessV7</label>
    <nextAutomatedApprover>
        <useApproverFieldOfRecordOwner>true</useApproverFieldOfRecordOwner>
        <userHierarchyField>approver__c</userHierarchyField>
    </nextAutomatedApprover>
    <processOrder>2</processOrder>
    <recordEditability>AdminOrCurrentApprover</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
