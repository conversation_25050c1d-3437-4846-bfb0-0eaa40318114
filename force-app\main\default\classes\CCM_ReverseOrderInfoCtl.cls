/**
 * @description: CCM_ReverseOrderInfoCtl
 * @Create Date: 2021-06-02
 * @revision: 2023-03-24 <PERSON> Add PO Number to Reverse Order Return Form
 */
public with sharing class CCM_ReverseOrderInfoCtl {

    public static String queryStr = 'SELECT Id, ' +
                                    ' Name, CurrencyIsoCode, ' +
                                    ' Org_Code__c, ' +
                                    ' Approval_Status__c, ' +
                                    ' External_Return_Reason__c, ' +
                                    // add haibo: french
                                    ' tolabel(External_Return_Reason__c) externalReturnReasonLabel, ' +
                                    ' Customer__c, ' +
                                    ' Customer__r.Name, ' +
                                    ' Customer__r.AccountNumber, ' +
                                    ' Customer_Contact_Email__c, ' +
                                    ' Remark__c, ' +
                                    ' BillTo__c, ' +
                                    ' BillTo_OracleID__c, ' +
                                    ' Billing_Address__c, ' +
                                    ' Billing_Address__r.Name, ' +
                                    ' Billing_Address__r.Address1__c, ' +
                                    ' Billing_Address__r.Address2__c, ' +
                                    ' Billing_Address__r.Country__c, ' +
                                    ' Billing_Address__r.State__c, ' +
                                    ' Billing_Address__r.City__c, ' +
                                    ' Billing_Address__r.Postal_Code__c, ' +
                                    ' Billing_Address__r.Contact__r.Name, ' +
                                    ' Billing_Address__r.Contact__r.Phone, ' +
                                    ' Billing_Address__r.Contact__r.Email, ' +
                                    ' Billing_Address__r.Contact__c, ' +
                                    ' Billing_Address__r.Contact2__c, ' +
                                    ' Billing_Address__r.Contact3__c, ' +
                                    ' Billing_Address__r.Contact4__c, ' +
                                    ' Billing_Address__r.Contact5__c, ' +
                                    ' ShipTo__c, ' +
                                    ' ShipTo_OracleID__c, ' +
                                    ' Shipping_Address__c, ' +
                                    ' Shipping_Address__r.Name, ' +
                                    ' Shipping_Address__r.Address1__c, ' +
                                    ' Shipping_Address__r.Address2__c, ' +
                                    ' Shipping_Address__r.Country__c, ' +
                                    ' Shipping_Address__r.State__c, ' +
                                    ' Shipping_Address__r.City__c, ' +
                                    ' Shipping_Address__r.Postal_Code__c, ' +
                                    ' Shipping_Address__r.Contact__r.Name, ' +
                                    ' Shipping_Address__r.Contact__r.Phone, ' +
                                    ' Shipping_Address__r.Contact__r.Email, ' +
                                    ' Shipping_Address__r.Dropship_Contact__c, ' +
                                    ' Shipping_Address__r.Dropship_Contact__r.Name, ' +
                                    ' Additional_Contact_Email__c, ' +
                                    ' Additional_Contact_Name__c, ' +
                                    ' Additional_Contact_Phone__c, ' +
                                    ' Additional_Shipping_City__c, ' +
                                    ' Additional_Shipping_Country__c, ' +
                                    ' Additional_Shipping_Postal_Code__c, ' +
                                    ' Additional_Shipping_Province__c, ' +
                                    ' Additional_Shipping_Street__c, ' +
                                    ' Additional_Shipping_Street2__c, ' +
                                    ' Is_Alternative_Address__c, ' +
                                    ' Purchase_Order__c, ' +
                                    ' Purchase_Order__r.Name, ' +
                                    ' Order__c, ' +
                                    ' Reverse_Order_Request_Number__c, ' +
                                    ' Original_Order_Number_in_SF__c, ' +
                                    ' Original_Customer_PO_Number__c, ' +
                                    ' Original_Order_Number_in_EBS__c, ' +
                                    ' Original_Order_Invoice_Number__c, ' +
                                    ' Return_Order_Number__c, ' +
                                    ' Credit_Invoice_Number__c, ' +
                                    ' Reverse_Order_Request_Start_Date__c, ' +
                                    ' Reverse_Order_Type__c, ' +
                                    // add haibo: french
                                    ' Reverse_Order_Request_Status__c, ' +
                                    ' tolabel(Reverse_Order_Request_Status__c) reverseOrderRequestStatusLabel, ' +
                                    ' Return_Goods_status__c, ' +
                                    ' tolabel(Return_Goods_status__c) returnGoodsStatusLabel, ' +
                                    ' Credit_Memo_Status__c, ' +
                                    ' tolabel(Credit_Memo_Status__c) creditMemoStatusLabel, ' +
                                    ' Sales_Group__c, ' +
                                    ' Inside_Sales__c, ' +
                                    ' Inside_Sales__r.Name, ' +
                                    ' Inside_Sales__r.FirstName, ' +
                                    ' Inside_Sales__r.LastName, ' +
                                    ' Description__c, ' +
                                    ' Model__c, ' +
                                    ' Invoice_Price__c, ' +
                                    ' Subtotal__c, ' +
                                    ' Product_Amount__c, ' +
                                    ' Claimed_Credit_Memo_Amount__c, ' +
                                    ' Tax__c, ' +
                                    ' Charges__c, ' +
                                    ' Total__c, ' +
                                    ' Date_Ordered__c, ' +
                                    ' Price_List__c, ' +
                                    ' Return_Goods_or_not__c, ' +
                                    ' Return_Order_Status__c, ' +
                                    ' Order_Type__c, ' +
                                    ' Line_Type__c, ' +
                                    ' Effect_Inventory__c, ' +
                                    ' Credit_Only__c, ' +
                                    ' Total_Credit_Memo_Amount__c, ' +
                                    ' Debit_Only__c, ' +
                                    ' Total_Debit_Memo_Amount__c, ' +
                                    ' CM_DM_Reason_Remark__c, ' +
                                    ' Confirmed_by_OPS__c, ' +
                                    ' Confirmed_by_OPS_Remark__c, ' +
                                    ' Total_Quantity__c, ' +
                                    ' Account_Manager__c, ' +
                                    ' CreatedById, ' +
                                    ' CreatedBy.Name, ' +
                                    ' CreatedBy.Email, ' +
                                    ' Return_Freight_Fee__c, ' +
                                    ' Return_Form_Date__c, ' +
                                    ' Overage_Buy_Tasks__c, ' +
                                    ' Overage_Reject_Tasks__c, ' +
                                    ' Delivery_Number__c, ' +
                                    ' Current_Approver__c, ' +
                                    ' Submitter__c, ' +
                                    ' Original_Invoice__c, ' +
                                    ' Shipment__c, ' +
                                    ' Original_Invoice__r.Invoice_Number__c, ' +
                                    ' Shipment__r.Ship_OracleID__c, ' +
                                    // Add carrier(shipper) and tracking order number to display on the page
                                    ' Shipment__r.Shipper__c, ' +
                                    ' Shipment__r.Tracking_Number__c, ' +
                                    ' (SELECT Id, ' +
                                            ' Brand__c, ' +
                                            ' Product2__c, ' +
                                            ' Product2__r.SF_Description__c, ' +
                                            // add haibo: product french
                                            ' Product2__r.SF_Description_French__c, ' +
                                            ' Product2__r.ProductCode, ' +
                                            ' Order_Item__c, ' +
                                            ' Order_Item__r.Selling_Warehouse__c, ' +
                                            ' Qty__c, ' +
                                            ' Unit__c, ' +
                                            ' Invoice_Price__c, ' +
                                            ' Item_Number__c, ' +
                                            ' Order_Product_Type__c, ' +
                                            ' Warehouse_Received_Qty__c, ' +
                                            ' Warehouse_Received_Subtotal__c, ' +
                                            ' Credit_Memo_Issued__c, ' +
                                            ' Next_Step_Action__c, ' +
                                            // add haibo: french
                                            ' tolabel(Next_Step_Action__c) nextStepActionLabel, ' +
                                            ' Warehouse_Return_Number__c, ' +
                                            ' Internal_Return_Reason__c ' +
                                        ' FROM Reverse_Order_Items__r), ' +
                                        ' (SELECT Id, ' +
                                            ' OrderNumber, ' +
                                            ' Order_Number__c ' +
                                        ' FROM Orders__r), ' +
                                        ' (SELECT Id, ' +
                                            ' Invoice_Number__c ' +
                                        ' FROM Invoices__r) ' +
                                    ' FROM Reverse_Order_Request__c ' +
                                    ' WHERE Is_Deleted__c = false';

    public static String orderQueryStr = 'SELECT Id, ' +
                                        ' AccountId, ' +
                                        ' Account.Name, ' +
                                        ' Account.Email__c, ' +
                                        ' Account.AccountNumber, ' +
                                        ' Org_Code__c, ' +
                                        ' CurrencyIsoCode, ' +
                                        ' BillTo__c, ' +
                                        ' BillTo__r.Account_Address__c, ' +
                                        ' BillTo__r.Account_Address__r.Address1__c, ' +
                                        ' BillTo__r.Account_Address__r.Address2__c, ' +
                                        ' BillTo__r.Account_Address__r.City__c, ' +
                                        ' BillTo__r.Account_Address__r.Country__c, ' +
                                        ' BillTo__r.Account_Address__r.State__c, ' +
                                        ' BillTo__r.Account_Address__r.Postal_Code__c, ' +
                                        ' BillTo__r.Account_Address__r.Contact__r.Name, ' +
                                        ' ShipTo__c, ' +
                                        ' ShipTo__r.Account_Address__c, ' +
                                        ' ShipTo__r.Account_Address__r.Address1__c, ' +
                                        ' ShipTo__r.Account_Address__r.Address2__c, ' +
                                        ' ShipTo__r.Account_Address__r.City__c, ' +
                                        ' ShipTo__r.Account_Address__r.Country__c, ' +
                                        ' ShipTo__r.Account_Address__r.State__c, ' +
                                        ' ShipTo__r.Account_Address__r.Postal_Code__c, ' +
                                        ' ShipTo__r.Account_Address__r.Contact__r.Name, ' +
                                        ' Purchase_Order__c,' +
                                        ' Order_Status__c, ' +
                                        ' Feright_Fee__c, ' +
                                        ' Order_Number__c, ' +
                                        ' (SELECT Id, ' +
                                                ' Brand__c,' +
                                                ' Order_Quantity__c,' +
                                                ' CurrencyIsoCode, ' +
                                                ' Hold_Reason__c, ' +
                                                ' Order__c, ' +
                                                ' OrderLine_OracleID__c, ' +
                                                ' Price_Book__c, ' +
                                                ' Product__c, ' +
                                                ' Purchase_Order_Item__c, ' +
                                                ' Request_Date__c, ' +
                                                ' Reverse_Quantity__c, ' +
                                                ' Ship_Date__c, ' +
                                                ' isHold__c, ' +
                                                ' Product__r.Short_description__c, ' +
                                                ' Product__r.ProductCode, ' +
                                                ' Product__r.SF_Description__c, ' +
                                                ' Product__r.Name, ' +
                                                // add haibo: product french
                                                ' Product__r.SF_Description_French__c, ' +
                                                ' Product__r.Product_Name_French__c, ' +
                                                ' Price__c ' +  // invoice price
                                            ' FROM Order_Items_Order__r WHERE Line_Status__c IN (\'INVOICED\', \'SHIPPED\', \'CLOSED\')) ' +
                                        ' FROM Order ' +
                                        ' WHERE RecordType.DeveloperName != \'Place_Parts_Order\' ' +
                                        ' AND Account.RecordType.DeveloperName = \'Channel\' ';

    /**
     * @description: get customer's reverse order by approval status
     */
    @AuraEnabled
    public static ResponseWrapper getReverseOrderNumberByStatus(String customerId){
        User currentUser = [SELECT Id, Contact.AccountId FROM User WHERE Id = :UserInfo.getUserId() ];
        customerId = currentUser.Contact.AccountId;
        ResponseWrapper response = new ResponseWrapper();
        try {
            AggregateResult[] results = [SELECT Count(Id) countId, Approval_Status__c FROM Reverse_Order_Request__c WHERE Customer__c = :customerId GROUP BY Approval_Status__c];

            CustomerWrapper objCustomer = new CustomerWrapper();
            for (AggregateResult result : results) {
                if (result.get('Approval_Status__c') == 'Rejected') {
                    objCustomer.rejectedReverseOrder = Integer.valueOf(result.get('countId'));
                }
                if (result.get('Approval_Status__c') == 'Recalled') {
                    objCustomer.recalledReverseOrder = Integer.valueOf(result.get('countId'));
                }
                if (result.get('Approval_Status__c') == 'Submitted for Approval' || result.get('Approval_Status__c') == 'OPS Approved') {
                    objCustomer.submittedReverseOrder = Integer.valueOf(result.get('countId'));
                }
                if (result.get('Approval_Status__c') == 'Approved') {
                    objCustomer.approvedReverseOrder = Integer.valueOf(result.get('countId'));
                }
            }
            response.isSuccess = true;
            response.returnData = objCustomer;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: get all help texts
     */
    @AuraEnabled
    public static ResponseWrapper getHelpTexts(){
        ResponseWrapper response = new ResponseWrapper();
        try {
            List<HelpTextWrapper> helpTexts = new List<HelpTextWrapper>();
            // next step action -> help text mapping
            List<Reverse_Order_Help_Text_Mapping__mdt> metaDatas = [SELECT MasterLabel,
                                                                            Order_NO__c,
                                                                            Request_Reason__c,
                                                                            // add haibo: french
                                                                            toLabel(Request_Reason__c) requestReasonLabel,
                                                                            toLabel(Next_Step_Action__c) nextStepActionLabel,
                                                                            Next_Step_Action__c,
                                                                            Help_Text_Content__c,
                                                                            Help_Text_Content_French__c,
                                                                            Product_Type__c
                                                                            FROM Reverse_Order_Help_Text_Mapping__mdt
                                                                            ORDER BY Product_Type__c, Order_NO__c];
            for( Reverse_Order_Help_Text_Mapping__mdt item : metaDatas){

                HelpTextWrapper wrapper = new HelpTextWrapper();
                wrapper.RequestReason   = item.Request_Reason__c;
                // add haibo: french
                wrapper.RequestReasonFrench   = String.valueOf(item.get('requestReasonLabel'));
                wrapper.NextStepAction  = item.Next_Step_Action__c;
                wrapper.NextStepActionFrench  = String.valueOf(item.get('nextStepActionLabel'));
                if(UserInfo.getLanguage() == Label.CCM_Portal_French){
                    wrapper.HelpTextContent = item.Help_Text_Content_French__c;
                }else{
                    wrapper.HelpTextContent = item.Help_Text_Content__c;
                }
                wrapper.productType     = item.Product_Type__c;
                helpTexts.add(wrapper);
            }
            response.currentUserProfile = [SELECT Name FROM Profile WHERE Id = :UserInfo.getProfileId() LIMIT 1].Name;
            response.isSuccess = true;
            response.returnData = helpTexts;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: get products
     */
    @AuraEnabled
    public static ResponseWrapper getProducts(String customerId, String keyWord, String purchaseOrderId) {
        if (String.isEmpty(customerId) || String.isEmpty(keyWord)) {
            return null;
        }

        ResponseWrapper response = new ResponseWrapper();
        try {
            keyWord = '%' + keyWord + '%';

            Set<String> pricebookIds = new Set<String>();
            Set<String> contractOracleIds = new Set<String>();
            Set<String> pricebookOracleIds = new Set<String>();
            for (Sales_Program__c program : [SELECT Id, Price_Book__c, Price_Book__r.Price_Book_OracleID__c, Price_Book__r.Contract_Price_Book_OracleID__c
                                                FROM Sales_Program__c
                                                WHERE Approval_Status__c = 'Approved'
                                                AND Customer__r.Id != null
                                                AND Price_Book__r.IsActive = true
                                                AND Customer__c = :customerId
                                                AND RecordType.DeveloperName != 'Service'
                                                AND RecordTypeId != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_ID
                                                AND RecordTypeId != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_ID ]) {
                pricebookIds.add(program.Price_Book__c);

                if (String.isNotEmpty(program.Price_Book__r.Contract_Price_Book_OracleID__c)) {
                    contractOracleIds.addAll(program.Price_Book__r.Contract_Price_Book_OracleID__c.split(','));
                }
            }
            List<PricebookEntry> contractPBEList = new List<PricebookEntry>();
            // get product from contract price book
            if (contractOracleIds.size() > 0) {
                contractPBEList = [SELECT Id,
                                        Pricebook2Id,
                                        Product2Id,
                                        Product2.Name,
                                        // add haibo: product french
                                        Product2.Product_Name_French__c,
                                        Product2.ProductCode,
                                        Product2.SF_Description__c,
                                        Product2.Brand_Name__c,
                                        Product2.Source__c,
                                        UnitPrice
                                    FROM PricebookEntry
                                    WHERE isActive = true
                                    AND product2.RecordType.DeveloperName != 'Parts'
                                    AND (Product2.SF_Description__c LIKE :keyWord OR Product2.Description LIKE :keyWord)
                                    AND Pricebook2Id IN (SELECT Id FROM Pricebook2 WHERE Price_Book_OracleID__c IN :contractOracleIds)
                                    AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                    ORDER BY Product2.Source__c DESC
                                    LIMIT 100];
            }

            List<PricebookEntry> pricebookEntryList = new List<PricebookEntry>();

            List<PricebookEntry> authPBEList = [SELECT Id,
                                                    Pricebook2Id,
                                                    Product2Id,
                                                    Product2.Name,
                                                    // add haibo: product french
                                                    Product2.Product_Name_French__c,
                                                    Product2.ProductCode,
                                                    Product2.SF_Description__c,
                                                    Product2.Brand_Name__c,
                                                    Product2.Source__c,
                                                    UnitPrice
                                                FROM PricebookEntry
                                                WHERE isActive = true
                                                AND product2.RecordType.DeveloperName != 'Parts'
                                                AND (Product2.SF_Description__c LIKE :keyWord OR Product2.Description LIKE :keyWord)
                                                AND Pricebook2Id IN :pricebookIds
                                                AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                                ORDER BY Product2.Source__c DESC
                                                LIMIT 100];

            if (contractPBEList.size() > 0) {
                Set<String> productCodeSet = new Set<String>();
                for (PricebookEntry pbe : authPBEList) {
                    productCodeSet.add(pbe.Product2.ProductCode);
                }

                for (PricebookEntry pbe : contractPBEList) {
                    if (!productCodeSet.contains(pbe.Product2.ProductCode)) {
                        System.debug(LoggingLevel.INFO, '**** pricebookEntry: ' + pbe);
                        authPBEList.add(pbe);
                        // add contract pricebook id
                        pricebookIds.add(pbe.Pricebook2Id);
                    }
                }
            }
            if (authPBEList.size() > 0) {
                pricebookEntryList = authPBEList;
            } else {
                pricebookEntryList = [SELECT Id,
                                        Pricebook2Id,
                                        Product2Id,
                                        Product2.Name,
                                        // add haibo: product french
                                        Product2.Product_Name_French__c,
                                        Product2.ProductCode,
                                        Product2.SF_Description__c,
                                        Product2.Brand_Name__c,
                                        Product2.Source__c,
                                        UnitPrice
                                    FROM PricebookEntry
                                    WHERE isActive = true
                                    AND product2.RecordType.DeveloperName != 'Parts'
                                    AND (Product2.SF_Description__c LIKE :keyWord OR Product2.Description LIKE :keyWord)
                                    AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                    ORDER BY Product2.Source__c DESC
                                    LIMIT 100];
            }

            // PIM product , EBS Product
            Map<String,ProductWrapper> wrapperList = new Map<String,ProductWrapper>();

            Set<String> PIMProducts = new Set<String>();
            Set<String> EBSProducts = new Set<String>();
            for (PricebookEntry entry : pricebookEntryList) {
                if (PIMProducts.contains(entry.Product2.ProductCode)) {
                    continue;
                }
                if (EBSProducts.contains(entry.Product2.ProductCode)) {
                    continue;
                }
                if (entry.Product2.Source__c == 'PIM') {
                    PIMProducts.add(entry.Product2.ProductCode);
                } else if (entry.Product2.Source__c == 'EBS') {
                    EBSProducts.add(entry.Product2.ProductCode);
                }
                ProductWrapper wrapper = wrapperList.get(entry.Product2Id);
                if(wrapper != null){
                    if (pricebookIds.contains(entry.Pricebook2Id)) {
                        wrapper.invoicePrice = entry.UnitPrice;
                    }
                }else{
                    wrapper = new ProductWrapper();
                    wrapper.recordId    = entry.Product2Id;
                    wrapper.productId    = entry.Product2Id;
                    wrapper.productName = entry.Product2.Name;
                    // add haibo: product french
                    if(UserInfo.getLanguage() == Label.CCM_Portal_French){
                        wrapper.productName = entry.Product2.Product_Name_French__c;
                    }else {
                        wrapper.productName = entry.Product2.Name;
                    }
                    wrapper.productCode = entry.Product2.ProductCode;
                    wrapper.description = entry.Product2.SF_Description__c;
                    wrapper.brand       = entry.Product2.Brand_Name__c;
                    wrapper.unit        = 'EA';
                    if (pricebookIds.contains(entry.Pricebook2Id)) {
                        wrapper.invoicePrice = entry.UnitPrice;
                    }
                    wrapperList.put(entry.Product2Id,wrapper);
                }
            }
            System.debug('*** ' + wrapperList.values());
            response.isSuccess = true;
            response.returnData = wrapperList.values();
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;

    }

    /**
     * @description: get product price from price book
     */
    @AuraEnabled
    public static ResponseWrapper getProductPrice(String prodId, String customerId, String purchaseOrderId) {
        ResponseWrapper response = new ResponseWrapper();
        try {
            if ([SELECT Id, ShipTo__r.Address_Type__c FROM Order WHERE Id = :purchaseOrderId].ShipTo__r.Address_Type__c.contains('Dropship')) {
                response = getDropShipInternalPriceBook(prodId, customerId);
            } else {
                response = getInternalPriceBook(prodId, customerId);
            }
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage() ;
        }
        return response;
    }

    /**
     * @description: get product's price
     */
    @AuraEnabled
    public static ResponseWrapper getInternalPriceBook(String prodId, String customerId){
        ResponseWrapper response = new ResponseWrapper();
        try {
            List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
            Account acc = [SELECT Id, ORG_Code__c FROM Account WHERE Id = :customerId LIMIT 1];
            String orgCode = String.isEmpty(acc.ORG_Code__c) ? CCM_Constants.ORG_CODE_CNA : acc.ORG_Code__c;

            ProductWrapper wrapper = new ProductWrapper();
            if (String.isNotBlank(prodId)){
                List<Product2> prodInfo = [SELECT Id, Brand_Name__c, Item_Number__c, ProductCode, Weight__c, OverSize__c FROM Product2 WHERE Id =:prodId];
                if (prodInfo != null && prodInfo.size() > 0){

                    if (String.isNotBlank(customerId)){
                        List<Sales_Program__c> authBrandList = [SELECT Id, Customer__r.ORG_Code__c, Customer__c, Brands__c, Customer__r.Distributor_or_Dealer__c,
                                                                    Approval_Status__c, IsDeleted, Price_Book_Mapping__r.Name,
                                                                    Price_Book__c, Contract_Price_List_Name__c, Contract_Price_Book__c
                                                                FROM Sales_Program__c
                                                                WHERE Customer__c =: customerId
                                                                AND Approval_Status__c = 'Approved'
                                                                AND Brands__c =: prodInfo[0].Brand_Name__c
                                                                AND IsDeleted = false LIMIT 1];
                        if (authBrandList != null && authBrandList.size() > 0){

                            Sales_Program__c authBrandInfo = authBrandList[0];
                            Boolean isContract = false;
                            if (authBrandInfo.Price_Book_Mapping__r.Name == 'Contract Price List' && String.isNotBlank(authBrandInfo.Contract_Price_Book__c)){
                                isContract = true;
                            }
                            //更新特殊客户有主副价格册的逻辑
                            prodEntryList = Util.getPriceBookEntryByProdId(prodId, authBrandInfo.Price_Book__c, isContract);
                            System.debug(LoggingLevel.INFO, '*** prodEntryList TEST: ' + prodEntryList);
                            //如果当前的产品不再当前的价格册里，就去查找当前brand & Customer Type下的标准价格册（取Customize 和 Standard的合集产品）
                            if (prodEntryList == null || prodEntryList.size() == 0){
                                prodEntryList = Util.getStandardPricebookEntryByProdId(prodId, authBrandInfo.Brands__c, authBrandInfo.Customer__r.Distributor_or_Dealer__c,'Sales');
                                System.debug(LoggingLevel.INFO, '*** prodEntry2: ' + prodEntryList);
                            }

                            if (prodEntryList != null && prodEntryList.size() > 0){
                                PricebookEntry entry    = prodEntryList[0];
                                wrapper.productId        = entry.Product2Id;
                                wrapper.invoicePrice    = entry.UnitPrice;
                            } else {
                                if (orgCode == CCM_Constants.ORG_CODE_CNA) {
                                    // get price from msrp pricebook
                                    Map<String, String> brandPriceBookMap = new Map<String, String>{'EGO' => 'CNA-EGO-MSRP', 'SKIL' => 'CNA-SKIL-MSRP', 'SKILSAW' => 'CNA-SKIL-MSRP', 'FLEX' => 'CNA-FLEX-MSRP'};
                                    String pricebookName = brandPriceBookMap.get(prodInfo[0].Brand_Name__c.toUpperCase());
                                    List<PricebookEntry> pbeList = [SELECT Id, UnitPrice, Product2Id FROM PricebookEntry WHERE Pricebook2.Name = :pricebookName AND Product2Id = :prodId];
                                    wrapper.productId = pbeList[0].Product2Id;
                                    wrapper.invoicePrice = pbeList[0].UnitPrice;

                                    System.debug('**** price from msrp pricebook: ' + wrapper);
                                } else {
                                    // get price from msrp pricebook
                                    List<PricebookEntry> pbeList = [SELECT Id, UnitPrice, Product2Id FROM PricebookEntry WHERE Pricebook2.Name = 'CA MSRP Price List' AND Product2Id = :prodId];
                                    if (pbeList.size() > 0) {
                                        wrapper.productId = pbeList[0].Product2Id;
                                        wrapper.invoicePrice = pbeList[0].UnitPrice;
                                        System.debug('**** price from msrp pricebook: ' + wrapper);
                                    } else {
                                        response.isSuccess = false;
                                        response.errorMsg = 'At least one of the prices of overaged products is not found!';
                                        return response;
                                    }
                                }

                            }
                        } else {
                            if (orgCode == CCM_Constants.ORG_CODE_CNA) {
                                // get price from msrp pricebook
                                Map<String, String> brandPriceBookMap = new Map<String, String>{'EGO' => 'CNA-EGO-MSRP', 'SKIL' => 'CNA-SKIL-MSRP', 'SKILSAW' => 'CNA-SKIL-MSRP', 'FLEX' => 'CNA-FLEX-MSRP'};
                                String pricebookName = brandPriceBookMap.get(prodInfo[0].Brand_Name__c.toUpperCase());
                                List<PricebookEntry> pbeList = [SELECT Id, UnitPrice, Product2Id FROM PricebookEntry WHERE Pricebook2.Name = :pricebookName AND Product2Id = :prodId];
                                wrapper.productId = pbeList[0].Product2Id;
                                wrapper.invoicePrice = pbeList[0].UnitPrice;

                                System.debug('**** price from msrp pricebook: ' + wrapper);
                            } else {
                                // get price from msrp pricebook
                                List<PricebookEntry> pbeList = [SELECT Id, UnitPrice, Product2Id FROM PricebookEntry WHERE Pricebook2.Name = 'CA MSRP Price List' AND Product2Id = :prodId];
                                if (pbeList.size() > 0) {
                                    wrapper.productId = pbeList[0].Product2Id;
                                    wrapper.invoicePrice = pbeList[0].UnitPrice;
                                    System.debug('**** price from msrp pricebook: ' + wrapper);
                                } else {
                                    response.isSuccess = false;
                                    response.errorMsg = 'At least one of the prices of overaged products is not found!';
                                    return response;
                                }
                            }
                        }
                    }
                }
            }
            response.isSuccess = true;
            response.returnData = wrapper;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }


    /**
     * @description: get dropship pricebook
     */
    @AuraEnabled
    public static ResponseWrapper getDropShipInternalPriceBook(String prodId, String customerId){
        ResponseWrapper response = new ResponseWrapper();
        try {
            List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
            ProductWrapper wrapper = new ProductWrapper();

            if (String.isNotBlank(prodId)){
                List<Product2> prodInfo = [SELECT Id, Brand_Name__c,Item_Number__c,ProductCode,Weight__c,OverSize__c FROM Product2 WHERE Id =:prodId];
                if (prodInfo != null && prodInfo.size() > 0){
                    String brandName = prodInfo[0].Brand_Name__c;
                    prodEntryList = Util.getDropShipPricebookEntryByProdId(prodId, brandName, customerId);
                    System.debug(LoggingLevel.INFO, '*** prodEntryList: ' + JSON.serialize(prodEntryList));
                    if (prodEntryList != null && prodEntryList.size() > 0){
                        PricebookEntry entry    = prodEntryList[0];
                        wrapper.productId        = entry.Product2Id;
                        wrapper.invoicePrice    = entry.UnitPrice;
                    } else {
                        // get price from msrp pricebook
                        Map<String, String> brandPriceBookMap = new Map<String, String>{'EGO' => 'CNA-EGO-MSRP', 'SKIL' => 'CNA-SKIL-MSRP', 'SKILSAW' => 'CNA-SKIL-MSRP', 'FLEX' => 'CNA-FLEX-MSRP'};
                        String pricebookName = brandPriceBookMap.get(prodInfo[0].Brand_Name__c.toUpperCase());
                        List<PricebookEntry> pbeList = [SELECT Id, UnitPrice, Product2Id FROM PricebookEntry WHERE Pricebook2.Name = :pricebookName AND Product2Id = :prodId];
                        wrapper.productId = pbeList[0].Product2Id;
                        wrapper.invoicePrice = pbeList[0].UnitPrice;

                        System.debug('**** price from msrp pricebook: ' + wrapper);
                    }
                }
            }
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: get all help texts
     */
    @AuraEnabled
    public static ResponseWrapper getOrder(String orderId, String ebsOrderNo){

        ResponseWrapper response = new ResponseWrapper();
        try {
            User currentUser = [SELECT Id, Contact.AccountId, Profile.Name, UserRole.Name, UserRole.DeveloperName FROM User WHERE Id = :UserInfo.getUserId()];
            // Get the current user role, according to the first three digits, it is CcA or CNA
            String str = currentUser.UserRole.DeveloperName;
            String userRole = '';
            if(str != null && str != '') {
                if(str.length() > 4) {
                    userRole = str.substring(0, 4);
                }
            }

            Map<String, CA_Tax_Rate_By_Province__mdt> rateMap = Util.getCanadaTaxRateMap();
            List<TaxRateWrapper> taxRates = new List<TaxRateWrapper>();
            for (CA_Tax_Rate_By_Province__mdt tax : rateMap.values()) {
                TaxRateWrapper taxRate = new TaxRateWrapper();
                taxRate.State   = tax.Province_Code__c;
                taxRate.GST     = tax.GST__c / 100;
                taxRate.HST     = tax.HST__c / 100;
                taxRate.PST     = tax.PST__c / 100;
                taxRate.QST     = tax.QST__c / 100;
                taxRate.TotalRate = tax.Total_Rate__c;
                taxRates.add(taxRate);
            }
            Boolean customerHasPortalUser = false;
            String portalUserEmail = '';
            if(String.isNotEmpty(orderId)){
                orderQueryStr += ' AND Id = :orderId';
            }
            if (String.isNotBlank(userRole) && userRole.substring(0, 3) == 'CA_') {
                orderQueryStr += ' AND Org_Code__c = \'' + CCM_Constants.ORG_CODE_CCA + '\' ';
            }
            if (String.isNotBlank(userRole) && userRole.substring(0, 3) == 'NA_' && userRole.substring(0, 4) != 'NA_A') {
                orderQueryStr += ' AND Org_Code__c = \'' + CCM_Constants.ORG_CODE_CNA + '\' ';
            }
            if(String.isNotEmpty(ebsOrderNo)){
                orderQueryStr += ' AND Order_Number__c = :ebsOrderNo';
                orderQueryStr += ' AND Order_Status__c IN (\'Partial Shipment\', \'Ship Complete\') ';
            }
            List<Order> orderList = CCM_ReverseOrderUtil.getOrder(orderQueryStr,orderId,ebsOrderNo);

            if (orderList.size() == 0) {
                throw new QueryException('No order found!');
            }

            Order objOrder = orderList[0];

            Boolean isACE = false;
            if(objOrder.Account.AccountNumber == '0376') {
                isACE = true;
            }

            List<InvoiceWrapper> invoiceWrapperList = new List<InvoiceWrapper>();
            invoiceWrapperList.add(new InvoiceWrapper());
            List<Invoice__c> invoiceList = [SELECT Id, Customer__c, Delivery_Number__c, Invoice_Date__c, Invoice_Number__c, Invoice_OracleID__c, Order_Number__c, Order_Type__c, Order__c, PO_Number__c, Shipment__c, Tracking_NO__c,
                                                (SELECT Id, Brand__c, Catalog_Item_Text__c, Catalog_Item__c, Customer__c, Description__c, Price__c, Qty_Extended__c, Product_Code__c FROM Invoice_Items__r)
                                                FROM Invoice__c WHERE Order__c = :objOrder.Id];
            for (Invoice__c inv : invoiceList) {
                InvoiceWrapper invWrapper = new InvoiceWrapper();
                invWrapper.recordId = inv.Id;
                invWrapper.invoiceNumber = inv.Invoice_Number__c;
                invWrapper.deliveryNumber = inv.Delivery_Number__c;
                invWrapper.shipment = inv.Shipment__c;
                for (Invoice_Item__c item : inv.Invoice_Items__r) {
                    InvoiceItemWrapper itemWrapper = new InvoiceItemWrapper();
                    itemWrapper.recordId = item.Id;
                    invWrapper.invoiceItemList.add(itemWrapper);
                }
                invoiceWrapperList.add(invWrapper);
            }

            String profileName = currentUser.Profile.Name;
            String userRoleName = currentUser.UserRole.Name;
            Integer shipDays = 0;
            if ([SELECT Id, Name FROM Profile WHERE Id = :UserInfo.getProfileId()].Name.contains('Community')) {
                shipDays = 30;
            } else if (Label.Specify_Reverse_Order_Submit_Profile == profileName || (Label.Specify_Reverse_Order_Submit_User.contains(UserInfo.getName()) && (profileName == 'NA Inside Sales' || userRoleName == 'CA Inside Sales'))) {
                shipDays = 999;
            }  else {
                shipDays = 45;
            }
            List<Shipment__c> shipments = [SELECT Id, Account_Number__c, Customer_PO_Number__c, Customer__c, Order_Number__c, Order__c, Ship_Date__c, Ship_Method__c, Ship_OracleID__c, Tracking_Number__c, ORG_Code__c,
                                                    (SELECT Id, Item_Quantity_in_Order__c, Item_Quantity_in_Shipment__c, Product_Text__c, Product__c, Shipment_Item_OracleID__c, Shipment__c
                                                        FROM Shipment_Items__r)
                                                FROM Shipment__c Where Order__c = :objOrder.Id];
            Map<String,ShipmentItemWrapper> shipmentItemWrappers = new Map<String,ShipmentItemWrapper>();
            List<ShipmentWrapper> shipmentList = new List<ShipmentWrapper>();
            shipmentList.add(new ShipmentWrapper());
            if (shipments.size() > 0) {
                for (Shipment__c shipmentItem : shipments) {
                    ShipmentWrapper sw  = new ShipmentWrapper();
                    sw.recordId         = shipmentItem.Id;
                    sw.shipmentNumber   = shipmentItem.Ship_OracleID__c;
                    sw.canReverse       = shipmentItem.Ship_Date__c.daysBetween(Date.today()) <= shipDays;
                    sw.shipDays         = shipDays;
                    for (Shipment_Item__c item : shipmentItem.Shipment_Items__r) {
                        ShipmentItemWrapper shipmentItemWrapper = shipmentItemWrappers.get(item.Product__c);
                        if(shipmentItemWrapper != null){
                            shipmentItemWrapper.itemQuantityInShipment += item.Item_Quantity_in_Shipment__c == null ? 0 : item.Item_Quantity_in_Shipment__c;
                        }else{
                            ShipmentItemWrapper itemwrapper     = new ShipmentItemWrapper();
                            itemWrapper.productId               = item.Product__c;
                            itemwrapper.itemQuantityInOrder     = item.Item_Quantity_in_Order__c == null ? 0 : item.Item_Quantity_in_Order__c;
                            itemwrapper.itemQuantityInShipment  = item.Item_Quantity_in_Shipment__c == null ? 0 : item.Item_Quantity_in_Shipment__c;
                            shipmentItemWrappers.put(item.Product__c,itemWrapper);
                        }

                        ShipmentItemWrapper itemWrapper = new ShipmentItemWrapper();
                        itemWrapper.productId               = item.Product__c;
                        itemwrapper.itemQuantityInOrder     = item.Item_Quantity_in_Order__c == null ? 0 : item.Item_Quantity_in_Order__c;
                        itemwrapper.itemQuantityInShipment  = item.Item_Quantity_in_Shipment__c == null ? 0 : item.Item_Quantity_in_Shipment__c;
                        sw.shipItemList.add(itemWrapper);
                    }
                    shipmentList.add(sw);
                }
            }

            if (objOrder.Order_Status__c != 'Partial Shipment' && objOrder.Order_Status__c != 'Ship Complete'){
                throw new QueryException('The Order in current status cannot create reverse order!');
            }
            OrderWrapper objOrderWrapper = new OrderWrapper();
            objOrderWrapper.invoiceList         = invoiceWrapperList;
            objOrderWrapper.shipmentList        = shipmentList;
            objOrderWrapper.orgCode             = String.isEmpty(objOrder.ORG_Code__c) ? CCM_Constants.ORG_CODE_CNA : objOrder.ORG_Code__c;
            objOrderWrapper.taxRates            = taxRates;
            System.debug('*** tax Rates: ' + objOrderWrapper.taxRates);
            objOrderWrapper.orderId             = objOrder.Id;
            objOrderWrapper.currencyCode        = objOrder.CurrencyIsoCode;
            objOrderWrapper.orderNumber         = objOrder.Order_Number__c;
            objOrderWrapper.customerId          = objOrder.AccountId;
            objOrderWrapper.customerName        = objOrder.Account.Name;
            objOrderWrapper.customerEmail       = objOrder.Account.Email__c;
            objOrderWrapper.customerNumber      = objOrder.Account.AccountNumber;
            objOrderWrapper.purchaseOrderId     = objOrder.Purchase_Order__c;
            objOrderWrapper.billingAddressId    = objOrder.BillTo__c;
            objOrderWrapper.freightFee          = objOrder.Feright_Fee__c;
            if (objOrder.BillTo__c != null && objOrder.BillTo__r.Account_Address__c != null) {
                AddressWrapper billTo   = new AddressWrapper();
                billTo.addressId        = objOrder.BillTo__r.Account_Address__c;
                billTo.address1         = objOrder.BillTo__r.Account_Address__r.Address1__c;
                billTo.address2         = objOrder.BillTo__r.Account_Address__r.Address2__c;
                billTo.city             = objOrder.BillTo__r.Account_Address__r.City__c;
                billTo.country          = objOrder.BillTo__r.Account_Address__r.Country__c;
                billTo.state            = objOrder.BillTo__r.Account_Address__r.State__c;
                billTo.postalCode       = objOrder.BillTo__r.Account_Address__r.Postal_Code__c;
                billTo.contactName      = objOrder.BillTo__r.Account_Address__r.Contact__r.Name;
                objOrderWrapper.billTo  = billTo;
            }

            objOrderWrapper.shippingAddressId = objOrder.ShipTo__c;
            if (objOrder.ShipTo__c != null && objOrder.ShipTo__r.Account_Address__c != null) {
                AddressWrapper shipTo   = new AddressWrapper();
                shipTo.addressId        = objOrder.ShipTo__r.Account_Address__c;
                shipTo.address1         = objOrder.ShipTo__r.Account_Address__r.Address1__c;
                shipTo.address2         = objOrder.ShipTo__r.Account_Address__r.Address2__c;
                shipTo.city             = objOrder.ShipTo__r.Account_Address__r.City__c;
                shipTo.country          = objOrder.ShipTo__r.Account_Address__r.Country__c;
                shipTo.state            = objOrder.ShipTo__r.Account_Address__r.State__c;
                shipTo.postalCode       = objOrder.ShipTo__r.Account_Address__r.Postal_Code__c;
                shipTo.contactName      = objOrder.ShipTo__r.Account_Address__r.Contact__r.Name;
                objOrderWrapper.shipTo  = shipTo;
            }

            List<OrderItemWrapper> itemWrappers = new List<OrderItemWrapper>();
            for (Order_Item__c item : objOrder.Order_Items_Order__r) {
                OrderItemWrapper itemwrapper    = new OrderItemWrapper();
                itemwrapper.orderItemId         = item.Id;
                itemWrapper.productId           = item.Product__c;
                // add haibo: product french
                if(UserInfo.getLanguage() == Label.CCM_Portal_French){
                    itemwrapper.productName = item.Product__r.Product_Name_French__c;
                }else{
                    itemwrapper.productName = item.Product__r.Name;
                }
                itemwrapper.productCode         = item.Product__r.ProductCode;
                itemwrapper.brand               = item.Brand__c;
                itemwrapper.unit                = 'EA'; //TODO
                itemwrapper.invoicePrice        = item.Price__c;
                itemwrapper.orderItemQuantity   = item.Order_Quantity__c;
                itemwrapper.productDescription  = item.Product__r.SF_Description__c;
                itemwrappers.add(itemWrapper);
            }

            objOrderWrapper.orderItems = itemWrappers;
            objOrderWrapper.shipmentItems = shipmentItemWrappers.values();

            // Find the portal user corresponding to customer
            List<Contact> contacts = [SELECT Id, (SELECT Id, Email FROM Users WHERE Profile.Name like 'Partner Community%' LIMIT 1)  FROM Contact WHERE AccountId = :objOrderWrapper.customerId];
            for (Contact contact : contacts) {
                if(contact.Users != null && contact.Users.size()>0){
                    customerHasPortalUser = true;
                    portalUserEmail = contact.Users[0].Email;
                }
            }

            objOrderWrapper.customerHasPortalUser = customerHasPortalUser;
            if(isACE) {
                objOrderWrapper.portalUserEmail = System.Label.Reverse_Order_ACE_Email;
            }
            else {
                objOrderWrapper.portalUserEmail = portalUserEmail;
            }

            response.returnData = objOrderWrapper;
            response.isSuccess = true;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description getInvoice with shipment info
     * @param  orderId     orderId description
     * @param  orderItemId orderItemId description
     * @return             return description
     */
    @AuraEnabled
    public static ResponseWrapper getInvoice(String orderId, String orderItemId) {
        ResponseWrapper response = new ResponseWrapper();
        Integer shipDays = 0;
        Profile objProfile = [SELECT Id, Name FROM Profile WHERE Id = :UserInfo.getProfileId()];
        UserRole role = [SELECT Name FROM UserRole WHERE Id = :UserInfo.getUserRoleId() LIMIT 1];
        if (objProfile.Name.contains('Community')) {
            shipDays = Integer.valueOf(Label.Reverse_Order_Ship_Date);
        } else if (objProfile.Name == 'BEAM' || objProfile.Name.contains('System Admin') || (Label.Specify_Reverse_Order_Submit_User.contains(UserInfo.getName()) && (objProfile.Name == 'NA Inside Sales' || role.Name == 'CA Inside Sales'))) {
            shipDays = Integer.valueOf(Label.Reverse_Order_Ship_Days_For_BEAM);
        } else {
            shipDays = Integer.valueOf(Label.Reverse_Order_Ship_Days_For_Inner_User);
        }
        try {
            String productCode = '';
            if (String.isNotBlank(orderItemId)) {
                productCode = CCM_ReverseOrderUtil.getProductCodeByOrderItemId(orderItemId);
            }
            String sql = 'SELECT Id, Customer__c, Delivery_Number__c, Invoice_Number__c, Invoice_OracleID__c, Order_Number__c, Order_Type__c, Order__c, PO_Number__c, Shipment__c, ' +
                        ' (SELECT Id, Brand__c, Catalog_Item_Text__c, Catalog_Item__c, Customer__c, Description__c, Price__c, Qty_Extended__c, Product_Code__c FROM Invoice_Items__r ';
            if (String.isNotBlank(productCode)) {
                sql += ' WHERE Product_Code__c = \'' + productCode + '\' ';
            }
            sql += ')  FROM Invoice__c WHERE Order__c  = \'' + orderId + '\' ';
            List<InvoiceWrapper> invoiceWrapperList = new List<InvoiceWrapper>();
            Set<String> deliveryNumbers = new Set<String>();
            for (Invoice__c inv : CCM_ReverseOrderUtil.getInvoiceAndItems(sql)) {
                if (inv.Invoice_Items__r.size() > 0) {
                    if (String.isNotEmpty(inv.Delivery_Number__c)) {
                        deliveryNumbers.addAll(inv.Delivery_Number__c.split(','));
                    }
                    InvoiceWrapper invWrapper = new InvoiceWrapper();
                    invWrapper.recordId = inv.Id;
                    invWrapper.invoiceNumber = inv.Invoice_Number__c;
                    invWrapper.deliveryNumber = inv.Delivery_Number__c;
                    invWrapper.shipment = inv.Shipment__c;
                    for (Invoice_Item__c item : inv.Invoice_Items__r) {
                        InvoiceItemWrapper itemWrapper = new InvoiceItemWrapper();
                        itemWrapper.recordId = item.Id;
                        invWrapper.invoiceItemList.add(itemWrapper);
                    }
                    invoiceWrapperList.add(invWrapper);
                }
            }

            if (deliveryNumbers.size() > 0) {
                List<String> dnList = new List<String>();
                dnList.addAll(deliveryNumbers);
                String deliveryNumberStr = '(\'' + String.join(dnList, '\',\'') + '\')';
                String shipSQL = 'SELECT Id, Account_Number__c, Customer_PO_Number__c, Customer__c, Order_Number__c, Order__c, Ship_Date__c, Ship_OracleID__c, Tracking_Number__c, ORG_Code__c, ' +
                                '(SELECT Id, Item_Quantity_in_Order__c, Item_Quantity_in_Shipment__c, Product_Text__c, Product__c, Shipment_Item_OracleID__c, Shipment__c  FROM Shipment_Items__r ';
                if (String.isNotBlank(productCode)) {
                    shipSQL += ' WHERE Product__r.ProductCode = \'' + productCode + '\' ';
                }
                shipSQL += ' ) FROM Shipment__c WHERE Order__c = \'' + orderId + '\' AND Ship_OracleID__c IN ' + deliveryNumberStr;
                System.debug('**** shipSQL: ' + shipSQL);
                for (InvoiceWrapper inv : invoiceWrapperList) {
                    for (Shipment__c item : CCM_ReverseOrderUtil.getShipmentWithShipmentItem(shipSQL)) {
                        if (item.Shipment_Items__r.size() > 0) {
                            ShipmentWrapper sw  = new ShipmentWrapper();
                            sw.recordId         = item.Id;
                            sw.shipmentNumber   = item.Ship_OracleID__c;
                            sw.canReverse       = item.Ship_Date__c.daysBetween(Date.today()) <= shipDays;
                            sw.shipDays         = shipDays;
                            for (Shipment_Item__c sitem : item.Shipment_Items__r) {
                                ShipmentItemWrapper itemWrapper = new ShipmentItemWrapper();
                                itemWrapper.productId               = sitem.Product__c;
                                itemwrapper.itemQuantityInOrder     = sitem.Item_Quantity_in_Order__c == null ? 0 : sitem.Item_Quantity_in_Order__c;
                                itemwrapper.itemQuantityInShipment  = sitem.Item_Quantity_in_Shipment__c == null ? 0 : sitem.Item_Quantity_in_Shipment__c;
                                sw.shipItemList.add(itemWrapper);
                            }
                            if (String.isNotEmpty(inv.deliveryNumber) && inv.deliveryNumber.contains(item.Ship_OracleID__c)) {
                                inv.shipments.add(sw);
                            }
                        }
                    }
                }
            }

            response.returnData = invoiceWrapperList;
            response.isSuccess = true;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description get shipment info and shipment item info
     * @param  shipmentId shipmentId description
     * @param  orderId    orderId description
     * @return            return description
     */
    @AuraEnabled
    public static ResponseWrapper getShipmentItems(String shipmentId, String orderId, String orderItemId) {
        ResponseWrapper response = new ResponseWrapper();
        try {
            Map<String, Decimal> productShipQTYMap = new Map<String, Decimal>();
            // add haibo: product french
            String shipSQL = 'SELECT Id, Item_Quantity_in_Order__c, Item_Quantity_in_Shipment__c, Product_Text__c, Product__c, Product__r.Name, Product__r.Product_Name_French__c, Product__r.ProductCode, Product__r.Brand__c FROM Shipment_Item__c WHERE Shipment__c = \'' + shipmentId + '\'';
            for (Shipment_Item__c item : CCM_ReverseOrderUtil.getShipmentItemInfo(shipSQL)) {
                if (!productShipQTYMap.containsKey(item.Product__r.ProductCode)) {
                    productShipQTYMap.put(item.Product__r.ProductCode, 0);
                }
                productShipQTYMap.put(item.Product__r.ProductCode, productShipQTYMap.get(item.Product__r.ProductCode) + item.Item_Quantity_in_Shipment__c);
            }

            List<OrderItemWrapper> itemwrappers = new List<OrderItemWrapper>();
            Set<String> existingProductCode = new Set<String>();
            // add haibo: product french
            String oiSQL = 'SELECT Id, Brand__c, Order_Quantity__c, Product__c, Product__r.ProductCode, Product__r.SF_Description__c, Product__r.Name, Product__r.Product_Name_French__c, Price__c FROM Order_Item__c WHERE Order__c = \'' + orderId  + '\' AND Line_Status__c IN (\'INVOICED\', \'SHIPPED\', \'CLOSED\')';
            if (String.isNotEmpty(orderItemId)) {
                oiSQL += ' AND Id = \'' + orderItemId + '\'';
            }
            for (Order_Item__c item : CCM_ReverseOrderUtil.getOrderItems(oiSQL)) {
                if (!existingProductCode.contains(item.Product__r.ProductCode)) {
                    if (productShipQTYMap.containsKey(item.Product__r.ProductCode)) {
                        OrderItemWrapper itemwrapper    = new OrderItemWrapper();
                        itemwrapper.orderItemId         = item.Id;
                        itemWrapper.productId           = item.Product__c;
                        // add haibo: product french
                        if(UserInfo.getLanguage() == Label.CCM_Portal_French){
                            itemwrapper.productName = item.Product__r.Product_Name_French__c;
                        }else{
                            itemwrapper.productName = item.Product__r.Name;
                        }
                        itemwrapper.productCode         = item.Product__r.ProductCode;
                        itemwrapper.brand               = item.Brand__c;
                        itemwrapper.unit                = 'EA';
                        itemwrapper.invoicePrice        = item.Price__c;
                        itemwrapper.orderItemQuantity   = productShipQTYMap.get(item.Product__r.ProductCode);
                        itemwrapper.productDescription  = item.Product__r.SF_Description__c;
                        itemwrappers.add(itemWrapper);
                    }
                }
                existingProductCode.add(item.Product__r.ProductCode);
            }
            response.returnData = itemwrappers;
            response.isSuccess = true;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: add by Yanko :get shipping address Real Time Search
     */
    @AuraEnabled
    public static ResponseWrapper getShipping(String filter, String customerId) {
        System.debug('customer Id ===== ' + customerId);
        ResponseWrapper response = new ResponseWrapper();
        try {
            List<String> recordtype = new List<String> {'Dropship_Shipping_Address','Shipping_Address'};
            String queryStr = 'SELECT Id, Name, Address1__c, Address2__c, City__c, Country__c, State__c,RecordType_Name__c FROM Account_Address__c WHERE Active__c = true AND RecordType_Name__c IN :recordtype AND Approval_Status__c = \'Approved\' ';
            queryStr += ' AND Customer__c = :customerId ';
            if (String.isNotEmpty(filter) && String.isNotBlank(filter)) {
                String filterStr = '%' + filter.trim() + '%';
                queryStr += ' AND (Name LIKE :filterStr OR Address1__c LIKE :filterStr OR Address2__c LIKE :filterStr OR City__c LIKE :filterStr OR Country__c LIKE :filterStr OR State__c LIKE :filterStr ) ';
            }
            queryStr += ' LIMIT 250 ';
            System.debug(Database.query(queryStr));
            List<CCM_ReverseOrderInfoCtl.AddressWrapper> addressList = new List<CCM_ReverseOrderInfoCtl.AddressWrapper>();
            for (Account_Address__c aa : (List<Account_Address__c>)Database.query(queryStr)) {

                CCM_ReverseOrderInfoCtl.AddressWrapper wrapper = new CCM_ReverseOrderInfoCtl.AddressWrapper();

                wrapper.address1    = aa.Address1__c;
                wrapper.address2    = aa.Address2__c;
                wrapper.state       = aa.State__c;
                wrapper.city        = aa.City__c;
                wrapper.country     = aa.Country__c;
                wrapper.name        = aa.Name;
                wrapper.addressId   = aa.Id;
                wrapper.RecordTypeName = aa.RecordType_Name__c;

                addressList.add(wrapper);
            }

            response.returnData = addressList;
            response.isSuccess = true;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: get all billing address
     */
    @AuraEnabled
    public static ResponseWrapper getAddress(String customerId) {
        return CCM_ReverseOrderUtil.getAddress(customerId);
    }


    /**
     * @description: save reverse order request detail info
     */
    @AuraEnabled
    public static ResponseWrapper saveReverseOrder(String reverseOrderStr) {
        ResponseWrapper response = new ResponseWrapper();
        try {
            ReverseOrderWrapper wrapper = (ReverseOrderWrapper)JSON.deserialize(reverseOrderStr, ReverseOrderWrapper.class);
            Reverse_Order_Request__c objReverse = new Reverse_Order_Request__c();
            objReverse.Id = wrapper.recordId;
            objReverse.External_Return_Reason__c = wrapper.externalReturnReason;
            objReverse.Customer__c = wrapper.customerId;
            objReverse.Original_Invoice__c = wrapper.invoiceId;
            objReverse.Shipment__c = wrapper.shipmentId;
            // reverse order's tracking_number__c
            if (String.isNotBlank(wrapper.shipmentId)) {
                List<Shipment__c> lstShipment = CCM_WithoutSharingUtil.queryShipments(wrapper.shipmentId);
                // 公式字段改成自定义字段之后，才能写入这个值
                objReverse.Tracking_Number__c = lstShipment[0].Tracking_Number__c;
            }

            if (String.isNotEmpty(objReverse.Customer__c)) {
                Account objCustomer = [SELECT id, ORG_Code__c FROM Account WHERE Id = :objReverse.Customer__c LIMIT 1];
                objReverse.ORG_Code__c = objCustomer.ORG_Code__c;
                if (objReverse.ORG_Code__c == CCM_Constants.ORG_CODE_CCA) {
                    objReverse.Order_Type__c = 'CA Return – Sales & Inv. Adj.';
                } else {
                    objReverse.Order_Type__c = 'CNA Return – Sales & Inv. Adj.';
                }
            }
            objReverse.Return_Freight_Fee__c = wrapper.returnFreightFee!=null ? wrapper.returnFreightFee : null;
            objReverse.Customer_Contact_Email__c = wrapper.customerContactEmails;
            objReverse.Remark__c = wrapper.remark;
            objReverse.Delivery_Number__c = wrapper.deliveryNumber;
            if (String.isNotBlank(wrapper.orderId)) {
                objReverse.Order__c = wrapper.orderId;
                // Order objOrder = [SELECT Id, Purchase_Order__c,  Price_Book__c FROM Order WHERE Id = :objReverse.Order__c LIMIT 1];
                Order objOrder = CCM_ReverseOrderUtil.getOrderById(objReverse.Order__c);
                objReverse.CurrencyIsoCode = objOrder.CurrencyIsoCode;
                objReverse.Purchase_Order__c = objOrder.Purchase_Order__c;
                if(objOrder.ShipTo__c != null){
                    objReverse.ShipTo_OracleID__c = objOrder.ShipTo__r.Customer_Line_Oracle_ID__c;
                    objReverse.ShipTo__c = objOrder.ShipTo__c;
                    if(objOrder.ShipTo__r.Account_Address__c != null){
                        objReverse.Shipping_Address__c = objOrder.ShipTo__r.Account_Address__c;
                    }
                }
                // objReverse.Price_List__c = objOrder.Price_Book__c;
            }
            if(wrapper.externalReturnReason == CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_LESS_THAN_I_ORDERED){
                objReverse.Reverse_Order_Type__c = 'Shortage';
            }
            if(wrapper.externalReturnReason == CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_MORE_THAN_I_ORDERED){
                objReverse.Reverse_Order_Type__c = 'Overage';
            }
            if(wrapper.externalReturnReason == CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_WRONG_PRODUCT){
                objReverse.Reverse_Order_Type__c = 'Wrong Product';
            }
            if(wrapper.externalReturnReason == CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_PRODUCT_DAMAGED_IN_SHIPMENT){
                objReverse.Reverse_Order_Type__c = 'Damaged in Shipment';
            }
            if(wrapper.externalReturnReason == CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_WITH_NO_REASON){
                objReverse.Reverse_Order_Type__c = 'CustomerRefusal with no reason';
            }
            if(wrapper.externalReturnReason == CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_ERROR_BY_SALES){
                objReverse.Reverse_Order_Type__c = 'Order Entry Error by Sales';
            }

            // allen, inside sales user change to other order when update, remove all reverse order items first
            if(String.isNotBlank(objReverse.Id)){
                String originalId = objReverse.Id;
                String reverseQuery = queryStr + ' AND Id = :originalId LIMIT 1 ';
                Reverse_Order_Request__c originalReverse = Database.query(reverseQuery);
                List<String> itemIds = new List<String>();
                for (Reverse_Order_Item__c item : originalReverse.Reverse_Order_Items__r) {
                    itemIds.add(item.Id);
                }
                if (wrapper.takeItems != null && wrapper.takeItems.size() > 0) {
                    for (ReverseOrderItemWrapper itemWrapper : wrapper.takeItems) {
                        if(itemWrapper.recordId != null && itemIds.contains(itemWrapper.recordId)){
                            itemIds.remove(itemIds.indexOf(itemWrapper.recordId));
                        }
                    }
                }
                if (wrapper.returnItems != null && wrapper.returnItems.size() > 0) {
                    for (ReverseOrderItemWrapper itemWrapper : wrapper.returnItems) {
                        if(itemWrapper.recordId != null && itemIds.contains(itemWrapper.recordId)){
                            itemIds.remove(itemIds.indexOf(itemWrapper.recordId));
                        }
                    }
                }
                wrapper.deleteItemIds = itemIds;
            }

            if (String.isNotEmpty(wrapper.recordId) && wrapper.deleteItemIds.size() > 0) {
                deleteItems(wrapper.deleteItemIds);
            }

            // billing
            if (wrapper.billingAddress != null) {
                Util.AddressOracleInfo info = Util.getAddressOracelId(wrapper.billingAddress.addressId, objReverse.Customer__c, String.join(wrapper.brands, '&'), true, true, 'Reverse Order');
                objReverse.Billing_Address__c = wrapper.billingAddress == null ? null : wrapper.billingAddress.addressId;
                objReverse.BillTo_OracleID__c = info.oracelId;
                if (String.isNotBlank(info.sfId)) {
                    objReverse.BillTo__c = info.sfId;
                }
            }

            objReverse.Is_Alternative_Address__c = false;
            // shipping
            if (wrapper.isAlternativeAddress != null && wrapper.isAlternativeAddress) {
                AddressWrapper address = new AddressWrapper();
                objReverse.Additional_Shipping_Street__c        = wrapper.additionalShippingStreet;
                objReverse.Additional_Shipping_Street2__c       = wrapper.additionalShippingStreet2;
                objReverse.Additional_Shipping_City__c          = wrapper.additionalShippingCity;
                objReverse.Additional_Shipping_Country__c       = wrapper.additionalShippingCountry;
                objReverse.Additional_Shipping_Postal_Code__c   = wrapper.additionalShippingPostalCode;
                objReverse.Additional_Contact_Email__c          = wrapper.additionalcontactEmail;
                objReverse.Additional_Contact_Name__c           = wrapper.additionalcontactName;
                objReverse.Additional_Contact_Phone__c          = wrapper.additionalContactPhone;
                objReverse.Additional_Shipping_Province__c      = wrapper.additionalShippingProvince;
                objReverse.Is_Alternative_Address__c            = true;
            } else {
                if (wrapper.shippingAddress != null) {
                    Util.AddressOracleInfo info = Util.getAddressOracelId(wrapper.shippingAddress.addressId, objReverse.Customer__c, String.join(wrapper.brands, '&'), false, true, 'Reverse Order');
                    objReverse.ShipTo_OracleID__c = info.oracelId;
                    if (String.isNotBlank(info.sfId)) {
                        objReverse.ShipTo__c = info.sfId;
                    }
                    objReverse.Shipping_Address__c = wrapper.shippingAddress.addressId;
                }
            }

            // Allen assign return goods status, credit memo status according to the reason and next step
            List<String> returnGoodsSteps = new List<String>{
                CCM_ReverseOrderUtil.NEXT_STEP_ACTION_REJECT_AND_RETURN_THE_OVERAGE_PRODUCT,
                CCM_ReverseOrderUtil.NEXT_STEP_ACTION_RETURN_DAMAGED_GOODS_AND_GET_REPLACEMENT,
                CCM_ReverseOrderUtil.NEXT_STEP_ACTION_RETURN_DAMAGED_GOODS_AND_GET_CREDIT_BACK,
                CCM_ReverseOrderUtil.NEXT_STEP_ACTION_RETURN_GOODS_AND_GET_CREDIT_BACK
            };
            List<String> returnCreditMemoSteps = new List<String>{
                CCM_ReverseOrderUtil.NEXT_STEP_ACTION_CREDIT_MEMO_BACK_FOR_SHORTAGE_PRODUCT,
                CCM_ReverseOrderUtil.NEXT_STEP_ACTION_RETURN_DAMAGED_GOODS_AND_GET_CREDIT_BACK,
                CCM_ReverseOrderUtil.NEXT_STEP_ACTION_RETURN_GOODS_AND_GET_CREDIT_BACK,
                CCM_ReverseOrderUtil.NEXT_STEP_ACTION_RESHIP_THE_SHORTAGE_PRODUCT,
                CCM_ReverseOrderUtil.NEXT_STEP_ACTION_RETURN_DAMAGED_GOODS_AND_GET_REPLACEMENT
            };
            if (wrapper.takeItems != null && wrapper.takeItems.size() > 0) {
                if(returnGoodsSteps.contains(wrapper.takeItems[0].nextStepAction)){
                    // add haibo: french
                    objReverse.Return_Goods_Status__c = Label.CCM_Portal_NotReceived;
                }
                if(returnCreditMemoSteps.contains(wrapper.takeItems[0].nextStepAction)){
                    // add haibo: french
                    objReverse.Credit_Memo_Status__c = Label.CCM_Portal_NotIssued;
                }
            }
            if (wrapper.returnItems != null && wrapper.returnItems.size() > 0) {
                if(objReverse.Return_Goods_Status__c == null && returnGoodsSteps.contains(wrapper.returnItems[0].nextStepAction)){
                    // add haibo: french
                    objReverse.Return_Goods_Status__c = Label.CCM_Portal_NotReceived;
                }
                if(objReverse.Credit_Memo_Status__c == null && returnCreditMemoSteps.contains(wrapper.returnItems[0].nextStepAction)){
                    // add haibo: french
                    objReverse.Credit_Memo_Status__c = Label.CCM_Portal_NotIssued;
                }
            }
            // add haibo: french
            objReverse.Return_Goods_Status__c = objReverse.Return_Goods_Status__c == null ? Label.CCM_Portal_NA : objReverse.Return_Goods_Status__c;
            objReverse.Credit_Memo_Status__c = objReverse.Credit_Memo_Status__c == null ? Label.CCM_Portal_NA : objReverse.Credit_Memo_Status__c;

            // upsert objReverse;

            objReverse = CCM_ReverseOrderUtil.upsertReverseOrderRequest(objReverse);

            // Map<Id, Order_Item__c> orderItemMap = new Map<Id, Order_Item__c>([SELECT Id, Price__c, Price_Book__c, Brand__c FROM Order_Item__c WHERE Order__c = :objReverse.Order__c]);
            Map<Id, Order_Item__c> orderItemMap = CCM_ReverseOrderUtil.getOrderItemsByOrderId(objReverse.Order__c);
            List<Reverse_Order_Item__c> itemList = new List<Reverse_Order_Item__c>();
            Set<String> itemProductCodeSet = new Set<String>();

            if (wrapper.takeItems != null && wrapper.takeItems.size() > 0) {
                for (ReverseOrderItemWrapper itemWrapper : wrapper.takeItems) {
                    itemList.add(generateItem(objReverse, itemWrapper, orderItemMap));
                }
            }
            if (wrapper.returnItems != null && wrapper.returnItems.size() > 0) {
                for (ReverseOrderItemWrapper itemWrapper : wrapper.returnItems) {
                    itemList.add(generateItem(objReverse, itemWrapper, orderItemMap));
                }
            }


            if (itemList.size() > 0) {
                Set<String> productIds = new Set<String>();
                for (Reverse_Order_Item__c item : itemList) {
                    productIds.add(item.Product2__c);
                }
                for (Product2 objP : [SELECT Id, ProductCode FROM Product2 WHERE Id IN :productIds]) {
                    itemProductCodeSet.add(objP.ProductCode);
                }
                System.debug('*** itemProductCodeSet: ' + itemProductCodeSet);
                // Determine whether the orderItem priceBook is valid, if not, find a valid PriceBook
                //Find the PriceBookEntry through the main PriceBook
                List<PricebookEntry> lstPBEntry = [select id, pricebook2Id,UnitPrice,product2Id FROM pricebookentry WHERE IsActive = true
                                                                                        AND Pricebook2Id =: itemList[0].Price_Book__c
                                                                                        AND Product2.ProductCode IN : itemProductCodeSet
                                                                                        AND priceBook2.isActive = true];
                System.debug('*** lstPBEntry: ' + lstPBEntry);
                System.debug('*** lstPBEntry size: ' + lstPBEntry.size());
                // if(lstPBEntry.size() == itemProductCodeSet.size()){
                //     objReverse.Price_List__c = itemList[0].Price_Book__c;
                //     itemList[0].PricebookPrice__c = lstPBEntry[0].UnitPrice;
                // }else{
                //     //Find the sub PriceBook and sub PriceBookEntry
                //     List<Pricebook2> lstPricebook2 = [SELECT Id, Contract_Price_Book_OracleID__c FROM priceBook2 WHERE Id = : itemList[0].Price_Book__c];
                //     Set<String> pbOracleId = new Set<String>();
                //     if (lstPricebook2.size() > 0 && String.isNotEmpty(lstPricebook2[0].Contract_Price_Book_OracleID__c)){
                //         pbOracleId.addAll(lstPricebook2[0].Contract_Price_Book_OracleID__c.split(','));
                //     }
                //     List<PricebookEntry> lstPBEntry1 = [select id, pricebook2Id,UnitPrice,product2Id FROM pricebookentry WHERE IsActive = true
                //                                                                         AND Pricebook2.price_book_OracleID__c IN : pbOracleId
                //                                                                         AND Product2.ProductCode IN : itemProductCodeSet
                //                                                                         AND priceBook2.isActive = true];

                //     System.debug('*** lstPBEntry1: ' + lstPBEntry1);
                //     System.debug('*** lstPBEntry1 size: ' + lstPBEntry1.size());
                //     if(lstPBEntry1.size() == itemProductCodeSet.size()){
                //         objReverse.Price_List__c = lstPBEntry1[0].Pricebook2Id;
                //         itemList[0].PricebookPrice__c = lstPBEntry1[0].UnitPrice;
                //     }else{
                //         throw new QueryException('The product has no active price');
                //     }
                // }
                CCM_ReverseOrderUtil.updateReverseOrder(objReverse);
                itemList = CCM_ReverseOrderUtil.upsertReverseOrderItems(itemList);
            }
            response.isSuccess = true;
            return getReverseOrderInfo(objReverse.Id);
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: Update OPS
     */
    @AuraEnabled
    public static ResponseWrapper updateReverseOrderOPS(String reverseOrderId, String opsResult, String opsRemark) {
        Map<String, String> brandCodeMap = new Map<String, String>{
            'EGO' => '000030',
            'FLEX' => '000050',
            'SKIL' => '000070',
            'SKILSAW' => '000080'
        };
        ResponseWrapper response = new ResponseWrapper();
        try {
            Reverse_Order_Request__c reverseOrder = new Reverse_Order_Request__c();
            reverseOrder.Id = reverseOrderId;
            reverseOrder.Confirmed_by_OPS__c = opsResult;
            reverseOrder.Confirmed_by_OPS_Remark__c = opsRemark;
            reverseOrder.OPS_Approved_Date__c = Date.today();
            // upsert reverseOrder;
            reverseOrder = CCM_ReverseOrderUtil.updateReverseOrder(reverseOrder);
            if (opsResult == 'OPS Not Confirmed') {
                List<Reverse_Order_Charge_Credit_Account__c> creditAccountList = new List<Reverse_Order_Charge_Credit_Account__c>();
                delete [SELECT Id FROM Reverse_Order_Charge_Credit_Account__c WHERE Reverse_Order_Request__c = :reverseOrderId];
                Reverse_Order_Request__c reverse = [SELECT Id,
                                                            Reverse_Order_Type__c,
                                                            // add haibo: french
                                                            (SELECT Id, Brand__c, Description__c, Next_Step_Action__c, toLabel(Next_Step_Action__c) nextStepActionLabel, Subtotal__c, Invoice_Price__c, Qty__c, Order_Product_Type__c FROM Reverse_Order_Items__r)
                                                        FROM Reverse_Order_Request__c
                                                        WHERE Id = :reverseOrderId];
                if (reverse.Reverse_Order_Type__c == 'Shortage') {
                    for (Reverse_Order_Item__c item : reverse.Reverse_Order_Items__r) {
                        Reverse_Order_Charge_Credit_Account__c creditAccount = new Reverse_Order_Charge_Credit_Account__c();
                        creditAccount.Amount__c                 = item.Invoice_Price__c;
                        creditAccount.QTY__c                    = item.Qty__c;
                        creditAccount.Brand_Code__c             = brandCodeMap.get(item.Brand__c.toUppercase());
                        creditAccount.Cost_Center__c            = '200242';
                        creditAccount.GL_Code__c                = '********';
                        creditAccount.Reverse_Order_Request__c  = reverseOrderId;
                        creditAccount.Reverse_Order_Item__c     = item.Id;
                        creditAccountList.add(creditAccount);
                    }
                } else if (reverse.Reverse_Order_Type__c == 'Overage') {
                    for (Reverse_Order_Item__c item : reverse.Reverse_Order_Items__r) {
                        if (item.Next_Step_Action__c == 'I want to buy the overage product') {
                            Reverse_Order_Charge_Credit_Account__c creditAccount = new Reverse_Order_Charge_Credit_Account__c();
                            creditAccount.Amount__c                 = item.Invoice_Price__c;
                            creditAccount.QTY__c                    = item.Qty__c;
                            creditAccount.Brand_Code__c             = brandCodeMap.get(item.Brand__c.toUppercase());
                            creditAccount.Cost_Center__c            = '';
                            creditAccount.GL_Code__c                = '********';
                            creditAccount.Reverse_Order_Request__c  = reverseOrderId;
                            creditAccount.Reverse_Order_Item__c     = item.Id;
                            creditAccountList.add(creditAccount);
                        }
                    }
                } else if (reverse.Reverse_Order_Type__c == 'Wrong Product') {
                    for (Reverse_Order_Item__c item : reverse.Reverse_Order_Items__r) {
                        if (item.Order_Product_Type__c == 'Shortage') {
                            Reverse_Order_Charge_Credit_Account__c creditAccount = new Reverse_Order_Charge_Credit_Account__c();
                            creditAccount.Amount__c                 = item.Invoice_Price__c;
                            creditAccount.QTY__c                    = item.Qty__c;
                            creditAccount.Brand_Code__c             = brandCodeMap.get(item.Brand__c.toUppercase());
                            creditAccount.Cost_Center__c            = '200242';
                            creditAccount.GL_Code__c                = '********';
                            creditAccount.Reverse_Order_Request__c  = reverseOrderId;
                            creditAccount.Reverse_Order_Item__c     = item.Id;
                            creditAccountList.add(creditAccount);
                        }
                        if (item.Order_Product_Type__c == 'Overage') {
                            if (item.Next_Step_Action__c == 'I want to buy the overage product') {
                                Reverse_Order_Charge_Credit_Account__c creditAccount = new Reverse_Order_Charge_Credit_Account__c();
                                creditAccount.Amount__c                 = item.Invoice_Price__c;
                                creditAccount.QTY__c                    = item.Qty__c;
                                creditAccount.Brand_Code__c             = brandCodeMap.get(item.Brand__c.toUppercase());
                                creditAccount.Cost_Center__c            = '';
                                creditAccount.GL_Code__c                = '********';
                                creditAccount.Reverse_Order_Request__c  = reverseOrderId;
                                creditAccount.Reverse_Order_Item__c     = item.Id;
                                creditAccountList.add(creditAccount);
                            }
                        }
                    }
                }
                if (creditAccountList.size() > 0) {
                    insert creditAccountList;
                }
            }
            response.isSuccess = true;
            response.returnData = reverseOrderId;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: Update DeliveryNumber
     */
    @AuraEnabled
    public static ResponseWrapper updateReverseOrderDeliveryNumber(String reverseOrderId, String deliveryNumber) {
        ResponseWrapper response = new ResponseWrapper();
        try {
            Reverse_Order_Request__c reverseOrder = new Reverse_Order_Request__c();
            reverseOrder.Id = reverseOrderId;
            reverseOrder.Delivery_Number__c = deliveryNumber;
            // upsert reverseOrder;
            reverseOrder = CCM_ReverseOrderUtil.updateReverseOrder(reverseOrder);
            response.isSuccess = true;
            response.returnData = reverseOrderId;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: Update Tasks
     */
    @AuraEnabled
    public static ResponseWrapper updateReverseOrderTasks(String reverseOrderId, String completeTasks, String taskType) {
        ResponseWrapper response = new ResponseWrapper();
        try {
            Reverse_Order_Request__c reverseOrder = new Reverse_Order_Request__c();
            reverseOrder.Id = reverseOrderId;
            if(taskType == 'buy'){
                reverseOrder.Overage_Buy_Tasks__c = completeTasks;
                List<String> options = new List<String>{'NA OPS: Correct Warehouse Inventory',
                                                        'NJ OPS: Correct Oracle Inventory',
                                                        'NJ OPS: Place a non-shipment Order'};
                if(options.size() == completeTasks.split(';').size()){
                    reverseOrder.Completed_In_Manual__c = true;
                    reverseOrder.Reverse_Order_Request_Status__c = Label.CCM_Portal_Completed;
                    // reverseOrder.Return_Goods_Status__c = 'Received';
                }
            }else{
                reverseOrder.Overage_Reject_Tasks__c = completeTasks;
                List<String> options = new List<String>{'NA OPS: Check the over-shipped goods is returned to warehouse'};
                if(options.size() == completeTasks.split(';').size()){
                     // add haibo: french
                     reverseOrder.Completed_In_Manual__c = true;
                     reverseOrder.Reverse_Order_Request_Status__c = Label.CCM_Portal_Completed;
                     reverseOrder.Return_Goods_Status__c = Label.CCM_Portal_Received;
                }
            }
            // upsert reverseOrder;
            reverseOrder = CCM_ReverseOrderUtil.updateReverseOrder(reverseOrder);
            response.isSuccess = true;
            response.returnData = reverseOrderId;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: generate reverse order item
     */
    private static Reverse_Order_Item__c generateItem(Reverse_Order_Request__c objReverse, ReverseOrderItemWrapper itemWrapper, Map<Id, Order_Item__c> orderItemMap) {
        Reverse_Order_Item__c item      = new Reverse_Order_Item__c();
        item.Reverse_Order_Request__c   = objReverse.Id;
        item.Id                         = String.isEmpty(itemWrapper.recordId) ? null : itemWrapper.recordId ;
        item.Product2__c                = itemWrapper.productId;
        item.Order_Item__c              = itemWrapper.orderItemId;
        item.Qty__c                     = itemWrapper.reverseOrderItemQuantity == null ? 0 : Integer.valueOf(itemWrapper.reverseOrderItemQuantity);
        item.Order_Product_Type__c      = itemWrapper.orderProductType;
        item.Unit__c                    = itemWrapper.unit;
        item.Item_Number__c             = itemWrapper.itemNumber;
        item.Next_Step_Action__c        = itemWrapper.nextStepAction;
        item.Invoice_Price__c           = orderItemMap.get(itemWrapper.orderItemId) == null ? itemWrapper.invoicePrice : orderItemMap.get(itemWrapper.orderItemId).Price__c;
        item.Brand__c                   = orderItemMap.get(itemWrapper.orderItemId) == null ? itemWrapper.brand : orderItemMap.get(itemWrapper.orderItemId).Brand__c;
        item.Price_Book__c              = orderItemMap.get(itemWrapper.orderItemId) == null ? null : orderItemMap.get(itemWrapper.orderItemId).Price_Book__c;
        item.Warehouse_Return_Number__c = itemWrapper.warehouseReturnNumber;
        return item;
    }

    /**
     * @description: get reverse order request info and reverse order item info
     */
    @AuraEnabled
    public static ResponseWrapper getReverseOrderInfo(String reverseOrderId) {
        ResponseWrapper response = new ResponseWrapper();
        try {
            User currentUser = [SELECT Id, Contact.AccountId, Profile.Name, UserRoleId, UserRole.DeveloperName FROM User WHERE Id = :UserInfo.getUserId()];
            Map<String, String> sellingWarehouseMap = new Map<String, String>();
            for(Selling_Warehouse_Configuration__mdt meta : [SELECT Id, Selling_Warehouse_Code__c, Address__c FROM Selling_Warehouse_Configuration__mdt]) {
                sellingWarehouseMap.put(meta.Selling_Warehouse_Code__c, meta.Address__c);
            }

            queryStr += ' AND Id = :reverseOrderId ';
            // List<Reverse_Order_Request__c> requestList = (List<Reverse_Order_Request__c>)Database.query(queryStr);
            List<Reverse_Order_Request__c> requestList = CCM_ReverseOrderUtil.getReverseOrder(queryStr,reverseOrderId);

            // List<ReverseOrderWrapper> wrapperList = new List<ReverseOrderWrapper>();

            if(requestList.size()>0){
                Reverse_Order_Request__c  request = requestList[0];
                ReverseOrderWrapper wrapper = new ReverseOrderWrapper();
                if (String.isNotBlank(currentUser.UserRoleId) && Label.Canada_User_Role.contains(currentUser.UserRole.DeveloperName)) {
                    wrapper.orgCode = CCM_Constants.ORG_CODE_CCA;
                }
                wrapper.recordId                    = request.Id;
                wrapper.requestNumber               = request.Reverse_Order_Request_Number__c;
                wrapper.originalOrderNumberInEBS    = request.Original_Order_Number_in_EBS__c;
                wrapper.originalOrderNumberInSF     = request.Original_Order_Number_IN_SF__c;
                wrapper.customerPONumber            = request.Original_Customer_PO_Number__c;
                // wrapper.deliveryNumber              = request.Delivery_Number__c;
                wrapper.deliveryNumber              = request.Shipment__r.Ship_OracleID__c;
                // Add carrier(shipper) and tracking order number to display on the page
                wrapper.carrier                     = request.Shipment__r.Shipper__c;
                wrapper.trackingNumber              = request.Shipment__r.Tracking_Number__c;
                wrapper.shipmentId                  = request.Shipment__c;
                wrapper.invoiceId                   = request.Original_Invoice__c;
                wrapper.currentApprover             = String.isEmpty(request.Current_Approver__c) ? '' : request.Current_Approver__c;
                wrapper.submitter                   = request.Submitter__c;
                if(request.Reverse_Order_Request_Start_Date__c != null){
                    wrapper.requestStartDate            = DateTime.newInstance(
                        request.Reverse_Order_Request_Start_Date__c.year(),
                        request.Reverse_Order_Request_Start_Date__c.month(),
                        request.Reverse_Order_Request_Start_Date__c.day()
                    ).format('MM-dd-yyyy');
                }
                wrapper.customerId                  = request.Customer__c;
                wrapper.customerName                = request.Customer__r.Name;
                wrapper.customerNumber              = request.Customer__r.AccountNumber;
                wrapper.shipTo                      = request.ShipTo__c;
                wrapper.billTo                      = request.BillTo__c;
                wrapper.externalReturnReason        = request.External_Return_Reason__c;
                // add haibo: french
                wrapper.externalReturnReasonFrench  = String.valueOf(request.get('externalReturnReasonLabel'));
                wrapper.isAlternativeAddress        = request.Is_Alternative_Address__c;
                wrapper.approvalStatus              = request.Approval_Status__c;
                wrapper.orderId                     = request.Order__c;
                wrapper.insideSalesId               = request.Inside_Sales__c;
                wrapper.createdById                 = request.CreatedById;
                wrapper.createdByName               = request.CreatedBy.Name;
                wrapper.createdByEmail              = request.CreatedBy.Email;
                // add haibo: french
                wrapper.reverseOrderRequestStatus   = String.valueOf(request.get('reverseOrderRequestStatusLabel'));
                wrapper.reverseOrderType            = request.Reverse_Order_Type__c;
                wrapper.returnFreightFee            = request.Return_Freight_Fee__c;
                wrapper.opsResult                   = request.Confirmed_by_OPS__c;
                wrapper.opsRemark                   = request.Confirmed_by_OPS_Remark__c;
                // add haibo: french
                wrapper.returnGoodsStatus           = String.valueOf(request.get('returnGoodsStatusLabel'));
                wrapper.creditMemoStatus            = String.valueOf(request.get('creditMemoStatusLabel'));
                wrapper.returnFormDate              = request.Return_Form_Date__c == null ? '' : request.Return_Form_Date__c.format('MM-dd-yyyy');
                wrapper.customerContactEmails       = request.Customer_Contact_Email__c;
                wrapper.remark                      = request.Remark__c;
                wrapper.overageBuyTasks             = request.Overage_Buy_Tasks__c;
                wrapper.overageRejectTasks          = request.Overage_Reject_Tasks__c;
                wrapper.ponumber                    = wrapper.customerPONumber;

                wrapper.currentUserProfileName = currentUser.Profile.Name;
                wrapper.currentUserRole = currentUser.UserRole.DeveloperName;

                if(request.Orders__r.size()>0){
                    wrapper.returnOrderNumberInEBS = request.Orders__r[0].Order_Number__c;
                }

                if(request.Invoices__r.size()>0){
                    List<String> invoiceNumerList = new List<String>();
                    List<InvoiceWrapper> creditInvoiceNumbers = new List<InvoiceWrapper>();
                    for(Invoice__c inv : request.Invoices__r) {
                        invoiceNumerList.add(inv.Invoice_Number__c);
                        InvoiceWrapper i = new InvoiceWrapper();
                        i.title = inv.Invoice_Number__c;
                        i.url = '/apex/Invoice?invoiceID=' + inv.Id;
                        creditInvoiceNumbers.add(i);
                    }
                    wrapper.returnCreditInvoiceNumber = String.join(invoiceNumerList, ',');
                    wrapper.creditInvoiceNumbers = creditInvoiceNumbers;
                }

                if(request.Inside_Sales__r != null ){
                    wrapper.insideSalesName = request.Inside_Sales__r.FirstName + ' ' + request.Inside_Sales__r.LastName;
                }

                // get next approver
                for (ProcessInstanceWorkitem pi : [SELECT Id, ProcessInstanceId, OriginalActorId, OriginalActor.Name, ActorId, Actor.Name
                                                        FROM ProcessInstanceWorkitem
                                                        WHERE ProcessInstance.TargetObjectId = :reverseOrderId
                                                        ORDER BY CreatedDate, Actor.Name DESC]) {
                    wrapper.nextApprover = pi.Actor.Name;
                    break;
                }

                // get Original Order Invoice Number
                // List<Invoice__c> invoices = CCM_ReverseOrderUtil.getInvoices(request.Order__c);
                // List<InvoiceWrapper> invoiceNumbers = new List<InvoiceWrapper>();
                // for (Invoice__c invoice : invoices) {
                //     if(invoice.Invoice_Number__c != null){
                //         InvoiceWrapper i = new InvoiceWrapper();
                //         i.recordId = invoice.Id;
                //         i.title = invoice.Invoice_Number__c;
                //         invoiceNumbers.add(i);
                //     }
                // }
                List<InvoiceWrapper> invoiceNumbers = new List<InvoiceWrapper>();
                InvoiceWrapper i = new InvoiceWrapper();
                i.recordId = request.Original_Invoice__c;
                i.title = request.Original_Invoice__r.Invoice_Number__c;
                invoiceNumbers.add(i);
                wrapper.invoiceNumbers = invoiceNumbers;


                String contactId = null;
                contactId = request.Billing_Address__r.Contact5__c != null ? request.Billing_Address__r.Contact5__c : contactId;
                contactId = request.Billing_Address__r.Contact4__c != null ? request.Billing_Address__r.Contact4__c : contactId;
                contactId = request.Billing_Address__r.Contact3__c != null ? request.Billing_Address__r.Contact3__c : contactId;
                contactId = request.Billing_Address__r.Contact2__c != null ? request.Billing_Address__r.Contact2__c : contactId;
                contactId = request.Billing_Address__r.Contact__c != null ? request.Billing_Address__r.Contact__c : contactId;
                if(String.isNotBlank(contactId)){
                    List<Contact> contact = [SELECT Id, Name, Email, Phone  FROM Contact WHERE Id = :contactId LIMIT 1];
                    if(contact != null && contact.size()>0){
                        wrapper.billingAddressContact = contact[0].Name;
                        wrapper.billingAddressContactEmail = contact[0].Email;
                        wrapper.billingAddressContactPhone = contact[0].Phone;
                    }
                }

                if (request.Is_Alternative_Address__c) {
                    AddressWrapper address  = new AddressWrapper();
                    address.address1        = request.Additional_Shipping_Street__c;
                    address.address2        = request.Additional_Shipping_Street2__c;
                    address.city            = request.Additional_Shipping_City__c;
                    address.country         = request.Additional_Shipping_Country__c;
                    address.postalCode      = request.Additional_Shipping_Postal_Code__c;
                    address.contactEmail    = request.Additional_Contact_Email__c;
                    address.contactName     = request.Additional_Contact_Name__c;
                    address.contactPhone    = request.Additional_Contact_Phone__c;
                    address.province        = request.Additional_Shipping_Province__c;
                    wrapper.shippingAddress = address;
                } else if (String.isNotBlank(request.Shipping_Address__c)) {
                    AddressWrapper address  = new AddressWrapper();
                    address.addressId       = request.Shipping_Address__c;
                    address.name            = request.Shipping_Address__r.Name;
                    address.address1        = request.Shipping_Address__r.Address1__c;
                    address.address2        = request.Shipping_Address__r.Address2__c;
                    address.city            = request.Shipping_Address__r.City__c;
                    address.country         = request.Shipping_Address__r.Country__c;
                    address.state           = request.Shipping_Address__r.State__c;
                    address.postalCode      = request.Shipping_Address__r.Postal_Code__c;
                    address.contactEmail    = request.Shipping_Address__r.Contact__r.Email;
                    address.contactName     = request.Shipping_Address__r.Contact__r.Name;
                    address.contactPhone    = request.Shipping_Address__r.Contact__r.Phone;
                    wrapper.shippingAddress = address;
                }
                if (String.isNotBlank(request.Billing_Address__c)) {
                    AddressWrapper address  = new AddressWrapper();
                    address.addressId       = request.Billing_Address__c;
                    address.name            = request.Billing_Address__r.name;
                    address.address1        = request.Billing_Address__r.Address1__c;
                    address.address2        = request.Billing_Address__r.Address2__c;
                    address.city            = request.Billing_Address__r.City__c;
                    address.state           = request.Billing_Address__r.State__c;
                    address.country         = request.Billing_Address__r.Country__c;
                    address.postalCode      = request.Billing_Address__r.Postal_Code__c;
                    address.contactEmail    = request.Billing_Address__r.Contact__r.Email;
                    address.contactName     = request.Billing_Address__r.Contact__r.Name;
                    address.contactPhone    = request.Billing_Address__r.Contact__r.Phone;
                    wrapper.billingAddress  = address;
                }

                List<ReverseOrderItemWrapper> overageList   = new List<ReverseOrderItemWrapper>();
                List<ReverseOrderItemWrapper> shortageList  = new List<ReverseOrderItemWrapper>();
                List<ReverseOrderItemWrapper> damagedList   = new List<ReverseOrderItemWrapper>();

                List<String> itemIds = new List<String>();
                for (Reverse_Order_Item__c item : request.Reverse_Order_Items__r) {
                    itemIds.add(item.Id);
                    ReverseOrderItemWrapper itemWrapper = new ReverseOrderItemWrapper();
                    itemWrapper.recordId                    = item.Id;
                    itemWrapper.brand                       = item.Brand__c;
                    itemWrapper.productId                   = item.Product2__c;
                    itemWrapper.productCode                 = item.Product2__r.ProductCode;
                    // add haibo: french
                    if(UserInfo.getLanguage() == Label.CCM_Portal_French){
                        itemWrapper.productDescription      = item.Product2__r.SF_Description_French__c;
                    }else{
                        itemWrapper.productDescription      = item.Product2__r.SF_Description__c;
                    }
                    itemWrapper.orderItemId                 = item.Order_Item__c;
                    itemWrapper.reverseOrderItemQuantity    = item.Qty__c;
                    itemWrapper.unit                        = item.Unit__c;
                    itemWrapper.brand                       = item.Brand__c;
                    itemWrapper.itemNumber                  = item.Item_Number__c;
                    itemWrapper.invoicePrice                = item.Invoice_Price__c;
                    itemWrapper.orderProductType            = item.Order_Product_Type__c;
                    itemWrapper.warehouseReceivedQty        = item.Warehouse_Received_Qty__c;
                    // itemWrapper.warehouseReceivedSubtotal   = item.Warehouse_Received_Subtotal__c;
                    if(item.Warehouse_Received_Qty__c != null && Math.abs(item.Warehouse_Received_Qty__c) > 0 && item.Invoice_Price__c > 0){
                        itemWrapper.warehouseReceivedSubtotal   = Math.abs(item.Warehouse_Received_Qty__c) * item.Invoice_Price__c;
                    }
                    itemWrapper.creditMemoIssued            = item.Credit_Memo_Issued__c;
                    itemWrapper.nextStepAction              = item.Next_Step_Action__c;
                    // add haibo: french
                    itemWrapper.nextStepActionFrench        = String.valueOf(item.get('nextStepActionLabel'));
                    itemWrapper.subtotal                    = item.Invoice_Price__c == null || item.Qty__c == null ? 0 : item.Invoice_Price__c * item.Qty__c;
                    itemWrapper.warehouseReturnNumber       = item.Warehouse_Return_Number__c;
                    itemWrapper.internalReturnReason        = item.Internal_Return_Reason__c;
                    // Canada Warehouse Address: DSV Solutions Inc. 2200 Yukon Court Milton ON Canada L9E 1N5
                    itemWrapper.sellingWarehouse            = String.isNotBlank(item.Order_Item__c) ? item.Order_Item__r.Selling_Warehouse__c : '';
                    if(itemWrapper.sellingWarehouse.equalsIgnoreCase('CNA01')) {
                        itemWrapper.sellingWarehouse = 'CNA08';
                    }
                    itemWrapper.sellingWarehouse            = sellingWarehouseMap.get(itemWrapper.sellingWarehouse);
                    if(request.Reverse_Order_Type__c== 'Damaged in Shipment' && request.ORG_Code__c != 'CCA'){
                        // itemWrapper.sellingWarehouse = 	sellingWarehouseMap.get('CNA03');
                        itemWrapper.sellingWarehouse = 	sellingWarehouseMap.get('CNA27');
                    }

                    if (item.Order_Product_Type__c == 'Overage') {
                        overageList.add(itemWrapper);
                    }
                    if (item.Order_Product_Type__c == 'Shortage') {
                        shortageList.add(itemWrapper);
                    }
                }
                //
                Set<String> setSellingWarehouseCode = new Set<String>();
                for (Order_Item__c item : [SELECT Id, Selling_Warehouse__c FROM Order_Item__c WHERE Order__c = :request.Order__c AND Selling_Warehouse__c != null]) {
                    setSellingWarehouseCode.add(item.Selling_Warehouse__c);
                }
                List<String> lstSellingWarehouseCode = new List<String>();
                lstSellingWarehouseCode.addAll(setSellingWarehouseCode);
                for (ReverseOrderItemWrapper item : overageList) {
                    if(request.Reverse_Order_Type__c== 'Damaged in Shipment' && request.ORG_Code__c != 'CCA'){
                        // item.sellingWarehouse = sellingWarehouseMap.get('CNA03');
                        item.sellingWarehouse = sellingWarehouseMap.get('CNA27');
                    }else if (setSellingWarehouseCode.size() > 0 && String.isEmpty(item.sellingWarehouse)) {
                        String warehouseCode = lstSellingWarehouseCode[0];
                        if(warehouseCode.equalsIgnoreCase('CNA01')) {
                            warehouseCode = 'CNA08';
                        }
                        item.sellingWarehouse = sellingWarehouseMap.get(warehouseCode);
                    }
                }
                wrapper.takeItems   = shortageList;
                wrapper.returnItems = overageList;
                wrapper.itemImages  = (List<ImageWrapper>)getAllImage(itemIds).returnData;
                List<ImageWrapper> attachments = new List<ImageWrapper>();
                for(ContentDocumentLink iterator : [SELECT ContentDocumentId,ContentDocument.Title, LinkedEntityId, ContentDocument.CreatedById FROM ContentDocumentLink WHERE LinkedEntityId = :reverseOrderId ]) {
                    attachments.add(new ImageWrapper(iterator.LinkedEntityId, iterator.ContentDocumentId, iterator.ContentDocument.Title, iterator.ContentDocument.CreatedById));
                }
                wrapper.attachments = attachments;

                response.returnData = wrapper;
                response.isSuccess = true;
            }
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: submit reverse order for approve
     */
    @AuraEnabled
    public static ResponseWrapper submitForApproval(String reverseOrderRequestId){
        ResponseWrapper response = new ResponseWrapper();
        try {
            String currentUserProfileName = [SELECT Id, Name FROM Profile WHERE Id = :UserInfo.getProfileId()].Name;
            Reverse_Order_Request__c reverseOrder = [Select Id,
                                                        Reverse_Order_Request_Number__c,
                                                        External_Return_Reason__c,
                                                        Approval_Status__c,
                                                        Inside_Sales__r.Email,
                                                        Account_Manager__c,
                                                        Account_Manager__r.Email,
                                                        Customer_Contact_Email__c
                                                    From Reverse_Order_Request__c
                                                        Where Id = :reverseOrderRequestId
                                                        Limit 1];
            if(reverseOrder.External_Return_Reason__c == CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_WITH_NO_REASON || reverseOrder.External_Return_Reason__c == CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_ERROR_BY_SALES){
                // add haibo: french
                reverseOrder.Approval_Status__c = Label.CCM_Portal_Approved;
                reverseOrder.Reverse_Order_Request_Status__c = Label.CCM_Portal_Approved;
                reverseOrder.Submitted_Date__c = Date.today();
                //upsert reverseOrder;
                CCM_ReverseOrderUtil.updateReverseOrder(reverseOrder);
                // send email to Account Manager, cc inside sales self.
                Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                String[] toaddresses = new String[]{reverseOrder.Account_Manager__r.Email};
                toaddresses.addAll(reverseOrder.Customer_Contact_Email__c.split(';'));
                email.toaddresses = toaddresses;
                email.ccaddresses = new String[]{reverseOrder.Inside_Sales__r.Email};
                email.subject = 'New Reverse Order Request Notification';
                String url = URL.getSalesforceBaseUrl() + '/' + reverseOrder.Id;
                email.htmlbody = 'Inside Sales has created a reverse order <a href=\"'+ url +'\">' + reverseOrder.Reverse_Order_Request_Number__c + '</a>, please review!';
                Messaging.SingleEmailMessage[] emails = new Messaging.SingleEmailMessage[]{email};
                Messaging.SendEmailResult[] results = Messaging.sendEmail(emails);
                response.isSuccess = true;
                response.returnData = reverseOrderRequestId;
            }
            else{
                if (currentUserProfileName.contains('Community')) {
                    reverseOrder.Submitter__c = 'Portal User';
                } else {
                    reverseOrder.Submitter__c = 'Inside Sales';
                }
                CCM_ReverseOrderUtil.updateReverseOrder(reverseOrder);
                Approval.ProcessSubmitRequest request = new Approval.ProcessSubmitRequest();
                request.setComments('Reverse order request submitted for approval.');
                request.setObjectId(reverseOrderRequestId);
                Approval.ProcessResult result = Approval.process(request);
                response.isSuccess = result.success;
                if(result.success){
                    List<OrgWideEmailAddress> emailAddressList = new List<OrgWideEmailAddress>([
                        SELECT Id
                        FROM OrgWideEmailAddress
                        WHERE DisplayName = 'SFDC Notification'
                    ]);
                    Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                    email.setOrgWideEmailAddressId(emailAddressList[0].Id);
                    email.toaddresses = reverseOrder.Customer_Contact_Email__c.split(';');
                    email.subject = 'Reverse Order Submitted!';
                    String url = System.URL.getSalesforceBaseUrl() + '/' + reverseOrder.Id;
                    email.htmlbody = 'Reverse Order ' + reverseOrder.Reverse_Order_Request_Number__c + ' has submitted!';
                    Messaging.SingleEmailMessage[] emails = new Messaging.SingleEmailMessage[]{email};
                    Messaging.SendEmailResult[] results = Messaging.sendEmail(emails);
                }
                response.returnData = reverseOrderRequestId;
            }
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: recall reverse order that submitted
     */
    @AuraEnabled
    public static ResponseWrapper recallForApproval(String reverseOrderRequestId){
        ResponseWrapper response = new ResponseWrapper();
        try {
            ProcessInstanceWorkitem[] workItems = [ SELECT Id FROM ProcessInstanceWorkitem
                                                    WHERE ProcessInstance.TargetObjectId = :reverseOrderRequestId
                                                    AND ProcessInstance.Status = 'Pending' ];
            Approval.ProcessWorkitemRequest pwr = new Approval.ProcessWorkitemRequest();
            pwr.setAction('Removed');
            pwr.setWorkItemId(workItems[0].id);

            Approval.ProcessResult result = Approval.process(pwr);
            response.isSuccess = result.isSuccess();
            response.returnData = reverseOrderRequestId;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * send email and custom notification
     */
    public static ResponseWrapper sendNotificationAndEmail(String reverseOrderRequestId,
                                                            String emailToAddress,
                                                            String emailSubject,
                                                            String emailBody,
                                                            String notificationRecipientsId,
                                                            String notificationSubject,
                                                            String notificationBody) {
        ResponseWrapper response = new ResponseWrapper();
        try {
            // Get the Id for our custom notification type
            CustomNotificationType notificationType = [SELECT Id, DeveloperName FROM CustomNotificationType WHERE DeveloperName='Reverse_Order_OPS_Notification'];

            // Create a new custom notification
            Messaging.CustomNotification notification = new Messaging.CustomNotification();

            // Set the contents for the notification
            notification.setTitle(notificationSubject);
            notification.setBody(notificationBody);

            // Set the notification type and target
            notification.setNotificationTypeId(notificationType.Id);
            notification.setTargetId(reverseOrderRequestId);

            Set<String> recipientsIds = new Set<String>();
            recipientsIds.add(notificationRecipientsId);
            notification.send(recipientsIds);

            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            email.toaddresses = new String[]{emailToAddress};
            // email.optoutpolicy = 'FILTER';
            email.subject = emailSubject;
            email.htmlbody = emailBody;
            Messaging.SingleEmailMessage[] emails = new Messaging.SingleEmailMessage[]{email};
            Messaging.SendEmailResult[] results = Messaging.sendEmail(emails);

            response.isSuccess = true;
            response.returnData = reverseOrderRequestId;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: get all reverse order by customer
     */
    @AuraEnabled
    public static ResponseWrapper getAllReverseOrderByCustomer(String requestNumber,
                                                                String customerPONumber,
                                                                String originalEBSOrderNumber,
                                                                String requestInitiator,
                                                                String customerName,
                                                                String customerNumber,
                                                                String reverseOrderType,
                                                                String returnGoodsStatus,
                                                                String creditMemoStatus,
                                                                String requestStatus,
                                                                String startDateFrom,
                                                                String startDateTo,
                                                                Integer pageIndex,
                                                                Integer pageSize){
        ResponseWrapper response = new ResponseWrapper();
        try {
            User currentUser = [SELECT Id, Contact.AccountId, Profile.Name,UserRoleId,UserRole.Name FROM User WHERE Id = :UserInfo.getUserId()];
            String customerId = currentUser.Contact.AccountId;
            String cnaOrg = 'CNA';
            String ccaOrg = 'CCA';
            String filter = ' AND Is_Deleted__c = false';
            if (String.isNotBlank(customerId)) {
                Set<String> customerIds = new Set<String>{customerId};
                filter += ' AND Customer__c IN :customerIds ';
            }
            if (String.isNotBlank(requestNumber)) {
                filter += ' AND Reverse_Order_Request_Number__c LIKE \'%' + requestNumber.trim() + '%\' ';
            }
            if (String.isNotBlank(customerPONumber)) {
                filter += ' AND Original_Customer_PO_Number__c LIKE \'%' + customerPONumber.trim() + '%\' ';
            }
            if(String.isNotBlank(originalEBSOrderNumber)){
                filter += ' AND Original_Order_Number_in_EBS__c LIKE \'%' + originalEBSOrderNumber.trim() + '%\' ';
            }
            if(String.isNotBlank(customerName)){
                customerName = '%' + customerName.trim() + '%';
                filter += ' AND Customer__r.Name LIKE :customerName ';
            }
            if(String.isNotBlank(customerNumber)){
                filter += ' AND Customer__r.AccountNumber LIKE \'%' + customerNumber.trim() + '%\' ';
            }
            if (String.isNotBlank(reverseOrderType)) {
                filter += ' AND Reverse_Order_Type__c = :reverseOrderType ';
            }
            if (String.isNotBlank(returnGoodsStatus)) {
                filter += ' AND Return_Goods_status__c = :returnGoodsStatus ';
            }
            if (String.isNotBlank(creditMemoStatus)) {
                filter += ' AND Credit_Memo_Status__c = :creditMemoStatus ';
            }
            if (String.isNotBlank(requestStatus)) {
                filter += ' AND Reverse_Order_Request_Status__c = :requestStatus ';
            }
            if (String.isNotBlank(startDateFrom)) {
                Date startDate = Date.valueOf(startDateFrom);
                filter += ' AND Reverse_Order_Request_Start_Date__c >= :startDate ';
            }
            if (String.isNotBlank(startDateTo)) {
                Date endDate = Date.valueOf(startDateTo);
                filter += ' AND Reverse_Order_Request_Start_Date__c <= :endDate ';
            }

            if(currentUser.Profile.Name == 'NA Inside Sales'){
                filter += ' AND ORG_Code__c = :cnaOrg ';
            }

              if(currentUser.Profile.Name == 'Canada Sales Manager' && currentUser.UserRoleId != null && currentUser.UserRole.Name == 'CA Inside Sales'){
                filter += ' AND ORG_Code__c = :ccaOrg ';
            }

            if (String.isNotBlank(requestInitiator)) {
                if (!requestInitiator.startsWith('%')) {
                    requestInitiator = '%' + requestInitiator;
                }
                if (!requestInitiator.endsWith('%')) {
                    requestInitiator = requestInitiator + '%';
                }
                filter += ' AND CreatedBy.Name LIKE \'' + requestInitiator + '\' ';
            }
            String countQuery = 'SELECT Id FROM Reverse_Order_Request__c WHERE Is_Deleted__c = false';
            countQuery += filter;
            queryStr += filter;
            String queryStrCount = countQuery;

            Integer offsetsize = pageIndex * pageSize;
            queryStr += ' ORDER BY Reverse_Order_Request_Start_Date__c DESC LIMIT :pageSize OFFSET :offsetsize ';

            List<Reverse_Order_Request__c> requestList = (List<Reverse_Order_Request__c>)Database.query(queryStr);
            List<ReverseOrderWrapper> requestWrapperList = new List<ReverseOrderWrapper>();
            Set<Id> reverseOrderIdSet = new Set<Id>();
            for (Reverse_Order_Request__c request : requestList) {
                reverseOrderIdSet.add(request.Id);
                ReverseOrderWrapper wrapper = new ReverseOrderWrapper();
                wrapper.recordId            = request.Id;
                wrapper.requestNumber       = request.Reverse_Order_Request_Number__c;
                wrapper.customerPONumber    = request.Original_Customer_PO_Number__c;
                // add haibo: french
                wrapper.returnGoodsStatus   = String.valueOf(request.get('returnGoodsStatusLabel'));
                wrapper.creditMemoStatus    = String.valueOf(request.get('creditMemoStatusLabel'));
                // System.debug('Reverse_Order_Request_Start_Date__c = ' + request.Reverse_Order_Request_Start_Date__c);
                if(request.Reverse_Order_Request_Start_Date__c != null){
                    wrapper.requestStartDate    = DateTime.newInstance(
                        request.Reverse_Order_Request_Start_Date__c.year(),
                        request.Reverse_Order_Request_Start_Date__c.month(),
                        request.Reverse_Order_Request_Start_Date__c.day()
                    ).format('MM-dd-yyyy');
                }
                wrapper.approvalStatus      = request.Approval_Status__c;
                wrapper.purchaseOrderId     = request.Purchase_Order__c;
                wrapper.orderId             = request.Order__c;
                // add haibo: french
                wrapper.reverseOrderRequestStatus = String.valueOf(request.get('reverseOrderRequestStatusLabel'));
                wrapper.originalOrderNumberInEBS  = request.Original_Order_Number_in_EBS__c;
                wrapper.reverseOrderType    = request.Reverse_Order_Type__c;
                wrapper.customerName        = request.Customer__r.Name;
                wrapper.customerNumber      = request.Customer__r.AccountNumber;
                wrapper.createdByName       = request.CreatedBy.Name;
                System.debug('*** wrapper: ' + wrapper);
                requestWrapperList.add(wrapper);
            }
            // get next approver
            for (ProcessInstanceWorkitem pi : [SELECT Id, ProcessInstanceId, OriginalActorId, OriginalActor.Name, ActorId, Actor.Name, ProcessInstance.TargetObjectId
                    FROM ProcessInstanceWorkitem
                    WHERE ProcessInstance.TargetObjectId IN :reverseOrderIdSet
                    ORDER BY CreatedDate, Actor.Name DESC]) {
                for (ReverseOrderWrapper wrapper : requestWrapperList) {
                    if (wrapper.recordId == pi.ProcessInstance.TargetObjectId) {
                        wrapper.nextApprover = pi.Actor.Name;
                        continue;
                    }
                }
            }

            List<Reverse_Order_Request__c> reverseOrderCount = (List<Reverse_Order_Request__c>)Database.query(queryStrCount);
            PaginationWrapper pagination = new PaginationWrapper();
            pagination.pageIndex = pageIndex;
            pagination.pageSize = pageSize;
            pagination.data = requestWrapperList;
            pagination.totalSize = reverseOrderCount.size();

            response.isSuccess = true;
            response.returnData = pagination;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: get all images
     */
    @AuraEnabled
    public static ResponseWrapper getAllImage(List<String> itemIds){
        ResponseWrapper response = new ResponseWrapper();
        try {
            List<ImageWrapper> imageList = new List<ImageWrapper>();
            for(ContentDocumentLink iterator : [SELECT ContentDocumentId,ContentDocument.Title, LinkedEntityId, ContentDocument.CreatedById FROM ContentDocumentLink WHERE LinkedEntityId IN :itemIds ]) {
                imageList.add(new ImageWrapper(iterator.LinkedEntityId, iterator.ContentDocumentId, iterator.ContentDocument.Title, iterator.ContentDocument.CreatedById));
            }
            response.returnData = imageList;
            response.isSuccess = true;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: delete image
     */
    @AuraEnabled
    public static ResponseWrapper deleteItems(List<String> deleteItemIds){
        if (deleteItemIds == null || deleteItemIds.size() == 0) {
            return null;
        }
        ResponseWrapper response = new ResponseWrapper();
        try {
            List<ContentDocument> needDelete = new List<ContentDocument>();
            for(ContentDocumentLink entity : [SELECT Id, ContentDocumentId FROM ContentDocumentLink WHERE LinkedEntityId IN :deleteItemIds]) {
                needDelete.add(new ContentDocument(Id = entity.ContentDocumentId));
            }
            if (needDelete.size() > 0) {
                delete needDelete;
            }

            List<Reverse_Order_Item__c> itemList = new List<Reverse_Order_Item__c>();
            for (String itemId : deleteItemIds) {
                itemList.add(new Reverse_Order_Item__c(Id = itemId));
            }
            CCM_ReverseOrderUtil.deleteReverseOrderItems(itemList);
            response.isSuccess = true;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: delete content document
     */
    @AuraEnabled
    public static void deleteContentDocument(String cdId){
        ContentDocument cd = new ContentDocument(Id=cdId);
        delete cd;
    }

    @AuraEnabled
    public static ResponseWrapper deleteReverseOrder(String recordId){
        ResponseWrapper response = new ResponseWrapper();
        try {
            Reverse_Order_Request__c reverseOrder = [SELECT Id FROM Reverse_Order_Request__c WHERE Id = :recordId LIMIT 1];
            if(reverseOrder != null){
                reverseOrder.Is_Deleted__c = true;
                CCM_ReverseOrderUtil.updateReverseOrder(reverseOrder);
                response.isSuccess = true;
            }else{
                response.isSuccess = false;
                response.errorMsg = 'Cannot found the record!';
            }
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: get days between ship date and today
     */
    @AuraEnabled
    public static ResponseWrapper getShipmentDays(String deliveryNumber) {
        ResponseWrapper wrapper = new ResponseWrapper();
        List<Shipment__c> shipmentList = [SELECT Id, Account_Number__c, Customer_PO_Number__c, Customer__c,
                                            Forecast_Date__c, Order_Number__c, Order_Text__c, Order__c, Ship_Date__c, Ship_Method__c,
                                            Ship_OracleID__c, Shipper__c, Tracking_Number__c, WeightUnit__c, Weight__c, ORG_Code__c,
                                            (SELECT Id, Item_Quantity_in_Order__c, Item_Quantity_in_Shipment__c, Product_Text__c, Product__c, Shipment_Item_OracleID__c, Shipment__c, UPC_code__c, ORG_Code__c FROM Shipment_Items__r)
                                            FROM Shipment__c WHERE Ship_OracleID__c = :deliveryNumber];

        if(shipmentList.size() > 0) {
            wrapper.isSuccess = true;
            wrapper.returnData = shipmentList[0].Ship_Date__c.daysBetween(Date.today());
        } else {
            wrapper.isSuccess = false;
            wrapper.errorMsg = 'Not Find Shipment.';
        }
        return wrapper;
    }

    /**
     * @description: reverse order wrapper class
     */
    public class ReverseOrderWrapper {

        @AuraEnabled public String orgCode {get; set;}

        @AuraEnabled public String recordId {get; set;}

        @AuraEnabled public String name {get; set;}

        @AuraEnabled public String requestNumber {get; set;}

        @AuraEnabled public String requestStartDate {get; set;}

        @AuraEnabled public List<String> brands {get; set;}

        @AuraEnabled public String customerId {get; set;}

        @AuraEnabled public String customerName {get; set;}

        @AuraEnabled public String customerNumber {get; set;}

        @AuraEnabled public String customerContactEmails {get; set;}

        @AuraEnabled public String remark {get; set;}

        @AuraEnabled public String customerPONumber {get; set;}

        @AuraEnabled public String orderId {get; set;}

        @AuraEnabled public String purchaseOrderId {get; set;}

        @AuraEnabled public String reverseOrderType {get; set;}

        @AuraEnabled public String billTo {get; set;}

        @AuraEnabled public String shipTo {get; set;}

        @AuraEnabled public String billingAddressId {get; set;}

        @AuraEnabled public String shippingAddressId {get; set;}

        @AuraEnabled public String externalReturnReason {get; set;}

        @AuraEnabled public String externalReturnReasonFrench {get; set;}

        @AuraEnabled public String ReverseOrderStatus {get; set;}

        @AuraEnabled public String model {get; set;}

        @AuraEnabled public String orderType {get; set;}

        @AuraEnabled public String approvalStatus {get; set;}

        @AuraEnabled public String returnOrderStatus {get; set;}

        @AuraEnabled public String returnGoodsStatus {get; set;}

        @AuraEnabled public String creditMemoStatus {get; set;}

        @AuraEnabled public String reverseOrderRequestStatus;

        @AuraEnabled public Boolean isAlternativeAddress {get; set;}

        @AuraEnabled public String additionalShippingStreet {get; set;}

        @AuraEnabled public String additionalShippingStreet2 {get; set;}

        @AuraEnabled public String additionalShippingCity {get; set;}

        @AuraEnabled public String additionalShippingCountry {get; set;}

        @AuraEnabled public String additionalShippingPostalCode {get; set;}

        @AuraEnabled public String additionalContactEmail {get; set;}

        @AuraEnabled public String additionalContactName {get; set;}

        @AuraEnabled public String additionalContactPhone {get; set;}

        @AuraEnabled public String additionalShippingProvince {get; set;}

        @AuraEnabled public String originalOrderNumberInEBS {get; set;}

        @AuraEnabled public String originalOrderNumberInSF {get; set;}

        @AuraEnabled public String billingAddressContact {get; set;}

        @AuraEnabled public String billingAddressContactEmail {get; set;}

        @AuraEnabled public String billingAddressContactPhone {get; set;}

        @AuraEnabled public String createdById {get; set;}

        @AuraEnabled public String createdByName {get; set;}

        @AuraEnabled public String createdByEmail {get; set;}

        @AuraEnabled public String insideSalesId {get; set;}

        @AuraEnabled public String insideSalesName {get; set;}

        @AuraEnabled public String currentUserProfileName {get; set;}

        @AuraEnabled public String currentUserRole {get;set;}

        @AuraEnabled public Decimal returnFreightFee {get; set;}

        @AuraEnabled public String opsResult {get; set;}

        @AuraEnabled public String opsRemark {get; set;}

        @AuraEnabled public String returnFormDate {get; set;}

        @AuraEnabled public String overageBuyTasks {get; set;}

        @AuraEnabled public String overageRejectTasks {get; set;}

        @AuraEnabled public String returnOrderNumberInEBS {get; set;}

        @AuraEnabled public String returnCreditInvoiceNumber {get; set;}

        @AuraEnabled public String nextApprover { get; set; }

        @AuraEnabled public String deliveryNumber {get; set;}

        @AuraEnabled public String currentApprover {get; set;}

        @AuraEnabled public String submitter {get; set;}

        @AuraEnabled public String invoiceId {get; set;}

        @AuraEnabled public String shipmentId {get; set;}

        @AuraEnabled public List<ReverseOrderItemWrapper> takeItems {get; set;}

        @AuraEnabled public List<ReverseOrderItemWrapper> returnItems {get; set;}

        @AuraEnabled public List<ReverseOrderProductItemWrapper> reverseOrderProductItems {get; set;}

        @AuraEnabled public List<ImageWrapper> itemImages {get; set;}

        @AuraEnabled public List<ImageWrapper> attachments {get; set;}

        // @AuraEnabled public List<ReverseOrderItemWrapper> damagedItems {get; set;}

        @AuraEnabled public AddressWrapper billingAddress {get; set;}

        @AuraEnabled public AddressWrapper shippingAddress {get; set;}

        // Add carrier(shipper) and tracking order number to display on the page
        @AuraEnabled public String carrier {get; set;}

        @AuraEnabled public String trackingNumber {get; set;}

        @AuraEnabled public List<String> deleteItemIds {get; set;}

        @AuraEnabled public List<InvoiceWrapper> invoiceNumbers {get; set;}

        @AuraEnabled public List<InvoiceWrapper> creditInvoiceNumbers {get; set;}

        @AuraEnabled public String ponumber {get;set;}

        public ReverseOrderWrapper() {
            this.orgCode = CCM_Constants.ORG_CODE_CNA;
            this.takeItems = new List<ReverseOrderItemWrapper>();
            this.returnItems = new List<ReverseOrderItemWrapper>();
            this.itemImages = new List<ImageWrapper>();
            this.reverseOrderProductItems = new List<ReverseOrderProductItemWrapper>();
            this.invoiceNumbers = new List<InvoiceWrapper>();
        }
    }

    /**
     * @description: invoice wrapper class
     */
    public class InvoiceWrapper {
        @AuraEnabled public String recordId {get; set;}

        @AuraEnabled public String title {get; set;}

        @AuraEnabled public String url {get; set;}

        @AuraEnabled public String invoiceNumber {get; set;}

        @AuraEnabled public String deliveryNumber {get; set;}

        @AuraEnabled public String shipment {get; set;}

        @AuraEnabled public List<ShipmentWrapper> shipments{get; set;}

        @AuraEnabled public List<InvoiceItemWrapper> invoiceItemList {get; set;}

        public InvoiceWrapper() {
            this.recordId = null;
            this.title = '';
            this.url = '';
            this.invoiceNumber = '';
            this.deliveryNumber = '';
            this.shipment = '';
            this.shipments = new List<ShipmentWrapper>();
            this.invoiceItemList = new List<InvoiceItemWrapper>();
        }
    }

    public class InvoiceItemWrapper {
        @AuraEnabled public String recordId {get; set;}
    }

    /**
     * @description: reverse order item wrapper class
     */
    public class ReverseOrderItemWrapper {

        @AuraEnabled public String recordId {get; set;}

        @AuraEnabled public String brand {get; set;}

        @AuraEnabled public String claimCreditMemoAmount {get; set;}

        @AuraEnabled public Decimal creditMemoIssued {get; set;}

        @AuraEnabled public String description {get; set;}

        @AuraEnabled public Decimal invoicePrice {get; set;}

        @AuraEnabled public String issuedCreditMemoAmount {get; set;}

        @AuraEnabled public String name {get; set;}

        @AuraEnabled public String nextStepAction {get; set;}
        // add haibo: french
        @AuraEnabled public String nextStepActionFrench {get; set;}

        @AuraEnabled public String orderItemId {get; set;}

        @AuraEnabled public String orderProductType {get; set;}

        @AuraEnabled public String productId {get; set;}

        @AuraEnabled public String productCode {get; set;}

        @AuraEnabled public String productDescription {get; set;}

        @AuraEnabled public String productAmount {get; set;}

        @AuraEnabled public String purchaseOrderItem {get; set;}

        @AuraEnabled public String itemNumber {get; set;}

        @AuraEnabled public Decimal reverseOrderItemQuantity {get; set;}

        @AuraEnabled public String reverseOrderRequest {get; set;}

        @AuraEnabled public Decimal subtotal {get; set;}

        @AuraEnabled public Integer totalQuantity {get; set;}

        @AuraEnabled public String unit {get; set;}

        @AuraEnabled public String warehouseReceivedProductAmount {get; set;}

        @AuraEnabled public Decimal warehouseReceivedQty {get; set;}

        @AuraEnabled public Decimal warehouseReceivedSubtotal {get; set;}

        @AuraEnabled public Decimal warehouseReceivedTotalQuantity {get; set;}

        @AuraEnabled public String warehouseReturnNumber {get; set;}

        @AuraEnabled public String internalReturnReason {get; set;}

        @AuraEnabled public String sellingWarehouse {get; set;}

    }

    /**
     * @description: reverse order product item wrapper class
     */
    public class ReverseOrderProductItemWrapper {

        @AuraEnabled public String recordId {get; set;}

        @AuraEnabled public String itemNumber {get; set;}

        // ProductCode
        @AuraEnabled public String modelNumber {get; set;}

        @AuraEnabled public Decimal quantity {get; set;}

        @AuraEnabled public String reverseOrderRequest {get; set;}

        @AuraEnabled public string warehouse {get; set;}
    }

    /**
     * @description: reverse order item image wrapper class
     */
    public class ImageWrapper {
        @AuraEnabled public String itemId {get; set;}

        @AuraEnabled public String documentId {get; set;}

        @AuraEnabled public String name {get; set;}

        @AuraEnabled public String createdById {get; set;}

        public ImageWrapper(String itemId, String documentId, String name, String createdById) {
            this.itemId = itemId;
            this.documentId = documentId;
            this.name = name;
            this.createdById = createdById;
        }
    }

    /**
     * @description: billing address & shipping address
     */
    public class BillingShippingAddress {
        @AuraEnabled public List<AddressWrapper> billing {get; set;}

        @AuraEnabled public List<AddressWrapper> shipping {get; set;}

        public BillingShippingAddress() {
            this.billing = new List<AddressWrapper>();
            this.shipping = new List<AddressWrapper>();
        }
    }

    /**
     * @description: address wrapper
     * order.BillTo__c
            Address_With_Program__c billingAddress = Util.getAddressInfo(order.BillTo__c);
            billingAddressId = billingAddress.Account_Address__c;
            billingAddress1 = billingAddress.Account_Address__r.Address1__c;
            billingAddress2 = billingAddress.Account_Address__r.Address2__c;
            billingAddressCity = billingAddress.Account_Address__r.City__c;
            billingAddressCountry = billingAddress.Account_Address__r.Country__c;
            billingAddressState = billingAddress.Account_Address__r.State__c;
            billingAddressPostalCode = billingAddress.Account_Address__r.Postal_Code__c;
            billingAddressContactName = billingAddress.Account_Address__r.Contact__r.Name;
     */
    public class AddressWrapper {
        @AuraEnabled public String addressId {get; set;}

        @AuraEnabled public String address1 {get; set;}

        @AuraEnabled public String address2 {get; set;}

        @AuraEnabled public String name {get; set;}

        @AuraEnabled public String city {get; set;}

        @AuraEnabled public String country {get; set;}

        @AuraEnabled public String state {get; set;}

        @AuraEnabled public String postalCode {get; set;}

        @AuraEnabled public String contactName {get; set;}

        @AuraEnabled public String contactEmail {get; set;}

        @AuraEnabled public String contactPhone {get; set;}

        @AuraEnabled public String province {get; set;}

        @AuraEnabled public String RecordTypeName {get; set;}

    }

    /**
     * @description: Order wrapper class
     */
    public class OrderWrapper {
        @AuraEnabled public String orderId {get; set;}

        @AuraEnabled public String orgCode {get; set;}

        @AuraEnabled public String currencyCode {get; set;}

        @AuraEnabled public String orderNumber {get; set;}

        @AuraEnabled public String customerId {get; set;}

        @AuraEnabled public String customerName {get; set;}

        @AuraEnabled public String customerEmail {get; set;}

        @AuraEnabled public String customerNumber {get; set;}

        @AuraEnabled public String purchaseOrderId {get; set;}

        @AuraEnabled public String billingAddressId {get; set;}

        @AuraEnabled public String shippingAddressId {get; set;}

        @AuraEnabled public AddressWrapper billTo {get; set;}

        @AuraEnabled public AddressWrapper shipTo {get; set;}

        @AuraEnabled public Boolean customerHasPortalUser {get; set;}

        @AuraEnabled public String portalUserEmail {get; set;}

        @AuraEnabled public Decimal freightFee {get; set;}

        @AuraEnabled public List<OrderItemWrapper> orderItems {get; set;}

        @AuraEnabled public List<ShipmentWrapper> shipmentList {get; set;}

        @AuraEnabled public List<ShipmentItemWrapper> shipmentItems {get; set;}

        @AuraEnabled public List<InvoiceWrapper> invoiceList {get; set;}

        @AuraEnabled public List<TaxRateWrapper> taxRates {get; set;}

        public OrderWrapper() {
            this.currencyCode = 'USD';
            this.orderItems = new List<OrderItemWrapper>();
            this.shipmentItems = new List<ShipmentItemWrapper>();
            this.taxRates = new List<TaxRateWrapper>();
            this.shipmentList = new List<ShipmentWrapper>();
            this.invoiceList = new List<InvoiceWrapper>();
        }
    }

    /**
     * @description: reverse order item wrapper class
     */
    public class OrderItemWrapper {
        @AuraEnabled public String orderItemId {get; set;}

        @AuraEnabled public String productId {get; set;}

        @AuraEnabled public String productName {get; set;}

        @AuraEnabled public String productCode {get; set;}

        @AuraEnabled public String productDescription {get; set;}

        @AuraEnabled public String brand {get; set;}

        @AuraEnabled public Decimal orderItemQuantity {get; set;}

        @AuraEnabled public String unit {get; set;}

        @AuraEnabled public Decimal invoicePrice {get; set;}

        @AuraEnabled public String subtotal {get; set;}

        @AuraEnabled public String warehouseReveivedQty{get; set;}

        @AuraEnabled public String warehouseReveicedSubTotal{get; set;}

        @AuraEnabled public String CreditMemoIssued{get; set;}
    }

    /**
     * @description: wrapper class for customer with reverse oder info
     */
    public class CustomerWrapper {
        @AuraEnabled public String customerName {get; set;}

        @AuraEnabled public String customerId {get; set;}

        @AuraEnabled public Integer submittedReverseOrder {get; set;}

        @AuraEnabled public Integer approvedReverseOrder {get; set;}

        @AuraEnabled public Integer rejectedReverseOrder {get; set;}

        @AuraEnabled public Integer recalledReverseOrder {get; set;}

        @AuraEnabled public Integer completedReverseOrder {get; set;}

        public CustomerWrapper() {
            submittedReverseOrder   = 0;
            approvedReverseOrder    = 0;
            rejectedReverseOrder    = 0;
            recalledReverseOrder    = 0;
            completedReverseOrder   = 0;
        }
    }

    /**
     * @description: product wrapper class
     */
    public class ProductWrapper {
        @AuraEnabled public String recordId {get; set;}

        @AuraEnabled public String productId {get; set;}

        @AuraEnabled public String productName {get; set;}

        @AuraEnabled public String productCode {get; set;}

        @AuraEnabled public String description {get; set;}

        @AuraEnabled public String brand {get; set;}

        @AuraEnabled public String unit {get; set;}

        @AuraEnabled public Decimal invoicePrice {get; set;}
    }

    /**
     * @description HelpText wrapper class
     */
    public class HelpTextWrapper {
        @AuraEnabled
        public String RequestReason;
        // add haibo: french
        @AuraEnabled
        public String RequestReasonFrench;

        @AuraEnabled
        public String NextStepActionFrench;

        @AuraEnabled
        public String NextStepAction;

        @AuraEnabled
        public String HelpTextContent;

        @AuraEnabled
        public String productType {get; set;}
    }

    /**
     * @description: wrapper class for response from frontend
     */
    public class ResponseWrapper {

        @AuraEnabled public Object returnData {get; set;}

        @AuraEnabled public String currentUserProfile {get; set;}

        @AuraEnabled public Boolean isSuccess {get; set;}

        @AuraEnabled public String stateCode {get; set;}

        @AuraEnabled public String errorMsg {get; set;}

        @AuraEnabled public Integer errorLineNumbber {get; set;}
    }

    /**
     * @description: pagination
     */
    public class PaginationWrapper {
        @auraEnabled public Integer pageSize {get; set;}

        @auraEnabled public Integer pageIndex {get; set;}

        @AuraEnabled public Integer totalSize {get; set;}

        @AuraEnabled public Object data {get; set;}
    }

    /**
     * @description: shipment wrapper class
     */
    public class ShipmentWrapper {
        @AuraEnabled public String recordId {get; set;}
        @AuraEnabled public String shipmentNumber {get; set;}
        @AuraEnabled public Boolean canReverse {get; set;}
        @AuraEnabled public Integer shipDays {get; set;}
        @AuraEnabled public List<ShipmentItemWrapper> shipItemList {get; set;}
        public ShipmentWrapper() {
            this.recordId = null;
            this.shipmentNumber = '';
            this.canReverse = false;
            this.shipDays = 0;
            this.shipItemList = new List<ShipmentItemWrapper>();
        }
    }

    /**
     * @description: shipment item wrapper class
     */
    public class ShipmentItemWrapper {
        @AuraEnabled public String productId {get; set;}

        @AuraEnabled public Decimal itemQuantityInOrder {get; set;}

        @AuraEnabled public Decimal itemQuantityInShipment {get; set;}
    }

    /**
     * @description:
     */
    public class ReverseOrderProductItem {
        public String Process_Status;
        public String Error_Message;
        public String Original_Order_Number_in_EBS;
        public String Model_Number;
        public String Item_Number;
        public String Quantity;
        public String Warehouse;
        public String Orde_Line_Number;
        public String Attribute1;
        public String Attribute2;
        public String Attribute3;
        public String Attribute4;
        public String Attribute5;
    }

    /**
     * @description: response body wrapper class
     */
    public class ResBody {
        public List<ReverseOrderProductItem> ArrayNode;
    }

    /**
     * @description: Tax Rate Wrapper
     */
    public class TaxRateWrapper {
        @AuraEnabled public String State {get; set;}
        @AuraEnabled public Decimal GST {get; set;}
        @AuraEnabled public Decimal HST {get; set;}
        @AuraEnabled public Decimal PST {get; set;}
        @AuraEnabled public Decimal QST {get; set;}
        @AuraEnabled public Decimal TotalRate {get; set;}
    }
}