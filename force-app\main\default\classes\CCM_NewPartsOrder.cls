public without sharing class CCM_NewPartsOrder {

    @AuraEnabled
    public static String GenerateVersionList(String productId){
        // productid，已经是 new product id ，符合要求的productid
        system.debug(productId);
        Map<String,Object> result = new Map<String,Object>();
        Set<String> versionList = new  Set<String>();
        if(String.isNotBlank(ProductId)){
            Product2 originProduct = [
                                        SELECT Id,ProductCode
                                        FROM Product2 WHERE Id != NULL
                                        AND Id =: productId
                                        AND Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                        LIMIT 1
                                    ];
            if(originProduct.ProductCode != null){
                // 通过 product code 查询，可能会有两份数据，new ，old
                Product2 pimProduct = [
                                        SELECT Id FROM Product2 
                                        WHERE Id != NULL 
                                        AND ProductCode =: originProduct.ProductCode 
                                        AND source__c = 'PIM' AND IsActive = true 
                                        AND Recordtype.Name = 'Product'
                                        AND Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                        LIMIT 1
                                    ];
                 for(Kit_Item__c ki : [
                                        SELECT Id,Diagram_Version__c
                                        FROM Kit_Item__c
                                        WHERE Product__c =: pimProduct.Id
                                        AND Recordtype.Name = 'Products and Diagram'
                                        AND Product__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                        AND Status__c = 'A']){
                    versionList.add(ki.Diagram_Version__c);
                }
                result.put('Version', versionList);
            }
        }

        return JSON.serialize(result);
    }

    @AuraEnabled
    public static String SetupBomList(String productId, String version){
        Map<String,Object> result = new Map<String,Object>();

        if(String.isNotEmpty(productId) ){
            Product2 originProduct = [
                                    SELECT Id,ProductCode 
                                    FROM Product2 WHERE Id != NULL 
                                    AND Id =: productId
                                    AND Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                    LIMIT 1];
            if(originProduct.ProductCode != null){
                Product2 pimProduct = [
                                    SELECT Id FROM Product2 WHERE Id != NULL 
                                    AND ProductCode =: originProduct.ProductCode 
                                    AND source__c = 'PIM' AND IsActive = true AND Recordtype.Name = 'Product'
                                    AND Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                    LIMIT 1];
                List<String> kiList = new List<String>();
                for(Kit_Item__c ki : [SELECT Id FROM Kit_Item__c WHERE Product__c =: pimProduct.Id 
                                        AND Recordtype.Name = 'Products and Diagram'
                                        AND Diagram_Version__c =: version ORDER BY Diagram_Page__c ASC]){
                    kiList.add(ki.Id);
                }

                if(kiList.size() > 0){
                    List<ContentDocumentLink> cdlList = [SELECT Id, LinkedEntityId, ContentDocumentId, ContentDocument.Title FROM ContentDocumentLink where LinkedEntityId IN: kiList ];

                    List<String> contentIdList = new List<String>();
                    for(String kiId : kiList) {
                        for(ContentDocumentLink cdl : cdlList){
                            if(cdl.LinkedEntityId == kiId) {
                                contentIdList.add(cdl.ContentDocumentId);
                            }
                        }
                    }

                    if(version == 'A'){
                        version = 'Spare part';
                    }else if(version == 'B'){
                        version = 'Spare part Version 1';
                    }else if(version == 'C'){
                        version = 'Spare part Version 2';
                    }
                    // add haibo: french
                    List<Kit_Item__c> itemList = [SELECT Id,ExplosionID__c,Product__r.ProductCode,Parts__r.ProductCode,
                                                        Parts__r.Description,Parts__r.Product_Description_French__c,Parts__r.Brand_Name__c,Parts__r.Name,Parts__r.Product_Name_French__c,Parts__r.Current_Price__c
                                                    FROM Kit_Item__c
                                                    WHERE Id != NULL 
                                                    AND Product__c =: pimProduct.Id
                                                    AND Product__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct 
                                                    AND Version__c =: version
                                                    AND Recordtype.Name = 'Products and Parts'
                                                    AND Status__c = 'A'
                                                    ORDER BY ExplosionID__c ASC];
                    Integer i =0;

                    List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
                    String priceBookId = '';
                    User currentUsr = Util.getUserInfo(UserInfo.getUserId());
                    if (currentUsr != null && currentUsr.Contact != null && String.isNotBlank(currentUsr.Contact.AccountId)){
                        List<Account> accList = [SELECT Id,Distributor_or_Dealer__c,ORG_Code__c FROM Account WHERE Id =: currentUsr.Contact.AccountId];
                        if(accList.size() > 0){
                            // add, napoleon, 23-1-4
                            if(accList[0].ORG_Code__c == 'CCA'){
                                priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != null AND ORG_Code__c = 'CCA' AND Type__c = 'Service' AND Is_Active__c = true AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c LIMIT 1].Price_Book__c;

                            } else {
                            //Add by napoleon, end=at 23-1-12===
                                if(accList[0].Distributor_or_Dealer__c.contains('Dealer')){
                                    priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Direct Dealer Price for Parts' AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c  LIMIT 1].Price_Book__c;
                                }else if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Distributor')){
                                    priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Distributor Price for Parts' AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c LIMIT 1].Price_Book__c;
                                }
                            }
                        }
                    }

                    List<String> ProductIdList = new List<String>();
                    for(Kit_Item__c ki : itemList){
                        ProductIdList.add(ki.Parts__c);
                    }

                    Map<String,Decimal> PartsIdMapToPrice = new Map<String,Decimal>();

                    for(PricebookEntry priceEntries : [
                        SELECT IsActive,IsDeleted,convertCurrency(UnitPrice),Product2Id,Name,
                               UseStandardPrice,Pricebook2Id
                            FROM PricebookEntry
                            WHERE IsActive = true
                            AND IsDeleted = false
                            AND Pricebook2Id =: priceBookId
                            AND Product2Id IN: ProductIdList]){
                        PartsIdMapToPrice.put(priceEntries.Product2Id, priceEntries.UnitPrice);
                    }

                    for(Kit_Item__c ki : itemList){
                        if (PartsIdMapToPrice.size() > 0){
                            ki.Parts__r.Current_Price__c = PartsIdMapToPrice.get(ki.Parts__c);
                        }
                    }


                    result.put('contentIdList', contentIdList);
                    result.put('ProductList', itemList);
                }
            }
        }

        return JSON.serialize(result);
    }

    @AuraEnabled
    public static String SetupBomList(String accountId, String productId, String version) {
        Map<String,Object> result = new Map<String,Object>();

        if(String.isNotEmpty(productId) ){
            Product2 originProduct = [SELECT Id,ProductCode FROM Product2 WHERE id =: productId LIMIT 1];
            if(originProduct.ProductCode != null){
                Product2 pimProduct = [SELECT Id FROM Product2 WHERE ProductCode =: originProduct.ProductCode AND source__c = 'PIM' AND IsActive = true AND Recordtype.Name = 'Product' LIMIT 1];
                List<String> kiList = new List<String>();
                for(Kit_Item__c ki : [SELECT Id FROM Kit_Item__c WHERE Product__c =: pimProduct.Id AND Recordtype.Name = 'Products and Diagram' AND Diagram_Version__c =: version ORDER BY Diagram_Page__c ASC]){
                    kiList.add(ki.Id);
                }

                if(kiList.size() > 0){
                    List<ContentDocumentLink> cdlList = [SELECT Id, LinkedEntityId, ContentDocumentId, ContentDocument.Title FROM ContentDocumentLink where LinkedEntityId IN: kiList ];

                    List<String> contentIdList = new List<String>();
                    for(String kiId : kiList) {
                        for(ContentDocumentLink cdl : cdlList){
                            if(cdl.LinkedEntityId == kiId) {
                                contentIdList.add(cdl.ContentDocumentId);
                            }
                        }
                    }

                    if(version == 'A'){
                        version = 'Spare part';
                    }else if(version == 'B'){
                        version = 'Spare part Version 1';
                    }else if(version == 'C'){
                        version = 'Spare part Version 2';
                    }
                    // add haibo: french
                    List<Kit_Item__c> itemList = [SELECT Id,ExplosionID__c,Product__r.ProductCode,Parts__r.ProductCode,Parts__r.Description,Parts__r.Product_Description_French__c,Parts__r.Brand_Name__c,Parts__r.Name,Parts__r.Product_Name_French__c,Parts__r.Current_Price__c
                                                FROM Kit_Item__c
                                                WHERE Product__c =: pimProduct.Id
                                                AND Version__c =: version
                                                AND Recordtype.Name = 'Products and Parts'
                                                AND Status__c = 'A'
                                                ORDER BY ExplosionID__c ASC];
                    Integer i =0;

                    List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
                    String priceBookId = '';
                    User currentUsr = Util.getUserInfo(UserInfo.getUserId());
                    if (currentUsr != null && currentUsr.Contact != null && String.isNotBlank(currentUsr.Contact.AccountId)){
                        // add napoleon,23-1-17,add Org_Code__c to return list.
                        List<Account> accList = [SELECT Id,Distributor_or_Dealer__c,ORG_Code__c FROM Account WHERE Id =: currentUsr.Contact.AccountId]; 
                 		
                        //Add by wells start at 2023-01-16====
                       	if(accList.size() > 0 && accList[0].ORG_Code__c=='CCA'){
                        	priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != null AND ORG_Code__c = 'CCA' AND Type__c = 'Service' AND Is_Active__c = true AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c LIMIT 1].Price_Book__c;
                        } else {
                        //Add by wells end=at 2023-01-16=== 
                            if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Dealer')){
                                priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Direct Dealer Price for Parts' AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c  LIMIT 1].Price_Book__c;
                            }else if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Distributor')){
                                priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Distributor Price for Parts' AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c LIMIT 1].Price_Book__c;
                            }
                        }
                        
                    } else {
                        List<Account> accList = [SELECT Id,Distributor_or_Dealer__c,ORG_Code__c FROM Account WHERE Id =: accountId];
                        //Add by wells start at 2023-01-16====
                        if(accList.size() > 0 && accList[0].ORG_Code__c=='CCA'){
                            priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != null AND ORG_Code__c = 'CCA' AND Type__c = 'Service' AND Is_Active__c = true AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c LIMIT 1].Price_Book__c;
                        } else {
                        //Add by wells end=at 2023-01-16=== 
                            if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Dealer')){
                                priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Direct Dealer Price for Parts' AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c  LIMIT 1].Price_Book__c;
                            }else if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Distributor')){
                                priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Distributor Price for Parts' AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c LIMIT 1].Price_Book__c;
                            }
                        }
                    }

                    List<String> ProductIdList = new List<String>();
                    for(Kit_Item__c ki : itemList){
                        ProductIdList.add(ki.Parts__c);
                    }

                    Map<String,Decimal> PartsIdMapToPrice = new Map<String,Decimal>();

                    for(PricebookEntry priceEntries : [
                        SELECT IsActive,IsDeleted,convertCurrency(UnitPrice),Product2Id,Name,
                               UseStandardPrice,Pricebook2Id
                            FROM PricebookEntry
                            WHERE IsActive = true
                            AND IsDeleted = false
                            AND Pricebook2Id =: priceBookId
                            AND Product2Id IN: ProductIdList]){
                        PartsIdMapToPrice.put(priceEntries.Product2Id, priceEntries.UnitPrice);
                    }

                    for(Kit_Item__c ki : itemList){
                        if (PartsIdMapToPrice.size() > 0){
                            ki.Parts__r.Current_Price__c = PartsIdMapToPrice.get(ki.Parts__c);
                        }
                    }


                    result.put('contentIdList', contentIdList);
                    result.put('ProductList', itemList);
                }
            }
        }

        return JSON.serialize(result);
    }

    // add,napoelon,23-2-10,get bom list in internal, parts order.
    @AuraEnabled
    public static String SetupBomListInternal(String accountId, String productId, String version) {
        Map<String,Object> result = new Map<String,Object>();
        // productid 已经为 new product
        if(String.isNotEmpty(productId) ){
            Product2 originProduct = [
                                        SELECT Id,ProductCode FROM Product2 WHERE id =: productId 
                                        LIMIT 1
                                    ];
            if(originProduct.ProductCode != null){
                Product2 pimProduct = [
                                        SELECT Id FROM Product2 WHERE Id != NULL 
                                        AND ProductCode =: originProduct.ProductCode
                                        AND source__c = 'PIM' 
                                        AND IsActive = true 
                                        AND Recordtype.Name = 'Product' 
                                        AND Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                        LIMIT 1];
                List<String> kiList = new List<String>();
                for(Kit_Item__c ki : [
                                        SELECT Id FROM Kit_Item__c WHERE Id != NULL 
                                        AND Product__c =: pimProduct.Id
                                        AND Product__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                        AND Recordtype.Name = 'Products and Diagram' 
                                        AND Diagram_Version__c =: version 
                                        ORDER BY Diagram_Page__c ASC]){
                    kiList.add(ki.Id);
                }

                if(kiList.size() > 0){
                    List<ContentDocumentLink> cdlList = [SELECT Id, LinkedEntityId, ContentDocumentId, ContentDocument.Title 
                                                    FROM ContentDocumentLink WHERE Id != NULL 
                                                    AND LinkedEntityId IN: kiList ];

                    List<String> contentIdList = new List<String>();
                    for(String kiId : kiList) {
                        for(ContentDocumentLink cdl : cdlList){
                            if(cdl.LinkedEntityId == kiId) {
                                contentIdList.add(cdl.ContentDocumentId);
                            }
                        }
                    }

                    if(version == 'A'){
                        version = 'Spare part';
                    }else if(version == 'B'){
                        version = 'Spare part Version 1';
                    }else if(version == 'C'){
                        version = 'Spare part Version 2';
                    }
                    // add haibo: french
                    List<Kit_Item__c> itemList = [
                                    SELECT Id,ExplosionID__c,Product__r.ProductCode,Product__r.Name,Product__r.Product_Name_French__c
                                    ,Parts__r.ProductCode,Parts__r.Description,Parts__r.Product_Description_French__c,Parts__r.Brand_Name__c
                                    ,Parts__r.Name,Parts__r.Product_Name_French__c,Parts__r.Current_Price__c,
                                    Product__r.Sellable__c, Product__r.Sellable_CCA__c, Parts__r.Sellable__c, Parts__r.Sellable_CCA__c
                                    FROM Kit_Item__c WHERE Id != NULL
                                    AND Product__c =: pimProduct.Id
                                    AND Version__c =: version
                                    AND Recordtype.Name = 'Products and Parts'
                                    AND Status__c = 'A'
                                    AND Product__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                    ORDER BY ExplosionID__c ASC
                    ];
                    Integer i =0;

                    List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
                    String priceBookId = '';
                    String priceBookAlternateId = '';
                    List<Account> accList = new List<Account>();
                    User currentUsr = Util.getUserInfo(UserInfo.getUserId());
                    if (currentUsr != null && currentUsr.Contact != null && String.isNotBlank(currentUsr.Contact.AccountId)){
                        accList = [
                                        SELECT Id, Distributor_or_Dealer__c, ORG_Code__c 
                                        FROM Account WHERE Id != NULL 
                                        AND Id =: currentUsr.Contact.AccountId
                                        ]; 
                                        // add napoleon,23-1-17,add Org_Code__c to return list.

                        //Add by wells start at 2023-01-16====
                        if(accList.size() > 0 && accList[0].ORG_Code__c=='CCA'){
                            // AND Name = 'CCA-Distributor Price for Parts' // remove name filter condition
                            priceBookId =  [
                                            SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != null 
                                            AND ORG_Code__c = 'CCA' 
                                            AND Type__c = 'Service' 
                                            AND Is_Active__c = true 
                                            AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c 
                                            LIMIT 1].Price_Book__c;
                        } else {
                            //Add by wells end=at 2023-01-16===
                            if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Dealer')){
                                priceBookId =  [
                                                SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != NULL 
                                                AND Type__c = 'Service' 
                                                AND Is_Active__c = true 
                                                AND Name = 'CNA-Direct Dealer Price for Parts' 
                                                AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c 
                                                LIMIT 1].Price_Book__c;

                            }else if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Distributor')){
                                priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != NULL 
                                            AND Type__c = 'Service' 
                                            AND Is_Active__c = true 
                                            AND Name = 'CNA-Distributor Price for Parts' 
                                            AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c 
                                            LIMIT 1].Price_Book__c;
                            }
                        }
                    } else {
                        accList = [SELECT Id, Distributor_or_Dealer__c, ORG_Code__c, AccountNumber, Is_Kobalt_Tractor__c FROM Account WHERE Id =: accountId];

                        //Add by wells start at 2023-01-16====
                        if(accList.size() > 0 && accList[0].ORG_Code__c=='CCA'){
                            //  AND Name = 'CCA-Distributor Price for Parts' // remove name filter condition
                            priceBookId =  [SELECT Price_Book__c 
                                            FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != null 
                                            AND ORG_Code__c = 'CCA' 
                                            AND Type__c = 'Service' 
                                            AND Is_Active__c = true 
                                            AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c 
                                            LIMIT 1].Price_Book__c;
                        } else {
                            //Add by wells end=at 2023-01-16===

                            if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Dealer')){
                                priceBookId =  [
                                                SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != NULL 
                                                AND Type__c = 'Service' 
                                                AND Is_Active__c = true 
                                                AND Name = 'CNA-Direct Dealer Price for Parts' 
                                                AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c  LIMIT 1].Price_Book__c;
                            }else if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Distributor')){
                                priceBookId =  [
                                                SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != NULL 
                                                AND Type__c = 'Service' 
                                                AND Is_Active__c = true 
                                                AND Name = 'CNA-Distributor Price for Parts' 
                                                AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c 
                                                LIMIT 1].Price_Book__c;
                            }
                            if(accList.size() > 0) {
                                if(accList[0].AccountNumber == 'B10127') {
                                    priceBookId =  [SELECT Id FROM Pricebook2 WHERE Name = 'CNA-MSRP for Parts' LIMIT 1].Id;
                                }
                                if(accList[0].AccountNumber == 'B10127' || accList[0].Is_Kobalt_Tractor__c) {
                                    List<Pricebook2> alternatePriceBooks = [SELECT Id FROM Pricebook2 WHERE Name = 'Lowe\'s Store collect'];
                                    for(PriceBook2 priceBook : alternatePriceBooks) {
                                        priceBookAlternateId = priceBook.Id;
                                    }
                                }
                            }
                        }
                    }

                    List<String> ProductIdList = new List<String>();
                    for(Kit_Item__c ki : itemList){
                        ProductIdList.add(ki.Parts__c);
                    }

                    Map<String,Decimal> PartsIdMapToPrice = new Map<String,Decimal>();
                    Map<String,Decimal> partsIdMapToPriceAlternate = new Map<String,Decimal>();
                    for(PricebookEntry priceEntries : [
                            SELECT IsActive,IsDeleted,UnitPrice,Product2Id,Name,
                                    UseStandardPrice,Pricebook2Id
                            FROM PricebookEntry
                            WHERE IsActive = true
                            AND IsDeleted = false
                            AND Pricebook2Id =: priceBookId
                            AND Product2Id IN: ProductIdList]){
                        PartsIdMapToPrice.put(priceEntries.Product2Id, priceEntries.UnitPrice);
                    }

                    if(String.isNotBlank(priceBookAlternateId)) {
                        for(PricebookEntry priceEntries : [
                                SELECT IsActive,IsDeleted,UnitPrice,Product2Id,Name,
                                        UseStandardPrice,Pricebook2Id
                                FROM PricebookEntry
                                WHERE IsActive = true
                                AND IsDeleted = false
                                AND Pricebook2Id =: priceBookAlternateId
                                AND Product2Id IN: ProductIdList
                                AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct]){
                            partsIdMapToPriceAlternate.put(priceEntries.Product2Id, priceEntries.UnitPrice);
                        }
                    }

                    for(Kit_Item__c ki : itemList){
                        if(PartsIdMapToPrice.containsKey(ki.Parts__c)) {
                            if(accList.size() > 0 && accList[0].AccountNumber == 'B10127') {
                                ki.Parts__r.Current_Price__c = PartsIdMapToPrice.get(ki.Parts__c) * 0.7;
                            }
                            else {
                                ki.Parts__r.Current_Price__c = PartsIdMapToPrice.get(ki.Parts__c);
                            }
                        }
                        else if(partsIdMapToPriceAlternate.containsKey(ki.Parts__c)) {
                            if(accList.size() > 0 && (accList[0].AccountNumber == 'B10127' || accList[0].Is_Kobalt_Tractor__c)) {
                                ki.Parts__r.Current_Price__c = partsIdMapToPriceAlternate.get(ki.Parts__c);
                            }
                        }
                    }
                    result.put('contentIdList', contentIdList);
                    result.put('ProductList', itemList);
                }
            }
        }
        return JSON.serialize(result);
    }

    @AuraEnabled
    public static String SetupBomListExternal(String productId, String version){
        Map<String,Object> result = new Map<String,Object>();
        // productid 为 new product。
        if(String.isNotEmpty(productId) ){
            Product2 originProduct = [SELECT Id,ProductCode FROM Product2 WHERE id =: productId LIMIT 1];
            if(originProduct.ProductCode != null){
                // pimProduct 限定为 new product
                // 根据 productcode 查询数据，可能会有多份数据
                Product2 pimProduct = [
                                        SELECT Id FROM Product2 WHERE Id != NULL 
                                        AND ProductCode =: originProduct.ProductCode 
                                        AND source__c = 'PIM' 
                                        AND IsActive = true
                                        AND Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                        AND Recordtype.Name = 'Product' 
                                        LIMIT 1
                                    ];
                List<String> kiList = new List<String>();
                for(Kit_Item__c ki : [
                                        SELECT Id FROM Kit_Item__c WHERE Id != NULL 
                                        AND Product__c =: pimProduct.Id 
                                        AND Recordtype.Name = 'Products and Diagram' 
                                        AND Diagram_Version__c =: version 
                                        AND Product__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                        ORDER BY Diagram_Page__c ASC]){
                    kiList.add(ki.Id);
                }

                if(kiList.size() > 0){
                    List<ContentDocumentLink> cdlList = [
                                            SELECT Id, LinkedEntityId, ContentDocumentId, ContentDocument.Title
                                            FROM ContentDocumentLink WHERE Id != NULL 
                                            AND LinkedEntityId IN: kiList 
                                        ];

                    List<String> contentIdList = new List<String>();
                    for(String kiId : kiList) {
                        for(ContentDocumentLink cdl : cdlList){
                            if(cdl.LinkedEntityId == kiId) {
                                contentIdList.add(cdl.ContentDocumentId);
                            }
                        }
                    }

                    if(version == 'A'){
                        version = 'Spare part';
                    }else if(version == 'B'){
                        version = 'Spare part Version 1';
                    }else if(version == 'C'){
                        version = 'Spare part Version 2';
                    }

                    List<Kit_Item__c> itemList = [
                        // add haibo: french
                        SELECT Id,ExplosionID__c,Product__r.ProductCode,Product__r.Name,Product__r.Product_Name_French__c
                        ,Parts__r.ProductCode,Parts__r.Description,Parts__r.Product_Description_French__c,
                        Parts__r.Brand_Name__c,Parts__r.Name,Parts__r.Product_Name_French__c,Parts__r.Current_Price__c,
                        Parts__r.Sellable__c, Parts__r.Sellable_CCA__c
                        FROM Kit_Item__c WHERE Id != NULL 
                        AND Product__c =: pimProduct.Id
                        AND Version__c =: version
                        AND Recordtype.Name = 'Products and Parts'
                        AND Status__c = 'A'
                        AND Product__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                        ORDER BY ExplosionID__c ASC
                    ];
                    
                    Integer i =0;

                    List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
                    List<PricebookEntry> prodEntryAlternateList = new List<PricebookEntry>();
                    String priceBookId = '';
                    String priceBookAlternateId = '';
                    User currentUsr = Util.getUserInfo(UserInfo.getUserId());
                    List<Account> accList = new List<Account>();
                    if (currentUsr != null && currentUsr.Contact != null && String.isNotBlank(currentUsr.Contact.AccountId)){
                        accList = [
                                    SELECT Id, Distributor_or_Dealer__c, ORG_Code__c, AccountNumber, Name, Is_Kobalt_Tractor__c
                                    FROM Account WHERE Id =: currentUsr.Contact.AccountId
                                ];
                        if(accList.size() > 0){
                            // @mark, napoleon, 23-1-4
                            if(accList[0].ORG_Code__c == 'CCA'){
                                // AND Name = 'CCA-Distributor Price for Parts' // remove name filter condition
                                priceBookId =  [
                                        SELECT Price_Book__c 
                                        FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != null 
                                        AND ORG_Code__c = 'CCA' 
                                        AND Type__c = 'Service' 
                                        AND Is_Active__c = true 
                                        AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c 
                                        LIMIT 1].Price_Book__c;

                            } else {
                                //Add by napoleon, end=at 23-1-12===
                                if(accList[0].Distributor_or_Dealer__c.contains('Dealer')){
                                    priceBookId =  [
                                                    SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != NULL 
                                                    AND Type__c = 'Service' 
                                                    AND Is_Active__c = true 
                                                    AND Name = 'CNA-Direct Dealer Price for Parts' 
                                                    AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c 
                                                    LIMIT 1].Price_Book__c;
                                }else if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Distributor')){
                                    priceBookId =  [
                                                    SELECT Price_Book__c 
                                                    FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != NULL 
                                                    AND Type__c = 'Service' 
                                                    AND Is_Active__c = true 
                                                    AND Name = 'CNA-Distributor Price for Parts' 
                                                    AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c 
                                                    LIMIT 1].Price_Book__c;
                                }
                                if(accList.size() > 0) {
                                    if(accList[0].AccountNumber == 'B10127') {
                                        priceBookId =  [SELECT Id FROM Pricebook2 WHERE Name = 'CNA-MSRP for Parts' LIMIT 1].Id;
                                    }
                                    if(accList[0].AccountNumber == 'B10127' || accList[0].Is_Kobalt_Tractor__c) {
                                        List<Pricebook2> alternatePriceBooks = [SELECT Id FROM Pricebook2 WHERE Name = 'Lowe\'s Store collect'];
                                        for(PriceBook2 priceBook : alternatePriceBooks) {
                                            priceBookAlternateId = priceBook.Id;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    List<String> ProductIdList = new List<String>();
                    for(Kit_Item__c ki : itemList){
                        ProductIdList.add(ki.Parts__c);
                    }

                    Map<String,Decimal> PartsIdMapToPrice = new Map<String,Decimal>();
                    Map<String,Decimal> partsIdMapToPriceAlternate = new Map<String,Decimal>();

                    for(PricebookEntry priceEntries : [
                            SELECT IsActive,IsDeleted,UnitPrice,Product2Id,Name,
                                    UseStandardPrice,Pricebook2Id
                            FROM PricebookEntry
                            WHERE IsActive = true
                            AND IsDeleted = false
                            AND Pricebook2Id =: priceBookId
                            AND Product2Id IN: ProductIdList
                            AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct]){
                        PartsIdMapToPrice.put(priceEntries.Product2Id, priceEntries.UnitPrice);
                    }

                    if(String.isNotBlank(priceBookAlternateId)) {
                        for(PricebookEntry priceEntries : [
                                SELECT IsActive,IsDeleted,UnitPrice,Product2Id,Name,
                                        UseStandardPrice,Pricebook2Id
                                FROM PricebookEntry
                                WHERE IsActive = true
                                AND IsDeleted = false
                                AND Pricebook2Id =: priceBookAlternateId
                                AND Product2Id IN: ProductIdList
                                AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct]){
                            partsIdMapToPriceAlternate.put(priceEntries.Product2Id, priceEntries.UnitPrice);
                        }
                    }
                    

                    for(Kit_Item__c ki : itemList){
                        if(PartsIdMapToPrice.containsKey(ki.Parts__c)) {
                            if(accList.size() > 0 && accList[0].AccountNumber == 'B10127') {
                                ki.Parts__r.Current_Price__c = PartsIdMapToPrice.get(ki.Parts__c) * 0.7;
                            }
                            else {
                                ki.Parts__r.Current_Price__c = PartsIdMapToPrice.get(ki.Parts__c);
                            }
                        }
                        else if(partsIdMapToPriceAlternate.containsKey(ki.Parts__c)) {
                            if(accList.size() > 0 && (accList[0].AccountNumber == 'B10127' || accList[0].Is_Kobalt_Tractor__c)) {
                                ki.Parts__r.Current_Price__c = partsIdMapToPriceAlternate.get(ki.Parts__c);
                            }
                        }
                    }

                    result.put('contentIdList', contentIdList);
                    result.put('ProductList', itemList);
                }
            }
        }

        return JSON.serialize(result);
    }
    // add end

    @AuraEnabled
    public static String getData(String recordId){
        return CCM_PartsOrder_DetailCtl.getData(recordId,'');
    }

    @AuraEnabled
    public static Boolean isInKobaltScope(String customerId){
        return CCM_PartsOrder_DetailCtl.isInKobaltScope(customerId);
    }

    @AuraEnabled
    public static String getPriceBook(String prodId) {
        InitData initD = new InitData();
        if (String.isNotBlank(prodId)){
            // 传入 的 productid 就是新的 product，is_history_product__c = false
            List<Product2> prodInfo = [
                                        SELECT Id,Name, Brand_Name__c,Item_Number__c,ProductCode 
                                        FROM Product2 WHERE Id != NULL 
                                        AND Id =:prodId
                                        AND Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                    ];
            if (prodInfo != null && prodInfo.size() > 0){
                initD.product = prodInfo[0];
                List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
                List<PricebookEntry> prodEntryAlternateList = new List<PricebookEntry>();
                User currentUsr = Util.getUserInfo(UserInfo.getUserId());
                if (currentUsr != null && currentUsr.Contact != null && String.isNotBlank(currentUsr.Contact.AccountId)){
                    List<Account> accList = [SELECT Id, Distributor_or_Dealer__c, AccountNumber, ORG_Code__c, Name, Is_Kobalt_Tractor__c FROM Account WHERE Id =: currentUsr.Contact.AccountId];
                    String priceBookId = '';
                    //Add by wells start at 2023-01-03====
                    if(accList.size() > 0 && accList[0].ORG_Code__c=='CCA'){
                        // AND Name = 'CCA-Distributor Price for Parts' // remove name filter condition
                        priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != null AND ORG_Code__c = 'CCA' AND Type__c = 'Service' AND Is_Active__c = true AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c LIMIT 1].Price_Book__c;
                    } else {
                        //Add by wells end=at 2023-01-03===
                        if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Dealer')){
                            priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Direct Dealer Price for Parts' AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c  LIMIT 1].Price_Book__c;
                        }else if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Distributor')){
                            priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Distributor Price for Parts' AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c LIMIT 1].Price_Book__c;
                        }
                        if(accList.size() > 0){
                            if(accList[0].AccountNumber == 'B10127') {
                                priceBookId =  [SELECT Id FROM Pricebook2 WHERE Name = 'CNA-MSRP for Parts' LIMIT 1].Id;
                            }
                            if(accList[0].AccountNumber == 'B10127' || accList[0].Is_Kobalt_Tractor__c) {
                                List<Pricebook2> alternatePriceBooks = [SELECT Id FROM Pricebook2 WHERE Name = 'Lowe\'s Store collect'];
                                if(!alternatePriceBooks.isEmpty()) {
                                    prodEntryAlternateList = Util.getPriceBookEntryByProdIdWithOutConvertCurrency(prodId, alternatePriceBooks[0].Id);
                                }
                            }
                        }
                    }
                    prodEntryList = Util.getPriceBookEntryByProdIdWithOutConvertCurrency(prodId, priceBookId);

                    if(accList[0].AccountNumber == 'B10127'){
                        if (prodEntryList != null && prodEntryList.size() > 0 && prodEntryList[0].UnitPrice != 0){
                            prodEntryList[0].UnitPrice = prodEntryList[0].UnitPrice * 0.7;
                            initD.priceBookEntry  = prodEntryList[0];
                        }
                        else if(!prodEntryAlternateList.isEmpty()) {
                            initD.priceBookEntry  = prodEntryAlternateList[0];
                        }
                    }else{
                        if (prodEntryList != null && prodEntryList.size() > 0 && prodEntryList[0].UnitPrice != 0){
                            initD.priceBookEntry  = prodEntryList[0];
                        }
                        else if (!prodEntryAlternateList.isEmpty()){
                            prodEntryAlternateList[0].UnitPrice = prodEntryAlternateList[0].UnitPrice;
                            initD.priceBookEntry  = prodEntryAlternateList[0];
                        }
                    }
                    
                }
            }
        }
        return JSON.serialize(initD);
    }

    @AuraEnabled
    public static String getInternalPriceBook(String prodId, String customerId){
        InitData initD = new InitData();
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        List<PricebookEntry> prodEntryAlternateList = new List<PricebookEntry>();
        if (String.isNotBlank(prodId)){
            List<Product2> prodInfo = [SELECT Id,Name, Brand_Name__c,Item_Number__c,ProductCode 
                                        FROM Product2 WHERE Id != NULL 
                                        AND Id =:prodId
                                        AND Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                        ];
            if (prodInfo != null && prodInfo.size() > 0){
                initD.product = prodInfo[0];

                if (String.isNotBlank(customerId)){

                    List<Account> accList = [
                                            SELECT Id, Distributor_or_Dealer__c, AccountNumber, ORG_Code__c, Is_Kobalt_Tractor__c
                                            FROM Account WHERE Id != NULL
                                            AND Id =: customerId];
                                            //add Org_Code for price book by wells at 2023-01-03
                    String priceBookId = '';
                    //Add by wells start at 2023-01-03====
                    if(accList.size() > 0 && accList[0].ORG_Code__c=='CCA'){
                        priceBookId =  [
                                        SELECT Id,Price_Book__c 
                                        FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != null
                                        AND ORG_Code__c = 'CCA' 
                                        AND Type__c = 'Service' 
                                        AND Is_Active__c = true 
                                        AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c 
                                        LIMIT 1].Price_Book__c;
                    } else{
                    //Add by wells end=at 2023-01-03===  
                        if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Dealer')){
                            priceBookId =  [
                                            SELECT Id, Price_Book__c 
                                            FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != NULL 
                                            AND Type__c = 'Service' 
                                            AND Is_Active__c = true 
                                            AND Name = 'CNA-Direct Dealer Price for Parts' 
                                            AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c
                                            LIMIT 1].Price_Book__c;
                        }else if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Distributor')){
                            priceBookId =  [
                                            SELECT Id, Price_Book__c 
                                            FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != NULL 
                                            AND Type__c = 'Service' 
                                            AND Is_Active__c = true 
                                            AND Name = 'CNA-Distributor Price for Parts' 
                                            AND Customer_Type__c =: accList[0].Distributor_or_Dealer__c 
                                            LIMIT 1].Price_Book__c;
                        }
                        if(accList.size() > 0){
                            if(accList[0].AccountNumber == 'B10127') {
                                priceBookId =  [SELECT Id FROM Pricebook2 WHERE Name = 'CNA-MSRP for Parts' LIMIT 1].Id;
                            }
                            if(accList[0].AccountNumber == 'B10127' || accList[0].Is_Kobalt_Tractor__c) {
                                List<Pricebook2> alternatePriceBooks = [SELECT Id FROM Pricebook2 WHERE Name = 'Lowe\'s Store collect'];
                                if(!alternatePriceBooks.isEmpty()) {
                                    prodEntryAlternateList = Util.getPriceBookEntryByProdIdWithOutConvertCurrency(prodId, alternatePriceBooks[0].Id);
                                }
                            }
                        }
                    }
                    prodEntryList = Util.getPriceBookEntryByProdIdWithOutConvertCurrency(prodId, priceBookId);

                    if(accList[0].AccountNumber == 'B10127'){
                        if (prodEntryList != null && prodEntryList.size() > 0 && prodEntryList[0].UnitPrice != 0){
                            prodEntryList[0].UnitPrice = prodEntryList[0].UnitPrice * 0.7;
                            initD.priceBookEntry  = prodEntryList[0];
                        }
                        else if(!prodEntryAlternateList.isEmpty()) {
                            initD.priceBookEntry  = prodEntryAlternateList[0];
                        }
                    }else{
                        if (prodEntryList != null && prodEntryList.size() > 0 && prodEntryList[0].UnitPrice != 0){
                            initD.priceBookEntry  = prodEntryList[0];
                        }
                        else if (!prodEntryAlternateList.isEmpty()){
                            initD.priceBookEntry  = prodEntryAlternateList[0];
                        }
                    }
                }
            }
        }
        return JSON.serialize(initD);
    }

    @AuraEnabled
    public static String getEntryCondition(){
        Set<Id> productIdSet = new Set<Id>();
        String accId = CCM_PortalPageUtil.getCustomerByUser(UserInfo.getUserId());
        List<Account> customerType = [SELECT Distributor_or_Dealer__c FROM Account WHERE Id =: accId];
        String priceBookId = null;
        if(customerType.size() > 0){
            if(customerType[0].Distributor_or_Dealer__c.contains('Dealer') || customerType[0].Distributor_or_Dealer__c.contains('Service Center')){
                priceBookId = [
                                SELECT Id, Price_Book__c 
                                FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != NULL 
                                AND Type__c = 'Service' 
                                AND Is_Active__c = true 
                                AND Name = 'CNA-Direct Dealer Price for Parts' 
                                LIMIT 1].Price_Book__c;
            }else if(customerType[0].Distributor_or_Dealer__c.contains('Distributor')){
                priceBookId = [
                                SELECT Id, Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Id != NULL 
                                AND Type__c = 'Service' 
                                AND Is_Active__c = true 
                                AND Name = 'CNA-Distributor Price for Parts' 
                                LIMIT 1].Price_Book__c;
            }
        }
        // item to model
        String sql = 'SELECT Product2Id FROM PricebookEntry WHERE Id != NULL '
                     + ' AND Pricebook2Id = \'' + priceBookId + '\' ' 
                     + ' AND Product2.IsActive = true '
                     + ' AND Product2.Source__c = \'PIM\' ' 
                     + ' AND Product2.RecordType.Name = \'Parts\'';

        return sql;
    }
    @AuraEnabled
    public static String saveQuotation(String poInfo, String poItemInfos, String customerId, Integer currentStep){
        InitData initD = new InitData();
        String strPoItemCurrencyIsoCode = 'USD';
        String strCcaPartsOrderItemType = 'CA Interco Line';

        if (String.isNotBlank(poInfo)){
            Map<String, String> rtMap = Util.getSobjectRecordTypeNameMap('Purchase_Order__c');
            User currentUsr = Util.getUserInfo(UserInfo.getUserId());
            Purchase_Order__c po = (Purchase_Order__c)JSON.deserialize(poInfo, Purchase_Order__c.class);
            po.Step__c = 'A';
            po.Customer__c = customerId;
            po.Freight_Fee_Waived__c = 0;
            Po.Buyer_Contact__c = currentUsr.Contact.Id;
            Po.RecordTypeId = rtMap.get('Place_Parts_Order');

            // add,napoleon, when save in selecting product,set other fee field as 0.00
            // refer to `total_amount__c` field which calculating the result fee;
            // Product_Price__c + Freight_Fee__c + Handling_Fee__c - ABS(Freight_Fee_Waived__c) - ABS(Extra_Freight_Fee_To_Be_Waived__c) - ABS( Discount_Amount__c) + QST__c + GST__c + HST__c + Surcharge_Amount__c
            po.QST__c = 0.00;
            po.GST__c = 0.00;
            po.HST__c = 0.00;
            po.PST__c = 0.00;

            po.Freight_Fee__c = 0.00;
            po.Freight_Fee_Waived__c = 0.00;
            po.Extra_Freight_Fee_To_Be_Waived__c = 0.00;
            po.Handling_Fee__c = 0.00;
            po.Discount_Amount__c = 0.00;
            po.Surcharge_Amount__c = 0.00;
            // at the next process, fill address, when save parts order, freight_fee__c,freight_fee_waived__c will be update and tax fields will update automatically in the background.
            // can view all fee info at `view` page and the next `preview & submit` page
            // add end

            // update,napoleon,22-12-30. order type c follow customer.org_code__c
            String strOrgCodeOfCustomer = [SELECT Org_Code__c FROM Account WHERE Id = :customerId limit 1].get(0).Org_Code__c;
            if (String.isNotBlank(strOrgCodeOfCustomer)) {
                po.Org_Id__c = strOrgCodeOfCustomer;
                if (strOrgCodeOfCustomer == CCM_Constants.ORG_CODE_CNA) {
                    po.Order_Type__c = CCM_Constants.PURCHASE_ORDER_ORDER_TYPE_CNA_PARTS_SALES_ORDER_USD;
                    po.CurrencyIsoCode  = 'USD';
                } else if (strOrgCodeOfCustomer == CCM_Constants.ORG_CODE_CCA){
                    po.Order_Type__c = CCM_Constants.PURCHASE_ORDER_ORDER_TYPE_CCA_PARTS_SALES_ORDER_CAD;
                    po.CurrencyIsoCode  = 'CAD';
                    strPoItemCurrencyIsoCode = 'CAD';
                }
            }
            // update end

            upsert po;
            initD.po = po;
            initD.recordId = po.Id;

            List<Purchase_Order_Item__c> poItemsExsit = [SELECT Id FROM Purchase_Order_Item__c WHERE Purchase_Order__c = :po.Id];

            if (String.isNotBlank(poItemInfos)){
                system.debug(poItemInfos);
                List<Object> deserialized = (List<Object>)JSON.deserializeUntyped(poItemInfos);
                System.debug(LoggingLevel.INFO, '*** deserialized: ' + deserialized);
                List<String > finalJsonString = new List<String>();
                List<Purchase_Order_Item__c> poItems = new List<Purchase_Order_Item__c>();
                String accId = CCM_PortalPageUtil.getCustomerByUser(UserInfo.getUserId());
                List<Account> customerType = [SELECT Distributor_or_Dealer__c,AccountNumber,Org_Code__c FROM Account WHERE Id =: accId];
                String priceBookId = null;
                if(customerType.size() > 0){
                    //Add by wells start at 2023-01-03====
                    if(customerType[0].ORG_Code__c == 'CCA'){
                        priceBookId =  [
                            SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c 
                            WHERE Id != null 
                                AND Type__c = 'Service'
                                AND Is_Active__c = true
                                AND Customer_Type__c =: customerType[0].Distributor_or_Dealer__c
                            LIMIT 1
                            ].Price_Book__c;                   
                    } else {   
                    //Add by wells end=at 2023-01-03===     
                        if(customerType[0].Distributor_or_Dealer__c.contains('Dealer') || customerType[0].Distributor_or_Dealer__c.contains('Service Center')){
                            priceBookId = [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Direct Dealer Price for Parts' LIMIT 1].Price_Book__c;
                        }else if (customerType[0].Distributor_or_Dealer__c.contains('Distributor')){
                            priceBookId = [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Distributor Price for Parts'  LIMIT 1].Price_Book__c;
                        }
                    }
                }
                if(customerType.size() > 0 && customerType[0].AccountNumber == 'B10127'){
                    priceBookId =  [SELECT Id FROM Pricebook2 WHERE Name = 'CNA-MSRP for Parts' LIMIT 1].Id;
                }

                for (Object instance : deserialized){
                    Map<String, Object> obj = (Map<String, Object>)instance;
                    Purchase_Order_Item__c item = new Purchase_Order_Item__c();
                    if(String.valueOf(obj.get('Parts__c')) != null){
                        item.Product__c = String.valueOf(obj.get('Parts__c'));
                    }else{
                        item.Product__c = String.valueOf(obj.get('Product__c'));
                    }
                    item.Quantity__c = Integer.valueOf(obj.get('Quantity__c'));
                    item.Purchase_Order__c = po.Id;
                    item.Sub_Total__c = String.valueOf(obj.get('Sub_Total__c')) == null? 0.00: Decimal.valueOf(String.valueOf(obj.get('Sub_Total__c'))).setScale(2);
                    item.Brand__c = String.valueOf(obj.get('Brand_Name__c'));
                    item.ProductCode__c = String.valueOf(obj.get('ProductCode__c'));
                    item.Unit_Price__c = Decimal.valueOf((String)obj.get('Unit_Price__c'));
                    item.Unit_Price__c = item.Unit_Price__c.setScale(2);
                    item.Price_Book__c = priceBookId;
                    if(strOrgCodeOfCustomer == CCM_Constants.ORG_CODE_CCA) {
                        item.Ship_Date__c = Date.today();
                    }
                    else {
                        item.Ship_Date__c = getMinSelectDate(po.Submit_Date__c);
                    }
                    
                    item.CurrencyIsoCode = strPoItemCurrencyIsoCode;
                    if(po.Order_Type__c == CCM_Constants.PURCHASE_ORDER_ORDER_TYPE_CCA_PARTS_SALES_ORDER_CAD){
                        item.Line_Type__c = strCcaPartsOrderItemType;
                    }
                    poItems.add(item);
                }

                upsert poItems;

                if(poItemsExsit.size() > 0){
                    delete poItemsExsit;
                }

                poItems = [SELECT Id,
                                Name,
                                Brand__c,
                                ProductCode__c,
                                Product__c,
                                Product__r.Name,
                                Product__r.Description,
                                Product__r.Item_Number__c,
                                Product__r.Brand_Name__c,
                                Quantity__c,
                                List_Price__c,
                                Unit_Price__c,
                                MSRP__c,
                                Sub_Total__c
                                FROM Purchase_Order_Item__c WHERE Purchase_Order__c = :po.Id];
                initD.poItems = poItems;
                initD.isSuccess = true;
            }
        }
        return JSON.serialize(initD);
    }

    private static Date getMinSelectDate(Datetime submitDate) {
        Set<String> weekends = new Set<String> {
            'Saturday',
            'Sunday'
        };
        Datetime orderDate = Datetime.now();
        if(submitDate != null) {
            orderDate = submitDate;
        }

        Integer selectDays = 3;
        while (selectDays > 0) {
            orderDate = orderDate.addDays(1);
            String dayOfWeek = orderDate.format('EEEE');
            if(!weekends.contains(dayOfWeek)) {
                selectDays = selectDays - 1;
            }
        }

        return Date.newInstance(orderDate.year(), orderDate.month(), orderDate.day());
    }

    @AuraEnabled
    public static String deleteQuotation(String recordId){
        if (String.isNotBlank(recordId)){
            Purchase_Order_Item__c poItem = [SELECT Id FROM Purchase_Order_Item__c WHERE Id =: recordId];
            delete poItem;

            return 'Success';
        }
        return 'Fail';
    }

    @AuraEnabled
    public static String saveData(String poString, String poItemString, Integer currentStep){
        return CCM_PartsOrder_DetailCtl.saveData(poString, poItemString, currentStep);
    }


    public class InitData{
        @AuraEnabled public Sales_Program__c authBrandInfo {get; set;}
        @AuraEnabled public PricebookEntry priceBookEntry {get; set;}
        @AuraEnabled public Product2 product {get; set;}
        @AuraEnabled public Integer currentStep {get; set;}
        @AuraEnabled public String recordId {get; set;}
        @AuraEnabled public Purchase_Order__c po {get; set;}
        @AuraEnabled public List<Purchase_Order_Item__c> poItems {get; set;}
        @AuraEnabled public Boolean isSuccess {get; set;}
        @AuraEnabled public String errorMsg {get; set;}


        public InitData(){
            this.authBrandInfo = new Sales_Program__c();
            this.priceBookEntry = new PricebookEntry();
            this.product = new Product2();
            this.po = new Purchase_Order__c();
            this.poItems = new List<Purchase_Order_Item__c>();
            this.isSuccess = false;
            this.errorMsg = '';
        }
    }

   
}