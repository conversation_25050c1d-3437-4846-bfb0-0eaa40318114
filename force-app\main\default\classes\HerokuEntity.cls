/**
* @Author: Zack
* @Date: 2018-07-17
* @Description:  
* @Test_Class: 
* @Related_Class: 
---------------------------------------------------
* @Last_Modified_by: 
* @Last_Modified_time: 
* @Modifiy_Purpose: 
*/
global class HerokuEntity {
    //********************HerokuResponseEntity******************//
    global virtual class ResponseEntity {
        public Integer statusCode;
        public String message;
        public ResponseEntity(Integer statusCode, String message) {
            this.statusCode = statusCode;
            this.message = message;
        }
    }

    // 关于用户的响应
    public class RCUserResponseEntity extends ResponseEntity {
        private String id;
        public RCUserResponseEntity(Integer statusCode, String message, String id) {
            super(statusCode, message);
            this.id = id;
        }
    }

    // 关于IoT接口中查询用户数据的响应
    public class RCUserDataResponseEntity extends ResponseEntity {
        private String id;
        private String name;
        private String email;
        private String password;
        private String address;
        private String address2;
        private String city;
        private String state;
        private String phone;
        private String lawnSize;
        private String firstName;
        private String shippingAddress;
        private Boolean sendMarketingEmails;
        public RCUserDataResponseEntity(Integer statusCode, String message, Account acc) {
            super(statusCode, message);
            this.id = acc.Id;
            this.name = acc.LastName;
            this.email = acc.PersonEmail;
            // Add by yujie for service migration, not include merge logic
            //this.password = acc.Password__c;
            this.password = acc.EGO_password__c;
            if (String.isNotEmpty(acc.ShippingStreet)) {
                String[] strArr = acc.ShippingStreet.split(' ');
                this.address = strArr[0];
                if (strArr.size() > 1) {
                    this.address2 = acc.ShippingStreet.split(' ')[1];
                }
            }
            this.city = acc.ShippingCity;
            this.state = acc.ShippingState;
            this.phone = acc.Phone;
            this.lawnSize = acc.Lawn_Size__c;
            this.firstName = acc.FirstName;
            this.sendMarketingEmails = acc.send_marketing_emails__c;
            this.shippingAddress = '';
            for (String strAP : new List<String>{
                acc.ShippingStreet,
                acc.ShippingCity,
                acc.ShippingState,
                acc.ShippingPostalCode,
                acc.ShippingCountry
            }) {
                // prettier-ignore
                if (String.isBlank(strAP)) continue;
                this.shippingAddress += strAP + ', ';
            }
            this.shippingAddress = this.shippingAddress.removeEnd(', ');
        }
    }

    // add by yujie for EU
    // 关于EU接口中查询用户数据的响应
    public class RCUserInfoResponseEntity extends ResponseEntity {
        private String id;
        private String firstName;
        private String lastName;
        private String email;
        private String phone;
        private String password;
        private String address1;
        private String address2;
        private String address3;
        private String postcode;
        private String country;
        private String company;
        private Boolean marketingOptIn;
        private String siteOrigin;
        private Boolean sendMarketingEmails;
        private String organizationName;

        public RCUserInfoResponseEntity(Integer statusCode, String message, Account acc) {
            super(statusCode, message);
            this.id = acc.Id;
            this.firstName = acc.FirstName;
            this.lastName = acc.LastName;
            this.email = acc.PersonEmail;
            //this.phone = acc.Phone;
            this.phone = acc.PersonMobilePhone;
            this.password = acc.EGO_password__c;
            this.address1 = acc.ShippingStreet;
            this.address2 = acc.ShippingCity;
            this.address3 = acc.ShippingState;
            this.postcode = acc.ShippingPostalCode;
            this.country = acc.ShippingCountry;
            this.company = acc.Company__pc;
            this.marketingOptIn = acc.MarketingOptIn__pc;
            this.siteOrigin = acc.Site_Origin__pc;
            this.sendMarketingEmails = acc.send_marketing_emails__c;
            this.organizationName = acc.Organization_Name__c;
        }
    }

    // 更改Case的响应
    public class RCCaseResponseEntity extends ResponseEntity {
        private String id;
        public RCCaseResponseEntity(Integer statusCode, String message, String caseId) {
            super(statusCode, message);
            this.id = caseId;
        }
    }

    // 批量查询Case的响应
    public class RCCaseListResponseEntity extends ResponseEntity {
        private RCCaseItemEntity[] cases;
        public RCCaseListResponseEntity(Integer statusCode, String message, RCCaseItemEntity[] caseItemArr) {
            super(statusCode, message);
            this.cases = caseItemArr;
        }
    }

    // 批量查询CaseType的响应
    public class RCCaseTypeListResponseEntity extends ResponseEntity {
        private String[] case_type;
        public RCCaseTypeListResponseEntity(Integer statusCode, String message, String[] caseTypeArr) {
            super(statusCode, message);
            this.case_type = caseTypeArr;
        }
    }

    // 更改Case状态的响应
    public class RCChangeCaseStatusResponseEntity extends ResponseEntity {
        private String id;
        public RCChangeCaseStatusResponseEntity(Integer statusCode, String message, String caseId) {
            super(statusCode, message);
            this.id = caseId;
        }
    }

    // 关于批量查询Order的响应体
    public class RCOrderResponseEntity extends ResponseEntity {
        private RCOrderItemEntity[] orderItemList;
        public RCOrderResponseEntity(Integer statusCode, String message, RCOrderItemEntity[] arrList) {
            super(statusCode, message);
            this.orderItemList = arrList;
        }
    }

    // 关于批量查询Warranty的响应体
    public class RCWarrantyListResponseEntity extends ResponseEntity {
        private RCWarrantyEntity[] warranties;
        public RCWarrantyListResponseEntity(Integer statusCode, String message, RCWarrantyEntity[] warranties) {
            super(statusCode, message);
            this.warranties = warranties;
        }
    }

    //********************HerokuRequestEntity******************//
    public virtual class RequestEntity {
    }

    //关于创建或更新用户的操作的请求体
    public class RCUserRequestEntity extends RequestEntity {
        public String customerId;
        public String brandName;
        public String firstName;
        public String lastName;
        public String organizationName;
        public String emailAddress;
        public String password;
        //add by Daniel for EU 2019/05/14
        public String siteOrigin;
        public String mobilePhone;
        public Boolean sendMarketingEmails;

        //add by yujie for EU 2019/6/17
        public String address1;
        public String address2;
        public String address3;
        public String postcode;
        public String country;
        public String company;
        public Boolean marketingOptIn;
        public String trade;
        public String tradeother;
        public String customerType;
    }

    //关于创建Case的操作的请求体
    public class RCCaseRequestEntity extends RequestEntity {
        public String customerId;
        public String description;
        public String attachmentUrl;
        public String phoneNumber;
        public String caseType;
        public String brandName;
    }

    //关于创建warranty的操作的请求体
    public class RCRegisterWarrantyRequestEntity extends RequestEntity {
        public String customerId;
        public String receiptStatus;
        public String receiptUrl;
        public String purchaseDate;
        public String productUseType;
        public String placeOfPurchase;
        public String masterModelNumber;
        public Boolean kit;
        public RCWarrantyItemEntity[] warrantyItems;
    }

    //********************对象封装类********************************
    //该RCOrderItemEntity与R2C网站集成所使用的
    public class RCOrderItemEntity {
        public String orderItemNumber;
        public String orderDate;
        public String productName;
        public String productModelNumber;
        public String serialNumber;
        public String status;
        public String trackingNumber;
    }

    //该RCCaseItemEntity与R2C网站集成所使用的
    public class RCCaseItemEntity {
        public String status;
        public String case_type;
        public String description;
        public String case_number;
        public String case_id;
    }

    //该RCWarrantyEntity与R2C网站集成所使用的
    public class RCWarrantyEntity {
        public String receipt;
        public String purchaseDate;
        public String productUseType;
        public String placeOfPurchase;
        public String kit;
        public String masterModelNumber;
        public String masterProductName;
        public RCWarrantyItemEntity[] warrantyItems;
    }

    //该RSWarrantyItemEntity与R2C网站集成所使用的
    public class RCWarrantyItemEntity {
        public String productType;
        public String productName;
        public String serialNumber;
        public String productModelNumber;
    }
}