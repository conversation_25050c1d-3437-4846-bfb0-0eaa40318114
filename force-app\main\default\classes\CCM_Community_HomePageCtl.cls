public without sharing class CCM_Community_HomePageCtl {
    @AuraEnabled
    public static String getInitInfo(){
        InitInfo init = new InitInfo();
        
        User currentUsr = Util.getUserInfo(UserInfo.getUserId());
        if (currentUsr != null && currentUsr.Contact != null && String.isNotBlank(currentUsr.Contact.AccountId)){
            Account acc = [
                    SELECT Id, OwnerId, ORG_Code__c
                    FROM Account 
                    WHERE Id =: currentUsr.Contact.AccountId];

            List<Purchase_Order__c> draftPOs = [SELECT Id FROM Purchase_Order__c WHERE Customer__c = :currentUsr.Contact.AccountId AND Status__c = 'Draft'];
            init.pendingOrderNumber = draftPOs.size();

            List<Accounting_Balance__c> overdueAccounts = [SELECT Id FROM Accounting_Balance__c WHERE Customer__c = :currentUsr.Contact.AccountId AND Amt_Original__c > 0 AND Amt_Due_Remaining__c > 0 AND Due_Date__c < :System.today() AND Invoice__r.Invoice_Type__c IN ('CNA_Invoice','CA_Invoice')];
            init.pendingInvoiceNumber = overdueAccounts.size();
            init.isCCA = acc.ORG_Code__c == 'CCA';
        }
        
        return JSON.serialize(init);
    }

    public class InitInfo {
        @AuraEnabled public Integer pendingOrderNumber {get; set;}
        @AuraEnabled public Integer pendingInvoiceNumber {get; set;}
        @AuraEnabled public Boolean isCCA {get;set;}

        public initInfo() {
            this.pendingOrderNumber = 0;
            this.pendingInvoiceNumber = 0;
            this.isCCA = false;
        }
    }
}