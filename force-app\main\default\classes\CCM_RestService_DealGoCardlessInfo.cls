/**
 * {
  "events": [
    {
      "id": "EV123",
      "created_at": "2014-08-03T12:00:00.000Z",
      "action": "confirmed",
      "resource_type": "payments",
      "links": {
        "payment": "PM123"
      },
      "details": {
        "origin": "gocardless",
        "cause": "payment_confirmed",
        "description": "Payment was confirmed as collected"
      }
    },
    {
      "id": "EV456",
      "created_at": "2014-08-03T12:00:00.000Z",
      "action": "failed",
      "resource_type": "payments",
      "links": {
        "payment": "PM456"
      },
      "details": {
        "origin": "bank",
        "cause": "mandate_cancelled",
        "description": "Customer cancelled the mandate at their bank branch.",
        "scheme": "bacs",
        "reason_code": "ARUDD-1"
      }
    }
  ],
  "meta": { "webhook_id": "WB123" }
}
 */
@RestResource(urlMapping='/CCM_RestService_DealGoCardlessInfo')
global without sharing class CCM_RestService_DealGoCardlessInfo {
    @HttpPost
    global static ResultObj doPost() {
        RestRequest req = RestContext.request;
        ReqestObj objRequest = new ReqestObj();
        ResultObj objResult = new ResultObj();
        try{
            objRequest = (ReqestObj)JSON.deserialize(req.requestBody.toString(), ReqestObj.class);
            System.debug(LoggingLevel.INFO, '*** req.requestBody.toString(): ' + req.requestBody.toString());
            System.debug(LoggingLevel.INFO, '*** objRequest: ' + objRequest);
            Set<String> setGcPaymentId = new Set<String>();

            for(GcEvent event: objRequest.events){
                if (event.resource_type == 'payments') {
                    setGcPaymentId.add(event.links.payment);
                }
            }

            if (setGcPaymentId.size() > 0) {
                List<Payment_Information__c> lstPaymentInfo = [
                    SELECT 
                        Id, Paypal_Status__c, Payment_Date__c, GoCardless_Payment_Id__c, GoCardless_Payment_Status__c, GoCardless_Origin__c, GoCardless_Cause__c, GoCardless_Description__c 
                    FROM 
                        Payment_Information__c 
                    WHERE 
                        GoCardless_Payment_Id__c IN :setGcPaymentId 
                ];
    
                // Map: Payment Id -> Payment Info Record
                Map<String, Payment_Information__c> mapPaymentIdToInfo = new Map<String, Payment_Information__c>();
                for (Payment_Information__c objPayment : lstPaymentInfo) {
                    mapPaymentIdToInfo.put(objPayment.GoCardless_Payment_Id__c, objPayment);
                }
    
                // 存储本次需要更新的PaymentInfo记录Id
                Set<Id> setPaymentNeedToUpdateId = new Set<Id>();
                // 存储支付成功需要推送到Oracle的PaymentInfo记录Id
                Set<Id> setSuccessPaymentInfoId = new Set<Id>();
                Set<String> setProcessingAction = new Set<String>{'created','customer_approval_granted','submitted','confirmed','chargeback_cancelled'};
                Set<String> setSuccessAction = new Set<String>{'paid_out'};
                Set<String> setFailedAction = new Set<String>{'customer_approval_denied','late_failure_settled','chargeback_settled','surcharge_fee_debited','failed','charged_back','cancelled'};
                for(GcEvent event: objRequest.events){
                    if (event.resource_type == 'payments') {
                        Payment_Information__c objPaymentInfo = mapPaymentIdToInfo.get(event.links.payment);
                        // 不要对已经失败的Payment进行操作
                        if (objPaymentInfo != null && objPaymentInfo.Paypal_Status__c != 'Payment Failed' ) {
                            objPaymentInfo.GoCardless_Payment_Status__c = statusFormat(event.action);
                            objPaymentInfo.GoCardless_Origin__c = event.details.origin;
                            objPaymentInfo.GoCardless_Cause__c = event.details.cause;
                            objPaymentInfo.GoCardless_Description__c = event.details.description;
                            if (setProcessingAction.contains(event.action)) {
                                objPaymentInfo.Paypal_Status__c = 'Processing';
                            }
                            else if (setSuccessAction.contains(event.action)) {
                                objPaymentInfo.Paypal_Status__c = 'Payment Success';
                                objPaymentInfo.Payment_Date__c = Date.today();
                                setSuccessPaymentInfoId.add(objPaymentInfo.Id);
                                if(!Test.isRunningTest()){
                                    CCM_Service.pushBeforePaymentInfo(objPaymentInfo.Id,true);
                                }
                            }else if (setFailedAction.contains(event.action)) {
                                objPaymentInfo.Paypal_Status__c = 'Payment Failed';
                                if(!Test.isRunningTest()){
                                    CCM_Service.pushBeforePaymentInfo(objPaymentInfo.Id,false);
                                }
                            }
                            setPaymentNeedToUpdateId.add(objPaymentInfo.Id);
                        }
                    }
                }
                objResult.returnCode = 'S';
                objResult.returnMsg = 'Update Payment Information: ' + JSON.serialize(setPaymentNeedToUpdateId);
                // 异步执行更新PaymentInfo、AccountBalance、推送到Oracle的相关逻辑
                CCM_DealGoCardlessWebhookQueueable objGoCardlessWebhookQueue = new CCM_DealGoCardlessWebhookQueueable(lstPaymentInfo,setPaymentNeedToUpdateId,setSuccessPaymentInfoId);
                System.enqueueJob(objGoCardlessWebhookQueue);
            }else{
                objResult.returnCode = 'S';
                objResult.returnMsg = 'There is no Payment Information update.';
                return objResult;
            }
        }catch (Exception objEx) {
            objResult.returnCode = 'F'; 
            objResult.returnMsg = 'ExceptionMessage: ' + objEx.getMessage() + '\n '
            + 'ExceptionType: ' + objEx.getTypeName() + '\n '
            + 'ExceptionLine: ' + objEx.getLineNumber() + '\n '
            + 'ExceptionStackTrace: \n ' + objEx.getStackTraceString();
            String logId = Util.logIntegration('GoCardless WebHooks Exception','CCM_RestService_DealGoCardlessInfo', 'POST', objResult.returnMsg, JSON.serialize(objRequest), JSON.serialize(objResult));
            // Util.pushExceptionEmail('Accept Go Cardless WebHook Info', logId, objResult.returnMsg);
            return objResult;
        }
        if(Label.CCM_needlog == 'Y'){
            Util.logIntegration('GoCardless WebHooks log','CCM_RestService_DealGoCardlessInfo', 'POST', '', JSON.serialize(objRequest), JSON.serialize(objResult));
        }
        return objResult;
    }

    /**
     * @description: GoCardless Status格式转换
     * 例如：pending_submission -> Pending Submission
     */
    public static String statusFormat(String strOriginVal){
        if (String.isBlank(strOriginVal)) {
            return null;
        }
        List<String> lstPart = strOriginVal.split('_');
        for (Integer i = 0; i < lstPart.size(); i++) {
            lstPart[i] = lstPart[i].capitalize();
        }
        String strTargetVal = String.join(lstPart, ' ');
        return strTargetVal;
    }

    public class ReqestObj{
        public List<GcEvent> events;

        public ReqestObj(){
            this.events = new List<GcEvent>();
        }
    }

    public class GcEvent {
        public String id;
        public String created_at;
        public String action;
        public String resource_type;
        public GcLink links;
        public GcDetail details;

        public GcEvent(){
            this.links = new GcLink();
            this.details = new GcDetail();
        }
    }

    public class GcLink{
        public String payment;
        public String mandate;
    }

    public class GcDetail{
        public String origin;
        public String cause;
        public String description;
        public String scheme;
        public String reason_code;
    }
    
    global class ResultObj {
        global String returnCode;
        global String returnMsg;
    }
}