/**
 * @description This class is used to display a dropdown list for selection.
 * @revisions Chenyang Li 2021-03-01 Enable delegation for placing Parts Order.
 */
public without sharing class CCM_AutoMatchPickListCtl {
    /*接口入参：
    String searchValue,  
    String objectType, 
    List<String> fieldlist  
    filterCondition:Sample[{"FieldName": "LastName","Condition": "=","Value": "Abby"}]*/
    @AuraEnabled
    public static String returnPicklistItemMap(String searchValue, String objectType, String fieldlist, String filterCondition, String labelField,String scopeCondtion,String orderField, String labelField1, Boolean populateWhenBlank, String isPortal, String orderType) {
        List<SelectItem> returnPickList = new List<SelectItem>();
        String searchquery = '';
        System.debug(LoggingLevel.INFO, '*** searchValue: ' + searchValue);

        if(populateWhenBlank == null) {
            populateWhenBlank = true;
        }
        if(String.isBlank(searchquery) && !populateWhenBlank) {
            return JSON.serialize(returnPickList);
        }

        // Add By John for secord tier 2020-12-08

        String accId = CCM_PortalPageUtil.getCustomerByUser(UserInfo.getUserId());
        if (String.isBlank(accId) && String.isNotBlank(filterCondition) && filterCondition.contains('Customer__c')) {
            for (Condtion objC : (List<Condtion>) JSON.deserialize(filterCondition, List<Condtion>.class)) {
                if ('Customer__c'.equals(objC.FieldName) && String.isNotBlank(objC.Value)) {
                    accId = objC.Value;
                    break;
                }
            }
        }
        System.debug('isPortal: '+isPortal);

        List<Account> accList = [SELECT Id,Distributor_or_Dealer__c, ParentId, Parent.AccountNumber, ORG_Code__c FROM account WHERE id =: accId];
        if(accList.size() > 0){
            if (accList[0].Distributor_or_Dealer__c == '2nd Tier Dealer' && objectType == 'Account_Address__c' && String.isNotEmpty(accList[0].ParentId) && accList[0].Parent.AccountNumber == Label.AceHareWareAccountNumber && filterCondition.contains('Dropship_Billing_Address')) {
                System.debug('***** ace hareware 2nd tier dealer');
                SelectItem objItem;
                for (Account_Address__c objBillTo : [SELECT Id, Name, Address1__c, City__c, Country__c, State__c
                                                        FROM Account_Address__c
                                                        WHERE Customer__c = :accList[0].ParentId
                                                        AND RecordType_Name__c = 'Dropship_Billing_Address'
                                                        AND Approval_Status__c = 'Approved'
                                                        AND Active__c = TRUE
                                                        LIMIT 250]) {
                    objItem = new SelectItem();
                    objItem.label = objBillTo.Name + ' ' + objBillTo.Address1__c + ' ' + objBillTo.State__c + ' ' + objBillTo.City__c + ' ' + objBillTo.Country__c;
                    objItem.value = objBillTo.Id;
                    System.debug('*** ' +  objItem);
                    returnPickList.add(objItem);
                }
            } else 
            if (accList[0].Distributor_or_Dealer__c == '2nd Tier Dealer' && objectType == 'Account_Address__c' && filterCondition.contains('Billing_Address')) {
                for (Account_Address__c objShipTo : [
                    SELECT Customer__c
                    FROM Account_Address__c
                    WHERE Approval_Status__c = 'Approved'
                    AND (RecordType_Name__c = 'Shipping_Address'
                    OR RecordType_Name__c = 'Dropship_Shipping_Address')
                    AND Active__c = TRUE
                    AND (x2nd_Tier_Dealer_for_Sales__c = :accId
                    OR x2nd_Tier_Dealer__c = :accId)
                    LIMIT 1
                ]) {
                    if (String.isBlank(objShipTo.Customer__c)) continue;
                    SelectItem objItem;
                    for (Account_Address__c objBillTo : [
                        SELECT Id, Name, Address1__c, City__c, Country__c, State__c,NP_Bill_To__c
                        FROM Account_Address__c
                        WHERE Customer__c = :objShipTo.Customer__c
                        AND RecordType_Name__c = 'Billing_Address'
                        AND Approval_Status__c = 'Approved'
                        AND Active__c = TRUE
                        LIMIT 250
                    ]) {
                        // sqy add
                        if((isPortal == 'true' || orderType == 'parts') && objBillTo.NP_Bill_To__c == true && accList[0].ORG_Code__c == 'CNA'){
                            System.debug('1111111:'+objBillTo.NP_Bill_To__c);
                            continue;
                        }
                        objItem = new SelectItem();
                        objItem.label = objBillTo.Name + ' ' + objBillTo.Address1__c + ' ' + objBillTo.State__c + ' ' + objBillTo.City__c + ' ' + objBillTo.Country__c;
                        objItem.value = objBillTo.Id;
                        returnPickList.add(objItem);
                    }
                }
            } else if (accList[0].Distributor_or_Dealer__c == '2nd Tier Dealer' && objectType == 'Account_Address__c' && filterCondition.contains('Shipping_Address')) {
            // } else if (accList[0].Distributor_or_Dealer__c == '2nd Tier Dealer' && objectType == 'Account_Address__c' && filterCondition.contains('Dropship_Shipping_Address')) {
                SelectItem objItem;
                for (Account_Address__c objShipTo : [
                    SELECT Id,Name,Address1__c,City__c,Country__c,State__c
                    FROM Account_Address__c
                    WHERE Approval_Status__c = 'Approved'
                    AND (RecordType_Name__c = 'Shipping_Address'
                    OR RecordType_Name__c = 'Dropship_Shipping_Address')
                    AND (x2nd_Tier_Dealer_for_Sales__c = :accId
                    OR x2nd_Tier_Dealer__c = :accId)
                    AND Active__c = TRUE
                    LIMIT 250
                ]) {
                    objItem = new SelectItem();
                    objItem.label = objShipTo.Name + ' ' + objShipTo.Address1__c + ' ' + objShipTo.State__c + ' ' + objShipTo.City__c + ' ' + objShipTo.Country__c;
                    objItem.value = objShipTo.Id;
                    returnPickList.add(objItem);
                }
            }
        }

        // Add By John for secord tier 2020-12-08

        // Search query by specific
        if (String.isNotBlank(searchValue)) {
            searchquery='FIND {1} IN ALL FIELDS RETURNING {2} ({3}{4})';
            // Used for fixing deployment issue
            if (searchValue.length() == 1) {
                searchValue = 'Test';
            }
            searchValue = '\'*' + searchValue + '*\'';
            searchquery = searchquery.replace('{1}', searchValue);
            searchquery = searchquery.replace('{2}', objectType);
            searchquery = searchquery.replace('{3}', fieldlist);
            searchquery = searchquery.replace('{4}', generateCondition(filterCondition,scopeCondtion));

            System.debug(LoggingLevel.INFO, '*** searchquery: ' + searchquery);
            List<List<SObject>>searchList=search.query(searchquery);
            if (searchList.size() > 0) {
                List<SObject> oblist = searchList[0];
                for (SObject sobj : oblist) {
                    // sqy add
                    if((isPortal == 'true' || orderType == 'parts') && sobj.get('NP_Bill_To__c') == true && accList[0].ORG_Code__c == 'CNA'){
                        continue;
                    }
                    SelectItem pitem = new SelectItem();
                    if (String.isNotBlank(labelField1)){
                        List<String> vals = labelField1.split(';');
                        if (vals.size() > 1) {
                            pitem.label = '<p>'+(String)sobj.get(labelField)+'</span><br/>';
                            for (String val : vals) {
                                pitem.label += '<span style="font-size:12px;padding-right:5px;">'+(String)sobj.get(val)+'</span>';
                            }
                            pitem.label += '</p>';
                        } else {
                            String labelFieldVal = (String)sobj.get(labelField1);
                            //Start:Added by Zoe on 2024-8-26 for french portal 
                            String labelFieldVal2 = (String)sobj.get(labelField);                           
                            if(objectType == 'product2' && labelField == 'Product_Name_French__c') {
                                labelFieldVal2 = (String)sobj.get(labelField) == null? (String)sobj.get('Name'):(String)sobj.get(labelField) ;
                            }         
                            // pitem.label = '<p><span>' + (String)sobj.get(labelField1) + '</span><br />' + (String)sobj.get(labelField) + '</p>';             
                            pitem.label = '<p><span>' + (String)sobj.get(labelField1) + '</span><br />' + labelFieldVal2 + '</p>';
                            //End:Added by Zoe on 2024-8-26 for french portal 
                        }
                    }else{
                        pitem.label = (String)sobj.get(labelField);
                    }
                    if(objectType == 'ZIP_Tiers__c') {
                        pitem.value = (String)sobj.get(labelField);
                    }
                    else {
                        pitem.value = (String)sobj.get('Id');
                    }
                    if (String.isNotBlank(pitem.label)) {
                        returnPickList.add(pitem);
                    }
                }
            }
        }

        // Setting Defalult select result
        if (String.isBlank(searchValue)) {
            searchquery='SELECT {1} FROM {2} {3}';
            searchquery = searchquery.replace('{1}', fieldlist);
            searchquery = searchquery.replace('{2}', objectType);
            searchquery = searchquery.replace('{3}', generateCondition(filterCondition,scopeCondtion));
            if (String.isNotBlank(orderField)){
                searchquery = searchquery + ' ORDER BY {5} ASC ';
                searchquery = searchquery.replace('{5}', orderField);
            }

            Integer lmt = 250;
            if (objectType == 'User') {
                lmt = 20;
            }
            searchquery =  searchquery + ' limit ' + lmt;
            SelectItem testpitem = new SelectItem();
            SelectItem testpitem1 = new SelectItem();
            SelectItem testpitem2 = new SelectItem();
            System.debug(LoggingLevel.INFO, '*** searchquery: ' + searchquery);
            List<SObject> sobjectlist = Database.query(searchquery);
            if (sobjectlist.size() > 0) {
                for (SObject sobj : sobjectlist) {
                    // sqy add
                    if((isPortal == 'true' || orderType == 'parts') && sobj.get('NP_Bill_To__c') == true && accList[0].ORG_Code__c == 'CNA'){
                        continue;
                    }
                    System.debug('isPortal: '+isPortal+'   NP_Bill: '+sobj.get('NP_Bill_To__c'));

                    SelectItem pitem = new SelectItem();
                    System.debug(LoggingLevel.INFO, '*** labelField1 Test: ' + labelField1);
                    if (String.isNotBlank(labelField1)){
                        List<String> vals = labelField1.split(';');
                        if (vals.size() > 1) {
                            pitem.label = '<p>'+(String)sobj.get(labelField)+'</span><br/>';
                            for (String val : vals) {
                                pitem.label += '<span style="font-size:12px; padding-right:5px;">'+(String)sobj.get(val)+'</span>';
                            }
                            pitem.label += '</p>';
                        } else {
                            String labelFieldVal = (String)sobj.get(labelField1);
                            //Start:Added by Zoe on 2024-8-26 for french portal 
                            String labelFieldVal2 = (String)sobj.get(labelField);                           
                            if(objectType == 'product2' && labelField == 'Product_Name_French__c') {
                                labelFieldVal2 = (String)sobj.get(labelField) == null? (String)sobj.get('Name'):(String)sobj.get(labelField) ;
                            }         
                            // pitem.label = '<p><span>' + (String)sobj.get(labelField1) + '</span><br />' + (String)sobj.get(labelField) + '</p>';             
                            pitem.label = '<p><span>' + (String)sobj.get(labelField1) + '</span><br />' + labelFieldVal2 + '</p>';
                            //End:Added by Zoe on 2024-8-26 for french portal 
                        }
                    }else{
                        pitem.label = (String)sobj.get(labelField);
                    }
                    System.debug(LoggingLevel.INFO, '*** pitem.label: ' + pitem.label);
                    if(objectType == 'ZIP_Tiers__c') {
                        pitem.value = (String)sobj.get(labelField);
                    }
                    else {
                        pitem.value = (String)sobj.get('Id');
                    }
                    if (String.isNotBlank(pitem.label)) {
                        returnPickList.add(pitem);
                    }
                }
                System.debug(LoggingLevel.INFO, '*** returnPickList: ' + returnPickList);
            }
        }
        return JSON.serialize(returnPickList);
    }

    public static String generateCondition(String filterCondition, String scopeCondtion) {
        System.debug(LoggingLevel.INFO, '*** filterCondition: ' + filterCondition);
        System.debug(LoggingLevel.INFO, '*** scopeCondtion: ' + scopeCondtion);
        if (String.isNotBlank(filterCondition)) {
            List<Condtion> condtions = (List<Condtion>)JSON.deserialize(filterCondition, List<Condtion>.class);

            if (String.isNotBlank(scopeCondtion)) {
                List<Condtion> condtions2 = (List<Condtion>)JSON.deserialize(scopeCondtion, List<Condtion>.class);
                condtions.addAll(condtions2);
            }
            String returnFitler = ' WHERE ';
            Integer i=1;
            for (Condtion con : condtions) {
                System.debug(LoggingLevel.INFO, '*** 1027 con: ' + con);
                if (i>1) {
                    returnFitler += ' And';
                }
                if (con.Condtion != 'IN' && con.Condtion != 'NOT IN' && !con.Value.equalsIgnoreCase('true') && !con.Value.equalsIgnoreCase('false')) {
                    returnFitler += (' ' + con.FieldName +' ' + con.Condtion + ' \''+ con.Value +'\'' + ' ');
                }else if(con.Value.equalsIgnoreCase('true') || con.Value.equalsIgnoreCase('false')){
                    returnFitler += (' ' + con.FieldName +' ' + con.Condtion + Boolean.valueOf(con.Value) + ' ');
                }else {
                    returnFitler += (' ' + con.FieldName +' ' + con.Condtion + ' '+ con.Value.replace('"',  '\'') +' ' + ' ');
                }

                i++;
            }
            System.debug(LoggingLevel.INFO, '*** returnFitler String: ' + returnFitler);
            return returnFitler;
        }

        return '';
    }

    public class Condtion {
        public String FieldName {get; set;}
        public String Condtion {get; set;}
        public String Value {get;set;}
    }

    public class SelectItem {
        @AuraEnabled public String label;
        @AuraEnabled public String value;
        @AuraEnabled public String descVal;
    }
}