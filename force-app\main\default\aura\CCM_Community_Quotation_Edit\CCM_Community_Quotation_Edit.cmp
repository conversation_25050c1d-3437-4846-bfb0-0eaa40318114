<aura:component controller="CCM_Quotation_DetailCtl" description="Quotation Edit page"
    implements="forceCommunity:availableForAllPageTypes,flexipage:availableForAllPageTypes,force:hasRecordId" access="global">
    <aura:attribute name="brandScope" type="String" default=""/>
    <aura:attribute name="shipDateScope" type="Date" default=""/>
    <aura:attribute name="brandScopeOpt" type="List" default="" access="public"/>
    <aura:attribute name="distributorScope" type="String" default=""/>
    <aura:attribute name="distributorScopeOpt" type="List" default="" access="public"/>
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="customerId" type="String" default=""/>
    <aura:attribute name="stepNameList" type="List" default="[]"/>
    <aura:attribute name="currentStep" type="Integer" default="1"/>
    <aura:attribute name="po" type="Object" default=""/>
    <aura:attribute name="orderItemList" type="List" default="[]"/>
    <aura:attribute name="columns" type="List" default="[]"/>
    <aura:attribute name="billingAddress" type="String" default=""/>
    <aura:attribute name="billingAddressInput" type="String" default=""/>
    <aura:attribute name="shippingAddress" type="String" default=""/>
    <aura:attribute name="shippingAddressInput" type="String" default=""/>
    <aura:attribute name="shippingMethod" type="String" default=""/>
    <aura:attribute name="logisticsSupplier" type="String" default=""/>
    <aura:attribute name="showEditModal" type="Boolean" default="false" />
    <aura:attribute name="modalContent" type="String" />
    <aura:attribute name="paymentTerm" type="String" default="" />
    <aura:attribute name="paymentTermLabel" type="String" default="" />
    <aura:attribute name="freightTerm" type="String" default="" />
    <aura:attribute name="freightTermLabel" type="String" default="" />
    <aura:attribute name="freightTermRuleFee" type="Currency" default="0" />
    <aura:attribute name="isShowTerm" type="Boolean" default="false"/>
    <aura:attribute name="oldBrandName" type="String" default=""/>
    <aura:attribute name="isShowFreightFeeMsg" type="Boolean" default="false"/>
    <aura:attribute name="showUpload" type="Boolean" default="false"/>
    <!-- sqy add -->
    <aura:attribute name="isPortal" type="String" default="true"/>
    <!--Add by Abby on 06012020 for research payment term-->
    <aura:attribute name="paymentTermAllOpts" type="List" default="[]"/>
    <aura:attribute name="paymentTermSelectOpt" type="List" default="[]"/>
    <aura:attribute name="paymentTermValue" type="String" default=""/>
    <aura:attribute name="needResearch" type="Boolean" default="false"/>
    <aura:attribute name="customerType" type="String" default=""/>
    <aura:attribute name="customerCluster" type="String" default=""/>
    <aura:method name="validatePaymentTerm" action="{!c.validatePaymentTerm}" access="public" />
    <aura:method name="setShipDate" action="{!c.setShipDate}" access="public" />

    <aura:attribute name="minSelectDate" type="String" />

    <!--Add by Eric on 03102021 for Whole Order and payment term Promotion-->
    <aura:attribute name="wholeOrderPromo" type="Object" default=""/>
    <aura:attribute name="termsPromo" type="Object" default=""/>

    <!--Customer Org Code-->
    <aura:attribute name="customerOrgCode" type="String" default=""/>

    <!--Add by Eric on 03072022 for Surcharge Rate-->
    <aura:attribute name="surcharge" type="Decimal" default="0"/>

    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <aura:registerEvent name="productSelected" type="c:CCM_SelectProductListEvt"/>
    <aura:handler name="change" value="{!v.brandScope}" action="{!c.brandChange}" />
    <aura:handler name="change" value="{!v.distributorScope}" action="{!c.distributorChange}"/>
    <ltng:require scripts="{!join(',', $Resource.Validator)}"/>


    <div class="slds-grid slds-grid_align-space">
        <div class="slds-grid slds-grid--align-center slds-size--1-of-1">
            <div class="slds-col slds-size_1-of-1 slds-wrap">
                <c:CCM_ProcessStep currentStep="{!v.currentStep}" stepName="{!v.stepNameList}" />
                <div class="slds-grid slds-grid_vertical-align-end slds-grid--align-spread">
                    <div class="slds-clearfix">
                        <div class="slds-col slds-list_horizontal slds-wrap">
                            <aura:if isTrue="{!v.currentStep == 1}">
                                <lightning:combobox aura:id="brandScope" name="brandScope" placeholder="{!$Label.c.CCM_Portal_SelectBrand}" value="{!v.brandScope}" options="{!v.brandScopeOpt}" label="{!$Label.c.CCM_Portal_AuthorizedBrands}" />
                            </aura:if>
                        </div>

                    </div>
                    <div class="slds-clearfix">
                        <div class="slds-col slds-list_horizontal slds-wrap ccm_paddingLeft">
                            <aura:if isTrue="{!v.currentStep == 1}">
                                <aura:if isTrue="{!v.customerOrgCode == 'CCA'}">
                                    <lightning:input aura:id="expectedShipDate" name="shipDateScope" label="{!$Label.c.CCM_Portal_ScheduleShipDate}" value="{!v.shipDateScope}" type="Date" min="{!v.minSelectDate}" required="true"/>
                                </aura:if>
                                <!-- <aura:if isTrue="{!OR(v.customerOrgCode == 'CNA',v.customerOrgCode == '')}">
                                    <lightning:input aura:id="expectedShipDate" name="shipDateScope" label="{!$Label.c.CCM_Portal_ScheduleShipDate}" value="{!v.shipDateScope}" type="Date" disabled ="true"/>
                                </aura:if> -->
                            </aura:if>
                        </div>
                    </div>

                    <div class="slds-clearfix">
                        <div class="slds-col slds-list_horizontal slds-wrap slds-float_right">
                            <aura:if isTrue="{!and(v.currentStep == 1, v.isShowTerm == true,!empty(v.brandScope))}">
                                <aura:if isTrue="{!!v.needResearch}">
                                    <span class="ccm_fontColor">{!$Label.c.CCM_Portal_PaymentTerm}: {!v.paymentTermLabel}<br/>{!$Label.c.CCM_Portal_FreightTerm}: {!v.freightTermLabel}</span>
                                    <aura:set attribute="else">
                                        <span class="ccm_fontColor">
                                            {!$Label.c.CCM_Portal_PaymentTerm}:
                                            <lightning:combobox
                                                class="paymentTermSelectList"
                                                aura:id="required-Field"
                                                name="paymentTermInfo"
                                                placeholder="{!$Label.c.CCM_Portal_SelectPaymentTerm}"
                                                value="{!v.paymentTermValue}"
                                                options="{!v.paymentTermSelectOpt}"
                                                label=""
                                                title="{!v.paymentTermLabel}"
                                                variant="label-hidden"
                                                required="true"
                                            />
                                            <br/>
                                            {!$Label.c.CCM_Portal_FreightTerm}: {!v.freightTermLabel}
                                        </span>
                                    </aura:set>
                                </aura:if>
                            </aura:if>
                        </div>
                    </div>
                </div>
                <div class="slds-path  slds-p-top_small slds-p-vertical_medium">
                    <aura:if isTrue="{!v.currentStep == 1}">
                        <!-- <c:CCM_Quotation_ProductSelect recordId="{!v.recordId}" quotation="{!v.po}" orderItemList="{!v.orderItemList}" currentStep="{!v.currentStep}" brand="{!v.brandScope}" brandScopeOpt="{!v.brandScopeOpt}" customerId="{!v.customerId}" freightTermRuleFee="{!v.freightTermRuleFee}" showFreeShippingMsg="{!v.isShowFreightFeeMsg}" paymentTermAllOpts="{!v.paymentTermAllOpts}" paymentTermSelectOpt="{!v.paymentTermSelectOpt}" paymentTermValue="{!v.paymentTermValue}" needResearch="{!v.needResearch}" customerType="{!v.customerType}" customerCluster="{!v.customerCluster}" defaultPaymentTerm="{!v.paymentTerm}" wholeOrderPromo="{!v.wholeOrderPromo}" termsPromo="{!v.termsPromo}" paymentTermLabel="{!v.paymentTermLabel}" parent="{!this}" customerOrgCode="{!v.customerOrgCode}" surcharge="{!v.surcharge}"/> -->
                        <c:CCM_Quotation_ProductSelect recordId="{!v.recordId}" quotation="{!v.po}" orderItemList="{!v.orderItemList}" currentStep="{!v.currentStep}" brand="{!v.brandScope}" brandScopeOpt="{!v.brandScopeOpt}" customerId="{!v.customerId}" freightTermRuleFee="{!v.freightTermRuleFee}" showFreeShippingMsg="{!v.isShowFreightFeeMsg}" paymentTermAllOpts="{!v.paymentTermAllOpts}" paymentTermSelectOpt="{!v.paymentTermSelectOpt}" paymentTermValue="{!v.paymentTermValue}" needResearch="{!v.needResearch}" customerType="{!v.customerType}" customerCluster="{!v.customerCluster}" defaultPaymentTerm="{!v.paymentTerm}" wholeOrderPromo="{!v.wholeOrderPromo}" termsPromo="{!v.termsPromo}" paymentTermLabel="{!v.paymentTermLabel}" customerOrgCode="{!v.customerOrgCode}" surcharge="{!v.surcharge}" shipDate = "{!v.shipDateScope}" minSelectDate = "{!v.minSelectDate}" parent ="{!this}" isPortal="{!v.isPortal}" showUpload ="{!v.showUpload}"/>
                    </aura:if>

                    <aura:if isTrue="{!v.currentStep == 2}">
                        <c:CCM_Quotation_FillAddress_Cmp recordId="{!v.recordId}" quotation="{!v.po}" customerId="{!v.customerId}" currentStep="{!v.currentStep}" freightTermRuleFee="{!v.freightTermRuleFee}" brandScope="{!v.brandScope}" freightTerm="{!v.freightTermLabel}" isPortal="{!v.isPortal}" paymentTermValue="{!v.paymentTermValue}"/>
                    </aura:if>

                    <aura:if isTrue="{!v.currentStep == 3}">
                        <c:CCM_Quotation_Detail recordId="{!v.recordId}" currentStep="{!v.currentStep}" />
                    </aura:if>
                </div>
            </div>
        </div>
    </div>

    <aura:if isTrue="{!v.showEditModal}">
    <div style="" >
        <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container" style="height:auto !important; transform: translate(0%, 50%);">
                <div class="modal-header slds-modal__header">
                    <button class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse" title="{!$Label.c.CCM_Portal_Close}" onclick="{!c.closeModal}">
                        <lightning:icon iconName="utility:close" alternativeText="{!$Label.c.CCM_Portal_Close}" variant="close" class = "modal_close"/>
                        <span class="slds-assistive-text">{!$Label.c.CCM_Portal_Close}</span>
                    </button>
                    <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">{!$Label.c.CCM_Portal_ConfirmAuthorizedBrandChange}</h2>
                </div>
                <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                    <p style="padding: 10px;">
                        {!v.modalContent}
                    </p>
                </div>
                <footer class="slds-modal__footer">
                    <button class="slds-button slds-button_neutral" onclick="{!c.closeModal}">{!$Label.c.CCM_Portal_Cancel}</button>
                    <button class="slds-button slds-button_brand" onclick="{!c.confirmChange}">{!$Label.c.CCM_Portal_Confirm}</button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </div>
    </aura:if>
</aura:component>