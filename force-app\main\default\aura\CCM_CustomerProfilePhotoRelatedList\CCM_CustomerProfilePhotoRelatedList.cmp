<aura:component
    implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction"
    access="global" controller="CCM_CustomerProfilePhotoController">
    <aura:attribute name="relatedRecords" type="List" />
    <aura:attribute name="allDatas" type="List"/>
    <aura:handler name="init" value="{!this}" action="{!c.onInit}"/>
    <aura:attribute name="selectedId" type="String" />
    <aura:attribute name="category" type="String" />
    <aura:attribute name="columns" type="List" />
    <aura:attribute name="showUploadModal" type="Boolean" default="false" />
    <aura:attribute name="showChangeCategoryModal" type="Boolean" default="false" />
    <aura:attribute name="showUpload" type="Boolean" default="false" />
    <aura:attribute name="photoCategoryOptions" type="List" default="[]" />
    <aura:attribute name="photoCategoryChanged" type="String" />
    <aura:attribute name="nameList" type="List" />

    <!-- add haibo : 2024/9/16 -->
    <aura:attribute name="categoryType" type="String" default=""/>
    <aura:attribute name="relatedStore" type="String" default=""/>
    <aura:attribute name="relatedStoreObj" type="Map" default="{Name: '', Id: ''}"/>
    <aura:attribute name="createdDateFrom" type="String" default=""/>
    <aura:attribute name="createdDateTo" type="String" default=""/>
    <aura:attribute name="createdBy" type="String" default=""/>
    <aura:attribute name="createdByObj" type="Map" default="{Name: '', Id: ''}"/>
    <aura:attribute name="isAscending" type="String" default="0"/>
    <aura:attribute name="photoCategoryOptionsByChange" type="List" default="[]" />

    
    <aura:registerEvent name="showToast" type="force:showToast" />
    <aura:handler name="closeModal" event="c:CCM_AddressWithBrandSaved" action="{!c.handleComponentEvent}" />

    <div class="container forceRelatedListSingleContainer" data-aura-class="forceRelatedListSingleContainer">
        <article class="slds-card slds-card_boundary related_list_card_border_top forceRelatedListCardDesktop"
            data-aura-class="forceRelatedListCardDesktop">
            <div class="related_list_themed_border_top"></div>
            <lightning:card iconName="custom:custom51" class="related_List">
                <aura:set attribute="actions">
                    <lightning:button label="New" onclick="{!c.uploadFile}" />
                    <lightning:button label="Change Category" onclick="{!c.handleChangeCategory}" />
                    <lightning:button label="Download" onclick="{!c.downloadSelectedFiles}" />
                </aura:set>
                <aura:set attribute="title">
                    <span
                        class="slds-card__header-title slds-truncate slds-m-right--xx-small slds-card__header-link">Photos</span>
                </aura:set>
                <lightning:layout multipleRows="true" horizontalAlign="space">
                    <lightning:layoutItem size="5" class="slds-p-top_small">
                        <lightning:combobox class="ccm_display"
                            name="photoCategory" 
                            value="{!v.categoryType}" 
                            options="{!v.photoCategoryOptions}" 
                            label="Photo Category"
                        />
                    </lightning:layoutItem>
                    <lightning:layoutItem size="5" class="slds-p-top_small">
                        <c:CCM_Community_LookUp aura:id="relatedStore"
                            fieldName="Related Store"
                            customerId="{!v.recordId}"
                            selectedValue="{!v.relatedStoreObj}"
                        />
                    </lightning:layoutItem>
                    <lightning:layoutItem size="5" class="slds-p-top_small">
                        <lightning:layout>
                            <lightning:layoutItem size="5">
                                <lightning:input type="date" class="date-box-item" name="submitFrom"  label="Created Date From" value="{!v.createdDateFrom}"/>
                            </lightning:layoutItem>
                            <lightning:layoutItem size="2" class="date-box-b">
                                <div>——</div>
                            </lightning:layoutItem>
                        <lightning:layoutItem size="5">
                            <lightning:input type="date" class="date-box-item slds-p-top--xx-small" name="Created Date To" value="{!v.createdDateTo}"/>
                            </lightning:layoutItem>
                        </lightning:layout>
                    </lightning:layoutItem>
                    <lightning:layoutItem size="5" class="slds-p-top_small">
                        <c:CCM_Community_LookUp aura:id="createdBy"
                            fieldName="Created By"
                            customerId="{!v.recordId}"
                            selectedValue="{!v.createdByObj}"
                        />
                    </lightning:layoutItem>
                </lightning:layout>
                <div class="slds-m-top--large slds-m-bottom--large">
                    <lightning:layout horizontalAlign="center">
                        <lightning:layoutItem >
                            <lightning:button variant="brand" label="Search" onclick="{! c.handleSearch }"/>
                            <lightning:button  variant="brand" label="Reset" onclick="{! c.doReset}"/>
                        </lightning:layoutItem>
                    </lightning:layout>
                </div>
                <div class="slds-m-around_small">
                    <!-- <div>
                        <c:ccmAutocomplete aura:id="autoComplete" label="" title-field="name" content-field="name" data-type="single" realtime-search="false" source="{!v.nameList}" display-text="name" onselected="{!c.handleNameChange}" onlocalsearchinputchange="{!c.handleSearchInputChange}"></c:ccmAutocomplete>
                    </div> -->
                    <table class="slds-table slds-table_cell-buffer slds-table_bordered">
                        <thead>
                            <tr class="slds-line-height_reset">
                                <th class="slds-text-title_caps" scope="col" style="width: 10%;">
                                    <lightning:input type="checkbox" variant="label-hidden"
                                        onchange="{!c.handleSelectAll}"></lightning:input>
                                </th>
                                <th class="slds-text-title_caps" scope="col" style="width: 15%;">
                                    <div class="slds-truncate" title="Photo Category">Photo Category</div>
                                </th>
                                <th class="slds-text-title_caps" scope="col" style="width: 15%;">
                                    <div class="slds-truncate" title="Related">Related</div>
                                </th>
                                <th class="slds-text-title_caps" scope="col" style="width: 15%;">
                                    <div class="slds-truncate sort-wrap" title="Related" onclick="{! c.tableDataSort }">Related Store</div>
                                </th>
                                <th class="slds-text-title_caps" scope="col" style="width:15%;">
                                    <div class="slds-truncate" title="Photo Link">Photos</div>
                                </th>
                                <th class="slds-text-title_caps" scope="col" style="width:15%;">
                                    <div class="slds-truncate" title="Created Date">Created Date</div>
                                </th>
                                <th class="slds-text-title_caps" scope="col" style="width:15%;">
                                    <div class="slds-truncate" title="Created By">Created By</div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <aura:iteration items="{!v.relatedRecords}" var="r">
                                <tr>
                                    <th scope="row">
                                        <div class="slds-truncate">
                                            <lightning:input type="checkbox" name="{!r.Id}" variant="label-hidden"
                                                checked="{!r.checked}" onchange="{!c.handleSelect}"></lightning:input>
                                        </div>
                                    </th>
                                    <th scope="row">
                                        <div class="slds-truncate">
                                            {!r.photoCategory}
                                        </div>
                                    </th>
                                    <th scope="row">
                                        <a href="{!'/'+r.RelatedID}" class="slds-truncate" target="_blank">{!r.RelatedName}</a> 
                                    </th>
                                    <th scope="row">
                                        <a href="{!'/'+r.RelatedStoreId}" class="slds-truncate" target="_blank">{!r.RelatedStoreName}</a>
                                    </th>
                                    <th scope="row">
                                        <div class="slds-truncate">
                                            <!-- <a onclick="{!c.preview}" data-id="{!r.docId}">{!r.photoName}</a> -->
                                            <a onclick="{!c.preview}" data-id="{!r.docId}"><img src="{!r.photoUrl}"
                                                    style="width: 45px; height:45px;"></img></a>
                                        </div>
                                    </th>
                                    <th scope="row">
                                        <div class="slds-truncate">
                                            {!r.CreatedDate}
                                        </div>
                                    </th>
                                    <th scope="row">
                                        <div class="slds-truncate">
                                            {!r.CreatedBy}
                                        </div>
                                    </th>
                                    <th scope="row">
                                        <div class="slds-truncate" style="overflow:initial;" data-id="{!r.Id}"
                                            onclick="{!c.selectRow}">
                                            <lightning:buttonMenu alternativeText="Show menu" variant="border-filled"
                                                onselect="{! c.handleMenuSelect }">
                                                <lightning:menuItem value="delete" label="Delete" />
                                            </lightning:buttonMenu>
                                        </div>
                                    </th>
                                </tr>
                            </aura:iteration>
                        </tbody>
                    </table>
                </div>
            </lightning:card>
        </article>
    </div>

    <aura:if isTrue="{!v.showUploadModal}">
        <div style="">
            <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true"
                aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                <div class="slds-modal__container" style="height:auto !important; transform: translate(0%, 50%);">
                    <div class="modal-header slds-modal__header">
                        <button
                            class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse"
                            title="Close" onclick="{!c.closeModal}">
                            <lightning:icon iconName="utility:close" alternativeText="close!" variant="close"
                                class="modal_close" />
                            <span class="slds-assistive-text">Close</span>
                        </button>
                        <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">Upload photos</h2>
                    </div>
                    <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                        <div class="slds-grid" style="flex-wrap: wrap">
                            <div class="slds-size_2-of-4">
                                {!v.body}
                            </div>
                            <div class="slds-size_4-of-4">
                                <lightning:fileUpload label="Upload Your Picture" name="fileUploader" multiple="false"
                                    accept=".pdf, .png, .jpg, .jpeg" recordId="{!v.recordId}"
                                    onuploadfinished="{!c.handleUploadFinished}" disabled="{!!v.showUpload}" />
                            </div>
                        </div>

                    </div>
                    <footer class="slds-modal__footer">
                        <button class="slds-button slds-button_neutral" onclick="{!c.closeModal}">Cancel</button>
                    </footer>
                </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
        </div>
    </aura:if>

    <aura:if isTrue="{!v.showChangeCategoryModal}">
        <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true"
            aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container" style="height:auto !important; transform: translate(0%, 50%);">
                <div class="modal-header slds-modal__header">
                    <button
                        class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse"
                        title="Close" onclick="{!c.closeModal}">
                        <lightning:icon iconName="utility:close" alternativeText="close!" variant="close"
                            class="modal_close" />
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">Change Category</h2>
                </div>
                <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                    <div class="slds-grid" style="flex-wrap: wrap">
                        <div class="slds-size_2-of-4">
                            <lightning:combobox label="Select Photo Category" variant="label-stacked" value=""
                                options="{!v.photoCategoryOptionsByChange}" onchange="{!c.handleCategoryChange}">
                            </lightning:combobox>
                        </div>
                    </div>

                </div>
                <footer class="slds-modal__footer">
                    <button class="slds-button slds-button_neutral" onclick="{!c.confirmModal}">Confirm</button>
                    <button class="slds-button slds-button_neutral" onclick="{!c.closeModal}">Cancel</button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </aura:if>

</aura:component>