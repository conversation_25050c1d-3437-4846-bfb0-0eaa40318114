<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>false</allowRecall>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Field_Service</submitter>
        <type>role</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>NA_Administrator</submitter>
        <type>role</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Sales_Operation</submitter>
        <type>role</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter><EMAIL></submitter>
        <type>user</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Owner</field>
        <field>Customer__c</field>
        <field>Address1__c</field>
        <field>Address2__c</field>
        <field>City__c</field>
        <field>State__c</field>
        <field>Country__c</field>
        <field>Postal_Code__c</field>
        <field>Contact__c</field>
        <field>Contact2__c</field>
        <field>Contact3__c</field>
        <field>Contact4__c</field>
        <field>Contact5__c</field>
        <field>x2nd_Tier_Dealer_for_Sales__c</field>
        <field>X2nd_Tier_Dealer__c</field>
        <field>Approval_Submit_Time__c</field>
        <field>CreatedBy</field>
        <field>ORG_ID__c</field>
        <field>RecordType</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Sales_Director__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Account_Address__c.ORG_ID__c</field>
                <operation>equals</operation>
                <value>CCA</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>ApproveRecord</ifCriteriaNotMet>
        <label>TM submits for approval</label>
        <name>TM_submits_for_approval</name>
    </approvalStep>
    <emailTemplate>Sales_Cloud/Address_Send_Approval</emailTemplate>
    <enableMobileDeviceAccess>true</enableMobileDeviceAccess>
    <entryCriteria>
        <booleanFilter>(1 OR 3 ) AND 2 AND 4</booleanFilter>
        <criteriaItems>
            <field>Account_Address__c.RecordType</field>
            <operation>equals</operation>
            <value>Shipping Address</value>
        </criteriaItems>
        <criteriaItems>
            <field>Account_Address__c.Is_2nd_Tier_Dealer__c</field>
            <operation>equals</operation>
            <value>True</value>
        </criteriaItems>
        <criteriaItems>
            <field>Account_Address__c.RecordType</field>
            <operation>equals</operation>
            <value>Dropship Shipping Address</value>
        </criteriaItems>
        <criteriaItems>
            <field>Account_Address__c.ORG_ID__c</field>
            <operation>notEqual</operation>
            <value>CCA</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Address_Update_Approval_Status_Approved</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Address_Update_Approved_Date</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>false</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Address_Rejected_Email</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Address_Update_Approval_Status_Rejected</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Address_Update_Approval_Status_Pending</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Address_Update_Submit_Time</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>2nd Tier Shipping Address Approval</label>
    <nextAutomatedApprover>
        <useApproverFieldOfRecordOwner>false</useApproverFieldOfRecordOwner>
        <userHierarchyField>Manager</userHierarchyField>
    </nextAutomatedApprover>
    <processOrder>4</processOrder>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
