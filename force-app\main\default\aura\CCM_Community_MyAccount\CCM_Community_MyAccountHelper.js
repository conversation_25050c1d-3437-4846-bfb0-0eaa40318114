({
    getObjectRecords: function (component, event, helper) {
        component.set("v.isBusy", true);
        var action = component.get("c.getInvoices");
        action.setParams({
            pageNumber: component.get("v.pageNumber"),
            pageSize: component.get("v.pageCount")
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            // console.log("state--->" + state);
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if (results) {
                    component.set("v.allData", results.allBalanceDataList);
                    component.set(
                        "v.currentData",
                        results.currentBalanceDataList
                    );
                    component.set(
                        "v.totalRecords",
                        results.allBalanceDataList.length
                    );

                    // component.set('v.allCreditData', results.allCreditMemoList);
                    // component.set('v.currentCreditData', results.currentCreditMemoList);
                    // component.set('v.totalRecords2', results.allCreditMemoList.length);

                    component.set(
                        "v.accountBalanceAmount",
                        results.accountBalanceAmount
                    );
                    component.set(
                        "v.dueTotalAmount",
                        results.dueRemainingTotalAmount
                    );
                    component.set(
                        "v.creditMemoTotalAmount",
                        results.remainingTotalAmount
                    );
                    component.set(
                        "v.lastModifiedDate",
                        results.lastModifiedDate
                    );
                    component.set(
                        "v.pastDueBalanceAmount",
                        results.totalPastDueAmount
                    );
                } else {
                    var errors = response.getError();
                    if (errors) {
                        if (errors[0] && errors[0].message) {
                            alert($A.get("$Label.c.CCM_Portal_Error") + ': ' + errors[0].message);
                        }
                    } else {
                        alert($A.get("$Label.c.CCM_Portal_ErrorTips"));
                    }
                }
                component.set("v.isBusy", false);
            }
        });
        $A.enqueueAction(action);
    },
    getInvoiceRecords: function (component, event, helper) {
        component.set("v.isBusy", true);
        var action = component.get("c.searchInvoice");
        action.setParams({
            pageNumber: component.get("v.pageNumber"),
            pageSize: component.get("v.pageCount"),
            isOverdueStr: component.get("v.overdueStr"),
            invoiceRangeStr: component.get("v.invoiceDateRange")
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            // console.log("state--->" + state);
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if (results) {
                    component.set("v.allData", results.allBalanceDataList);
                    component.set(
                        "v.currentData",
                        results.currentBalanceDataList
                    );
                    component.set(
                        "v.totalRecords",
                        results.allBalanceDataList.length
                    );
                } else {
                    var errors = response.getError();
                }
                component.set("v.isBusy", false);
            }
        });
        $A.enqueueAction(action);
    },
    getCreditMemoRecords: function (component, event, helper) {
        component.set("v.isBusy", true);
        var action = component.get("c.searchCreditMemo");
        action.setParams({
            pageNumber: component.get("v.pageNumber2"),
            pageSize: component.get("v.pageCount2")
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            // console.log("state--->" + state);
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if (results) {
                    component.set("v.allCreditData", results.allCreditMemoList);
                    component.set(
                        "v.currentCreditData",
                        results.currentCreditMemoList
                    );
                    component.set(
                        "v.totalRecords2",
                        results.allCreditMemoList.length
                    );
                } else {
                    var errors = response.getError();
                }
                component.set("v.isBusy", false);
            }
        });
        $A.enqueueAction(action);
    },
    getPaymentRecords: function (component, event, helper) {
        component.set("v.isBusy", true);
        var action = component.get("c.searchPayment");
        action.setParams({
            pageNumber: component.get("v.pageNumber3"),
            pageSize: component.get("v.pageCount3")
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if (results) {
                    // add haibo: french
                    console.log(JSON.stringify(results.currentPaymentList), 'results.currentPaymentList===========');
                    results.currentPaymentList.forEach((item)=>{
                        switch (item.paypalStatus) {
                            case 'Payment Success':
                                item.paypalStatus = $A.get("$Label.c.CCM_Portal_PaymentSuccess")
                                break;
                            case 'Processing':
                                item.paypalStatus = $A.get("$Label.c.CCM_Portal_Processing")
                                break;
                            case 'Payment Failed':
                                item.paypalStatus = $A.get("$Label.c.CCM_Portal_Paymentfailed")
                                break;
                            default:
                                break;
                        }
                    })
                    component.set(
                        "v.currentPaymentData",
                        results.currentPaymentList
                    );
                    console.log(results.currentPaymentList);
                    component.set(
                        "v.totalRecords3",
                        results.allPaymentList.length
                    );
                } else {
                    var errors = response.getError();
                    if (errors) {
                        if (errors[0] && errors[0].message) {
                            alert($A.get("$Label.c.CCM_Portal_Error") + ': ' + errors[0].message);
                        }
                    } else {
                        alert($A.get("$Label.c.CCM_Portal_ErrorTips"));
                    }
                }
                component.set("v.isBusy", false);
            }
        });
        $A.enqueueAction(action);
    },
    getUrlParameter: function (sParam) {
        var sPageURL = decodeURIComponent(window.location.search.substring(1)),
            sURLVariables = sPageURL.split("&"),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split("=");
            if (
                sParameterName[0] === sParam ||
                sParameterName[0] === "0." + sParam
            ) {
                return sParameterName[1] === undefined
                    ? true
                    : sParameterName[1];
            }
        }
    },
    // initExportTypeList: function(component) {
    //     let exportTypeList = [
    //         {"label": "Debit Memo",
    //          "value": "Debit Memo"},
    //         {"label": "Credit Memo",
    //          "value": "Credit Memo"},
    //         {"label": "Transaction History",
    //          "value": "Transaction History"}
    //     ];

    //     component.set('v.exportTypeList', exportTypeList);
    // },
    excelDebitMemoDataFormat: function(data) {
        let excelDatasFormat = [];
        data.forEach( item => {
                let excelData = {
                    'Memo Number': '',
                    'Customer PO Number': '',
                    'Order Number': '',
                    'Invoice Created Date': '',
                    'Due Date': '',
                    'Invoice Amount': '',
                    'Paid Amount': '',
                    'Outstanding Amount': '',
                    'Overdue': '',
                    'Overdue Days': ''
                };
                excelData['Memo Number'] = item['Memo_Number'];
                excelData['Customer PO Number'] = item['Customer_PO_Number'];
                excelData['Order Number'] = item['Order_Number'];
                excelData['Invoice Created Date'] = item['Invoice_Created_Date'];
                excelData['Due Date'] = item['Due_Date'];
                excelData['Invoice Amount'] = item['Invoice_Amount'];
                excelData['Paid Amount'] = item['Paid_Amount'];
                excelData['Outstanding Amount'] = item['Outstanding_Amount'];
                excelData['Overdue'] = item['Overdue'];
                excelData['Overdue Days'] = item['Overdue_Days'];
                excelDatasFormat.push(excelData);
            }
        );
        return excelDatasFormat;
    },
    excelCreditMemoDataFormat: function(data) {
        let excelDatasFormat = [];
        data.forEach( item => {
                let excelData = {
                    'Invoice Number': '',
                    'Customer PO Number': '',
                    'Invoice Date': '',
                    'Due Amount': '',
                    'Remaining Amount': ''
                };
                excelData['Invoice Number'] = item['Invoice_Number'];
                excelData['Customer PO Number'] = item['Customer_PO_Number'];
                excelData['Invoice Date'] = item['Invoice_Date'];
                excelData['Due Amount'] = item['Due_Amount'];
                excelData['Remaining Amount'] = item['Remaining_Amount'];
                excelDatasFormat.push(excelData);
            }
        );
        return excelDatasFormat;
    },
    excelTransactionHistoryDataFormat: function(data) {
        let excelDatasFormat = [];
        data.forEach( item => {
                let excelData = {
                    'Payment ID': '',
                    'Paid By': '',
                    'Payment Status': '',
                    'Transaction Status': '',
                    'Transaction Date': '',
                    'Payment Method': '',
                    'Card Last Four Digits': '',
                    'Payment Amount': '',
                    'Remark': '',
                    'Total Amount Paid': '',
                    'Invoice/Credit Memo': '',
                    'Invoice/Credit Memo Amount': '',
                    'Cash Discount for Early Payment': '',
                    'Total Net Payment': ''
                };
                excelData['Payment ID'] = item['Payment_ID'];
                excelData['Paid By'] = item['Paid_By'];
                excelData['Payment Status'] = item['Payment_Status'];
                excelData['Transaction Status'] = item['Transaction_Status'];
                excelData['Transaction Date'] = item['Transaction_Date'];
                excelData['Payment Method'] = item['Payment_Method'];
                excelData['Card Last Four Digits'] = item['Card_Last_Four_Digits'];
                excelData['Payment Amount'] = item['Payment_Amount'];
                excelData['Remark'] = item['Remark'];
                excelData['Total Amount Paid'] = item['Total_Amount_Paid'];
                excelData['Invoice/Credit Memo'] = item['Invoice_Credit_Memo'];
                excelData['Invoice/Credit Memo Amount'] = item['Invoice_Credit_Memo_Amount'];
                excelData['Cash Discount for Early Payment'] = item['Cash_Discount_for_Early_Payment'];
                excelData['Total Net Payment'] = item['Total_Net_Payment'];
                excelDatasFormat.push(excelData);
            }
        );
        return excelDatasFormat;
    },
    handleExportConfirm: function(component) {
        let self = this;
        let exportType = component.get('v.exportType');
        if(exportType) {
            let action;
            let exportFileName;
            if(exportType === 'Debit Memo') {
                action = component.get("c.getExportDebitMemo");
                action.setParams({
                    isOverdueStr: component.get("v.overdueStr"),
                    invoiceRangeStr: component.get("v.invoiceDateRange")
                });
                exportFileName = 'Debit Memo.xlsx';
            }
            else if(exportType === 'Credit Memo') {
                action = component.get("c.getExportCreditMemo");
                exportFileName = 'Credit Memo.xlsx';
            }
            else if(exportType === 'Transaction History') {
                action = component.get('c.getExportTransactionHistory');
                exportFileName = 'Transaction History.xlsx';
            }
            component.set('v.exportFileName', exportFileName);
            if(action) {
                action.setCallback(this, function(response) {
                    var state = response.getState();
                    if (state === "SUCCESS") {
                        if(response.getReturnValue()) {
                            component.set('v.showExport', false);
                            self.showToast($A.get("$Label.c.CCM_Portal_ExportInProgress"), $A.get("$Label.c.CCM_Portal_ExportIsInProgress"), 'success');
                            var data = JSON.parse(response.getReturnValue());
                            // format data to excel format, pass data to download
                            if(exportType === 'Debit Memo') {
                                data = self.excelDebitMemoDataFormat(data);
                            }
                            else if(exportType == 'Credit Memo') {
                                data = self.excelCreditMemoDataFormat(data);
                            }
                            else if(exportType == 'Transaction History') {
                                data = self.excelTransactionHistoryDataFormat(data);
                            }
                            let exportCmp = component.find('exportcmp');
                            exportCmp.setSourceData(data);
                            exportCmp.exportFile();
                        }
                    }
                });
            }
            $A.enqueueAction(action);
        }
    },
    showToast: function(title, message, type) {
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams({
            "title": title,
            "message": message,
            "type": type, //'error', 'warning', 'success', or 'info'. 
        });
        toastEvent.fire();
    }
});