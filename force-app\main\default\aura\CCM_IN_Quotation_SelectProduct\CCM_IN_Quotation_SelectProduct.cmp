<aura:component implements="flexipage:availableForAllPageTypes" access="global" controller="CCM_Quotation_DetailCtl">
    <ltng:require scripts="{!join(',', $Resource.Validator)}" />
    <aura:attribute name="customerId" type="String" default="" />
    <aura:attribute name="prodCondition" type="String" default="" />
    <aura:attribute name="quotation" type="Object" default="" />
    <!--order table-->
    <aura:attribute name="quotationItemList" type="List" default="[]" />
    <aura:attribute name="totalQuantity" type="String" default="0" />
    <aura:attribute name="subTotal" type="Decimal" default="0" />
    <aura:attribute name="currencySymbol" type="String" default="$" />
    <aura:attribute name="requireFlag" type="Boolean" default="true" />
    <aura:attribute name="requireDate" type="Boolean" default="true" />
    <aura:attribute name="disableFlag" type="Boolean" default="false" />
    <aura:attribute name="disableBtn" type="Boolean" default="true" />
    <aura:attribute name="disableDate" type="Boolean" default="true" />
    <aura:attribute name="isProductSelectStatus" type="Boolean" default="false" />
    <aura:attribute name="recordId" type="String" default="" />
    <aura:attribute name="currentStep" type="Integer" default="2" />
    <aura:attribute name="operationRow" type="Integer" default="0" />
    <!--Freight Term Fee in rule-->
    <aura:attribute name="freightTermRuleFee" type="Currency" />
    <!--Show free freight fee flag-->
    <aura:attribute name="showFreeShippingMsg" type="Boolean" default="false" />
    <aura:attribute name="isWaivedFreight" type="Boolean" default="false" />
    <!--Freight Fee to be waived-->
    <aura:attribute name="waiveShippingFee" type="Currency" default="" />
    <!--Waived freight fee massage-->
    <aura:attribute name="waivedShippingFeeMsg" type="String" default="" />

    <aura:attribute name="brandScope" type="String" default="" />
    <aura:attribute name="oldBrandName" type="String" default="" />
    <aura:attribute name="brandScopeOpt" type="List" default="" access="public" />
    <aura:attribute name="isShowTerm" type="Boolean" default="false" />
    <aura:attribute name="modalContent" type="String" />
    <aura:attribute name="paymentTermLabel" type="String" default="" />
    <aura:attribute name="freightTermLabel" type="String" default="" />
    <aura:attribute name="isBusy" type="Boolean" default="false" />
    <aura:attribute name="showEditModal" type="Boolean" default="false" />
    <!--Order Type: Distribute / Dropship-->
    <aura:attribute name="orderTypeVal" type="String" default="" />
    <aura:attribute name="isDropShip" type="Boolean" default="false" />

    <!--Add by Abby on 06022020 for research payment term-->
    <aura:attribute name="paymentTermAllOpts" type="List" default="[]" />
    <aura:attribute name="paymentTermSelectOpt" type="List" default="[]" />
    <aura:attribute name="paymentTermValue" type="String" default="" />
    <aura:attribute name="multipleOptions" type="Boolean" default="false" />
    <aura:attribute name="needResearch" type="Boolean" default="false" />
    <aura:attribute name="customerType" type="String" default="" />
    <aura:attribute name="customerCluster" type="String" default="" />
    <aura:attribute name="isPortal" type="String" default="false" />

    <!--Add by Abby on 08202020 for payment Term Table-->
    <aura:attribute name="defaultPaymentTerm" type="String" default="" />

    <!--Add by Eric on 03102021 for applying promotions-->
    <aura:attribute name="selectedPromotion" type="Object" default="" />
    <aura:attribute name="termsPromoCode" type="String" default="" />
    <aura:attribute name="showThresholdProduct" type="Boolean" default="false" />
    <aura:attribute name="isMultiThresholdProduct" type="Boolean" default="false" />
    <aura:attribute name="showOfferingProduct" type="Boolean" default="false" />
    <aura:attribute name="isPoolFreeGoods" type="Boolean" default="false" />
    <aura:attribute name="thresholdOpts" type="List" default="[]" />
    <aura:attribute name="oldSelectedThresholdProducts" type="List" default="[]" />
    <aura:attribute name="offeringOpts" type="List" default="[]" />
    <aura:attribute name="oldSelectedOfferingProducts" type="List" default="[]" />
    <aura:attribute name="editRow" type="Integer" default="-1" />
    <aura:attribute name="showAvailablePromotions" type="Boolean" default="false" />
    <aura:attribute name="showPromotionReminder" type="Boolean" default="false" />
    <aura:attribute name="notApplyPromoList" type="List" default="[]" />
    <aura:attribute name="reminderPromoList" type="List" default="[]" />
    <aura:attribute name="wholeOrderPromo" type="Object" default="" />
    <aura:attribute name="showWholeOrderOfferingProduct" type="Boolean" default="false" />
    <aura:attribute name="termsPromo" type="Object" default="" />
    <aura:attribute name="termsPromoPTValue" type="String" default="" />
    <aura:attribute name="prePaymentTermLabel" type="String" default="" />
    <aura:attribute name="promotionFilterCondition" type="String" default="" />
    <aura:attribute name="activeSections" type="List" default="['1']" />

    <aura:attribute name="minSelectDate" type="String" />
    <aura:attribute name="needCheckOrder" type="Boolean" />

    <lightning:navigation aura:id="navService" />

    <!--Yanko-->
    <aura:attribute name="showcaseqtyalert" type="Boolean" default="false" />
    <aura:attribute name="ExpirateDate" type="Boolean" default="true" />
    <aura:attribute name="showminqtyalert" type="Boolean" default="false" />
    <aura:attribute name="model" type="Decimal" default="0" />
    <aura:attribute name="minmodel" type="Decimal" default="0" />
    <aura:attribute name="minqty" type="Decimal" default="0" />
    <aura:attribute name="OrgCode" type="String" default="" />

    <!--Customer Org Code-->
    <aura:attribute name="customerOrgCode" type="String" default="" />

    <!--Add by Eric on 03072022 for Surcharge Rate-->
    <aura:attribute name="surcharge" type="Decimal" default="0" />

    <aura:attribute name="showUpload" type="Boolean" default="true" />

    <aura:attribute name="uploadDisabed" type="Boolean" default="true" />

    <aura:handler name="init" value="{!this}" action="{!c.doInit}" />
    <aura:handler name="change" value="{!v.brandScope}" action="{!c.brandChange}" />
    <aura:handler name="change" value="{!v.quotation.Expected_Delivery_Date__c}" action="{!c.ecpectedDateChange}" />
    <aura:handler name="change" value="{!v.quotation.Product_Price__c}" action="{!c.rebindPayment}" />

    <lightning:card class="mainContent">
        <lightning:spinner size="large" variant="brand"
            class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }" />
        <div class="slds-grid slds-grid_vertical-align-end slds-grid--align-spread">
            <div class="slds-clearfix">
                <div class="slds-col slds-list_horizontal slds-wrap ccm_paddingLeft">
                    <aura:if isTrue="{!v.currentStep == 2}">
                        <span class="ccm_fontColor">Authorized Brands:</span>
                        <lightning:combobox aura:id="brandScope" name="brandScope" placeholder="--Select Brand--"
                            value="{!v.brandScope}" options="{!v.brandScopeOpt}" label="Authorized Brands" />
                    </aura:if>
                </div>
            </div>

            <div class="slds-clearfix">
                <div class="slds-col slds-list_horizontal slds-wrap ccm_paddingLeft">
                    <aura:if isTrue="{!v.currentStep == 2}">
                        <span class="ccm_fontColor">Schedule Ship Date:</span>
                        <lightning:input aura:id="expectedDeliveryDate" label="Schedule Ship Date"
                            value="{!v.quotation.Expected_Delivery_Date__c}" type="Date" required="{!v.requireDate}"
                            disabled="{!v.disableDate}" min="{!v.minSelectDate}" />
                    </aura:if>
                </div>
            </div>

            <div class="slds-clearfix">
                <div class="slds-col slds-list_horizontal slds-wrap slds-float_right slds-p-right--large">
                    <aura:if isTrue="{!and(v.isShowTerm == true,!empty(v.brandScope))}">
                        <aura:if isTrue="{!!v.needResearch}">
                            <span class="ccm_fontColor">Payment Term: {!v.paymentTermLabel}<br />Freight Term:
                                {!v.freightTermLabel}</span>
                            <aura:set attribute="else">
                                <span class="ccm_fontColor">
                                    Payment Term:
                                    <lightning:combobox class="paymentTermSelectList" aura:id="required-Field"
                                        name="paymentTermInfo" placeholder="--Select Payment Term--"
                                        value="{!v.paymentTermValue}" options="{!v.paymentTermSelectOpt}" label=""
                                        title="{!v.paymentTermLabel}" /><br />
                                    Freight Term: {!v.freightTermLabel}
                                </span>
                            </aura:set>
                        </aura:if>
                    </aura:if>
                </div>
            </div>
        </div>

        <!--Calvin-->
        <aura:if isTrue="{!v.showcaseqtyalert}">
            <p style="color:red;" class="tips">"Model {!v.model}" must be ordered in Case Quantities. Please refer to
                the line item below for the Case Quantity for this product.</p>
        </aura:if>
        <aura:if isTrue="{!v.showminqtyalert}">
            <p style="color:red;" class="tips">"Model {!v.minmodel}" promotion has min Quantity({!v.minqty}).Please
                refer to the line item below for the min Quantity for this Promotion.</p>
        </aura:if>

        <p class="slds-p-horizontal_small slds-p-top_small">
        <table aria-multiselectable="true"
            class="slds-table slds-table_bordered slds-table_resizable-cols slds-table_striped" role="grid">
            <thead>
                <tr class="slds-line-height_reset">
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallXSWidth"
                        scope="col" style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button"
                            tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate" title="Line">Line</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth"
                        scope="col">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button"
                            tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate" title="Product Description">Product Description</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth"
                        scope="col">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button"
                            tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate" title="Available Promotions"
                                    style="text-align: center;">Available Promo</span>
                            </div>
                        </a>
                    </th>
                    <!-- <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallXSWidth" scope="col" style="">
                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                        <div class="slds-grid slds-grid_vertical-align-center text_center slds-has-flexi-truncate">
                            <span class="slds-truncate" title="Model Num" style="text-align: center;">Model #</span>
                        </div>
                    </a>
                </th> -->

                    <!-- <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallXSWidth" scope="col" style="">
                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                            <span class="slds-truncate" title="Brand" style="text-align: center;">Brand</span>
                        </div>
                    </a>
                </th> -->

                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button"
                            tabindex="0">
                            <div class="slds-text-align-center text_center">
                                <span class="slds-truncate" title="Model Num">
                                    <p style="text-align: center;">Model #</p>
                                </span>
                            </div>
                        </a>
                    </th>

                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button"
                            tabindex="0">
                        <div class="slds-text-align-center text_center">
                            <span class="slds-truncate" title="Brand">
                                <p style="text-align: center;">Brand</p>
                            </span>
                        </div>
                    </a>
                </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumLWidth"
                        scope="col" style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button"
                            tabindex="0">
                            <div
                                class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                <span class="slds-truncate" title="Launch Date" style="text-align: center;">Launch
                                    Date</span>
                        </div>
                    </a>
                </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumLWidth"
                        scope="col" style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button"
                            tabindex="0">
                            <div
                                class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                <span class="slds-truncate" title="Ship Date" style="text-align: center;">Expected Ship
                                    Date</span>
                        </div>
                    </a>
                </th>
                <th aria-label="" aria-sort="none" class="slds-text-align-center" scope="col" style="width: 140px;">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button"
                            tabindex="0">
                        <div class="slds-text-align-center text_center" style="width: 140px;">
                            <span class="slds-truncate" title="Quantity">
                                <p style="text-align: center;">Qty</p>
                                    <p style="text-align: center;">(EA)</p>
                                </span>
                        </div>
                    </a>
                </th>
                <!-- <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="">
                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                        <div class="slds-text-align-center text_center" style="width: 140px;">
                            <span class="slds-truncate" title="Quantity">Qty<p style="text-align: center;">(EA)</p></span>
                        </div>
                    </a>
                </th> -->
                    <!-- <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallXSWidth" scope="col" style="">
                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table text_center">
                            <span class="slds-truncate" title="Quantity UM" style="text-align: center;">Case Qty</span>
                        </div>
                    </a>
                </th> -->

                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button"
                            tabindex="0">
                            <div class="slds-text-align-center text_center">
                                <span class="slds-truncate" title="Case Qty">
                                    <p style="text-align: center;">Case Qty</p>
                                </span>
                            </div>
                        </a>
                    </th>

                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth"
                        scope="col" style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button"
                            tabindex="0">
                            <!-- <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                            <span class="slds-truncate" title="Everyday Price">Everyday Price</span>
                        </div> -->
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate text_center">
                                <span class="slds-truncate" title="Everyday Price">Everyday Price<p
                                        style="text-align: center;">(EA)</p></span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumMWidth"
                        scope="col" style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button"
                            tabindex="0">
                            <!-- <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                            <span class="slds-truncate" title="Invoice Price">Invoice Price</span>
                        </div> -->
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate text_center">
                                <span class="slds-truncate" title="Invoice Price">Invoice Price<p
                                        style="text-align: center;">(EA)</p></span>
                            </div>
                        </a>
                    </th>

                    <!-- <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="">
                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                            <span class="slds-truncate" title="Discount">Discount</span>
                        </div>
                    </a>
                </th> -->

                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                        style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button"
                            tabindex="0">
                            <!-- <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate"> -->
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate text_center">
                                <span class="slds-truncate" title="Discount" style="text-align: center;">Promo
                                    Discount</span>
                            </div>
                        </a>
                    </th>

                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col"
                        style="width: 120px">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button"
                            tabindex="0">
                            <div class="slds-text-align-center text_center" style="width: 130px">
                                <span class="slds-truncate" title="Subtotal Amount">
                                    <p style="text-align: center;">Subtotal</p>
                                </span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth" scope="col"
                        style="">
                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button"
                            tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate text_center">
                                <span class="slds-truncate" title="Promotion Code">Promotion Code</span>
                            </div>
                        </a>
                    </th>
                    <th class="buttonWidth" scope="col">
                        <div class="slds-truncate slds-assistive-text" title="Actions"></div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <aura:iteration items="{!v.quotationItemList}" var="orderItem" indexVar="index">
                    <tr aria-selected="false" class="slds-hint-parent" id="{!index}" onmouseover="{!c.rowFocus}">
                        <th scope="row">
                            <div class="slds-truncate" title="">
                                {!index + 1}
                            </div>
                        </th>
                        <td role="gridcell" title="{!orderItem.Product__r.SF_Description__c}">
                            <aura:if isTrue="{!orderItem.isOffering}">
                                <div class="slds-truncate largeWidth">
                                    {!orderItem.Product__r.SF_Description__c}
                                </div>
                                <aura:set attribute="else">
                                    <aura:if isTrue="{!orderItem.isThreshold}">
                                        <div class="slds-truncate largeWidth">
                                            {!orderItem.Product__r.SF_Description__c}
                                        </div>
                                        <aura:set attribute="else">
                                            <c:CCM_AutoMatchPickList aura:id="required-product" objectType="product2"
                                                showLabel="false" labelField="SF_Description__c"
                                                labelField1="ProductCode" filterCondition='{!v.prodCondition}'
                                                fieldList="Name,Item_Number__c,serial_number__c,ProductCode,SF_Description__c,ProductModel__c"
                                                value="{!orderItem.Product__c}"
                                                inputValue="{!orderItem.Product__r.SF_Description__c}"
                                                required="{!v.requireFlag}" disabled="{!v.disableFlag}"
                                                indexnum="{!index}" populateWhenBlank="false"
                                                onSelect="{!c.onSelectProd}" />
                                        </aura:set>
                                    </aura:if>
                                </aura:set>
                            </aura:if>

                            <aura:if
                                isTrue="{!and(and(or(orderItem.isThreshold,orderItem.HasPromo), orderItem.isMix), !orderItem.isMeet)}">
                                <span style="color:red;">The conditions for applying this promotion are not met.</span>
                            </aura:if>
                            <aura:if isTrue="{!and(and(orderItem.isOffering, orderItem.isPool), orderItem.isExceed)}">
                                <span style="color:red;">The total quantity of free goods exceeds the quantity
                                    available.</span>
                            </aura:if>
                            <aura:if isTrue="{!and(and(orderItem.isOffering, orderItem.isPool), orderItem.isLess)}">
                                <span style="color:orange;">You can add more free goods.</span>
                            </aura:if>
                            <aura:if isTrue="{!orderItem.counterror}">
                                <p style="color:red;">This product must be ordered by case qty.</p>
                            </aura:if>
                        </td>
                        <td role="gridcell" title="">
                            <aura:if isTrue="{!orderItem.HasPromo}">
                                <div style="position: relative;">
                                    <div class="slds-truncate slds-align_absolute-center">
                                        <lightning:icon iconName="utility:info" alternativeText="Info" variant="warning"
                                            size="small" indexnum="{!index}" onmouseover="{!c.onDisplayPromo}" />
                                    </div>
                                    <div class="slds-box hideTip" id="{!index + 'Tip'}">
                                        <div class="slds-grid">
                                            <div
                                                class="slds-col slds-size--1-of-2 slds-medium-size--5-of-6 slds-large-size--11-of-12">
                                                <h2 style="font-size: 0.9rem;"><b>Available Promotions:</b></h2>
                                            </div>
                                            <div
                                                class="slds-col slds-size--1-of-2 slds-medium-size--1-of-6 slds-large-size--1-of-12">
                                                <lightning:buttonIcon iconName="utility:close" variant="bare"
                                                    iconClass="dark" onclick="{!c.onHidePromo}" alternativeText="Close"
                                                    title="Close" />
                                            </div>
                                        </div>
                                        <br />
                                        <aura:iteration items="{!orderItem.promotionList}" var="pItem"
                                            indexVar="pIndex">
                                            <div
                                                class="slds-grid slds-grid_align-spread slds-grid_vertical-align-center slds-datepicker__filter">
                                                <div
                                                    class="slds-col slds-size--1-of-2 slds-medium-size--1-of-6 slds-large-size--2-of-12">
                                                    <lightning:button name="{!pIndex}"
                                                        class="slds-p-horizontal_xx-small" variant="brand" label="Apply"
                                                        title="Apply" onclick="{!c.onApplyPromo}" />
                                                </div>
                                                <div
                                                    class="slds-col slds-size--1-of-2 slds-medium-size--5-of-6 slds-large-size--10-of-12">
                                                    <p>{!pItem.promotion.Name} - Promo Code: <span style="color:blue;"
                                                            name="{!pItem.promotion.Id}"
                                                            onclick="{!c.navigateToPromotion}">{!pItem.promotion.Promotion_Code_For_External__c}</span>
                                                    </p>
                                                </div>
                                            </div>
                                        </aura:iteration>
                                    </div>
                                </div>
                                </aura:if>
                            </td>
                            <td role="gridcell" title="{!orderItem.ProductCode__c}">
                                <div class="slds-truncate slds-align_absolute-center">
                                    {!orderItem.ProductCode__c}
                                </div>
                            </td>
                            <td role="gridcell" title="{!orderItem.Brand__c}">
                                <div class="slds-truncate slds-align_absolute-center">
                                    {!orderItem.Brand__c}
                                </div>
                            </td>
                            <td role="gridcell" title="{!orderItem.Lanch_Date__c}">
                                <div class="slds-truncate slds-align_absolute-center">
                                    {!orderItem.Lanch_Date__c}
                                </div>
                            </td>
                            <td role="gridcell" title="{!orderItem.Ship_Date__c}">
                                <div class="slds-truncate slds-align_absolute-center" style="width: 200px;">
                                    <aura:if isTrue="{!orderItem.isOffering}">
                                    <lightning:input aura:id="shipdate" type="Date" label=""
                                        value="{!orderItem.Ship_Date__c}" name="{!index}" disabled="false"
                                        min="{!orderItem.minSelectDate}" />
                                        <aura:set attribute="else">
                                        <lightning:input aura:id="shipdate" type="Date" label="" name="{!index}"
                                            value="{!orderItem.Ship_Date__c}" min="{!orderItem.minSelectDate}" />
                                        </aura:set>
                                    </aura:if>
                                </div>
                            </td>
                            <td role="gridcell" title="{!orderItem.Quantity__c}">
                            <div class="slds-truncate clear-user-agent-styles slds-align_absolute-center"
                                style="width:140px;">
                                <aura:if
                                    isTrue="{!or(and(orderItem.isOffering,!orderItem.isPool),and(orderItem.isThreshold,and(!orderItem.isMix, orderItem.hasFreeGoods)))}">
                                    <lightning:input type="number" label="" value="{!orderItem.Quantity__c}" min="1"
                                        disabled="true" />
                                        <aura:set attribute="else">
                                            <!-- Yanko -->
                                            <!-- <lightning:input type="number" label=""  value="{!orderItem.Quantity__c}" min="1" disabled="{!v.disableFlag}" name="{!index}" oncommit="{!c.calculateSubTotal}"/> -->
                                        <div class="slds-grid slds-m-bottom_small item_center"
                                            style="margin-bottom: 0;">
                                                <div class="slds-col slds-size_5-of-16 slds-text-align_right">
                                                <lightning:button label="-" onclick="{!c.handleReduceClick }">
                                                </lightning:button>
                                                </div>
                                                <div class="slds-col slds-size_3-of-8 vertical-align:middel">
                                                <lightning:input class="vertical-align:middel" type="number" label=""
                                                    value="{!orderItem.Quantity__c}" disabled="{!v.disableFlag}"
                                                    name="{!index}" oncommit="{!c.calculateSubTotal}" />
                                                </div>
                                                <div class="slds-col slds-size_5-of-16 slds-text-align_left">
                                                <lightning:button label="+" onclick="{!c.handleIncreaseClick}">
                                                </lightning:button>
                                            </div>
                                        </div>
                                    </aura:set>
                                </aura:if>
                            </div>
                        </td>

                        <!-- <td role="gridcell" title="EA">
                                <div class="slds-truncate clear-user-agent-styles" >
                                    <span>EA</span>
                                </div>
                            </td> -->

                        <td role="gridcell" title="{!orderItem.CS_Exchange_Rate__c}">
                            <div class="slds-truncate slds-align_absolute-center">
                                {!orderItem.CS_Exchange_Rate__c}
                            </div>
                        </td>

                        <td role="gridcell" title="{!orderItem.List_Price__c}">
                            <div class="slds-truncate slds-align_absolute-center">
                                <lightning:formattedNumber value="{!orderItem.List_Price__c}" style="currency"
                                    currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" />
                            </div>
                        </td>

                        <td role="gridcell" title="{!orderItem.Unit_Price__c}">
                            <div class="slds-truncate slds-align_absolute-center slds-align_absolute-center"
                                style="{!join('','font-weight:',orderItem.List_Price__c>orderItem.Unit_Price__c?'bold':'normal')}">
                                <!--
                                    <span class="invoicePriceCurrency">{!v.currencySymbol}</span>
                                    <lightning:input value="{!orderItem.Unit_Price__c}" min="0" disabled="{!v.disableFlag}" name="{!index}" readonly="true" />
                                    -->
                                <!-- onchange="{!c.calculateSubTotal}" onblur="{!c.calculateCurrencyFormat}" -->
                                <lightning:formattedNumber value="{!orderItem.Unit_Price__c}" style="currency"
                                    currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" />
                            </div>
                        </td>


                        <!-- <td role="gridcell" title="{!orderItem.Discount_Amount__c}">
                                <div class="slds-truncate required">
                                    <lightning:formattedNumber value="{!(orderItem.Discount_Amount__c)}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/>
                                </div>
                            </td> -->

                        <td role="gridcell"
                            title="{!(orderItem.Promo_Discount_Amount__c + orderItem.Whole_Order_Promo_Discount_Amount__c)}">
                            <div class="slds-truncate required slds-align_absolute-center">
                                <lightning:formattedNumber
                                    value="{!((orderItem.Promo_Discount_Amount__c*100 + orderItem.Whole_Order_Promo_Discount_Amount__c*100)/100)}"
                                    style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" />
                            </div>
                        </td>


                        <td role="gridcell" title="{!orderItem.Sub_Total__c}">
                            <div class="slds-truncate slds-align_absolute-center">
                                <lightning:formattedNumber value="{!orderItem.Sub_Total__c}" style="currency"
                                    currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" />
                            </div>
                        </td>
                        <td role="gridcell" title="Promotion Code">
                            <aura:if isTrue="{!orderItem.isOffering}">
                                <div class="slds-truncate" style="width: 140px;">
                                    <lightning:input name="{!index}" value="{!orderItem.PromotionName__c}"
                                        disabled="true" />
                                </div>
                                <aura:set attribute="else">
                                    <aura:if isTrue="{!orderItem.isThreshold}">
                                        <div class="slds-truncate" style="width: 140px;">
                                            <lightning:input name="{!index}" value="{!orderItem.PromotionName__c}"
                                                disabled="true" />
                                        </div>
                                        <aura:set attribute="else">
                                            <c:CCM_AutoMatchPickList aura:id="{required-promotion}"
                                                objectType="Promotion2__c" showLabel="false"
                                                labelField="Promotion_Code_For_External__c" labelField1="Name"
                                                filterCondition='{!orderItem.promotionFilterCondition}'
                                                fieldList="Name,Promotion_Code_For_External__c"
                                                value="{!v.prePaymentTermLabel}"
                                                inputValue="{!orderItem.PromotionName__c}" required="false"
                                                disabled="{!v.disableFlag}" indexnum="{!index}"
                                                onSelect="{!c.onSelectPromo}" onInputFocus="{!c.promFocusHandler}"
                                                isSkipNullFilter="true" />

                                            <!--
                                                <lightning:input type="search" label="search"  name="{!index}" value="{!orderItem.PromotionName__c}"/>
                                                -->
                                        </aura:set>
                                    </aura:if>
                                </aura:set>
                            </aura:if>
                        </td>

                        <td role="gridcell" data-index="{!v.index}">
                            <aura:if isTrue="{!orderItem.isOffering}">
                                <div class="slds-truncate clear-user-agent-styles">
                                    <span><i>Free Goods</i></span>
                                </div>
                                <aura:set attribute="else">
                                    <aura:if isTrue="{!orderItem.isThreshold}">
                                        <div class="slds-truncate clear-user-agent-styles">
                                            <span><i>Mix Products</i></span>
                                        </div>
                                        <aura:set attribute="else">
                                            <lightning:icon iconName="utility:delete" alternativeText="Delete"
                                                size="x-small" onclick="{!c.handleDelete}" />
                                            <aura:if isTrue="{!or(orderItem.hasMix, orderItem.hasPool)}">
                                                <div class="slds-truncate clear-user-agent-styles"
                                                    style="font-size:xx-small;">
                                                    <lightning:button variant="Base" label="Re-select Product"
                                                        onclick="{! c.handleReSelectProductClick }" />
                                                </div>
                                            </aura:if>
                                        </aura:set>
                                    </aura:if>
                                </aura:set>
                            </aura:if>
                            <aura:if
                                isTrue="{!and(orderItem.isPool, orderItem.Promotion__c == orderItem.Whole_Order_Promotion__c)}">
                                <div class="slds-truncate clear-user-agent-styles" style="font-size:xx-small;">
                                    <lightning:button variant="Base" label="Re-select Product"
                                        onclick="{! c.handleReSelectWholeOrderPromoProductClick }" />
                                </div>
                            </aura:if>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="15" style="border: 0px solid black;">
                            <aura:if isTrue="{!orderItem.shipDateGreaterLanuchDate}">
                                <p style="color: red;">The Product expected ship date needs to be equal or greater than
                                    the Launch Date</p>
                            </aura:if>
                        </td>
                    </tr>
                </aura:iteration>
            </tbody>
        </table>
        </p>

        <div class="slds-m-vertical_small slds-m-horizontal_medium slds-border_bottom">
            <lightning:layout>
                <div class="c-container" style="padding: 10px">
                    <lightning:layoutItem alignmentBump="right">
                        <lightning:button label="Add Item" iconName="utility:add" iconPosition="left"
                            onclick="{!c.addItem}" disabled="{!v.disableBtn}" />
                    </lightning:layoutItem>
                </div>

                <lightning:layoutItem alignmentBump="left">
                    <aura:if isTrue="{!!v.isWaivedFreight}">
                        <div class="{!v.showFreeShippingMsg ? '':'slds-hide'}">Purchase additional
                            <lightning:formattedNumber value="{!v.quotation.Freight_Fee_To_Be_Waived__c}"
                                style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" /> for free
                            shipping.
                        </div>
                        <aura:set attribute="else">
                            <div class="{!v.showFreeShippingMsg ? '':'slds-hide'}">You have reached the free shipping
                                amount.</div>
                        </aura:set>
                    </aura:if>
                    <aura:if isTrue="{!v.isProductSelectStatus}">
                        {!v.quotation.Freight_Target_Fee__c}
                    </aura:if>
                </lightning:layoutItem>
            </lightning:layout>

            <div class="slds-grid totalSection slds-grid_vertical-align-center">
                <aura:if isTrue="{!v.termsPromo}">
                    <div class="slds-text-align--right slds-truncate">
                        {!v.termsPromo.promotion.Promotion_Code_For_External__c}
                    </div>
                    <div style="margin-left: 10px;margin-right: 10px;">
                        <lightning:icon iconName="utility:close" alternativeText="close" size="x-small"
                            onclick="{!c.handleDeleteTermsPromo}" />
                    </div>
                </aura:if>
                <aura:if isTrue="{!v.wholeOrderPromo}">
                    <div class="slds-text-align--right slds-truncate">
                        {!v.wholeOrderPromo.promotion.Promotion_Code_For_External__c}
                    </div>
                    <div style="margin-left: 10px;margin-right: 10px;">
                        <lightning:icon iconName="utility:close" alternativeText="close" size="x-small"
                            onclick="{!c.handleDeleteWholeOrderPromo}" />
                    </div>
                </aura:if>
                <div class="slds-text-align--right">
                    <lightning:input label="" name="termsPromoCode"
                        placeholder="Enter Terms Promo/Whole Order Promo Code" onchange="{!c.handleTermsPromoChange}"
                        style="font-size: small;width: 310px;" value="{!v.termsPromoCode}" />
                </div>
                <div style="margin-left: 10px;">
                    <lightning:button class="slds-p-horizontal_x-small" variant="brand" label="Apply" title="Apply"
                        onclick="{!c.onApplyTermsPromo}" />
                </div>
            </div>

            <div class="slds-grid totalSection">
                <div class="slds-text-align--right">
                    <p><strong>Total Quantity:</strong></p>
                    <p><strong>Product Amount: </strong></p>
                    <p><strong>Total Savings: </strong></p>
                    <p><strong>Total Amount:</strong> </p>
                </div>
                <div>
                    <p><strong>{!v.quotation.Total_Quantity__c}</strong></p>
                    <p><strong>
                            <lightning:formattedNumber value="{!v.quotation.Product_Price__c}" style="currency"
                                currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" />
                        </strong></p>
                    <p class="required"><strong>
                            <lightning:formattedNumber value="{!v.quotation.Total_Saving}" style="currency"
                                currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" />
                        </strong></p>
                    <p><strong>
                            <lightning:formattedNumber value="{!v.quotation.Actual_Total_Product_Amount__c}"
                                style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" />
                        </strong></p>
                </div>
            </div>
        </div>

        <aura:if isTrue="{!v.showEditModal}">
            <div style="">
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true"
                    aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container" style="height:auto !important; transform: translate(0%, 50%);">
                        <div class="modal-header slds-modal__header">
                            <button
                                class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse"
                                title="Close" onclick="{!c.closeModal}">
                                <lightning:icon iconName="utility:close" alternativeText="close!" variant="close"
                                    class="modal_close" />
                                <span class="slds-assistive-text">Close</span>
                            </button>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">Confirm Authorized
                                Brand Change</h2>
                        </div>
                        <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                            <p style="padding: 10px;">
                                {!v.modalContent}
                            </p>
                        </div>
                        <footer class="slds-modal__footer">
                            <button class="slds-button slds-button_neutral" onclick="{!c.closeModal}">Cancel</button>
                            <button class="slds-button slds-button_brand" onclick="{!c.confirmChange}">Confirm</button>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </aura:if>

        <aura:if isTrue="{!v.showThresholdProduct}">
            <div>
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true"
                    aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container" style="height:450px; transform: translate(0%, 50%);">
                        <div class="modal-header slds-modal__header">
                            <button
                                class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse"
                                title="Close" onclick="{!c.closeThresholdProduct}">
                                <lightning:icon iconName="utility:close" alternativeText="close!" variant="close"
                                    class="modal_close" />
                                <span class="slds-assistive-text">Close</span>
                            </button>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">Mix &#38; Match
                                Product</h2>
                        </div>
                        <div class="slds-modal__content" id="modal-content-id-1" style="overflow:scroll;">

                            <lightning:accordion aura:id="thresholdAccordion" activeSectionName="A"
                                allowMultipleSectionsOpen="true">
                                <aura:iteration items="{!v.thresholdOpts}" var="tOpt" indexVar="index">
                                    <lightning:accordionSection name="{!(index==0?'A':'B')}"
                                        label="{!'Mix &#38; Match List &nbsp;' + (index+1)}">
                                        <div class="slds-grid">
                                            <div class="slds-col slds-size_2-of-3">
                                                <aura:if
                                                    isTrue="{! tOpt.threshold.Minimum_Different_Tool_Models__c > 0 }">
                                                    <p style="padding: 10px;">
                                                        {!'Please select' +
                                                        (tOpt.threshold.Minimum_Different_Tool_Models__c > 0 ? ' at
                                                        least ' + tOpt.threshold.Minimum_Different_Tool_Models__c : '')+
                                                        ' different models'}
                                                    </p>
                                                    <aura:set attribute="else">
                                                        <p style="padding: 10px;">Please select model(s) </p>
                                                    </aura:set>
                                                </aura:if>
                                            </div>
                                            <div class="slds-col slds-size_1-of-3">
                                                <lightning:input label="" name="{!index}" type="search"
                                                    placeholder="Enter Key Words" onchange="{!c.searchMixMatchProducts}"
                                                    style="font-size: small;" />
                                            </div>
                                        </div>
                                        <div
                                            class="slds-border_top slds-border_right slds-border_left slds-border_bottom">
                                            <ul>
                                                <aura:iteration items="{!tOpt.products}" var="option">
                                                    <li class="{!'slds-dropdown__item ' + (option.selected ? 'slds-is-selected' : '')}"
                                                        style="{!(option.hidden ? 'display: none;' : '')}"
                                                        role="presentation" onclick="{!c.handleThresholdSelection}"
                                                        data-value="{!option.Product__r.SF_Description__c}"
                                                        data-selected="{!option.selected}"
                                                        data-name="{!tOpt.threshold.Name}">
                                                        <a href="javascript:void(0);" role="menuitemcheckbox"
                                                            aria-checked="true" tabindex="0">
                                                            <span
                                                                class="{!'slds-truncate ' + (option.selected ? 'productSelected' : 'productUnselected')}">
                                                                {!option.Product__r.ProductCode + ' ' +
                                                                option.Product__r.SF_Description__c}
                                                            </span>
                                                        </a>
                                                    </li>
                                                </aura:iteration>
                                            </ul>
                                        </div>
                                    </lightning:accordionSection>
                                </aura:iteration>
                            </lightning:accordion>

                        </div>
                        <footer class="slds-modal__footer">
                            <button class="slds-button slds-button_neutral"
                                onclick="{!c.closeThresholdProduct}">Cancel</button>
                            <aura:if isTrue="{!v.isPoolFreeGoods}">
                                <button class="slds-button slds-button_brand" onclick="{!c.finishThresholdProduct}">Save
                                    &#38; Choose Free Goods</button>
                                <aura:set attribute="else">
                                    <button class="slds-button slds-button_brand"
                                        onclick="{!c.finishThresholdProduct}">Finish</button>
                                </aura:set>
                            </aura:if>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </aura:if>

        <aura:if isTrue="{!v.showOfferingProduct}">
            <div>
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true"
                    aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container" style="height: 450px !important; transform: translate(0%, 50%);">
                        <div class="modal-header slds-modal__header">
                            <button
                                class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse"
                                title="Close" onclick="{!c.closeOfferingProduct}">
                                <lightning:icon iconName="utility:close" alternativeText="close!" variant="close"
                                    class="modal_close" />
                                <span class="slds-assistive-text">Close</span>
                            </button>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">Free Goods List
                            </h2>
                        </div>
                        <div class="slds-modal__content" id="modal-content-id-1" style="overflow:scroll;">
                            <lightning:accordion aura:id="offeringAccordion" activeSectionName="A"
                                allowMultipleSectionsOpen="true">
                                <aura:iteration items="{!v.offeringOpts}" var="oOpt" indexVar="index">
                                    <lightning:accordionSection name="{!(index==0?'A':'B')}"
                                        label="{!'Free Goods List' + (index+1)}">
                                        <div class="slds-grid">
                                            <div class="slds-col slds-size_2-of-3">
                                                <p style="padding: 10px;">
                                                    Please select free goods
                                                </p>
                                            </div>
                                            <div class="slds-col slds-size_1-of-3">
                                                <lightning:input label="" type="search" name="{!index}"
                                                    placeholder="Enter Key Words" onchange="{!c.searchPoolFreeGoods}"
                                                    style="font-size: small;" />
                                            </div>
                                        </div>
                                        <div
                                            class="slds-border_top slds-border_right slds-border_left slds-border_bottom">
                                            <ul>
                                                <aura:iteration items="{!oOpt.products}" var="option">
                                                    <li class="{!'slds-dropdown__item ' + (option.selected ? 'slds-is-selected' : '')}"
                                                        style="{!(option.hidden ? 'display: none;' : '')}"
                                                        role="presentation" onclick="{!c.handleOfferingSelection}"
                                                        data-value="{!option.Product__r.SF_Description__c}"
                                                        data-selected="{!option.selected}"
                                                        data-name="{!oOpt.offering.Name}">
                                                        <a href="javascript:void(0);" role="menuitemcheckbox"
                                                            aria-checked="true" tabindex="0">
                                                            <span
                                                                class="{!'slds-truncate ' + (option.selected ? 'productSelected' : 'productUnselected')}">
                                                                {!option.Product__r.ProductCode + ' ' +
                                                                option.Product__r.SF_Description__c}
                                                            </span>
                                                        </a>
                                                    </li>
                                                </aura:iteration>
                                            </ul>
                                        </div>
                                    </lightning:accordionSection>
                                </aura:iteration>
                            </lightning:accordion>
                        </div>
                        <footer class="slds-modal__footer">
                            <button class="slds-button slds-button_neutral"
                                onclick="{!c.closeOfferingProduct}">Cancel</button>
                            <button class="slds-button slds-button_brand"
                                onclick="{!c.finishOfferingProduct}">Finish</button>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </aura:if>

        <aura:if isTrue="{!v.showAvailablePromotions}">
            <div>
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true"
                    aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container" style="height:500px; transform: translate(0%, 50%);">
                        <div class="modal-header slds-modal__header">
                            <button
                                class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse"
                                title="Close" onclick="{!c.backToEditOrder}">
                                <lightning:icon iconName="utility:close" alternativeText="close!" variant="close"
                                    class="modal_close" />
                                <span class="slds-assistive-text">Close</span>
                            </button>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">Available
                                Promotions</h2>
                        </div>
                        <div class="slds-modal__content" id="modal-content-id-1" style="overflow:scroll;">
                            <p style="padding: 10px;">
                                Available promotions for the products in your order. Do you want to edit your order and
                                apply promotion?
                            </p>
                            <div class="slds-border_top slds-border_right slds-border_left slds-border_bottom">
                                <aura:iteration items="{!v.notApplyPromoList}" var="oItem">
                                    <h4 style="margin-left: 10px;margin-top:20px;margin-bottom:10px">{!oItem.name}</h4>
                                    <ul style="list-style-type:disc; margin-left: 30px;">
                                        <aura:iteration items="{!oItem.promoList}" var="pItem">
                                            <li style="margin-top:10px;margin-bottom:10px">
                                                <p>{!pItem.promotion.Name} - Promo Code: <span
                                                        style="color:blue;">{!pItem.promotion.Promotion_Code_For_External__c}</span>
                                                </p>
                                            </li>
                                        </aura:iteration>
                                    </ul>
                                </aura:iteration>
                            </div>
                        </div>
                        <footer class="slds-modal__footer">
                            <button class="slds-button slds-button_neutral"
                                onclick="{!c.nextToPromoReminder}">Next</button>
                            <button class="slds-button slds-button_brand" onclick="{!c.backToEditOrder}">Back to Edit
                                Order</button>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </aura:if>

        <aura:if isTrue="{!v.showPromotionReminder}">
            <div>
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true"
                    aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container"
                        style="height:auto !important; transform: translate(0%, 30%); max-height:500px">
                        <div class="modal-header slds-modal__header">
                            <button
                                class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse"
                                title="Close" onclick="{!c.backToEditOrder}">
                                <lightning:icon iconName="utility:close" alternativeText="close!" variant="close"
                                    class="modal_close" />
                                <span class="slds-assistive-text">Close</span>
                            </button>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">Promotion Reminder
                            </h2>
                        </div>
                        <div class="slds-modal__content" id="modal-content-id-1" style="overflow:scroll;">
                            <p style="padding: 10px;">
                                Do you want to add to your order and increase your promotional offer?
                            </p>
                            <div class="slds-border_top slds-border_right slds-border_left slds-border_bottom">
                                <aura:iteration items="{!v.reminderPromoList}" var="rItem">
                                    <ul style="list-style-type:disc; margin-left: 30px;">
                                        <li style="margin-top:10px;margin-bottom:10px">
                                            <p>{!rItem.name}: {!rItem.thresholdDesc + rItem.offeringDesc }</p>
                                        </li>
                                    </ul>
                                </aura:iteration>
                            </div>
                        </div>
                        <footer class="slds-modal__footer">
                            <button class="slds-button slds-button_neutral" onclick="{!c.nextToFinal}">Next</button>
                            <button class="slds-button slds-button_brand" onclick="{!c.backToEditOrder}">Back to Edit
                                Order</button>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </aura:if>

        <aura:if isTrue="{!v.showWholeOrderOfferingProduct}">
            <div>
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true"
                    aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container" style="height:auto !important; transform: translate(0%, 50%);">
                        <div class="modal-header slds-modal__header">
                            <button
                                class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse"
                                title="Close" onclick="{!c.closeOfferingProduct}">
                                <lightning:icon iconName="utility:close" alternativeText="close!" variant="close"
                                    class="modal_close" />
                                <span class="slds-assistive-text">Close</span>
                            </button>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">Free Goods List
                            </h2>
                        </div>
                        <div class="slds-modal__content" id="modal-content-id-1" style="overflow:scroll;">
                            <lightning:accordion aura:id="offeringAccordion" activeSectionName="A"
                                allowMultipleSectionsOpen="true">
                                <aura:iteration items="{!v.offeringOpts}" var="oOpt" indexVar="index">
                                    <lightning:accordionSection name="{!(index==0?'A':'B')}"
                                        label="{!'Free Goods List' + (index+1)}">
                                        <div class="slds-grid">
                                            <div class="slds-col slds-size_2-of-3">
                                                <p style="padding: 10px;">
                                                    Please select free goods
                                                </p>
                                            </div>
                                            <div class="slds-col slds-size_1-of-3">
                                                <lightning:input label="" type="search" name="{!index}"
                                                    placeholder="Enter Key Words" onchange="{!c.searchPoolFreeGoods}"
                                                    style="font-size: small;" />
                                            </div>
                                        </div>
                                        <div
                                            class="slds-border_top slds-border_right slds-border_left slds-border_bottom">
                                            <ul>
                                                <aura:iteration items="{!oOpt.products}" var="option">
                                                    <li class="{!'slds-dropdown__item ' + (option.selected ? 'slds-is-selected' : '')}"
                                                        style="{!(option.hidden ? 'display: none;' : '')}"
                                                        role="presentation" onclick="{!c.handleOfferingSelection}"
                                                        data-value="{!option.Product__r.SF_Description__c}"
                                                        data-selected="{!option.selected}"
                                                        data-name="{!oOpt.offering.Name}">
                                                        <a href="javascript:void(0);" role="menuitemcheckbox"
                                                            aria-checked="true" tabindex="0">
                                                            <span
                                                                class="{!'slds-truncate ' + (option.selected ? 'productSelected' : 'productUnselected')}">
                                                                {!option.Product__r.ProductCode + ' ' +
                                                                option.Product__r.SF_Description__c}
                                                            </span>
                                                        </a>
                                                    </li>
                                                </aura:iteration>
                                            </ul>
                                        </div>
                                    </lightning:accordionSection>
                                </aura:iteration>
                            </lightning:accordion>
                        </div>
                        <footer class="slds-modal__footer">
                            <button class="slds-button slds-button_neutral"
                                onclick="{!c.closeWholeOrderOfferingProduct}">Cancel</button>
                            <button class="slds-button slds-button_brand"
                                onclick="{!c.finishWholeOrderOfferingProduct}">Finish</button>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </aura:if>

        <aura:set attribute="footer">
            <div>
                <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" label="Previous"
                    title="Previous" onclick="{!c.previousStep}" />
                <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" label="Save" title="Save"
                    onclick="{!c.doSave}" />
                <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="Next" title="Next"
                    onclick="{!c.nextToAvailablePromo}" />
            </div>
        </aura:set>
    </lightning:card>

    <aura:if isTrue="{!v.showUpload}">
        <p style="margin: 10px 0 10px 0; font-size: 16px; font-weight:bold">Add Products From File</p>
        <div>
            <c:ccmPurchaseOrderUpload aura:id="uploadCmp" orderType="order" customerId="{!v.customerId}"
                productFilter="{!v.prodCondition}" orderTypeVal="{!v.orderTypeVal}"
                onuploadfinish="{!c.handleUploadFinish}" disabled="{!v.uploadDisabed}"></c:ccmPurchaseOrderUpload>
        </div>
    </aura:if>
</aura:component>