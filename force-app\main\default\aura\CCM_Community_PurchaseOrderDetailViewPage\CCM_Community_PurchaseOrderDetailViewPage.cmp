<aura:component description="CCM_Community_PurchaseOrderDetailViewPage"
                implements="forceCommunity:availableForAllPageTypes,force:hasRecordId,flexipage:availableForAllPageTypes" access="global"
                controller="CCM_Community_OrderApplicationDetailCtl">
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="currencySymbol" type="String" default="$"/>
    <aura:attribute name="brandScope" type="String" default=""/>
    <aura:attribute name="brandScopeOpt" type="List" default="" access="public"/>
    <aura:attribute name="customerId" type="String" default=""/>
    <aura:attribute name="quotation" type="Object" default=""/>
    <aura:attribute name="orderItemList" type="List" default="[]"/>
    <aura:attribute name="activeSections" type="List" default="" />
    <aura:attribute name="processData" type="List" default="[]"/>
    <aura:attribute name="requireFlag" type="Boolean" default="false"/>
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <aura:attribute name="currentStep" type="Integer" default="1"/>
    <aura:attribute name="isRequired" type="Boolean" default="false"/>
    <aura:attribute name="isDisabled" type="Boolean" default="false"/>
    <aura:attribute name="isDelegate" type="Boolean" default="false"/>
    <aura:attribute name="approvalComments" type="String" default=""/>
    <aura:attribute name="approvalOpt" type="List" default=""/>
    <aura:attribute name="approvalVal" type="String" default=""/>
    <aura:attribute name="isApprovalMode" type="Boolean" default="false"/>
    <aura:attribute name="paymentTermVal" type="String" default=""/>
    <aura:attribute name="freightTermVal" type="String" default=""/>

    <!-- ORG Code: CCA -->
    <aura:attribute name="isCCA" type="Boolean" default="false"/>
    <aura:attribute name="showTax" type="Boolean" default="true" />
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <div class="slds-grid slds-grid_align-space">
        <div class="slds-grid slds-grid--align-center slds-size--1-of-1">
            <div class="slds-col slds-size_1-of-1 slds-wrap">
                <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
                <c:CCM_PathProcess processData="{!v.processData}" currentStep="{!v.currentStep}"/>
                <div class="slds-p-top_medium">
                    <c:CCM_Section title="{!$Label.c.CCM_Portal_BasicInformation}" expandable="true">
                        <lightning:layout multipleRows="true">
                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="{!$Label.c.CCM_Portal_CustomerPONumber}">
                                    {!v.quotation.customerPONum}
                                </c:CCM_Field>
                            </lightning:layoutItem>

                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="{!$Label.c.CCM_Portal_CustomerName}">
                                    {!v.quotation.customeName}
                                </c:CCM_Field>
                            </lightning:layoutItem>

                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="{!$Label.c.CCM_Portal_OrderType}">
                                    {!v.quotation.orderType}
                                </c:CCM_Field>
                            </lightning:layoutItem>

                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="{!$Label.c.CCM_Portal_Brand}">
                                    {!v.quotation.brandScopeName}
                                </c:CCM_Field>
                            </lightning:layoutItem>

                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="{!$Label.c.CCM_Portal_OrderStatus}">
                                    {!v.quotation.orderStatus}
                                </c:CCM_Field>
                            </lightning:layoutItem>

                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="{!$Label.c.CCM_Portal_PaymentTerm}">
                                    {!v.paymentTermVal}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="{!$Label.c.CCM_Portal_CreatedBy}">
                                    {!v.quotation.createdBy}
                                </c:CCM_Field>
                            </lightning:layoutItem>

                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="{!$Label.c.CCM_Portal_FreightTerm}">
                                    {!v.freightTermVal}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="{!$Label.c.CCM_Portal_CreatedDate}">
                                    {!v.quotation.createdDate}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="{!$Label.c.CCM_Portal_SubmitDate}">
                                    {!v.quotation.submitDate}
                                </c:CCM_Field>
                            </lightning:layoutItem>

                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="{!$Label.c.CCM_Portal_IsDropShipOrder}">
                                    {!v.quotation.isDropShipOrder}
                                </c:CCM_Field>
                            </lightning:layoutItem>

                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="{!$Label.c.CCM_Portal_IsDelegatedOrder}">
                                    {!v.quotation.isDelegatedOrder}
                                </c:CCM_Field>
                            </lightning:layoutItem>

                            <aura:if isTrue="{!v.quotation.wholeOrderPromoCode}">
                                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                    <c:CCM_Field label="{!$Label.c.CCM_Portal_WholeOrderPromotion}">
                                        {!v.quotation.wholeOrderPromoCode}
                                    </c:CCM_Field>
                                </lightning:layoutItem>
                            </aura:if>

                            <aura:if isTrue="{!v.quotation.paymentTermPromoCode}">
                                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                    <c:CCM_Field label="{!$Label.c.CCM_Portal_PaymentTermPromotion}">
                                        {!v.quotation.paymentTermPromoCode}
                                    </c:CCM_Field>
                                </lightning:layoutItem>
                            </aura:if>
                        </lightning:layout>
                    </c:CCM_Section>

                    <c:CCM_Section title="{!$Label.c.CCM_Portal_DeliveryInformation}" expandable="true" >
                        <lightning:layout multipleRows="true">
                            <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <p><strong>{!$Label.c.CCM_Portal_BillingTo}</strong></p>
                                    <p>
                                        <aura:if isTrue="{!and(v.quotation.billingAddressId != null)}">
                                            <lightning:formattedAddress
                                                street="{!(v.quotation.billingAddress1 + v.quotation.billingAddress2)}"
                                                city="{!v.quotation.billingAddressCity}"
                                                country="{!v.quotation.billingAddressCountry}"
                                                province="{!v.quotation.billingAddressState}"
                                                postalCode="{!v.quotation.billingAddressPostalCode}"/>
                                            <ul>
                                                <li> {!v.quotation.billingAddressContactName}</li>
                                            </ul>
                                        </aura:if>
                                    </p>
                                </div>
                            </lightning:layoutItem>

                            <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <aura:if isTrue="{!v.isCCA}">
                                        <p><strong>Chervon Canada, Inc.</strong></p>
                                        <lightning:formattedAddress
                                            street="1-3480 Laird Rd."
                                            city="Mississauga"
                                            country="Canada"
                                            province="ON"
                                            postalCode="L5L 5Y4"/>
                                        <p>{!v.quotation.salesManagerName}</p>
                                        <aura:set attribute="else">
                                            <p><strong>Chervon North American</strong></p>
                                            <lightning:formattedAddress
                                                street="1203 East Warrenville Road"
                                                city="Naperville"
                                                country="US"
                                                province="IL"
                                                postalCode="60563"/>
                                            <p>{!v.quotation.salesManagerName}</p>
                                        </aura:set>
                                    </aura:if>
                                </div>
                            </lightning:layoutItem>

                            <lightning:layoutItem padding="around-small" size="6">
                                <p><strong>{!$Label.c.CCM_Portal_ShippingTo}</strong></p>
                                <p>
                                    <aura:if isTrue="{!and(v.quotation.shippingAddressId != null)}">
                                        <lightning:formattedAddress
                                            street="{!(v.quotation.shippingAddress1 + v.quotation.shippingAddress2)}"
                                            city="{!v.quotation.shippingAddressCity}"
                                            country="{!v.quotation.shippingAddressCountry}"
                                            province="{!v.quotation.shippingAddressState}"
                                            postalCode="{!v.quotation.shippingAddressPostalCode}"/>
                                        <ul>
                                            <li> {!v.quotation.shippingAddressContactName}</li>
                                        </ul>
                                        <aura:set attribute="else">
                                            <lightning:formattedAddress
                                                street="{!v.quotation.additionalShipAddressStreet}"
                                                city="{!v.quotation.additionalShipAddressCity}"
                                                country="{!v.quotation.additionalShipAddressCountry}"
                                                province="{!v.quotation.additionalShipAddressProvince}"
                                                postalCode="{!v.quotation.additionalShipAddressPostCode}"
                                            />
                                            <ul>
                                                <li> {!v.quotation.additionalContactName}</li>
                                                <li>{!v.quotation.additionalContactPhone}</li>
                                                <li>{!v.quotation.additionalContactEmail}</li>
                                            </ul>
                                        </aura:set>
                                    </aura:if>
                                </p>
                            </lightning:layoutItem>
                            <lightning:layoutItem padding="around-small" size="6">
                                <div>
                                    <p><strong>{!$Label.c.CCM_Portal_ShippingBy}</strong></p>
                                    <p>{!v.quotation.shippingByLabel}</p>
                                </div>
                                <aura:if isTrue="{!v.quotation.shippingBy != 'Chervon'}">
                                    <div>
                                        <p><strong>{!$Label.c.CCM_Portal_PreferCarrier}</strong></p>
                                        <p>{!v.quotation.deliverySupplier}</p>
                                    </div>
                                    <div>
                                        <p><strong>{!$Label.c.CCM_Portal_YourCustomerFreightAccount}</strong></p>
                                        <p>{!v.quotation.freightAccount}</p>
                                    </div>
                                <aura:set attribute="else">
                                    <div>
                                        <p><strong>{!$Label.c.CCM_Portal_ShippingMethod}</strong></p>
                                        <p>{!v.quotation.shippingMethod}</p>
                                    </div>
                                </aura:set>
                                </aura:if>
                            </lightning:layoutItem>

                            <!-- <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <p><strong>Expected Delivery Date</strong></p>
                                    <p>{!v.quotation.expectedDeliveryDate}</p>
                                </div>
                            </lightning:layoutItem> -->

                            <!-- <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <p><strong>Promotion Code</strong></p>
                                    <p>{!v.quotation.promotionCode}</p>
                                </div>
                            </lightning:layoutItem> -->

                            <lightning:layoutItem padding="around-small" size="6">
                                <div class="header-column">
                                    <p><strong>{!$Label.c.CCM_Portal_BuyerEmail}</strong></p>
                                    <p>{!v.quotation.buyerEmail}</p>
                                </div>
                            </lightning:layoutItem>

                            <lightning:layoutItem padding="around-small" size="12">
                                <p><strong>{!$Label.c.CCM_Portal_ShippingandRoutingInstruction}</strong></p>
                                <p>{!v.quotation.notes}</p>
                            </lightning:layoutItem>
                        </lightning:layout>
                    </c:CCM_Section>

                    <c:CCM_Section title="{!$Label.c.CCM_Portal_OrderItemInformation}" expandable="true" >
                        <table class="slds-p-horizontal_medium slds-m-bottom_medium"></table>
                        <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_fixed-layout slds-table_resizable-cols slds-table_striped productTable" role="grid">
                            <thead>
                                <tr class="slds-line-height_reset">
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Line}">{!$Label.c.CCM_Portal_Line}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth slds-size--1-of-4" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_ProductDescription}">{!$Label.c.CCM_Portal_ProductDescription}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_ModelNum}">{!$Label.c.CCM_Portal_ModelNum}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Brand}">{!$Label.c.CCM_Portal_Brand}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumLWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="Ship Date">Ship Date</span>
                                            </div>
                                        </a>
                                    </th> -->
                                    <!-- start calvin -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_QtyEA}">{!$Label.c.CCM_Portal_QtyEA}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- end -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Unit}">{!$Label.c.CCM_Portal_Unit}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- calvin start -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width:85px;">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_CaseQty}">{!$Label.c.CCM_Portal_CaseQty}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- end -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_EverydayPrice}">{!$Label.c.CCM_Portal_EverydayPrice}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_InvoicePrice}">{!$Label.c.CCM_Portal_InvoicePrice}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Subtotal}">{!$Label.c.CCM_Portal_Subtotal}</span>
                                            </div>
                                        </a>
                                    </th>

                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_PromoCode}">{!$Label.c.CCM_Portal_PromoCode}</span>
                                            </div>
                                        </a>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <aura:iteration items="{!v.orderItemList}" var="orderItem" indexVar="index">
                                    <tr aria-selected="false" class="slds-hint-parent" id="{!row_index}" onclick="{!c.rowFocus}">
                                        <th scope="row">
                                            <div class="slds-truncate" title="">
                                                {!index + 1}
                                            </div>
                                        </th>
                                        <td role="gridcell" title="{!orderItem.productName}">
                                            <div class="slds-truncate clear-user-agent-styles" >{!orderItem.productName}</div>
                                        </td>

                                        <td role="gridcell" title="{!orderItem.productCode}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.productCode}</span>
                                            </div>
                                        </td>

                                        <td role="gridcell" title="{!orderItem.brandName}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.brandName}</span>
                                            </div>
                                        </td>

                                        <!-- <td role="gridcell" title="{!orderItem.shipDate}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.shipDate}</span>
                                            </div>
                                        </td> -->

                                        <td role="gridcell" title="{!orderItem.quantity}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.quantity}</span>
                                            </div>
                                        </td>

                                        <td role="gridcell" title="EA">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>EA</span>
                                            </div>
                                        </td>

                                        <!-- calvin start -->
                                        <td role="gridcell" title="EA">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.caseQty}</span>
                                            </div>
                                        </td>
                                        <!-- end -->

                                        <td role="gridcell" title="{!orderItem.listPrice}">
                                            <div class="slds-truncate">
                                                <lightning:formattedNumber value="{!orderItem.listPrice}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/>
                                            </div>
                                        </td>

                                        <td role="gridcell" title="{!orderItem.unitPrice}">
                                            <div class="slds-truncate">
                                                <lightning:formattedNumber value="{!orderItem.unitPrice}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/>
                                            </div>
                                        </td>

                                        <td role="gridcell" title="{!orderItem.subTotal}">
                                            <div class="slds-truncate">
                                                <lightning:formattedNumber value="{!orderItem.subTotal}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/>
                                            </div>
                                        </td>

                                        <td role="gridcell" title="{!orderItem.promotionCode}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.promotionCode}</span>
                                            </div>
                                        </td>
                                    </tr>
                                </aura:iteration>
                                <tr aria-selected="false" class="slds-hint-parent" id="{!row_index}" >
                                    <aura:if isTrue="{!v.isCanReverse}">
                                        <th class="slds-p-right_x-small totalCon" scope="row" colspan="12" align="right">
                                            <div class="slds-grid slds-float--right">
                                                <div class="slds-text-align--right">
                                                    <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_TotalQuantity}:&nbsp;</div>
                                                    <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_ProductAmount}:&nbsp;</div>
                                                    <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_TotalSavings}:&nbsp;</div>
                                                    <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_FreightFee}:&nbsp;</div>
                                                    <!-- <aura:if isTrue="{!!v.isCCA}"> -->
                                                        <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_FreightFeeToBeWaived}:&nbsp;</div>
                                                    <!-- </aura:if>                                                     -->
                                                    <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_ShippingDiscount}:&nbsp;</div>
                                                    <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_HandlingFee}:&nbsp;</div>
                                                    <div class="slds-border_bottom ccm_paddingTop" />
                                                    <div class="slds-truncate ccm_padding ccm_paddingTop" title=""><strong>{!$Label.c.CCM_Portal_TotalAmount}:&nbsp;</strong></div>
                                                </div>
                                                <div>
                                                    <div class="slds-truncate" title=""><strong>{!v.quotation.totalQuantity}</strong></div>

                                                    <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.productPrice}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>

                                                    <div class="slds-truncate" title=""><strong class="ccm_fontColor2"><lightning:formattedNumber value="{!v.quotation.discountAmt}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>

                                                    <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.freightFee}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>

                                                    <!-- <aura:if isTrue="{!!v.isCCA}"> -->
                                                        <div class="slds-truncate " title=""><strong class="ccm_fontColor2">-<lightning:formattedNumber value="{!v.quotation.freightFeeWaived}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                                    <!-- </aura:if>                                                     -->

                                                    <div class="slds-truncate" title=""><strong class="ccm_fontColor2">-<lightning:formattedNumber value="{!v.quotation.extraFreightFeeToBeWaived}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>

                                                    <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.handingFee}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/> </strong></div>

                                                    <div class="slds-border_bottom ccm_paddingTop" />
                                                    <div class="slds-truncate ccm_paddingTop" title=""><strong><lightning:formattedNumber value="{!v.quotation.totalAmount}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                                </div>
                                            </div>
                                        </th>
                                        <aura:set attribute="else">
                                            <th class="slds-p-right_x-small totalCon" scope="row" colspan="11" align="right">
                                                <div class="slds-grid slds-float--right">
                                                    <div class="slds-text-align--right">
                                                        <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_TotalQuantity}:&nbsp;</div>
                                                        <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_ProductAmount}:&nbsp;</div>
                                                        <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_TotalSavings}:&nbsp;</div>
                                                        <aura:if isTrue="{!v.quotation.surchargeAmt > 0}">
                                                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_Surcharge}:&nbsp;</div>
                                                        </aura:if>
                                                        <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_FreightFee}:&nbsp;</div>
                                                        <!-- <aura:if isTrue="{!!v.isCCA}"> -->
                                                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_FreightFeeToBeWaived}:&nbsp;</div>
                                                        <!-- </aura:if>                                                         -->
                                                        <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_ShippingDiscount}:&nbsp;</div>
                                                        <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_HandlingFee}:&nbsp;</div>
                                                        <div class="slds-border_bottom ccm_paddingTop" />
                                                <aura:if isTrue="{!and(v.isCCA, v.showTax)}">
                                                    <div class="slds-truncate ccm_padding" title="">QST{!$Label.c.CCM_Portal_QST}:</div>
                                                    <div class="slds-truncate ccm_padding" title="">GST{!$Label.c.CCM_Portal_GST}:</div>
                                                    <div class="slds-truncate ccm_padding" title="">HST{!$Label.c.CCM_Portal_HST}:</div>
                                                </aura:if>
                                                <div class="slds-border_bottom ccm_paddingTop" />
                                                        <div class="slds-truncate ccm_padding ccm_paddingTop" title=""><strong>{!$Label.c.CCM_Portal_TotalAmount}:&nbsp;</strong></div>
                                                    </div>
                                                    <div>
                                                        <div class="slds-truncate" title=""><strong>{!v.quotation.totalQuantity}</strong></div>

                                                        <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.productPrice}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>

                                                        <div class="slds-truncate" title=""><strong class="ccm_fontColor2"><lightning:formattedNumber value="{!v.quotation.discountAmt}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>

                                                        <aura:if isTrue="{!v.quotation.surchargeAmt > 0}">
                                                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.surchargeAmt}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                                        </aura:if>

                                                        <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.freightFee}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>

                                                        <!-- <aura:if isTrue="{!!v.isCCA}"> -->
                                                            <div class="slds-truncate " title=""><strong class="ccm_fontColor2">-<lightning:formattedNumber value="{!v.quotation.freightFeeWaived}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                                        <!-- </aura:if>                                                         -->

                                                        <div class="slds-truncate" title=""><strong class="ccm_fontColor2">-<lightning:formattedNumber value="{!v.quotation.extraFreightFeeToBeWaived}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>

                                                        <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.handingFee}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/> </strong></div>

                                                <aura:if isTrue="{!and(v.isCCA, v.showTax)}">
                                                    <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.QST}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                                    <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.GST}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                                    <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.quotation.HST}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                                </aura:if>
                                                        <div class="slds-border_bottom ccm_paddingTop" />
                                                        <div class="slds-truncate ccm_paddingTop" title=""><strong><lightning:formattedNumber value="{!v.quotation.totalAmount}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                                    </div>
                                                </div>
                                            </th>
                                        </aura:set>
                                    </aura:if>
                                </tr>
                            </tbody>
                        </table>
                    </c:CCM_Section>

                    <div class="CCM_PaddingTop slds-m-bottom_medium">
                        <lightning:button class="slds-p-horizontal_x-large" label="{!$Label.c.CCM_Portal_Back}" title="{!$Label.c.CCM_Portal_Back}" onclick="{!c.doBack}"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</aura:component>