/**
 * <AUTHOR>
 * @date 2024-05-22
 * @description Trigger approval process when owner update
 */
public with sharing class CCM_OwnerUpdateApprovalProcess {
    
    public static void triggerApprovalProcess(List<SObject> records, String objType) {
        Set<String> salesGroupScope = new Set<String>{'SG23', 'SG24', 'SG25', 'SG26', 'SG30', 'SG31', 'SG32', 'SG43', 'SG44', 'SG45', 'SG46', 'SG47', 'SG48', 'SG49', 'SG50', 'SG51'};
        
        if(objType == 'Account') {
            List<Account> customers = new List<Account>();
            List<Account> storeLocations = new List<Account>();
            for(SObject record : records) {
                Account acc = (Account)record;
                if(acc.RecordTypeId == CCM_Constants.CUSTOMER_RECORD_TYPE_CHANNEL_ID) {
                    customers.add(acc);
                }
                else if(acc.RecordTypeId == CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID
                || acc.RecordTypeId == CCM_Constants.CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID) {
                    storeLocations.add(acc);
                }
            }

            if(!customers.isEmpty()) {
                for(Account customer : customers) {
                    if(salesGroupScope.contains(customer.Sales_Group__c)) {
                        customer.Update_Owner_Approval__c = true;

                        // Approval.ProcessSubmitRequest request = new Approval.ProcessSubmitRequest();
                        // request.setObjectId(customer.Id);
                        // request.setSubmitterId(UserInfo.getUserId());

                        // Approval.ProcessResult result;
                        // result = Approval.process(request);
                    }
                }
            }

            if(!storeLocations.isEmpty()) {
                Map<String, List<Account>> customerStoreLocationMap = new Map<String, List<Account>>();
                Map<String, List<Account>> prospectStoreLoationMap = new Map<String, List<Account>>();
                for(Account storeLocation : storeLocations) {
                    if(String.isNotBlank(storeLocation.Related_Entity__c)) {
                        if(!customerStoreLocationMap.containsKey(storeLocation.Related_Entity__c)) {
                            customerStoreLocationMap.put(storeLocation.Related_Entity__c, new List<Account>());
                        }
                        customerStoreLocationMap.get(storeLocation.Related_Entity__c).add(storeLocation);
                    }
                    else if(String.isNotBlank(storeLocation.Related_Entity_Prospect__c)) {
                        if(!prospectStoreLoationMap.containsKey(storeLocation.Related_Entity_Prospect__c)) {
                            prospectStoreLoationMap.put(storeLocation.Related_Entity_Prospect__c, new List<Account>());
                        }
                        prospectStoreLoationMap.get(storeLocation.Related_Entity_Prospect__c).add(storeLocation);
                    }
                }

                if(!customerStoreLocationMap.keySet().isEmpty()) {
                    for(Account acc : [SELECT Sales_Group__c FROM Account WHERE Id IN :customerStoreLocationMap.keySet()]) {
                        if(salesGroupScope.contains(acc.Sales_Group__c)) {
                            for(Account storeLocation : customerStoreLocationMap.get(acc.Id)) {
                                storeLocation.Update_Owner_Approval__c = true;

                                // Approval.ProcessSubmitRequest request = new Approval.ProcessSubmitRequest();
                                // request.setObjectId(storeLocation.Id);
                                // request.setSubmitterId(UserInfo.getUserId());

                                // Approval.ProcessResult result;
                                // result = Approval.process(request);
                            }
                        }
                    }
                }
                
                if(!prospectStoreLoationMap.keySet().isEmpty()) {
                    for(Lead l : [SELECT Sales_Group__c FROM Lead WHERE Id IN :prospectStoreLoationMap.keySet()]) {
                        if(salesGroupScope.contains(l.Sales_Group__c)) {
                            for(Account storeLocation : prospectStoreLoationMap.get(l.Id)) {
                                storeLocation.Update_Owner_Approval__c = true;
                            }
                        }
                    }
                }
            }
        }
        else if(objType == 'Lead') {
            for(SObject record : records) {
                Lead l = (Lead)record;
                if(salesGroupScope.contains(l.Sales_Group__c)) {
                    l.Update_Owner_Approval__c = true;
                }
            }
        }
    }
}