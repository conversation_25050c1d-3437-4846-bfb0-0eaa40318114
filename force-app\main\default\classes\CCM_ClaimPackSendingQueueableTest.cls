/**
 * <AUTHOR>
 * @date 2021-11-25
 * @description This is the test class of CCM_ClaimPackSendingQueueable.
 */
@IsTest
public class CCM_ClaimPackSendingQueueableTest {
    @TestSetup
    static void testSetup() {
        Test.startTest();
        Profile objPartnerProfile = [SELECT Id FROM Profile WHERE Name = 'Partner Community Sales' LIMIT 1];
        Account objChannel = new Account(Name = 'test', AccountNumber = 'test', RecordTypeId = CCM_Constants.CUSTOMER_RECORD_TYPE_CHANNEL_ID);
        objChannel.TaxID__c = 'testTax';
        insert objChannel;
        Contact objContact = new Contact(LastName = 'test', AccountId = objChannel.Id, Email = System.now().getTime() + '@test.com');
        insert objContact;
        User objPartner = new User(
            Email = System.now().getTime() + '<EMAIL>',
            ProfileId = objPartnerProfile.Id,
            Username = System.now().getTime() + '<EMAIL>',
            Alias = 'test',
            TimeZoneSidKey = 'America/New_York',
            EmailEncodingKey = 'ISO-8859-1',
            LocaleSidKey = 'en_US',
            LanguageLocaleKey = 'en_US',
            ContactId = objContact.Id,
            FirstName = 'test',
            LastName = 'test'
        );
        insert objPartner;
        Account_Address__c objAddress = new Account_Address__c(Customer__c = objChannel.Id);
        insert objAddress;
        Address_With_Program__c objBAWB = new Address_With_Program__c(Account_Address__c = objAddress.Id);
        insert objBAWB;
        Date datToday = Date.today();
        List<Claim_Pack__c> lstClaimPack = new List<Claim_Pack__c>{
            new Claim_Pack__c(
                Channel_Customer__c = objChannel.Id,
                Bill_To_Address__c = objBAWB.Id,
                Year__c = String.valueOf(datToday.year()),
                Month__c = String.valueOf(datToday.month()),
                Type__c = CCM_Constants.CLAIM_PACK_TYPE_SERVICE_CLAIM
            ),
            new Claim_Pack__c(
                Channel_Customer__c = objChannel.Id,
                Bill_To_Address__c = objBAWB.Id,
                Year__c = String.valueOf(datToday.year()),
                Month__c = String.valueOf(datToday.month()),
                Type__c = CCM_Constants.CLAIM_PACK_TYPE_FLEET_CLAIM
            )
        };
        insert lstClaimPack;
        Case objCase = new Case();
        insert objCase;
        Warranty_Claim__c objWarrantyClaim = new Warranty_Claim__c(
            Case__c = objCase.Id,
            Customer__c = objChannel.Id,
            Status__c = 'Approved',
            Service_Partner__c = objChannel.Id,
            BillTo__c = objBAWB.Id,
            Total__c = 100,
            Last_Approval_Date__c = Date.today(),
            Claim_Pack__c = lstClaimPack[0].Id
        );
        insert objWarrantyClaim;
        Fleet_Claim__c objFleetClaim = new Fleet_Claim__c(
            Approval_Status__c = CCM_Constants.FLEET_CLAIM_APPROVAL_STATUS_APPROVED,
            Approved_Time__c = System.now(),
            Channel_Customer__c = objChannel.Id,
            Bill_To_Address__c = objBAWB.Id,
            Claim_Pack__c = lstClaimPack[1].Id
        );
        insert objFleetClaim;
        Test.stopTest();
    }
    @IsTest
    static void testSuccess() {
        Test.startTest();
        Test.setMock(HttpCalloutMock.class, new SuccessMock());
        Id idJob = System.enqueueJob(
            new CCM_ClaimPackSendingQueueable(
                Date.today(),
                [
                    SELECT
                        Name,
                        Channel_Customer__r.AccountNumber,
                        Bill_To_Address__r.Customer_Line_Oracle_ID__c,
                        Bill_To_Address__r.Account_Address__r.ORG_ID__c,
                        Ship_to_Address__r.Customer_Line_Oracle_ID__c,
                        Amount__c,
                        CurrencyIsoCode,
                        Type__c,Warranty_parts_credit_mark_up__c,
                        (SELECT Name, Total__c, Total_Except_Markup__c,Service_Partner__c, GST__c, HST__c, PST__c, QST__c,Total_without_Tax__c  FROM Warranty_Claims__r),
                        (SELECT Name, Estimated_Credit_Return__c FROM Fleet_Claims__r)
                    FROM Claim_Pack__c
                    WHERE Channel_Customer__c != NULL AND Bill_To_Address__r.Account_Address__c != NULL
                ]
            )
        );
        Test.stopTest();
        System.assertNotEquals(null, idJob);
        System.assertEquals(6, [SELECT COUNT() FROM Log__c]); // 2 for claim pack sending, 2 for updating sub claims and another 2 for inserting logs.
        List<Claim_Pack__c> lstClaimPack = [SELECT Sent_to_ERP__c, Send_Time__c, Sync_Status__c, Error_Message__c FROM Claim_Pack__c LIMIT 10];
        System.assertEquals(2, lstClaimPack.size());
        System.assertEquals(true, lstClaimPack[0].Sent_to_ERP__c);
        System.assertNotEquals(null, lstClaimPack[0].Send_Time__c);
        System.assertEquals('Success', lstClaimPack[0].Sync_Status__c);
        System.assertEquals(null, lstClaimPack[0].Error_Message__c);
        System.assertEquals(true, lstClaimPack[1].Sent_to_ERP__c);
        System.assertNotEquals(null, lstClaimPack[1].Send_Time__c);
        System.assertEquals('Success', lstClaimPack[1].Sync_Status__c);
        System.assertEquals(null, lstClaimPack[1].Error_Message__c);
    }
    @IsTest
    static void testFailure() {
        Test.startTest();
        FailureMock objMock = new FailureMock();
        Test.setMock(HttpCalloutMock.class, objMock);
        Id idJob = System.enqueueJob(
            new CCM_ClaimPackSendingQueueable(
                Date.today(),
                [
                    SELECT
                        Name,
                        Channel_Customer__r.AccountNumber,
                        Bill_To_Address__r.Customer_Line_Oracle_ID__c,
                        Bill_To_Address__r.Account_Address__r.ORG_ID__c,
                        Ship_to_Address__r.Customer_Line_Oracle_ID__c,
                        Amount__c,
                        CurrencyIsoCode,
                        Type__c,Warranty_parts_credit_mark_up__c,
                        (SELECT Name, Total__c, Total_Except_Markup__c,Service_Partner__c, GST__c, HST__c, PST__c, QST__c,Total_without_Tax__c  FROM Warranty_Claims__r),
                        (SELECT Name, Estimated_Credit_Return__c FROM Fleet_Claims__r)
                    FROM Claim_Pack__c
                    WHERE Channel_Customer__c != NULL AND Bill_To_Address__r.Account_Address__c != NULL
                ]
            )
        );
        Test.stopTest();
        System.assertNotEquals(null, idJob);
        System.assertEquals(6, [SELECT COUNT() FROM Log__c], '2 for claim pack sending, 2 for updating claim packs and another 2 for inserting logs.');
        List<Claim_Pack__c> lstClaimPack = [SELECT Sent_to_ERP__c, Send_Time__c, Sync_Status__c, Error_Message__c FROM Claim_Pack__c LIMIT 10];
        System.assertEquals(2, lstClaimPack.size());
        System.assertEquals(true, lstClaimPack[0].Sent_to_ERP__c);
        System.assertNotEquals(null, lstClaimPack[0].Send_Time__c);
        System.assertEquals('Fail', lstClaimPack[0].Sync_Status__c);
        System.assertNotEquals(null, lstClaimPack[0].Error_Message__c);
        System.assertEquals(true, lstClaimPack[1].Sent_to_ERP__c);
        System.assertNotEquals(null, lstClaimPack[1].Send_Time__c);
        System.assertEquals('Fail', lstClaimPack[1].Sync_Status__c);
        System.assertNotEquals(null, lstClaimPack[1].Error_Message__c);
    }
    @IsTest
    static void testCalloutException() {
        Test.startTest();
        ExceptionMock objMock = new ExceptionMock();
        Test.setMock(HttpCalloutMock.class, objMock);
        Id idJob = System.enqueueJob(
            new CCM_ClaimPackSendingQueueable(
                Date.today(),
                [
                    SELECT
                        Name,
                        Channel_Customer__r.AccountNumber,
                        Bill_To_Address__r.Customer_Line_Oracle_ID__c,
                        Bill_To_Address__r.Account_Address__r.ORG_ID__c,
                        Ship_to_Address__r.Customer_Line_Oracle_ID__c,
                        Amount__c,
                        CurrencyIsoCode,
                        Type__c,Warranty_parts_credit_mark_up__c,
                        (SELECT Name, Total__c, Total_Except_Markup__c,Service_Partner__c, GST__c, HST__c, PST__c, QST__c,Total_without_Tax__c  FROM Warranty_Claims__r),
                        (SELECT Name, Estimated_Credit_Return__c FROM Fleet_Claims__r)
                    FROM Claim_Pack__c
                    WHERE Channel_Customer__c != NULL AND Bill_To_Address__r.Account_Address__c != NULL
                ]
            )
        );
        Test.stopTest();
        System.assertEquals(2, objMock.intInvokedCount, 'There should be a retry but only one depth of queueable can be tolerated in testing class.');
        System.assertNotEquals(null, idJob);
        List<Claim_Pack__c> lstClaimPack = [SELECT Sent_to_ERP__c, Send_Time__c, Sync_Status__c, Error_Message__c FROM Claim_Pack__c LIMIT 10];
        System.assertEquals(2, lstClaimPack.size());
        System.assertEquals(false, lstClaimPack[0].Sent_to_ERP__c);
        System.assertNotEquals(null, lstClaimPack[0].Send_Time__c);
        System.assertEquals(null, lstClaimPack[0].Sync_Status__c);
        System.assertEquals(null, lstClaimPack[0].Error_Message__c);
        System.assertEquals(false, lstClaimPack[1].Sent_to_ERP__c);
        System.assertNotEquals(null, lstClaimPack[1].Send_Time__c);
        System.assertEquals(null, lstClaimPack[1].Sync_Status__c);
        System.assertEquals(null, lstClaimPack[1].Error_Message__c);
        System.assertEquals(6, [SELECT COUNT() FROM Log__c]);
    }
    @IsTest
    static void testEmptyResponse() {
        Test.startTest();
        EmptyResponseMock objMock = new EmptyResponseMock();
        Test.setMock(HttpCalloutMock.class, objMock);
        Id idJob = System.enqueueJob(
            new CCM_ClaimPackSendingQueueable(
                Date.today(),
                [
                    SELECT
                        Name,
                        Channel_Customer__r.AccountNumber,
                        Bill_To_Address__r.Customer_Line_Oracle_ID__c,
                        Bill_To_Address__r.Account_Address__r.ORG_ID__c,
                        Ship_to_Address__r.Customer_Line_Oracle_ID__c,
                        Amount__c,
                        CurrencyIsoCode,
                        Type__c,Warranty_parts_credit_mark_up__c,
                        (SELECT Name, Total__c, Total_Except_Markup__c,Service_Partner__c, GST__c, HST__c, PST__c, QST__c,Total_without_Tax__c   FROM Warranty_Claims__r),
                        (SELECT Name, Estimated_Credit_Return__c FROM Fleet_Claims__r)
                    FROM Claim_Pack__c
                    WHERE Channel_Customer__c != NULL AND Bill_To_Address__r.Account_Address__c != NULL
                ]
            )
        );
        Test.stopTest();
        System.assertNotEquals(null, idJob);
        System.assertEquals(2, objMock.intInvokedCount, 'There are two Claim Packs with two syncing requests in total.');
        System.assertEquals(8, [SELECT COUNT() FROM Log__c], '2 for claim pack sending, 2 for updating claim packs, 2 for parsing errors and another 2 for inserting logs.');
    }
    @IsTest
    static void testStoppedQueue() {
        Test.startTest();
        SuccessMock objMock = new SuccessMock();
        Test.setMock(HttpCalloutMock.class, objMock);
        Id idJob = System.enqueueJob(new CCM_ClaimPackSendingQueueable(Date.today(), null));
        Test.stopTest();
        System.assertNotEquals(null, idJob);
        System.assertEquals(0, objMock.intInvokedCount, 'The queue will be stopped once no configuration could be found.');
    }
    @IsTest
    static void testRetryConstructor() {
        Test.startTest();
        SuccessMock objMock = new SuccessMock();
        Test.setMock(HttpCalloutMock.class, objMock);
        Id idJob = System.enqueueJob(
            new CCM_ClaimPackSendingQueueable(
                Date.today(),
                [
                    SELECT
                        Name,
                        Channel_Customer__r.AccountNumber,
                        Bill_To_Address__r.Customer_Line_Oracle_ID__c,
                        Bill_To_Address__r.Account_Address__r.ORG_ID__c,
                        Ship_to_Address__r.Customer_Line_Oracle_ID__c,
                        Amount__c,
                        CurrencyIsoCode,
                        Type__c,Warranty_parts_credit_mark_up__c,
                        (SELECT Name, Total__c, Total_Except_Markup__c,Service_Partner__c, GST__c, HST__c, PST__c, QST__c,Total_without_Tax__c  FROM Warranty_Claims__r),
                        (SELECT Name, Estimated_Credit_Return__c FROM Fleet_Claims__r)
                    FROM Claim_Pack__c
                    WHERE Channel_Customer__c != NULL AND Bill_To_Address__r.Account_Address__c != NULL
                ],
                1
            )
        );
        Test.stopTest();
        System.assertEquals(2, objMock.intInvokedCount, 'There are two Claim Packs with two syncing requests in total.');
        System.assertNotEquals(null, idJob);
        System.assertEquals(6, [SELECT COUNT() FROM Log__c]);
        List<Claim_Pack__c> lstClaimPack = [SELECT Sent_to_ERP__c, Sync_Status__c, Error_Message__c FROM Claim_Pack__c LIMIT 10];
        System.assertEquals(2, lstClaimPack.size());
        System.assertEquals(true, lstClaimPack[0].Sent_to_ERP__c);
        System.assertEquals('Success', lstClaimPack[0].Sync_Status__c);
        System.assertEquals(null, lstClaimPack[0].Error_Message__c);
        System.assertEquals(true, lstClaimPack[1].Sent_to_ERP__c);
        System.assertEquals('Success', lstClaimPack[1].Sync_Status__c);
        System.assertEquals(null, lstClaimPack[1].Error_Message__c);
    }
    /**
     * @description This is a mock class to respond a success.
     */
    public class SuccessMock implements HttpCalloutMock {
        public Integer intInvokedCount = 0;
        /**
         * @description This method should be implemented to mock a success response.
         * @param objHttpRequest the request context
         * @return an Http response
         */
        public HttpResponse respond(HttpRequest objHttpRequest) {
            intInvokedCount++;
            HttpResponse objHttpResponse = new HttpResponse();
            objHttpResponse.setHeader('Content-Type', 'application/json');
            objHttpResponse.setBody('{"Process_Status": "Success","Process_Result": [{"SFDC_Id": "Pack-20210909249","Oracle_Id": "B10409"}]}');
            objHttpResponse.setStatusCode(200);
            return objHttpResponse;
        }
    }
    private class FailureMock implements HttpCalloutMock {
        protected Integer intInvokedCount = 0;
        /**
         * @description This method should be implemented to mock a success response.
         * @param objHttpRequest the request context
         * @return an Http response
         */
        public HttpResponse respond(HttpRequest objHttpRequest) {
            intInvokedCount++;
            HttpResponse objHttpResponse = new HttpResponse();
            objHttpResponse.setHeader('Content-Type', 'application/json');
            objHttpResponse.setBody(
                '{' +
                '    "Process_Status": "Fail",' +
                '    "Process_Result": [' +
                '    {' +
                '        "SFDC_Id": "Pack-20210909249",' +
                '        "Oracle_Id": "B10409",' +
                '        "Error_Message": "This CLAIM was failed processing in Oracle.",' +
                '        "Error_Detail": "The item is already exited. Please type in another value."' +
                '    }]' +
                '}'
            );
            objHttpResponse.setStatusCode(200);
            return objHttpResponse;
        }
    }
    private class ExceptionMock implements HttpCalloutMock {
        protected Integer intInvokedCount = 0;
        /**
         * @description This method should be implemented to mock a success response.
         * @param objHttpRequest the request context
         * @return an Http response
         */
        public HttpResponse respond(HttpRequest objHttpRequest) {
            intInvokedCount++;
            throw new CalloutException('This is a fake callout exception.');
        }
    }
    private class EmptyResponseMock implements HttpCalloutMock {
        protected Integer intInvokedCount = 0;
        /**
         * @description This method should be implemented to mock a success response.
         * @param objHttpRequest the request context
         * @return an Http response
         */
        public HttpResponse respond(HttpRequest objHttpRequest) {
            intInvokedCount++;
            return new HttpResponse();
        }
    }
}