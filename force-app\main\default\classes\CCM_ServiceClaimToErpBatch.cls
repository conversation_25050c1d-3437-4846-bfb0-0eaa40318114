/**
 * <AUTHOR>
 * @date 2020-06-10
 * @description service claim to ERP by month
 * @revisions Chenyang Li 2021-04-20 Group Claim Packs for Tier 2 Customers separately.
 *            Chenyang Li 2021-09-08 Update the batch to send Warranty Claims as Packs on a daily basis.
 */
global class CCM_ServiceClaimToErpBatch extends CCM_Core implements Database.Batchable<SObject>, Database.Stateful {
    private Integer intMonth;
    private Integer intYear;
    private final String COLON = CCM_Constants.COLON;
    global CCM_ServiceClaimToErpBatch(Integer intYear, Integer intMonth) {
        this.intYear = intYear == null || intYear < 1900 || intYear > 3999 || intMonth == null || intMonth > 12 || intMonth < 1 ? 3999 : intYear;
        this.intMonth = intYear == null || intYear < 1900 || intYear > 3999 || intMonth == null || intMonth > 12 || intMonth < 1 ? 12 : intMonth;
    }
    global Database.QueryLocator start(Database.BatchableContext objBC) {
        return Database.getQueryLocator(
            [
                SELECT BillTo__c, ShipTo__c, Total__c, CreatedById, Service_Partner__c, Service_Partner__r.Tier_2_Dealer__c,Warranty_parts_credit_mark_up__c
                FROM Warranty_Claim__c
                WHERE
                    Status__c = :CCM_Constants.WARRANTY_CLAIM_STATUS_APPROVED
                    AND CALENDAR_YEAR(Last_Approval_Date__c) = :intYear
                    AND CALENDAR_MONTH(Last_Approval_Date__c) = :intMonth
                    AND Service_Partner__c != NULL
                    AND BillTo__r.Account_Address__c != NULL
                    AND Claim_Pack__c = NULL
            ]
        );
    }
    global void execute(Database.BatchableContext objBC, List<Warranty_Claim__c> lstWarrantyClaim) {
        Savepoint objSP;
        Set<Id> setBillToId = new Set<Id>();
        Set<Id> setShipToId = new Set<Id>();
        Set<Id> setCustomerId = new Set<Id>();
        Set<Id> setTier2DealerId = new Set<Id>();
        List<Warranty_Claim__c> lstWarrantyClaimToUpdate = new List<Warranty_Claim__c>();
        Map<Id, Id> mapAccountIdChild2Parent = new Map<Id, Id>();
        Map<String, Claim_Pack__c> mapKey2ClaimPack = new Map<String, Claim_Pack__c>();
        Map<String, List<Warranty_Claim__c>> mapKey2ListWarrantyClaim = new Map<String, List<Warranty_Claim__c>>();
        try {
            objSP = Database.setSavepoint();
            getKey2WarrantyClaimList(lstWarrantyClaim, setBillToId, setShipToId, setCustomerId, setTier2DealerId, mapKey2ListWarrantyClaim);
            getCustomerMapping(setTier2DealerId, mapAccountIdChild2Parent);
            getKey2ClaimPackMap(mapKey2ListWarrantyClaim.keySet(), setCustomerId, setBillToId, setShipToId, mapAccountIdChild2Parent, mapKey2ClaimPack,mapKey2ListWarrantyClaim);
            cumulateClaimToPack(mapKey2ClaimPack, mapKey2ListWarrantyClaim, lstWarrantyClaimToUpdate);
            // prettier-ignore
            if (!mapKey2ClaimPack.isEmpty()) update mapKey2ClaimPack.values();
            // prettier-ignore
            if (!lstWarrantyClaimToUpdate.isEmpty()) update lstWarrantyClaimToUpdate;
            // prettier-ignore
            if (Test.isRunningTest()) throw new CCM_CustomException('test');
        } catch (Exception objE) {
            // prettier-ignore
            if (!Test.isRunningTest()) Database.rollback(objSP);
            insert new Log__c(
                ApexName__c = getClassName(),
                Error_Message__c = objE.getMessage() + CCM_Constants.NEXT_LINE + objE.getStackTraceString(),
                Method__c = getMethodName(),
                Name = 'Warranty Claim processing failed',
                ReqParam__c = JSON.serialize(mapKey2ListWarrantyClaim),
                ResParam__c = JSON.serialize(mapKey2ClaimPack)
            );
        }
    }
    private void getKey2WarrantyClaimList(
        List<Warranty_Claim__c> lstWarrantyClaim,
        Set<Id> setBillToId,
        Set<Id> setShipToId,
        Set<Id> setCustomerId,
        Set<Id> setTier2DealerId,
        Map<String, List<Warranty_Claim__c>> mapKey2ListWarrantyClaim
    ) {
        String strKey;
        for (Warranty_Claim__c objWC : lstWarrantyClaim) {
            // prettier-ignore
            if (objWC.Service_Partner__r.Tier_2_Dealer__c == true) setTier2DealerId.add(objWC.Service_Partner__c);
            if(objWC.Warranty_parts_credit_mark_up__c == null){
                objWC.Warranty_parts_credit_mark_up__c = 0;
            }
            setBillToId.add(objWC.BillTo__c);
            if(String.isNotBlank(objWC.ShipTo__c)) {
                setShipToId.add(objWC.ShipTo__c);
            }
            setCustomerId.add(objWC.Service_Partner__c);
            strKey = objWC.Service_Partner__c + COLON + objWC.BillTo__c;
            if(String.isNotBlank(objWC.ShipTo__c)) {
                strKey += COLON + objWC.ShipTo__c;
            }
            if (!mapKey2ListWarrantyClaim.containsKey(strKey)) {
                mapKey2ListWarrantyClaim.put(strKey, new List<Warranty_Claim__c>());
            }
            mapKey2ListWarrantyClaim.get(strKey).add(objWC);
        }
    }
    private void getCustomerMapping(Set<Id> setTier2DealerId, Map<Id, Id> mapAccountIdChild2Parent) {
        for (Account_Address__c objAA : [
            SELECT Customer__c, X2nd_Tier_Dealer__c
            FROM Account_Address__c
            WHERE
                X2nd_Tier_Dealer__c IN :setTier2DealerId
                AND (RecordTypeId = :CCM_Constants.ADDRESS_SHIPPING_ADDRESS_RECORD_TYPE_ID
                OR RecordTypeId = :CCM_Constants.ADDRESS_DROPSHIP_SHIPPING_ADDRESS_RECORD_TYPE_ID)
        ]) {
            // prettier-ignore
            if (String.isBlank(objAA.Customer__c)) continue;
            mapAccountIdChild2Parent.put(objAA.X2nd_Tier_Dealer__c, objAA.Customer__c);
        }
    }
    private void getKey2ClaimPackMap(
        Set<String> setKey,
        Set<Id> setCustomerId,
        Set<Id> setBillToId,
        Set<Id> setShipToId,
        Map<Id, Id> mapAccountIdChild2Parent,
        Map<String, Claim_Pack__c> mapKey2ClaimPack,
        Map<String, List<Warranty_Claim__c>> mapKey2ListWarrantyClaim
    ) {
        String strKey;
        List<String> lstKeyPart;
        List<Claim_Pack__c> lstClaimPackToInsert = new List<Claim_Pack__c>();
        for (Claim_Pack__c objCP : [
            SELECT Id, Name, Submitter__c, Channel_Customer__c, Second_Tier_Customer__c, Bill_To_Address__c, Year__c, Month__c, Amount__c,Warranty_parts_credit_mark_up__c
            FROM Claim_Pack__c
            WHERE
                (Channel_Customer__c IN :setCustomerId
                OR Second_Tier_Customer__c IN :setCustomerId)
                AND Bill_To_Address__c IN :setBillToId
                AND Ship_To_Address__c IN :setShipToId
                AND Year__c = :String.valueOf(this.intYear)
                AND Month__c = :String.valueOf(this.intMonth)
                AND Type__c = :CCM_Constants.CLAIM_PACK_TYPE_SERVICE_CLAIM
                AND Sent_to_ERP__c = FALSE
        ]) {
            strKey = (String.isNotBlank(objCP.Second_Tier_Customer__c) ? objCP.Second_Tier_Customer__c : objCP.Channel_Customer__c) + COLON + objCP.Bill_To_Address__c;
            if(String.isNotBlank(objCP.Ship_To_Address__c)) {
                strKey += COLON + objCP.Ship_To_Address__c;
            }
            mapKey2ClaimPack.put(strKey, objCP);
        }
        Map<String,Decimal> customerIdMapToMarkup = new Map<String,Decimal>();
        for (String strK : setKey) {
            for(Warranty_Claim__c wc : mapKey2ListWarrantyClaim.get(strK)){
            system.debug(wc);
                if(customerIdMapToMarkup.containsKey(strK)){
                    system.debug('1'+customerIdMapToMarkup.get(strK));
                    customerIdMapToMarkup.put(strK, customerIdMapToMarkup.get(strK)+wc.Warranty_parts_credit_mark_up__c);
                }else{
                    system.debug('2');
                    customerIdMapToMarkup.put(strK, wc.Warranty_parts_credit_mark_up__c);

                }
            }
            if (!mapKey2ClaimPack.containsKey(strK)) {
                lstKeyPart = strK.split(COLON);
                lstClaimPackToInsert.add(
                    new Claim_Pack__c(
                        Channel_Customer__c = mapAccountIdChild2Parent.containsKey(lstKeyPart[0]) ? mapAccountIdChild2Parent.get(lstKeyPart[0]) : lstKeyPart[0],
                        Second_Tier_Customer__c = mapAccountIdChild2Parent.containsKey(lstKeyPart[0]) ? lstKeyPart[0] : null,
                        Bill_To_Address__c = lstKeyPart[1],
                        Ship_To_Address__c = lstKeyPart[2],
                        Year__c = String.valueOf(this.intYear),
                        Month__c = String.valueOf(this.intMonth),
                        Is_Paid__c = false,
                        Type__c = CCM_Constants.CLAIM_PACK_TYPE_SERVICE_CLAIM,
                        Warranty_parts_credit_mark_up__c = customerIdMapToMarkup.get(strK)

                    )
                );
            }
        }
        if (!lstClaimPackToInsert.isEmpty()) {
            insert lstClaimPackToInsert;
            for (Claim_Pack__c objCP : lstClaimPackToInsert) {
                strKey = (String.isNotBlank(objCP.Second_Tier_Customer__c) ? objCP.Second_Tier_Customer__c : objCP.Channel_Customer__c) + COLON + objCP.Bill_To_Address__c;
                if(String.isNotBlank(objCP.Ship_To_Address__c)) {
                    strKey += COLON + objCP.Ship_To_Address__c;
                }
                mapKey2ClaimPack.put(strKey, objCP);
            }
        }
    }
    private void cumulateClaimToPack(
        Map<String, Claim_Pack__c> mapKey2ClaimPack,
        Map<String, List<Warranty_Claim__c>> mapKey2ListWarrantyClaim,
        List<Warranty_Claim__c> lstWarrantyClaimToUpdate
    ) {
        Claim_Pack__c objClaimPack;
        for (String strK : mapKey2ClaimPack.keySet()) {
            objClaimPack = mapKey2ClaimPack.get(strK);
            objClaimPack.Amount__c = objClaimPack.Amount__c == null ? 0 : objClaimPack.Amount__c;
            for (Warranty_Claim__c objWC : mapKey2ListWarrantyClaim.get(strK)) {
                objClaimPack.Submitter__c = objWC.CreatedById;
                objClaimPack.Amount__c += objWC.Total__c;
                lstWarrantyClaimToUpdate.add(new Warranty_Claim__c(Id = objWC.Id, Claim_Pack__c = objClaimPack.Id));
            }
        }
    }
    global void finish(Database.BatchableContext objBC) {
        Set<Id> setClaimPackId = new Set<Id>();
        for (Claim_Pack__c objCP : [SELECT Id FROM Claim_Pack__c WHERE Sent_to_ERP__c = FALSE AND Type__c !='Registration Claim']) {
            setClaimPackId.add(objCP.Id);
        }
        // prettier-ignore
        if (setClaimPackId.isEmpty()) return;
        Database.executeBatch(new CCM_FleetClaimToErp2Batch(Date.today(), setClaimPackId), Integer.valueOf(Label.CCM_FleetClaimToErp2Batch));
    }
    @TestVisible
    private static Date datFrom = Date.today();
    @InvocableMethod
    public static void invoke() {
        CCM_ServiceClaimToErpBatch objBatch;
        switch on datFrom.day() {
            when 1 {
                datFrom = datFrom.addMonths(-1);
                objBatch = new CCM_ServiceClaimToErpBatch(datFrom.year(), datFrom.month());
            }
            when else {
                objBatch = new CCM_ServiceClaimToErpBatch(datFrom.year(), datFrom.month());
            }
        }
        Database.executeBatch(objBatch, Integer.valueOf(Label.CCM_ServiceClaimToErpBatch));
    }
}