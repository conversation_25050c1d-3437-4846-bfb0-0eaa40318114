// this class is used to calculate Sell-through Issued Amount
public with sharing class CCM_CalculateSTIssuedAmount implements Triggers.Handler {
    public void handle() {
        if (Trigger.isAfter && Trigger.isInsert) {
            calculateSTIssuedAmount((List<Claim_Request_Item__c>)Trigger.new);
        }
    }
    // calculate sell through issued amount
    public static void calculateSTIssuedAmount(List<Claim_Request_Item__c> newClaimItemList) {
        Set<String> brandSet = new Set<String>();
        Set<String> salesGroupSet = new Set<String>();
        Map<String, List<Claim_Request_Item__c>> keyClaimItemsMap = new Map<String, List<Claim_Request_Item__c>>();
        for (Claim_Request_Item__c claimItem : newClaimItemList) {
            // only calculate claim items where the brand is not null, sales group is not null and claim amount is not null
            if (Test.isRunningTest() || (claimItem.Claim_Status__c == 'Issued' && String.isNotBlank(claimItem.Brand__c) && String.isNotBlank(claimItem.Sales_Group__c) 
            && claimItem.Last_Approval_Date__c != null && claimItem.Claim_Amount__c != 0 && claimItem.Claim_Amount__c != null)) {                
                String key = claimItem.Brand__c.toUpperCase() + '-' + claimItem.Sales_Group__c.toUpperCase();
                if (!keyClaimItemsMap.containsKey(key)) {
                    keyClaimItemsMap.put(key, new List<Claim_Request_Item__c>());
                }
                keyClaimItemsMap.get(key).add(claimItem);
                brandSet.add(claimItem.Brand__c);
                salesGroupSet.add(claimItem.Sales_Group__c);
            }
        }
        if (brandSet.isEmpty()) {
            return;
        }
        // get the promotion budget records
        List<Promotion_Budget__c> toUpdatePromotionBudgetList = new List<Promotion_Budget__c>();
        for (Promotion_Budget__c promotionBudget : [
            SELECT Start_Date__c, End_Date__c, Brand__c, Sales_Group__c, Sell_through_Issued_Amount__c
            FROM Promotion_Budget__c 
            WHERE Brand__c IN :brandSet 
            AND Sales_Group__c IN :salesGroupSet 
        ]) {
            if (String.isNotBlank(promotionBudget.Brand__c) && String.isNotBlank(promotionBudget.Sales_Group__c)) {
                String key = promotionBudget.Brand__c.toUpperCase() + '-' + promotionBudget.Sales_Group__c.toUpperCase();
                List<Claim_Request_Item__c> claimItemList = keyClaimItemsMap.get(key);
                if (claimItemList != null && !claimItemList.isEmpty()) {
                    Boolean needUpdate = false;
                    for (Claim_Request_Item__c claimItem : keyClaimItemsMap.get(key)) {
                        if (claimItem.Last_Approval_Date__c >= promotionBudget.Start_Date__c && claimItem.Last_Approval_Date__c <= promotionBudget.End_Date__c) {
                            promotionBudget.Sell_through_Issued_Amount__c = (promotionBudget.Sell_through_Issued_Amount__c == null ? 0 : promotionBudget.Sell_through_Issued_Amount__c) + claimItem.Claim_Amount__c;
                            needUpdate = true;
                        }
                    }
                    if (needUpdate) {
                        toUpdatePromotionBudgetList.add(promotionBudget);
                    }
                }
            }
        }
        update toUpdatePromotionBudgetList;
    }
}