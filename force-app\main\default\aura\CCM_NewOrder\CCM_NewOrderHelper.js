/**
 * Created by gluo006 on 5/20/2019.
 */
({
    init: function (component) {
        var self = this;
        //use data service get order and orderItem field
        //self.getOrderAndOrderItemData(component);
        self.iniOrderData(component);
    },
    iniOrderData:function(component){
        var self = this;
        //initial the case, warranty and customer name info
        var recordId = component.get('v.recordId');
        console.log('recordId' + recordId);
        var caseId = '', warrantyId = '';
        var workspace = component.find("workspace");
        var openTabs = [];
        workspace.getAllTabInfo().then(function (tabInfo) {
            for(var i=0; i<tabInfo.length; i++){
                var currentTab = tabInfo[i];
                if(currentTab.focused){
                    component.set('v.focusedTabId', currentTab.recordId);
                    if(currentTab.subtabs.length > 1){
                        for(var j=0; j< currentTab.subtabs.length; j++){
                            openTabs.push(currentTab.subtabs[j].recordId);
                        }
                    }
                }
            }
            //all the open tab
            component.set('v.openTabs', openTabs);
            if(component.get('v.csrCaseId') !== ''){
                caseId = component.get('v.csrCaseId');
            }
            self.getInitOrderInfo(component, caseId, warrantyId);
        })
    },
    getInitOrderInfo:function(component, caseId, warrantyId){
        var self = this;
        var action = component.get('c.getOrder');
        action.setParams({
            'caseId': caseId,
            'warId': warrantyId,
            idOrderRecordType: component.get("v.orderRecordTypeId") || null
        })
        action.setCallback(this, function (response) {
             var state = response.getState();
             var result = JSON.parse(response.getReturnValue());
            console.log('line49: '+state);
             if (state === 'SUCCESS' ) {
                 if(result.Message){
                     self.showToast('Failed', result.Message);
                     self.closeActiveTab(component);
                     self.hideEle(component, 'spinner')
                 }else{
                     if(result.ActualSolution){
                         component.set('v.isRecallCase', true);
                         /*var actualSolution = result.ActualSolution;
                         if(actualSolution == 'Repair' || actualSolution == 'Refund'){
                             component.set('v.recallOrderTypeList', ['Recall Package'])
                         }else if(actualSolution == 'Replacement'){
                             component.set('v.recallOrderTypeList', ['Recall Package', 'Recall Order']);
                         }*/
                     }
                     self.getOrderAndOrderItemData(component, result);
                     component.set('v.csManagerFlag', result.csManagerFlag);
                     console.log('csManagerFlag',result.csManagerFlag);
                 }
             } else if (state === 'ERROR') {
                 self.showToast('Failed', response.getError()[0].message);
                 self.hideEle(component, 'spinner')
             }
        });
        $A.enqueueAction(action);
    },
    getWarehouse:function(component, caseId, productId, productName){
        var self = this;
        var action = component.get('c.getWarehouse');
        var productObj = JSON.parse(JSON.stringify(component.get('v.productObj')));
        var partsObj = JSON.parse(JSON.stringify(component.get('v.partsObj')));
        console.log('productName' + productName);
        action.setParams({
            'caseId': caseId,
            'productId': productId,
            'productName': productName
        })
        action.setCallback(this, function (response) {
             var state = response.getState();
             var result = JSON.parse(response.getReturnValue());
             if (state === 'SUCCESS' ) {
                 if(result.Message){
                     self.showToast('Failed', result.Message);
                     self.closeActiveTab(component);
                     self.hideEle(component, 'spinner')
                 }else{
                     if(result.warehouse){
                      if(component.get('v.price') == 0){
                         self.showToast('Failed', 'There is no correct price for this product!');
                         return;
                      }
                      var productId;
                      var isValid = self.getValidation(component, 'secondStep');
                      if (!isValid) {
                          return;
                      }
                      var orderItemData = JSON.parse(JSON.stringify(component.get('v.orderItemData')));

                      if(result.needCheckAlternativeProduct) {
                            let notOverlap = this.checkProductAddScope(orderItemData, productObj, result.alternativeProducts);
                            if(!notOverlap) {
                                return;
                            }
                      }

                      var newOrderItem = {};
                      var selectedPart = component.get('v.partsVal').split(';');
                      newOrderItem.ProductLine = {Id: productObj.Id, Name: productObj.Name, ProductCode: productObj.ProductCode};
                      newOrderItem.PartLine = {Id: partsObj.Id, Name: partsObj.Name};
                      //console.log(`line199: ${partsObj}`);
                      if(partsObj.Id == null || partsObj.Id == ''){
                        newOrderItem.isParts = false;
                      }else {
                        newOrderItem.isParts = true;
                      }
                      newOrderItem.price = component.find('price').get('v.value');
                      newOrderItem.quantity = component.find('quantity').get('v.value');
                      newOrderItem.productType = component.find('orderLineType').get('v.value');
                      newOrderItem.warrantyItem = component.find('warrantyItem').get('v.value');
                      newOrderItem.scheduleShippingDate = component.find('scheduleShippingDate').get('v.value');
                      newOrderItem.warehouse = result.warehouse;
                      //if the product already added, can not add again
                      if(orderItemData.length > 0){
                          for(var i=0; i<orderItemData.length; i++){
                              if(orderItemData[i].ProductLine.Name == productObj.Name && orderItemData[i].PartLine.Name == partsObj.Name){
                                  self.showToast('Failed', 'You already added the product and the part');
                                  return;
                              }
                              if((!orderItemData[i].PartLine.Name && partsObj.Name !== '') || (orderItemData[i].PartLine.Name && !partsObj.Name)){
                                  self.showToast('Failed', 'Product and Parts cannot be placed in one order');
                                  return;
                              }
                          }
                      }
                      if(!partsObj.Name && component.get('v.caseType') == 'Non-warranty Order'){
                         self.showToast('Failed', 'You can not add Finish Goods product in Non-warranty Order');
                         return;
                      }
                      orderItemData.push(newOrderItem);
                      component.set('v.orderItemData', orderItemData);
                      //reset the form
                      //fire the product clear in the order page
                      var cmpEvent = $A.get("e.c:CCM_ProductLookUpChange");
                      cmpEvent.fire();
                      component.set('v.productObj', {Name: '', Id: ''});
                      component.set('v.partsObj', {Name: '', Id: ''});
                      component.set('v.price', 0);
                      component.set('v.quantity', 1);
                      component.set('v.total', 0);
                      component.find('orderLineType').set('v.value', component.get('v.orderItemFields.Product_Type__c'))
                      component.find('warrantyItem').set('v.value', false);
                      component.find('scheduleShippingDate').set('v.value', component.get('v.iniShippingScheduleDate'));
                     }
                     console.log('warehouse',result.warehouse.Name);
                     self.hideEle(component, 'spinner');
                 }
                 var hasca = false;
                 var hasus = false;
                 for(var i = 0; i<orderItemData.length; i++){
                     if(orderItemData[i].warehouse.substr(0,2) == 'CA'){
                         hasca = true;
                     }else if(orderItemData[i].warehouse.substr(0,3) == 'CNA'){
                         hasus = true;
                     }
                 }

                  if(!component.get('v.popupCAandUS') && hasca && hasus){
                      window.alert('Please make sure the order products are all from the warehouses of the same country. Otherwise please place separate orders.');
                      component.set('v.popupCAandUS',true);
                  }
             } else if (state === 'ERROR') {
                 self.showToast('Failed', response.getError()[0].message);
                 self.hideEle(component, 'spinner')
             }
        });
        $A.enqueueAction(action);
    },
    getGeolocation:function(component, caseId, address){
        var self = this;
        var action = component.get('c.getGeolocation');
        action.setParams({
            'caseId': caseId,
            'street': address.street,
            'city' : address.city,
            'state': address.state,
            'postalCode': address.zipCode,
            'country': address.country

        })
        action.setCallback(this, function (response) {
             var state = response.getState();
             var result = JSON.parse(response.getReturnValue());
             if (state === 'SUCCESS' ) {
                 if(result.Message){
                     self.showToast('Failed', result.Message);
                     self.closeActiveTab(component);
                     self.hideEle(component, 'spinner')
                 }else{
                     console.log('contact',result.contactId);
                     self.hideEle(component, 'spinner');
                 }
             } else if (state === 'ERROR') {
                 self.showToast('Failed', response.getError()[0].message);
                 self.hideEle(component, 'spinner')
             }
        });
        $A.enqueueAction(action);
    },
    getOrderAndOrderItemData:function(component, result){
        var self = this;
        //get order info
          component.find("orderRecord").getNewRecord(
              "Order", // objectApiName
              component.get('v.recordTypeId'), // recordTypeId
              true, // skip cache
              $A.getCallback(function () {
                  var rec = component.get("v.orderRecord");
                  if(!component.get('v.isEdit')){
                      component.set('v.orderFields.Case__c', result.CaseId);
                      component.set('v.caseName', result.CaseName);
                      component.set('v.orderFields.Brand__c', result.BrandName);
                      component.set('v.orderFields.AccountId', result.AccId);
                      component.set('v.accountName', result.AccName);
                      component.set('v.caseType', result.CaseType);
                    //   if(result.CaseType == 'Warranty Order'){
                    //       component.set('v.partsDisabled', true);
                    //   }

                      if(result.placeOrderScope == 'fg') {
                        component.set('v.partsDisabled', true);
                      }
                      component.set('v.placeOrderScope', result.placeOrderScope);
                      component.set('v.orderFields.Warranty__c', result.WarId);
                      component.set('v.warrantyName', result.WarName);
                      component.set('v.orderFields.EffectiveDate', result.EffectiveDate);
                      component.set('v.iniStartDate', result.EffectiveDate);
                      component.set('v.iniShippingScheduleDate', result.EffectiveDate);
                      component.set('v.orderFields.Custome_Phone__c', result.Phone);
                      component.set('v.orderFields.CustomerEmail__c', result.Email);
                      component.set('v.orderFields.Type', result.Type);
                      //address
                      component.set('v.orderFields.Shipping_Address__c', result.Street);
                      component.set('v.orderFields.Shipping_City__c', result.City);
                      component.set('v.orderFields.Shipping_State__c', result.State);
                      component.set('v.orderFields.Shipping_Zip_Code__c', result.ZipCode);
                      component.set('v.orderFields.Shipping_Country__c', result.Country);
                      console.table(result);
                      //create address attribute for Melissa Data Component- Added By Nitish Gyansys (SC-08)
                      let address = {
                          'street' : result.Street,
                          'city' : result.City,
                          'state' : result.State,
                          'country' : result.Country,
                          'zipCode' : result.ZipCode
                      }
                      component.set("v.address", address);
                      component.set("v.addressSet", true);
                      self.hideEle(component, 'spinner')
                  }

                  console.log(JSON.stringify(rec));
                  console.log(JSON.stringify(component.get("v.orderFields")));
                  var error = component.get("v.targetError");
                  if (error || (rec === null)) {
                      console.log("Error initializing record template: " + error);
                  } else {
                      console.log("Record template initialized: " + rec.apiName);

                  }
              })
          );

          //get OrderItem info
        component.find("orderItemRecord").getNewRecord(
            "OrderItem", // objectApiName
            component.get('v.recordTypeId'), // recordTypeId
            true, // skip cache
            $A.getCallback(function () {
                var rec = component.get("v.orderItemRecord");
                console.log(JSON.stringify(rec));
                self.hideEle(component, 'spinner')
                var error = component.get("v.targetError");
                if (error || (rec === null)) {
                    console.log("Error initializing record template: " + error);
                } else {
                    console.log("Record template initialized: " + rec.apiName);

                }
            })
        );
    },
    reloadData: function (component) {
        component.find("orderRecord").reloadRecord(
            true,
            $A.getCallback(function () {
                var rec = component.get("v.orderRecord");
                self.hideEle(component, 'spinner')
                console.log(JSON.stringify(rec));
                var error = component.get("v.targetError");
                if (error || (rec === null)) {
                    console.log("Error initializing record template: " + error);
                } else {
                    console.log("Record template initialized: " + rec.apiName);
                }
            })
        );
    },
    getLocationUrl: function (paramName) {
        var searchString = window.location.search.substring(1),
            i, val, params = searchString.split("&");

        for (i = 0; i < params.length; i++) {
            val = params[i].split("=");
            if (val[0] == paramName) {
                return val[1];
            }
        }
        return null;
    },
    getValidation: function (component, step) {
        var valid = true;
        var recordType = component.get('v.recordType')
        if(step && step == 'secondStep'){
         if(component.get('v.caseType') == 'Warranty Order'){
             valid = valid && this.getElementRequiredError(component, 'product-lookup');
         }
        }else if(step && step == 'fourStep'){
            /*valid = valid && this.getElementRequiredError(component, 'billToCardNumber');
            valid = valid && this.getElementRequiredError(component, 'billToExpirationDate');
            valid = valid && this.getElementRequiredError(component, 'billToCvv');
            valid = valid && this.getElementRequiredError(component, 'billToFirstName');
            valid = valid && this.getElementRequiredError(component, 'billToLastName');
            valid = valid && this.getElementRequiredError(component, 'billToShippingCountry', 'LenLimit');
            valid = valid && this.getElementRequiredError(component, 'billToShippingPostalCode');
            valid = valid && this.getElementRequiredError(component, 'billToShippingState', 'LenLimit');
            valid = valid && this.getElementRequiredError(component, 'billToShippingCity');
            valid = valid && this.getElementRequiredError(component, 'billToShippingStreet');*/
        }else{
            valid = valid && this.getElementRequiredError(component, 'customerName');
            if(valid) {
                valid = this.getAddressValidation(component);
            }

            //valid = valid && this.getElementRequiredError(component, 'shippingStreet');
            //valid = valid && this.getElementRequiredError(component, 'shippingCity');
            //valid = valid && this.getElementRequiredError(component, 'shippingZipCode');
            //valid = valid && this.getElementRequiredError(component, 'shippingState', 'LenLimit');
            //valid = valid && this.getElementRequiredError(component, 'shippingCountry', 'LenLimit');
            //TO DO   add the Please input the abbreviation (in 2 letters) of the state and country.
        }
        return valid;
    },
    getAddressValidation : function(component) {
        let address = component.get("v.address");
        if($A.util.isEmpty(address.street) || $A.util.isEmpty(address.city) || $A.util.isEmpty(address.zipCode)) {
            component.set("v.melissaDataError", 'Address is a required field.');
            return false;
        }
        if($A.util.isEmpty(address.state) || $A.util.isEmpty(address.country) || address.state.length !== 2 || address.country.length !== 2) {
            component.set("v.melissaDataError", 'Please input the abbreviation (in 2 letters) of the country/state.');
            return false;
        }
        if(address.city.length > 30) {
            component.set("v.melissaDataError", 'Warning: You could enter up to 30 characters.');
            return false;
        }
        if( /^[A-Za-z,.\- ]+$/.test(address.city) == false) {
            component.set("v.melissaDataError", 'Please remove the tildes.');
            return false;
        }
        component.set("v.melissaDataError", '');
        return true;
	},
    getElementRequiredError: function (component, ele, lenLimit) {
        var val;
        var valid;
        var requiredText = component.find(ele + '-error-required');
        if(ele.toUpperCase().indexOf('LOOKUP') > -1){
            val = JSON.parse(JSON.stringify(component.get('v.productObj'))).Name;
        }else{
            var element = component.find(ele);
            val = element.get('v.value');
        }
        if(val){
            val = val.trim();
        }
        if(lenLimit){
            valid = (val && (/^[A-Z|a-z][A-Z|a-z]$/.test(val)))
        }else{
            valid = !!val;
        }
        if (valid) {
            $A.util.removeClass(element, 'field-error');
            if (requiredText) {
                $A.util.addClass(requiredText, 'slds-hide');
            }
        } else {
            $A.util.addClass(element, 'field-error');
            if (requiredText) {
                $A.util.removeClass(requiredText, 'slds-hide');
            }
        }
        return valid;
    },
    getPriceBook:function(component){
        let idOrderRecordType = component.get("v.orderRecordTypeId"),
            self = this,
            action = component.get("c.Next");
        action.setParams({
            idOrderRecordType,
            caseId: component.get('v.orderFields.Case__c'),
            warId: component.get('v.orderFields.Warranty__c'),
            brand: component.get('v.orderFields.Brand__c'),
            type: component.get('v.orderFields.Type')
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = response.getReturnValue();
            if (state === "SUCCESS") {
                if(result){
                    self.showToast('Failed', result);
                }else{
                    self.hideEle(component, 'firstStep');
                    self.hideEle(component, 'firstActions');
                    self.showEle(component, 'secondStep');
                    self.showEle(component, 'secondActions');
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    checkAddress:function(component){
        var self = this;
        var street;
        var city;
        var state;
        var country = '';
        var action = component.get('c.checkAddress');
        street = component.find('shippingStreet').get('v.value');
        city = component.find('shippingCity').get('v.value');
        state = component.find('shippingState').get('v.value');
        country = component.find('shippingCountry').get('v.value');
        action.setParams({
            'country': country,
            'province': state,
            'city': city,
            'street': street,
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = response.getReturnValue();
            var status;
            var message;
            if (state === 'SUCCESS' ) {
                if(!result){
                    status = 'Failed';
                    message = 'Can not find the address';
                    self.showToast(status, message);
                }
            }
            self.hideEle(component, 'streetLoading');
            self.hideEle(component, 'cityLoading');
            self.hideEle(component, 'stateLoading');
            self.hideEle(component, 'countryLoading');
        });
        $A.enqueueAction(action);
    },
    getAddressByCode:function(component){
        var self = this;
        var zipCode = component.find('shippingZipCode').get('v.value');
        var country = component.find('shippingCountry').get('v.value');
        var action = component.get('c.getAddressByCode');
        action.setParams({
            'postalCode': zipCode,
            'country': country
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = response.getReturnValue();
            if (state === 'SUCCESS' ) {
                if(result !== null){
                    result = result.split(',');
                    var newCity = result[0];
                    var newState = result[1];
                    var newCountry = result[2];
                    if(zipCode && country && (newCity.trim() == '' && newState.trim() == '')){
                        self.showToast('Failed', 'Zip code and city do not match, please check again.')
                        self.hideEle(component, 'cityLoading');
                        self.hideEle(component, 'stateLoading');
                        self.hideEle(component, 'countryLoading');
                        return;
                    }
                    component.find('shippingCity').set('v.value', newCity);
                    component.find('shippingState').set('v.value', newState);
                    component.find('shippingCountry').set('v.value', newCountry);
                }
                self.hideEle(component, 'cityLoading');
                self.hideEle(component, 'stateLoading');
                self.hideEle(component, 'countryLoading');
            }
        });
        $A.enqueueAction(action);
    },
    checkProductType:function(component){
        let orderRecordTypeId = component.get("v.orderRecordTypeId"),
            self = this,
            action = component.get("c.Next2"),
            orderItemList = JSON.stringify(component.get("v.orderItemData"));
        action.setParams({
            orderRecordTypeId,
            'caseId': component.get('v.orderFields.Case__c'),
            'warId': component.get('v.orderFields.Warranty__c'),
            'brand': component.get('v.orderFields.Brand__c'),
            'type': component.get('v.orderFields.Type'),
            'result': orderItemList,
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = response.getReturnValue();
            if (state === 'SUCCESS' ) {
                if(result){
                    self.showToast('Failed', result);
                    component.set('v.isSaving', false);
                }else{
                    //self.showEle(component, 'lastStep');
                    //self.showEle(component, 'thirdActions');
                    self.hideEle(component, 'secondStep');
                    self.hideEle(component, 'secondActions');
                    //initial the shipping method
                    component.set('v.orderFields.Shipping_method__c', 'Fedex Ground');
                }
            }
        });
        $A.enqueueAction(action);
    },
//    getPartByProduct:function(component, prodId){
//        var self = this;
//        var action = component.get('c.getParts');
//        action.setParams({
//            'caseId': component.get('v.orderFields.Case__c'),
//            'warId': component.get('v.orderFields.Warranty__c'),
//            'brand': component.get('v.orderFields.Brand__c'),
//            'type': component.get('v.orderFields.Type'),
//            'productId': prodId,
//        })
//        action.setCallback(this, function (response) {
//            var state = response.getState();
//            var result = JSON.parse(response.getReturnValue());
//            if (state === 'SUCCESS' ) {
//                if(result.Message){
//                    self.showToast('Failed', result.Message);
//                }else{
//                    component.set('v.parts', result);
//                    self.hideEle(component, 'spinnerParts');
//                }
//            }
//        });
//        $A.enqueueAction(action);
//    },
    getPriceByProduct:function(component, prodId, partsId){
        let orderRecordTypeId = component.get("v.orderRecordTypeId"),
            self = this,
            action = component.get("c.retrievePrice");
        action.setParams({
            orderRecordTypeId,
            'caseId': component.get('v.orderFields.Case__c'),
            'warId': component.get('v.orderFields.Warranty__c'),
            'brand': component.get('v.orderFields.Brand__c'),
            'type': component.get('v.orderFields.Type'),
            'productStorageId': prodId,
            'partStorageId': partsId,
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = JSON.parse(response.getReturnValue());
            if (state === 'SUCCESS' ) {
                if(result){
                    component.set('v.price', result.price);
                    component.set('v.addItemDisabled', false );
                    self.calculateTotal(component);
                }else{
                    self.showToast('Failed', result.message);
                }
                self.hideEle(component, 'spinnerPrice');
            }
        });
        $A.enqueueAction(action);
    },
    calculateTotal:function(component, event, helper){
        var price = Number(component.find('price').get('v.value'));
        var quantity = Number(component.find('quantity').get('v.value'));
        if(quantity <=0){
            return;
        }
        var total = Number(price * quantity).toFixed(2);
        component.set('v.total', total);
    },
    checkCanCreatePartsOrder: function (component) {
        let self = this,
        action = component.get("c.checkCanCreatePartsOrder"),
        orderItemList = JSON.stringify(component.get("v.orderItemData"));
    action.setParams({
        'caseId': component.get('v.orderFields.Case__c'),
        'orderRecordTypeId':component.get("v.orderRecordTypeId"),
        'warrantyId': component.get('v.orderFields.Warranty__c'),
        'result': orderItemList,
    })
    action.setCallback(this, function (response) {
        var state = response.getState();
        var result = response.getReturnValue();
        if (state === 'SUCCESS' ) {
            if(!result){
                self.showToast('Failed', 'Can not create Order');
                component.set('v.isSaving', false);
                return;
            }else{
                self.checkProductType(component);
                //Added to automate the Freight Calculation
                self.showEle(component, 'spinner');
                //added By Nitish Gyansys (SC-08) : Skip ShippingInfo
                self.checkOnRates(component);
                //let action;
            }
        }
    });
    $A.enqueueAction(action);
    },
    checkOnRates:function(component){
        let orderRecordTypeId = component.get("v.orderRecordTypeId"),
            self = this,
            orderItemList = component.get("v.orderItemData"),
            action = component.get("c.Serify"),
            productCost = 0;
        //check is parts order
        var isPartsOrder = false;
        orderItemList.forEach(function(item){
            productCost += Number(item.price) * Number(item.quantity);
            if(item.PartLine.Name){
               isPartsOrder = true;
            }
        });
        action.setParams({
            orderRecordTypeId,
            'caseId':component.get('v.orderFields.Case__c'),
            'warId':component.get('v.orderFields.Warranty__c'),
            'brand':component.get('v.orderFields.Brand__c'),
            'type': component.get('v.orderFields.Type'),
            'zipCode': component.get('v.orderFields.Shipping_Zip_Code__c'),
            'shippingmethod':component.get('v.orderFields.Shipping_method__c'),
            'result': JSON.stringify(orderItemList),
            'shippingFree':productCost
        })
        action.setCallback(this, function (response) {
         var state = response.getState();
         var result = JSON.parse(response.getReturnValue());
         if (state === 'SUCCESS' ) {
            if(result.message){
                self.showToast('Failed', result.message);
                component.set('v.isSaving', false);
                component.set('v.orderFields.Total_Shipping_Charges__c', 0);
            }else{
                var currency = $A.get("$Locale.currency");
                component.set('v.orderFields.Total_Shipping_Charges__c', result.price);
                if(isPartsOrder && (component.get('v.caseType') == 'Non-warranty Order')){
                    var billToAddress = result.BillTo.Account;
                    var address = component.get("v.address");
                    if (billToAddress.ShippingCity == '' || billToAddress.ShippingCity == undefined || billToAddress.ShippingCity == null) {
                        component.set('v.billToShippingCity', address.city);
                    } else {
                        component.set('v.billToShippingCity', billToAddress.ShippingCity);
                    }

                    if (billToAddress.ShippingCountry == '' || billToAddress.ShippingCountry == undefined || billToAddress.ShippingCountry == null) {
                        component.set('v.billToShippingCountry', address.country);
                    } else {
                        component.set('v.billToShippingCountry', billToAddress.ShippingCountry);
                    }

                    if (billToAddress.ShippingPostalCode == '' || billToAddress.ShippingPostalCode == undefined || billToAddress.ShippingPostalCode == null) {
                        component.set('v.billToShippingPostalCode', address.zipCode);
                    } else {
                        component.set('v.billToShippingPostalCode', billToAddress.ShippingPostalCode);
                    }

                    if (billToAddress.ShippingState == '' || billToAddress.ShippingState == undefined || billToAddress.ShippingState == null) {
                        component.set('v.billToShippingState', address.state);
                    } else {
                        component.set('v.billToShippingState', billToAddress.ShippingState);
                    }

                    if (billToAddress.ShippingStreet == '' || billToAddress.ShippingStreet == undefined || billToAddress.ShippingStreet == null) {
                        component.set('v.billToShippingStreet', address.street);
                    } else {
                        component.set('v.billToShippingStreet', billToAddress.ShippingStreet);
                    }

                    component.set('v.billToFirstName', result.FirstName);
                    component.set('v.billToLastName', result.LastName);
                    component.set('v.productCost', productCost);
                     var shippingOverride = component.find('shippingOverride').get('v.value');
                    if(result.Tax){
                        if(result['Include Freight'] &&  shippingOverride == ''){
                            component.set('v.tax', ((Number(result.Tax) * productCost) + (Number(result.Tax) * result.price)).toFixed(2));
                        }else{
                            if(shippingOverride != 0 ){
                                component.set('v.tax', ((Number(result.Tax) * productCost) + (Number(result.Tax) * shippingOverride)).toFixed(2));
                            }else{
                                component.set('v.tax', (Number(result.Tax) * productCost) .toFixed(2));
                            }
                        }
                    }else{
                        component.set('v.tax', 0);
                    }
                    component.set('v.isParts', true);
                }else{
                    component.set('v.canBeSave', true);
                }
                var totalTax = component.get('v.tax');
                component.set('v.orderFields.Total_Tax__c', totalTax);
                component.set('v.isIncludeFreight', result['Include Freight']);
                component.set('v.taxRate', result.Tax);
            }
         } else if (state === 'ERROR') {
            self.showToast('Failed', response.getError()[0].message);
            component.set('v.isSaving', false);
         }
         self.hideEle(component, 'spinner');
            if(component.get("v.isParts")) {
                let action = component.get('c.toInvoice');
                component.set('v.isSaving', false);
                $A.enqueueAction(action);
            }
            if(component.get("v.canBeSave")) {
                let action = component.get('c.onClickSave');
                $A.enqueueAction(action);
            }
        });
        $A.enqueueAction(action);
    },
    checkPaymentStatus: function(component){
        var self = this;
        var action = component.get('c.authorizePayPalPayment');
        action.setParams({
            'cardNum': component.get('v.billToCardNumber'),
            'expDate':component.get('v.billToExpirationDate').replace('/', ''),
            'cvv2':component.get('v.billToCvv'),
            'amount':component.get('v.subTotal'),
            'billToFirstName': component.get('v.billToFirstName'),
            'billToLastName': component.get('v.billToLastName'),
            'billToStreet':component.get('v.billToShippingStreet'),
            'billToCity': component.get('v.billToShippingCity'),
            'billToState': component.get('v.billToShippingState'),
            'billToCountry':component.get('v.billToShippingCountry'),
            'billToZIP': component.get('v.billToShippingPostalCode')
        })
        component.set('v.orderFields.Billing_Address__c', component.get('v.billToShippingStreet'));
        component.set('v.orderFields.Billing_State__c', component.get('v.billToShippingState'));
        component.set('v.orderFields.Billing_City__c', component.get('v.billToShippingCity'));
        component.set('v.orderFields.Billing_Postal_Code__c ', component.get('v.billToShippingPostalCode'));
        component.set('v.orderFields.Billing_Country__c', component.get('v.billToShippingCountry'));

        action.setCallback(this, function (response) {
         var state = response.getState();
         var result = response.getReturnValue();
         if (state === 'SUCCESS' ) {
             if(result){
                  result = JSON.parse(result);
                  if(result.Status == 'Success'){
                      if(result.PNREF){
                         component.set('v.orderFields.PNREF__c', result.PNREF);
                      }
                      self.onSaveRecord(component);
                  }else{
                    component.set('v.isSaving', false);
                    self.showToast('Failed', result.Message);
                  }
             }
         } else if (state === 'ERROR') {
            component.set('v.isSaving', false);
            self.showToast('Failed', response.getError()[0].message);
         }
        });
        $A.enqueueAction(action);
    },
    onSaveRecord:function(component){
        var self = this;
        var orderItemList = JSON.stringify(component.get('v.orderItemData'));
        var orderItemRequiredData = {
            caseId:component.get('v.orderFields.Case__c'),
            warId:component.get('v.orderFields.Warranty__c'),
            brand:component.get('v.orderFields.Brand__c'),
            zipCode: component.get('v.orderFields.Shipping_Zip_Code__c'),
            type:component.get('v.orderFields.Type'),
            shippingmethod:component.get('v.orderFields.Shipping_method__c'),
            result: orderItemList
        };
        var data = component.get('v.orderFields');
        component.find("orderRecord").saveRecord(function (saveResult) {
            if (saveResult.state === "SUCCESS" || saveResult.state === "DRAFT") {
                self.saveOrderItem(component, saveResult.recordId, orderItemRequiredData);
            } else if (saveResult.state === "INCOMPLETE") {
                console.log("User is offline, device doesn't support drafts.");
                component.set('v.isSaving', false);
                $A.get('e.force:showToast').setParams({
                    "title": "Error",
                    "message": "User is offline, device doesn't support drafts.",
                    "type": "error"
                }).fire();
            } else if (saveResult.state === "ERROR") {
                component.set('v.isSaving', false);
                $A.get('e.force:showToast').setParams({
                    "title": "Error",
                    "message": saveResult.error.message,
                    "type": "error"
                }).fire();
            } else {
                console.log('Unknown problem, state: ' + saveResult.state +
                    ', error: ' + saveResult.error);
                component.set('v.isSaving', false);
                $A.get('e.force:showToast').setParams({
                    "title": "Error",
                    "message": saveResult.error.message,
                    "type": "error"
                }).fire();
            }
        });
    },
    saveOrderItem:function(component, recordId, orderItemRequiredData){
        let orderRecordTypeId = component.get("v.orderRecordTypeId"),
            self = this,
            action = component.get("c.saveOrder");
        self.showEle(component, 'spinner');
        action.setParams({
            orderRecordTypeId,
            'orderId': recordId,
            'caseId':orderItemRequiredData.caseId,
            'warId':orderItemRequiredData.warId,
            'brand':orderItemRequiredData.brand,
            'type': orderItemRequiredData.type,
            'zipCode': orderItemRequiredData.zipCode,
            'shippingmethod':orderItemRequiredData.shippingmethod,
            'result': orderItemRequiredData.result
        })
        action.setCallback(this, function (response) {
         var state = response.getState();
         var result = response.getReturnValue();
         if (state === 'SUCCESS' ) {
            if(result){
                self.showToast('Failed', result);
                component.set('v.isSaving', false);
            }else{
                // Success! Prepare a toast UI message
                if (component.get('v.isEdit')) {
                    component.set('v.isSaving', false);
                    $A.get('e.force:showToast').setParams({
                        "title": "Record Updated",
                        "message": "The order was updated.",
                        "type": "success"
                    }).fire();
                } else {
                    component.set('v.isSaving', false);
                    $A.get('e.force:showToast').setParams({
                        "title": "Record Created",
                        "message": "The new order was created.",
                        "type": "success"
                    }).fire();
                }
                self.hideEle(component, 'spinner');
                self.focusCaseTab(component, recordId);
                self.closeParentTab(component);
            }
         } else if (state === 'ERROR') {
            component.set('v.isSaving', false);
            self.showToast('Failed', response.getError()[0].message)
         }
        });
        $A.enqueueAction(action);
    },
    navigateToRecord: function (id) {
        var navEvt = $A.get("e.force:navigateToSObject");
        navEvt.setParams({
            "recordId": id,
            "slideDevName": "related"
        });
        navEvt.fire();
    },
    closeActiveTab: function (component) {
        var workspaceAPI = component.find("workspace");
        workspaceAPI.getFocusedTabInfo().then(function (response) {
            var focusedTabId = response.tabId;
            workspaceAPI.closeTab({tabId: focusedTabId});
        })
    },
    focusCaseTab: function (component, id) {
        var self = this;
        var workspaceAPI = component.find("workspace");
        var pageReference = JSON.parse(JSON.stringify(component.get('v.pageReference')));
        workspaceAPI.getFocusedTabInfo().then(function (response) {
            var focusedTabId = response.tabId;
            if(pageReference){
                var ws = pageReference.state.ws;
                workspaceAPI.openTab({
                    recordId: component.get('v.focusedTabId'),
                    focus: true
                }).then(function(response1) {
                    workspaceAPI.refreshTab({
                          tabId: response1,
                          includeAllSubtabs: false
                    });
                    var openTabs = component.get('v.openTabs');
                    for(var i=0;i<openTabs.length; i++){
                        workspaceAPI.openSubtab({
                            parentTabId: response1,
                            recordId: openTabs[i],
                            focus: true
                        });
                    }
                    workspaceAPI.openSubtab({
                        parentTabId: response1,
                        recordId: id,
                        focus: true
                    });
                    workspaceAPI.closeTab({tabId: focusedTabId});
                })
                .catch(function(error) {
                    console.log(error);
                });
            }else{
                workspaceAPI.openSubtab({
                    parentTabId: focusedTabId,
                    url: '/lightning/r/Order/' + id + '/view',
                    focus: true
                });
            }
        })
    },
    showEle:function(component, ele){
        $A.util.removeClass(
              component.find(ele),
              "slds-hide"
        );
    },
    hideEle:function(component, ele){
        $A.util.addClass(
              component.find(ele),
              "slds-hide"
        );
    },
    showToast: function(status, message){
         var toastEvent = $A.get('e.force:showToast');
         if(status == 'Success'){
              toastEvent.setParams({
                  'title': 'Success',
                  'message': message,
                  'type':'success',
              });
         }else if(status == 'Failed'){
              toastEvent.setParams({
                  'title': 'Error',
                  'message': message,
                  'type':'error',
              });
         }
         toastEvent.fire();
    },

    getCookie:function(cname) {
        var name = cname + "=";
        var ca = document.cookie.split(';');
        for(var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') {
                c = c.substring(1);
            }
            if (c.indexOf(name) == 0) {
                return c.substring(name.length, c.length);
            }
        }
        return "";
    },

    closeParentTab: function(component) {
        let workspaceAPI = component.find("workspace");
        let parentTabId = component.get('v.parentTabId');
        workspaceAPI.closeTab({tabId: parentTabId});
    },

    checkProductAddScope: function(orderItemData, productObj, alternativeProductsScope) {
        let isAdded = false;
        orderItemData.forEach(item => {
            if(alternativeProductsScope.includes(item.ProductLine.ProductCode)) {
                isAdded = true;
            }
        });

        if(isAdded) {
            if(alternativeProductsScope.includes(productObj.ProductCode)) {
                this.showToast('Failed', 'You can only add one kind of product amount these products: ' + alternativeProductsScope.join(', '));
                return false;
            }
        }
        return true;
    }
})